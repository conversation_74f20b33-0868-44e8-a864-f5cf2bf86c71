import DeviceMarkerMap from '@/components/common/deviceMarkerMap'
import { ref, markRaw } from 'vue'
export default function clickMap () {
  interface JwdList {
    objX: number,
    objY: number
  }
  const componentId = ref<any>('')
  const jwdList = ref<Array<JwdList> | Array<any>>([])
  const  openMap =(objX: number, objY: number)=>{
    componentId.value = markRaw(DeviceMarkerMap)
    console.log(componentId.value);
    debugger
    jwdList.value.push({
      objX,
      objY
    })
  }
  const closeModal =()=> {
    componentId.value = ''
  }
  return {
    componentId,
    jwdList,
    openMap,
    closeModal
  }
}