import { useRoute } from "vue-router"
const getLocationId = (): string => {
 return (useRoute() as any)?.query.id || ''
}
class GetQueryParams{
  [key:string]:any;
  constructor(){
    for (const key in useRoute().query) {
      this[key] = useRoute().query[key]
    }
  }
}
class GetRouteParams{
  [key:string]:any;
  constructor(){
    for (const key in useRoute().query) {
      this[key] = useRoute().params[key]
    }
  }
}
export {
  GetRouteParams,
  GetQueryParams,
  getLocationId
}