// 获取巡检方案类型
import { ref } from 'vue';
import { getPatrolProgrammeList } from "@/api/safeManage/parkPatrolService";
const categoryList = ref<any>([]);
export default async function getPatrolProgramme() {
  let res: any = await getPatrolProgrammeList({
    page: { size: -1, current: 1 },
    customQueryParams: {},
  });
  console.log(res.data.records);
  if (res.success) {
    categoryList.value = res.data.records;
  }
  return {
    categoryList: categoryList.value
  }
}