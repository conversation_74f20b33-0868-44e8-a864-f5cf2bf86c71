import { PromiseBody } from '@/api/common'
import { Message, Modal } from 'view-ui-plus'
import { isEmpty } from 'wei-util'
export function MessageSuccess(title: string) {
  Message.success(title)
}
export function MessageWarning(title: string) {
  Message.warning(title)
}
export function MessageError(title: string) {
  Message.error(title)
}
export function MessageInfo(title: string) {
  Message.info(title)
}
export function ModalDelete(func: Function, params: any, getData: Function) {
  if(isEmpty(params)) return MessageInfo('至少选择一条记录')
  Modal.confirm({
    title: '提示',
    content: '是否删除选中内容',
    onOk: async () => {
      const { success }: PromiseBody = await func(params)
      if (success) {
        MessageSuccess('删除成功！')
        getData()
      }
    }
  })
}
export function ModalConfirm(textOption:any,func: Function, params: any, getData: Function) {
  Modal.confirm({
    title: textOption.title,
    content: textOption.content,
    onOk: async () => {
      const { success }: PromiseBody = await func(params)
      if (success) {
        MessageSuccess(textOption.successMsg)
        getData()
      }
    }
  })
}