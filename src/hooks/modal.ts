import { ref, Component } from "vue";

export default function useModal() {
    const modalStatus = ref(true);
    const componentName = ref<Component | string>();
    const emitCloseModal = () => {
        modalStatus.value = false;
        setModal("");
    };

    const setModal = (name: Component | string) => {
        componentName.value = name;
    };

    return {
        modalStatus,
        setModal,
        emitCloseModal,
        componentName,
    };
}
