import Cookies from 'js-cookie'

const TokenKey = 'Login-Token'
const domains = document.domain.split('.')
const domain = domains.slice(-2).join('.')
console.log(domain)

export function getToken() {
    return Cookies.get(Token<PERSON>ey)
}

export function setToken(token) {
    if (domains.length == 4 || domains.length == 1) return Cookies.set(To<PERSON><PERSON><PERSON>, token)
    return Cookies.set(To<PERSON><PERSON><PERSON>, token, { domain: `.${domain}`, path: '/' })
}

export function removeToken() {
    if (domains.length == 4 || domains.length == 1) return Cookies.remove(Token<PERSON>ey)
    return Cookies.remove(To<PERSON><PERSON><PERSON>, { domain: `.${domain}`, path: '/' })
}
