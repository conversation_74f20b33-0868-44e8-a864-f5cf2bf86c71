import { Notice, But<PERSON>, Message } from 'view-ui-plus';
import store from '@/store';

export const setPeerStore = () => {
    return new Promise(resolve => {
        let peerStore = {
            /**
             * 本地对等体对象
             */
            localPeer: undefined,
            /**
             * 正在通话的数据连接对象
             */
            dataConnection: undefined,
            /**
             * 正在通话的媒体连接对象
             * @function mediaConnection.on
             */
            mediaConnection: undefined,
            /**
             * 是否激活通知
             */
            activateNotification: false,
            /**
             * 视频通话指令枚举值
             */
            instruction: {
                /**
                 * 请求视频通话
                 */
                request: 1,
                /**
                 * 拒绝视频通话
                 */
                reject: 2,
                /**
                 * 接受视频通话
                 */
                accept: 3,
                /**
                 * 取消视频通话
                 */
                cancel: 4,
                /**
                 * 正常挂断
                 */
                ringOff: 5,
                /**
                 * 忙碌挂断
                 */
                busy: 6
            },
        };

        // let { host, port, path } = { ...networkConfiguration.server.peerServer };
        // const host = window.location.hostname
        const host = process.env.VUE_APP_LOGIN_URL ? 'hlhpeer.net4iot.com' : window.location.hostname // 正式环境
        // const port = '9000'
        const port = process.env.VUE_APP_LOGIN_URL ? '16005' : '9000' // 正式环境
        const path = '/'
        let localPeer = new Peer({ host, port, path });

        localPeer.on('connection', dataConnection => {
            console.log('localPeer on connection', dataConnection);
            if (peerStore.dataConnection) {
                dataConnection.on('open', () => {
                    dataConnection.send({
                        instruction: peerStore.instruction.busy
                    });
                });
            } else {

                dataConnection.on('data', data => {
                    console.log('dataConnection data', data);

                    // 请求通话
                    if (data.instruction === peerStore.instruction.request) {
                        peerStore.dataConnection = dataConnection;
                        // 激活通知
                        peerStore.activateNotification = true;
                        Notice.info({
                            title: `${data.nickName} -邀请你语音通话`,
                            desc: data.topicName,
                            duration: 0,
                            name: `${dataConnection.peer}`,
                            render: h => {
                                return h('div', [
                                    h('div', data.topicName),
                                    h(Button, {
                                        props: {
                                            size: 'small',
                                            type: 'primary'
                                        },
                                        type: 'primary',
                                        style: {
                                            float: 'right',
                                        },
                                        onClick: () => {
                                            store.dispatch('user/updateMeeting', true)
                                            store.dispatch('user/updateVoiceComName', 'voiceAnserCom')
                                            store.dispatch('user/updateMeetingInfo', {
                                                topicName: data.topicName,
                                                callLogId: data.callLogId,
                                                people: [{
                                                    nickname: data.nickName
                                                }]
                                            })
                                            Notice.close(`${dataConnection.peer}`)
                                        },
                                    }, '查看')
                                ])
                            }
                        })
                    }

                    // 对方取消
                    else if (data.instruction === peerStore.instruction.cancel) {
                        // 关闭数据连接
                        peerStore.dataConnection?.close();
                        peerStore.dataConnection = undefined;
                        // 取消激活通知
                        peerStore.activateNotification = false;
                    }

                    // 对方挂断
                    else if (data.instruction === peerStore.instruction.ringOff) {
                        console.log('ringOff')
                        Message.error(`对方已挂断`)
                        // 关闭数据连接
                        peerStore.dataConnection?.close();
                        peerStore.dataConnection = undefined;
                        // 关闭媒体连接
                        peerStore.mediaConnection?.close();
                        peerStore.mediaConnection = undefined;
                        store.dispatch('user/updateMeeting', false)
                        store.dispatch('user/updateVoiceComName', '')
                    }
                });
            }
        });

        localPeer.on('call', async(mediaConnection) => {
            console.log('localPeer call');
            await (peerStore.mediaConnection = mediaConnection);
        });

        localPeer.on('disconnected', () => {
            console.warn('localPeer disconnected');
            localPeer.reconnect();
        });

        localPeer.on('error', error => {
            console.error('localPeer error,the error information is : ', JSON.stringify(error));
        });

        localPeer.on('open', localPeerId => {
            console.log('localPeer opened,the local peer id is: ', localPeerId);
            peerStore.localPeer = localPeer;
            resolve(peerStore);
        });
    })
}
