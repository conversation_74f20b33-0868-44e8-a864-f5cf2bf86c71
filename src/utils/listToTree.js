// list转树
let defaultParentKey = 'parentId'
let defaultChildrenKey = 'id'
export function listToTree(list, parentKey, childrenKey) {
    if (parentKey) {
        defaultParentKey = parentKey
    }
    if (childrenKey) {
        defaultChildrenKey = childrenKey
    }
    list = _listTotree(list)
    list = _sortBysortId(list)
    return list
}

// list转树
export function treeParentMenuName(list) {
    list.forEach(item => {
        _treeParentMenuName(item)
    })
    return list
}

function _treeParentMenuName(data) {
    if (data.children && data.children.length > 0) {
        data.children.forEach(item => {
            if (data.menuName) {
                item.menuName = [...data.menuName, data.name]
            } else {
                item.menuName = [data.name]
            }
            _treeParentMenuName(item)
        })
    }
    return data
}

/**
 * 树转list
 */
export function treeToList(arr) {
    let res = []; // 用于存储递归结果（扁平数据）
    // 递归函数
    const fn = (source) => {
        source.forEach((el) => {
            res.push(el);
            el.children && el.children.length > 0 ? fn(el.children) : ''; // 子级递归
        });
    };
    fn(arr);
    return res;
}

function _listTotree(oldArr) {
    oldArr.forEach(element => {
        let parentId = element[defaultParentKey];
        if(parentId){
            oldArr.forEach(ele => {
                if (ele[defaultChildrenKey] == parentId) {
                    if(!ele.children){
                        ele.children = [];
                    }
                    ele.children.push(element);
                }
            })
        }
    });
    oldArr = oldArr.filter(ele => ele[defaultParentKey] == null)
    return oldArr
}

function _sortBysortId(oldArr) {  // 将树根据 sort 排序
    // console.log("排序前的树",oldArr)
    // oldArr.sort((a, b)=>{return b.sort - a.sort})
    oldArr.forEach(item => {
        if(item.children){
            item.children = _sortBysortId(item.children)
            // item.checked = false  // 对于半勾选的节点，要去掉他们的checked 状态,避免联动全选了所有子节点
        }
    })
    // console.log("排序后的树",oldArr)
    return oldArr
}
