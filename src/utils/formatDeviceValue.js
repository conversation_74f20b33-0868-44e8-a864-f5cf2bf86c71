// 格式化设备的物模型内容
// decimalPlaceNumbers，item为数时的小数位
export function formatDeviceValue(item, decimalPlaceNumbers) {
    if (!item) {
        return ''
    }
    let specs = ''
    let dataType = ''
    if (item.dataType) {
        // 基于devicePropertyStatusList + physicModel
        specs = item.dataType.specs
        dataType = item.dataType.type
    } else {
        // 基于完整devicePropertyStatusList
        specs = item.specs
        dataType = item.unit
    }
    if (typeof (specs) == 'string') {
        specs = JSON.parse(specs)
    }
    // console.log(specs)
    let specsKeys = specs && Object.keys(specs) || []
    let specsValues = specs && Object.values(specs) || []
    let value = item && item.value || ''

    let realData = ''
    switch (dataType) {
        case 'bool':
            if (specsKeys.length > 0) {
                specsKeys.forEach((enumItem) => {
                    if (value == enumItem) {
                        realData = specs[enumItem]
                    }
                })
            }
            if (realData == '') realData = value;
            break
        case 'enum':
            if (specsKeys.length > 0) {
                specsKeys.forEach((enumItem) => {
                    if (value == enumItem) {
                        realData = specs[enumItem]
                    }
                })
                if (!realData) {
                    specsValues.forEach((enumItem, index) => {
                        if (value == enumItem) {
                            realData = specsKeys[index]
                        }
                    })
                }
            }
            if (realData == '') realData = value;
            break;
        case 'double':
        case 'int':
        case 'float':
        case 'long':
            if(value !== '' && typeof decimalPlaceNumbers == 'number') {
                value = (+value).toFixed(decimalPlaceNumbers)
            }
            if (specs && specs.unit) {
                realData = value + ' ' + specs.unit
            } else {
                realData = value
            }
            break;
        default:
            realData = value;
            break;
    }

    return realData;
}


/**
* 表格中格式化物模型
 * row: 表格数据  key:属性code
* */
export function tableInFormatDeviceValue(row, key) {
    if (!row.devicePropertyStatusList || !key) {
        return ''
    }
    let attr = row.devicePropertyStatusList.find(item => item.prop === key)
    if (attr) {
        let obj = row.physicModel?.find(item => item.identifier === key)
        if (obj) {
            attr.dataType = obj.dataType
            return formatDeviceValue(attr)
        }
        return attr.value
    } else {
        return ''
    }
}
