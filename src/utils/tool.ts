import { ComponentInfo } from "@/api/fireHydrantService";
import { useRouter } from "vue-router";
import moment from "moment";
import store from "@/store";
import Util from ".";
import { number } from "echarts";
import { type } from "os";
/**
 * 查找符合条件节点的所有父级节点
 * @return {*}
 */
export function getParentIds(key: string, tree: any[], func: Function, path: any[] = []) {
    if (!tree) return [];
    for (let i = 0; i < tree.length; i++) {
        if (key) {
            path.push(`${tree[i][key]}`);
        } else {
            path.push(tree[i]);
        }

        if (tree[i]?.children?.length) {
            const findIds: any[] = getParentIds(key, tree[i].children, func, path);
            if (findIds.length) return findIds;
        }
        if (func(tree[i])) {
            return path;
        }

        path.pop();
    }
    return [];
}
/**
 * 查找树结构中某个值的节点及其所有父级节点
 * @param {Array} tree - 树结构数据
 * @param {string|number} targetId - 目标节点的值
 * @returns {Array} - 包含目标节点及其所有父级节点的数组
 */
export function getParentItems(tree: any[], targetId: string | number,key:string) {
    // 递归查找函数
    const findNode = (node: any, targetId: string | number, path: any[] = []) => {
      if (node[key] === targetId) {
        return [...path, node]; // 找到目标节点，返回路径
      }
  
      if (node.children && node.children.length > 0) {
        for (const child of node.children) {
          const result = findNode(child, targetId, [...path, node]);
          if (result) {
            return result; // 如果在子树中找到目标节点，返回路径
          }
        }
      }
  
      return null; // 未找到目标节点
    };
  
    // 遍历树结构
    for (const node of tree) {
      const result = findNode(node, targetId, []);
      if (result) {
        return result; // 返回找到的路径
      }
    }
  
    return []; // 未找到目标节点，返回空数组
  }
  
/**
 * 树转list
 */
export function treeToList(arr: any[]) {
    let res: any[] = []; // 用于存储递归结果（扁平数据）
    // 递归函数
    const fn = (source: any[]) => {
        source.forEach((el) => {
            res.push(el);
            el.children && el.children.length > 0 ? fn(el.children) : ""; // 子级递归
        });
    };
    fn(arr);
    return res;
}
/**
 * 深度优先遍历
 * @param node
 * @param f
 */

export function trace(node: any[], f: Function) {
    node?.forEach((element) => {
        f(element);
        if (element?.children?.length) trace(element?.children, f);
    });
}

/**
 * 广度优先遍历
 * @param node
 */
export function Ntrace(node: any[]) {
    let nodes = new Array();
    nodes.push(node);
    while (nodes.length > 0) {
        let Child = nodes.shift();
        Child.children.forEach((element: any) => {
            nodes.push(element);
        });
    }
}
/**
 * 带复选框的树 父子联动
 * @param checkedKeys 当前已选中的节点数组
 * @param selectKey 当前项
 * @param areaTree 全树
 */
export function treeParentChildLinkage(
    checkedKeys: any[],
    selectKey: any,
    areaTree: any[],
    nodeKey: string
) {
    const newKeys = checkedKeys.map((k) => k[nodeKey]);
    const { checked, children = [] } = selectKey;
    // 当前节点的所有子节点
    const childrenKeys = children ? treeToList(children).map((k) => k[nodeKey]) : [];

    // 当前勾选的所有父级节点
    const parentKeys = getParentIds(
        nodeKey,
        areaTree,
        (data: any) => {
            if (selectKey[nodeKey] == data[nodeKey]) return true;
            return false;
        },
        []
    );
    // 如果是勾选加入所有父级节点和子节点
    // 如果是去掉勾选去掉所有子节点
    const selectIds = checked
        ? Array.from(new Set([...childrenKeys, ...parentKeys, ...newKeys]))
        : newKeys.filter((k) => !childrenKeys.includes(k));

    // 遍历树勾选
    trace(areaTree, (data: any) => {
        if (selectIds.includes(data[nodeKey])) {
            data["checked"] = true;
        } else {
            data["checked"] = false;
        }
    });
}
/**
 * 数组转对象
 * @param data
 * @param key
 * @param value
 * @returns
 */
export function arrayToObject(data: any[], key: string, value: string) {
    return data.reduce(function (result, item) {
        result[item[key]] = item[value];
        return result;
    }, {});
}

/**
 * 过滤满足条件的树
 */
export function filterTree(data: any[], fun: Function) {
    const res: any[] = [];
    data.forEach((item) => {
        const tmp = { ...item, expand: true };
        if (fun(tmp)) {
            if (tmp.children) {
                tmp.children = filterTree(tmp.children, fun);
            }
            res.push(tmp);
        }
    });
    return res;
}

// 搜索树
export function searchTree(
    tree: any[],
    fun: Function,
    includeChildren = false,
    expand: boolean = true
) {
    if (!fun) return tree;
    const newTree: any[] = [];
    for (let i = 0; i < tree.length; i++) {
        const node = tree[i];
        if (fun(node)) {
            newTree.push({
                ...node,
                expand: expand || node.expand,
                children: includeChildren ? searchTree(node.children || [], fun, true, expand) : [],
            });
        } else if (node.children) {
            const result: any[] = searchTree(node.children, fun, true, expand);
            if (result.length) {
                newTree.push({ ...node, expand: expand || node.expand, children: result });
            }
        }
    }
    return newTree;
}

export function launchIntoFullscreen(element: HTMLElement) {
    if (element.requestFullscreen) {
        element.requestFullscreen();
    } else if (element["mozRequestFullScreen"]) {
        element["mozRequestFullScreen"]();
    } else if (element["webkitRequestFullscreen"]) {
        element["webkitRequestFullscreen"]();
    } else if (element["msRequestFullscreen"]) {
        element["msRequestFullscreen"]();
    }
}
//退出全屏封装
export function exitFullscreen(element: HTMLElement) {
    if (document.exitFullscreen) {
        document.exitFullscreen();
    } else if (document["mozCancelFullScreen"]) {
        document["mozCancelFullScreen"]();
    } else if (document["webkitExitFullscreen"]) {
        document["webkitExitFullscreen"]();
    }
}
// 处理面积后缀
export function handleArea(area: string | number) {
    if (area && area !== null) {
        return area + "㎡";
    }
}
//   处理位置
export function handleCoordinate(objInfo: ComponentInfo | undefined) {
    if (!objInfo) return "";
    if (objInfo.gdx && objInfo.gdy && objInfo.gdx !== null && objInfo.gdy !== null) {
        let XY: string = objInfo.gdx + " , " + objInfo.gdy;
        return XY;
    }
    return "";
}
export function isNullOrEmpty(param: any) {
    if (param == null) {
        return true; // 参数为 null 或 undefined
    } else if (typeof param === "string" && param.trim() === "") {
        return true; // 参数为空字符串
    } else if (Array.isArray(param) && param.length === 0) {
        return true; // 参数为空数组
    } else if (typeof param === "object" && Object.keys(param).length === 0) {
        return true; // 参数为空对象
    } else {
        return false; // 参数不为空
    }
}
// 处理区域
export function handleLocation(content: ComponentInfo) {
    if (!content) return "";
    return (
        ((!isNullOrEmpty(content.szjd) && content.szjd) || "") +
        ((!isNullOrEmpty(content.szsq) && "/" + content.szsq) || "") +
        ((!isNullOrEmpty(content.szdywg) && "/" + content.szdywg) || "")
    );
}
/**
 * 获取路由query参数
 * @return {*}
 */
export function getRouterQuery(key = ""):string {
    const router = useRouter();
    if (key) return (router.currentRoute.value?.query?.[key] || "") as unknown as string;
    return router.currentRoute.value?.query  as unknown as string;
}

/**
 * 获取路由params参数
 * @return {*}
 */
export function getRouterParams(key: string) {
    const router = useRouter();
    if (key) return router.currentRoute.value?.params?.[key] || "";
    return router.currentRoute.value?.params;
}
// 根据字典类型获取字典值
export function getDicValue(code: string, val: string | number | undefined) {
    return val ? store.getters.dictionary[code][val] || "--" : "--";
}

// 对象扁平化
export function flatObj(obj: any, flatObject = {}) {
    for (const key in obj) {
        if (typeof obj[key] === "object") {
            if (obj[key] !== null) {
                flatObj(obj[key], flatObject);
            }
        } else {
            if (obj[key] !== undefined) {
                flatObject[key] = obj[key];
            }
        }
    }
    return flatObject;
}
// 合并对象data\form是对象
export function ObjAssign(data: any, form: any, list: Array<any>) {
    list.forEach((item: any) => {
        item.forEach((i: any) => {
            for (let key in i) {
                if (key === "slot") {
                    data[i[key]] = form[i[key]];
                }
            }
        });
    });
    return data;
}
// 从地址信息中去除逗号
export function formatAreaStr(areaStr: string): string {
    return areaStr?.replace(/,|，/g, "");
}

// 获取视频第一帧图片
export function getFirstFpsPic(videoUrl: string, callback: any) {
    const video = document.createElement("video");
    video.setAttribute("crossOrigin", "anonymous");
    const source = document.createElement("source");
    source.setAttribute("type", "video/mp4");
    source.setAttribute("src", videoUrl);
    video.appendChild(source);
    video.style.position = "absolute";
    video.style.left = "100000px";
    video.style.top = "-100000px";
    document.querySelector("body")?.appendChild(video);
    video.currentTime = 1;
    video.onloadeddata = () => {
        const canvas: any = document.createElement("canvas");
        canvas.width = video.clientWidth;
        canvas.height = video.clientHeight;
        canvas.getContext("2d").drawImage(video, 0, 0, canvas.width, canvas.height);
        const dataURL = canvas.toDataURL("image/png");
        video.remove();
        callback(dataURL);
    };
}
// 排序
export function sortData(arr: Array<any> = [], sortType: string = "asc") {
    if (sortType === "asc") return arr.sort((a: number | string, b: number | string) => +a - +b);
    if (sortType === "desc") return arr.sort((a: number | string, b: number | string) => +b - +a);
}
// 根据时间排序
export function sortTime(arr: Array<any> = [], sortType: string = "asc") {
    if (sortType === "asc")
        return arr.sort(function (a: any, b: any) {
            const timeA = a.split(":");
            const timeB = b.split(":");
            if (timeA[0] === timeB[0]) {
                return timeA[1] - timeB[1];
            } else {
                return timeA[0] - timeB[0];
            }
        });
    if (sortType === "desc")
        return arr.sort(function (a: any, b: any) {
            const timeA = a.split(":");
            const timeB = b.split(":");
            if (timeA[0] === timeB[0]) {
                return timeB[1] - timeA[1];
            } else {
                return timeB[0] - timeA[0];
            }
        });
}

// 额外附加到浮层的 css 样式
export const tooltipExtraCssText =
    "background: linear-gradient(304.17deg, rgba(253, 254, 255, 0.6) -6.04%, rgba(244, 247, 252, 0.6) 85.2%);backdrop-filter: blur(5px);";

//arg为formatter参数，unit为单位，width为tooltip的自定义宽度，默认为180px
export function EchartsTooltip(
    arg: any,
    unit: string | string[] = "",
    width: number | string = 180
) {
    if (Array.isArray(arg)) {
        let dom = `<div style="">
                <div style="padding:8px 0 8px 14px;">${arg[0].name}</div>
                <div style="
                display:flex;
                min-width: ${typeof width === "number" ? width + "px" : width};
                flex-direction: column;
                row-gap: 4px;
                ">`;
        arg.forEach((item: any, index: number) => {
            const showData = () => {
                if (arg[index].value === 0 || arg[index].value) {
                    return (
                        arg[index].value +
                        (typeof unit === "string" ? unit || "" : unit[index] || "")
                    );
                } else {
                    return "--";
                }
            };
            let linearColor = "";
            if (typeof item.color === "object") {
                if (item.color.x2 === 1) {
                    const linearArr = item.color.colorStops.map((item: any) => {
                        return {
                            color: hexToRgba(item.color),
                            offset: item.offset * 100 + "%",
                        };
                    });
                    linearColor =
                        linearArr.reduce((per: any, cur: any, index: number, arr: any) => {
                            return per + "," + cur.color + " " + cur.offset;
                        }, "linear-gradient(to right") + ")";
                }
            }
            dom += `<div style="
                  display:flex;
                  align-items: center;
                  min-height: 32px;
                  justify-content: space-between;
                  background: rgba(255, 255, 255, 0.9);
                  box-shadow: 6px 0px 20px rgba(34, 87, 188, 0.1);
                  border-radius: 4px;
                  ">
                <span style="margin:0 8px;height:11px;width:11px;border-radius:50%;background:${(linearColor && linearColor) || hexToRgb(item.color)
                };"></span>
                  <span style="
                  color: #4E5969;
                  font-size: 12px;
                  margin-right: 8px;
                  font-family: 'PingFang SC';
                  ">${arg[index].seriesName}</span>
                  <span style="
                  color: #1D2129;
                  font-size: 13px;
                  font-family: 'PingFang SC';
                  font-weight: 720;
                  margin-left: auto;
                  margin-right: 8px;
                  ">${showData()}</span>
                  </div>`;
        });
        dom += "</div></div>";
        return dom;
    } else {
        const showData = () => {
            if (typeof arg.data === "object") {
                if (arg.data.value === 0 || arg.data.value) {
                    return (
                        Math.abs(arg.data.value) +
                        (Array.isArray(unit) ? unit[arg.seriesIndex] : unit)
                    );
                } else {
                    return "--";
                }
            } else {
                if (arg.data === 0 || arg.data) {
                    return (
                        Math.abs(arg.data) + (Array.isArray(unit) ? unit[arg.seriesIndex] : unit)
                    );
                } else {
                    return "--";
                }
            }
        };
        return `
        <div style="">
        <div style="padding:8px 0 8px 14px;">${arg.name}</div>
        <div style="
        display:flex;
        width: ${typeof width === "number" ? width + "px" : width};
        flex-direction: column;
        row-gap: 4px;
        ">
          <div style="
          display:flex;
          align-items: center;
          min-height: 32px;
          justify-content: flex-start;
          background: rgba(255, 255, 255, 0.9);
          box-shadow: 6px 0px 20px rgba(34, 87, 188, 0.1);
          border-radius: 4px;
          ">
          <span style="margin:0 8px;height:11px;width:11px;border-radius:50%;background:${hexToRgb(
            arg.color
        )};"></span>
          <span style="
          color: #4E5969;
          font-size: 12px;
          font-family: 'PingFang SC';
          ">${arg.seriesName}</span>
          <span style="
          color: #1D2129;
          font-size: 13px;
          font-family: 'PingFang SC';
          font-weight: 720;
          margin-left: auto;
          margin-right: 8px;
          ">${showData()}</span>
          </div>
          </div>
          </div>
        `;
    }
}
// 十六进制转成rgb
export function hexToRgb(hex: string | any) {
    const hexRegex = /^[0-9A-Fa-f]+$/;
    if (!hexRegex.test(hex)) {
        return hex;
    } else {
        // 将 # 号去掉
        hex = hex.replace("#", "");
        // 将 R、G、B 分别转换为十进制值
        let r = parseInt(hex.substring(0, 2), 16);
        let g = parseInt(hex.substring(2, 4), 16);
        let b = parseInt(hex.substring(4, 6), 16);
        // 返回 RGB 值
        return "rgb(" + r + ", " + g + ", " + b + ")";
    }
}

// 十六进制转成rgb
export function hexToRgba(hex: string, opacity: number = 1) {
    const hexRegex = /^#?[0-9A-Fa-f]+$/;
    if (!hexRegex.test(hex)) {
        return hex;
    } else {
        // 将 # 号去掉
        hex = hex.replace("#", "");
        // 将 R、G、B 分别转换为十进制值
        let r = parseInt(hex.substring(0, 2), 16);
        let g = parseInt(hex.substring(2, 4), 16);
        let b = parseInt(hex.substring(4, 6), 16);
        // 返回 RGB 值
        return "rgba(" + r + ", " + g + ", " + b + ", " + opacity + ")";
    }
}

// 秒转化为时分秒
export function secondsFormat(s: number) {
    var hour = Math.floor(s / 3600);
    var minute = Math.floor((s - hour * 3600) / 60);
    var second = Math.floor(s - hour * 3600 - minute * 60);
    return (
        (hour < 10 ? `0${hour}` : hour) +
        ":" +
        (minute < 10 ? `0${minute}` : minute) +
        ":" +
        (second < 10 ? `0${second}` : second)
    );
}
// 全局事件总线
export const bus: any = {
    events: {},
    on(eventName: string, fn: any) {
        if (!this.events[eventName]) {
            this.events[eventName] = [];
        }
        this.events[eventName].push(fn);
    },
    off(eventName: string, fn?: any) {
        if (!this.events[eventName]) {
            return;
        }
        this.events[eventName] = this.events[eventName].filter((eventFn: any) => eventFn !== fn);
    },
    emit(eventName: string, data?: any) {
        if (!this.events[eventName]) {
            return;
        }
        this.events[eventName].forEach((eventFn: any) => eventFn(data));
    },
};
type SameValue = string | number;
/**
 * 查找符合项的值
 * @param arr 传进来的数组
 * @param sameValue 与之匹配的值
 * @param key 改值得指针，深层对象指针例子'a.b.c'
 * @param value 找的值,深层值指针例子，'a.b.c',不传返回匹配的对象
 * @returns 返回值
 */
export const findValue = (
    arr: any[],
    sameValue: SameValue,
    key: string,
    value?: string,
    defaultValue?: any
) => {
    try {
        const getDeepValue = (keyArr: string[], obj: any) => {
            return keyArr.reduce((pre: string, cur: string) => {
                return pre[cur];
            }, obj);
        };
        const getItemValue = (item: any) => {
            return getDeepValue(key.split("."), item);
        };
        return value
            ? getValue(
                arr.find((item: any) => getItemValue(item) === sameValue),
                value
            )
            : arr.find((item: any) => getItemValue(item) === sameValue);
    } catch (error) {
        return defaultValue || "";
    }
};
/**
 * 查找index，防止指针指向过程报错
 * @param arr 数组
 * @param sameValue 与之匹配的值
 * @param key 改值得指针，深层对象指针例子'a.b.c'
 * @returns 返回下标，找不到返回-1
 */
export function findIndex(arr: any[], sameValue: SameValue, key: string) {
    try {
        const getDeepValue = (keyArr: string[], obj: any) => {
            return keyArr.reduce((pre: string, cur: string) => {
                return pre[cur];
            }, obj);
        };
        const getItemValue = (item: any) => {
            return getDeepValue(key.split("."), item);
        };
        return arr.findIndex((item: any) => getItemValue(item) === sameValue);
    } catch (error) {
        return -1;
    }
}
/**
 * 取值(防止指针指向过程中报错问题)
 * @param data 对象
 * @param keyString 指向key值可为['a','b','c']或'a.b.c'
 * @param defaultValue 默认值(可选)，不传时返回空字符串
 * @returns 返回值
 */
export function getValue(data: any, keyString: string | string[], defaultValue?: any) {
    const type = toString.call(keyString);
    const getVal = (arr: string[]) => {
        return arr.reduce((per, cur) => {
            try {
                if (!per[cur]) {
                    throw new Error();
                } else {
                    return per[cur];
                }
            } catch (error) {
                return defaultValue || "";
            }
        }, data);
    };
    switch (type) {
        case "[object Array]":
            return getVal(keyString as string[]);
        case "[object String]":
            const keyArr = (keyString as string).split(".");
            return getVal(keyArr);
    }
}
/**
 * 保留小数位
 * @param value 值
 * @param toFixed 保留几位小数，默认两位
 * @returns
 */
export function toFixed(value: number | string, toFixed: number = 2): string | number {
    try {
        const type = typeof value;
        if (type === "number") {
            const [, decimals] = value.toString().split(".");
            if (decimals.length >= toFixed) return Number(value).toFixed(toFixed);
        } else {
            const [, decimals] = (value as string).split(".");
            if (decimals.length >= toFixed) return Number(value).toFixed(toFixed);
        }
        throw new Error();
    } catch (error) {
        return value === undefined || null ? "" : value;
    }
}
export const getBase64String = (imgUrl: string, callback: Function) => {
    const image = new Image();
    image.crossOrigin = "anonymous";
    image.onload = () => {
        const canvas = document.createElement("canvas");
        const ctx: any = canvas.getContext("2d");
        canvas.height = image.naturalHeight;
        canvas.width = image.naturalWidth;
        ctx.drawImage(image, 0, 0);
        const dataUrl = canvas.toDataURL();
        callback && callback(dataUrl);
    };
    image.src = imgUrl;
};

export const mapPointMarker = (name: string, url: any) => {
    return `
    <div class="map-icon-container">
    <div class="map-top-label">
    <div class="name">${name}</div>
    </div>
    <div class="icon">
    <img  src="${url}" />
    <div class="point"></div>
    </div>
    </div>
    `;
};
export const filterArr = (arr, name) => {
    let hash = {};
    return arr.reduce((ss, item) => {
        hash[item[name]] ? "" : (hash[item[name]] = true && ss.push(item));
        return ss;
    }, []);
};
export const isImageURL = (url: string) => {
    const imageExtensions = ["jpg", "jpeg", "png", "gif", "webp", "bmp", "tiff"];
    const extension = url?.split(".").pop()?.toLowerCase();
    return extension ? imageExtensions.includes(extension) : false;
};
export const isVideoURL = (url: string) => {
    const imageExtensions = ["mp4", "avi", "mkv", "flv", "wmv", "swf", "ogg"];
    const extension = url?.split(".").pop()?.toLowerCase();
    return extension ? imageExtensions.includes(extension) : false;
};
// 饼图轮播
export class CarouselPie {
    myChart: any;
    timer: any;
    option: any;
    currentIndex: number;
    constructor(chart: any, option: any) {
        this.myChart = chart;
        this.option = option;
        this.currentIndex = 0;
    }
    highlightPie = () => {
        // 取消所有高亮并高亮当前图形
        for (let index in this.option.series[0].data) {
            // 遍历饼图数据，取消所有图形的高亮效果
            this.myChart.dispatchAction({
                type: "downplay",
                seriesIndex: 0,
                dataIndex: index,
            });
            // 高亮当前图形
            this.myChart.dispatchAction({
                type: "highlight",
                seriesIndex: 0,
                dataIndex: this.currentIndex,
            });
        }
    };
    listenerMouseEvent = () => {
        this.myChart.on("mousemove", (params: any) => {
            this.currentIndex = params.dataIndex;
            this.highlightPie();
            clearInterval(this.timer);
        });
        this.myChart.on("mouseout", (params: any) => {
            this.currentIndex = params.dataIndex;
            this.handleCarousel();
            this.timer = setInterval(this.handleCarousel, 4000);
        });
    };
    handleCarousel = () => {
        this.highlightPie();
        if (this.currentIndex >= this.option.series[0].data.length - 1) this.currentIndex = 0;
        else this.currentIndex++;
    };
    carousel = () => {
        clearInterval(this.timer);
        this.handleCarousel();
        this.timer = setInterval(this.handleCarousel, 4000);
        this.listenerMouseEvent();
    };
}
