 
//GCJ-02：中国坐标偏移标准，Google Map、高德、腾讯使用
//BD-09：百度坐标偏移标准，Baidu Map使用 (百度坐标)
//WGS-84：是国际标准，GPS坐标（Google Earth使用、或者GPS模块）
 
/**
 * @description 高德转百度(垃圾收运处理)
 * @description  GCJ-02 转 BD-09
 * @param{*}list [lng,lat]
 */
 function garbagetransBdMap(obj) {
    debugger
    if(obj.gdx){
        let list = [{lng:obj.gdx,lat:obj.gdy}]
        let newList = qqMapTransBdMap(list)
        obj.objx = newList[0].lng
        obj.objy = newList[0].lat
    }
}


/**
 * @description 高德转百度
 * @description  GCJ-02 转 BD-09
 * @param{*}list [lng,lat]
 */
 function qqMapTransBdMap(list) {
    let result=[]
    for (const res of list) {
        let x_pi = 3.14159265358979324 * 3000.0 / 180.0;
        let x = res.lng;
        let y = res.lat;
        let z = Math.sqrt(x * x + y * y) + 0.00002 * Math.sin(y * x_pi);
        let theta = Math.atan2(y, x) + 0.000003 * Math.cos(x * x_pi);
        let lngs = z * Math.cos(theta) + 0.0065;
        let lats = z * Math.sin(theta) + 0.006;
        result.push({
            lng: lngs,
            lat: lats
        })
    }
    return result;
}
 
/**
 * @description 百度转高德
 * @description BD-09 转 GCJ-02
 * @param{*}list [lng,lat]
 */
function bdMapTransQqMap(list) {
    let bitem = []
    for (const g of list) {
        let x_pi = (3.14159265358979324 * 3000.0) / 180.0
        let x = g.lng - 0.0065
        let y = g.lat - 0.006
        let z = Math.sqrt(x * x + y * y) - 0.00002 * Math.sin(y * x_pi)
        let theta = Math.atan2(y, x) - 0.000003 * Math.cos(x * x_pi)
        let g2_lnn = z * Math.cos(theta)
        let g2_lat = z * Math.sin(theta)
        bitem.push({lng: g2_lnn, lat: g2_lat})
    }
    return bitem
}
/**
 * @description 高德转火星
 * @description GCJ-02 转 BD-09
 * @param{*}list [lng,lat]
 */
function gdMapTransHxMap(list) {
    let gitem = []
    for (const b of list) {
        let x_pi = (3.14159265358979324 * 3000.0) / 180.0
        let z =
            Math.sqrt(b.lng * b.lng + b.lat * b.lat) +
            0.00002 * Math.sin(b.lat * x_pi)
        let theta = Math.atan2(b.lat, b.lng) + 0.000003 * Math.cos(b.lng * x_pi)
        let bd_lng = z * Math.cos(theta) + 0.0065
        let bd_lat = z * Math.sin(theta) + 0.006
        gitem.push({lng: bd_lng, lat: bd_lat})
    }
    return gitem
}
 
/**
 * @description GPS转高德
 * @description WGS84转GCj02
 * @param lng GPS纬度
 * @param lat GPS经度
 * @returns {*[]}
 */
function qqMapTransGpsMap(lng, lat) {
    var x_PI = 3.14159265358979324 * 3000.0 / 180.0;
    var PI = 3.1415926535897932384626;
    var a = 6378245.0;
    var ee = 0.00669342162296594323;
 
    if (this.out_of_china(lng, lat)) {
        return [lng, lat]
    } else {
        var dlat =this.transformlat(lng - 105.0, lat - 35.0);
        var dlng = this.transformlng(lng - 105.0, lat - 35.0);
        var radlat = lat / 180.0 * PI;
        var magic = Math.sin(radlat);
        magic = 1 - ee * magic * magic;
        var sqrtmagic = Math.sqrt(magic);
        dlat = (dlat * 180.0) / ((a * (1 - ee)) / (magic * sqrtmagic) * PI);
        dlng = (dlng * 180.0) / (a / sqrtmagic * Math.cos(radlat) * PI);
        var mglat = lat + dlat;
        var mglng = lng + dlng;
        return [mglng, mglat]
    }
}
/**
 * @description 高德转GPS
 * @description GCj02转WGS84
 * @param lng 高德经度
 * @param lat 高德经度
 * @returns {*[]}
 */
function QqMapTransGpsMap(lng, lat) {
    var x_PI = 3.14159265358979324 * 3000.0 / 180.0;
    var PI = 3.1415926535897932384626;
    var a = 6378245.0;
    var ee = 0.00669342162296594323;
 
    if (this.out_of_china(lng, lat)) {
        return [lng, lat]
    } else {
        var dlat =this.transformlat(lng - 105.0, lat - 35.0);
        var dlng = this.transformlng(lng - 105.0, lat - 35.0);
        var radlat = lat / 180.0 * PI;
        var magic = Math.sin(radlat);
        magic = 1 - ee * magic * magic;
        var sqrtmagic = Math.sqrt(magic);
        dlat = (dlat * 180.0) / ((a * (1 - ee)) / (magic * sqrtmagic) * PI);
        dlng = (dlng * 180.0) / (a / sqrtmagic * Math.cos(radlat) * PI);
        let  mglat = lat + dlat;
        let mglng = lng + dlng;
        return [lng * 2 - mglng, lat * 2 - mglat]
    }
}
function transformlng(lng, lat) {
    var x_PI = 3.14159265358979324 * 3000.0 / 180.0;
    var PI = 3.1415926535897932384626;
    var a = 6378245.0;
    var ee = 0.00669342162296594323;
    var ret = 300.0 + lng + 2.0 * lat + 0.1 * lng * lng + 0.1 * lng * lat + 0.1 * Math.sqrt(Math.abs(lng));
    ret += (20.0 * Math.sin(6.0 * lng * PI) + 20.0 * Math.sin(2.0 * lng * PI)) * 2.0 / 3.0;
    ret += (20.0 * Math.sin(lng * PI) + 40.0 * Math.sin(lng / 3.0 * PI)) * 2.0 / 3.0;
    ret += (150.0 * Math.sin(lng / 12.0 * PI) + 300.0 * Math.sin(lng / 30.0 * PI)) * 2.0 / 3.0;
    return ret
}
function transformlat(lng, lat) {
    var x_PI = 3.14159265358979324 * 3000.0 / 180.0;
    var PI = 3.1415926535897932384626;
    var a = 6378245.0;
    var ee = 0.00669342162296594323;
    var ret = -100.0 + 2.0 * lng + 3.0 * lat + 0.2 * lat * lat + 0.1 * lng * lat + 0.2 * Math.sqrt(Math.abs(lng));
    ret += (20.0 * Math.sin(6.0 * lng * PI) + 20.0 * Math.sin(2.0 * lng * PI)) * 2.0 / 3.0;
    ret += (20.0 * Math.sin(lat * PI) + 40.0 * Math.sin(lat / 3.0 * PI)) * 2.0 / 3.0;
    ret += (160.0 * Math.sin(lat / 12.0 * PI) + 320 * Math.sin(lat * PI / 30.0)) * 2.0 / 3.0;
    return ret
}
 
/**
 * @description {{国外地址转换}}
 * @param lng
 * @param lat
 * @returns {boolean}
 */
function out_of_china(lng, lat) {
    return (lng < 72.004 || lng > 137.8347) || ((lat < 0.8293 || lat > 55.8271) || false);
}
 
export {garbagetransBdMap,qqMapTransBdMap, bdMapTransQqMap, gdMapTransHxMap,qqMapTransGpsMap,QqMapTransGpsMap}