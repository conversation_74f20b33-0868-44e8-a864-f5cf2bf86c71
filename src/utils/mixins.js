// 饼图自动滚动
export const pieAutoAnimation = {
    data() {
        return {
            currentHighlightIndex: 0,
            myChart: null,
            dataLen: 0
        }
    },
    computed: {
        downplay() {
            let arr = []
            for (let i = 0; i < this.dataLen; i++) {
                arr.push(i)
            }
            return arr
        }
    },
    methods: {
        loadChart(echart) {
            this.myChart = echart
            if (echart) {
                let mouseoutTime = null
                // console.log('loadChart')
                this.showHighlightText()
                echart.on('click', (param) => {
                    // console.log(param)
                    this.clearTimeAndHigh(param.dataIndex)
                })
                echart.on('mouseover', (param) => {
                    mouseoutTime && clearTimeout(mouseoutTime)
                    // console.log('mouseover', param.dataIndex, this.currentHighlightIndex, param.name)
                    this.clearTimeAndHigh(param.dataIndex)
                    this.legendScroll(param.dataIndex)
                })
                echart.on('mouseout', (param) => {
                    mouseoutTime && clearTimeout(mouseoutTime)
                    mouseoutTime = setTimeout(() => {
                        // console.log('mouseout', param.dataIndex, this.currentHighlightIndex, param.name)
                        this.initChartAnim()
                    }, 100)
                    
                })
                // echart.on('globalout', (param) => {
                //     console.log('globalout')
                //     this.initChartAnim()
                // })
                echart.on('highlight', (param) => {
                    // console.log('highlight', param)
                    this.legendScroll(param.dataIndex)
                    // console.log(param)
                })
                echart.on('downplay', (param) => {
                    // console.log(param)
                })
                echart.on('legendscroll', (param) => {
                    if (param.scrollDataIndex != this.currentHighlightIndex) {
                        // console.log('legendscroll', param.scrollDataIndex, this.currentHighlightIndex)
                        this.clearTimeAndHigh(param.scrollDataIndex)
                        this.initChartAnim()
                    }
                })
            }
        },
        legendScroll(index) {
            // console.log('legendScroll', index)
            if (this.myChart && index >= 0) {
                this.myChart.dispatchAction({
                    type: 'legendScroll',
                    scrollDataIndex: index,
                })
            }
        },
        clearTimeAndHigh(dataIndex) {
            if (this.timmer) {
                clearInterval(this.timmer)
            }
            if (this.currentHighlightIndex != dataIndex) {
                this.clearHighlightText()
                this.currentHighlightIndex = dataIndex
            }
        },
        initChartAnim() {
            if (this.timmer) {
                clearInterval(this.timmer)
            }
            // console.log('initChartAnim', this.currentHighlightIndex)
            this.clearHighlightAll()
            setTimeout(this.showHighlightText, 100)
            this.timmer = setInterval(() => {
                this.clearHighlightText()
                let index = (this.currentHighlightIndex + 1) % this.dataLen
                this.currentHighlightIndex = index
                this.showHighlightText()
            }, 4000)
        },
        clearAnim() {
            if (this.timmer) {
                clearInterval(this.timmer)
            }
            this.clearHighlightAll()
        },
        clearHighlightAll() {
            // 取消之前高亮的图形
            if (this.myChart) {
                this.myChart.dispatchAction({
                    type: 'downplay',
                    seriesIndex: 0,
                    dataIndex: this.downplay
                })
            }
        },
        clearHighlightText(dataIndex) {
            // 取消之前高亮的图形
            if (this.myChart) {
                let index = this.currentHighlightIndex
                if (dataIndex || dataIndex === 0) {
                    index = dataIndex
                }
                this.myChart.dispatchAction({
                    type: 'downplay',
                    seriesIndex: 0,
                    dataIndex: index
                })
            }
        },
        showHighlightText() {
            // 高亮当前图形
            if (this.myChart) {
                this.myChart.dispatchAction({
                    type: 'highlight',
                    seriesIndex: 0,
                    dataIndex: this.currentHighlightIndex
                });
            }
        }
    }
}

// 直接调用模态框组件
export const ModalCommon = {
    props: {
        show: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            modalWidth: 600,
            loading: false
        }
    },
    computed: {
        modalShow: {
            get() {
                return this.show
            },
            set() {}
        }
    },
    methods: {
        visibleChange(val) {
            if (!val) {
                this.closeModel()
            }
        },
        closeModel() {
            this.$emit('close')
        }
    }
}
