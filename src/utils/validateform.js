import moment from 'moment';
const validateform = {
    // 该规则必须是字符串
    selectRequired: { required: true, message: '请完善必填项' },
    nonnegativeNumber: { pattern: /^[0-9]+(\.[0-9]+)?$/, trigger: 'change,blur', message: '不能输入负数' },
    required: { required: true, message: '请完善必填项', trigger: 'change,blur' },
    requiredNum: { required: true, message: '请完善必填项', trigger: 'change,blur', type: 'number' },
    requiredNumGreater0: { pattern: /^[1-9]$|^10$/, trigger: 'change,blur', message: '请输入1~10的整数' },
    nonnegativePositiveInteger: { pattern: /^\d+$/, trigger: 'change,blur', message: '请输入非负数正整数' },
    requiredArray: { required: true, message: '请完善必填项', trigger: 'change,blur', type: 'array' },
    requiredDate: { required: true, message: '请完善必填项', trigger: 'change,blur', type: 'date' },
    requiredObject: { required: true, message: '请完善必填项', trigger: 'change,blur', type: 'object' },
    maxText20: { pattern: /^.{1,20}$/, trigger: 'blur', message: '限制20个字' },
    requiredPhone: [
        { required: true, message: '请完善必填项', trigger: 'change,blur' },
        { pattern: /^1[3456789]\d{9}$/, trigger: 'blur', message: '手机号错误' }
    ],
    checkPhone: { pattern: /^1[3456789]\d{9}$/, trigger: 'blur', message: '请输入正确格式的手机号' },
    // 固定电话和手机
    telephone: { pattern: /^(1[3456789]\d{9}|\d{3}-?\d{8}|\d{4}-?\d{7})$/, trigger: 'blur', message: '请输入正确手机号或固定电话' },
    nonnegativeNum: { pattern: /^([1-9]\d*\.?\d*)|(0\.\d*)$/, trigger: 'change', message: '请输入非负数' },
    maxDayRang30: {
        type: 'array', trigger: 'change', validator: (rule, value, callback) => {
            if (!value || !value[0]) {
                callback('请完善必填项');
            } else if ((value[1].setHours(0, 0, 0, 0) - value[0].setHours(0, 0, 0, 0)) / 86400000 > 29) {
                callback('请勿选择大于30天')
            } else {
                callback()
            }

        }
    },
    // 效验网址格式是否正确
    verifyUrl: { pattern: /^http(s)?:\/\/([\w-]+\.)+[\w-]+(\/[\w-./?%&=]*)?/g, trigger: 'blur', message: '请输入正确格式的网址' },
    lengthLessThan(length = 20) {
        return {
            validator: (rule, value, callback) => {
                if ((value || '').length < length) {
                    callback()
                } else {
                    callback(new Error(`长度不能大于${length}`))
                }
            },
            trigger: 'change,blur'
        }

    },
    dateIsAfterReferDate(referDate, msg = '不能大于上次修改时间') {
        return {
            validator: (rule, value, callback) => {
                console.log(referDate, value);
                if (typeof referDate === 'object') {
                    referDate = referDate.value
                }
                const referDateTimestamp = new Date(referDate).getTime();
                const timestamp = new Date(value).getTime();
                if (timestamp > referDateTimestamp) {
                    callback(new Error(msg))
                } else {
                    callback()
                }
            },
            trigger: 'change,blur'
        }

    },
    // 判断日期是否在某个日期之前
    dateIsBeforeReferDate(referDate, msg = '不能小于上次修改时间') {
        return {
            validator: (rule, value, callback) => {
                console.log(referDate, value);
                if (typeof referDate === 'object') {
                    referDate = referDate.value
                }
                const referDateTimestamp = new Date(referDate).getTime();
                const timestamp = new Date(value).getTime();
                if (timestamp < referDateTimestamp) {
                    callback(new Error(msg))
                } else {
                    callback()
                }
            },
            trigger: 'change,blur'
        }

    },
    decimals(decimalBit = 2) {
        return {
            type: 'number', trigger: 'change,blur', validator: (rule, value, callback) => {
                const [, bit] = value.toString().split('.')
                bit && (bit.length > decimalBit) ? callback(`小数部分为${decimalBit}位`) : callback()
            }
        }
    },
    maxDayRang365: {
        type: 'array', trigger: 'change', validator: (rule, value, callback) => {
            if (!value || !value[0]) {
                callback('请完善必填项');
            } else if ((value[1].setHours(0, 0, 0, 0) - value[0].setHours(0, 0, 0, 0)) / 86400000 > 365) {
                callback('请勿选择大于1年')
            } else {
                callback()
            }

        }
    },
    money: [{
        type: 'number', trigger: 'change,blur', validator: (rule, value, callback) => {
            if (!value) {
                callback();
            } else {
                // let reg = /^([1-9]\d*\.?\d*)|(0\.\d*)$/
                if (value < 0) {
                    return callback('请输入非负数');
                }
                let arr = (value + '').split('.')
                if (arr.length > 1) {
                    // 有小数点
                    if (arr[1].length > 2) {
                        return callback('小数点后只能有2位')
                    }
                }
                if (arr[0].length > 12) {
                    callback('整数只能有12位')
                } else {
                    callback()
                }
            }
        }
    }],
    requiredPowerNum(integerDigits = 9, decimalDigits = 2) {
        return {
            type: 'number', trigger: 'change,blur', validator: (rule, value, callback) => {
                if (!value) {
                    callback();
                } else {
                    const regexStr = `^-?\\d{1,${integerDigits}}(\\.\\d{1,${decimalDigits}})?$`;
                    const regex = new RegExp(regexStr);
                    if (regex.test(value)) {
                        callback();
                    } else {
                        return callback(`请输入整数部分为${integerDigits}位，小数部分为${decimalDigits}位的数`);
                    }
                }
            }
        }
    },
    // len：整数的位数  point：小数点的位数，为0时为正整数
    powerNum(len = 9, point = 2) {
        return {
            type: 'number', trigger: 'change,blur', validator: (rule, value, callback) => {
                if (!value) {
                    callback();
                } else {
                    // let reg = /^([1-9]\d*\.?\d*)|(0\.\d*)$/
                    if (value < 0) {
                        return callback('请输入非负数');
                    }
                    let reg = /^[0-9]+.?[0-9]*/
                    if (!reg.test(value)) {
                        return callback('请输入数字');
                    }

                    let arr = (value + '').split('.')
                    if (arr.length > 1) {
                        // 有小数点
                        if (point > 0) {
                            if (arr[1].length > point) {
                                return callback('小数点后只能有' + point + '位')
                            }
                        } else {
                            // 不能有小数点
                            return callback('请输入正整数');
                        }
                    }
                    if (arr[0].length > len) {
                        callback('整数只能有' + len + '位')
                    } else {
                        callback()
                    }
                }
            }
        }
    },
    // 车牌号验证
    carNoVerify: {
        trigger: 'blur', validator: (rule, value, callback) => {
            if (isVehicleNumber(value)) {
                callback()
            } else {
                callback('请输入正确车牌号')
            }
        }
    },
    // 判断日期是否在今天之前
    dateIsBefore: {
        validator: (rule, value, callback) => {
            console.log(value, 'value');
            const timestamp = moment(value).valueOf();
            const nowTimesTamp = Date.now()
            console.log(timestamp, nowTimesTamp);
            if (nowTimesTamp < timestamp) {
                callback(new Error('选择时间不能大于当前时间'))
            } else {
                callback()
            }
        },
        trigger: 'change'
    },
    // 经度验证,最多保留5位小数
    longitudeVerification: {
        trigger: 'blur', validator: (rule, value, callback) => {
            if (value) {
                const reg = /^([-+]?((1[0-7][0-9])|([1-9]?[0-9]))(\.[0-9]{1,5})?)$|^180(\.0{1,5})?$/;
                if (!reg.test(value)) {
                    callback('请输入正确的经度，最多五位小数')
                } else {
                    callback()
                }
            } else {
                callback()
            }
        }
    },
    // 纬度验证,最多保留5位小数
    dimensionVerification: {
        trigger: 'blur', validator: (rule, value, callback) => {
            if (value) {
                const reg = /^([-+]?((8[0-9])|([0-7]?[0-9]))(\.[0-9]{1,5})?)$|^90(\.0{1,5})?$/;
                if (!reg.test(value)) {
                    callback('请输入正确的纬度，最多五位小数')
                } else {
                    callback()
                }
            } else {
                callback()
            }
        }
    }

}
const isVehicleNumber = (val) => {
    if (!val) return true
    else {
        var xreg = /^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z]{1}(([0-9]{5}[DF]$)|([DF][A-HJ-NP-Z0-9][0-9]{4}$))/;
        var creg = /^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z]{1}[A-HJ-NP-Z0-9]{4}[A-HJ-NP-Z0-9挂学警港澳]{1}$/;
        if (val.length == 7) {
            return creg.test(val);
        } else if (val.length == 8) {
            return xreg.test(val);
        } else {
            return false;
        }
    }
}
export {
    validateform
}
