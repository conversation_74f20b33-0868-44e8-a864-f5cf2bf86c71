import axios from 'axios';
import { Message } from 'view-ui-plus';
import { removeToken } from './auth';
import store from '@/store';

// create an axios instance
const service = axios.create({
    // baseURL: process.env.VUE_APP_BASE_API, // url = base url + request url
    maxRedirects: 0,
    // withCredentials: true, // send cookies when cross-domain requests
    timeout: 60000 // request timeout
});

// request interceptor
service.interceptors.request.use(
    (config) => {
        // 本地起后端代理就注释掉
        if (!config.baseURL) {
            config.baseURL = `${process.env.VUE_APP_BASE_API}/${store.getters.pageName}`
        }
        const routeCode = store.state.permission.routeCode
        let url = location.hash?.slice(1).split('?')[0]
        if (routeCode[url]) {
            config.headers['privilege-code'] = routeCode[url]
        }
        return config;
    },
    (error) => {
        // do something with request error
        console.log(error); // for debug
        return Promise.reject(error);
    }
);

// response interceptor
service.interceptors.response.use(
    (response) => {
        // console.log(response)
        // console.log(response);
        const res = response.data;
        if (response.status !== 200) {
            if (response.status == 401) {
                store.dispatch('user/resetToken').then(() => {
                    window.location.href = window.location.pathname + '#/login';
                })
                return;
            }
            Message.error({
                content: res.message || '服务异常，请稍后再试',
                duration: 5
            });
            return Promise.reject(new Error(res.message || 'Error'));
        } else {
            const { config } = response;
            if (config.url.includes('logout')) {
                Message.success({
                    content: '已退出登录',
                    duration: 2
                });
                removeToken();
                // setTimeout(() => window.location.href = '/login', 2000)
                return;
            }
            if (!res.success && !config.hideMsg) {
                if (res.type !== 'application/octet-stream' && Object.prototype.toString.call(res) !== '[object Blob]') { // 流文件不弹框
                    Message.error({
                        content: res.msg || res.message || '服务异常，请稍后再试',
                        duration: 3
                    });
                }
            }
            return res;
        }
    },
    (error) => {
        console.log(error); // for debug
        console.log(error.code);
        if (error.message?.includes('401')) {
            store.dispatch('user/resetToken').then(() => {
                setTimeout(() => (window.location.href = window.location.pathname + '#/login'), 2000);
            })
            return;
        }
        Message.error({
            content: error.message || '服务异常，请稍后再试',
            duration: 5
        });
        return Promise.reject(error);
    }
);

export default service;
