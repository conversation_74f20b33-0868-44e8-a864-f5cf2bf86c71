import SockJS from 'sockjs-client/dist/sockjs.min';
import Stomp from 'stompjs';
import store from '@/store';

// websocket 相关
const websocket = {
    url: '',
    socket: null,
    stompClient: null,
    reconnecting: false,
    headers: { Authorization: '' },
    listenerList: [],
    // topic 订阅的主题 subDestination 订阅的目的地 callback 回调函数
    // 初始化websocket
    initWebSocket(topic, subDestination, callback) {
        this.listenerList = [];
        this.url = './api' + store.getters.pageName + '/stomp/websocket'
        this.socket = new SockJS(this.url);
        this.stompClient = Stomp.over(this.socket);
        this.stompClient.connect(
            this.headers, // 可添加客户端的认证信息
            (frame) => {
                // 连接成功回调
                console.log('webSocket 连接成功');
                this.subscribe(topic + subDestination, callback);
            },
            (error) => {
                console.log('webSocket 连接失败：' + error);
            }
        );
    },
    // 连接成功回调 订阅主题
    connectSucceed(topic, subDestination, callback) {
        this.stompClient.subscribe(topic + subDestination, function(response) {
            const result = JSON.parse(response.body);
            console.log('websocket 推送：' + result);
            callback(result);
        });
    },
    // 关闭websocket
    closeSocket() {
        if (stompClient != null) {
            this.stompClient.disconnect();
        }
    },
    send(topic, params, callback, responseTopic) { // 外部调用，发送消息
        if (this.stompClient && this.stompClient.connected) { // 当前已连接
            if (responseTopic) { // 此消息发送后，有响应topic对应回复消息
                var i = 1
                var self = this
                this.subscribe(responseTopic, function(response) {
                    console.log(responseTopic + '-------', response)
                    var data = JSON.parse(response.body)
                    if (data.status == '200') { // 请求成功
                        self.stompClient.unsubscribe(responseTopic)
                        if (callback) callback({ success: true, data: data })
                    } else { // 请求失败
                        console.log('重复发送次数--------', i)
                        if (i >= 5) { // 超过5次则不再请求
                            self.stompClient.unsubscribe(responseTopic)
                            if (callback) callback({ success: false, msg: data.msg })
                        } else {
                            i++
                            setTimeout(() => {
                                self.stompClient.send(topic, {}, JSON.stringify(params));
                            }, 2000);
                        }
                    }
                })
            }
            this.stompClient.send(topic, {}, JSON.stringify(params));
        } else {
            if (callback) callback({ success: false, msg: '未连接' })
        }
    },
    unsubscribe(topic) { // 外部调用，解除订阅
        for (let i = 0; i < this.listenerList.length; i++) {
            if (this.listenerList[i].topic == topic) {
                var subscription = this.listenerList[i].subscription
                if (subscription) {
                    subscription.unsubscribe()
                }
                this.listenerList.splice(i, 1)
                console.log('解除订阅：' + topic + ' size:' + this.listenerList.length)
                break;
            }
        }
    },
    subscribe(topic, callback) { // 外部调用，订阅
        if (this.stompClient && this.stompClient.connected) {
            if (this.listenerList.some(item => item.topic == topic)) { // 之前有订阅过，需要解除订阅
                this.unsubscribe(topic)
            }
            var subscription = this.stompClient.subscribe(topic, callback)
            this.listenerList.push({
                topic: topic,
                callback: callback,
                subscription: subscription
            })
        } else {
            var flag = false
            for (let i = 0; i < this.listenerList.length; i++) {
                if (this.listenerList[i].topic == topic) {
                    flag = true
                    this.listenerList[i].callback = callback
                    console.log('订阅：' + topic + ' size:' + this.listenerList.length)
                    break;
                }
            }
            if (!flag) { // 之前没有监听此topic
                this.listenerList.push({
                    topic: topic,
                    callback: callback
                })
            }
        }
    }
};

export default websocket;
