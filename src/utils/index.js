import defaultSettings from '@/settings'
import store from '@/store';
import { createElementVNode } from 'vue';
import { isNullOrEmpty } from './tool';
import request from './request';
import moment from 'moment'

const title = defaultSettings.title[store.getters.pageName] || 'base'

const Util = {
    getPageTitle(pageTitle) {
        if (pageTitle) {
            return `${pageTitle} - ${title}`
        }
        return `${title}`
    },
    // 判断权限
    checkAuth: (code) => {
        const { authCodes } = store.state.user;
        if (authCodes.includes(code)) return true;
        return false
    },
    isEmpty(val) {
        return val === undefined || val === null || val == '' || val.length === 0;
    },
    objClone(source, defaultValue) {
        // 拷贝的对象中的属性值不能是fun
        return source ? JSON.parse(JSON.stringify(source)) : defaultValue
    },
    deepCopy(data) {
        // 如果拷贝的对象中有fun，则采用此方法进行拷贝
        const t = typeof (data)
        let o
        if (t === 'array') {
            o = []
        } else if (t === 'object') {
            o = {}
        } else {
            return data
        }
        if (t === 'array') {
            for (let i = 0; i < data.length; i++) {
                o.push(this.deepCopy(data[i]))
            }
        } else if (t === 'object') {
            for (const i in data) {
                o[i] = this.deepCopy(data[i])
            }
        }
        return o
    },
    request,
    // 身份证加*
    idCardFuzzy(id) {
        if (!id) {
            return ''
        }
        // let reg = /^[1-9]\d{5}(18|19|20|(3\d))\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/
        // if (!reg.test(id)) {
        //     return '--'
        // }
        let arr = id.split('')
        for (let i = 4; i < arr.length - 4; i++) {
            arr[i] = '*'
        }
        return arr.join('')
    },
    /* 数字进行万化 */
    formatPowerNum(num) {
        let len = (parseInt(num) + '').length
        if (len > 4) {
            return this.formatNum(num / 10000, 1) + 'w'
        }
        return num
    },
    /* 格式化数字
    * len：保留的小数位
    * */
    formatNum(num, len = 2) {
        if (typeof num === 'string') {
            num = parseFloat(num)
        }
        if (num === 0) {
            return 0
        }
        if(!num){
            return ''
        }
        return parseFloat(num.toFixed(len))
    },
    formatDate(date, fmt) {
        if (!fmt) {
            fmt = 'YYYY-MM-DD HH:mm:ss';
        }
        return moment(date).format(fmt)
    },
    /* 获取时间范围 如果不传入默认
    * isCurTime：是否取当前的时分秒
    * customDay：当type为‘cd’时有效，自定义的天数
    * */
    getDateRangeS(date, type, format = 'YYYY-MM-DD', isCurTime = false, customDay) {
        // 获取时间范围 如果不传入默认
        let res = [];
        let start, end = '';
        if (typeof date !== 'object') {
            date = new Date(date);
        }
        let endTime = null;
        switch (type) {
            case 'h':
                // 日
                endTime = new Date(date)
                break;
            case 'd': // 本周
                date.setDate(date.getDate() - date.getDay() + 1);
                endTime = new Date(date);
                endTime.setDate((endTime.getDate() - endTime.getDay()) + 7)
                break;
            case 'nd':// 近7天
                endTime = new Date(date)
                date.setDate(date.getDate() - 6);
                break;
            case 'm':// 本月
                date.setDate(1);
                endTime = new Date(date.getFullYear(), date.getMonth() + 1, 0)
                break;
            case 'nm':// 近30天
                endTime = new Date(date)
                date.setDate(date.getDate() - 29);
                break;
            case 'y':// 本年
                date.setMonth(0)
                date.setDate(1)
                endTime = new Date(date)
                endTime.setMonth(12)
                endTime.setDate(0)
                break;
            case 'cd':// 自定义天数
                endTime = new Date(date)
                date.setDate(date.getDate() - (customDay - 1));
                break;
            default:
                // statements_def
                break;
        }
        date.setHours(0)
        date.setMinutes(0)
        date.setSeconds(0)
        if (!isCurTime) {
            endTime.setHours(23)
            endTime.setMinutes(59)
            endTime.setSeconds(59)
        }

        start = this.formatDate(date, format)
        end = this.formatDate(endTime, format)
        res.push(start, end)
        return res;
    },
    /* 通过秒得到时分秒
	 * time秒
	 */
    getTimeBySeconds(time) {
        let h = Math.floor(time / (60 * 60))
        let m = Math.floor(time / 60 % 60)
        let s = time % 60
        let arr = []
        h = h > 9 ? h : '0' + h
        arr.push(h)
        m = m > 9 ? m : '0' + m
        s = s > 9 ? s : '0' + s
        arr.push(m)
        arr.push(s)
        return arr.join(':')
    },
    download(href, name) {
        let downloadElement = document.createElement('a');
        downloadElement.href = href;
        downloadElement.download = name;
        document.body.appendChild(downloadElement);
        downloadElement.click();
        document.body.removeChild(downloadElement);
        window.URL.revokeObjectURL(href);
    },
    // 判断是否超出内容禁用tooltip content:内容，width：为表头宽度
    judgeDisabled(content, width) {
        let span = document.createElement('span')
        span.style.display = 'inline-block'
        span.style.fontSize = '14px'
        span.textContent = content
        document.body.appendChild(span)
        let contentWidth = span.clientWidth
        document.body.removeChild(span)
        if (contentWidth > width - 36) {
            return false
        } else {
            return true
        }
    },
    // 拼接地址
    getAddressPath(objInfo) {
        if (!objInfo) {
            return ''
        }
        let address = []
        if (objInfo.szjd) {
            address.push(objInfo.szjd)
        }
        if (objInfo.szsq) {
            address.push(objInfo.szsq)
        }
        if (objInfo.szdywg) {
            address.push(objInfo.szdywg)
        }
        if (address.length > 0) {
            return address.join('/')
        } else {
            return ''
        }
    },
    // 查找列表中的数据，返回具体的值 value:要查找的值   key：要查找值的key  name：要返回值的key
    findNameByList(list, value, key = 'value', name = 'name') {
        let obj = list.find(item => item[key] == value)
        if (obj) {
            return obj[name]
        } else {
            return ''
        }
    },
    handleLocation(content) {
        if (!content) return ''
        return (!isNullOrEmpty(content.szjd) && content.szjd || '') + (!isNullOrEmpty(content.szsq) && ('/' + content.szsq) || '') + (!isNullOrEmpty(content.szdywg) && ('/' + content.szdywg )|| '');
    },
    // 树形结构数据转换
    transformData(data) {
        const result = [];
        data.forEach((item) => {
          const newItem = {
            ...item,
            id: item.code,
            expand: true,
            selected: false,
            checked: false,
            title: item.label,
            value: item.value,
          };
          if (item.children && item.children.length) {
            newItem.children = this.transformData(item.children);
          }
          result.push(newItem);
        })
        return result;
    }
}


export default Util
