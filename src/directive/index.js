import store from '@/store';
export default {
    install(app) {
        app.directive('auth', {
            mounted(el, binding, vnode) {
                const { value } = binding;
                const { authCodes } = store.state.user;
                let _val = [];
                if (value && value.length > 0) {
                    if (typeof value === 'string') {
                        _val = new Set([value]);
                    } else {
                        _val = new Set([...value]);
                    }
                    // 求交集
                    const hasPermission = new Set([...authCodes].filter((x) => _val.has(x)));

                    if (!hasPermission.size && authCodes.length) {
                        el.style.display = 'none'
                        el.parentNode && el.parentNode.removeChild(el);
                        el && el.remove()
                    }
                } else {
                    console.warn(`use v-auth suggest fill roles! Like v-auth="['admin','editor']"`);
                }
            }
        })
        app.directive('defaultImg', {
            mounted(e, { value, arg }) {
                let flag, nowSrc
                const defaultImg = require('@/assets/images/icon-图片占位.png')
                const img = new Image()
                img.src = defaultImg
                nowSrc = img.src
                if (!e.src) {
                    e.style.objectFit = 'contain'
                    if (value) {
                        e.setAttribute('src', value)
                    } else {
                        e.setAttribute('src', defaultImg)
                    }
                }
                e.addEventListener('load', async(event) => {
                    // console.log(event);
                    var ev = event || window.event;
                    var elem = ev.target;
                    if (elem.tagName.toLowerCase() == 'img') {
                        if (!flag && e.src !== nowSrc && e.src !== value) {
                            e.style.objectFit = arg || 'cover'
                        }
                    }

                })
                e.addEventListener('error', (el) => {
                    flag = true
                    e.style.objectFit = 'contain'
                    if (value) {
                        el.target.setAttribute('src', value)
                    } else {
                        e.style.objectFit = 'contain'
                        e.setAttribute('src', defaultImg)
                        e.classList.add('error-img')
                    }
                })
            },
        })
        app.directive('downLoadUrl', {
            mounted(el, binding, vnode) {
                if (binding.value) {
                    el.addEventListener('click', () => {
                        const a = document.createElement('a')
                        //   let url = baseUrl + binding.value // 若是不完整的url则需要拼接baseURL
                        const url = binding.value // 完整的url则直接使用
                        // 这里是将url转成blob地址，
                        fetch(url).then(res => res.blob()).then(blob => { // 将链接地址字符内容转变成blob地址
                            a.href = URL.createObjectURL(blob)
                            // a.download = binding.value.name || '' // 下载文件的名字
                            a.download = url.split('/')[url.split('/').length - 1] //  // 下载文件的名字
                            document.body.appendChild(a)
                            a.click()
                            // 在资源下载完成后 清除 占用的缓存资源
                            window.URL.revokeObjectURL(a.href);
                            document.body.removeChild(a);
                        })
                    })
                }
            }
        })
    }
}
