
/**
 * @Description: 垃圾运收管理
 */
export default {
    path: '/garbageCollectionManagement',
    redirect: '/garbageCollectionManagement/basic/factory',
    meta: {
        authCode: 'garbageCollectionManagement:basic',
        title: '垃圾运收管理'
    },
    children: [
        {
            path: 'basic/factory',
            name: 'garbageCollectionManagement:basic:factory',
            component: () => require.ensure([], (require) => require('@/views/livableManage/garbageCollectionManagement/baseInfoManagement/wasteTreatmentPlant/index.vue')),
            meta: {
                authCode: 'garbageCollectionManagement:basic:factory',
                title: '垃圾处理厂'
            }
        },
        {
            path: 'addGBTreatPlant',
            component: () => require.ensure([], (require) => require('@/views/livableManage/garbageCollectionManagement/baseInfoManagement/wasteTreatmentPlant/add.vue')),
            name: 'garbageCollectionManagement:basic:factory:add',
            meta: {
                parentCode: 'garbageCollectionManagement:basic:factory',
                title: '垃圾处理厂-新增'
            }
        },
        {
            path: 'editGBTreatPlant',
            component: () => require.ensure([], (require) => require('@/views/livableManage/garbageCollectionManagement/baseInfoManagement/wasteTreatmentPlant/detail.vue')),
            name: 'garbageCollectionManagement:basic:factory:edit',
            meta: {
                parentCode: 'garbageCollectionManagement:basic:factory',
                title: '垃圾处理厂-详情'
            }
        },
        {
            path: 'basic/car',
            name: 'garbageCollectionManagement:basic:car',
            component: () => require.ensure([], (require) => require('@/views/livableManage/garbageCollectionManagement/baseInfoManagement/vehicleManagement/index.vue')),
            meta: {
                authCode: 'garbageCollectionManagement:basic:car',
                title: '车辆管理'
            }
        },
        {
            path: 'basic/addCar',
            component: () => require.ensure([], (require) => require('@/views/livableManage/garbageCollectionManagement/baseInfoManagement/vehicleManagement/add.vue')),
            name: 'garbageCollectionManagement:basic:car:add',
            meta: {
                parentCode: 'garbageCollectionManagement:basic:car',
                title: '车辆管理-新增'
            }
        },
        {
            path: 'basic/editCar',
            component: () => require.ensure([], (require) => require('@/views/livableManage/garbageCollectionManagement/baseInfoManagement/vehicleManagement/detail.vue')),
            name: 'garbageCollectionManagement:basic:car:edit',
            meta: {
                parentCode: 'garbageCollectionManagement:basic:car',
                title: '车辆管理-详情'
            }
        },
        {
            path: 'basic/company',
            name: 'garbageCollectionManagement:basic:company',
            component: () => require.ensure([], (require) => require('@/views/livableManage/garbageCollectionManagement/baseInfoManagement/shippingCompany/index.vue')),
            meta: {
                authCode: 'garbageCollectionManagement:basic:company',
                title: '收运公司'
            }
        },
        {
            path: 'basic/addCompany',
            component: () => require.ensure([], (require) => require('@/views/livableManage/garbageCollectionManagement/baseInfoManagement/shippingCompany/add.vue')),
            name: 'garbageCollectionManagement:basic:company:add',
            meta: {
                parentCode: 'garbageCollectionManagement:basic:company',
                title: '收运公司-新增'
            }
        },
        {
            path: 'basic/editCompany',
            component: () => require.ensure([], (require) => require('@/views/livableManage/garbageCollectionManagement/baseInfoManagement/shippingCompany/detail.vue')),
            name: 'garbageCollectionManagement:basic:company:edit',
            meta: {
                parentCode: 'garbageCollectionManagement:basic:company',
                title: '收运公司-详情'
            }
        },
        {
            path: 'collectionPlan',
            component: () => require.ensure([], (require) => require('@/views/livableManage/garbageCollectionManagement/collectionPlan')),
            name: 'garbageCollectionManagement:collectionPlan',
            meta: {
                authCode: 'garbageCollectionManagement:collectionPlan',
                title: '收运计划'
            }
        },
        {
            path: 'collectionPlanAdd',
            component: () => require.ensure([], (require) => require('@/views/livableManage/garbageCollectionManagement/collectionPlan/add')),
            name: 'garbageCollectionManagement:collectionPlan:add',
            meta: {
                parentCode: 'garbageCollectionManagement:collectionPlan',
                title: '收运计划 - 新增'
            }
        },
        {
            path: 'collectionPlanDetail',
            component: () => require.ensure([], (require) => require('@/views/livableManage/garbageCollectionManagement/collectionPlan/detail')),
            name: 'garbageCollectionManagement:collectionPlan:detail',
            meta: {
                parentCode: 'garbageCollectionManagement:collectionPlan',
                title: '收运计划 - 查看'
            }
        },
        {
            path: 'collectionMap',
            component: () => require.ensure([], (require) => require('@/views/livableManage/garbageCollectionManagement/collectionMap/index')),
            name: 'garbageCollectionManagement:collectionMap',
            meta: {
                parentCode: 'garbageCollectionManagement:collectionMap',
                title: '收运地图'
            }
        },
        {
            path: 'basic/enterprise',
            name: 'garbageCollectionManagement:basic:enterprise',
            component: () => require.ensure([], (require) => require('@/views/livableManage/garbageCollectionManagement/baseInfoManagement/enterpriseManagement/index.vue')),
            meta: {
                authCode: 'garbageCollectionManagement:basic:enterprise',
                title: '企业管理'
            }
        },
        {
            path: 'basic/addEnterprise',
            component: () => require.ensure([], (require) => require('@/views/livableManage/garbageCollectionManagement/baseInfoManagement/enterpriseManagement/add.vue')),
            name: 'garbageCollectionManagement:basic:enterprise:add',
            meta: {
                parentCode: 'garbageCollectionManagement:basic:enterprise',
                title: '企业管理-新增'
            }
        },
        {
            path: 'basic/editEnterprise',
            component: () => require.ensure([], (require) => require('@/views/livableManage/garbageCollectionManagement/baseInfoManagement/enterpriseManagement/detail.vue')),
            name: 'garbageCollectionManagement:basic:enterprise:edit',
            meta: {
                parentCode: 'garbageCollectionManagement:basic:enterprise',
                title: '企业管理-详情'
            }
        },
        {
            path: 'basic/point',
            name: 'garbageCollectionManagement:basic:point',
            component: () => require.ensure([], (require) => require('@/views/livableManage/garbageCollectionManagement/baseInfoManagement/collectionPointManagement/index.vue')),
            meta: {
                authCode: 'garbageCollectionManagement:basic:point',
                title: '收运点管理'
            }
        },
        {
            path: 'basic/addPoint',
            component: () => require.ensure([], (require) => require('@/views/livableManage/garbageCollectionManagement/baseInfoManagement/collectionPointManagement/add.vue')),
            name: 'garbageCollectionManagement:basic:point:add',
            meta: {
                parentCode: 'garbageCollectionManagement:basic:point',
                title: '收运点管理-新增'
            }
        },
        {
            path: 'basic/editPoint',
            component: () => require.ensure([], (require) => require('@/views/livableManage/garbageCollectionManagement/baseInfoManagement/collectionPointManagement/detail.vue')),
            name: 'garbageCollectionManagement:basic:point:edit',
            meta: {
                parentCode: 'garbageCollectionManagement:basic:point',
                title: '收运点管理-详情'
            }
        },
        {
            path: 'collectionRecord',
            name: 'garbageCollectionManagement:collectionRecord',
            component: () => require.ensure([], (require) => require('@/views/livableManage/garbageCollectionManagement/collectionRecord/index.vue')),
            meta: {
                authCode: 'garbageCollectionManagement:collectionRecord',
                title: '收运记录'
            }
        },
        {
            path: 'collectionRecordDetail',
            name: 'garbageCollectionManagement:collectionRecord:detail',
            component: () => require.ensure([], (require) => require('@/views/livableManage/garbageCollectionManagement/collectionRecord/detail.vue')),
            meta: {
                parentCode: 'garbageCollectionManagement:collectionRecord',
                title: '收运记录 - 详情'
            }
        },
        {
            path: 'collectionTask',
            name: 'garbageCollectionManagement:collectionTask',
            component: () => require.ensure([], (require) => require('@/views/livableManage/garbageCollectionManagement/collectionTask/index.vue')),
            meta: {
                authCode: 'garbageCollectionManagement:collectionTask',
                title: '收运任务'
            }
        },
        {
            path: 'collectionTaskDetail',
            component: () => require.ensure([], (require) => require('@/views/livableManage/garbageCollectionManagement/collectionTask/detail.vue')),
            name: 'garbageCollectionManagement:collectionTask:detail',
            meta: {
                parentCode: 'garbageCollectionManagement:collectionTask',
                title: '收运任务-详情'
            }
        },
        {
            path: 'collection',
            component: () => require.ensure([], (require) => require('@/views/livableManage/foodWasteManagement/components/addCollectModal.vue')),
            name: 'garbageCollectionManagement:collectionTask:collection',
            meta: {
                parentCode: 'garbageCollectionManagement:collectionTask',
                title: '收运任务-收运'
            }
        },
        {
            path: 'collection',
            component: () => require.ensure([], (require) => require('@/views/livableManage/foodWasteManagement/components/addCollectModal.vue')),
            name: 'garbageCollectionManagement:collectionRecord:collection',
            meta: {
                parentCode: 'garbageCollectionManagement:collectionRecord',
                title: '收运记录-收运'
            }
        },
        {
            path: 'statistic',
            component: () => require.ensure([], (require) => require('@/views/livableManage/garbageCollectionManagement/statisticalAnalysis/index.vue')),
            name: 'garbageCollectionManagement:statistic',
            meta: {
                parentCode: 'garbageCollectionManagement:statistic',
                title: '统计分析'
            }
        },

    ]
}
