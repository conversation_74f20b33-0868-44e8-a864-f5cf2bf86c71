/**
 * 统计分析
 */
export default [
    // 污染源分析
    {
        path: '/hazardAnalysis',
        component: () => require.ensure([], (require) => require('@/views/livableManage/statisticalAnalysis/hazardAnalysis/index.vue')),
        name: 'hazardAnalysis',
        meta: {
            authCode: 'hazardAnalysis',
            title: '污染源分析'
        },
    },
    // 垃圾屋概览
    {
        path: '/garbageHousesOverView',
        component: () => require.ensure([], (require) => require('@/views/livableManage/statisticalAnalysis/garbageHousesOverView/index.vue')),
        name: 'garbageHousesOverView',
        meta: {
            authCode: 'garbageHousesOverView',
            title: '垃圾屋概览'
        },
    },
    // 回收屋概览
    {
        path: '/recyclingHousesOverview',
        component: () => require.ensure([], (require) => require('@/views/livableManage/statisticalAnalysis/recyclingHousesOverview/index.vue')),
        name: 'recyclingHousesOverview',
        meta: {
            authCode: 'recyclingHousesOverview',
            title: '回收屋概览'
        },
    },
    // 污染源监测统计分析
    {
        path: '/polluteMonitorAnalysis',
        component: () => require.ensure([], (require) => require('@/views/livableManage/statisticalAnalysis/polluteMonitorAnalysis/index.vue')),
        name: 'polluteMonitorAnalysis',
        meta: {
            authCode: 'polluteMonitorAnalysis',
            title: '污染源监测统计分析'
        },
    },
    {
      path: '/noiseDetectionAnalysis',
      component: () => require.ensure([], (require) => require('@/views/livableManage/statisticalAnalysis/noiseDetectionAnalysis/index.vue')),
      name: 'noiseDetectionAnalysis',
      meta: {
        authCode: 'noiseDetectionAnalysis',
        title: '噪音检测统计分析'
      },
    },
    {
      path: '/gardenEnvironment',
      component: () => require.ensure([], (require) => require('@/views/livableManage/statisticalAnalysis/gardenEnvironment/index.vue')),
      name: 'gardenEnvironment',
      meta: {
        authCode: 'gardenEnvironment',
        title: '园林环境监测统计分析'
      },
    }
]
