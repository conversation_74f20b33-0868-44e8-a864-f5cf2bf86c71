/**
 * 自动灌溉
 */
export default [
    {
        path: '/autoIrrigate',
        redirect: '/autoIrrigate/equipmentManagement',
        meta: {
            authCode: 'autoIrrigate',
            title: '自动灌溉'
        },
        children: [
            {
                path: 'autoIrrigateMap',
                component: () => require.ensure([], (require) => require('@/views/livableManage/autoIrrigate/autoIrrigateMap')),
                // component: () => import('@/views/livableManage/autoIrrigate/autoIrrigateMap'),
                name: 'autoIrrigate:map',
                meta: {
                    authCode: 'autoIrrigate:map',
                    title: '灌溉地图'
                }
            },
            {
                path: 'equipmentManagement',
                name: 'autoIrrigate:deviceManage',
                component: () => require.ensure([], (require) => require('@/views/livableManage/autoIrrigate/equipmentManagement')),
                // component: () => import('@/views/livableManage/autoIrrigate/equipmentManagement'),
                meta: {
                    authCode: 'autoIrrigate:deviceManage',
                    title: '设备管理'
                }
            },
            {
                path: 'groupManagement',
                name: 'autoIrrigate:groupManage',
                component: () => require.ensure([], (require) => require('@/views/livableManage/autoIrrigate/groupManagement/index')),
                // component: () => import('@/views/livableManage/autoIrrigate/groupManagement/index'),
                meta: {
                    authCode: 'autoIrrigate:groupManage',
                    title: '分组管理'
                }
            },
            {
                path: 'groupManagementAdd',
                name: 'autoIrrigate:groupManagementAdd',
                component: () => require.ensure([], (require) => require('@/views/livableManage/autoIrrigate/groupManagement/add')),
                // component: () => import('@/views/livableManage/autoIrrigate/groupManagement/add'),
                meta: {
                    parentCode: 'autoIrrigate:groupManage',
                    title: '分组管理-新增'
                }
            },
            {
                path: 'groupManagementDetail',
                name: 'autoIrrigate:groupManagementDetail',
                component: () => require.ensure([], (require) => require('@/views/livableManage/autoIrrigate/groupManagement/detail')),
                // component: () => import('@/views/livableManage/autoIrrigate/groupManagement/detail'),
                meta: {
                    parentCode: 'autoIrrigate:groupManage',
                    title: '分组管理-详情'
                }
            },

            {
                path: 'equipmentManagementDetail',
                component: () => require.ensure([], (require) => require('@/views/livableManage/autoIrrigate/equipmentManagement/detail')),
                // component: () => import('@/views/livableManage/autoIrrigate/equipmentManagement/detail'),
                name: 'autoIrrigate:equipmentManagementDetail',
                meta: {
                    parentCode: 'autoIrrigate:deviceManage',
                    title: '设备管理-详情'
                }
            },
            {
                path: 'equipmentManagementEdit',
                component: () => require.ensure([], (require) => require('@/views/livableManage/autoIrrigate/equipmentManagement/edit')),
                // component: () => import('@/views/livableManage/autoIrrigate/equipmentManagement/edit'),
                name: 'autoIrrigate:equipmentManagementEdit',
                meta: {
                    parentCode: 'autoIrrigate:deviceManage',
                    title: '设备管理-编辑'
                }
            },
            {
                path: 'irrigationPlan',
                name: 'autoIrrigate:plan',
                component: () => require.ensure([], (require) => require('@/views/livableManage/autoIrrigate/irrigationPlan/index')),
                // component: () => import('@/views/livableManage/autoIrrigate/irrigationPlan/index'),
                meta: {
                    authCode: 'autoIrrigate:plan',
                    title: '灌溉计划'
                }
            },
            {
                path: 'irrigationPlanAdd',
                name: 'irrigationPlanAdd',
                component: () => require.ensure([], (require) => require('@/views/livableManage/autoIrrigate/irrigationPlan/add')),
                // component: () => import('@/views/livableManage/autoIrrigate/irrigationPlan/add'),
                meta: {
                    parentCode: 'autoIrrigate:plan',
                    title: '灌溉计划-新增'
                }
            },
            {
                path: 'irrigationPlanDetail',
                name: 'irrigationPlanDetail',
                component: () => require.ensure([], (require) => require('@/views/livableManage/autoIrrigate/irrigationPlan/detail')),
                // component: () => import('@/views/livableManage/autoIrrigate/irrigationPlan/detail'),
                meta: {
                    parentCode: 'autoIrrigate:plan',
                    title: '灌溉计划-详情'
                }
            },
            {
                path: 'alarmManagement',
                name: 'autoIrrigate:alarmManagement',
                component: () => require.ensure([], (require) => require('@/views/livableManage/autoIrrigate/alarmManagement')),
                // component: () => import('@/views/livableManage/autoIrrigate/alarmManagement'),
                meta: {
                    authCode: 'autoIrrigate:alarmManagement',
                    title: '告警管理'
                }
            },
            {
                path: 'irrigateGeneral',
                name: 'autoIrrigate:overview',
                component: () => require.ensure([], (require) => require('@/views/livableManage/autoIrrigate/irrigateGeneral/index')),
                meta: {
                    authCode: 'autoIrrigate:overview',
                    title: '灌溉概况'
                }
            },
            {
                path: 'statisticsAnalyze',
                name: 'autoIrrigate:statisticsAnalyze',
                component: () => require.ensure([], (require) => require('@/views/livableManage/autoIrrigate/statisticsAnalyze/index')),
                meta: {
                    authCode: 'autoIrrigate:statisticsAnalyze',
                    title: '统计分析'
                }
            }
        ]
    },
]
