/**
 * 环境监测
 */
export default [
    // 环境概览
    {
        path: '/environmentalMap',
        component: () => require.ensure([], (require) => require('@/views/livableManage/environmentalMap/index')),
        name: 'environmentOverView',
        meta: {
            authCode: 'environmentOverView',
            title: '环境概览'
        }
    },
    // 水质环境概况
    {
        path: '/waterEnvironmentAnalysis',
        component: () => require.ensure([], (require) => require('@/views/livableManage/environmentalMonitoring/waterEnvironmentAnalysis/index')),
        name: 'waterEnvironmentAnalysis',
        meta: {
            authCode: 'waterEnvironmentAnalysis',
            title: '水质环境概况'
        }
    },
    // 空气环境概况
    {
        path: '/airStatisticalAnalysis',
        component: () => require.ensure([], (require) => require('@/views/livableManage/airEnvironmentAnalysis/index.vue')),
        name: 'airStatisticalAnalysis',
        meta: {
            authCode: 'airStatisticalAnalysis',
            title: '空气环境概况'
        }
    },
    // 环境综合告警
    {
        path: '/environmentTotalAlarm',
        component: () => require.ensure([], (require) => require('@/views/livableManage/environmentalMonitoring/alarmManagement/index')),
        name: 'environmentTotalAlarm',
        meta: {
            authCode: 'environmentTotalAlarm',
            title: '环境综合告警'
        }
    },
    // 环境设置
    {
        path: '/environmentalSet',
        component: () => require.ensure([], (require) => require('@/views/livableManage/environmentalMonitoring/environmentSetting/index.vue')),
        name: 'environmentMonitoring:envSet',
        meta: {
            authCode: 'environmentMonitoring:envSet',
            title: '环境设置'
        }
    },
    // 空气环境分析
    {
        path: '/airEnvironmentAnalysis',
        component: () => require.ensure([], (require) => require('@/views/livableManage/airStatisticalAnalysis/index.vue')),
        name: 'airEnvironmentAnalysis',
        meta: {
            authCode: 'airEnvironmentAnalysis',
            title: '空气环境分析'
        }
    },
    // 水质环境分析
    {
        path: '/waterQualityAnalysis',
        component: () => require.ensure([], (require) => require('@/views/livableManage/waterStatisticalAnalysis/index.vue')),
        name: 'waterQualityAnalysis',
        meta: {
            authCode: 'waterQualityAnalysis',
            title: '水质环境分析'
        }
    },
    // 电子围栏概览
    {
        path: '/electronicFenceAnalysis',
        component: () => require.ensure([], (require) => require('@/views/livableManage/electronicFenceAnalysis/index.vue')),
        name: 'electronicFenceAnalysis',
        meta: {
            authCode: 'electronicFenceAnalysis',
            title: '电子围栏概览'
        }
    },
    // 园林土壤
    {
        path: '/parkSoil',
        redirect: '/parkSoil/equipmentManagement',
        meta: {
            authCode: 'environmentMonitoring:GardenSoil',
            title: '园林土壤'
        },
        children: [
            {
                path: 'equipmentManagement',
                component: () => require.ensure([], (require) => require('@/views/livableManage/parkSoil/equipmentManagement')),
                name: 'environmentMonitoring:GardenSoil:deviceManage',
                meta: {
                    authCode: 'environmentMonitoring:GardenSoil:deviceManage',
                    title: '设备管理'
                }
            },
            {
                path: 'equipmentManagementDetail',
                component: () => require.ensure([], (require) => require('@/views/livableManage/parkSoil/equipmentManagement/detail')),
                name: 'equipmentManagementDetail',
                meta: {
                    parentCode: 'environmentMonitoring:GardenSoil:deviceManage',
                    title: '设备管理-详情'
                }
            },
            {
                path: 'equipmentManagementEdit',
                component: () => require.ensure([], (require) => require('@/views/livableManage/parkSoil/equipmentManagement/edit')),
                name: 'equipmentManagementEdit',
                meta: {
                    parentCode: 'environmentMonitoring:GardenSoil:deviceManage',
                    title: '设备管理-编辑'
                }
            },
            {
                path: 'monitoringData',
                component: () => require.ensure([], (require) => require('@/views/livableManage/parkSoil/monitoringData/index')),
                name: 'environmentMonitoring:GardenSoil:MonitoringData',
                meta: {
                    authCode: 'environmentMonitoring:GardenSoil:MonitoringData',
                    title: '监测数据'
                }
            },
            {
                path: 'alarmManagement',
                component: () => require.ensure([], (require) => require('@/views/livableManage/parkSoil/alarmManagement')),
                name: 'environmentMonitoring:GardenSoil:alarm',
                meta: {
                    authCode: 'environmentMonitoring:GardenSoil:alarm',
                    title: '告警管理'
                }
            }

        ]

    },
    // 空气质量
    {
        path: '/airQuality',
        redirect: '/airQuality/devicement',
        meta: {
            authCode: 'environmentMonitoring:airQuality',
            title: '空气质量'
        },
        children: [
            {
                path: 'devicement',
                component: () => require.ensure([], (require) => require('@/views/livableManage/airQuality/devicement')),
                name: 'environmentMonitoring:airQuality:deviceManage',
                meta: {
                    authCode: 'environmentMonitoring:airQuality:deviceManage',
                    title: '设备管理'
                }
            },
            {
                path: 'deviceManagementDetail',
                component: () => require.ensure([], (require) => require('@/views/livableManage/airQuality/devicement/detail')),
                name: 'airQuality:deviceManagementDetail',
                meta: {
                    parentCode: 'environmentMonitoring:airQuality:deviceManage',
                    title: '设备管理-详情'
                }
            },
            {
                path: 'devicementEdit',
                component: () => require.ensure([], (require) => require('@/views/livableManage/airQuality/devicement/edit')),
                name: 'airQuality:deviceManage:edit',
                meta: {
                    parentCode: 'environmentMonitoring:airQuality:deviceManage',
                    title: '设备管理-编辑'
                }
            },
            {
                path: 'monitoringData',
                component: () => require.ensure([], (require) => require('@/views/livableManage/airQuality/monitoringData/index')),
                name: 'environmentMonitoring:airQuality:MonitoringData',
                meta: {
                    authCode: 'environmentMonitoring:airQuality:MonitoringData',
                    title: '监测数据'
                }
            },
            {
                path: 'alarmManagement',
                component: () => require.ensure([], (require) => require('@/views/livableManage/airQuality/alarmManagement')),
                name: 'environmentMonitoring:airQuality:alarm',
                meta: {
                    authCode: 'environmentMonitoring:airQuality:alarm',
                    title: '告警管理'
                }
            }
        ]
    },
    // 水质监测
    {
        path: '/waterQuality',
        redirect: '/waterQuality/devicement',
        meta: {
            authCode: 'environmentMonitoring:waterQualityTest',
            title: '水质监测'
        },
        children: [
            {
                path: 'devicement',
                component: () => require.ensure([], (require) => require('@/views/livableManage/waterQuality/devicement')),
                name: 'environmentMonitoring:waterQualityTest:deviceManage',
                meta: {
                    authCode: 'environmentMonitoring:waterQualityTest:deviceManage',
                    title: '设备管理'
                }
            },
            {
                path: 'deviceManagementDetail',
                component: () => require.ensure([], (require) => require('@/views/livableManage/waterQuality/devicement/detail')),
                name: 'waterQualityTest:deviceManagementDetail',
                meta: {
                    parentCode: 'environmentMonitoring:waterQualityTest:deviceManage',
                    title: '设备管理-详情'
                }
            },
            {
                path: 'devicementEdit',
                component: () => require.ensure([], (require) => require('@/views/livableManage/waterQuality/devicement/edit')),
                name: 'waterQualityTest:devicementEdit',
                meta: {
                    parentCode: 'environmentMonitoring:waterQualityTest:deviceManage',
                    title: '设备管理-编辑'
                }
            },
            {
                path: 'monitoringData',
                component: () => require.ensure([], (require) => require('@/views/livableManage/waterQuality/monitoringData/index')),
                name: 'environmentMonitoring:waterQualityTest:MonitoringData',
                meta: {
                    authCode: 'environmentMonitoring:waterQualityTest:MonitoringData',
                    title: '监测数据'
                }
            },
            {
                path: 'alarmManagement',
                component: () => require.ensure([], (require) => require('@/views/livableManage/waterQuality/alarmManagement')),
                name: 'environmentMonitoring:waterQualityTest:alarm',
                meta: {
                    authCode: 'environmentMonitoring:waterQualityTest:alarm',
                    title: '告警管理'
                }
            }
        ]
    },
    // 气象环境
    {
        path: '/meteorological',
        redirect: '/meteorological/equipmentManagement',
        meta: {
            authCode: 'environmentMonitoring:meteorologicalEnvironment',
            title: '气象环境'
        },
        children: [
            {
                path: 'equipmentManagement',
                component: () => require.ensure([], (require) => require('@/views/livableManage/meteorological/devicement')),
                name: 'environmentMonitoring:meteorologicalEnvironment:deviceManage',
                meta: {
                    authCode: 'environmentMonitoring:meteorologicalEnvironment:deviceManage',
                    title: '设备管理'
                }
            },
            {
                path: 'deviceManagementDetail',
                component: () => require.ensure([], (require) => require('@/views/livableManage/meteorological/devicement/detail')),
                name: 'meteorologicalEnvironment:deviceManagementDetail',
                meta: {
                    parentCode: 'environmentMonitoring:meteorologicalEnvironment:deviceManage',
                    title: '设备管理-详情'
                }
            },
            {
                path: 'devicementEdit',
                component: () => require.ensure([], (require) => require('@/views/livableManage/meteorological/devicement/edit')),
                name: 'meteorologicalEnvironment:devicementEdit',
                meta: {
                    parentCode: 'environmentMonitoring:meteorologicalEnvironment:deviceManage',
                    title: '设备管理-编辑'
                }
            },
            {
                path: 'monitoringData',
                component: () => require.ensure([], (require) => require('@/views/livableManage/meteorological/monitoringData/index')),
                name: 'environmentMonitoring:meteorologicalEnvironment:MonitoringData',
                meta: {
                    authCode: 'environmentMonitoring:meteorologicalEnvironment:MonitoringData',
                    title: '监测数据'
                }
            },
            {
                path: 'alarmManagement',
                component: () => require.ensure([], (require) => require('@/views/livableManage/meteorological/alarmManagement')),
                name: 'environmentMonitoring:meteorologicalEnvironment:alarm',
                meta: {
                    authCode: 'environmentMonitoring:meteorologicalEnvironment:alarm',
                    title: '告警管理'
                }
            }
        ]
    },
    // 噪声环境
    {
        path: '/noise',
        redirect: '/noise/equipmentManagement',
        meta: {
            authCode: 'environmentMonitoring:noiseEnvironment',
            title: '噪声环境'
        },
        children: [
            {
                path: 'equipmentManagement',
                component: () => require.ensure([], (require) => require('@/views/livableManage/noise/devicement')),
                name: 'environmentMonitoring:noiseEnvironment:deviceManage',
                meta: {
                    authCode: 'environmentMonitoring:noiseEnvironment:deviceManage',
                    title: '设备管理'
                }
            },
            {
                path: 'deviceManagementDetail',
                component: () => require.ensure([], (require) => require('@/views/livableManage/noise/devicement/detail')),
                name: 'noiseEnvironment:deviceManagementDetail',
                meta: {
                    parentCode: 'environmentMonitoring:noiseEnvironment:deviceManage',
                    title: '设备管理-详情'
                }
            },
            {
                path: 'devicementEdit',
                component: () => require.ensure([], (require) => require('@/views/livableManage/noise/devicement/edit')),
                name: 'noiseEnvironment:devicementEdit',
                meta: {
                    parentCode: 'environmentMonitoring:noiseEnvironment:deviceManage',
                    title: '设备管理-编辑'
                }
            },
            {
                path: 'monitoringData',
                component: () => require.ensure([], (require) => require('@/views/livableManage/noise/monitoringData/index')),
                name: 'environmentMonitoring:noiseEnvironment:MonitoringData',
                meta: {
                    authCode: 'environmentMonitoring:noiseEnvironment:MonitoringData',
                    title: '监测数据'
                }
            },
            {
                path: 'alarmManagement',
                component: () => require.ensure([], (require) => require('@/views/livableManage/noise/alarmManagement')),
                name: 'environmentMonitoring:noiseEnvironment:alarm',
                meta: {
                    authCode: 'environmentMonitoring:noiseEnvironment:alarm',
                    title: '告警管理'
                }
            }
        ]
    },
    // 积水监测
    {
        path: '/pondingMonitoring',
        redirect: '/pondingMonitoring/deviceManagement',
        meta: {
            authCode: 'environmentMonitoring:pondingMonitoring',
            title: '积水监测'
        },
        children: [
            {
                path: 'deviceManagement',
                component: () => require.ensure([], (require) => require('@/views/livableManage/pondingMonitoring/deviceManagement')),
                name: 'environmentMonitoring:pondingMonitoring:deviceManage',
                meta: {
                    authCode: 'environmentMonitoring:pondingMonitoring:deviceManage',
                    title: '设备管理'
                }
            },
            {
                path: 'deviceManagementDetail',
                component: () => require.ensure([], (require) => require('@/views/livableManage/pondingMonitoring/deviceManagement/detail')),
                name: 'pondingMonitoring:deviceManagementDetail',
                meta: {
                    parentCode: 'environmentMonitoring:pondingMonitoring:deviceManage',
                    title: '设备管理-详情'
                }
            },
            {
                path: 'deviceManagementEdit',
                component: () => require.ensure([], (require) => require('@/views/livableManage/pondingMonitoring/deviceManagement/edit')),
                name: 'pondingMonitoring:deviceManagementEdit',
                meta: {
                    parentCode: 'environmentMonitoring:pondingMonitoring:deviceManage',
                    title: '设备管理-编辑'
                }
            },
            {
                path: 'monitoringData',
                component: () => require.ensure([], (require) => require('@/views/livableManage/pondingMonitoring/monitoringData/index')),
                name: 'environmentMonitoring:pondingMonitoring:MonitoringData',
                meta: {
                    authCode: 'environmentMonitoring:pondingMonitoring:MonitoringData',
                    title: '监测数据'
                }
            },
            {
                path: 'alarmManagement',
                component: () => require.ensure([], (require) => require('@/views/livableManage/pondingMonitoring/alarmManagement')),
                name: 'environmentMonitoring:pondingMonitoring:alarm',
                meta: {
                    authCode: 'environmentMonitoring:pondingMonitoring:alarm',
                    title: '告警管理'
                }
            }
        ]
    },
    // 雨情监测
    {
        path: '/rainfallMonitoring',
        redirect: '/rainfallMonitoring/deviceManagement',
        meta: {
            authCode: 'environmentMonitoring:rainfallMonitoring',
            title: '雨情监测'
        },
        children: [
            {
                path: 'deviceManagement',
                component: () => require.ensure([], (require) => require('@/views/livableManage/rainfallMonitoring/deviceManagement')),
                name: 'environmentMonitoring:rainfallMonitoring:deviceManage',
                meta: {
                    authCode: 'environmentMonitoring:rainfallMonitoring:deviceManage',
                    title: '设备管理'
                }
            },
            {
                path: 'deviceManagementDetail',
                component: () => require.ensure([], (require) => require('@/views/livableManage/rainfallMonitoring/deviceManagement/detail')),
                name: 'rainfallMonitoring:deviceManagementDetail',
                meta: {
                    parentCode: 'environmentMonitoring:rainfallMonitoring:deviceManage',
                    title: '设备管理-详情'
                }
            },
            {
                path: 'deviceManagementEdit',
                component: () => require.ensure([], (require) => require('@/views/livableManage/rainfallMonitoring/deviceManagement/edit')),
                name: 'rainfallMonitoring:deviceManagementEdit',
                meta: {
                    parentCode: 'environmentMonitoring:rainfallMonitoring:deviceManage',
                    title: '设备管理-编辑'
                }
            },
            {
                path: 'monitoringData',
                component: () => require.ensure([], (require) => require('@/views/livableManage/rainfallMonitoring/monitoringData/index')),
                name: 'environmentMonitoring:rainfallMonitoring:MonitoringData',
                meta: {
                    authCode: 'environmentMonitoring:rainfallMonitoring:MonitoringData',
                    title: '监测数据'
                }
            },
            {
                path: 'alarmManagement',
                component: () => require.ensure([], (require) => require('@/views/livableManage/rainfallMonitoring/alarmManagement')),
                name: 'environmentMonitoring:rainfallMonitoring:alarm',
                meta: {
                    authCode: 'environmentMonitoring:rainfallMonitoring:alarm',
                    title: '告警管理'
                }
            }
        ]
    },
    // 湖渠监测
    {
        path: '/lakeMonitoring',
        redirect: '/lakeMonitoring/deviceManagement',
        meta: {
            authCode: 'environmentMonitoring:lakeMonitoring',
            title: '湖渠监测'
        },
        children: [
            {
                path: 'deviceManagement',
                component: () => require.ensure([], (require) => require('@/views/livableManage/lakeMonitoring/deviceManagement')),
                name: 'environmentMonitoring:lakeMonitoring:deviceManage',
                meta: {
                    authCode: 'environmentMonitoring:lakeMonitoring:deviceManage',
                    title: '设备管理'
                }
            },
            {
                path: 'deviceManagementDetail',
                component: () => require.ensure([], (require) => require('@/views/livableManage/lakeMonitoring/deviceManagement/detail')),
                name: 'lakeMonitoring:deviceManagementDetail',
                meta: {
                    parentCode: 'environmentMonitoring:lakeMonitoring:deviceManage',
                    title: '设备管理-详情'
                }
            },
            {
                path: 'deviceManagementEdit',
                component: () => require.ensure([], (require) => require('@/views/livableManage/lakeMonitoring/deviceManagement/edit')),
                name: 'lakeMonitoring:deviceManagementEdit',
                meta: {
                    parentCode: 'environmentMonitoring:lakeMonitoring:deviceManage',
                    title: '设备管理-编辑'
                }
            },
            {
                path: 'monitoringData',
                component: () => require.ensure([], (require) => require('@/views/livableManage/lakeMonitoring/monitoringData/index')),
                name: 'environmentMonitoring:lakeMonitoring:MonitoringData',
                meta: {
                    authCode: 'environmentMonitoring:lakeMonitoring:MonitoringData',
                    title: '监测数据'
                }
            },
            {
                path: 'alarmManagement',
                component: () => require.ensure([], (require) => require('@/views/livableManage/lakeMonitoring/alarmManagement')),
                name: 'environmentMonitoring:lakeMonitoring:alarm',
                meta: {
                    authCode: 'environmentMonitoring:lakeMonitoring:alarm',
                    title: '告警管理'
                }
            }
        ]
    },
    // 渣土监测
    {
        path: '/muckMonitoring',
        redirect: '/muckMonitoring/muckAlarmMap',
        meta: {
            authCode: 'environmentMonitoring:muckMonitoring',
            title: '渣土监测'
        },
        children: [
            {
                path: 'muckAlarmMap',
                component: () => require.ensure([], (require) => require('@/views/livableManage/muckMonitoring/muckAlarmMap/index.vue')),
                name: 'environmentMonitoring:muckMonitoring:alarmMap',
                meta: {
                    authCode: 'environmentMonitoring:muckMonitoring:alarmMap',
                    title: '告警地图'
                }
            },
            {
                path: 'alarmRecords',
                component: () => require.ensure([], (require) => require('@/views/livableManage/muckMonitoring/alarmRecords/index.vue')),
                name: 'environmentMonitoring:muckMonitoring:alarmRecords',
                meta: {
                    authCode: 'environmentMonitoring:muckMonitoring:alarmRecords',
                    title: '告警记录'
                }
            },
            {
                path: 'alarmRecordsDetail',
                component: () => require.ensure([], (require) => require('@/views/livableManage/muckMonitoring/alarmRecords/detail.vue')),
                name: 'muckMonitoring:alarmRecordsDetail',
                meta: {
                    parentCode: 'environmentMonitoring:muckMonitoring:alarmRecords',
                    title: '告警记录-详情'
                }
            },
        ]
    },
    // 火情监测
    {
        path: '/fireMonitoring',
        redirect: '/fireMonitoring/monitoringMap',
        meta: {
            authCode: 'environmentMonitoring:fireMonitoring',
            title: '火情监测'
        },
        children: [
            {
                path: 'monitoringMap',
                component: () => require.ensure([], (require) => require('@/views/livableManage/fireMonitoring/monitoringMap/index.vue')),
                name: 'environmentMonitoring:fireMonitoring:monitoringMap',
                meta: {
                    authCode: 'environmentMonitoring:fireMonitoring:monitoringMap',
                    title: '监测地图'
                }
            },
            {
                path: 'alarmManage',
                component: () => require.ensure([], (require) => require('@/views/livableManage/fireMonitoring/alarmRecords/index.vue')),
                name: 'environmentMonitoring:fireMonitoring:alarmManage',
                meta: {
                    authCode: 'environmentMonitoring:fireMonitoring:alarmManage',
                    title: '告警管理'
                }
            },
            {
                path: 'alarmRecordsDetail',
                component: () => require.ensure([], (require) => require('@/views/livableManage/fireMonitoring/alarmRecords/detail.vue')),
                name: 'fireMonitoring:alarmRecordsDetail',
                meta: {
                    parentCode: 'environmentMonitoring:fireMonitoring:alarmManage',
                    title: '告警记录-详情'
                }
            },
        ]
    },
    // 深基监测
    {
        path: '/deepFoundationMonitoring',
        redirect: '/deepFoundationMonitoring/deviceManage',
        meta: {
            authCode: 'environmentMonitoring:deepFoundationMonitoring',
            title: '深基监测'
        },
        children: [
            {
                path: 'deviceManage',
                component: () => require.ensure([], (require) => require('@/views/livableManage/deepFoundationMonitoring/deviceManagement/index.vue')),
                name: 'environmentMonitoring:deepFoundationMonitoring:deviceManage',
                meta: {
                    authCode: 'environmentMonitoring:deepFoundationMonitoring:deviceManage',
                    title: '设备管理'
                }
            },
            {
                path: 'deviceManagementDetail',
                component: () => require.ensure([], (require) => require('@/views/livableManage/deepFoundationMonitoring/deviceManagement/detail')),
                name: 'deepFoundationMonitoring:deviceManagementDetail',
                meta: {
                    parentCode: 'environmentMonitoring:deepFoundationMonitoring:deviceManage',
                    title: '设备管理-详情'
                }
            },
            {
                path: 'deviceManagementEdit',
                component: () => require.ensure([], (require) => require('@/views/livableManage/deepFoundationMonitoring/deviceManagement/edit')),
                name: 'deepFoundationMonitoring:deviceManagementEdit',
                meta: {
                    parentCode: 'environmentMonitoring:deepFoundationMonitoring:deviceManage',
                    title: '设备管理-编辑'
                }
            },
            {
                path: 'monitorManage',
                component: () => require.ensure([], (require) => require('@/views/livableManage/deepFoundationMonitoring/monitoringData/index.vue')),
                name: 'environmentMonitoring:deepFoundationMonitoring:monitorManage',
                meta: {
                    authCode: 'environmentMonitoring:deepFoundationMonitoring:monitorManage',
                    title: '监测数据'
                }
            },
            {
                path: 'alarmManage',
                component: () => require.ensure([], (require) => require('@/views/livableManage/deepFoundationMonitoring/alarmRecords/index.vue')),
                name: 'environmentMonitoring:deepFoundationMonitoring:alarmManage',
                meta: {
                    authCode: 'environmentMonitoring:deepFoundationMonitoring:alarmManage',
                    title: '告警管理'
                }
            },
        ]
    },
    // 危废监测
    {
        path: '/hazardousWaste',
        redirect: '/hazardousWaste/hazardManage',
        meta: {
            authCode: 'environmentMonitoring:hazardousWaste',
            title: '危废监测'
        },
        children: [
            {
                path: 'hazardManage',
                component: () => require.ensure([], (require) => require('@/views/livableManage/hazardousWaste/hazardManage/index.vue')),
                name: 'environmentMonitoring:hazardousWaste:hazardManage',
                meta: {
                    authCode: 'environmentMonitoring:hazardousWaste:hazardManage',
                    title: '危险源管理'
                }
            },
            {
                path: 'hazardManageDetail',
                component: () => require.ensure([], (require) => require('@/views/livableManage/hazardousWaste/hazardManage/detail.vue')),
                name: 'hazardousWaste:hazardManageDetail',
                meta: {
                    parentCode: 'environmentMonitoring:hazardousWaste:hazardManage',
                    title: '危险源管理-详情'
                }
            },
            {
                path: 'hazardManageAdd',
                component: () => require.ensure([], (require) => require('@/views/livableManage/hazardousWaste/hazardManage/add.vue')),
                name: 'hazardousWaste:hazardManageAdd',
                meta: {
                    parentCode: 'environmentMonitoring:hazardousWaste:hazardManage',
                    title: '危险源管理-新增'
                }
            },
            {
                path: 'hazardManageEdit',
                component: () => require.ensure([], (require) => require('@/views/livableManage/hazardousWaste/hazardManage/edit.vue')),
                name: 'hazardousWaste:hazardManageEdit',
                meta: {
                    parentCode: 'environmentMonitoring:hazardousWaste:hazardManage',
                    title: '危险源管理-编辑'
                }
            },
            {
                path: 'deviceManage',
                component: () => require.ensure([], (require) => require('@/views/livableManage/hazardousWaste/deviceManage/index.vue')),
                name: 'environmentMonitoring:hazardousWaste:deviceManage',
                meta: {
                    authCode: 'environmentMonitoring:hazardousWaste:deviceManage',
                    title: '设备管理'
                }
            },
            {
                path: 'deviceManageDetail',
                component: () => require.ensure([], (require) => require('@/views/livableManage/hazardousWaste/deviceManage/detail.vue')),
                name: 'hazardousWaste:deviceManageDetail',
                meta: {
                    parentCode: 'environmentMonitoring:hazardousWaste:deviceManage',
                    title: '设备管理-详情'
                }
            },
            {
                path: 'deviceManageEdit',
                component: () => require.ensure([], (require) => require('@/views/livableManage/hazardousWaste/deviceManage/edit.vue')),
                name: 'hazardousWaste:deviceManageEdit',
                meta: {
                    parentCode: 'environmentMonitoring:hazardousWaste:deviceManage',
                    title: '设备管理-编辑'
                }
            },
            {
                path: 'monitorManage',
                component: () => require.ensure([], (require) => require('@/views/livableManage/hazardousWaste/monitorManage/index.vue')),
                name: 'environmentMonitoring:hazardousWaste:monitorManage',
                meta: {
                    authCode: 'environmentMonitoring:hazardousWaste:monitorManage',
                    title: '监测数据管理'

                }
            },
            {
                path: 'alarmManage',
                component: () => require.ensure([], (require) => require('@/views/livableManage/hazardousWaste/alarmManage/index.vue')),
                name: 'environmentMonitoring:hazardousWaste:alarmManage',
                meta: {
                    parentCode: 'environmentMonitoring:hazardousWaste:alarmManage',
                    title: '告警管理'
                }
            }]
    }
]
