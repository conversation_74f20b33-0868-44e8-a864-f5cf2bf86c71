/**
 * 餐厨垃圾管理
 */
export default {
    path: '/kitchenWasteManagement',
    redirect: '/kitchenWasteManagement/basic/factory',
    meta: {
        authCode: 'kitchenWasteManagement:basic',
        title: '餐厨垃圾管理'
    },
    children: [
        {
            path: 'basic/factory',
            name: 'kitchenWasteManagement:basic:factory',
            component: () => require.ensure([], (require) => require('@/views/livableManage/foodWasteManagement/baseInfoManagement/wasteTreatmentPlant/index.vue')),
            meta: {
                authCode: 'kitchenWasteManagement:basic:factory',
                title: '垃圾处理厂'
            }
        },
        {
            path: 'addGBTreatPlant',
            component: () => require.ensure([], (require) => require('@/views/livableManage/foodWasteManagement/baseInfoManagement/wasteTreatmentPlant/add.vue')),
            name: 'kitchenWasteManagement:basic:factory:add',
            meta: {
                parentCode: 'kitchenWasteManagement:basic:factory',
                title: '垃圾处理厂-新增'
            }
        },
        {
            path: 'editGBTreatPlant',
            component: () => require.ensure([], (require) => require('@/views/livableManage/foodWasteManagement/baseInfoManagement/wasteTreatmentPlant/detail.vue')),
            name: 'kitchenWasteManagement:basic:factory:edit',
            meta: {
                parentCode: 'kitchenWasteManagement:basic:factory',
                title: '垃圾处理厂-详情'
            }
        },
        {
            path: 'basic/car',
            name: 'kitchenWasteManagement:basic:car',
            component: () => require.ensure([], (require) => require('@/views/livableManage/foodWasteManagement/baseInfoManagement/vehicleManagement/index.vue')),
            meta: {
                authCode: 'kitchenWasteManagement:basic:car',
                title: '车辆管理'
            }
        },
        {
            path: 'basic/addCar',
            component: () => require.ensure([], (require) => require('@/views/livableManage/foodWasteManagement/baseInfoManagement/vehicleManagement/add.vue')),
            name: 'kitchenWasteManagement:basic:car:add',
            meta: {
                parentCode: 'kitchenWasteManagement:basic:car',
                title: '车辆管理-新增'
            }
        },
        {
            path: 'basic/editCar',
            component: () => require.ensure([], (require) => require('@/views/livableManage/foodWasteManagement/baseInfoManagement/vehicleManagement/detail.vue')),
            name: 'kitchenWasteManagement:basic:car:edit',
            meta: {
                parentCode: 'kitchenWasteManagement:basic:car',
                title: '车辆管理-详情'
            }
        },
        {
            path: 'basic/company',
            name: 'kitchenWasteManagement:basic:company',
            component: () => require.ensure([], (require) => require('@/views/livableManage/foodWasteManagement/baseInfoManagement/shippingCompany/index.vue')),
            meta: {
                authCode: 'kitchenWasteManagement:basic:company',
                title: '收运公司'
            }
        },
        {
            path: 'basic/addCompany',
            component: () => require.ensure([], (require) => require('@/views/livableManage/foodWasteManagement/baseInfoManagement/shippingCompany/add.vue')),
            name: 'kitchenWasteManagement:basic:company:add',
            meta: {
                parentCode: 'kitchenWasteManagement:basic:company',
                title: '收运公司-新增'
            }
        },
        {
            path: 'basic/editCompany',
            component: () => require.ensure([], (require) => require('@/views/livableManage/foodWasteManagement/baseInfoManagement/shippingCompany/detail.vue')),
            name: 'kitchenWasteManagement:basic:company:edit',
            meta: {
                parentCode: 'kitchenWasteManagement:basic:company',
                title: '收运公司-详情'
            }
        },
        {
            path: 'basic/enterprise',
            name: 'kitchenWasteManagement:basic:enterprise',
            component: () => require.ensure([], (require) => require('@/views/livableManage/foodWasteManagement/baseInfoManagement/enterpriseManagement/index.vue')),
            meta: {
                authCode: 'kitchenWasteManagement:basic:enterprise',
                title: '企业管理'
            }
        },
        {
            path: 'basic/addEnterprise',
            component: () => require.ensure([], (require) => require('@/views/livableManage/foodWasteManagement/baseInfoManagement/enterpriseManagement/add.vue')),
            name: 'kitchenWasteManagement:basic:enterprise:add',
            meta: {
                parentCode: 'kitchenWasteManagement:basic:enterprise',
                title: '企业管理-新增'
            }
        },
        {
            path: 'basic/editEnterprise',
            component: () => require.ensure([], (require) => require('@/views/livableManage/foodWasteManagement/baseInfoManagement/enterpriseManagement/detail.vue')),
            name: 'kitchenWasteManagement:basic:enterprise:edit',
            meta: {
                parentCode: 'kitchenWasteManagement:basic:enterprise',
                title: '企业管理-详情'
            }
        },
        {
            path: 'basic/point',
            name: 'kitchenWasteManagement:basic:point',
            component: () => require.ensure([], (require) => require('@/views/livableManage/foodWasteManagement/baseInfoManagement/collectionPointManagement/index.vue')),
            meta: {
                authCode: 'kitchenWasteManagement:basic:point',
                title: '收运点管理'
            }
        },
        {
            path: 'basic/addPoint',
            component: () => require.ensure([], (require) => require('@/views/livableManage/foodWasteManagement/baseInfoManagement/collectionPointManagement/add.vue')),
            name: 'kitchenWasteManagement:basic:point:add',
            meta: {
                parentCode: 'kitchenWasteManagement:basic:point',
                title: '收运点管理-新增'
            }
        },
        {
            path: 'basic/editPoint',
            component: () => require.ensure([], (require) => require('@/views/livableManage/foodWasteManagement/baseInfoManagement/collectionPointManagement/detail.vue')),
            name: 'kitchenWasteManagement:basic:point:edit',
            meta: {
                parentCode: 'kitchenWasteManagement:basic:point',
                title: '收运点管理-详情'
            }
        },
        {
            path: 'collectionMap',
            component: () => require.ensure([], (require) => require('@/views/livableManage/foodWasteManagement/collectionMap')),
            name: 'kitchenWasteManagement:collectionMap',
            meta: {
                authCode: 'kitchenWasteManagement:collectionMap',
                title: '收运地图'
            }
        },
        {
            path: 'collectionPlan',
            component: () => require.ensure([], (require) => require('@/views/livableManage/foodWasteManagement/collectionPlan')),
            name: 'kitchenWasteManagement:collectionPlan',
            meta: {
                authCode: 'kitchenWasteManagement:collectionPlan',
                title: '收运计划'
            }
        },
        {
            path: 'collectionPlanAdd',
            component: () => require.ensure([], (require) => require('@/views/livableManage/foodWasteManagement/collectionPlan/add')),
            name: 'kitchenWasteManagement:collectionPlan:add',
            meta: {
                parentCode: 'kitchenWasteManagement:collectionPlan',
                title: '收运计划 - 新增'
            }
        },
        {
            path: 'collectionPlanDetail',
            component: () => require.ensure([], (require) => require('@/views/livableManage/foodWasteManagement/collectionPlan/detail')),
            name: 'kitchenWasteManagement:collectionPlan:detail',
            meta: {
                parentCode: 'kitchenWasteManagement:collectionPlan',
                title: '收运计划 - 详情'
            }
        },
        {
            path: 'enterpriseReport',
            component: () => require.ensure([], (require) => require('@/views/livableManage/foodWasteManagement/enterpriseDeclaration/index.vue')),
            name: 'kitchenWasteManagement:enterpriseReport',
            meta: {
                authCode: 'kitchenWasteManagement:enterpriseReport',
                title: '企业申报'
            }
        },
        {
            path: 'enterpriseReportAdd',
            component: () => require.ensure([], (require) => require('@/views/livableManage/foodWasteManagement/enterpriseDeclaration/add.vue')),
            name: 'kitchenWasteManagement:enterpriseReport:add',
            meta: {
                parentCode: 'kitchenWasteManagement:enterpriseReport',
                title: '企业申报 - 新增'
            }
        },
        {
            path: 'enterpriseReportDetail',
            component: () => require.ensure([], (require) => require('@/views/livableManage/foodWasteManagement/enterpriseDeclaration/detail.vue')),
            name: 'kitchenWasteManagement:enterpriseReport:detail',
            meta: {
                parentCode: 'kitchenWasteManagement:enterpriseReport',
                title: '企业申报 - 详情'
            }
        },
        {
            path: 'enterpriseReportApproval',
            component: () => require.ensure([], (require) => require('@/views/livableManage/foodWasteManagement/enterpriseDeclaration/approval.vue')),
            name: 'kitchenWasteManagement:enterpriseReport:approval',
            meta: {
                parentCode: 'kitchenWasteManagement:enterpriseReport',
                title: '企业申报 - 审核'
            }
        },
        {
            path: 'collectionTask',
            name: 'kitchenWasteManagement:collectionTask',
            component: () => require.ensure([], (require) => require('@/views/livableManage/foodWasteManagement/collectionTask/index.vue')),
            meta: {
                authCode: 'kitchenWasteManagement:collectionTask',
                title: '收运任务'
            }
        },
        {
            path: 'collectionTaskDetail',
            component: () => require.ensure([], (require) => require('@/views/livableManage/foodWasteManagement/collectionTask/detail.vue')),
            name: 'kitchenWasteManagement:collectionTask:detail',
            meta: {
                parentCode: 'kitchenWasteManagement:collectionTask',
                title: '收运任务-详情'
            }
        },
        {
            path: 'collection',
            component: () => require.ensure([], (require) => require('@/views/livableManage/foodWasteManagement/components/addCollectModal.vue')),
            name: 'kitchenWasteManagement:collectionTask:collection',
            meta: {
                parentCode: 'kitchenWasteManagement:collectionTask',
                title: '收运任务-收运'
            }
        },
        {
            path: 'collection',
            component: () => require.ensure([], (require) => require('@/views/livableManage/foodWasteManagement/components/addCollectModal.vue')),
            name: 'kitchenWasteManagement:collectionRecord:collection',
            meta: {
                parentCode: 'kitchenWasteManagement:collectionRecord',
                title: '收运记录-收运'
            }
        },
        {
            path: 'dataFilling',
            component: () => require.ensure([], (require) => require('@/views/livableManage/foodWasteManagement/collectionDataFill/index.vue')),
            name: 'kitchenWasteManagement:dataFilling',
            meta: {
                authCode: 'kitchenWasteManagement:dataFilling',
                title: '数据填报'
            }
        },
        {
            path: 'dataFillingAdd',
            component: () => require.ensure([], (require) => require('@/views/livableManage/foodWasteManagement/collectionDataFill/add.vue')),
            name: 'kitchenWasteManagement:dataFilling:add',
            meta: {
                parentCode: 'kitchenWasteManagement:dataFilling',
                title: '数据填报 - 新增'
            }
        },
        {
            path: 'dataFillingDetail',
            component: () => require.ensure([], (require) => require('@/views/livableManage/foodWasteManagement/collectionDataFill/detail.vue')),
            name: 'kitchenWasteManagement:dataFilling:detail',
            meta: {
                parentCode: 'kitchenWasteManagement:dataFilling',
                title: '数据填报 - 详情'
            }
        },
        {
            path: 'collectionRecord',
            name: 'kitchenWasteManagement:collectionRecord',
            component: () => require.ensure([], (require) => require('@/views/livableManage/foodWasteManagement/collectionRecord/index.vue')),
            meta: {
                authCode: 'kitchenWasteManagement:collectionRecord',
                title: '收运记录'
            }
        },
        {
            path: 'collectionRecordDetail',
            name: 'kitchenWasteManagement:collectionRecord:detail',
            component: () => require.ensure([], (require) => require('@/views/livableManage/foodWasteManagement/collectionRecord/detail.vue')),
            meta: {
                parentCode: 'kitchenWasteManagement:collectionRecord',
                title: '收运记录 - 详情'
            }
        },
        // {
        //     path: 'collectionRecordEdit',
        //     name: 'kitchenWasteManagement:collectionRecord:edit',
        //     component: () => require.ensure([], (require) => require('@/views/livableManage/foodWasteManagement/components/addCollectModal.vue')),
        //     meta: {
        //         parentCode: 'kitchenWasteManagement:collectionRecord',
        //         title: '收运记录 - 编辑'
        //     }
        // },
        {
            path: 'statistic',
            component: () => require.ensure([], (require) => require('@/views/livableManage/foodWasteManagement/statisticalAnalysis/index.vue')),
            name: 'kitchenWasteManagement:statistic',
            meta: {
                parentCode: 'kitchenWasteManagement:statistic',
                title: '统计分析'
            }
        },
    ]
}
