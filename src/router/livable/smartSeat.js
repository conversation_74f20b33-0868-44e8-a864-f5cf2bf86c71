export default [
  // 智慧座椅 - 座椅管理
  {
    path: '/smartSeat',
    name: 'smartSeat',
    meta: {
      authCode: 'smartSeats',
      title: '智慧座椅'
    },
    children: [
      {
        path: 'smartSeatsMap',
        component: () => require.ensure([], (require) => require('@/views/livableManage/smartSeat/seatMap')),
        name: 'smartSeats:smartSeatsMap',
        meta: {
          authCode: 'smartSeats:smartSeatsMap',
          title: '座椅地图'
        }
      },
      {
        path: 'groupControlManage',
        name: 'smartSeats:groupControlManage',
        component: () => require.ensure([], (require) => require('@/views/livableManage/smartSeat/groupControlManage/index')),
        meta: {
          authCode: 'smartSeats:groupControlManage',
          title: '分组控制管理'
        }
      },
      {
        path: 'groupControlManageAdd',
        name: 'groupControlManageAdd',
        component: () => require.ensure([], (require) => require('@/views/livableManage/smartSeat/groupControlManage/add')),
        meta: {
          parentCode: 'smartSeats:groupControlManage',
          title: '分组控制管理-新增'
        }
      },
      {
        path: 'groupControlManageDetail',
        name: 'groupControlManageDetail',
        component: () => require.ensure([], (require) => require('@/views/livableManage/smartSeat/groupControlManage/detail')),
        meta: {
          parentCode: 'smartSeats:groupControlManage',
          title: '分组控制管理-详情'
        }
      },
      {
        path: 'smartSeatsManagement',
        component: () => require.ensure([], (require) => require('@/views/livableManage/smartSeat/seatManagement')),
        name: 'smartSeats:smartSeatsManagement',
        meta: {
          authCode: 'smartSeats:smartSeatsManagement',
          title: '座椅管理'
        }
      },
      {
        path: 'seatManagementAdd',
        component: () => require.ensure([], (require) => require('@/views/livableManage/smartSeat/seatManagement/add')),
        name: 'smartSeats:seatManagement:add',
        meta: {
          parentCode: 'smartSeats:smartSeatsManagement',
          title: '座椅管理-新建'
        }
      },
      {
        path: 'seatManagementDetailOrEdit',
        component: () => require.ensure([], (require) => require('@/views/livableManage/smartSeat/seatManagement/detailEdit')),
        name: 'smartSeats:seatManagement:detail:edit',
        meta: {
          parentCode: 'smartSeats:smartSeatsManagement',
          title: '座椅管理-详情-编辑'
        }
      },
      {
        path: 'switchPlan',
        component: () => require.ensure([], (require) => require('@/views/livableManage/smartSeat/switchPlan/index')),
        name: 'smartSeats:switchPlan',
        meta: {
          authCode: 'smartSeats:switchPlan',
          title: '开关计划'
        }
      },
      {
        path: 'switchPlanAdd',
        component: () => require.ensure([], (require) => require('@/views/livableManage/smartSeat/switchPlan/add')),
        name: 'smartSeats:switchPlan:add',
        meta: {
          parentCode: 'smartSeats:switchPlan',
          title: '开关计划-新建'
        }
      },
      {
        path: 'switchPlanDetail',
        component: () => require.ensure([], (require) => require('@/views/livableManage/smartSeat/switchPlan/detail')),
        name: 'smartSeats:switchPlan:detail:edit',
        meta: {
          parentCode: 'smartSeats:switchPlan',
          title: '开关计划-详情-编辑'
        }
      },
      {
        path: 'statisticalAnalysis',
        name: 'smartSeats:statisticalAnalysis',
        component: () => require.ensure([], (require) => require('@/views/livableManage/smartSeat/statisticalAnalysis')),
        meta: {
          authCode: 'smartSeats:statisticalAnalysis',
          title: '统计分析'
        }
      },
    ]
  },

]
