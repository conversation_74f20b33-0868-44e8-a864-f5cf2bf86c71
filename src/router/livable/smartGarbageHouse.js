/**
 * 智能垃圾屋
 */
export default {
    path: '/smartGarbageHouse',
    redirect: '/smartGarbageHouse/garbageAlarm',
    meta: {
        authCode: 'smartGarbageHouse',
        title: '智能垃圾屋'
    },
    children: [
        {
            path: 'garbageHouseAlarm',
            name: 'smartGarbageHouse:garbageAlarm',
            component: () => require.ensure([], (require) => require('@/views/livableManage/smartGarbageHouse/garbageHouseAlarm/index.vue')),
            meta: {
                authCode: 'smartGarbageHouse:garbageAlarm',
                title: '垃圾屋告警'
            },
        },
        {
            path: 'garbageHouseManagement',
            name: 'smartGarbageHouse:garbageHouseManagement',
            component: () => require.ensure([], (require) => require('@/views/livableManage/smartGarbageHouse/garbageHouseManagement/index.vue')),
            meta: {
                authCode: 'smartGarbageHouse:garbageHouseManagement',
                title: '垃圾屋管理'
            },
        },
        {
            path: 'garbageHouseManagementDetail',
            name: 'garbageHouseManagementDetail',
            component: () => require.ensure([], (require) => require('@/views/livableManage/smartGarbageHouse/garbageHouseManagement/detail.vue')),
            meta: {
                parentCode: 'smartGarbageHouse:garbageHouseManagement',
                title: '垃圾屋详情'
            },
        },
        {
            path: 'garbageHouseMap',
            name: 'smartGarbageHouse:garbageHouseMap',
            component: () => require.ensure([], (require) => require('@/views/livableManage/smartGarbageHouse/garbageHouseMap')),
            meta: {
                authCode: 'smartGarbageHouse:garbageHouseMap',
                title: '垃圾屋地图'
            },
        },

    ]
}
