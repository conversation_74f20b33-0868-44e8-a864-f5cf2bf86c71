// 智慧公厕
export default {
    path: '/smartPublicToilet',
    redirect: '/smartPublicToilet/toiletList',
    meta: {
        authCode: 'smartPublicToilet',
        title: '智慧公厕'
    },
    children: [
        {
            path: 'toiletList',
            name: 'smartPublicToilet:baseInfoManage:toiletList',
            component: () => require.ensure([], (require) => require('@/views/livableManage/smartToilet/baseInfo/toiletList/index.vue')),
            meta: {
                authCode: 'smartPublicToilet:baseInfoManage:toiletList',
                title: '公厕列表'
            }
        },
        {
            path: 'toiletListAdd',
            name: 'toiletListAdd',
            component: () => require.ensure([], (require) => require('@/views/livableManage/smartToilet/baseInfo/toiletList/add.vue')),
            meta: {
                parentCode: 'smartPublicToilet:baseInfoManage:toiletList',
                title: '公厕列表-新增'
            }
        },
        {
            path: 'toiletListDetail',
            name: 'toiletListDetail',
            component: () => require.ensure([], (require) => require('@/views/livableManage/smartToilet/baseInfo/toiletList/detail.vue')),
            meta: {
                parentCode: 'smartPublicToilet:baseInfoManage:toiletList',
                title: '公厕列表-详情'
            }
        },
        {
            path: 'toiletDeviceList',
            name: 'smartPublicToilet:baseInfoManage:toiletDeviceList',
            component: () => require.ensure([], (require) => require('@/views/livableManage/smartToilet/baseInfo/deviceManagement/index')),
            meta: {
                authCode: 'smartPublicToilet:baseInfoManage:toiletDeviceList',
                title: '设备管理'
            }
        },
        {
            path: 'deviceManagementDetail',
            name: 'smartPublicToilet:baseInfoManage:deviceManagementDetail',
            component: () => require.ensure([], (require) => require('@/views/livableManage/smartToilet/baseInfo/deviceManagement/detail')),
            meta: {
                parentCode: 'smartPublicToilet:baseInfoManage:toiletDeviceList',
                title: '设备管理-详情'
            }
        },
        {
            path: 'gasMonitoring',
            name: 'smartPublicToilet:smartServe:gasMonitor',
            component: () => require.ensure([], (require) => require('@/views/livableManage/smartToilet/smartService/gasMonitoring/index')),
            meta: {
                authCode: 'smartPublicToilet:smartServe:gasMonitor',
                title: '气体监测'
            }
        },
        {
            path: 'gasMonitoringHistory',
            name: 'smartPublicToilet:baseInfoManage:gasMonitoringHistory',
            component: () => require.ensure([], (require) => require('@/views/livableManage/smartToilet/smartService/gasMonitoring/history')),
            meta: {
                parentCode: 'smartPublicToilet:smartServe:gasMonitor',
                title: '气体监测-历史记录'
            }
        },
        {
            path: 'smartLinkageSterilization',
            name: 'smartPublicToilet:smartServe:linkageKill',
            component: () => require.ensure([], (require) => require('@/views/livableManage/smartToilet/smartService/smartLinkageSterilization/index')),
            meta: {
                authCode: 'smartPublicToilet:smartServe:linkageKill',
                title: '智能联动杀菌'
            }
        },
        {
            path: 'multiNetworkSwitch',
            name: 'smartPublicToilet:smartServe:moreNet',
            component: () => require.ensure([], (require) => require('@/views/livableManage/smartToilet/smartService/multiNetworkSwitch/index')),
            meta: {
                authCode: 'smartPublicToilet:smartServe:moreNet',
                title: '多网智能切换'
            }
        },
        {
            path: 'advertisingSystem',
            name: 'smartPublicToilet:smartServe:adSystem',
            component: () => require.ensure([], (require) => require('@/views/livableManage/smartToilet/smartService/advertisingSystem/index')),
            meta: {
                authCode: 'smartPublicToilet:smartServe:adSystem',
                title: '广告系统'
            }
        },
        {
            path: 'advertisingSystemDetail',
            name: 'smartPublicToilet:smartServe:advertisingSystemDetail',
            component: () => require.ensure([], (require) => require('@/views/livableManage/smartToilet/smartService/advertisingSystem/detail')),
            meta: {
                parentCode: 'smartPublicToilet:smartServe:adSystem',
                title: '广告系统-详情'
            }
        },
        {
            path: 'energyManager',
            name: 'smartPublicToilet:smartServe:energyManager',
            component: () => require.ensure([], (require) => require('@/views/livableManage/smartToilet/smartService/energyManagement/index')),
            meta: {
                authCode: 'smartPublicToilet:smartServe:energyManager',
                title: '能耗管理'
            }
        },
        {
            path: 'alarmInfo',
            name: 'smartPublicToilet:alarmManager:alarmList',
            component: () => require.ensure([], (require) => require('@/views/livableManage/smartToilet/alarmManagement/alarmInfo/index')),
            meta: {
                authCode: 'smartPublicToilet:alarmManager:alarmList',
                title: '报警信息'
            }
        },
        {
            path: 'cleaningPersonnelList',
            name: 'smartPublicToilet:cleanManager:personList',
            component: () => require.ensure([], (require) => require('@/views/livableManage/smartToilet/cleaningManagement/cleaningPersonnelList/index')),
            meta: {
                authCode: 'smartPublicToilet:cleanManager:personList',
                title: '保洁人员列表'
            }
        },
        {
            path: 'cleaningPersonnelAdd',
            name: 'smartPublicToilet:smartServe:cleaningPersonnelAdd',
            component: () => require.ensure([], (require) => require('@/views/livableManage/smartToilet/cleaningManagement/cleaningPersonnelList/add')),
            meta: {
                parentCode: 'smartPublicToilet:cleanManager:personList',
                title: '保洁人员-新增'
            }
        },
        {
            path: 'cleaningPersonnelDetail',
            name: 'smartPublicToilet:smartServe:cleaningPersonnelDetail',
            component: () => require.ensure([], (require) => require('@/views/livableManage/smartToilet/cleaningManagement/cleaningPersonnelList/detail')),
            meta: {
                parentCode: 'smartPublicToilet:cleanManager:personList',
                title: '保洁人员-详情'
            }
        },
        {
            path: 'attendanceRecord',
            name: 'smartPublicToilet:cleanManager:atteList',
            component: () => require.ensure([], (require) => require('@/views/livableManage/smartToilet/cleaningManagement/attendanceRecord/index')),
            meta: {
                authCode: 'smartPublicToilet:cleanManager:atteList',
                title: '考勤记录'
            }
        },
        {
            path: 'cleaningRecord',
            name: 'smartPublicToilet:cleanManager:cleanRecord',
            component: () => require.ensure([], (require) => require('@/views/livableManage/smartToilet/cleaningManagement/cleaningRecord/index')),
            meta: {
                authCode: 'smartPublicToilet:cleanManager:cleanRecord',
                title: '清洁记录'
            }
        },
        {
            path: 'smartToiletMap',
            name: 'smartPublicToilet:baseInfoManage:toiletMap',
            component: () => require.ensure([], (require) => require('@/views/livableManage/smartToilet/smartToiletMap/index')),
            meta: {
                authCode: 'smartPublicToilet:baseInfoManage:toiletMap',
                title: '地图分布'
            }
        },
        {
            path: 'statisticalAnalysis',
            children:[
                {
                    path: 'todayData',
                    name: 'smartPublicToilet:statisticalAnalysis:todayData',
                    component: () => require.ensure([], (require) => require('@/views/livableManage/smartToilet/summaryStatistics/todayData/index.vue')),
                    meta: {
                        authCode: 'smartPublicToilet:statisticalAnalysis:todayData',
                        title: '今日数据'
                    }
                },
                {
                    path: 'environmentalData',
                    name: 'smartPublicToilet:statisticalAnalysis:environmentalData',
                    component: () => require.ensure([], (require) => require('@/views/livableManage/smartToilet/summaryStatistics/environmentalData/index.vue')),
                    meta: {
                        authCode: 'smartPublicToilet:statisticalAnalysis:environmentalData',
                        title: '环境数据'
                    }
                },
                {
                    path: 'energyConsumptionAnalysis',
                    name: 'smartPublicToilet:statisticalAnalysis:energyConsumptionAnalysis',
                    component: () => require.ensure([], (require) => require('@/views/livableManage/smartToilet/summaryStatistics/energyConsumptionAnalysis/index.vue')),
                    meta: {
                        authCode: 'smartPublicToilet:statisticalAnalysis:energyConsumptionAnalysis',
                        title: '能耗分析'
                    }
                },
                {
                    path: 'passengerFlowStatistics',
                    name: 'smartPublicToilet:statisticalAnalysis:passengerFlowStatistics',
                    component: () => require.ensure([], (require) => require('@/views/livableManage/smartToilet/summaryStatistics/passengerFlow/index.vue')),
                    meta: {
                        authCode: 'smartPublicToilet:statisticalAnalysis:passengerFlowStatistics',
                        title: '客流统计'
                    }
                },
                {
                    path: 'alarmStatistics',
                    name: 'smartPublicToilet:statisticalAnalysis:alarmStatistics',
                    component: () => require.ensure([], (require) => require('@/views/livableManage/smartToilet/summaryStatistics/alarmStatistics/index.vue')),
                    meta: {
                        authCode: 'smartPublicToilet:statisticalAnalysis:alarmStatistics',
                        title: '告警统计'
                    }
                },
            ]
        }
    
    ]
}
