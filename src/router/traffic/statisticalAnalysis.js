export default
{
    path: 'statisticalAnalysis',
    name: 'statisticalAnalysis',
    redirect: '/statisticalAnalysis/manholeAnalysis',
    meta: {
        authCode: 'statisticalAnalysis',
        title: '统计分析'
    },
    children: [
        {
            path: 'parkingStatisticsAnalysis',
            name: 'statisticalAnalysis:parkingStatisticsAnalysis',
            component: () => require.ensure([], (require) =>
                require('@/views/trafficManage/statisticalAnalysis/parkingStatisticsAnalysis')),
            meta: {
                authCode: 'statisticalAnalysis:parkingStatisticsAnalysis',
                title: '停车统计分析'
            }
        },
        {
            path: 'vehicleStatisticalAnalysis',
            name: 'statisticalAnalysis:vehicleStatisticalAnalysis',
            component: () => require.ensure([], (require) =>
                require('@/views/trafficManage/statisticalAnalysis/vehicleStatisticalAnalysis')),
            meta: {
                authCode: 'statisticalAnalysis:vehicleStatisticalAnalysis',
                title: '车辆统计分析'
            }
        },
        {
            path: 'parkTrafficAnalysis',
            name: 'statisticalAnalysis:parkTrafficAnalysis',
            component: () => require.ensure([], (require) =>
                require('@/views/trafficManage/statisticalAnalysis/parkTrafficAnalysis')),
            meta: {
                authCode: 'statisticalAnalysis:parkTrafficAnalysis',
                title: '园区交通分析'
            }
        },
        {
            path: 'comprehensiveMonitoringAnalysis',
            name: 'statisticalAnalysis:comprehensiveMonitoringAnalysis',
            component: () => require.ensure([], (require) =>
                require('@/views/trafficManage/statisticalAnalysis/comprehensiveMonitoringAnalysis')),
            meta: {
                authCode: 'statisticalAnalysis:comprehensiveMonitoringAnalysis',
                title: '综合监测分析'
            }
        },
    ]
}
