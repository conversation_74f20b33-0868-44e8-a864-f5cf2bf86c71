/**
 *   智慧路灯
 */
export default {
  path: 'smartStreetLamp',
  name: 'smartStreetLamp',
  redirect: '/smartStreetLamp/streetlightManage',
  meta: {
    authCode: 'smartRoadBridges',
    title: '智慧路灯'
  },
  children: [
    {
      path: 'streetLightMap',
      name: 'smartStreetLamp:streetlightMap',
      component: () => require.ensure([], (require) => require('@/views/trafficManage/wisdomStreetLight/streetLightMap/index')),
      // component: () => import('@/views/trafficManage/wisdomStreetLight/streetLightMap/index'),
      meta: {
        authCode: 'smartStreetLamp:streetlightMap',
        title: '地图监控'
      }
    },
    {
      path: 'streetlightManage',
      name: 'smartStreetLamp:streetlightManage',
      component: () => require.ensure([], (require) => require('@/views/trafficManage/wisdomStreetLight/streetLightManagement/index')),
      // component: () => import('@/views/trafficManage/wisdomStreetLight/streetLightManagement/index'),
      meta: {
        authCode: 'smartStreetLamp:streetlightManage',
        title: '路灯管理'
      }
    },
    {
      path: 'streetlightManageDetail',
      name: 'streetlightManageDetail',
      component: () => require.ensure([], (require) => require('@/views/trafficManage/wisdomStreetLight/streetLightManagement/detail')),
      // component: () => import('@/views/trafficManage/wisdomStreetLight/streetLightManagement/detail'),
      meta: {
        parentCode: 'smartStreetLamp:streetlightManage',
        title: '路灯管理-详情'
      }
    },
    {
      path: 'maintenanceRecordDetail',
      name: 'smartStreetLamp:maintenanceRecordDetail',
      component: () => require.ensure([], (require) => require('@/views/trafficManage/wisdomStreetLight/maintenanceRecord/detail')),
      // component: () => import('@/views/trafficManage/wisdomStreetLight/maintenanceRecord/detail'),
      meta: {
        parentCode: 'smartStreetLamp:streetlightManage',
        title: '养护记录-详情'
      }
    },
    {
      path: 'streetlightGroup',
      name: 'smartStreetLamp:streetlightGroup',
      component: () => require.ensure([], (require) => require('@/views/trafficManage/wisdomStreetLight/groupManagement/index')),
      // component: () => import('@/views/trafficManage/wisdomStreetLight/groupManagement/index'),
      meta: {
        authCode: 'smartStreetLamp:streetlightGroup',
        title: '分组管理'
      }
    },
    {
      path: 'streetlightGroupAdd',
      name: 'streetlightGroupAdd',
      component: () => require.ensure([], (require) => require('@/views/trafficManage/wisdomStreetLight/groupManagement/add')),
      // component: () => import('@/views/trafficManage/wisdomStreetLight/groupManagement/add'),
      meta: {
        parentCode: 'smartStreetLamp:streetlightGroup',
        title: '分组管理-新增'
      }
    },
    {
      path: 'streetlightGroupDetail',
      name: 'streetlightGroupDetail',
      component: () => require.ensure([], (require) => require('@/views/trafficManage/wisdomStreetLight/groupManagement/detail')),
      // component: () => import('@/views/trafficManage/wisdomStreetLight/groupManagement/detail'),
      meta: {
        parentCode: 'smartStreetLamp:streetlightGroup',
        title: '分组管理-详情'
      }
    },
    {
      path: 'streetlightPlan',
      name: 'smartStreetLamp:streetlightPlan',
      component: () => require.ensure([], (require) => require('@/views/trafficManage/wisdomStreetLight/streetLightControlTask/index')),
      // component: () => import('@/views/trafficManage/wisdomStreetLight/streetLightControlTask/index'),
      meta: {
        authCode: 'smartStreetLamp:streetlightPlan',
        title: '路灯控制任务'
      }
    },
    {
      path: 'streetlightPlanAdd',
      name: 'streetlightPlanAdd',
      component: () => require.ensure([], (require) => require('@/views/trafficManage/wisdomStreetLight/streetLightControlTask/add')),
      // component: () => import('@/views/trafficManage/wisdomStreetLight/streetLightControlTask/add'),
      meta: {
        parentCode: 'smartStreetLamp:streetlightPlan',
        title: '路灯控制任务-新增'
      }
    },
    {
      path: 'streetlightPlanDetail',
      name: 'streetlightPlanDetail',
      component: () => require.ensure([], (require) => require('@/views/trafficManage/wisdomStreetLight/streetLightControlTask/detail')),
      // component: () => import('@/views/trafficManage/wisdomStreetLight/streetLightControlTask/detail'),
      meta: {
        parentCode: 'smartStreetLamp:streetlightPlan',
        title: '路灯控制任务-详情'
      }
    },
    {
      path: 'streetlightGeneral',
      name: 'smartStreetLamp:streetlightGeneral',
      component: () => require.ensure([], (require) => require('@/views/trafficManage/wisdomStreetLight/streetLightOverview/index')),
      // component: () => import('@/views/trafficManage/wisdomStreetLight/streetLightOverview/index'),
      meta: {
        authCode: 'smartStreetLamp:streetlightGeneral',
        title: '路灯概况'
      }
    },
    {
      path: 'streetlightAlarm',
      name: 'smartStreetLamp:streetlightAlarm',
      component: () => require.ensure([], (require) => require('@/views/trafficManage/wisdomStreetLight/streetlightAlarm')),
      // component: () => import('@/views/trafficManage/wisdomStreetLight/streetlightAlarm'),
      meta: {
        authCode: 'smartStreetLamp:streetlightAlarm',
        title: '路灯告警'
      }
    },
    {
      path: 'statisticalAnalysis',
      name: 'smartStreetLamp:statisticalAnalysis',
      component: () => require.ensure([], (require) => require('@/views/trafficManage/wisdomStreetLight/statisticalAnalysis')),
      // component: () => import('@/views/trafficManage/wisdomStreetLight/streetlightAlarm'),
      meta: {
        authCode: 'smartStreetLamp:statisticalAnalysis',
        title: '统计分析'
      }
    },
      {
          path: 'program',
          name: 'smartStreetLamp:program',
          component: () => require.ensure([], (require) => require('@/views/trafficManage/wisdomStreetLight/programManagement')),
          meta: {
              authCode: 'smartStreetLamp:program',
              title: '节目管理'
          }
      },
      {
          path: 'programAdd',
          name: 'smartStreetLamp:programAdd',
          component: () => require.ensure([], (require) => require('@/views/trafficManage/wisdomStreetLight/programManagement/add')),
          meta: {
              parentCode: 'smartStreetLamp:program',
              title: '节目管理-新增'
          }
      },
      {
          path: 'video',
          name: 'smartStreetLamp:video',
          component: () => require.ensure([], (require) => require('@/views/trafficManage/wisdomStreetLight/videoMonitoring')),
          meta: {
              authCode: 'smartStreetLamp:video',
              title: '视频监控'
          }
      },
      {
        path: 'videoDetail',
        name: 'smartStreetLamp:videoDetail',
        component: () => require.ensure([], (require) => require('@/views/trafficManage/wisdomStreetLight/videoMonitoring/detail')),
        meta: {
          parentCode: 'smartStreetLamp:video',
          title: '视频监控-详情'
        }
      },
      {
        path: 'statisticalLights',
        name: 'smartStreetLamp:statisticalLights',
        component: () => require.ensure([], (require) => require('@/views/trafficManage/wisdomStreetLight/statisticalLights/index')),
        meta: {
            authCode: 'smartStreetLamp:statisticalLights',
            title: '路灯统计分析'
        }
    },
  ]
}
