/**
 * 智慧畅行
 */
export default {
    path: '/wisdomSmoothness',
    redirect: '/wisdomSmoothness/signalLightManagement',
    meta: {
        authCode: 'wisdomSmoothness',
        title: '智慧畅行'
    },
    children: [
        {
            path: 'intersectionOverview',
            name: 'intersectionOverview',
            meta: {
                authCode: 'wisdomSmoothness:intersectionOverview',
                title: '路口概览'
            },
            children: [
                {
                    path: 'lamp',
                    name: 'wisdomSmoothness:intersectionOverview:lamp',
                    component: () => require.ensure([], (require) => require('@/views/trafficManage/wisdomSmoothness/overview/signalLight/index')),
                    meta: {
                        authCode: 'wisdomSmoothness:intersectionOverview:lamp',
                        title: '信号灯概览'
                    }
                },
                {
                    path: 'events',
                    name: 'wisdomSmoothness:intersectionOverview:events',
                    component: () => require.ensure([], (require) => require('@/views/trafficManage/wisdomSmoothness/overview/adaptiveEvent/index')),
                    meta: {
                        authCode: 'wisdomSmoothness:intersectionOverview:events',
                        title: '路口自适应概览'
                    }
                },
            ]
        },
        {
            path: 'intersection',
            name: 'intersection',
            meta: {
                authCode: 'wisdomSmoothness:intersection',
                title: '路口管理'
            },
            children: [
                {
                    path: 'electric',
                    name: 'wisdomSmoothness:intersection:lamp',
                    component: () => require.ensure([], (require) => require('@/views/trafficManage/wisdomSmoothness/signalLightManagement/index')),
                    meta: {
                        authCode: 'wisdomSmoothness:intersection:lamp',
                        title: '信号灯管理'
                    }
                },
                {
                    path: 'signalLightManagementDetail',
                    name: 'wisdomSmoothness:intersection:lamp:detail',
                    component: () => require.ensure([], (require) => require('@/views/trafficManage/wisdomSmoothness/signalLightManagement/detail')),
                    meta: {
                        parentCode: 'wisdomSmoothness:intersection:lamp',
                        title: '信号灯管理-详情'
                    }
                },
                {
                    path: 'events',
                    name: 'wisdomSmoothness:intersection:events',
                    component: () => require.ensure([], (require) => require('@/views/trafficManage/wisdomSmoothness/adaptiveEventManagement/index')),
                    meta: {
                        authCode: 'wisdomSmoothness:intersection:events',
                        title: '自适应事件管理'
                    }
                },
                {
                    path: 'adaptiveEventManagementDetail',
                    name: 'wisdomSmoothness:adaptiveEventManagementDetail',
                    component: () => require.ensure([], (require) => require('@/views/trafficManage/wisdomSmoothness/adaptiveEventManagement/detail')),
                    meta: {
                        parentCode: 'wisdomSmoothness:intersection:events',
                        title: '自适应事件管理-详情'
                    }
                },
                {
                    path: 'monitor',
                    name: 'wisdomSmoothness:intersection:monitor',
                    component: () => require.ensure([], (require) => require('@/views/trafficManage/wisdomSmoothness/monitorMap/index')),
                    meta: {
                        authCode: 'wisdomSmoothness:intersection:monitor',
                        title: '信号灯监控'
                    }
                },
            ]
        },
        {
            path: 'violation',
            redirect: '/wisdomSmoothness/violation/electric',
            meta: {
                authCode: 'wisdomSmoothness:violation:electric',
                title: '违章违规'
            },
            children: [
                {
                    path: 'electric',
                    name: 'wisdomSmoothness:violation:electric',
                    component: () => require.ensure([], (require) => require('@/views/trafficManage/wisdomSmoothness/violation/electric/index')),
                    meta: {
                        authCode: 'wisdomSmoothness:violation:electric',
                        title: '电警违规'
                    }
                },
                {
                    path: 'electricDetail',
                    name: 'wisdomSmoothness:violation:electric:detail',
                    component: () => require.ensure([], (require) => require('@/views/trafficManage/wisdomSmoothness/violation/electric/detail')),
                    meta: {
                        parentCode: 'wisdomSmoothness:violation:electric',
                        title: '电警违规-详情'
                    }
                },
                {
                    path: 'electricDetail',
                    name: 'wisdomSmoothness:violation:common:detail',
                    component: () => require.ensure([], (require) => require('@/views/trafficManage/wisdomSmoothness/violation/components/detail')),
                    meta: {
                        parentCode: 'wisdomSmoothness:violation:electric',
                        title: '违章违规-详情'
                    }
                },
                {
                    path: 'bayonet',
                    name: 'wisdomSmoothness:violation:bayonet',
                    component: () => require.ensure([], (require) => require('@/views/trafficManage/wisdomSmoothness/violation/bayonet/index')),
                    meta: {
                        authCode: 'wisdomSmoothness:violation:bayonet',
                        title: '卡口测速'
                    }
                },
                {
                    path: 'pedestrians',
                    name: 'wisdomSmoothness:violation:pedestrians',
                    component: () => require.ensure([], (require) => require('@/views/trafficManage/wisdomSmoothness/violation/pedestrians/index')),
                    meta: {
                        authCode: 'wisdomSmoothness:violation:pedestrians',
                        title: '礼让行人'
                    }
                },
                {
                    path: 'horn',
                    name: 'wisdomSmoothness:violation:horn',
                    component: () => require.ensure([], (require) => require('@/views/trafficManage/wisdomSmoothness/violation/horn/index')),
                    meta: {
                        authCode: 'wisdomSmoothness:violation:horn',
                        title: '禁止鸣笛'
                    }
                },
                {
                    path: 'highBeam',
                    name: 'wisdomSmoothness:violation:highBeam',
                    component: () => require.ensure([], (require) => require('@/views/trafficManage/wisdomSmoothness/violation/highBeam/index')),
                    meta: {
                        authCode: 'wisdomSmoothness:violation:highBeam',
                        title: '远光灯违规'
                    }
                },
            ]
        },
        {
            path: 'overview',
            redirect: '/wisdomSmoothness/overview/electricOverview',
            meta: {
                authCode: 'wisdomSmoothness:overview',
                title: '违章概况'
            },
            children: [
                {
                    path: 'electricOverview',
                    name: 'wisdomSmoothness:overview:electricOverview',
                    component: () => require.ensure([], (require) => require('@/views/trafficManage/wisdomSmoothness/overview/electricOverview/index')),
                    meta: {
                        authCode: 'wisdomSmoothness:overview:electricOverview',
                        title: '电警违规概览'
                    }
                },
                {
                    path: 'bayonetOverview',
                    name: 'wisdomSmoothness:overview:bayonetOverview',
                    component: () => require.ensure([], (require) => require('@/views/trafficManage/wisdomSmoothness/overview/bayonetOverview/index')),
                    meta: {
                        authCode: 'wisdomSmoothness:overview:bayonetOverview',
                        title: '卡口测速概览'
                    }
                },
                {
                    path: 'pedestriansOverview',
                    name: 'wisdomSmoothness:overview:pedestriansOverview',
                    component: () => require.ensure([], (require) => require('@/views/trafficManage/wisdomSmoothness/overview/pedestriansOverview/index')),
                    meta: {
                        authCode: 'wisdomSmoothness:overview:pedestriansOverview',
                        title: '礼让行人概览'
                    }
                },
                {
                    path: 'hornOverview',
                    name: 'wisdomSmoothness:overview:hornOverview',
                    component: () => require.ensure([], (require) => require('@/views/trafficManage/wisdomSmoothness/overview/hornOverview/index')),
                    meta: {
                        authCode: 'wisdomSmoothness:overview:hornOverview',
                        title: '禁止鸣笛概览'
                    }
                },
                {
                    path: 'highBeamOverview',
                    name: 'wisdomSmoothness:overview:highBeamOverview',
                    component: () => require.ensure([], (require) => require('@/views/trafficManage/wisdomSmoothness/overview/highBeamOverview/index')),
                    meta: {
                        authCode: 'wisdomSmoothness:overview:highBeamOverview',
                        title: '远光灯违规概览'
                    }
                },
            ]
        }
    ]

}
