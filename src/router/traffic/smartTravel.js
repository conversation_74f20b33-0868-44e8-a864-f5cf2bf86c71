/**
 *   智慧出行
 */
export default {
    path: 'information',
    name: 'information',
    meta: {
        authCode: 'smartTravel',
        title: '智慧出行',
    },
    children: [
        {
            path: 'guidanceList',
            name: 'smartTravel:information:guidanceList',
            component: () =>
                require.ensure([], (require) =>
                    require('@/views/trafficManage/smartTravel/guidanceList/index')
                ),
            meta: {
                authCode: 'smartTravel:information:guidanceList',
                title: '诱导屏列表'
            }
        },
        {
            path: 'guidanceListDetail',
            name: 'smartTravel:guidanceListDetail',
            component: () =>
                require.ensure([], (require) =>
                    require('@/views/trafficManage/smartTravel/guidanceList/detail')
                ),
            meta: {
                parentCode: 'smartTravel:information:guidanceList',
                title: '诱导屏详情'
            }
        },
        {
            path: 'automaticList',
            name: 'smartTravel:information:automaticList',
            component: () =>
                require.ensure([], (require) =>
                    require('@/views/trafficManage/smartTravel/automaticList/index')
                ),
            meta: {
                authCode: 'smartTravel:information:automaticList',
                title: '自动文案列表'
            }
        },
        {
            path: 'automaticListDetail',
            name: 'smartTravel:automaticListDetail',
            component: () =>
                require.ensure([], (require) =>
                    require('@/views/trafficManage/smartTravel/automaticList/detail')
                ),
            meta: {
                parentCode: 'smartTravel:information:automaticList',
                title: '自动文案列表'
            }
        },
        {
            path: 'submitList',
            name: 'smartTravel:information:submitList',
            component: () =>
                require.ensure([], (require) =>
                    require('@/views/trafficManage/smartTravel/submitList/index')
                ),
            meta: {
                authCode: 'smartTravel:information:submitList',
                title: '上报信息列表'
            }
        },
        {
            path: 'submitListDetail',
            name: 'smartTravel:submitListDetail',
            component: () =>
                require.ensure([], (require) =>
                    require('@/views/trafficManage/smartTravel/submitList/detail')
                ),
            meta: {
                parentCode: 'smartTravel:information:submitList',
                title: '上报信息列表-详情'
            }
        },
        {
            path: 'release',
            name: 'smartTravel:information:release',
            component: () =>
                require.ensure([], (require) =>
                    require('@/views/trafficManage/smartTravel/release/index')
                ),
            meta: {
                authCode: 'smartTravel:information:release',
                title: '发布信息查询'
            }
        },
        {
            path: 'releaseLogs',
            name: 'smartTravel:information:releaseLogs',
            component: () =>
                require.ensure([], (require) =>
                    require('@/views/trafficManage/smartTravel/releaseLogs/index')
                ),
            meta: {
                authCode: 'smartTravel:information:releaseLogs',
                title: '发布日志查询'
            }
        },
        {
            path: 'internetGuid',
            name: 'smartTravel:internetguid:log',
            component: () =>
                require.ensure([], (require) =>
                    require('@/views/trafficManage/smartTravel/internetGuid/index')
                ),
            meta: {
                authCode: 'smartTravel:internetguid:log',
                title: '发布日志查询'
            }
        },
        {
            path: 'statistics',
            name: 'smartTravel:information:statistics',
            component: () =>
                require.ensure([], (require) =>
                    require('@/views/trafficManage/smartTravel/infoStatistics/index')
                ),
            meta: {
                authCode: 'smartTravel:information:statistics',
                title: '信息统计'
            }
        }
    ]
};
