/**
 *   智慧停车
 */
export default {
    path: "intelligentParking",
    name: "intelligentParking",
    meta: {
        authCode: "intelligentParking",
        title: "智慧停车",
    },
    children: [
        {
            path: "overviewParkingSpaces",
            name: "intelligentParking:overviewParkingSpaces",
            component: () =>
                require.ensure([], (require) =>
                    require("@/views/trafficManage/wisdomParking/overviewParkingSpaces/index")
                ),
            meta: {
                authCode: "intelligentParking:overviewParkingSpaces",
                title: "停车位概览",
            },
        },
        {
            path: "parkingLot",
            name: "intelligentParking:basic:parkingLot",
            component: () =>
                require.ensure([], (require) =>
                    require("@/views/trafficManage/wisdomParking/parkingManagement/index")
                ),
            meta: {
                authCode: "intelligentParking:basic:parkingLot",
                title: "停车场管理",
            },
        },
        {
            path: "parkingManagementAdd",
            name: "parkingManagementAdd",
            component: () =>
                require.ensure([], (require) =>
                    require("@/views/trafficManage/wisdomParking/parkingManagement/add")
                ),
            meta: {
                parentCode: "intelligentParking:basic:parkingLot",
                title: "停车场管理-新增",
            },
        },
        {
            path: "parkingManagementDetail",
            name: "parkingManagementDetail",
            component: () =>
                require.ensure([], (require) =>
                    require("@/views/trafficManage/wisdomParking/parkingManagement/detail")
                ),
            meta: {
                parentCode: "intelligentParking:basic:parkingLot",
                title: "停车场管理-详情",
            },
        },
        {
            path: "parkingManagementCorrelation",
            name: "parkingManagementCorrelation",
            component: () =>
                require.ensure([], (require) =>
                    require("@/views/trafficManage/wisdomParking/parkingManagement/correlation")
                ),
            meta: {
                parentCode: "intelligentParking:basic:parkingLot",
                title: "停车场管理-关联车位",
            },
        },
        {
            path: "internalVehicle",
            name: "intelligentParking:basic:internalVehicle",
            component: () =>
                require.ensure([], (require) =>
                    require("@/views/trafficManage/wisdomParking/internalVehicle/index")
                ),
            meta: {
                authCode: "intelligentParking:basic:internalVehicle",
                title: "白名单",
            },
        },
        {
            path: "internalVehicleAdd",
            name: "intelligentParking:internalVehicleAdd",
            component: () =>
                require.ensure([], (require) =>
                    require("@/views/trafficManage/wisdomParking/internalVehicle/add")
                ),
            meta: {
                parentCode: "intelligentParking:basic:internalVehicle",
                title: "白名单-新增",
            },
        },
        {
            path: "internalVehicleDetail",
            name: "intelligentParking:internalVehicleDetail",
            component: () =>
                require.ensure([], (require) =>
                    require("@/views/trafficManage/wisdomParking/internalVehicle/detail")
                ),
            meta: {
                parentCode: "intelligentParking:basic:internalVehicle",
                title: "白名单-详情",
            },
        },
        {
            path: "magnetic",
            name: "intelligentParking:magnetic",
            component: () =>
                require.ensure([], (require) =>
                    require("@/views/trafficManage/wisdomParking/magneticManagement/index")
                ),
            meta: {
                authCode: "intelligentParking:magnetic",
                title: "地磁管理",
            },
        },
        {
            path: "magneticDetail",
            name: "intelligentParking:magneticDetail",
            component: () =>
                require.ensure([], (require) =>
                    require("@/views/trafficManage/wisdomParking/magneticManagement/detail")
                ),
            meta: {
                parentCode: "intelligentParking:magnetic",
                title: "地磁管理-详情",
            },
        },
        {
            path: "inOut",
            name: "intelligentParking:inOut",
            component: () =>
                require.ensure([], (require) =>
                    require("@/views/trafficManage/wisdomParking/inOutRecord/index")
                ),
            meta: {
                authCode: "intelligentParking:inOut",
                title: "出入记录",
            },
        },
        {
            path: "inOutDetail",
            name: "intelligentParking:inOutDetail",
            component: () =>
                require.ensure([], (require) =>
                    require("@/views/trafficManage/wisdomParking/inOutRecord/detail")
                ),
            meta: {
                parentCode: "intelligentParking:inOut",
                title: "出入记录-详情",
            },
        },
        {
            path: "fee",
            name: "intelligentParking:fee",
            component: () =>
                require.ensure([], (require) =>
                    require("@/views/trafficManage/wisdomParking/feeManagement/index")
                ),
            meta: {
                authCode: "intelligentParking:fee",
                title: "收费管理",
            },
        },
        {
            path: "feeDetail",
            name: "intelligentParking:feeDetail",
            component: () =>
                require.ensure([], (require) =>
                    require("@/views/trafficManage/wisdomParking/feeManagement/detail")
                ),
            meta: {
                parentCode: "intelligentParking:fee",
                title: "收费管理-详情",
            },
        },
        {
            path: "alarm",
            name: "intelligentParking:alarm",
            component: () =>
                require.ensure([], (require) =>
                    require("@/views/trafficManage/wisdomParking/alarmManagement/index")
                ),
            meta: {
                authCode: "intelligentParking:alarm",
                title: "告警管理",
            },
        },
        {
            path: "blacklist",
            name: "intelligentParking:basic:blacklist",
            component: () =>
                require.ensure([], (require) =>
                    require("@/views/trafficManage/wisdomParking/blacklist/index")
                ),
            meta: {
                authCode: "intelligentParking:basic:blacklist",
                title: "黑名单",
            },
        },
        {
            path: "blacklistDetail",
            name: "blacklist:detail",
            component: () =>
                require.ensure([], (require) =>
                    require("@/views/trafficManage/wisdomParking/blacklist/detail")
                ),
            meta: {
                parentCode: "intelligentParking:basic:blacklist",
                title: "黑名单-详情",
            },
        },
        {
            path: "blacklistAdd",
            name: "blacklist:add",
            component: () =>
                require.ensure([], (require) =>
                    require("@/views/trafficManage/wisdomParking/blacklist/add")
                ),
            meta: {
                parentCode: "intelligentParking:fee",
                title: "黑名单-新增",
            },
        },
        {
            path: "illegalRecord",
            name: "intelligentParking:illegalRecord",
            component: () =>
                require.ensure([], (require) =>
                    require("@/views/trafficManage/wisdomParking/illegalParkingRecord/index")
                ),
            meta: {
                authCode: "intelligentParking:illegalRecord",
                title: "违停记录",
            },
        },
        {
            path: "illegalParkingRecordDetail",
            name: "illegalParkingRecord:detail",
            component: () =>
                require.ensure([], (require) =>
                    require("@/views/trafficManage/wisdomParking/illegalParkingRecord/detail")
                ),
            meta: {
                parentCode: "intelligentParking:illegalRecord",
                title: "违停记录-详情",
            },
        },
        {
            path: "parkingStatistics",
            name: "intelligentParking:parkingStatistics",
            component: () =>
                require.ensure([], (require) =>
                    require("@/views/trafficManage/wisdomParking/parkingStatisticAnalysis/index")
                ),
            meta: {
                authCode: "intelligentParking:parkingStatistics",
                title: "停车统计分析",
            },
        },
        {
            path: "feeStatistics",
            name: "intelligentParking:feeStatistics",
            component: () =>
                require.ensure([], (require) =>
                    require("@/views/trafficManage/wisdomParking/feeStatisticAnalysis/index")
                ),
            meta: {
                authCode: "intelligentParking:feeStatistics",
                title: "收费统计分析",
            },
        },
        {
            path: "paymentMonitoring",
            name: "intelligentParking:paymentMonitoring",
            component: () =>
                require.ensure([], (require) =>
                    require("@/views/trafficManage/wisdomParking/paymentMonitoring/index")
                ),
            meta: {
                authCode: "intelligentParking:paymentMonitoring",
                title: "缴费管理",
            },
        },
        {
            path: "paymentMonitoring/detail",
            name: "intelligentParking:paymentMonitoring:detail",
            component: () =>
                require.ensure([], (require) =>
                    require("@/views/trafficManage/wisdomParking/paymentMonitoring/detail")
                ),
            meta: {
                authCode: "intelligentParking:paymentMonitoring",
                title: "缴费详情",
            },
        },
        {
            path: "actionLog",
            name: "intelligentParking:actionLog",
            component: () =>
                require.ensure([], (require) =>
                    require("@/views/trafficManage/wisdomParking/actionLog/index")
                ),
            meta: {
                authCode: "intelligentParking:actionLog",
                title: "操作日志",
            },
        },
        {
            path: "parkingMeter",
            name: "intelligentParking:carManages:parkingMeter",
            component: () =>
                require.ensure([], (require) =>
                    require("@/views/trafficManage/wisdomParking/parkingMeter/index")
                ),
            meta: {
                authCode: "intelligentParking:carManages:parkingMeter",
                title: "停车计时",
            },
        },
        {
            path: "parkingMeter/detail",
            name: "intelligentParking:carManages:parkingMeter:detail",
            component: () =>
                require.ensure([], (require) =>
                    require("@/views/trafficManage/wisdomParking/parkingMeter/detail")
                ),
            meta: {
                authCode: "intelligentParking:carManages:parkingMeter",
                title: "停车计时-详情",
            },
        },
        {
            path: "berthDetection",
            name: "intelligentParking:carManages:berthDetection",
            component: () =>
                require.ensure([], (require) =>
                    require("@/views/trafficManage/wisdomParking/berthDetection/index")
                ),
            meta: {
                authCode: "intelligentParking:carManages:berthDetection",
                title: "泊位检测",
            },
        },
        {
            path: "berthDetection/detail",
            name: "intelligentParking:carManages:berthDetection:detail",
            component: () =>
                require.ensure([], (require) =>
                    require("@/views/trafficManage/wisdomParking/berthDetection/detail")
                ),
            meta: {
                authCode: "intelligentParking:carManages:berthDetection",
                title: "泊位检测-详情",
            },
        },
        {
            path: "parkingGuiding",
            name: "intelligentParking:carManages:parkingGuiding",
            component: () =>
                require.ensure([], (require) =>
                    require("@/views/trafficManage/wisdomParking/parkingGuiding/index")
                ),
            meta: {
                authCode: "intelligentParking:carManages:parkingGuiding",
                title: "反向寻车",
            },
        },
        {
            path: "carManageStatistics",
            name: "intelligentParking:carManages:statistics",
            component: () =>
                require.ensure([], (require) =>
                    require("@/views/trafficManage/wisdomParking/carManageStatistics/index")
                ),
            meta: {
                authCode: "intelligentParking:carManages:statistics",
                title: "统计分析",
            },
        },
    ],
};
