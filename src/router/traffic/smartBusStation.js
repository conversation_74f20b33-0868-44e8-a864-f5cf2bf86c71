/**
 *   智慧公交站
 */
export default {
    path: 'smartBusStation',
    name: 'smartBusStation',
    meta: {
        authCode: 'smartBusStation',
        title: '智慧公交站',
    },
    children: [
        {
            path: 'mapOverview',
            name: 'smartBusStation:mapOverview',
            component: () =>
                require.ensure([], (require) =>
                    require('@/views/trafficManage/smartBusStation/mapOverview/index')
                ),
            meta: {
                authCode: 'smartBusStation:mapOverview',
                title: '地图监控',
            },
        },
        {
            path: 'infoMonitor',
            name: 'smartBusStation:infoMonitor',
            component: () =>
                require.ensure([], (require) =>
                    require('@/views/trafficManage/smartBusStation/busInfoMonitoring/index')
                ),
            meta: {
                authCode: 'smartBusStation:infoMonitor',
                title: '公交信息监控',
            },
        },
        {
            path: 'stationList',
            name: 'smartBusStation:stationList',
            component: () =>
                require.ensure([], (require) =>
                    require('@/views/trafficManage/smartBusStation/stationList/index')
                ),
            meta: {
                authCode: 'smartBusStation:stationList',
                title: '公交站点列表',
            },
        },
        {
            path: 'alarm',
            name: 'smartBusStation:alarm',
            component: () => require.ensure([], (require) => require('@/views/trafficManage/smartBusStation/alarm/index')),
            meta: {
                authCode: 'smartBusStation:alarm',
                title: '报警信息'
            }
        }, {
            path: 'lineList',
            name: 'smartBusStation:lineList',
            component: () => require.ensure([], (require) => require('@/views/trafficManage/smartBusStation/BusLineList/index.vue')),
            meta: {
                authCode: 'smartBusStation:lineList',
                title: '公交线路列表'
            }
        }, {
            path: 'addLineList',
            name: 'smartBusStation:lineList:add',
            component: () => require.ensure([], (require) => require('@/views/trafficManage/smartBusStation/BusLineList/add.vue')),
            meta: {
                parentCode: 'smartBusStation:lineList',
                title: '公交线路列表-新增'
            }
        }, {
            path: 'editLineList',
            name: 'smartBusStation:lineList:detail',
            component: () => require.ensure([], (require) => require('@/views/trafficManage/smartBusStation/BusLineList/detail.vue')),
            meta: {
                parentCode: 'smartBusStation:lineList',
                title: '公交线路列表-详情'
            }
        },
        {
            path: 'videoMonitor',
            name: 'smartBusStation:videoMonitor',
            component: () => require.ensure([], (require) => require('@/views/trafficManage/smartBusStation/videoMonitor/index.vue')),
            meta: {
                authCode: 'smartBusStation:videoMonitor',
                title: '公交视频监控'
            }
        },
        {
            path: 'busPassengerMonitor',
            name: 'smartBusStation:busPassengerMonitor',
            component: () => require.ensure([], (require) => require('@/views/trafficManage/smartBusStation/busPassengerMonitor/index.vue')),
            meta: {
                authCode: 'smartBusStation:busPassengerMonitor',
                title: '公交乘客监控'
            }
        },
        {
            path: 'busMonitor',
            name: 'smartBusStation:busMonitor',
            component: () => require.ensure([], (require) => require('@/views/trafficManage/smartBusStation/busMonitor/index.vue')),
            meta: {
                authCode: 'smartBusStation:busMonitor',
                title: '公交机动车监控'
            }
        },
        {
            path: 'statisticsAnalyze',
            name: 'smartBusStation:statisticsAnalyze',
            component: () => require.ensure([], (require) => require('@/views/trafficManage/smartBusStation/statisticsAnalyze/index.vue')),
            meta: {
                authCode: 'smartBusStation:statisticsAnalyze',
                title: '统计分析'
            }
        },
        {
            path: 'dataStatisticsAnalyze',
            name: 'smartBusStation:dataStatisticsAnalyze',
            component: () => require.ensure([], (require) => require('@/views/trafficManage/smartBusStation/dataStatisticsAnalyze/index.vue')),
            meta: {
                authCode: 'smartBusStation:dataStatisticsAnalyze',
                title: '数据统计分析'
            }
        }
    ],
};
