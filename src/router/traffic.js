import { createRouter, createWebHistory, createWebHashHistory } from 'vue-router';
import common from './common'
import Layout from '@/layout'
const files = require.context('./traffic', false, /\.js$/);
const routerList = []
files.keys().forEach(url => {
    const routerItem = require('./traffic' + url.replace('.',''))
    routerList.push(routerItem)
});
export const constantRoutes = [
    {
        path: '/',
        component: Layout,
        name: 'home',
        showSingle: true,
        meta: {
            icon: 'home',
            title: '首页'
        },

        children: [
            {
                path: '/',
                component: () => import('@/views/error-page/404'),
            },
            {
                path: '/mainHomePage',
                component: () => require.ensure([], (require) => require('@/views/trafficManage/index/index')),
                name: 'mainHomePage',
                meta: {
                    icon: 'home',
                    title: '首页'
                }
            },
            // 操作日志
            {
                path: 'operationLog',
                name: 'businessLog',
                component: () => require.ensure([], (require) => require('@/views/common/operationLog/index')),
                meta: {
                    authCode: 'businessLog',
                    title: '操作日志'
                },
            },
            // 智慧道桥
            {
                path: 'wisdomRoadBridge',
                name: 'wisdomRoadBridge',
                redirect: '/wisdomRoadBridge/maintenanceLedger',
                meta: {
                    authCode: 'smartRoadBridges',
                    title: '智慧道桥'
                },
                children: [
                    {
                        path: 'maintenanceLedger',
                        name: 'smartRoadBridges:maintennace',
                        component: () => require.ensure([], (require) => require('@/views/trafficManage/wisdomRoadBridge/maintenanceLedger/index')),
                        // component: () => import('@/views/trafficManage/wisdomRoadBridge/maintenanceLedger/index'),
                        meta: {
                            authCode: 'smartRoadBridges:maintennace',
                            title: '养护记录'
                        }
                    },
                    {
                        path: 'maintenanceLedgerAdd',
                        name: 'maintenanceLedgerAdd',
                        component: () => require.ensure([], (require) => require('@/views/trafficManage/wisdomRoadBridge/maintenanceLedger/add')),
                        // component: () => import('@/views/trafficManage/wisdomRoadBridge/maintenanceLedger/add'),
                        meta: {
                            parentCode: 'smartRoadBridges:maintennace',
                            title: '养护记录-新增'
                        }
                    },
                    {
                        path: 'maintenanceLedgerDetail',
                        name: 'maintenanceLedgerDetail',
                        component: () => require.ensure([], (require) => require('@/views/trafficManage/wisdomRoadBridge/maintenanceLedger/detail')),
                        // component: () => import('@/views/trafficManage/wisdomRoadBridge/maintenanceLedger/detail'),
                        meta: {
                            parentCode: 'smartRoadBridges:maintennace',
                            title: '养护记录-详情'
                        }
                    },
                    {
                        path: 'roadManagement',
                        name: 'smartRoadBridges:roadManage',
                        component: () => require.ensure([], (require) => require('@/views/trafficManage/wisdomRoadBridge/roadManagement/index')),
                        // component: () => import('@/views/trafficManage/wisdomRoadBridge/roadManagement/index'),
                        meta: {
                            authCode: 'smartRoadBridges:roadManage',
                            title: '道路管理'
                        }
                    },
                    {
                        path: 'bridgeManagement',
                        name: 'smartRoadBridges:bridgesManage',
                        component: () => require.ensure([], (require) => require('@/views/trafficManage/wisdomRoadBridge/bridgeManagement/index')),
                        // component: () => import('@/views/trafficManage/wisdomRoadBridge/bridgeManagement/index'),
                        meta: {
                            authCode: 'smartRoadBridges:bridgesManage',
                            title: '桥梁管理'
                        }
                    },
                    {
                        path: 'TestReport',
                        name: 'smartRoadBridges:detect',
                        component: () => require.ensure([], (require) => require('@/views/trafficManage/wisdomRoadBridge/TestReport/index')),
                        // component: () => import('@/views/trafficManage/wisdomRoadBridge/TestReport/index'),
                        meta: {
                            authCode: 'smartRoadBridges:detect',
                            title: '检测报告'
                        }
                    },
                    {
                        path: 'TestReportAdd',
                        name: 'TestReportAdd',
                        component: () => require.ensure([], (require) => require('@/views/trafficManage/wisdomRoadBridge/TestReport/add')),
                        // component: () => import('@/views/trafficManage/wisdomRoadBridge/TestReport/add'),
                        meta: {
                            parentCode: 'smartRoadBridges:detect',
                            title: '检测报告-新增'
                        }
                    },
                    {
                        path: 'TestReportDetail',
                        name: 'TestReportDetail',
                        component: () => require.ensure([], (require) => require('@/views/trafficManage/wisdomRoadBridge/TestReport/detail')),
                        // component: () => import('@/views/trafficManage/wisdomRoadBridge/TestReport/detail'),
                        meta: {
                            parentCode: 'smartRoadBridges:detect',
                            title: '检测报告-详情'
                        }
                    },
                    {
                        path: 'bridgeMap',
                        name: 'smartRoadBridges:roadBridgeMap',
                        component: () => require.ensure([], (require) => require('@/views/trafficManage/wisdomRoadBridge/bridgeMap/index')),
                        // component: () => import('@/views/trafficManage/wisdomRoadBridge/bridgeMap/index'),
                        meta: {
                            authCode: 'smartRoadBridges:roadBridgeMap',
                            title: '道桥地图'
                        }
                    }, {
                        path: 'statisticalAnalysis',
                        name: 'smartRoadBridges:statisticalAnalysis',
                        component: () => require.ensure([], (require) => require('@/views/trafficManage/wisdomRoadBridge/statisticalAnalysis/index.vue')),
                        meta: {
                            authCode: 'smartRoadBridges:statisticalAnalysis',
                            title: '统计分析'
                        }
                    },
                    {
                        path: 'bridgeFile',
                        name: 'smartRoadBridges:bridgesFile',
                        component: () => require.ensure([], (require) => require('@/views/trafficManage/wisdomRoadBridge/bridgeFile/index.vue')),
                        meta: {
                            authCode: 'smartRoadBridges:bridgesFile',
                            title: '桥梁资料'
                        }
                    },
                    {
                        path: 'roadFile',
                        name: 'smartRoadBridges:roadFile',
                        component: () => require.ensure([], (require) => require('@/views/trafficManage/wisdomRoadBridge/roadFile/index.vue')),
                        meta: {
                            authCode: 'smartRoadBridges:roadFile',
                            title: '道路资料'
                        }
                    }
                ]
            }
        ]
    },
    ...common
];
routerList.forEach(i => {
    constantRoutes[0].children.push(i.default)
})
const router = createRouter({
    history: createWebHashHistory(),
    // history: createMemoryHistory(process.env.BASE_URL),
    routes: constantRoutes
});

// 重定向到404
export const asyncRoutes = [
    { path: '*', redirect: '/404', hidden: true }
]


export default router;
