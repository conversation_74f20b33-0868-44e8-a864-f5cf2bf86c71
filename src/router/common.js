import Layout from '@/layout'

export default [
    {
        path: '/common',
        component: Layout,
        meta: {},
        children: [
            {
                path: 'openIframe',
                name: 'openIframe',
                component: () => import('@/views/common/openIframe/index'),
                props: true,
                meta: {
                    authCode: 'openIframe',
                }
            },
            {
                path: 'messages',
                name: 'messages',
                component: () => import('@/views/common/messages/index'),
                meta: { hidden: true }

            },
            {
                path: 'messagesDetail',
                name: 'messages:detail',
                component: () => import('@/views/common/messages/detail'),
                meta: { hidden: true }
            }
        ]
    },
    {
        path: '/login',
        component: () => import('@/views/login/index'),
        meta: { hidden: true }
    },
    {
        path: '/personal',
        component: () => import('@/views/personal/index'),
        meta: {}
    },
    {
        path: '/404',
        component: () => import('@/views/error-page/404'),
        meta: { hidden: true }
    },
    {
        path: '/401',
        name: '401',
        component: () => import('@/views/error-page/401'),
        meta: { hidden: true }
    },
    { path: '/:pathMatch(.*)*', redirect: '/', meta: { hidden: true }}
]
