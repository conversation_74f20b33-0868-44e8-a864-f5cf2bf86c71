/**
 * 应急预案
 */
export default {
  path: '/emergencyPlan',
  name: 'emergencyPlan',
  meta: {
    authCode: 'emergencyResponse:emergencyPlan',
    title: '应急预案'
  },
  children: [
    {
      path: 'taskPlanManagement',
      name: 'emergencyResponse:emergencyPlan:schemeManage',
      component: () => require.ensure([], (require) => require('@/views/safeManage/emergencyManagement/emergencyPlan/taskPlanManagement/index.vue')),
      meta: {
        authCode: 'emergencyResponse:emergencyPlan:schemeManage',
        title: '任务方案管理'
      },
    },
    {
      path: 'taskPlanManagementDetail',
      name: 'taskPlanManagement:detail',
      component: () => require.ensure([], (require) => require('@/views/safeManage/emergencyManagement/emergencyPlan/taskPlanManagement/detail.vue')),
      meta: {
        parentCode: 'emergencyResponse:emergencyPlan:schemeManage',
        title: '任务方案管理详情'
      },
    },
    {
      path: 'taskPlanManagementAdd',
      name: 'taskPlanManagement:add',
      component: () => require.ensure([], (require) => require('@/views/safeManage/emergencyManagement/emergencyPlan/taskPlanManagement/add.vue')),
      meta: {
        parentCode: 'emergencyResponse:emergencyPlan:schemeManage',
        title: '任务方案管理新增'
      },
    },
    {
      path: 'planManagement',
      name: 'emergencyResponse:emergencyPlan:planManage',
      component: () => require.ensure([], (require) => require('@/views/safeManage/emergencyManagement/emergencyPlan/emergencyPlanManagement/index.vue')),
      meta: {
        authCode: 'emergencyResponse:emergencyPlan:planManage',
        title: '应急预案管理'
      },
    },
    {
      path: 'planManagementAdd',
      name: 'emergencyResponse:emergencyPlan:planManage:add',
      component: () => require.ensure([], (require) => require('@/views/safeManage/emergencyManagement/emergencyPlan/emergencyPlanManagement/add.vue')),
      meta: {
        parentCode: 'emergencyResponse:emergencyPlan:planManage',
        title: '应急预案管理-新增'
      },
    },
    {
      path: 'planManagementDetail',
      name: 'emergencyResponse:emergencyPlan:planManage:detail',
      component: () => require.ensure([], (require) => require('@/views/safeManage/emergencyManagement/emergencyPlan/emergencyPlanManagement/detail.vue')),
      meta: {
        parentCode: 'emergencyResponse:emergencyPlan:planManage',
        title: '应急预案管理-详情'
      },
    },

  ]
}