/**
 * 能源能耗
 */
export default {
    path: 'energyConsumption',
    name: 'energyConsumption',
    redirect: '/energyConsumption',
    meta: {
        authCode: 'energyConsumption',
        title: '能源能耗',
    },
    children: [
        {
            path: 'dataImport',
            name: 'consumption:dataImport',
            component: () =>
                require.ensure([], (require) =>
                    require('@/views/safeManage/energyConsumption/dataImport')
                ),
            meta: {
                authCode: 'consumption:dataImport',
                title: '能耗数据导入',
            },
        },
        {
            path: 'alarmManagement',
            name: 'consumption:alarmRecord',
            component: () =>
                require.ensure([], (require) =>
                    require('@/views/safeManage/energyConsumption/alarmRecord')
                ),
            meta: {
                authCode: 'consumption:alarmRecord',
                title: '告警管理',
            },
        },
        {
            path: 'partitionManagemant',
            name: 'consumption:baseInfo:partition',
            component: () =>
                require.ensure([], (require) =>
                    require('@/views/safeManage/energyConsumption/baseInfoManagement/partitionManagemant')
                ),

            meta: {
                authCode: 'consumption:baseInfo:partition',
                title: '能耗分区管理',
            },
        },
        {
            path: 'subItemManagement',
            name: 'consumption:baseInfo:subItem',
            component: () =>
                require.ensure([], (require) =>
                    require('@/views/safeManage/energyConsumption/baseInfoManagement/subItemManagement')
                ),

            meta: {
                authCode: 'consumption:baseInfo:subItem',
                title: '分项管理',
            },
        },
        {
            path: 'collectionPointManagement',
            name: 'consumption:baseInfo:collectionPoint',
            component: () =>
                require.ensure([], (require) =>
                    require('@/views/safeManage/energyConsumption/baseInfoManagement/collectionPointManagement')
                ),

            meta: {
                authCode: 'consumption:baseInfo:collectionPoint',
                title: '采集点管理',
            },
        },
        {
            path: 'meterFileManagement',
            name: 'consumption:baseInfo:device',
            component: () =>
                require.ensure([], (require) =>
                    require('@/views/safeManage/energyConsumption/baseInfoManagement/meterFileManagement')
                ),

            meta: {
                authCode: 'consumption:baseInfo:device',
                title: '表计档案管理',
            },
        },
        {
            path: 'meterFileDetail',
            name: 'consumption:baseInfo:device:detail',
            component: () =>
                require.ensure([], (require) =>
                    require('@/views/safeManage/energyConsumption/baseInfoManagement/meterFileManagement/detail')
                ),

            meta: {
                parentCode: 'consumption:baseInfo:device',
                title: '表计档案管理 - 查看',
            },
        },
        {
            path: 'analysis',
            name: 'consumption:analysis',
            component: () =>
                require.ensure([], (require) =>
                    require('@/views/safeManage/energyConsumption/consumptionAnalysisMap/index.vue')
                ),
            meta: {
                authCode: 'consumption:analysis',
                title: '能耗分析',
            },
        },
        {
            path: 'map',
            name: 'consumption:map',
            component: () =>
                require.ensure([], (require) =>
                    require('@/views/safeManage/energyConsumption/consumptionMap/index.vue')
                ),
            meta: {
                authCode: 'consumption:map',
                title: '能耗地图',
            },
        },
        {
            path: 'report',
            name: 'consumption:report',
            component: () =>
                require.ensure([], (require) =>
                    require('@/views/safeManage/energyConsumption/consumptionReport/index.vue')
                ),
            meta: {
                authCode: 'consumption:report',
                title: '能耗报表',
            },
        },
        {
            path: 'consumptionReportDetail',
            name: 'consumption:report:detail',
            component: () =>
                require.ensure([], (require) =>
                    require('@/views/safeManage/energyConsumption/consumptionReport/detail.vue')
                ),
            meta: {
                parentCode: 'consumption:report',
                title: '能耗报表-详情',
            },
        },
        {
            path: 'overview',
            name: 'overview',
            component: () =>
                require.ensure([], (require) =>
                    require('@/views/safeManage/energyConsumption/overview/index.vue')
                ),
            meta: {
                parentCode: 'consumption:overview',
                title: '能耗概览',
            },
        },
        {
            path: 'statisticAnalysis',
            name: 'consumption:statisticAnalysis',
            component: () =>
                require.ensure([], (require) =>
                    require('@/views/safeManage/energyConsumption/statisticAnalysis/index.vue')
                ),
            meta: {
                authCode: 'consumption:statisticAnalysis',
                title: '统计分析',
            },
        },
        {
            path: 'energyFlowAnalysis',
            name: 'consumption:energyFlowAnalysis',
            component: () =>
                require.ensure([], (require) =>
                    require('@/views/safeManage/energyConsumption/energyFlowAnalysis/index.vue')
                ),
            meta: {
                authCode: 'consumption:statisticAnalysis',
                title: '能源流向分析',
            },
        },
    ],
};
