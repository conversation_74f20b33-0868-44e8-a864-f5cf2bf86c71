/**
 * 智慧管网
 */
export default // 智慧管网
    {
        path: 'smartNetwork',
        name: 'smartNetwork',
        redirect: '/smartNetwork/wisdomNetworkOverViewMap',
        meta: {
            authCode: 'smartNetwork',
            title: '智慧管网'
        },
        children: [
            {
                path: 'pipelineManagement/standard',
                name: 'smartNetwork:pipelineManagement:standard',
                component: () => require.ensure([], (require) => require('@/views/safeManage/smartNetwork/analysisConfig/index.vue')),
                meta: {
                    authCode: 'smartNetwork:pipelineManagement:standard',
                    title: '管网分析配置'
                }
            },
            {
                path: 'pipelineManagement/statistic',
                name: 'smartNetwork:pipelineManagement:statistic',
                component: () => require.ensure([], (require) => require('@/views/safeManage/smartNetwork/statisticalAnalysis/index.vue')),
                meta: {
                    authCode: 'smartNetwork:pipelineManagement:statistic',
                    title: '管网统计分析'
                }
            },
            {
                path: 'wisdomNetworkOverViewMap',
                name: 'smartNetwork:wisdomNetworkOverViewMap',
                component: () => require.ensure([], (require) => require('@/views/safeManage/smartNetwork/wisdomDrainPipe/index.vue')),
                meta: {
                    authCode: 'smartNetwork:wisdomNetworkOverViewMap',
                    title: '排水可视化'
                }
            },
            {
                path: 'drainageHouseholdManagement',
                name: 'smartNetwork:drainageHouseholdManagement',
                // redirect: '/smartNetwork/smartNetworkOverView',
                // component: () => require.ensure([], (require) => require('@/views/safeManage/wisdomFireHydrant/fireHydrantManage/index.vue')),
                // component: () => import('@/views/safeManage/wisdomFireHydrant/fireHydrantManage/index.vue'),
                meta: {
                    authCode: 'smartNetwork:drainageHouseholdManagement',
                    title: '排水户管理'
                },
                children: [
                    {
                        path: 'drainageHouseholdArchives',
                        name: 'smartNetwork:drainageHouseholdManagement:drainageHouseholdArchives',
                        component: () => require.ensure([], (require) => require('@/views/safeManage/smartNetwork/drainageHouseholdArchives/index.vue')),
                        meta: {
                            authCode: 'smartNetwork:drainageHouseholdManagement:drainageHouseholdArchives',
                            title: '排水户档案'
                        }
                    },
                    {
                        path: 'drainageHouseholdArchivesAdd',
                        name: 'smartNetwork:drainageHouseholdManagement:drainageHouseholdArchives:add',
                        component: () => require.ensure([], (require) => require('@/views/safeManage/smartNetwork/drainageHouseholdArchives/add.vue')),
                        meta: {
                            parentCode: 'smartNetwork:drainageHouseholdManagement:drainageHouseholdArchives',
                            title: '排水户档案-新增'
                        }
                    },
                    {
                        path: 'drainageHouseholdArchivesDetail',
                        name: 'smartNetwork:drainageHouseholdManagement:drainageHouseholdArchives:detail',
                        component: () => require.ensure([], (require) => require('@/views/safeManage/smartNetwork/drainageHouseholdArchives/detail.vue')),
                        meta: {
                            parentCode: 'smartNetwork:drainageHouseholdManagement:drainageHouseholdArchives',
                            title: '排水户档案-详情'
                        }
                    },
                    {
                        path: 'drainageHouseholdCheck',
                        name: 'smartNetwork:drainageHouseholdManagement:drainageHouseholdCheck',
                        component: () => require.ensure([], (require) => require('@/views/safeManage/smartNetwork/drainageHouseholdCheck/index.vue')),
                        meta: {
                            authCode: 'smartNetwork:drainageHouseholdManagement:drainageHouseholdCheck',
                            title: '排水户检查'
                        }
                    },
                    {
                        path: 'drainageHouseholdCheckAdd',
                        name: 'smartNetwork:drainageHouseholdManagement:drainageHouseholdCheckAdd',
                        component: () => require.ensure([], (require) => require('@/views/safeManage/smartNetwork/drainageHouseholdCheck/add.vue')),
                        meta: {
                            parentCode: 'smartNetwork:drainageHouseholdManagement:drainageHouseholdCheck',
                            title: '排水户检查-新增'
                        }
                    },
                    {
                        path: 'drainageHouseholdCheckDetail',
                        name: 'smartNetwork:drainageHouseholdManagement:drainageHouseholdCheckDetail',
                        component: () => require.ensure([], (require) => require('@/views/safeManage/smartNetwork/drainageHouseholdCheck/detail.vue')),
                        meta: {
                            parentCode: 'smartNetwork:drainageHouseholdManagement:drainageHouseholdCheck',
                            title: '排水户检查-详情'
                        }
                    }
                ]
            },
            {
                path: 'drainInspect',
                name: 'smartNetwork:drainInspect',
                redirect: '/smartNetwork/drainInspect/monitorInspectPlan',
                meta: {
                    authCode: 'smartNetwork:drainInspect',
                    title: '排水户检查'
                },
                children: [
                  {
                      path: 'monitorInspectPlan',
                      name: 'smartNetwork:drainInspect:monitorInspectPlan',
                      component: () => require.ensure([], (require) => require('@/views/safeManage/smartNetwork/drainInspect/monitorInspectPlan/index.vue')),
                      meta: {
                          authCode: 'smartNetwork:drainInspect:monitorInspectPlan',
                          title: '监测检查计划'
                      }
                  },
                  {
                      path: 'monitorInspectPlanAdd',
                      name: 'smartNetwork:drainInspect:monitorInspectPlanAdd',
                      component: () => require.ensure([], (require) => require('@/views/safeManage/smartNetwork/drainInspect/monitorInspectPlan/add.vue')),
                      meta: {
                          parentCode: 'smartNetwork:drainInspect:monitorInspectPlan',
                          title: '监测检查计划-新增'
                      }
                  },
                  {
                      path: 'monitorInspectPlanDetail',
                      name: 'smartNetwork:drainInspect:monitorInspectPlanDetail',
                      component: () => require.ensure([], (require) => require('@/views/safeManage/smartNetwork/drainInspect/monitorInspectPlan/detail.vue')),
                      meta: {
                          parentCode: 'smartNetwork:drainInspect:monitorInspectPlan',
                          title: '监测检查计划-详情'
                      }
                  },
                  {
                      path: 'monitorInspectPlanFillIn',
                      name: 'smartNetwork:drainInspect:monitorInspectPlanFillIn',
                      component: () => require.ensure([], (require) => require('@/views/safeManage/smartNetwork/drainInspect/monitorInspectPlan/inspectorFillIn.vue')),
                      meta: {
                          parentCode: 'smartNetwork:drainInspect:monitorInspectPlan',
                          title: '监测检查计划-检查人填写'
                      }
                  },
                    {
                      path: 'rectifyPlan',
                      name: 'smartNetwork:drainInspect:rectifyPlan',
                      component: () => require.ensure([], (require) => require('@/views/safeManage/smartNetwork/drainInspect/rectifyPlan/index.vue')),
                      meta: {
                          authCode: 'smartNetwork:drainInspect:rectifyPlan',
                          title: '整改计划'
                      }
                  },
                  {
                      path: 'rectifyPlanAdd',
                      name: 'smartNetwork:drainInspect:rectifyPlanAdd',
                      component: () => require.ensure([], (require) => require('@/views/safeManage/smartNetwork/drainInspect/rectifyPlan/add.vue')),
                      meta: {
                          parentCode: 'smartNetwork:drainInspect:rectifyPlan',
                          title: '整改计划-新增'
                      }
                  },
                  {
                      path: 'rectifyPlanDetail',
                      name: 'smartNetwork:drainInspect:rectifyPlanDetail',
                      component: () => require.ensure([], (require) => require('@/views/safeManage/smartNetwork/drainInspect/rectifyPlan/detail.vue')),
                      meta: {
                          parentCode: 'smartNetwork:drainInspect:rectifyPlan',
                          title: '整改计划-详情'
                      }
                  },
                  {
                      path: 'rectifyPlanSubmit',
                      name: 'smartNetwork:drainInspect:rectifyPlanSubmit',
                      component: () => require.ensure([], (require) => require('@/views/safeManage/smartNetwork/drainInspect/rectifyPlan/submit.vue')),
                      meta: {
                          parentCode: 'smartNetwork:drainInspect:rectifyPlan',
                          title: '整改计划-流程'
                      }
                  }
                ]
            },
            {
                path: 'pipelineManagement',
                name: 'pipelineManagement',
                meta: {
                    authCode: 'smartNetwork:pipelineManagement',
                    title: '管道管理'
                },
                children: [
                    {
                        path: 'pipelineArchives',
                        name: 'smartNetwork:pipelineManagement:pipelineArchives',
                        component: () => require.ensure([], (require) => require('@/views/safeManage/smartNetwork/pipelineArchives/index.vue')),
                        meta: {
                            authCode: 'smartNetwork:pipelineManagement:pipelineArchives',
                            title: '管道档案'
                        }
                    },
                    {
                        path: 'pipelineArchivesAdd',
                        name: 'pipelineArchivesAdd',
                        component: () => require.ensure([], (require) => require('@/views/safeManage/smartNetwork/pipelineArchives/add.vue')),
                        // component: () => import('@/views/livableManage/autoIrrigate/groupManagement/add'),
                        meta: {
                            parentCode: 'smartNetwork:pipelineManagement:pipelineArchives',
                            title: '管道档案-新增'
                        }
                    },
                    {
                        path: 'pipelineArchivesEdit',
                        name: 'pipelineArchivesEdit',
                        component: () => require.ensure([], (require) => require('@/views/safeManage/smartNetwork/pipelineArchives/detail.vue')),
                        meta: {
                            parentCode: 'smartNetwork:pipelineManagement:pipelineArchives',
                            title: '管道档案-编辑'
                        }
                    },
                    {
                        path: 'pipelineArchivesMonitor',
                        name: 'pipelineArchivesMonitor',
                        component: () => require.ensure([], (require) => require('@/views/safeManage/smartNetwork/pipelineArchives/monitor.vue')),
                        meta: {
                            parentCode: 'smartNetwork:pipelineManagement:pipelineArchives',
                            title: '管道档案-监测'
                        }
                    },
                    {
                        path: 'pipelineMonitor',
                        name: 'smartNetwork:pipelineManagement:pipelineMonitor',
                        component: () => require.ensure([], (require) => require('@/views/safeManage/smartNetwork/pipelineMonitor/index.vue')),
                        meta: {
                            authCode: 'smartNetwork:pipelineManagement:pipelineMonitor',
                            title: '管道监测'
                        }
                    },
                    {
                        path: 'pipelineMonitorDetail',
                        name: 'pipelineMonitorDetail',
                        component: () => require.ensure([], (require) => require('@/views/safeManage/smartNetwork/pipelineMonitor/detail.vue')),
                        meta: {
                            // authCode: 'smartNetwork:pipelineManagement:pipelineMonitor',
                            parentCode: 'smartNetwork:pipelineManagement:pipelineMonitor',
                            title: '管道监测:查看'
                        }
                    },
                    {
                        path: 'pipelineOverviewMap',
                        name: 'smartNetwork:pipelineManagement:pipelineOverView',
                        component: () => require.ensure([], (require) => require('@/views/safeManage/smartNetwork/pipelineOverviewMap/index.vue')),
                        meta: {
                            authCode: 'smartNetwork:pipelineManagement:pipelineOverView',
                            title: '管网概览'
                        }
                    },
                    {
                        path: 'pipelineAlarm',
                        name: 'smartNetwork:pipelineManagement:pipelineAlarm',
                        component: () => require.ensure([], (require) => require('@/views/safeManage/smartNetwork/pipelineAlarm/index.vue')),
                        meta: {
                            authCode: 'smartNetwork:pipelineManagement:pipelineAlarm',
                            title: '告警管理'
                        }
                    },

                ]
            }
        ]
    }

