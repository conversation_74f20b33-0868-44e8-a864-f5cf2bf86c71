export default
    {
        path: 'statisticalAnalysis',
        name: 'statisticalAnalysis',
        redirect: '/statisticalAnalysis/manholeAnalysis',
        meta: {
            authCode: 'statisticalAnalysis',
            title: '统计分析'
        },
        children: [
            {
                path: 'manholeAnalysis',
                name: 'manholeAnalysis',
                component: () => require.ensure([], (require) => require('@/views/safeManage/statisticalAnalysis/manholeAnalysis')),
                meta: {
                    authCode: 'manholeAnalysis',
                    title: '井盖概览'
                }
            },
            {
                path: 'fireHydrantAnalysis',
                name: 'fireHydrantAnalysis',
                component: () => require.ensure([], (require) => require('@/views/safeManage/statisticalAnalysis/fireHydrantAnalysis')),
                meta: {
                    authCode: 'fireHydrantAnalysis',
                    title: '消防栓概览'
                }
            },
            {
                path: 'securitySituationAnalysis',
                name: 'securitySituationAnalysis',
                component: () => require.ensure([], (require) => require('@/views/safeManage/statisticalAnalysis/securitySituationAnalysis')),
                meta: {
                    authCode: 'securitySituationAnalysis',
                    title: '安防态势分析'
                }
            },
            {
                path: 'publicSecuritySituationAnalysis',
                name: 'publicSecuritySituationAnalysis',
                component: () => require.ensure([], (require) => require('@/views/safeManage/statisticalAnalysis/publicSecuritySituationAnalysis')),
                meta: {
                    authCode: 'publicSecuritySituationAnalysis',
                    title: '治安态势分析'
                }
            },
        ]
    }
