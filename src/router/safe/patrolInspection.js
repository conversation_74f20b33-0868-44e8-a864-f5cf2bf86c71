export default
    {
        path: 'patrolInspection',
        name: 'patrolInspection',
        redirect: '/patrolInspection/droneManagement',
        meta: {
            authCode: 'patrolInspection',
            title: '统计分析'
        },
        children: [
            {
                path: 'droneManagement',
                name: 'droneManagement',
                component: () => require.ensure([], (require) => require('@/views/safeManage/patrolInspection/droneManagement')),
                meta: {
                    authCode: 'droneManagement',
                    title: '无人机管理'
                }
            },
            {
                path: 'droneDetail',
                name: 'droneManagement:detail',
                component: () => require.ensure([], (require) => require('@/views/safeManage/patrolInspection/droneManagement/detail.vue')),
                meta: {
                  parentCode: 'droneManagement',
                  title: '设备详情'
                }
              },
            {
                path: 'droneRealtimeMonitoring',
                name: 'droneRealtimeMonitoring',
                component: () => require.ensure([], (require) => require('@/views/safeManage/patrolInspection/droneRealtimeMonitoring')),
                meta: {
                    authCode: 'droneRealtimeMonitoring',
                    title: '无人机实时监控'
                }
            },      {
                path: 'UAVInspectionPlan',
                name: 'UAVInspectionPlan',
                component: () => require.ensure([], (require) => require('@/views/safeManage/patrolInspection/UAVInspectionPlan')),
                meta: {
                    authCode: 'UAVInspectionPlan',
                    title: '无人机巡检计划'
                }
            },   {
                path: 'UAVInspectionPlanAdd',
                name: 'UAVInspectionPlan:add',
                component: () => require.ensure([], (require) => require('@/views/safeManage/patrolInspection/UAVInspectionPlan/add')),
                meta: {
                    parentCode: 'UAVInspectionPlan',
                    title: '无人机巡检计划-新增'
                }
            },{
                path: 'UAVInspectionPlanDetail',
                name: 'UAVInspectionPlan:detail',
                component: () => require.ensure([], (require) => require('@/views/safeManage/patrolInspection/UAVInspectionPlan/detail')),
                meta: {
                    parentCode: 'UAVInspectionPlan',
                    title: '无人机巡检计划-详情'
                }
            },        {
                path: 'droneHistory',
                name: 'droneHistory',
                component: () => require.ensure([], (require) => require('@/views/safeManage/patrolInspection/droneHistory')),
                meta: {
                    authCode: 'droneHistory',
                    title: '无人机历史记录'
                }
            },      {
                path: 'UAVWarningManagement',
                name: 'UAVWarningManagement',
                component: () => require.ensure([], (require) => require('@/views/safeManage/patrolInspection/UAVWarningManagement')),
                meta: {
                    authCode: 'UAVWarningManagement',
                    title: '无人机预警管理'
                }
            },
            {
              path: 'DataAnalysis',
              name: 'DataAnalysis',
              component: () => require.ensure([], (require) => require('@/views/safeManage/patrolInspection/dataAnalysis')),
              meta: {
                authCode: 'DataAnalysis',
                title: '数据分析'
              }
            },
        ]
    }
