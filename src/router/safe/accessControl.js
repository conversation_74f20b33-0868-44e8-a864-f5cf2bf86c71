export default {
    path: '/accessControl',
    redirect: '/accessControl/deviceList',
    meta: {
        authCode: 'accessControl',
        title: '门禁管理'
    },
    children: [
        {
            path: 'deviceList',
            name: 'accessControl:deviceList',
            component: () => require.ensure([], (require) => require('@/views/safeManage/accessControlManagement/access/index.vue')),
            meta: {
                authCode: 'accessControl:deviceList',
                title: '门禁列表'
            }
        },
        {
            path: 'deviceListAdd',
            name: 'accessControl:deviceList:add',
            component: () => require.ensure([], (require) => require('@/views/safeManage/accessControlManagement/access/add.vue')),
            meta: {
                parentCode: 'accessControl:deviceList',
                title: '门禁列表-新增'
            }
        },
        {
            path: 'deviceListEdit',
            name: 'accessControl:deviceList:edit',
            component: () => require.ensure([], (require) => require('@/views/safeManage/accessControlManagement/access/detail.vue')),
            meta: {
                parentCode: 'accessControl:deviceList',
                title: '门禁列表-编辑'
            }
        },
        {
            path: 'deviceListDetail',
            name: 'accessControl:deviceList:detail',
            component: () => require.ensure([], (require) => require('@/views/safeManage/accessControlManagement/access/detail.vue')),
            meta: {
                parentCode: 'accessControl:deviceList',
                title: '门禁列表-详情'
            }
        },
        {
            path: 'authorizationRecord',
            name: 'accessControl:authorizationRecord',
            component: () => require.ensure([], (require) => require('@/views/safeManage/accessControlManagement/authorizationRecord/index.vue')),
            meta: {
                authCode: 'accessControl:authorizationRecord',
                title: '门禁授权记录'
            }
        },
        {
            path: 'authorizationRecordAdd',
            name: 'accessControl:authorizationRecord:add',
            component: () => require.ensure([], (require) => require('@/views/safeManage/accessControlManagement/authorizationRecord/add.vue')),
            meta: {
                parentCode: 'accessControl:authorizationRecord',
                title: '门禁授权记录-新增'
            }
        },
        {
            path: 'authorizationRecordDetail',
            name: 'accessControl:authorizationRecord:detail',
            component: () => require.ensure([], (require) => require('@/views/safeManage/accessControlManagement/authorizationRecord/detail.vue')),
            meta: {
                parentCode: 'accessControl:authorizationRecord',
                title: '门禁授权记录-详情'
            }
        }
    ]
}
