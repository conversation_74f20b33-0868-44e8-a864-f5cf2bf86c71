export default {
    path: '/controlManagement',
    redirect: '/controlManagement/groupManagement',
    meta: {
        authCode: 'controlManagement',
        title: '布控管理'
    },
    children: [
        {
            path: 'controlMap',
            name: 'controlManagement:controlMap',
            component: () => require.ensure([], (require) => require('@/views/safeManage/controlManagement/controlMap/index.vue')),
            meta: {
                authCode: 'controlManagement:controlMap',
                title: '布控地图'
            }
        },
        {
            path: 'groupManagement',
            name: 'controlManagement:groupManagement',
            component: () => require.ensure([], (require) => require('@/views/safeManage/controlManagement/groupManagement/index.vue')),
            meta: {
                authCode: 'controlManagement:groupManagement',
                title: '分组管理'
            }
        }, {
            path: 'groupManagementDetail',
            name: 'controlManagement:groupManagement:detail',
            component: () => require.ensure([], (require) => require('@/views/safeManage/controlManagement/groupManagement/detail.vue')),
            meta: {
                parentCode: 'controlManagement:groupManagement',
                title: '分组管理-详情'
            }
        }, {
            path: 'cameraManagement',
            name: 'controlManagement:cameraManagement',
            component: () => require.ensure([], (require) => require('@/views/safeManage/controlManagement/cameraManagement/index.vue')),
            meta: {
                authCode: 'controlManagement:cameraManagement',
                title: '摄像头管理'
            }
        }, {
            path: 'cameraManagementDetail',
            name: 'controlManagement:cameraManagement:detail',
            component: () => require.ensure([], (require) => require('@/views/safeManage/controlManagement/cameraManagement/detail.vue')),
            meta: {
                parentCode: 'controlManagement:cameraManagement',
                title: '摄像头管理-详情'
            }
        }, {
            path: 'cameraManagementlargeScreen',
            name: 'controlManagement:cameraManagement:largeScreen',
            component: () => require.ensure([], (require) => require('@/views/safeManage/controlManagement/cameraManagement/bigScreenVideo/home.vue')),
            meta: {
                parentCode: 'controlManagement:cameraManagement',
                title: '摄像头管理-大屏监控'
            }
        },
        {
            path: 'controlVehicleDepot',
            name: 'controlManagement:controlVehicleDepot',
            component: () => require.ensure([], (require) => require('@/views/safeManage/controlManagement/controlVehicleDepot/index.vue')),
            meta: {
                authCode: 'controlManagement:controlVehicleDepot',
                title: '布控车辆库'
            }
        }, {
            path: 'addControlVehicleDepot',
            name: 'controlManagement:controlVehicleDepot:add',
            component: () => require.ensure([], (require) => require('@/views/safeManage/controlManagement/controlVehicleDepot/add.vue')),
            meta: {
                parentCode: 'controlManagement:controlVehicleDepot',
                title: '布控车辆库-新增'
            }
        }, {
            path: 'editControlVehicleDepot',
            name: 'controlManagement:controlVehicleDepot:detail',
            component: () => require.ensure([], (require) => require('@/views/safeManage/controlManagement/controlVehicleDepot/detail.vue')),
            meta: {
                parentCode: 'controlManagement:controlVehicleDepot',
                title: '布控车辆库-详情'
            }
        }, {
            path: 'controlPersonnelPool',
            name: 'controlManagement:controlPersonnelPool',
            component: () => require.ensure([], (require) => require('@/views/safeManage/controlManagement/controlPersonnelPool/index.vue')),
            meta: {
                authCode: 'controlManagement:controlPersonnelPool',
                title: '布控人员库'
            }
        }, {
            path: 'addControlPersonnelPool',
            name: 'controlManagement:controlPersonnelPool:add',
            component: () => require.ensure([], (require) => require('@/views/safeManage/controlManagement/controlPersonnelPool/add.vue')),
            meta: {
                parentCode: 'controlManagement:controlPersonnelPool',
                title: '布控人员库-新增'
            }
        }, {
            path: 'editControlPersonnelPool',
            name: 'controlManagement:controlPersonnelPool:detail',
            component: () => require.ensure([], (require) => require('@/views/safeManage/controlManagement/controlPersonnelPool/detail.vue')),
            meta: {
                parentCode: 'controlManagement:controlPersonnelPool',
                title: '布控人员库-详情'
            }
        }, {
            path: 'controlTasks',
            name: 'controlManagement:controlTasks',
            component: () => require.ensure([], (require) => require('@/views/safeManage/controlManagement/controlTasks/index.vue')),
            meta: {
                authCode: 'controlManagement:controlTasks',
                title: '布控任务'
            }
        }, {
            path: 'addControlTasks',
            name: 'controlManagement:controlTasks:add',
            component: () => require.ensure([], (require) => require('@/views/safeManage/controlManagement/controlTasks/add.vue')),
            meta: {
                parentCode: 'controlManagement:controlTasks',
                title: '布控任务-新增'
            }
        }, {
            path: 'editControlTasks',
            name: 'controlManagement:controlTasks:detail',
            component: () => require.ensure([], (require) => require('@/views/safeManage/controlManagement/controlTasks/detail.vue')),
            meta: {
                parentCode: 'controlManagement:controlTasks',
                title: '布控任务-详情'
            }
        }, {
            path: 'identificationRecord',
            name: 'controlManagement:identificationRecord',
            component: () => require.ensure([], (require) => require('@/views/safeManage/controlManagement/identificationRecord/index.vue')),
            meta: {
                authCode: 'controlManagement:identificationRecord',
                title: '识别记录'
            }
        }, {
            path: 'detailIdentificationRecord',
            name: 'controlManagement:identificationRecord:detail',
            component: () => require.ensure([], (require) => require('@/views/safeManage/controlManagement/identificationRecord/detail.vue')),
            meta: {
                parentCode: 'controlManagement:identificationRecord',
                title: '识别记录-详情'
            }
        }, {
            path: 'tracingTasks',
            name: 'controlManagement:tracingTasks',
            component: () => require.ensure([], (require) => require('@/views/safeManage/controlManagement/tracingTasks/index.vue')),
            meta: {
                authCode: 'controlManagement:tracingTasks',
                title: '追溯任务'
            }
        }, {
            path: 'addTracingTasks',
            name: 'controlManagement:tracingTasks:add',
            component: () => require.ensure([], (require) => require('@/views/safeManage/controlManagement/tracingTasks/add.vue')),
            meta: {
                parentCode: 'controlManagement:tracingTasks',
                title: '追溯任务-新增'
            }
        }, {
            path: 'trajectoryMap',
            name: 'controlManagement:tracingTasks:trajectoryMap',
            component: () => require.ensure([], (require) => require('@/views/safeManage/controlManagement/tracingTasks/trajectoryMap.vue')),
            meta: {
                parentCode: 'controlManagement:tracingTasks',
                title: '追溯轨迹'
            }
        }, {
            path: 'cameraGroup',
            name: 'controlManagement:cameraGroup',
            component: () => require.ensure([], (require) => require('@/views/safeManage/controlManagement/cameraGroup/index.vue')),
            meta: {
                authCode: 'controlManagement:cameraGroup',
                title: '摄像机组'
            }
        },
        {
            path: 'cameraGroupAdd',
            name: 'controlManagement:cameraGroup:add',
            component: () => require.ensure([], (require) => require('@/views/safeManage/controlManagement/cameraGroup/add.vue')),
            meta: {
                parentCode: 'controlManagement:cameraGroup',
                title: '摄像机组 - 新增'
            }
        },
        {
            path: 'cameraGroupDetail',
            name: 'controlManagement:cameraGroup:detail',
            component: () => require.ensure([], (require) => require('@/views/safeManage/controlManagement/cameraGroup/detail.vue')),
            meta: {
                parentCode: 'controlManagement:cameraGroup',
                title: '摄像机组 - 查看'
            }
        },
    ]
}
