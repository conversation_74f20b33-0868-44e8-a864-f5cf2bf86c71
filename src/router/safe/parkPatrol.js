/**
 * 园区巡查
 */
export default
  {
    path: '/parkPatrol',
    meta: {
      autoCode: 'parkPatrol',
      title: '园区巡查'
    },
    children: [
      {
        path: 'plan',
        name: 'parkPatrol:plan',
        component: () => require.ensure([], (require) => require('@/views/safeManage/parkPatrol/plan/index.vue')),
        meta: {
          authCode: 'parkPatrol:plan',
          title: '云巡检计划'
        }
      },
      {
        path: 'planAdd',
        name: 'parkPatrol:plan:add',
        component: () => require.ensure([], (require) => require('@/views/safeManage/parkPatrol/plan/add.vue')),
        meta: {
          parentCode: 'parkPatrol:plan',
          title: '云巡检计划-新增'
        }
      },
      {
        path: 'planDetail',
        name: 'parkPatrol:plan:detail',
        component: () => require.ensure([], (require) => require('@/views/safeManage/parkPatrol/plan/detail.vue')),
        meta: {
          parentCode: 'parkPatrol:plan',
          title: '云巡检计划-详情'
        }
      },
      {
        path: 'report',
        name: 'parkPatrol:report',
        component: () => require.ensure([], (require) => require('@/views/safeManage/parkPatrol/report/index.vue')),
        meta: {
          authCode: 'parkPatrol:report',
          title: '云巡检报告'
        }
      },
      {
        path: 'reportDetail',
        name: 'parkPatrol:report:detail',
        component: () => require.ensure([], (require) => require('@/views/safeManage/parkPatrol/report/detail.vue')),
        meta: {
          parentCode: 'parkPatrol:report',
          title: '云巡检报告-详情'
        }
      },
      {
        path: 'category',
        name: 'parkPatrol:category',
        component: () => require.ensure([], (require) => require('@/views/safeManage/parkPatrol/categoryManagement/index.vue')),
        meta: {
          authCode: 'parkPatrol:category',
          title: '巡检类型管理'
        }
      },
      {
        path: 'programme',
        name: 'parkPatrol:programme',
        component: () => require.ensure([], (require) => require('@/views/safeManage/parkPatrol/programmeManagement/index.vue')),
        meta: {
          authCode: 'parkPatrol:programme',
          title: '巡检方案管理'
        }
      },
      {
        path: 'programmeDetail',
        name: 'parkPatrol:programme:detail',
        component: () => require.ensure([], (require) => require('@/views/safeManage/parkPatrol/programmeManagement/detail.vue')),
        meta: {
          parentCode: 'parkPatrol:programme',
          title: '巡检方案管理-详情'
        }
      },
      {
        path: 'programmeAdd',
        name: 'parkPatrol:programme:add',
        component: () => require.ensure([], (require) => require('@/views/safeManage/parkPatrol/programmeManagement/add.vue')),
        meta: {
          parentCode: 'parkPatrol:programme',
          title: '巡检方案管理-详情'
        }
      },
      {
        path: 'prowlAlarm',
        name: 'parkPatrol:prowlAlarm',
        component: () => require.ensure([], (require) => require('@/views/safeManage/parkPatrol/prowlAlarm/index.vue')),
        meta: {
          authCode: 'parkPatrol:prowlAlarm',
          title: '巡检告警'
        }
      },
    ]
  }
