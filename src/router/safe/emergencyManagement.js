/**
 * 应急处置管理
 */
export default {
    path: '/emergencyResource',
    redirect: '/emergencyManagement/emergencyScreen',
    meta: {
        authCode: 'emergencyManagement',
        title: '应急处置管理'
    },
    children: [
      {
          path: 'emergencyScreen',
          component: () => require.ensure([], (require) => require('@/views/safeManage/emergencyManagement/emergencyScreen/index.vue')),
          name: 'emergencyResource:emergencyScreen',
          meta: {
              authCode: 'emergencyResource:emergencyScreen',
              title: '应急大屏'
          }
      },
        {
            path: 'emergencyResource',
            redirect: '/emergencyManagement/emergencyPerson',
            meta: {
                authCode: 'emergencyManagement:resourceManagement',
                title: '应急资源管理'
            }
        },
        {
            path: 'emergencyPerson',
            component: () => require.ensure([], (require) => require('@/views/safeManage/emergencyManagement/resourceManagement/personnel/index.vue')),
            name: 'emergencyResponse:emergencyResource:emergencyPerson',
            meta: {
                authCode: 'emergencyResponse:emergencyResource:emergencyPerson',
                title: '应急人员'
            }
        },
        {
            path: 'emergencyPersonAdd',
            component: () => require.ensure([], (require) => require('@/views/safeManage/emergencyManagement/resourceManagement/personnel/add.vue')),
            name: 'emergencyManagement:resourceManagement:personnelAdd',
            meta: {
                parentCode: 'emergencyResponse:emergencyResource:emergencyPerson',
                title: '应急人员-新增'
            }
        },
        {
            path: 'emergencyPersonDetail',
            component: () => require.ensure([], (require) => require('@/views/safeManage/emergencyManagement/resourceManagement/personnel/detail.vue')),
            name: 'emergencyManagement:resourceManagement:personnelDetail',
            meta: {
                parentCode: 'emergencyResponse:emergencyResource:emergencyPerson',
                title: '应急人员-详情'
            }
        },
        {
            path: 'emergencyTeam',
            component: () => require.ensure([], (require) => require('@/views/safeManage/emergencyManagement/resourceManagement/team/index.vue')),
            name: 'emergencyResponse:emergencyResource:emergencyTeam',
            meta: {
                authCode: 'emergencyResponse:emergencyResource:emergencyTeam',
                title: '应急队伍'
            }
        },
        {
            path: 'emergencyTeamAdd',
            component: () => require.ensure([], (require) => require('@/views/safeManage/emergencyManagement/resourceManagement/team/add.vue')),
            name: 'emergencyManagement:resourceManagement:emergencyTeamAdd',
            meta: {
                parentCode: 'emergencyResponse:emergencyResource:emergencyTeam',
                title: '应急队伍-新增'
            }
        },
        {
            path: 'emergencyTeamDetail',
            component: () => require.ensure([], (require) => require('@/views/safeManage/emergencyManagement/resourceManagement/team/detail.vue')),
            name: 'emergencyManagement:resourceManagement:emergencyTeamDetail',
            meta: {
                parentCode: 'emergencyResponse:emergencyResource:emergencyTeam',
                title: '应急队伍-详情'
            }
        },
        {
            path: 'emergencyExpert',
            component: () => require.ensure([], (require) => require('@/views/safeManage/emergencyManagement/resourceManagement/professor/index.vue')),
            name: 'emergencyResponse:emergencyResource:emergencyExpert',
            meta: {
                authCode: 'emergencyResponse:emergencyResource:emergencyExpert',
                title: '应急专家'
            }
        },
        {
            path: 'emergencyExpertAdd',
            component: () => require.ensure([], (require) => require('@/views/safeManage/emergencyManagement/resourceManagement/professor/add.vue')),
            name: 'emergencyResponse:emergencyResource:emergencyExpertAdd',
            meta: {
                authCode: 'emergencyResponse:emergencyResource:emergencyExpert',
                title: '应急专家-新增'
            }
        },
        {
            path: 'emergencyExpertDetail',
            component: () => require.ensure([], (require) => require('@/views/safeManage/emergencyManagement/resourceManagement/professor/detail.vue')),
            name: 'emergencyResponse:emergencyResource:emergencyExpertDetail',
            meta: {
                authCode: 'emergencyResponse:emergencyResource:emergencyExpert',
                title: '应急专家-详情'
            }
        },
        {
            path: 'emergencyExpertGroup',
            component: () => require.ensure([], (require) => require('@/views/safeManage/emergencyManagement/resourceManagement/professorTeam/index.vue')),
            name: 'emergencyResponse:emergencyResource:emergencyExpertGroup',
            meta: {
                authCode: 'emergencyResponse:emergencyResource:emergencyExpertGroup',
                title: '应急专家组'
            }
        },
        {
            path: 'emergencyExpertGroupAdd',
            component: () => require.ensure([], (require) => require('@/views/safeManage/emergencyManagement/resourceManagement/professorTeam/add.vue')),
            name: 'emergencyResponse:emergencyResource:emergencyExpertGroupAdd',
            meta: {
                parentCode: 'emergencyResponse:emergencyResource:emergencyExpertGroup',
                title: '应急专家组-新增'
            }
        },
        {
            path: 'emergencyExpertGroupDetail',
            component: () => require.ensure([], (require) => require('@/views/safeManage/emergencyManagement/resourceManagement/professorTeam/detail.vue')),
            name: 'emergencyResponse:emergencyResource:emergencyExpertGroupDetail',
            meta: {
                parentCode: 'emergencyResponse:emergencyResource:emergencyExpertGroup',
                title: '应急专家组-详情'
            }
        },
        {
            path: 'emergencySupplyLibrary',
            component: () => require.ensure([], (require) => require('@/views/safeManage/emergencyManagement/resourceManagement/materialStorage/index.vue')),
            name: 'emergencyResponse:emergencyResource:emergencySupplyLibrary',
            meta: {
                authCode: 'emergencyResponse:emergencyResource:emergencySupplyLibrary',
                title: '应急物资储备库'
            }
        },
        {
            path: 'emergencySupplyLibraryAdd',
            component: () => require.ensure([], (require) => require('@/views/safeManage/emergencyManagement/resourceManagement/materialStorage/add.vue')),
            name: 'emergencyResponse:emergencyResource:emergencySupplyLibraryAdd',
            meta: {
                parentCode: 'emergencyResponse:emergencyResource:emergencySupplyLibrary',
                title: '应急物资储备库-新增'
            }
        },
        {
            path: 'emergencySupplyLibraryDetail',
            component: () => require.ensure([], (require) => require('@/views/safeManage/emergencyManagement/resourceManagement/materialStorage/detail.vue')),
            name: 'emergencyResponse:emergencyResource:emergencySupplyLibraryDetail',
            meta: {
                parentCode: 'emergencyResponse:emergencyResource:emergencySupplyLibrary',
                title: '应急物资储备库-详情'
            }
        },
        {
            path: 'emergencySupply',
            component: () => require.ensure([], (require) => require('@/views/safeManage/emergencyManagement/resourceManagement/material/index.vue')),
            name: 'emergencyResponse:emergencyResource:emergencySupply',
            meta: {
                authCode: 'emergencyResponse:emergencyResource:emergencySupply',
                title: '应急物资'
            }
        },
        {
            path: 'emergencySupplyAdd',
            component: () => require.ensure([], (require) => require('@/views/safeManage/emergencyManagement/resourceManagement/material/add.vue')),
            name: 'emergencyResponse:emergencyResource:emergencySupplyAdd',
            meta: {
                parentCode: 'emergencyResponse:emergencyResource:emergencySupply',
                title: '应急物资-新增'
            }
        },
        {
            path: 'emergencySupplyDetail',
            component: () => require.ensure([], (require) => require('@/views/safeManage/emergencyManagement/resourceManagement/material/detail.vue')),
            name: 'emergencyResponse:emergencyResource:emergencySupplyDetail',
            meta: {
                parentCode: 'emergencyResponse:emergencyResource:emergencySupply',
                title: '应急物资-详情'
            }
        },
        {
            path: 'emergencySupplyOutIn',
            component: () => require.ensure([], (require) => require('@/views/safeManage/emergencyManagement/resourceManagement/material/outInStore.vue')),
            name: 'emergencyResponse:emergencyResource:emergencySupplyOutIn',
            meta: {
                parentCode: 'emergencyResponse:emergencyResource:emergencySupply',
                title: '应急物资-出入库'
            }
        },
        {
            path: 'taskTypeManagement',
            component: () => require.ensure([], (require) => require('@/views/safeManage/emergencyManagement/emergencyPlan/taskTypeManagement/index.vue')),
            name: 'emergencyResponse:emergencyPlan:taskType',
            meta: {
                parentCode: 'emergencyResponse:emergencyPlan:taskType',
                title: '应急预案-任务类型管理'
            }
        },
        {
            path: 'emergencyDuty',
            component: () => require.ensure([], (require) => require('@/views/safeManage/emergencyManagement/emergencyMonitoring/emergencyDuty/index.vue')),
            name: 'emergencyResponse:emergencyMonitoring:duty',
            meta: {
                parentCode: 'emergencyResponse:emergencyMonitoring',
                title: '应急监测-应急值守'
            }
        },
        {
            path: 'emergencyDutyAdd',
            component: () => require.ensure([], (require) => require('@/views/safeManage/emergencyManagement/emergencyMonitoring/emergencyDuty/add.vue')),
            name: 'emergencyResponse:emergencyMonitoring:duty:add',
            meta: {
                parentCode: 'emergencyResponse:emergencyMonitoring:duty',
                title: '应急监测-应急值守'
            }
        },
        {
            path: 'emergencyDutyDetail',
            component: () => require.ensure([], (require) => require('@/views/safeManage/emergencyManagement/emergencyMonitoring/emergencyDuty/detail.vue')),
            name: 'emergencyResponse:emergencyMonitoring:duty:detail',
            meta: {
                parentCode: 'emergencyResponse:emergencyMonitoring:duty',
                title: '应急监测-应急值守'
            }
        },
        {
            path: 'emergencyTask',
            name: 'emergencyResponse:emergencyMonitoring:task',
            component: () => require.ensure([], (require) => require('@/views/safeManage/emergencyManagement/emergencyMonitoring/taskExecute/index.vue')),
            meta: {
                authCode: 'emergencyResponse:emergencyMonitoring:task',
                title: '任务执行'
            },
        },
        {
            path: 'emergencyTaskDetail',
            name: 'emergencyResponse:emergencyMonitoring:task:detail',
            component: () => require.ensure([], (require) => require('@/views/safeManage/emergencyManagement/emergencyMonitoring/taskExecute/detail.vue')),
            meta: {
                parentCode: 'emergencyResponse:emergencyMonitoring:task',
                title: '任务执行-详情'
            },
        },
        {
            path: 'emergencyEvent',
            name: 'emergencyResponse:emergencyMonitoring:event',
            component: () => require.ensure([], (require) => require('@/views/safeManage/emergencyManagement/emergencyMonitoring/emergencyEvent/index.vue')),
            meta: {
                authCode: 'emergencyResponse:emergencyMonitoring:event',
                title: '应急事件'
            },
        },
        {
            path: 'emergencyEventDetail',
            name: 'emergencyResponse:emergencyMonitoring:event:detail',
            component: () => require.ensure([], (require) => require('@/views/safeManage/emergencyManagement/emergencyMonitoring/emergencyEvent/detail.vue')),
            meta: {
                parentCode: 'emergencyResponse:emergencyMonitoring:event',
                title: '应急事件-详情'
            },
        },
        {
            path: 'emergencyRegula',
            name: 'emergencyResponse:emergencyResource:emergencyRegula',
            component: () => require.ensure([], (require) => require('@/views/safeManage/emergencyManagement/resourceManagement/emergencyRegula/index.vue')),
            meta: {
                authCode: 'emergencyResponse:emergencyResource:emergencyRegula',
                title: '应急法规'
            },
        },
        {
            path: 'emergencyRegulaAdd',
            name: 'emergencyResponse:emergencyResource:emergencyRegula:add',
            component: () => require.ensure([], (require) => require('@/views/safeManage/emergencyManagement/resourceManagement/emergencyRegula/add.vue')),
            meta: {
                parentCode: 'emergencyResponse:emergencyResource:emergencyRegula',
                title: '应急法规 - 新增'
            },
        },
        {
            path: 'emergencyRegulaDetail',
            name: 'emergencyResponse:emergencyResource:emergencyRegula:detail',
            component: () => require.ensure([], (require) => require('@/views/safeManage/emergencyManagement/resourceManagement/emergencyRegula/detail.vue')),
            meta: {
                parentCode: 'emergencyResponse:emergencyResource:emergencyRegula',
                title: '应急法规 - 详情'
            },
        },
        {
            path: 'emergencyOverview',
            name: 'emergencyResponse:emergencyResource:overview',
            component: () => require.ensure([], (require) => require('@/views/safeManage/emergencyManagement/resourceManagement/overview/index.vue')),
            meta: {
                authCode: 'emergencyResponse:emergencyResource:overview',
                title: '应急资源概况'
            },
        },
        {
            path: 'emergencyPlanExecute',
            name: 'emergencyResponse:emergencyMonitoring:planExecute',
            component: () => require.ensure([], (require) => require('@/views/safeManage/emergencyManagement/emergencyMonitoring/planExecute/index.vue')),
            meta: {
                authCode: 'emergencyResponse:emergencyMonitoring:planExecute',
                title: '预案执行'
            },
        },
        {
            path: 'emergencyPlanExecuteDetail',
            name: 'emergencyResponse:emergencyMonitoring:planExecute:detail',
            component: () => require.ensure([], (require) => require('@/views/safeManage/emergencyManagement/emergencyMonitoring/planExecute/detail.vue')),
            meta: {
                parentCode: 'emergencyResponse:emergencyMonitoring:planExecute',
                title: '预案执行 - 详情'
            },
        },
        {
            path: 'eventOverview',
            name: 'emergencyResponse:emergencyMonitoring:eventOverview',
            component: () => require.ensure([], (require) => require('@/views/safeManage/emergencyManagement/emergencyMonitoring/eventOverview/index.vue')),
            meta: {
                authCode: 'emergencyResponse:emergencyMonitoring:eventOverview',
                title: '应急事件概况'
            },
        },
        {
            path: 'simulatyExercise',
            name: 'emergencyResponse:emergencyMonitoring:simulatyExercise',
            component: () => require.ensure([], (require) => require('@/views/safeManage/emergencyManagement/emergencyMonitoring/eventOverview/index2.vue')),
            meta: {
                authCode: 'emergencyResponse:emergencyMonitoring:simulatyExercise',
                title: '模拟演练概况'
            },
        },
        {
            path: 'departmentManage',
            name: 'emergencyResponse:emergencyMonitoring:departmentManage',
            component: () => require.ensure([], (require) => require('@/views/safeManage/emergencyManagement/emergencyMonitoring/departmentManage/index.vue')),
            meta: {
                authCode: 'emergencyResponse:emergencyMonitoring:departmentManage',
                title: '部门管理'
            },
        },
        {
            path: 'departmentManageAdd',
            name: 'emergencyResponse:emergencyMonitoring:departmentManage:add',
            component: () => require.ensure([], (require) => require('@/views/safeManage/emergencyManagement/emergencyMonitoring/departmentManage/add.vue')),
            meta: {
                parentCode: 'emergencyResponse:emergencyMonitoring:departmentManage',
                title: '部门管理 - 新增'
            },
        },
        {
            path: 'departmentManageDetail',
            name: 'emergencyResponse:emergencyMonitoring:departmentManage:detail',
            component: () => require.ensure([], (require) => require('@/views/safeManage/emergencyManagement/emergencyMonitoring/departmentManage/detail.vue')),
            meta: {
                parentCode: 'emergencyResponse:emergencyMonitoring:departmentManage',
                title: '部门管理 - 详情'
            },
        },
        {
            path: 'collaborativeProcess',
            name: 'emergencyResponse:emergencyCommand:collaborativeProcess',
            component: () => require.ensure([], (require) => require('@/views/safeManage/emergencyManagement/emergencyCommand/collaborativeProcess/index.vue')),
            meta: {
                authCode: 'emergencyResponse:emergencyCommand:collaborativeProcess',
                title: '协同会商'
            },
        },
        {
            path: 'collaborativeProcessAdd',
            name: 'emergencyResponse:emergencyCommand:collaborativeProcess:add',
            component: () => require.ensure([], (require) => require('@/views/safeManage/emergencyManagement/emergencyCommand/collaborativeProcess/add.vue')),
            meta: {
                parentCode: 'emergencyResponse:emergencyCommand:collaborativeProcess',
                title: '协同会商 - 新增'
            },
        },
        {
            path: 'collaborativeProcessDetail',
            name: 'emergencyResponse:emergencyCommand:collaborativeProcess:detail',
            component: () => require.ensure([], (require) => require('@/views/safeManage/emergencyManagement/emergencyCommand/collaborativeProcess/detail.vue')),
            meta: {
                parentCode: 'emergencyResponse:emergencyCommand:collaborativeProcess',
                title: '协同会商 - 详情'
            },
        },
        {
            path: 'messagePublish',
            name: 'emergencyResponse:emergencyCommand:messagePublish',
            component: () => require.ensure([], (require) => require('@/views/safeManage/emergencyManagement/emergencyCommand/messagePublish/index.vue')),
            meta: {
                authCode: 'emergencyResponse:emergencyCommand:messagePublish',
                title: '信息发布'
            },
        },
        {
            path: 'messagePublishDetail',
            name: 'emergencyResponse:emergencyCommand:messagePublish:detail',
            component: () => require.ensure([], (require) => require('@/views/safeManage/emergencyManagement/emergencyCommand/messagePublish/detail.vue')),
            meta: {
                parentCode: 'emergencyResponse:emergencyCommand:messagePublish',
                title: '信息发布 - 详情'
            },
        },
        {
            path: 'messagePublishAdd',
            name: 'emergencyResponse:emergencyCommand:messagePublish:add',
            component: () => require.ensure([], (require) => require('@/views/safeManage/emergencyManagement/emergencyCommand/messagePublish/add.vue')),
            meta: {
                parentCode: 'emergencyResponse:emergencyCommand:messagePublish',
                title: '信息发布 - 新增'
            },
        },
    ]
}
