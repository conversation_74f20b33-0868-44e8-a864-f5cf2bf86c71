/**
 * 巡逻
 */
export default {
    path: '/prowl',
    redirect: '/emergencyManagement/resourceManagement/personnel',
    meta: {
        authCode: 'prowl',
        title: '巡检计划',
    },
    children: [
        {
            path: 'plan',
            name: 'prowl:plan',
            component: () =>
                require.ensure([], (require) =>
                    require('@/views/safeManage/prowl/inspectionPlan/index.vue')
                ),
            meta: {
                authCode: 'prowl:plan',
                title: '巡逻计划',
            },
        },
        {
            path: 'prowlPlanAdd',
            name: 'prowl:plan:add',
            component: () =>
                require.ensure([], (require) =>
                    require('@/views/safeManage/prowl/inspectionPlan/add.vue')
                ),
            meta: {
                parentCode: 'prowl:plan',
                title: '巡逻计划 - 新增',
            },
        },
        {
            path: 'prowlPlanDetail',
            name: 'prowl:plan:detail',
            component: () =>
                require.ensure([], (require) =>
                    require('@/views/safeManage/prowl/inspectionPlan/detail.vue')
                ),
            meta: {
                parentCode: 'prowl:plan',
                title: '巡逻计划 - 详情',
            },
        },
        {
            path: 'archive',
            name: 'prowl:archive',
            component: () =>
                require.ensure([], (require) =>
                    require('@/views/safeManage/prowl/archive/index.vue')
                ),
            meta: {
                authCode: 'prowl:archive',
                title: '巡逻归档',
            },
        },
        {
            path: 'archiveDetail',
            name: 'prowl:archive:detail',
            component: () =>
                require.ensure([], (require) =>
                    require('@/views/safeManage/prowl/archive/detail.vue')
                ),
            meta: {
                parentCode: 'prowl:archive',
                title: '巡逻归档 - 详情',
            },
        },
        {
            path: 'implement',
            name: 'prowl:implement',
            component: () =>
                require.ensure([], (require) =>
                    require('@/views/safeManage/prowl/implement/index.vue')
                ),
            meta: {
                authCode: 'prowl:implement',
                title: '巡逻执行',
            },
        },
        {
            path: 'implementDetail',
            name: 'prowl:implement:detail',
            component: () =>
                require.ensure([], (require) =>
                    require('@/views/safeManage/prowl/implement/detail.vue')
                ),
            meta: {
                parentCode: 'prowl:implement',
                title: '巡逻执行 - 详情',
            },
        },
        {
            path: 'videoMonitor',
            name: 'prowl:videoMonitor',
            component: () =>
                require.ensure([], (require) =>
                    require('@/views/safeManage/prowl/videoMonitor/index.vue')
                ),
            meta: {
                authCode: 'prowl:videoMonitor',
                title: '路面视频监测',
            },
        },
        {
            path: 'videoProwl',
            name: 'prowl:videoProwl',
            component: () =>
                require.ensure([], (require) =>
                    require('@/views/safeManage/prowl/videoProwl/index.vue')
                ),
            meta: {
                authCode: 'prowl:videoProwl',
                title: '视频巡逻',
            },
        },
    ],
};
