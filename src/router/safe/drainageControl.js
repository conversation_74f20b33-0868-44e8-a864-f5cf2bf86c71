export default
    {
        path: 'drainageControl',
        name: 'drainageControl',
        redirect: '/drainageControl/equipmentManagement',
        meta: {
            authCode: 'drainageControl',
            title: '排水控制管理'
        },
        children: [
            {
                path: 'pumpStationsOverView',
                name: 'drainageControl:pumpStationsOverView',
                component: () => require.ensure([], (require) => require('@/views/safeManage/drainageControl/pumpStationsOverView')),
                meta: {
                    authCode: 'drainageControl:pumpStationsOverView',
                    title: '泵站概览'
                }
            },
            {
                path: 'equipmentManagement',
                name: 'drainageControl:equipmentManagement',
                component: () => require.ensure([], (require) => require('@/views/safeManage/drainageControl/equipmentManagement')),
                meta: {
                    authCode: 'drainageControl:equipmentManagement',
                    title: '设备管理'
                }
            },
            {
                path: 'equipmentAdd',
                name: 'equipmentAdd',
                component: () => require.ensure([], (require) => require('@/views/safeManage/drainageControl/equipmentManagement/add.vue')),
                meta: {
                    parentCode: 'drainageControl:equipmentManagement',
                    title: '设备管理 - 新增'
                }
            },
            {
                path: 'equipmentDetail',
                name: 'equipmentDetail',
                component: () => require.ensure([], (require) => require('@/views/safeManage/drainageControl/equipmentManagement/detail.vue')),
                meta: {
                    parentCode: 'drainageControl:equipmentManagement',
                    title: '设备管理 - 详情'
                }
            },
            {
                path: 'pumpStationsArchives',
                name: 'drainageControl:pumpStationsArchives',
                component: () => require.ensure([], (require) => require('@/views/safeManage/drainageControl/pumpStationsArchives')),
                meta: {
                    authCode: 'drainageControl:pumpStationsArchives',
                    title: '泵站档案'
                }
            },
            {
                path: 'pumpDetail',
                name: 'pumpDetail',
                component: () => require.ensure([], (require) => require('@/views/safeManage/drainageControl/pumpStationsArchives/detail.vue')),
                meta: {
                    parentCode: 'drainageControl:pumpStationsArchives',
                    title: '泵站档案 - 详情'
                }
            },
            {
                path: 'pumpStationsArchivesAdd',
                name: 'pumpStationsArchivesAdd',
                component: () => require.ensure([], (require) => require('@/views/safeManage/drainageControl/pumpStationsArchives/add.vue')),
                meta: {
                    parentCode: 'drainageControl:pumpStationsArchives',
                    title: '泵站档案 - 新增'
                }
            },
            {
                path: 'pumpStationsMonitoring',
                name: 'drainageControl:pumpStationsMonitoring',
                component: () => require.ensure([], (require) => require('@/views/safeManage/drainageControl/pumpStationsMonitoring')),
                meta: {
                    authCode: 'drainageControl:pumpStationsMonitoring',
                    title: '泵站监测'
                }
            },
            {
                path: 'pumpStationsMonitoringController',
                name: 'drainageControl:pumpStationsMonitoringController',
                component: () => require.ensure([], (require) => require('@/views/safeManage/drainageControl/pumpStationsVideo')),
                meta: {
                    authCode: 'drainageControl:pumpStationsMonitoringController',
                    title: '泵站监控'
                }
            },
            {
                path: 'patrolManagement',
                name: 'drainageControl:patrolManagement',
                component: () => require.ensure([], (require) => require('@/views/safeManage/drainageControl/patrolManagement')),
                meta: {
                    authCode: 'drainageControl:patrolManagement',
                    title: '巡检管理'
                }
            },
            {
                path: 'patrolManagementDetail',
                name: 'patrolManagementDetail',
                component: () => require.ensure([], (require) => require('@/views/safeManage/drainageControl/patrolManagement/detail.vue')),
                meta: {
                    parentCode: 'drainageControl:patrolManagement',
                    title: '巡检管理 - 详情'
                }
            },
            {
                path: 'patrolManagementAdd',
                name: 'patrolManagementAdd',
                component: () => require.ensure([], (require) => require('@/views/safeManage/drainageControl/patrolManagement/add.vue')),
                meta: {
                    parentCode: 'drainageControl:patrolManagement',
                    title: '巡检管理 - 新增'
                }
            },
            {
                path: 'maintenanceManagement',
                name: 'drainageControl:maintenanceManagement',
                component: () => require.ensure([], (require) => require('@/views/safeManage/drainageControl/maintenanceManagement')),
                meta: {
                    authCode: 'drainageControl:maintenanceManagement',
                    title: '检修管理',
                }
            },
            {
                path: 'maintenanceManagementDetail',
                name: 'maintenanceManagementDetail',
                component: () => require.ensure([], (require) => require('@/views/safeManage/drainageControl/maintenanceManagement/detail.vue')),
                meta: {
                    parentCode: 'drainageControl:maintenanceManagement',
                    title: '检修管理 - 详情'
                }
            },
            {
                path: 'maintenanceManagementAdd',
                name: 'maintenanceManagementAdd',
                component: () => require.ensure([], (require) => require('@/views/safeManage/drainageControl/maintenanceManagement/add.vue')),
                meta: {
                    parentCode: 'drainageControl:maintenanceManagement',
                    title: '检修管理 - 新增'
                }
            },
            {
                path: 'alarmManagement',
                name: 'drainageControl:alarmManagement',
                component: () => require.ensure([], (require) => require('@/views/safeManage/drainageControl/alarmManagement')),
                meta: {
                    authCode: 'drainageControl:alarmManagement',
                    title: '告警管理',
                }
            },
            {
                path: 'lakeChannelMonitoring',
                name: 'drainageControl:lakeChannelMonitoring',
                component: () => require.ensure([], (require) => require('@/views/safeManage/drainageControl/lakeChannelMonitoring')),
                meta: {
                    authCode: 'drainageControl:lakeChannelMonitoring',
                    title: '湖渠监测',
                }
            },
            {
                path: 'waterRainMonitoring',
                name: 'drainageControl:waterRainMonitoring',
                component: () => require.ensure([], (require) => require('@/views/safeManage/drainageControl/waterRainMonitoring')),
                meta: {
                    authCode: 'drainageControl:waterRainMonitoring',
                    title: '水雨情监测',
                }
            },
            {
                path: 'statisticAnalysis',
                name: 'drainageControl:statisticAnalysis',
                component: () => require.ensure([], (require) => require('@/views/safeManage/statisticalAnalysis/drainageControlAnalysis')),
                meta: {
                    authCode: 'drainageControl:statisticAnalysis',
                    title: '统计分析',
                }
            },
          {
            path: 'operationStaff',
            name: 'drainageControl:operationStaff',
            component: () => require.ensure([], (require) => require('@/views/safeManage/drainageControl/operationStaff')),
            meta: {
              parentCode: 'drainageControl:operationStaff',
              title: '运营人员',
            }
          },
          {
            path: 'operationStaffAdd',
            name: 'drainageControl:operationStaffAdd',
            component: () => require.ensure([], (require) => require('@/views/safeManage/drainageControl/operationStaff/add.vue')),
            meta: {
              parentCode: 'drainageControl:operationStaff',
              title: '运营人员-新增',
            }
          },
          {
            path: 'operationStaffDetail',
            name: 'drainageControl:operationStaffDetail',
            component: () => require.ensure([], (require) => require('@/views/safeManage/drainageControl/operationStaff/detail.vue')),
            meta: {
              parentCode: 'drainageControl:operationStaff',
              title: '运营人员-查看',
            }
          },
        ]
    }
