import { createRouter, createWebHistory, createWebHashHistory } from 'vue-router';
import common from './common'
import Layout from '@/layout'
const files = require.context('./safe', false, /\.js$/);
const routerList = []
files.keys().forEach(url => {
    const routerItem = require('./safe' + url.replace('.', ''))
    routerList.push(routerItem)
});
export const constantRoutes = [
    {
        path: '/',
        component: Layout,
        name: 'home',
        showSingle: true,
        meta: {
            icon: 'home',
            title: '首页'
        },
        children: [
            {
                path: '/',
                component: () => import('@/views/error-page/404'),
            },
            {
                path: '/mainHomePage',
                component: () => require.ensure([], (require) => require('@/views/safeManage/index/index')),
                name: 'mainHomePage',
                meta: {
                    icon: 'home',
                    title: '首页'
                }
            },
            // 操作日志
            {
                path: 'operationLog',
                name: 'businessLog',
                component: () => require.ensure([], (require) => require('@/views/common/operationLog/index')),
                meta: {
                    authCode: 'businessLog',
                    title: '操作日志'
                },
            },
            // 智慧消防栓
            {
                path: 'wisdomFireHydrant',
                name: 'wisdomFireHydrant',
                redirect: '/wisdomFireHydrant/fireHydrantMap',
                meta: {
                    authCode: 'wisdomFireHydrant',
                    title: '智慧消防栓'
                },
                children: [
                    {
                        path: 'fireHydrantMap',
                        name: 'smartFireHydrant:fireHydrantMap',
                        component: () => require.ensure([], (require) => require('@/views/safeManage/wisdomFireHydrant/fireHydrantMap/index.vue')),
                        // component: () => import('@/views/safeManage/wisdomFireHydrant/fireHydrantMap/index.vue'),
                        meta: {
                            authCode: 'smartFireHydrant:fireHydrantMap',
                            title: '消防栓地图'
                        }
                    },
                    {
                        path: 'fireHydrantManage',
                        name: 'smartFireHydrant:fireHydrantManage',
                        component: () => require.ensure([], (require) => require('@/views/safeManage/wisdomFireHydrant/fireHydrantManage/index.vue')),
                        // component: () => import('@/views/safeManage/wisdomFireHydrant/fireHydrantManage/index.vue'),
                        meta: {
                            authCode: 'smartFireHydrant:fireHydrantManage',
                            title: '消防栓管理'
                        }
                    },
                    {
                        path: 'fireHydrantAdd',
                        name: 'fireHydrantAdd',
                        component: () => require.ensure([], (require) => require('@/views/safeManage/wisdomFireHydrant/fireHydrantManage/add.vue')),
                        // component: () => import('@/views/safeManage/wisdomFireHydrant/fireHydrantManage/add.vue'),
                        meta: {
                            parentCode: 'smartFireHydrant:fireHydrantManage',
                            title: '消防栓详情 - 新增'
                        }
                    },
                    {
                        path: 'fireHydrantDetail',
                        name: 'fireHydrantDetail',
                        component: () => require.ensure([], (require) => require('@/views/safeManage/wisdomFireHydrant/fireHydrantManage/detail.vue')),
                        // component: () => import('@/views/safeManage/wisdomFireHydrant/fireHydrantManage/detail.vue'),
                        meta: {
                            parentCode: 'smartFireHydrant:fireHydrantManage',
                            title: '消防栓详情 - 详情'
                        }
                    },
                    // 消防栓告警
                    {
                        path: 'fireHydrantAlarm',
                        name: 'smartFireHydrant:fireHydrantAlarm',
                        component: () => require.ensure([], (require) => require('@/views/safeManage/wisdomFireHydrant/fireHydrantAlert')),
                        // component: () => import('@/views/safeManage/wisdomFireHydrant/fireHydrantAlert'),
                        meta: {
                            authCode: 'smartFireHydrant:fireHydrantAlarm',
                            title: '消防栓告警'
                        }
                    }
                ]
            },
            // 智慧井盖
            {
                path: 'wisdomManholeCover',
                name: 'wisdomManholeCover',
                redirect: '/wisdomManholeCover/manholeCoverMap',
                meta: {
                    authCode: 'wisdomManholeCover',
                    title: '智慧井盖'
                },
                children: [
                    {
                        path: 'manholeCoverMap',
                        name: 'smartManholeCover:manholeCoverMap',
                        component: () => require.ensure([], (require) => require('@/views/safeManage/wisdomManholeCover/manholeCoverMap/index')),
                        // component: () => import('@/views/safeManage/wisdomManholeCover/manholeCoverMap/index'),
                        meta: {
                            authCode: 'smartManholeCover:manholeCoverMap',
                            title: '井盖地图'
                        }
                    },
                    {
                        path: 'manholeCoverManage',
                        name: 'smartManholeCover:manholeCoverManage',
                        component: () => require.ensure([], (require) => require('@/views/safeManage/wisdomManholeCover/manholeCoverManage/index')),
                        // component: () => import('@/views/safeManage/wisdomManholeCover/manholeCoverManage/index'),
                        meta: {
                            authCode: 'smartManholeCover:manholeCoverManage',
                            title: '井盖设备管理'
                        }
                    },
                    {
                        path: 'manholeCoverAdd',
                        name: 'manholeCoverAdd',
                        component: () => require.ensure([], (require) => require('@/views/safeManage/wisdomManholeCover/manholeCoverManage/add')),
                        // component: () => import('@/views/safeManage/wisdomManholeCover/manholeCoverManage/add'),
                        meta: {
                            parentCode: 'smartManholeCover:manholeCoverManage',
                            title: '井盖详情 - 新增'
                        }
                    },
                    {
                        path: 'manholeCoverDetail',
                        name: 'manholeCoverDetail',
                        component: () => require.ensure([], (require) => require('@/views/safeManage/wisdomManholeCover/manholeCoverManage/detail')),
                        // component: () => import('@/views/safeManage/wisdomManholeCover/manholeCoverManage/detail'),
                        meta: {
                            parentCode: 'smartManholeCover:manholeCoverManage',
                            title: '井盖详情 - 详情'
                        }
                    },
                    // 井盖告警
                    {
                        path: 'manholeAlarm',
                        name: 'smartManholeCover:manholeAlarm',
                        component: () => require.ensure([], (require) => require('@/views/safeManage/wisdomManholeCover/manholeAlert')),
                        // component: () => import('@/views/safeManage/wisdomManholeCover/manholeAlert'),
                        meta: {
                            authCode: 'smartManholeCover:manholeAlarm',
                            title: '井盖告警'
                        }
                    },
                    {
                        path: 'partsManagement',
                        name: 'smartManholeCover:partsManagement',
                        component: () => require.ensure([], (require) => require('@/views/safeManage/wisdomManholeCover/partsManagement/index')),
                        meta: {
                            authCode: 'smartManholeCover:partsManagement',
                            title: '井盖部件管理'
                        }
                    },
                    {
                        path: 'partsManagementDetail',
                        name: 'smartManholeCover:partsManagementDetail',
                        component: () => require.ensure([], (require) => require('@/views/safeManage/wisdomManholeCover/partsManagement/detail')),
                        meta: {
                            parentCode: 'smartManholeCover:partsManagement',
                            title: '井盖部件管理 - 详情'
                        }
                    },
                ]
            },
        ]
    },
    ...common
];
routerList.forEach(i => {
    constantRoutes[0].children.push(i.default)
})
const router = createRouter({
    history: createWebHashHistory(),
    // history: createMemoryHistory(process.env.BASE_URL),
    routes: constantRoutes
});

// 重定向到404
export const asyncRoutes = [
    { path: '*', redirect: '/404', hidden: true }
]


export default router;
