import { createRouter, createWebHistory, createWebHashHistory } from 'vue-router';
import common from './common'
import Layout from '@/layout'
const files = require.context('./livable', false, /\.js$/);
let routerList = []
files.keys().forEach(url => {
    const routerItem = require('./livable' + url.replace('.', ''))
    if (routerItem.default instanceof Array) {

        routerList = [...routerList, ...routerItem.default]
    } else {
        routerList.push(routerItem.default)
    }
});

export const constantRoutes = [
    {
        path: '/',
        component: Layout,
        name: 'home',
        showSingle: true,
        meta: {
            icon: 'home',
            title: '首页'
        },
        children: [
            {
                path: '/',
                component: () => import('@/views/error-page/404'),
            },
            {
                path: '/mainHomePage',
                component: () => require.ensure([], (require) => require('@/views/livableManage/index/index')),
                name: 'mainHomePage',
                meta: {
                    icon: 'home',
                    title: '首页'
                }
            },
            // 操作日志
            {
                path: 'operationLog',
                name: 'businessLog',
                component: () => require.ensure([], (require) => require('@/views/common/operationLog/index')),
                meta: {
                    authCode: 'businessLog',
                    title: '操作日志'
                },
            },
            // 绿地管理
            {
                path: '/greenlandManagement',
                redirect: '/greenlandManagement',
                meta: {
                    authCode: 'greenLandManagement',
                    title: '绿地管理'
                },
                children: [
                    {
                        path: 'greenlandMap',
                        component: () => require.ensure([], (require) => require('@/views/livableManage/greenlandManagement/greenlandMap/index')),
                        // component: () => import('@/views/livableManage/greenlandManagement/greenlandMap/index'),
                        name: 'greenLand:greenLandManagementOverView',
                        meta: {
                            authCode: 'greenLand:greenLandManagementOverView',
                            title: '绿地地图'
                        }
                    },
                    // 绿地管理
                    {
                        path: 'greenlandManagement',
                        name: 'greenLand:greenLandManagement',
                        component: () => require.ensure([], (require) => require('@/views/livableManage/greenlandManagement/greenlandManagement')),
                        // component: () => import('@/views/livableManage/greenlandManagement/greenlandManagement'),
                        meta: {
                            authCode: 'greenLand:greenLandManagement',
                            title: '绿地管理'
                        }
                    },
                    {
                        path: 'greenlandManagementAdd',
                        name: 'greenLand:greenlandManagementAdd',
                        component: () => require.ensure([], (require) => require('@/views/livableManage/greenlandManagement/greenlandManagement/newAdd')),
                        // component: () => import('@/views/livableManage/greenlandManagement/greenlandManagement/newAdd'),
                        meta: {
                            parentCode: 'greenLand:greenLandManagement',
                            title: '绿地管理-新增'
                        }
                    },
                    {
                        path: 'greenlandManagementDetail',
                        name: 'greenLand:greenlandManagementDetail',
                        component: () => require.ensure([], (require) => require('@/views/livableManage/greenlandManagement/greenlandManagement/detail')),
                        // component: () => import('@/views/livableManage/greenlandManagement/greenlandManagement/detail'),
                        meta: {
                            parentCode: 'greenLand:greenLandManagement',
                            title: '绿地管理-详情'
                        }
                    },
                    {
                        path: 'greenLandTypeManagement',
                        name: 'greenLand:greenLandTypeManagement',
                        component: () => require.ensure([], (require) => require('@/views/livableManage/greenlandManagement/greenlandManagementType')),
                        // component: () => import('@/views/livableManage/greenlandManagement/greenlandManagementType'),
                        meta: {
                            authCode: 'greenLand:greenLandTypeManagement',
                            title: '绿地类型管理'
                        }
                    },
                    // 统计分析
                    {
                        path: 'statisticalAnalysis',
                        name: 'greenLand:greenlandStatisticalAnalysis',
                        component: () => require.ensure([], (require) => require('@/views/livableManage/greenlandManagement/statisticalAnalysis')),
                        meta: {
                            authCode: 'greenLand:greenlandStatisticalAnalysis',
                            title: '统计分析'
                        }
                    },
                ]
            },
            // 电子围栏
            {
                path: '/electronicFenceManagement',
                redirect: '/electronicFenceManagement/electronicFence',
                meta: {
                    authCode: 'electronicFence',
                    title: '电子围栏'
                },
                children: [
                    {
                        path: 'electronicFence',
                        name: 'electronicFence:carVehicle',
                        component: () => require.ensure([], (require) => require('@/views/livableManage/electronicFenceManagement/baseInfoManagement/electronicFence')),
                        // component: () => import('@/views/livableManage/electronicFenceManagement/baseInfoManagement/electronicFence'),
                        meta: {
                            authCode: 'electronicFence:carVehicle',
                            title: '车辆轨迹查看'
                        }
                    },
                    {
                        path: 'electronicFenceMap',
                        name: 'electronicFence:ef_management',
                        component: () => require.ensure([], (require) => require('@/views/livableManage/electronicFenceManagement/electronicFenceMap')),
                        // component: () => import('@/views/livableManage/electronicFenceManagement/baseInfoManagement/electronicFence'),
                        meta: {
                            authCode: 'electronicFence:ef_management',
                            title: '电子围栏管理'
                        }
                    },
                    {
                        path: 'locationDeviceManagement',
                        name: 'electronicFence:positioningDevice',
                        component: () => require.ensure([], (require) => require('@/views/livableManage/electronicFenceManagement/baseInfoManagement/locationDeviceManagement/index')),
                        // component: () => import('@/views/livableManage/electronicFenceManagement/baseInfoManagement/locationDeviceManagement/index'),
                        meta: {
                            authCode: 'electronicFence:positioningDevice',
                            title: '定位设备管理'
                        }
                    },
                    {
                        path: 'locationDeviceManagementDetail',
                        name: 'locationDeviceManagementDetail',
                        component: () => require.ensure([], (require) => require('@/views/livableManage/electronicFenceManagement/baseInfoManagement/locationDeviceManagement/detail')),
                        // component: () => import('@/views/livableManage/electronicFenceManagement/baseInfoManagement/locationDeviceManagement/detail'),
                        meta: {
                            parentCode: 'electronicFence:positioningDevice',
                            title: '定位设备管理-详情'
                        }
                    },
                    {
                        path: 'alarmManagement',
                        name: 'ef_alarm',
                        component: () => require.ensure([], (require) => require('@/views/livableManage/electronicFenceManagement/alarmManagement/index')),
                        // component: () => import('@/views/livableManage/electronicFenceManagement/alarmManagement/index'),
                        meta: {
                            authCode: 'ef_alarm',
                            title: '告警管理'
                        }
                    },
                    {
                        path: 'vehicleManagement',
                        name: 'electronicFence:car',
                        component: () => require.ensure([], (require) => require('@/views/livableManage/electronicFenceManagement/baseInfoManagement/vehicleManagement/index')),
                        // component: () => import('@/views/livableManage/electronicFenceManagement/baseInfoManagement/vehicleManagement/index'),
                        meta: {
                            authCode: 'electronicFence:car',
                            title: '车辆管理'
                        }
                    },
                    {
                        path: 'vehicleManagementDetail',
                        component: () => require.ensure([], (require) => require('@/views/livableManage/electronicFenceManagement/baseInfoManagement/vehicleManagement/detail.vue')),
                        // component: () => import('@/views/livableManage/electronicFenceManagement/baseInfoManagement/vehicleManagement/detail.vue'),
                        name: 'vehicleManagementDetail',
                        meta: {
                            parentCode: 'electronicFence:car',
                            title: '车辆管理-详情'
                        }
                    },
                    {
                        path: 'addVehicle',
                        component: () => require.ensure([], (require) => require('@/views/livableManage/electronicFenceManagement/baseInfoManagement/vehicleManagement/add.vue')),
                        // component: () => import('@/views/livableManage/electronicFenceManagement/baseInfoManagement/vehicleManagement/add.vue'),
                        name: 'addVehicle',
                        meta: {
                            parentCode: 'electronicFence:car',
                            title: '车辆管理-新增'
                        }
                    },
                    // 人员管理
                    {
                        path: 'peopleManagement',
                        name: 'electronicFence:peopleManagement',
                        component: () => require.ensure([], (require) => require('@/views/livableManage/electronicFenceManagement/baseInfoManagement/peopleManagement/index')),
                        meta: {
                            authCode: 'electronicFence:peopleManagement',
                            title: '人员管理'
                        }
                    },
                    {
                        path: 'peopleManagementDetail',
                        component: () => require.ensure([], (require) => require('@/views/livableManage/electronicFenceManagement/baseInfoManagement/peopleManagement/detail.vue')),
                        name: 'electronicFence:car:detail',
                        meta: {
                            parentCode: 'electronicFence:car',
                            title: '人员管理-详情'
                        }
                    },
                    {
                        path: 'peoplePositionDeviceManagement',
                        name: 'electronicFence:peoplePositionDeviceManagement',
                        component: () => require.ensure([], (require) => require('@/views/livableManage/electronicFenceManagement/baseInfoManagement/peoplePositionDeviceManagement/index')),
                        meta: {
                            authCode: 'electronicFence:peoplePositionDeviceManagement',
                            title: '人员工牌设备管理'
                        }
                    },
                    {
                        path: 'peoplePositionDeviceManagementDetail',
                        name: 'electronicFence:peoplePositionDeviceManagement:detail',
                        component: () => require.ensure([], (require) => require('@/views/livableManage/electronicFenceManagement/baseInfoManagement/peoplePositionDeviceManagement/detail')),
                        meta: {
                            parentCode: 'electronicFence:peoplePositionDeviceManagement',
                            title: '人员工牌设备管理-详情'
                        }
                    },
                    {
                        path: 'peopleTrack',
                        name: 'electronicFence:peopleTrack',
                        component: () => require.ensure([], (require) => require('@/views/livableManage/electronicFenceManagement/baseInfoManagement/peopleTrack')),
                        // component: () => import('@/views/livableManage/electronicFenceManagement/baseInfoManagement/electronicFence'),
                        meta: {
                            authCode: 'electronicFence:peopleTrack',
                            title: '人员轨迹查看'
                        }
                    },
                ]
            },

        ]
    },
    ...common
];
routerList.forEach(i => {
    constantRoutes[0].children.push(i)
})
const router = createRouter({
    history: createWebHashHistory(),
    // history: createMemoryHistory(process.env.BASE_URL),
    routes: constantRoutes
});

// 重定向到404
export const asyncRoutes = [
    { path: '*', redirect: '/404', hidden: true }
]


export default router;
