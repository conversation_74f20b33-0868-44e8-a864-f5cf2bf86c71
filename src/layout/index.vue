<template>
    <Layout class="app-layout">
        <Header class="layout-header">
            <div class="header-left">
                <Logo />
            </div>
            <user />
        </Header>

        <Layout>
            <Sider ref="sider" class="layout-sider" breakpoint="xl" hide-trigger v-model="isCollapsed" collapsible
                :collapsed-width="48">
                <SiderMain :is-collapsed="isCollapsed" />
                <div class="coll-fixed">
                    <settingMenu v-show="!isCollapsed" />
                    <div class="coll-box" :class="[isCollapsed ? 'rotate' : '']" @click="toggleCollapse">
                        <Icon custom="iconfont icon-menu-fold" />
                    </div>
                </div>
            </Sider>
            <Content class="layout-content">
                <div class="content-main">
                    <app-main />
                    <Modal v-model="voiceCallModal" :title="meetingInfo.topicName" footer-hide :width="900" :mask-closable="false">
                        <component :is="isVoiceComName" @handleClose="voiceCallModal = false" ref="voiceCallRef"
                            :meeting-info="meetingInfo" />
                    </Modal>
                </div>
            </Content>
        </Layout>
    </Layout>
</template>

<script>
import { SiderMain, AppMain, User, settingMenu } from './components'
import voiceCallCom from './components/voiceCallCom'
import voiceAnserCom from './components/voiceAnserCom'
import Logo from './components/SiderMain/Logo';
import store from '@/store';
import { mapGetters } from 'vuex';
export default {
    name: 'Index',
    components: {
        Logo,
        SiderMain,
        AppMain,
        User,
        settingMenu,
        voiceCallCom,
        voiceAnserCom
    },
    data() {
        return {
            isCollapsed: false,
            voiceCallModal: false,
            isVoiceComName: ''
        }
    },
    computed: {
        ...mapGetters(['meetingStatus', 'meetingInfo', 'voiceComName', 'loginUser'])
    },
    watch: {
        voiceComName(val) {
            this.isVoiceComName = val
        },
        meetingInfo(val) {
            console.log(val)
        },
        meetingStatus(val) {
            this.voiceCallModal = val;
        },
        isCollapsed(newVal, oldVal) {
            store.dispatch('app/updateIsCollapsed', newVal)
        },
        voiceCallModal(newVal) {
            console.log(newVal)
            if (!newVal) {
                this.$refs.voiceCallRef.cancelVideoCall()
                store.dispatch('user/updateMeeting', false)
                store.dispatch('user/updateVoiceComName', '')
            }
        }
    },
    created() {
    },
    mounted() {
        // console.log(this.$socket.send)
    },
    methods: {
        setCurrentRoute(route) {
            store.dispatch('permission/setCurrentRoute', route)
        },
        toggleCollapse() {
            this.isCollapsed = !this.isCollapsed
        }
    }
}
</script>

<style lang="less" scoped>
.app-layout {
    min-width: 844px;

    /deep/ .ivu-layout-header {
        padding: 0 24px 0 0px;
    }
}

.ivu-layout.ivu-layout-has-sider {
    flex: 1;
    height: auto;
    overflow: hidden;
}

.ivu-layout-sider {
    min-width: 48px !important;
    z-index: 200;
}

.coll-fixed {
    position: absolute;
    width: 100%;
    background: #fff;
    bottom: 0;
    right: 0;
    z-index: 10;
    padding: 12px;
    display: flex;
    justify-content: space-between;

    .coll-box {

        width: 24px;
        height: 24px;
        display: flex;
        cursor: pointer;
        background: #F8FAFB;
        border-radius: 2px;

        .ivu-icon {
            margin: auto;
            color: @text-color;
        }

        &.rotate {
            transform: rotate(180deg);
        }
    }
}

.layout-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 48px;
    background: #fff url("../assets/layout/layout_bg.png") center no-repeat;
    background-size: 100%;
    filter: drop-shadow(0px 4px 7px rgba(216, 230, 244, 0.41)) drop-shadow(0px 2px 4px #F0F0F0);

    .header-left {
        display: flex;
        align-items: center;

        .menu-icon {
            transition: all .3s;
        }

        .rotate-icon {
            transform: rotate(-90deg);
        }
    }
}

.layout-content {
    //padding: 16px;
    overflow: hidden;
    position: relative;
    z-index: 200;

    .content-main {
        width: 100%;
        height: 100%;
        padding: 8px 0px 0px;
        overflow: hidden;
    }

}
</style>
