<template>
    <Form ref="formRef" :model="formValidate" :rules="ruleValidate" :label-width="80">
        <!-- <FormItem label="旧密码" prop="passwordAuthentication">
            <Input v-model="formValidate.passwordAuthentication" placeholder="请输入"></Input>
        </FormItem> -->
        <FormItem label="新密码" prop="password">
            <Input v-model="formValidate.password" password placeholder="请输入" type="password"></Input>
        </FormItem>
        <FormItem label="确认密码" prop="passwordConfirm">
            <Input v-model="formValidate.passwordConfirm" password placeholder="请输入" type="password"></Input>
        </FormItem>
    </Form>
</template>
<script lang="ts">
import { defineComponent, ref, computed } from 'vue';
import { loginService } from '@/api/loginService';
import { Message } from 'view-ui-plus';
import { useRouter } from 'vue-router';
import store from '@/store';
import { mapGetters } from 'vuex';
export default defineComponent({
    setup() {
        const router = useRouter()
        const handleConfirm = async () => {
            const validated = await formRef.value.validate();
            if (validated) {
                if (formValidate.value.password != formValidate.value.passwordConfirm) {
                    Message.error('请保持两次输入密码一致')
                    return
                }
                const res = await loginService.password({ model: { ...formValidate.value, id: loginUser.value.id } })
                const { success }: { success: boolean } = res as unknown as HttpResponse<any>;
                if (success) {
                    Message.success('密码修改成功，请重新登录')
                    setTimeout(() => {
                        store.dispatch('user/logout').then(() => {
                            router.push('/login');
                        });
                    }, 1500)
                }
            }

            // return true
        }
        const loginUser = computed(
            mapGetters(['loginUser']).loginUser.bind({ $store: store })
        );
        const formRef = ref()
        const formValidate = ref({
            id: '',
            password: '',
            passwordConfirm: '',
            passwordAuthentication: ''
        })
        const ruleValidate = ref({
            password: [
                { required: true, message: '请输入必填项', trigger: 'change' },
                { pattern: /^(?=.*[A-Za-z])(?=.*\d)(?=.*[`~!@#$%^&*()_+<>?:"{},.\/\\;'[\]])[A-Za-z\d`~!@#$%^&*()_+<>?:"{},.\/\\;'[\]]{6,20}$/, trigger: 'change', message: '请输入数字+字母+特殊字符，长度6-20位的密码' }
            ],
            passwordConfirm: [
                { required: true, message: '请输入必填项', trigger: 'change' },
                { pattern: /^(?=.*[A-Za-z])(?=.*\d)(?=.*[`~!@#$%^&*()_+<>?:"{},.\/\\;'[\]])[A-Za-z\d`~!@#$%^&*()_+<>?:"{},.\/\\;'[\]]{6,20}$/, trigger: 'change', message: '请输入数字+字母+特殊字符，长度6-20位的密码' }
            ],
            // passwordAuthentication: [
            //     { required: true, message: '请输入必填项', trigger: 'change' }
            // ],
        })
        return {
            handleConfirm,
            formValidate,
            ruleValidate,
            formRef
        }
    }
})
</script>
<style></style>
