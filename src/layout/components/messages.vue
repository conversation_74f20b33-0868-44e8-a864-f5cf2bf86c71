<script lang="ts" setup>
import { defineComponent, reactive, defineEmits, toRefs, onMounted, ref, computed } from "vue";
import { useRouter } from "vue-router";
import { commonService } from "@/api/commonService";
import { Dictionary ,ModelSelect} from "@/views/common/messages/type";
import { bus } from "@/utils/tool";
import Util from "@/utils";
const emit: any = defineEmits();
const router = useRouter();
interface Data {
    messageList: any[];
    unReadCount: number;
    model: Dictionary[] | any[];
}
const Data = reactive<Data>({
    messageList: [],
    model: [],
    unReadCount: 0,
});

const { model, messageList, unReadCount } = toRefs(Data);
const goMessagePage = (id: number | null) => {
    if (id) {
        router.push({
            name: "messages:detail",
            query: {
                id,
                origin: "unread",
            },
        });
    } else {
        router.push({
            name: "messages",
        });
    }
};
// 查询模块下拉
// const getModelSelect = async () => {
//     const path: string = window.window.document.location.href;
//     let data:ModelSelect = {
//         parentCode: "", //08园区畅行   09生态宜居 10生态宜居
//     };
//     if (path.indexOf("/traffic") !== -1) {
//         data.parentCode =  "08"
//     } else if (path.indexOf("/safe") !== -1) {
//         data.parentCode =  "10"
//     } else {
//         data.parentCode =  "09"

//     }
//     commonService.queryModelSelect(data).then((res:any) => {
//         if(res.success){
//             model.value = res.data;
//         }
//     })
// };
// getModelSelect()
// async function getDictionary() {
//     let res: any = await dictionary.getDictionarys({
//         page: { size: -1, current: 1 },
//         customQueryParams: {
//             categoryCode: "application_model",
//         },
//     });
//     console.log(res.data.records);
//     if (res.success) {
//         model.value = res.data.records;
//     }
// }
// getDictionary();
// 获取最新五条消息
const getMessage = () => {
    commonService.getMessageList().then((res: any) => {
        if (res.success) {
            messageList.value = res.data.records;
        }
    });
};
getMessage();
// 处理时间
function formatDate(date: string) {
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);
    const inputDate = new Date(date);
    if (inputDate.toDateString() === today.toDateString()) {
        return "今天" + date.split(" ")[1];
    } else if (inputDate.toDateString() === yesterday.toDateString()) {
        return "昨天" + date.split(" ")[1];
    } else {
        return date;
    }
}
// 一键已读
const handleAllRead = () => {
    commonService.oneReadMessage().then((res: any) => {
        if (res.success) {
            getMessage();
            getUnreadMessagesNumber();
            bus.emit("allRead");
        }
    });
};
// 获取未读消息条数
const getUnreadMessagesNumber = () => {
    commonService.getUnreadMessages().then((res: any) => {
        if (res.success) {
            unReadCount.value = res.data;
            let unReadCountTitle: number | string = Util.objClone(res.data);
            if (res.data > 99) {
                unReadCountTitle = res.data + "+";
            }
            emit("update:modelValue", unReadCountTitle);
        }
    });
};
getUnreadMessagesNumber();
onMounted(() => {
    bus.on("read", () => {
        getUnreadMessagesNumber();
        getMessage();
    });
});
const width = (content: string) => {
    let span = document.createElement("span");
    span.style.display = "inline-block";
    span.style.fontSize = "14px";
    span.textContent = content;
    document.body.appendChild(span);
    let contentWidth = span.clientWidth;
    document.body.removeChild(span);
    return (396 - 64 - contentWidth) + 'px';
};
</script>
<template>
    <div class="message-container">
        <div class="header-box">
            <div class="header">
                <span>消息({{ unReadCount }})</span><span @click="handleAllRead">全部已读</span>
            </div>
        </div>
        <div class="content">
            <ul v-if="messageList.length">
                <li v-for="i in messageList" :key="i" @click="goMessagePage(i.id)">
                    <div class="top">
                        <span
                            :style="{width:width(i.modelName)}"
                            >{{ i.title }}</span
                        >
                        <span>{{
                            i.modelName
                        }}</span>
                    </div>
                    <div class="middle">
                        {{ i.content }}
                    </div>
                    <div class="bottom">{{ formatDate(i.recordTime) }}</div>
                </li>
            </ul>
            <div v-else class="no-data">
                <div class="no-data-img">
                    <img src="@/assets/images/no_data.png" alt="" />
                </div>
                <span>暂无内容</span>
            </div>
        </div>
        <div class="footer" @click="goMessagePage(null)"><span>查看更多</span></div>
    </div>
</template>

<style lang="less" scoped>
.message-container {
    width: 396px;
    min-height: 300px;
    // height: 530px;
    display: flex;
    flex-direction: column;
    z-index: 10000;
    .header-box {
        border-bottom: 1px solid #e0e6f1;
        .header {
            height: 40px;
            display: flex;
            padding: 0 12px;
            justify-content: space-between;
            background-color: #fff;
            align-items: center;
            z-index: 100;
            span:first-child {
                color: #4e5969;
                cursor: pointer;
            }
            span:last-child {
                color: @primary-color;
                cursor: pointer;
            }
        }
    }
    .content {
        flex: 1;
        position: relative;

        ul {
            display: flex;
            flex-direction: column;
            cursor: pointer;

            li {
                border-bottom: 1px solid #e0e6f1;
                display: flex;
                flex-direction: column;
                padding: 18px 20px 13px;
                row-gap: 5px;
                &:hover {
                    background: #f3f7fb;
                }
                .top {
                    font-size: 14px;
                    display: flex;
                    justify-content: space-between;
                    span:first-child {
                        font-family: 'PingFang SC';
                        color: #1e2a55;
                        font-weight: 500;
                        overflow: hidden;
                        white-space: nowrap;
                        word-wrap: break-word;
                        word-break: break-all;
                        text-overflow: ellipsis;
                    }
                    span:last-child {
                        color: #3491fa;
                        background: #e8f7ff;
                        height: 20px;
                        text-align: center;
                        line-height: 20px;
                        padding: 0 8px;
                        border-radius: 2px;
                    }
                }
                .middle {
                    width: 360px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    word-wrap: break-word;
                    word-break: break-all;
                }
                .bottom {
                    color: #798799;
                }
            }
        }
        .no-data {
            display: flex;
            flex-direction: column;
            row-gap: 20px;
            position: absolute;
            left: 50%;
            top: 50%;
            transform: translate3d(-50%, -50%, 0);

            align-items: center;
            .no-data-img {
                width: 80px;
                height: 80px;
                img {
                    width: 100%;
                    height: 100%;
                }
                span {
                    color: #86909c;
                }
            }
        }
    }
    .footer {
        border-top: 1px solid #e0e6f1;
        height: 40px;
        padding: 0 8px;
        text-align: center;
        line-height: 40px;
        background-color: #fff;
        color: @primary-color;
        span {
            cursor: pointer;
        }
    }
}
</style>
