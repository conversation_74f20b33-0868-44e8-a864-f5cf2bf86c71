<template>
    <div class="tabs-head">
        <Tabs type="card" closable v-model="tabsKey">
            <TabPane :name="n" :label="'ssss' + n" v-for="n in 10"></TabPane>
        </Tabs>
    </div>
</template>

<script>
export default {
    name: 'TabsHead',
    data() {
        return {
            tabsKey: 1
        }
    },
    methods: {}
}
</script>

<style lang="less" scoped>

.tabs-head{
    position: absolute;
    top: 19px;
    left: 24px;
    right: 24px;
    overflow: hidden;
}
</style>
