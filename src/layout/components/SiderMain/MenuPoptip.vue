<template>
    <MenuItemCustom v-if="type == 2 && !menu.children" :menu="menu" :active-name="activeName" />
    <Poptip
        v-else
        trigger="hover"
        placement="right"
        :transfer="transfer"
        padding="0"
        popper-class="menu-popper"
        @on-popper-show="showPoper"
        @on-popper-hide="hidePoper"
    >
        <div class="icon-title" v-if="type == 1" :class="[hasActiveMenuClass(menu), showPopFlag ? 'showPop' : '']">
            <span class="icon iconfont" :class="[menu.iconName]"></span>
            <!-- <Icon size="18" :custom="`iconfont ${menuIconEnums[menu.code] || 'icon-apps'}`" /> -->
        </div>
        <div
            class="sec-title"
            :style="!isCollapsed ? 'padding-left: 56px;' : ''"
            v-else-if="type == 2"
            :class="[hasActiveMenuClass(menu), showPopFlag ? 'showPop' : '']">
            {{ menu.name }}
            <Icon type="ios-arrow-forward" v-if="menu.children && menu.children.length > 0" class="submenu-title-icon" />
        </div>
        <template #content>
            <div v-if="menu.children && menu.children.length > 0">
                <menu-poptip v-for="sec in menu.children" :menu="sec" type="2" :active-name="activeName" :open-names="openNames" :is-collapsed="true" :transfer="false" />
            </div>
            <MenuItemCustom v-else :menu="menu" :active-name="activeName" />
        </template>
    </Poptip>
</template>

<script>
import MenuItemCustom from './MenuItemCustom'
import menuIconEnums from '@/config/menuIcon.ts'
export default {
    name: 'MenuPoptip',
    components: {
        MenuItemCustom
    },
    props: {
        menu: { default() { return {} } },
        transfer: { default: true },
        activeName: { default: '' },
        openNames: { default: () => [] },
        type: { default: '1' }, // 1：只显示图标   2：显示名称和箭头
        isCollapsed: { default: false }
    },
    data() {
        return {
            showPopFlag: false,
            menuIconEnums
        };
    },
    methods: {
        // 判断该菜单下是否有被选中的
        hasActiveMenuClass(menu) {
            if (this.openNames.indexOf(menu.code) >= 0) {
                return 'on'
            }
        },
        showPoper() {
            this.showPopFlag = true
        },
        hidePoper() {
            this.showPopFlag = false
        }
    }
};
</script>

<style lang="less" scoped>

</style>
