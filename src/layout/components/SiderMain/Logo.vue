<template>
    <div class="logo" @click.native="changeSys">
        <img :src="require('@/assets/logo/logo_' + $store.getters.pageName + '.png')" alt="">
    </div>
</template>

<script>
import setting from '@/settings'
export default {
    name: '<PERSON>go',
    data() {
        return {
            logo: setting.logo.blue
        }
    },
    methods: {
        changeSys() {
            let url = `${location.protocol}//${location.hostname}:16007`;
            //兼容宜昌环境三智跳转，如果是宜昌环境，端口要改成**********:15673
            if (location.hostname.startsWith('**********')) {
                url = `${location.protocol}//${location.hostname}:15673`;
            }
            if (process.env.VUE_APP_LOGIN_URL) {
                url = process.env.VUE_APP_LOGIN_URL;
            }
            location.href = url + "/#/login/changeSysPage";
        },
    }
}
</script>

<style lang="less" scoped>

.logo{
    width: 200px;
    height: @layout-header-height;
    display: flex;
    justify-content: center;
    align-items: center;
    img{
        width: 120px;
    }
}
.logo:hover{
    background-color:  rgba(124, 156, 215, 0.5);
    cursor: pointer;
}
</style>
