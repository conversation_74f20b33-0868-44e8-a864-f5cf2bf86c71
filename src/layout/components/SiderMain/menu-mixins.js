import { mapGetters } from 'vuex';
export const MenuListMix = {
    data() {
        return {
            menuList: [],
            openNames: []
        }
    },
    computed: {
        ...mapGetters(['permission_routes', 'openIframeObj']),
        activeName() {
            const route = this.$route;
            let name = route.name
            if (route.meta.parentCode) {
                name = route.meta.parentCode
            }

            if (name) {
                if (name === 'openIframe') {
                    name = this.openIframeObj.code
                }
                this.getOpenNames(name)
            }
            return name || '';
        }
    },
    methods: {

        // 判断该菜单下是否有被选中的
        hasActiveMenuClass(menu) {
            // console.log(menu);
            if (this.openNames.indexOf(menu.code) >= 0) {
                return 'on'
            }
        },
        getOpenNames(activeName) {
            this.openNames = []
            this.permission_routes.forEach(item => {
                this.getParentName(activeName, item)
            })
            
            this.$nextTick(() => {
                this.$refs.menuR.updateOpened()
            })
            // console.log(this.openNames, activeName)
        },
        getParentName(name, data) {
            let activeName = ''
            if (data.children) {
                data.children.forEach(cit => {
                    if (cit.children) {
                        name = this.getParentName(name, cit)
                    }
                    if (cit.code === name) {
                        activeName = data.code
                    }
                })
            }
            if (activeName) {
                this.openNames.push(activeName)
            }
            return activeName || name
        }
    }
}

