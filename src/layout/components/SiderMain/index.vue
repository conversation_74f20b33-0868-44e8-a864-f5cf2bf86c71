<template>
    <div class="sider-main">
        <div class="menu-main" v-if="!isCollapsed">
            <div class="menu-list" v-if="permission_routes.length > 0">
                <Menu :active-name="activeName" :open-names="openNames" ref="menuR" @on-select="selectMenu" width="200">
                    <template v-for="menu in permission_routes">
                        <Submenu v-if="menu.children && menu.children.length > 0" :name="menu.code" :class="[hasActiveMenuClass(menu)]">
                            <template #title>
                                <span class="icon iconfont" :class="[menu.iconName]" v-if="menu.iconName"></span>
                                <Icon size="18" :custom="`iconfont ${menuIconEnums[menu.code] || 'icon-apps'}`" v-else />
                                {{ menu.name }}
                            </template>
                            <template v-for="sec in menu.children">
                                <div class="sec-menu" v-if="sec.children && sec.children.length > 0" :name="sec.code">
                                    <menu-poptip :active-name="activeName" :open-names="openNames" :menu="sec" type="2" />
                                </div>
                                <MenuItem v-else :to="getPath(sec)" :target="sec.accessType == 3 ? '_blank':'_self'" style="padding-left: 56px;" :name="sec.code">{{ sec.name }}</MenuItem>
                            </template>
                        </Submenu>
                        <MenuItem v-else :to="getPath(menu)" :name="menu.code" :target="menu.accessType == 3 ? '_blank':'_self'">
                            <span class="icon iconfont" :class="[menu.iconName]" v-if="menu.iconName"></span>
                            <Icon size="18" :custom="`iconfont ${menuIconEnums[menu.code] || 'icon-apps'}`" v-else />
                            <span class="menu-item">{{ menu.name }}</span>
                        </MenuItem>
                    </template>
                </Menu>
            </div>
        </div>

        <!--    收起    -->
        <div class="menu-main" v-else>
            <div class="menu-list" style="padding-left: 4px;padding-right: 4px;">
                <Menu :active-name="activeName" :open-names="openNames" ref="menuR" width="200">
                    <template v-for="menu in permission_routes">
                        <menu-poptip :menu="menu" :active-name="activeName" :open-names="openNames" :is-collapsed="true" />
                    </template>
                </Menu>
            </div>
        </div>
    </div>
</template>

<script>
import { MenuListMix } from './menu-mixins'
import MenuPoptip from './MenuPoptip';
import menuIconEnums from '@/config/menuIcon.ts'

export default {
    name: 'SiderMain',
    components: {
        MenuPoptip
    },
    mixins: [MenuListMix],
    props: {
        isCollapsed: { default: false }
    },
    data() {
        return {
            iframeAll: {},
            menuIconEnums
        };
    },
    methods: {
        selectMenu(code) {
            if (this.iframeAll[code]) {
                this.$store.dispatch('common/setOpenIframeObj', this.iframeAll[code])
            }
        },
        getPath(menu) {
            if (menu.accessType == 2) {
                this.iframeAll[menu.code] = menu
                return '/common/openIframe'
            }
            if (menu.url) {
                return menu.url
            }
            return '/'
        },
        getCurMenu(code, item) {

        }
    }
};
</script>

<style lang="less" scoped>
.sider-main {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;

    .menu-main {
        width: 100%;
        flex: 1;
        overflow: hidden;
    }
    .menu-list{
        width: 100%;
        height: 100%;
        overflow: auto;
        padding: 0 8px 48px;
    }
    .menu-item { margin-left:6px;}
}
</style>
