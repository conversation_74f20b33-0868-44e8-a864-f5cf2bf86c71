<template>
    <a :href="getPath(menu)" @click="selectMenu" :target="menu.accessType == 3 ? '_blank':'_self'" class="menu-item-a" :class="[activeName == menu.code ? 'on' : '']">
        {{ menu.name }}
    </a>
</template>

<script>

export default {
    name: 'MenuItemCustom',
    props: {
        menu: { default() { return {} } },
        activeName: { default: '' }
    },
    data() {
        return {}
    },
    methods: {
        selectMenu() {
            if (this.menu.accessType == 2) {
                this.$store.dispatch('common/setOpenIframeObj', this.menu)
            }
        },
        getPath() {
            if (this.menu.accessType == 2) {
                return '#/common/openIframe'
            }else if (this.menu.accessType == 3) {
                return this.menu.url
            }
            if (this.menu.url) {
                return '#' + this.menu.url
            } else {
                return '#/'
            }
        }
    }
}
</script>

<style lang="less" scoped>
.menu-item{

}
</style>
