<template>
    <Dropdown
        placement="top-start"
        trigger="click"
        @on-click="getPath"
        v-if="settingMenu.length > 0"
    >
        <div class="coll-box" :class="{on: activeName}">
            <Icon custom="iconfont icon-settings" />
        </div>
        <template #list>
            <DropdownMenu>
                <DropdownItem v-for="menu in settingMenu" :name="menu.code"
                              :key="menu.code" :selected="menu.code === activeName">{{ menu.name }}</DropdownItem>
            </DropdownMenu>
        </template>
    </Dropdown>
    <div v-else></div>
</template>

<script>
import { mapGetters } from 'vuex';
export default {
    name: 'settingMenu',
    components: {
    },
    data() {
        return {
            showPopFlag: false
        }
    },
    computed: {
        ...mapGetters(['settingMenu']),
        activeName() {
            const route = this.$route;
            let name = route.name
            let obj = this.settingMenu.find(it => it.code == name)
            if (obj) {
                return name || '';
            }
            return ''
        }
    },
    methods: {
        getPath(code) {
            this.$router.push({
                name: code
            })
        }
    }
}
</script>

<style lang="less" scoped>
.coll-box{

    width: 24px;
    height: 24px;
    display: flex;
    cursor: pointer;
    background: #F8FAFB;
    border-radius: 2px;
    .ivu-icon{
        margin: auto;
        color: @text-color;
    }
    &.on{
        background: @fill-2;
        .ivu-icon{
            color: @primary-color;
        }
    }
}
</style>
