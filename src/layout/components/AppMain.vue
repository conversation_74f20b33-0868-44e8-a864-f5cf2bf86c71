<template>
    <section class="app-main">
        <!-- <transition name="fade-transform" mode="out-in">
            <keep-alive>
                <router-view />
            </keep-alive>
        </transition> -->
        <router-view v-slot="{ Component }">
            <transition name="fade-transform" mode="out-in">
                <keep-alive>
                    <div>
                        <component :is="Component" />
                    </div>
                </keep-alive>
            </transition>
        </router-view>
    </section>
</template>

<script>
export default {
    name: 'AppMain',
    computed: {
    }
};
</script>

<style lang="less" scoped>
.app-main{
    width: 100%;
    height: 100%;
    overflow-y: auto;
    overflow-x: hidden;
    padding: 0 16px 16px;
}
</style>
