<template>
    <div class="user-main">
        <Dropdown placement="bottom-end" transfer-class-name="cancel-max-height" class="message-drop" transfer>
            <div class="message-box">
                <div class="message">
                    <i class="iconfont icon-notification"></i>
                    <span v-show="unread" class="unread">{{ unread }}</span>
                </div>
            </div>

            <template #list>
                <DropdownMenu>
                    <Messages v-model="unread" />
                </DropdownMenu>
            </template>
        </Dropdown>
        <Dropdown placement="bottom-end" transfer>
            <div class="user-name">
                <Avatar icon="ios-person" :size="24" style="background-color: #94bfff" />
                <span class="name">{{ loginUser.nickname }}</span>
                <div class="arrow">
                    <Icon type="ios-arrow-down"></Icon>
                </div>
            </div>
            <template #list>
                <DropdownMenu>
                    <DropdownItem @click.native="changePersonal">个人中心</DropdownItem>
                    <DropdownItem @click.native="changePwd">修改密码</DropdownItem>
                    <DropdownItem v-if="applicationAuth.length > 1" @click.native="changeSys">切换系统</DropdownItem>
                    <DropdownItem @click.native="logout">退出登录</DropdownItem>
                </DropdownMenu>
            </template>
        </Dropdown>
    </div>
    <s-modal :width="500" title="修改密码" :component-name="componentName" @emitClose="componentName = ''" :ref-box="modalRef"
        :transfer="true">
        <component :is="componentName" ref="modalRef" />
    </s-modal>
</template>

<script>
import { mapGetters } from 'vuex';
import Messages from './messages.vue';
import PwdModal from './pwd-modal.vue';
export default {
    name: 'User',
    components: {
        Messages,
        PwdModal
    },
    data() {
        return {
            unread: 0,
            componentName: '',
            modalRef: null
        };
    },
    computed: {
        ...mapGetters(['loginUser', 'applicationAuth'])
    },
    methods: {
        changeSys() {
            let url = `${location.protocol}//${location.hostname}:16007`;
            //兼容宜昌环境三智跳转，如果是宜昌环境，端口要改成**********:15673
            if (location.hostname.startsWith('**********')) {
                url = `${location.protocol}//${location.hostname}:15673`;
            }
            if (process.env.VUE_APP_LOGIN_URL) {
                url = process.env.VUE_APP_LOGIN_URL;
            }
            location.href = url + '/#/login/changeSysPage';
        },
        changePwd() {
            this.componentName = 'PwdModal'
            this.$nextTick(() => {
                setTimeout(() => this.modalRef = this.$refs.modalRef, 1000)
            })
        },
        changePersonal() {
            this.$router.push('/personal');
        },
        logout() {
            this.$store.dispatch('user/logout').then(() => {
                this.$router.push('/login');
            });
        },
    },
};
</script>

<style lang="less" scoped>
.user-main {
    display: flex;
    align-items: center;

    .message-drop {
        .message-box {
            height: 48px;
            display: flex;
            align-items: center;
            justify-content: center;

            .message {
                width: 24px;
                height: 24px;
                background-color: #fff;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                color: @primary-color;
                margin-right: 16px;
                cursor: pointer;
                position: relative;

                .unread {
                    position: absolute;
                    top: -7px;
                    left: 11px;
                    display: block;
                    background-color: rgb(255, 0, 0, 0.8);
                    color: #fff;
                    text-align: center;
                    scale: 0.8;
                    min-width: 20px;
                    height: 20px;
                    border: 2px solid #ffffff;
                    border-radius: 20px;
                    font-size: 12px;
                    line-height: calc(20px * 0.8);
                    padding: 0 5px;
                }

                &:hover {
                    background-color: #f3f7fb;
                }
            }
        }
    }

    .user-name {
        display: flex;
        align-items: center;

        .name {
            padding-left: 4px;
            padding-right: 6px;
        }

        .arrow {
            display: inline-block;
            background: #ffffff;
            border-radius: 50%;
            line-height: 14px;
            width: 14px;
            height: 14px;
            vertical-align: middle;
            color: @text-color;
            font-size: 12px;
            text-align: center;
            transition: transform 0.2s;
        }

        .ivu-avatar.ivu-avatar-icon {
            font-size: 16px !important;
        }

        &:hover {
            .arrow {
                transform: rotate(180deg);
            }
        }
    }
}</style>
