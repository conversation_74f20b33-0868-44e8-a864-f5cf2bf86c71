<template>
    <div class="memeber-list">
        <div class="memeber-item">
            <div class="" v-show="!showSelf || !video">
                <div class="img-box">{{ loginUser.nickname?.slice(0, 1) }}</div>
            </div>
            <video ref="mainVideo" class="main-video" v-show="video && showSelf" autoplay></video>
            <div class="name">{{ loginUser.nickname }}</div>
        </div>
        <div class="memeber-item">
            <div class="" v-show="!isConnected || !personVideo">
                <div class="img-box">
                    {{ connectPeople[0]?.label?.slice(0, 1) }}
                    <div class="mask" v-show="!isConnected">...</div>
                </div>
            </div>
            <video class="secondary-video" ref="secondaryVideo" autoplay v-show="isConnected && personVideo"></video>
            <div class="name">{{ connectPeople[0]?.label }}</div>
        </div>
    </div>
    <div class="bottom-box">
        <div class="left-operation">
            <!-- <div class="">
                <Icon custom="iconfont icon-bulb" color="#165DFF" />扬声器已打开
            </div> -->
            <div class="" @click="audio = !audio">
                <Icon :custom="`iconfont ${audio ? 'icon-voice' : 'icon-voice-off'}`" color="#165DFF" />麦克风{{ audio ? '已打开'
                    :
                    '已关闭' }}
            </div>
            <div class="" @click="video = !video">
                <Icon :custom="`iconfont ${video ? 'icon-video-camera' : 'icon-video-camera-off'}`" color="#165DFF" />摄像头{{
                    video ? '已打开' : '已关闭' }}
            </div>
        </div>
        <div class="right-box" @click="video = !video">
            <Button type="error" @click="cancelVideoCall">
                <Icon type="md-close" />取消
            </Button>
        </div>
    </div>
</template>
<script lang="ts" setup>
import { Message } from 'view-ui-plus';
import { onMounted, ref, defineEmits, defineProps, watch } from 'vue';
import { useStore } from 'vuex';
import { updateCallLog } from '@/api/safeManage/emergencyManagement';
const mainVideo = ref();
const secondaryVideo = ref(); // 接收方ref
const localUserMedia = ref(); // 本地ref
const isConnected = ref(false) // 是否接通
const showSelf = ref(false) //
const connectPeople = ref([]) // 接收人员
const store = useStore();
const audio = ref(true); // 开启音频
const video = ref(false) // 是否开启视频
const personVideo = ref(false); // 对方video状态
const { peerStore, loginUser } = store.getters
const emits = defineEmits(['handleClose'])
const props = defineProps(
    {
        meetingInfo: {
            type: Array,
            default: () => { }
        }
    }
)
const getUserMedia = (constrains: any) => {
    if (window.navigator.mediaDevices.getUserMedia) {
        return window.navigator.mediaDevices.getUserMedia(constrains);
    } else if (window.navigator.webkitGetUserMedia) {
        return window.navigator.webkitGetUserMedia(constrains);
    } else if (window.navigator.mozGetUserMedia) {
        return window.navigator.mozGetUserMedia(constrains);
    } else if (window.navigator.getUserMedia) {
        return window.navigator.getUserMedia(constrains);
    }
}
const initPeerStore = async() => {
    if (!peerStore) { return; }
    connectPeople.value = props.meetingInfo.people?.filter((k: any) => k.contactPhone != loginUser.phone) || []
    const calleePeerId = connectPeople.value[0]?.peerId
    getUserMedia({ audio: true, video: true }).then(async(userMedia: any) => {
        localUserMedia.value = userMedia;
        if (localUserMedia.value && localUserMedia.value.getVideoTracks()) {
            localUserMedia.value.getVideoTracks()[0].enabled = false
        }
        if (!showSelf.value) {
            showSelf.value = true
        }
        mainVideo.value.srcObject = localUserMedia.value;
        if (!calleePeerId || isConnected.value) return;
        peerStore.dataConnection = peerStore.localPeer.connect(calleePeerId);
        listenData(calleePeerId)
    }).catch(() => {
        listenData(calleePeerId)
    });
}
const listenData = (calleePeerId: string) => {
    peerStore.dataConnection.on('data', (data: any) => {
        console.log(data)
        if (data.instruction === peerStore.instruction.accept) { // 接受
            console.log('call')
            peerStore.mediaConnection = peerStore.localPeer.call(calleePeerId, localUserMedia.value);
            if (isConnected.value) return
            isConnected.value = true;
            peerStore.mediaConnection.on('stream', (remoteUserMedia: any) => {
                setTimeout(() => {
                    console.log('connected');
                    secondaryVideo.value.srcObject = remoteUserMedia
                }, 50)
            });
        }
        if (data.instruction === peerStore.instruction.ringOff) { // 对方手动刮断
            Message.error(`${connectPeople.value[0]?.label} 已挂断`)
            peerStore.dataConnection?.close();
            peerStore.mediaConnection?.close();
            cancelVideoCall(false)

        } else if (data.instruction === peerStore.instruction.cancel) { // 程序取消
            cancelVideoCall(false)
        } else if (data.instruction === peerStore.instruction.reject) { // 对方手动拒绝
            Message.error(`对方已拒绝`)
            peerStore.dataConnection?.close();
            cancelVideoCall(false)

        } else if (data.video == true || data.video == false) {
            personVideo.value = data.video
        }
    })
    // 当连接被打开的时候理解发送一个指令给对方
    peerStore.dataConnection.on('open', () => {
        console.log('open')
        peerStore.dataConnection.send({
            instruction: peerStore.instruction.request,
            topicName: props.meetingInfo?.topicName || '会议主题',
            nickName: loginUser.nickname,
            callLogId: props.meetingInfo?.callLogId
        });
    });
}
const cancelVideoCall = (sendMsg = true) => {
    for (let track of localUserMedia.value?.getTracks()) {
        track.stop();
    }
    mainVideo.value.srcObject = null
    secondaryVideo.value.srcObject = null
    if (sendMsg) {
        peerStore.dataConnection?.send({
            instruction: peerStore.instruction.ringOff
        });
    }
    peerStore.dataConnection?.close();
    peerStore.mediaConnection?.close();
    // 有可能连接还没建立，所以这里用可选链操作符
    peerStore.dataConnection = undefined;
    peerStore.mediaConnection = undefined;
    emits('handleClose')
    if (isConnected.value) {
        // 更新通话时间
        updateCallLog({
            id: props.meetingInfo.callLogId,
        })
    }
    isConnected.value = false
}
defineExpose({ cancelVideoCall })
watch(() => [video.value], () => {
    if (localUserMedia.value && localUserMedia.value.getVideoTracks()) {
        localUserMedia.value.getVideoTracks()[0].enabled = video.value
        peerStore.dataConnection?.send({
            video: video.value
        });
    }

})
watch(() => audio.value, () => {
    if (localUserMedia.value && localUserMedia.value.getAudioTracks()) {
        localUserMedia.value.getAudioTracks()[0].enabled = audio.value
    }

})
onMounted(() => {
    initPeerStore()
});
</script>
<style lang="less" scoped>
.memeber-list {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    text-align: center;
    color: #4E627E;
    font-size: 12px;
    padding-bottom: 16px;

    .memeber-item {
        height: 300px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        row-gap: 16px;

        &>div {
            display: flex;
            flex-direction: column;
            gap: 16px;
        }

        video {
            height: 100%;
            width: 100%;
        }

        .img-box {
            height: 64px;
            width: 64px;
            border-radius: 100%;
            background-color: #C2C6CE;
            line-height: 64px;
            color: #fff;
            font-size: 28px;
            font-weight: bold;
            position: relative;

            .mask {
                position: absolute;
                height: 100%;
                width: 100%;
                background: rgba(0, 0, 0, 0.3);
                color: #fff;
                line-height: 46px;
                border-radius: 100%;
                font-size: 30px;
                font-weight: bold;
                top: 0;
            }
        }
    }
}

.bottom-box {
    padding-top: 16px;
    border-top: 1px solid #E5E6EB;
    display: flex;
    height: 48px;
    color: #165DFF;
    justify-content: space-between;

    .left-operation {
        font-size: 12px;
        display: flex;
        column-gap: 8px;
        line-height: 32px;

        &>div {
            cursor: pointer;
            padding: 0 16px;

            &:hover {
                background-color: #f3f7fb;
            }
        }

    }

}
</style>
