<template>
    <div class="memeber-list">
        <div class="memeber-item">
            <div class="" v-show="!isConnected || !personVideo">
                <div class="img-box">
                    {{ props.meetingInfo.people?.[0]?.nickname?.slice(0, 1) }}
                </div>
                <!-- <div class="name">{{ props.meetingInfo.people?.[0]?.nickname }}</div> -->
            </div>
            <video ref="mainVideo" class="main-video" autoplay v-show="isConnected && personVideo"></video>
            <div class="name">{{ props.meetingInfo.people?.[0]?.nickname }}</div>
        </div>
        <div class="memeber-item">
            <div class="" v-show="!isConnected || !video">
                <div class="img-box">{{ loginUser.nickname?.slice(0, 1) }}</div>
            </div>
            <video class="secondary-video" ref="secondaryVideo" autoplay v-show="isConnected && video"></video>
            <div class="name">{{ loginUser.nickname }}</div>
        </div>
    </div>
    <div class="bottom-box">
        <div class="left-operation">
            <!-- <div class="">
                <Icon custom="iconfont icon-bulb" color="#165DFF" />扬声器已打开
            </div> -->
            <div class="" @click="audio = !audio">
                <Icon :custom="`iconfont ${audio ? 'icon-voice' : 'icon-voice-off'}`" color="#165DFF" />麦克风{{ audio ? '已打开'
                    :
                    '已关闭' }}
            </div>
            <div class="" @click="video = !video">
                <Icon :custom="`iconfont ${video ? 'icon-video-camera' : 'icon-video-camera-off'}`" color="#165DFF" />摄像头{{
                    video ? '已打开' : '已关闭' }}
            </div>
        </div>
        <div class="right-box">
            <Button type="error" @click="isConnected ? cancelVideoCall() : rejectVideoCall()">
                <Icon type="md-close" />{{ isConnected ? '取消' : '拒绝' }}
            </Button>
            <Button type="primary" @click="initPeerStore" v-if="!isConnected">
                <Icon type="md-microphone" />接听
            </Button>
        </div>
    </div>
</template>
<script lang="ts" setup>
import { onMounted, ref, defineEmits, defineProps, computed, watch } from 'vue';
import { useStore, mapGetters } from 'vuex';

import { addCallLogPerson } from '@/api/safeManage/emergencyManagement';
const mainVideo = ref();
const secondaryVideo = ref();
const localUserMedia = ref();
const isConnected = ref(false)
const store = useStore();
const audio = ref(true);
const video = ref(false)
const personVideo = ref(false) // 对方video状态
const mediaConnection = ref()
const { loginUser } = store.getters
const emits = defineEmits(['handleClose'])
const props = defineProps(
    {
        meetingInfo: {
            type: Array,
            default: () => { }
        }
    }
)

const _peerStore = computed<any>(
    mapGetters(['peerStore']).peerStore.bind({ $store: store })
);
const peerStore = _peerStore.value
const getUserMedia = (constrains: any) => {
    if (window.navigator.mediaDevices.getUserMedia) {
        return window.navigator.mediaDevices.getUserMedia(constrains);
    } else if (window.navigator.webkitGetUserMedia) {
        return window.navigator.webkitGetUserMedia(constrains);
    } else if (window.navigator.mozGetUserMedia) {
        return window.navigator.mozGetUserMedia(constrains);
    } else if (window.navigator.getUserMedia) {
        return window.navigator.getUserMedia(constrains);
    }
}
const initPeerStore = () => {
    console.log('initPeerStore')
    if (!peerStore) { return; }
    peerStore.activateNotification = false;
    // 通知发送方 同意连接
    peerStore.dataConnection.send({
        instruction: peerStore.instruction.accept
    });

    // 加入通话接口接口
    addCallLogPerson({
        callLogId: props.meetingInfo?.callLogId,
        personPhone: loginUser.phone
    })

    // 监听对方发生的data
    listenData()
    getMedia()
}

const getMedia = () => {
    isConnected.value = true;
    getUserMedia({ audio: true, video: true }).then(async(userMedia: any) => {
        console.log('userMedia', secondaryVideo.value)
        localUserMedia.value = userMedia;
        if (localUserMedia.value && localUserMedia.value.getVideoTracks()) {
            localUserMedia.value.getVideoTracks()[0].enabled = false
        }
        secondaryVideo.value.srcObject = userMedia;
        peerStore.mediaConnection.answer(localUserMedia.value);
        peerStore.mediaConnection.on('stream', (remoteUserMedia: any) => {
            console.log('getConnected');
            mainVideo.value.srcObject = remoteUserMedia
        });
    }).catch((e: any) => {
        console.log(e)
        console.log('1', peerStore.mediaConnection)
        setTimeout(() => {
            console.log('2', peerStore.mediaConnection)
            peerStore.mediaConnection?.answer(localUserMedia.value);
            peerStore.mediaConnection?.on('stream', (remoteUserMedia: any) => {
                console.log('getConnected');
                mainVideo.value.srcObject = remoteUserMedia
            }, (err: any) => {
                console.log(err)
            });
        }, 2000)
    })
}
const listenData = () => {
    peerStore.dataConnection.on('data', (data: any) => {
        if (data.instruction === peerStore.instruction.cancel) { // 对方程序取消
            cancelVideoCall(false)

        } else if (data.instruction === peerStore.instruction.ringOff) { // 对方手动挂断
            cancelVideoCall(false)
        } else if (data.video == true || data.video == false) {
            personVideo.value = data.video
        }
    })
}
watch(() => _peerStore.value.mediaConnection, newValue => {
    console.log(newValue)
    if (newValue) {
        mediaConnection.value = newValue

        getMedia()
    }
}, {
    deep: true
})
// 取消
const cancelVideoCall = async(sendMsg = true) => {
    if (localUserMedia.value) {
        for (let track of localUserMedia.value?.getTracks()) {
            await track.stop();
        }
    }
    if (mainVideo.value) mainVideo.value.srcObject = null
    if (secondaryVideo.value) secondaryVideo.value.srcObject = null

    if (sendMsg) {
        peerStore.dataConnection?.send({
            instruction: peerStore.instruction.ringOff
        });
    }
    peerStore.dataConnection?.close();
    peerStore.mediaConnection?.close();
    peerStore.dataConnection = undefined;
    peerStore.mediaConnection = undefined;
    localUserMedia.value = undefined
    isConnected.value = false
    console.log('handleClose')
    emits('handleClose')
}
// 拒绝
const rejectVideoCall = () => {
    peerStore.dataConnection?.send({
        instruction: peerStore.instruction.reject
    });
    peerStore.dataConnection = undefined;
    peerStore.activateNotification = false;
    emits('handleClose')
}
defineExpose({ cancelVideoCall })
onMounted(() => {
    // initPeerStore()
});
watch(() => [video.value], () => {
    if (localUserMedia.value && localUserMedia.value.getVideoTracks()) {
        localUserMedia.value.getVideoTracks()[0].enabled = video.value
        peerStore.dataConnection?.send({
            video: video.value
        });
    }

})
watch(() => audio.value, () => {
    if (localUserMedia.value && localUserMedia.value.getAudioTracks()) {
        localUserMedia.value.getAudioTracks()[0].enabled = audio.value
    }
})
</script>
<style lang="less" scoped>
.memeber-list {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    text-align: center;
    color: #4E627E;
    font-size: 12px;
    padding-bottom: 16px;

    .memeber-item {
        height: 300px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        row-gap: 16px;

        &>div {
            display: flex;
            flex-direction: column;
            gap: 16px;
        }

        video {
            height: 100%;
            width: 100%;
        }

        .img-box {
            height: 64px;
            width: 64px;
            border-radius: 100%;
            background-color: #C2C6CE;
            line-height: 64px;
            color: #fff;
            font-size: 28px;
            font-weight: bold;
            position: relative;

            .mask {
                position: absolute;
                height: 100%;
                width: 100%;
                background: rgba(0, 0, 0, 0.3);
                color: #fff;
                line-height: 46px;
                border-radius: 100%;
                font-size: 30px;
                font-weight: bold;
                top: 0;
            }
        }
    }
}

.bottom-box {
    padding-top: 16px;
    border-top: 1px solid #E5E6EB;
    display: flex;
    height: 48px;
    color: #165DFF;
    justify-content: space-between;

    .left-operation {
        font-size: 12px;
        display: flex;
        column-gap: 8px;
        line-height: 32px;

        &>div {
            cursor: pointer;
            padding: 0 16px;

            &:hover {
                background-color: #f3f7fb;
            }
        }

    }

}
</style>
