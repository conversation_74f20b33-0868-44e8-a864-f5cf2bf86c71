import axios from 'axios'
import config from '@/config/config'

const util = {
	baseURL: config.env === 'development' ? config.devProject : config.proProject,
	language: { code: 'zh' },
	theme: 'themeLight',
	axios,
	tbHeight: 600,
	defComTxt: '--',
	defAttrTxt: '--',
	timeRangeOption: {
		disabledDate(date) {
			return date && date.valueOf() > Date.now();
		},
		shortcuts: [
			{
				text: '一周内',
				value() {
					const end = new Date();
					const start = new Date();
					start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
					return [start, end];
				}
			},
			{
				text: '一个月内',
				value() {
					const end = new Date();
					const start = new Date();
					start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
					return [start, end];
				}
			},
			{
				text: '三个月内',
				value() {
					const end = new Date();
					const start = new Date();
					start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
					return [start, end];
				}
			}
		]
	},
	isVideoType(item) {
		let arr = ['ezopen', 'flvUrl', 'mu38', 'live_address', 'hlsUrl']
		return item.unit === 'videoMedia' && arr.indexOf(item.propCode) >= 0
	},
	isInternalIP(){
		const ipRegex = /^(10\.|172\.(1[6-9]|2[0-9]|3[0-1])\.|192\.168\.)/;
		return ipRegex.test(window.location.hostname)
	},
	getMapStyle(theme, url) {
		if (!theme) {
			theme = this.local.get('themeColor')
		}
		if (theme === 'dark') {
			return 'amap://styles/darkblue'
		} else {
			return 'amap://styles/normal'
			// return 'amap://styles/0400cd8783ea24fdc813701b8e44b102'
		}
	},
	title(title, platform) {
		if (!platform) {
			platform = this.local.get('platformTitle') || '应用使能平台'
		}
		title = title ? (title + ' - ' + platform) : platform
		document.title = title
	},
	request(url, data, method = 'get', headers, responseType, fallTip, cancelToken) {
		const _data = data || {}
    headers = headers || {}
		headers.aep = this.local.get('aep') || ''
    const $saasToken$ = this.local.get('$saasToken$')
    if ($saasToken$) {
      headers['token'] = $saasToken$ // 使用token可以免登录
    }
		return axios({
			method,
			baseURL: this.baseURL,
			url,
			data: _data,
			responseType,
			fallTip, // 失败是否自动弹窗
			headers: Object.assign({ 'X-Requested-With': 'XMLHttpRequest' }, headers,{'source':'aep'}),
			cancelToken
		})
	},
  requestGet(url, headers, responseType) {
    return this.request(url, {}, 'get', headers, responseType)
  },
  requestPost(url, data, headers, responseType) {
    return this.request(url, data, 'post', headers, responseType)
  },
  requestPut(url, data, headers, responseType) {
    return this.request(url, data, 'put', headers, responseType)
  },
  requestDelete(url, data, headers, responseType) {
    return this.request(url, data, 'delete', headers, responseType)
  },
	requestT(url, data, method = 'get') {
		const _data = data || {}
		return axios({
			method,
			url: `/test${url}`,
			data: _data
		})
	},
	getWsUrl(path, ip) {
		let user = this.getUser()
		let hostname = location.host
		let ws = 'ws'
		if (location.protocol == 'https:') {
			ws = 'wss'
		}
		if (ip) {
            hostname = ip
		} else {
			let reg = /^\d{3}\.\d{1,3}\.\d{1,3}\.\d{1,3}\:(80)\d{2}$/g
			if (hostname.indexOf('localhost') >= 0 || reg.test(hostname)) {
				hostname = 'linkapp-out-test-service.easylinkin.com'
                // hostname = 'linkapp-in-mirror-service.easylinkin.com'
			}
			hostname += '/api'
		}
        let url = `${ws}://${hostname}/${path}/${user.tenantId}/${user.id}`
        return url
	},
	objClone(source, defaultValue) {
		// 拷贝的对象中的属性值不能是fun
		return source ? JSON.parse(JSON.stringify(source)) : defaultValue
	},
	deepCopy(data) {
		// 如果拷贝的对象中有fun，则采用此方法进行拷贝
		const t = typeof (data)
		let o
		if (t === 'array') {
			o = []
		} else if (t === 'object') {
			o = {}
		} else {
			return data
		}
		if (t === 'array') {
			for (let i = 0; i < data.length; i++) {
				o.push(this.deepCopy(data[i]))
			}
		} else if (t === 'object') {
			for (const i in data) {
				o[i] = this.deepCopy(data[i])
			}
		}
		return o
	},
	extend(source, target) {
		for (const key in target) {
			source[key] = source[key] && source[key].toString() === '[object Object]' ? this.extend(source[key], target[key]) : source[key] = target[key]
		}
		return source
	},
	copyAttr(obj, ...arrs) {
		// 拷贝obj的部分属性
		const copyObj = {}
		arrs.forEach(item => copyObj[item] = obj[item])
		return copyObj
	},
	setTime(days) {
		const endTime = this.getNowTime().endTime
		const befDate = new Date(this.getNowTime().nowDate.getTime() - days * 24 * 3600 * 1000)
		const byear = befDate.getFullYear()
		const bmonth = befDate.getMonth() + 1
		const bday = befDate.getDate()
		const start_h = befDate.getHours()
		const start_m = befDate.getMinutes()
		const start_s = befDate.getSeconds()
		const startTime = `${byear}-${bmonth}-${bday} ${start_h}:${start_m}:${start_s}`
		return { startTime, endTime }
	},
	getNowTime() {
		const nowDate = new Date()
		const year = nowDate.getFullYear()
		const month = nowDate.getMonth() + 1
		const day = nowDate.getDate()
		const end_h = nowDate.getHours()
		const end_m = nowDate.getMinutes()
		const end_s = nowDate.getSeconds()
		const endTime = `${year}-${month}-${day} ${end_h}:${end_m}:${end_s}`
		return { nowDate, endTime }
	},
	compareTime(startTime, endTime) {
		if (!endTime) endTime = this.getNowTime().endTime
		return new Date(startTime).getTime() > new Date(endTime).getTime()
	},
	ObjToArray(target, source) {
		let result = [];
		_.map(source).forEach(item => {
			result.push(item[target])
		})
		return result;
	},
	changeNum(attr) {
		/** 该方法是为了让不是必填但是又必须为数字型的字段通过async-validate的校验
		 * 校验时，只有为null或undefined的时候才能通过校验，详见async-validate
		 * 的number.js里面的校验规则。
		 */
		if (this.editObj[attr] === '') this.editObj[attr] = undefined
	},
	local: {
		set(key, value) {
			if (typeof value !== 'undefined') localStorage[key] = value
		},
		get(key, defaultValue) {
			return localStorage[key] || defaultValue
		},
		removeItem(key) {
			return localStorage.removeItem(key)
		},
		clearSession() {
			localStorage.clear()
			sessionStorage.clear()
		},
    set$saasToken$(val) {
      this.set('$saasToken$', val)
    },
    delete$saasToken$() {
      this.removeItem('$saasToken$')
    }
	},
	arr: {
		append(source, target) {
			Array.prototype.push.apply(source, target)
		},
		// 数组根据某个属性分组
		group(arr, attr) {
			const groupObj = { noVal: [] }
			arr.forEach(item => {
				item.title = item.name // 为了权限树
				const val = item[attr]
				if (val !== undefined && val !== null) {
					groupObj[val] ? groupObj[val].push(item) : groupObj[val] = [item]
				} else {
					groupObj.noVal.push(item)
				}
			})
			return groupObj
		},
		// 获取数组某个属性的所有值
		attr(arr, attr) {
			if (!arr) return []
			return arr.map(item => item[attr])
		}
	},
	string: {
		trim(str) {
			return str.replace(/(^\s*)|(\s*$)/g, '')
		},
		camelCase(string) {
			return string.replace(/-([a-z])/g, function($1, $2) {
				return $2.toUpperCase()
			})
		}
	},
	duplist(list, attr = 'name') {
		if (!this.isAdmin()) return list
		// const nameArr = this.arr.attr(list, attr)
		list.map(item => {
			const company = this.companyList.find(company => company.id === (item.companyId || (item.company && item.company.id))) || {}
			if (company.name) {
				item[attr] = `${item[attr]}(${company.name})`
			}
		})
		return list
	},
	formatDecimal(value, decimal = 2) {
		// 对源数据截取decimals位小数，不进行四舍五入
		let realVal = ''
		if (!isNaN(value) && value !== '') {
			// 截取当前数据到小数点后x位
			let tempVal = parseFloat(value).toFixed(decimal + 1)
			realVal = tempVal.substring(0, tempVal.length - 1)
		} else {
			realVal = '--'
		}
		return realVal
	},
	verifyObjsAttrsNull(source, ...params) {
		if (!source) {
			return;
		}
		let flag = false;
		Object.keys(source).forEach(key => {
			_.forEach(params, item => {
				if (item == key && source[key]) {
					flag = true
				}
			})
		})
		return flag;
	},
	formatDate(date, fmt) {
		if (!fmt) fmt = 'yyyy-MM-dd hh:mm:ss'
		if (!date || date == null) return null;
		if (typeof date !== 'object') date = new Date(date)
		var o = {
			'M+': date.getMonth() + 1, // 月份
			'd+': date.getDate(), // 日
			'h+': date.getHours(), // 小时
			'm+': date.getMinutes(), // 分
			's+': date.getSeconds(), // 秒
			'q+': Math.floor((date.getMonth() + 3) / 3), // 季度
			'S': date.getMilliseconds() // 毫秒
		}
		if (/(y+)/.test(fmt)) fmt = fmt.replace(RegExp.$1, (date.getFullYear() + '').substr(4 - RegExp.$1.length))
		for (var k in o) {
			if (new RegExp('(' + k + ')').test(fmt)) fmt = fmt.replace(RegExp.$1, (RegExp.$1.length === 1) ? (o[k]) : (('00' + o[k]).substr(('' + o[k]).length)))
		}
		return fmt
	},
	getLocalTime(date) {
		if (!date) return new Date()
		const timeStr = this.formatDate(date, 'yyyy/MM/dd HH:mm:ss')
		return new Date(timeStr)
	},
	resetTheme() {
		this.theme = 'themeLight'
		const links = document.querySelectorAll('link')
		if (links.length > 1) document.querySelector('head').removeChild(links[1])
	},
	getDateRangeS(date, type, format = 'yyyy-MM-dd', isCurTime = false) {
		// 获取时间范围 如果不传入默认
		let res = [];
		let start, end = '';
		if (typeof date !== 'object') {
			date = new Date(date);
		}
		let endTime = null;
		switch (type) {
			case 'h':
				// 日
				endTime = new Date(date)
				break;
			case 'd': // 本周
				date.setDate(date.getDate() - date.getDay() + 1);
				endTime = new Date(date);
				endTime.setDate((endTime.getDate() - endTime.getDay()) + 7)
				break;
			case 'nd':// 近7天
				endTime = new Date(date)
				date.setDate(date.getDate() - 6);
				break;
			case 'm':// 本月
				date.setDate(1);
				endTime = new Date(date.getFullYear(), date.getMonth() + 1, 0)
				break;
			case 'nm':// 近30天
				endTime = new Date(date)
				date.setDate(date.getDate() - 29);
				break;
			case 'y':// 本年
				date.setMonth(0)
				date.setDate(1)
				endTime = new Date(date)
				endTime.setMonth(12)
				endTime.setDate(0)
				break;
			default:
				// statements_def
				break;
		}
		date.setHours(0)
		date.setMinutes(0)
		date.setSeconds(0)
		if (!isCurTime) {
			endTime.setHours(23)
			endTime.setMinutes(59)
			endTime.setSeconds(59)
		}

		start = this.formatDate(date, format)
		end = this.formatDate(endTime, format)
		res.push(start, end)
		return res;
	},
	isEmpty(val) {
		return val === undefined || val === null || val == '' || val.length === 0;
	},
	isObjEmpty(obj) {
		return this.isEmpty(Object.keys(obj))
	},
	getUser() {
		return JSON.parse(this.local.get('user') || '{}');
	},
	// 校验整个对象不为空
	verifyPar(targetParams, source) {
		if ($_.isEmpty(targetParams) || $_.isEmpty(source)) {
			return false;
		}
		for (let i = 0; i < targetParams.length; i++) {
			// 分割
			let obj = targetParams[i]
			let paths = obj.split('.');
			obj = this.findPathsByTargetObj(paths, source)
			if (($_.isArray(obj) && obj.length <= 0) || (typeof (obj) === 'object' && Object.keys(obj).length <= 0)) {
				return false;
			} else if ($_.isEmpty(obj)) {
				return false;
			}
		}
		return true;
	},
	findPathsByTargetObj(targetParams, source) {
		if (targetParams.length >= 1) {
			source = source[targetParams[0]];
			targetParams = targetParams.splice(1)
			return this.findPathsByTargetObj(targetParams, source)
		}
		return source;
	},
	verStringIfyObj(obj, ...items) {
		let flag = true;
		Object.keys(obj).forEach(item => {
			let par = obj[item];
			if (items.indexOf(item) > -1 && !par) {
				flag = false
			}
		})
		return flag;
	},
	hashSet(source, key) {
		let newArray = [];
		source.forEach(item => {
			let flag = true;
			newArray.forEach(copyItem => {
				if (item[key] === copyItem[key]) {
					flag = false;
				}
			})
			if (flag) {
				newArray.push(item)
			}
		})
		return newArray;
	},
	checkAuth(authCode) {
		if (this.isEmpty(authCode)) {
			return true;
		}
		const auth = JSON.parse(this.local.get('auth') || '{}');
		if (Array.isArray(authCode)) {
			return authCode.some(item => auth[item])
		} else {
			return auth[authCode];
		}
	},
	isNil(val) {
		return $_.isNil(val)
	},
	isSpace(item) {
		return item.nodeType == 'space'
	},
	assignObj(obj1, obj2) {
		return $_.assign(this.objClone(obj1), this.objClone(obj2))
	},
	setAreaPath(areaPath) {
		if (areaPath) {
			let arr = areaPath.replace(':', '/').split('/').slice(1);
			return arr.join()
		}
		return ''
	},
	setAreaPathFormat(areaPath, fmt = '/') {
		if (areaPath) {
			return areaPath.split(':').join(fmt)
		}
		return ''
	},
	pointMsgModal($this, Func, Obj = { title: '提示', text: '你确定要删除吗?' }) {
		$this.$Modal.confirm({
			title: `${Obj.title}`,
			content: `<p>${Obj.text}</p>`,
			onOk: () => {
				Func();
			}
		})
	},
	checkPhone(rule, value, callback) {
		if (this.isEmpty(value)) {
			callback();
		} else {
			var pattern = /^1[3456789]\d{9}$/;
			if (pattern.test(value)) {
				callback();
			} else {
				return callback('请输入正确的手机号');
			}
		}
	},
	checkEmail(rule, value, callback) {
		if (this.isEmpty(value)) {
			callback();
		} else {
			var pattern = /^([a-zA-Z0-9]+[_|\-|\.]?)*[a-zA-Z0-9]+@([a-zA-Z0-9]+[_|\-|\.]?)*[a-zA-Z0-9]+(\.[a-zA-Z]{2,3})+$/;
			if (pattern.test(value)) {
				callback();
			} else {
				return callback('请输入正确的邮箱');
			}
		}
	},
	// 格式化设备的物模型内容
	formatDeviceValue(item) {
		let specs = item && (item.specs || item.propUnit)
		if (typeof (specs) == 'string') {
			specs = JSON.parse(specs)
		}
		// console.log(specs)
		let specsKeys = specs && Object.keys(specs) || []
		let specsValues = specs && Object.values(specs) || []
		let value = item && item.propValue || 0
		let dataType = item && item.unit

		let realData = ''
		switch (dataType) {
			case 'bool':
				if (specsKeys.length > 0) {
					specsKeys.forEach((enumItem) => {
						if (value == enumItem) {
							realData = specs[enumItem]
						}
					})
				}
				if (realData == '') realData = value;
				break
			case 'enum':
				if (specsKeys.length > 0) {
					specsKeys.forEach((enumItem) => {
						if (value == enumItem) {
							realData = specs[enumItem]
						}
					})
					if (!realData) {
						specsValues.forEach((enumItem, index) => {
							if (value == enumItem) {
								realData = specsKeys[index]
							}
						})
					}
				}
				if (realData == '') realData = value;
				break;
			case 'double':
			case 'int':
			case 'float':
			case 'long':
				if (specs && specs.unit) {
					realData = value + ' ' + specs.unit
				} else {
					realData = value
				}
				break;
			default:
				realData = value;
				break;
		}

		return realData;
	},
	convertDeviceModelArrayValue(value, specsObj) {
		let arrayList = null
		if ($_.isArray(value)) {
			arrayList = value
		} else {
			arrayList = JSON.parse(value)
		}
		let unit = ''
		if (specsObj) {
			// 枚举数组
			if (specsObj.type == 'enum' || specsObj.type == 'bool') {
				if (!$_.isArray(arrayList)) {
					return specsObj.specs[value]
				}
				const list = $_.map(arrayList, v => specsObj.specs[v])
				return list.join(', ')
			}
			// 其它数组
			if (specsObj.unit) {
				unit = $_.toString(specsObj['unit'])
			} else if (specsObj.specs) {
				unit = $_.toString(specsObj.specs['unit'])
			}
		}
		if (!$_.isArray(arrayList)) {
			return arrayList + unit
		}
		const list = $_.map(arrayList, item => item + unit)
		return list.join(', ')
	},
  getURLParameter(sParam) {
    const url = location.href;
    let sPageURL = url.substring(url.indexOf('?') + 1);
    let sURLVariables = sPageURL.split('&');
    for (let i = 0; i < sURLVariables.length; i++) {
      let sParameterName = sURLVariables[i].split('=');
      if (sParameterName[0] == sParam) {
        return sParameterName[1];
      }
    }
  },
  removeURLParameter(sParam) {
    const url = location.href;
    let sPageURL = url.substring(url.indexOf('?') + 1);
    let sURLVariables = sPageURL.split('&');
    let search = []
    for (let i = 0; i < sURLVariables.length; i++) {
      if (!sURLVariables[i].startsWith(sParam + '=')) {
        search.push(sURLVariables[i]);
      }
    }
    return location.origin + location.pathname + '?' + search.join('&')
  },
  exportData(url, params, name, method = 'post', errorMsg = '导出异常') {
		this.request(url, params, method, {}, 'blob').then(resp => {
			let blob = new Blob([resp.data], { type: 'application/octet-stream' });
			let href = window.URL.createObjectURL(blob);
			this.download(href, name)
		}).catch(function(error) {
			console.log(errorMsg, error);
		})
	},
	download(href, name, target = '_blank') {
		let downloadElement = document.createElement('a');
		downloadElement.href = href;
		downloadElement.download = name;
		downloadElement.target = target;
		document.body.appendChild(downloadElement);
		downloadElement.click();
		document.body.removeChild(downloadElement);
		window.URL.revokeObjectURL(href);
	},
	EncodeURIFilter(str) {
		if (str != null && str != '') {
			str = str.replace(/\+/g, '%2B')
			str = str.replace(/\&/g, '%26')
		}
		return str
	},
  jumpDefaultPage (user) {
    if (user.indexConfigUrl) {
      if (user.indexConfigCode) {
        let hash = '/#'
        if (!user.indexConfigUrl.startsWith('/')) {
          hash += '/'
        }
        location.href = hash + user.indexConfigUrl
      } else {
        if (user.indexConfigUrl.indexOf('vis.html?token') >= 0 && user.indexConfigUrl.indexOf('type=preview') < 0) {
          location.href = user.indexConfigUrl + '&type=preview'
        } else {
          location.href = user.indexConfigUrl
        }
      }
    }
  }
}
export default util
