html,
body {
  width: 100%;
  height: 100%;
  color: @title-color;
  position: relative;
  //font-family: 'PingFang SC', 'Segoe UI', 'SourceHanSansCN', 'SF Pro Display';
}

#app,
.ivu-layout {
  width: 100%;
  height: 100%;
}

//滚动条
::-webkit-scrollbar {
  width: 6px;
  height: 8px;
  background: rgba(0, 0, 0, 0);
  cursor: pointer;
}

::-webkit-scrollbar-thumb {
  border-radius: 10px;
  box-shadow: inset 0 0 5px rgba(0, 0, 0, 0);
  background: rgba(0, 0, 0, 0.15);
  cursor: pointer;

  &:hover {
    background: rgba(0, 0, 0, 0.4);
    cursor: pointer;
  }
}

::-webkit-scrollbar-track {
  box-shadow: inset 0 0 5px rgba(0, 0, 0, 0);
  // border-radius: 0;
  background: rgba(0, 0, 0, 0);
}

::-webkit-scrollbar-track-piece {
  background: rgba(0, 0, 0, 0);
}

.ellipsis-1 {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.ellipsis-2 {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}


.btn-action {
  margin-right: 8px;
  font-weight: 400;
  padding: 0 4px;
  line-height: 24px;
  height: 24px;
  vertical-align: middle;
  border-radius: 2px;
  display: inline-block;

  &:hover {
    background: #BEDAFF;
  }

  &:active {
    background: #6AA1FF;
  }

  &[disabled],
  &[disabled]:hover,
  &.disabled,
  &.disabled:hover {
    color: #94BFFF;
    cursor: not-allowed;
    background: none;
  }

  &:last-child {
    margin-right: 0;
  }
}

.btn-submit {
  display: flex;
  justify-content: center;
  align-items: center;
  padding-top: 16px;

  .ivu-btn {
    min-width: 68px;
    margin: 0;

    &+.ivu-btn {
      margin-left: 8px;
    }
  }
}

// 修改表单默认样式
.ivu-input {
  border-radius: 2px;
}

.ivu-form-item {
  margin-bottom: 20px;

  button+button {
    margin-left: 8px;
  }

  &.operation-btn {
    .ivu-form-item-content {
      display: flex;
    }

    button {
      width: 82px;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
}

// 公共样式
.plate-tit {
  color: @title-color;
  font-weight: 500;
  font-size: 16px;
  line-height: 24px;
  height: 24px;
  display: flex;

  img.title-icon {
    display: block;
    height: 24px;
    width: 24px;
    margin-right: 4px;
  }
}

.ivu-select-selection {
  border-radius: 2px;
}

.scorll-map-cont {
  margin-top: 8px;
  margin-bottom: 8px;
  flex: 1;
  overflow-y: auto;
  padding-right: 4px;
  position: relative;
  overflow-x: hidden;
}

.amap-marker-label {
  border: none;
  background: none;
  padding: 0
}


.text-red {
  color: @error-color ;
  font-weight: bold;
}

.text-blue {
  color: @primary-color;
  font-weight: bold;
}

.primary-text {
  color: @primary-color;
  font-weight: 500;
}

.green-text {
  color: #4CD263;
  font-weight: 500;
}

.plate-bg {
  background: #FFFFFF;
  box-shadow: -2px 1px 13px rgba(83, 117, 167, 0.2);
  border-radius: 4px;
}

.online {
  background: #E8FFEA;
  color: #00B42A;
}

.offline {
  background: #F2F3F5;
  color: #4E627E;
}

.nodata-box {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

button+button {
  margin-left: 8px;
}

.list-cont-card {
  min-height: ~'calc(100vh - 100px)';
}

// 常用样式
.no-padding {
  padding: 0 !important;
}

.flex-box-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.ml-6 {
  margin-left: 6px;
}

.mt-8 {
  margin-top: 8px;
}

.mt-16 {
  margin-top: 16px;
}

.mt-32 {
  margin-top: 32px;
}

.mb-16 {
  margin-bottom: 16px;
}

.amap-logo {
  display: none !important; //去掉高德地图logo
}

.amap-copyright {
  opacity: 0; //去掉高德的版本号
}

.cancel-max-height {
  max-height: none; //取消下拉菜单的最大高度
}
.message-s-tab {
  justify-content: flex-start !important;
  column-gap: 12px;
  margin-bottom: 16px;
}

.air-tooltip {
  div {
    width: 180px;
    display: flex;
    justify-content: space-between;
    column-gap: 20px;

    span:first-child {
      width: 104px;
    }

    span:last-child {
      width: 60px;
      text-align: right;
    }
  }
}

.modal-footer-none {
  .ivu-modal-footer {
    display: none;
  }
}

.margin-bottom-46 {
  margin-bottom: 46px !important;
}

.add-form-planManagement1 {
  margin-top: 16px;
}

.add-form-planManagement2 {
  margin-top: 32px;
}

.map-icon-container {
  display: flex;
  flex-direction: column;
  align-items: center;

  .map-top-label {
    margin-bottom: 2px;
    height: 36px;
    padding: 8px;
    border-radius: 6px;
    background: linear-gradient(304.17deg, rgba(253, 254, 255, 0.6) -6.04%, rgba(244, 247, 252, 0.6) 85.2%),
      linear-gradient(300.56deg, #FFFFFF 5.95%, rgba(255, 255, 255, 0) 66.2%);
    border: 0.5px solid;
    border-image-source: linear-gradient(300.56deg, #FFFFFF 5.95%, rgba(255, 255, 255, 0) 66.2%);
    display: grid;
    place-items: center;

    .name {
      padding: 0 7px;
      box-shadow: 6px 0px 20px 0px #2257BC1A;
      height: 20px;
      border-radius: 4px;
      background-color: #ffffff;
      text-align: center;
      line-height: 20px;
      color: #165DFF;
      font-size: 12px;

    }
  }

  .icon {
    position: relative;
    img{
      width: 24px;
      height: 29px;
    }
    .point {
      position: absolute;
      left: 50%;
      bottom: 0;
      transform: translateX(-50%);
      width: 10px;
      height: 10px;
      border-radius: 50%;
      background: #165DFF;
      border: 1px solid #165DFF;
    }
  }
}