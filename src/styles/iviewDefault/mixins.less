// 样式公共方法


//分页大小
.page-size-set(@w) {
    .ivu-page-item{
        width: @w;
        height: @w;
        line-height: @w;
        min-width: @w;
    }
    .ivu-page-options{
        .ivu-select-single .ivu-select-selection{
            height: @w;
            .ivu-select-selected-value{
                height: @w;
                line-height: @w;
            }
        }
        .ivu-page-options-elevator{
            height: @w;
            line-height: @w;
            @input-h: @w - 2px;
            input{
                height: @input-h;
            }
        }
    }
}


//按钮填充状态悬停
.btn-fill-hover(@bg; @hoverBg; @activeBg; @disabledBg; @color: #fff; @disabledColor: #fff) {
    background: @bg;
    border-color: @bg;
    color: @color;
    &:focus{
        box-shadow: 0 0 0 2px fade(@bg, 20%);
    }
    &:hover{
        background: @hoverBg;
        color: @color;
        border-color: @hoverBg;
    }
    &:active{
        background: @activeBg;
        color: @color;
        border-color: @activeBg;
    }
    &[disabled]{
        background: @disabledBg;
        color: @disabledColor;
        border-color: @disabledBg;
        &:hover, &:active{
            background: @disabledBg;
            color: @disabledColor;
            border-color: @disabledBg;
        }
    }
}
//按钮镂空状态悬停
.btn-ghost-hover(@hoverColor; @activeColor) {

    &:hover{
        color: @hoverColor;
        border-color: @hoverColor;
        background: transparent;
    }
    &:active{
        color: @activeColor;
        border-color: @activeColor;
    }
    &[disabled]{
        background: @btn-disable-bg;
        color: @btn-disable-color;
        border-color: @btn-disable-border;
        &:hover, &:active{
            background: @btn-disable-bg;
            color: @btn-disable-color;
            border-color: @btn-disable-border;
        }
    }
}

// 左侧菜单项
.menu-item-fun(){
  .sec-title,
  .icon-title,
  .menu-item-a{
    &:hover{
      background: @menu-hover-bg;
      color: #4E627E;
    }
    &.on{
      color: @primary-color;
    }
  }
  .sec-title{
    padding: 9px 12px;
    color: @title-color;
    .submenu-title-icon{
      position: absolute;
      right: 12px;
      top: 50%;
      transform: translateY(-50%);
    }
      &.on{
          background: @menu-hover-bg;
      }
      &.showPop{
          background: @menu-hover-bg;
      }
  }
  .icon-title{
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: @title-color;
      &.showPop{
          background: @menu-hover-bg;
      }
  }
  .menu-item-a{
    padding: 9px 12px;
    color: @title-color;
    line-height: 22px;
    display: block;
    padding-right: 22px;
  }
}
