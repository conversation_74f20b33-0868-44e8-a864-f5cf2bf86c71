.ivu-modal{
    .ivu-btn-text{
        border-color: #dcdee2;
    }
    &-close{
        right: 24px;
        .ivu-icon-ios-close:hover{
            color: @primary-color;
        }
    }
    &-header{
        padding: 12px 24px;
    }
    &-body{
        padding: 16px 32px;
    }
}

// 图片预览弹窗
.preview-modal {
    .ivu-modal {
        top: 40px;
        height: ~'calc(100% - 80px)';

        .ivu-modal-content {
            height: 100%;

            .ivu-modal-header {
                padding: 14px 32px;
            }

            .ivu-modal-close {
                right: 16px;
            }

            .ivu-modal-body {
                height: ~'calc(100% - 52px)';
                padding: 16px 32px;
            }
        }
    }
    &.no-title{
        .ivu-modal-content .ivu-modal-body{
            height: 100%;
        }
    }
}

.fill-page-modal{
    .ivu-modal{
        top: 40px;
        height: ~'calc(100% - 80px)';
        &-content {
            height: 100%;
            .ivu-modal-header {
                padding: 14px 24px;
            }
            .ivu-modal-close {
                right: 24px;
            }
            .ivu-modal-body {
                height: ~'calc(100% - 52px - 57px)';
                padding: 16px 24px;
                overflow: auto;
            }
            .ivu-modal-footer{
                padding: 12px 24px;
            }
        }
    }
    &.no-title:not(.no-footer){
        .ivu-modal-content .ivu-modal-body{
            height: ~'calc(100% - 57px)';
        }
    }
    &.no-footer:not(.no-title){
        .ivu-modal-content .ivu-modal-body{
            height: ~'calc(100% - 52px)';
        }
    }
    &.no-footer.no-title{
        .ivu-modal-content .ivu-modal-body{
            height: 100%;
        }
    }
}
