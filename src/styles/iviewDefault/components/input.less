.ivu-input{
    &:hover{
        border-color: @input-hover-border-color;
    }
    &[disabled]{
        color: @input-disabled-color;
    }
    &[type='number']{
        &::-webkit-inner-spin-button{
            //-webkit-appearance: none;
        }
    }
    &-number{
        border-radius: 2px;
        &-input{
            border-radius: 2px;
        }
        &-handler-wrap{
            padding: 4px;
            width: 26px;
            border: none;
        }
        &-handler{
            height: 12px;
            background: #F2F3F5;
            &-down{
                border: none;
                top: 0;
            }
            &-up-inner, &-down-inner{
                color: @text-color;
                font-size: 12px;
                right: 3px;
            }
        }
    }
    &-group-prepend, &-group-append{
        border-radius: 2px;
    }
}

.ivu-select{
    &-selection:hover, &-selection-focused{
        border-color: @input-hover-border-color;
    }
}

// 输入框清除图标
.ivu-input-icon-clear,
.ivu-select-selection .ivu-icon-ios-close-circle{
    font-family: "iconfont" !important;
    color: #4E627E;
    font-size: 16px;
    &:before{
        content: "\e705";
    }
}
fieldset[disabled] .ivu-input{
    color: @input-disabled-color;
}
