// 按钮
.ivu-btn{
    padding: 0 16px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border-radius: 2px;
    img{
        margin-right: 4px;
        width: 12px;
        height: 12px;
    }
    & > .ivu-icon + span, & > span + .ivu-icon{
        margin-left: 0;
    }
    & > .ivu-icon,
    & > .iconfont{
        margin-right: 8px;
    }
    .btn-ghost-hover(@primary-hover-color; @primary-active-color);

    &-primary{
        .btn-fill-hover(@primary-color; @primary-hover-color; @primary-active-color; @primary-disabled-color);
    }
    &-info{
        .btn-fill-hover(@info-color; @info-hover-color; @info-active-color; @info-disabled-color);
    }
    &-success{
        .btn-fill-hover(@success-color; @success-hover-color; @success-active-color; @success-disabled-color);
    }
    &-warning{
        .btn-fill-hover(@warning-color; @warning-hover-color; @warning-active-color; @warning-disabled-color);
    }
    &-error{
        .btn-fill-hover(@error-color; @error-hover-color; @error-active-color; @error-disabled-color);
    }
    &-grey{
        .btn-fill-hover(@fill-2; @fill-3; @fill-3; @fill-1; @text-color; @text-2-1);
    }
    &-ghost&-primary{
        .btn-ghost-hover(@primary-hover-color; @primary-active-color);
    }
    &-ghost&-info{
        .btn-ghost-hover(@info-hover-color; @info-active-color);
    }
    &-ghost&-success{
        .btn-ghost-hover(@success-hover-color; @success-active-color);
    }
    &-ghost&-warning{
        .btn-ghost-hover(@warning-hover-color; @warning-active-color);
    }
    &-ghost&-error{
        .btn-ghost-hover(@error-hover-color; @error-active-color);
    }
    &-small{
        font-size: 12px;
    }
    .ivu-icon.ivu-icon-ios-search{
        font-family: "iconfont" !important;
        &:before{
            content: "\e6dd";
        }
    }
    .ivu-icon.ivu-icon-ios-refresh{
        font-family: "iconfont" !important;
        &:before{
            content: "\e6da";
        }
    }
    .ivu-icon.ivu-icon-md-add, .ivu-icon.ivu-icon-ios-add{
        font-family: "iconfont" !important;
        &:before{
            content: "\e70c";
        }
    }
    .ivu-icon.ivu-icon-ios-trash{
        font-family: "iconfont" !important;
        &:before{
            content: "\e61e";
        }
    }
}
