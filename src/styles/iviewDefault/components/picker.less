.ivu-picker{
    &-panel-icon-btn{
        color: #4E5969;
    }
}
.ivu-date-picker{
    &-header-label{
        color: @title-color;
    }

    &-cells{
        margin: 8px 2px;

        width: 248px;
        padding: 6px 12px;

        &-header span{
            color: #798799;
            width: 28px;
        }
        span.ivu-date-picker-cells-cell{
            width: 32px;
            height: 32px;
        }
        span em{
            border-radius: 50%;
            margin: 4px;
        }
        &-focused em{
            box-shadow: none;
        }

        &-cell:not(.ivu-date-picker-cells-cell-selected):hover em{
            background: #E5E6EB;
        }

    }

    &-cells-year, &-cells-month{
        padding: 0;
        width: 276px;
        span.ivu-date-picker-cells-cell{
            width: 64px;
            height: 28px;
            margin: 8px 14px;
            border-radius: 12px;
            em{
                width: 64px;
                border-radius: 12px;
                margin: 0;
            }
        }
    }

    &-with-range{
        .ivu-picker-panel-body {
            min-width: 504px;
        }

        .ivu-date-picker-cells-cell{
            &-selected{
                background: #E8F3FF;
                border-radius: 0px 32px 32px 0px;
                &.ivu-date-picker-cells-focused{
                    border-radius: 32px 0px 0px 32px;
                }
                &:hover em{
                    background: @primary-color;
                }
            }

            &-range{
                &:before{
                    background: #F3F7FB;
                    color: #C9CDD4;
                    top: 0px;
                    bottom: 0px;
                }
                &:hover em{
                    background: none;
                }
            }
        }
    }

    &-rel{
        .ivu-input-small{
            height: 28px;
        }
        .ivu-input-wrapper-small .ivu-input-prefix i, .ivu-input-wrapper-small .ivu-input-suffix i{
            line-height: 28px;
        }
    }

}

.ivu-picker-panel{
    &-content{
        .ivu-picker-panel-content{
            .ivu-time-picker-cells-list{
                width: 126px;
                ul li{
                    padding: 0 0 0 56px;
                }
            }
            .ivu-time-picker-cells-with-seconds{
                .ivu-time-picker-cells-list{
                    width: 84px;
                    ul li{
                        padding: 0 0 0 34px;
                    }
                }
            }
        }
    }
}


.ivu-color-picker-confirm{
    .ivu-btn{
        padding: 0 9px;
        margin-left: 0;
    }
}
