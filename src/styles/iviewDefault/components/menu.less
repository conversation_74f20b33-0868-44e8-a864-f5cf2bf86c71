// 左侧菜单
.menu-list{
    line-height: 22px;
    .ivu-menu-light.ivu-menu-vertical .ivu-menu-item:after,
    .ivu-menu-vertical.ivu-menu-light:after{
        display: none;
    }
    .ivu-menu-vertical{
        .ivu-menu-submenu .ivu-menu-item{
            color: #1E2A55;
        }
        .ivu-menu-submenu-title, .ivu-menu-item{
            padding: 9px 12px;
            margin: 2px 0;
            &:hover{
                background: @menu-hover-bg;
                color: #4E627E;
            }
        }
        .ivu-menu-submenu-title{
            color: #4E627E;
        }
        & > .ivu-menu-item{
            color: @text-color;
        }

        .ivu-menu-item-active:not(.ivu-menu-submenu){
            background: @menu-hover-bg;
            border-radius: 2px;
            color: @primary-color;
        }
        .ivu-menu-submenu-title-icon{
            right: 12px;
        }
        .ivu-menu-submenu-title > i, .ivu-menu-submenu-title span > i{
            margin-right: 8px;
        }
        .ivu-menu-submenu.on > .ivu-menu-submenu-title{
            color: @primary-color;
        }
    }
    .ivu-poptip, .ivu-poptip-rel{
        display: block;
    }
    .sec-menu{
        position: relative;
        cursor: pointer;
    }
    .menu-item-fun();
    .sec-title{
        padding-left: 43px;
        margin: 2px 0;
    }

}
.menu-popper{
    min-width: initial;
    .ivu-poptip, .ivu-poptip-rel{
        display: block;
    }
    .menu-item-fun();
    .sec-title{
        padding-right: 42px;
    }
    .menu-item-a{
        &:first-child{
            border-top-left-radius: 4px;
            border-top-right-radius: 4px;
        }
        &:last-child{
            border-bottom-left-radius: 4px;
            border-bottom-right-radius: 4px;
        }
    }
}
