@import "./defaultTheme";
@import "./mixins";
@import "components/button";
@import "components/input";
@import "components/form";
@import "components/menu";
@import "components/modal";
@import "components/picker";
@import "components/table";
@import "components/tabs";
@import "components/transfer";
@import "components/select";

h5.ivu-typography, div.ivu-typography-h5, div.ivu-typography-h5 textarea, .ivu-typography h5{
  font-weight: 600;
}
h6.ivu-typography{
    font-size: 14px;
    line-height: 24px;
    font-weight: 600;
    margin-bottom: 8px;
    color: @title-color;
}

.ivu-login{
  .ivu-btn{
    font-size: 14px;
    padding: 0;
  }
}

//面包屑
.ivu-breadcrumb{
    color: @text-color;
    font-size: 12px;
    a{
        &:hover, &:active{
            color: @primary-color;
            background: none;
        }
    }
    & > span:last-child{
        color: @title-color;
    }
}

.ivu-upload{
    &-drag{
        background: #F3F7FB;
        border: 1px dashed #E5E6EB;
        &:hover{
            border: 1px dashed #C9CDD4;
            background: #E5E6EB;
        }
    }
}

.ivu-slider{
    &-button-wrap{
        top: -6px;
    }
    &-disabled{
        .ivu-slider-wrap{
            background: @slider-disabled-bg;
        }
    }
}




