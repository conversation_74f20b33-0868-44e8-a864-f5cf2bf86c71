<template>
    <div class="rank-list" ref="rankR">
        <div v-for="(item, index) in list" class="box">
            <div class="tit-box">
                <div class="num">{{ index + 1 }}</div>
                <div class="name">
                    <TooltipAutoShow :content="item.name" />
                </div>
                <div class="value">{{ item.value }}{{ unit ? unit : ''}}</div>
            </div>
            <div class="progress">
                <div class="progress-hide" :style="{width: item.percent + '%'}">
                    <div class="progress-cover" :style="{width: size.w + 'px'}">
                        <span v-for="n in 15"></span>
                    </div>
                </div>
                <div class="progress-bg">
                    <span v-for="n in 15"></span>
                </div>
            </div>
        </div>
        <no-data v-show="list.length === 0" />
    </div>
</template>

<script>
export default {
    name: 'rankDashed',
    props: {
        list: { default() { return [] } },
        unit: { default: '次' }
    },
    data() {
        return {
            size: {
                w: 0
            }
        }
    },
    mounted() {
        this.$nextTick(() => {
            this.initSize()
        })
        window.addEventListener('resize', this.initSize)
    },
    beforeUnmount() {
        window.removeEventListener('resize', this.initSize)
    },
    methods: {
        initSize() {
            this.size.w = this.$refs.rankR.clientWidth
        }
    }
}
</script>

<style lang="less" scoped>
.no-data{
    padding-top: 80px;
}
.rank-list{
    .box{
        padding: 8px 0;
        .tit-box{
            display: flex;
            align-items: center;
            margin-bottom: 8px;
            font-size: 12px;
            .num{
                min-width: 16px;
                height: 16px;
                background: #EBEDF0;
                border-radius: 2px;
                color: @text-3-1;
                text-align: center;
                vertical-align: middle;
                line-height: 16px;
                margin-right: 8px;
            }
            .name{
                overflow: hidden;
                flex: 1;
                color: #4E5969;
                line-height: 20px;
                .ivu-tooltip{
                    height: 20px;
                    display: block;
                    /deep/.ivu-tooltip-rel{
                        line-height: 20px;
                    }
                }
            }
            .value{
                color: #4E5969;
                font-weight: 500;
                line-height: 20px;
            }
        }
        .progress{
            position: relative;
            .progress-cover,
            .progress-bg{
                display: flex;
                justify-content: space-between;
                span{
                    background: #F3F7FB;
                    border-radius: 1px;
                    height: 6px;
                    margin-right: 4px;
                    flex: 1;
                    &:last-child{
                        margin-right: 0;
                    }
                }
            }
            .progress-hide{
                position: absolute;
                height: 6px;
                top: 0;
                left: 0;
                overflow: hidden;
            }
            .progress-cover{
                position: absolute;
                width: 100%;
                top: 0;
                left: 0;
                span{
                    background: #21CCFF;
                }
            }
        }
        &:nth-child(1){
            .tit-box .num{
                background: #FDA979;
                box-shadow: inset 0px 2px 3px rgba(255, 255, 255, 0.5);
                color: #fff;
            }
            .progress .progress-cover span{
                background: #FDA979;
            }
        }
        &:nth-child(2){
            .tit-box .num{
                background: #FFC876;
                box-shadow: inset 0px 2px 3px rgba(255, 255, 255, 0.5);
                color: #fff;
            }
            .progress .progress-cover span{
                background: #FFC876;
            }
        }
        &:nth-child(3){
            .tit-box .num{
                background: #FDE079;
                box-shadow: inset 0px 2px 3px rgba(255, 255, 255, 0.5);
                color: #fff;
            }
            .progress .progress-cover span{
                background: #FDE079;
            }
        }
    }
}
</style>
