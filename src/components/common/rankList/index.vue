<template>
    <div class="rank-list">
        <div v-for="(item, index) in list" class="box">
            <div class="tit-box">
                <div class="num">{{ index + 1 }}</div>
                <div class="name">
                    <TooltipAutoShow :content="item.name" />
                </div>
                <div class="value">{{ item.value }}{{ unit }}</div>
            </div>
            <Progress :percent="item.percent" stroke-color="#4086FF" :stroke-width="8" hide-info />
        </div>
        <no-data v-show="list.length === 0" />
    </div>
</template>

<script>
export default {
    name: 'index',
    props: {
        list: { default() { return [] } },
        unit: { default: '' }
    },
    data() {
        return {}
    },
    methods: {}
}
</script>

<style lang="less" scoped>
.rank-list{
    .box{
        padding: 8px 0;
        .tit-box{
            display: flex;
            align-items: center;
            margin-bottom: 8px;
            font-size: 12px;
            .num{
                min-width: 16px;
                height: 16px;
                background: #EBEDF0;
                border-radius: 2px;
                color: @text-3-1;
                text-align: center;
                vertical-align: middle;
                line-height: 16px;
                margin-right: 8px;
            }
            .name{
                overflow: hidden;
                flex: 1;
                color: #4E5969;
                line-height: 20px;
                .ivu-tooltip{
                    height: 20px;
                    display: block;
                    /deep/.ivu-tooltip-rel{
                        line-height: 20px;
                    }
                }
            }
            .value{
                color: #4E5969;
                font-weight: 500;
                line-height: 20px;
            }
        }
        .ivu-progress, /deep/.ivu-progress-outer{
            display: block;
        }
        /deep/.ivu-progress-inner{
            display: block;
            background: none;
        }
        &:nth-child(1){
            .tit-box .num{
                background: #FDA979;
                box-shadow: inset 0px 2px 3px rgba(255, 255, 255, 0.5);
                color: #fff;
            }
        }
        &:nth-child(2){
            .tit-box .num{
                background: #FFC876;
                box-shadow: inset 0px 2px 3px rgba(255, 255, 255, 0.5);
                color: #fff;
            }
        }
        &:nth-child(3){
            .tit-box .num{
                background: #FDE079;
                box-shadow: inset 0px 2px 3px rgba(255, 255, 255, 0.5);
                color: #fff;
            }
        }
    }
}
</style>
