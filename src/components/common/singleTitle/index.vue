<template>
    <div class="title-card" :style="props.styles">
        <slot name="title">
            <div v-if="props.src" class="title-icon-img">
                <img :src="props.src" alt="" style="width: 100%;height: 100%;">
            </div>
            <Title :level="5">{{ props.title }}</Title>
        </slot>
    </div>
</template>

<script lang="ts" setup>
import { defineProps } from 'vue';
const props = defineProps(['title', 'src', 'styles'])

</script>

<style lang="less" scoped>
.title-card{
    display: flex;
    align-items: center;
    margin-bottom: 16px;
    padding-bottom: 8px;
    .title-icon{
        background: #6AA1FF;
        border-radius: 2px;
        width: 24px;
        height: 24px;
        margin-right: 8px;
        color: #fff;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    .title-icon-img{
        width: 24px;
        height: 24px;
        margin-right: 8px;
    }
    h5{
        margin-bottom: 0;
    }
    &.no-title{
        margin-bottom: 0;
    }
}
</style>
