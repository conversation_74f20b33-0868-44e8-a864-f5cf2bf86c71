<script lang="ts" setup>
import { defineProps, defineEmits, computed } from "vue";
const props = defineProps({
    isShow: {
        default: false,
    },
    option: {
        type: Object,
        default: () => {
            return {
                width: 720,
            };
        },
    },
    showBtn: {
        default: true,
    },
    loading:{
        default:false
    }
});
const emit: any = defineEmits(['on-confirm','on-cancel']);
function handelConfirm() {
    emit("on-confirm");
}
function handelCancel() {
    emit("on-cancel");
}
const isShowFooter = computed(() => {
    return props.showBtn ? "" : "modal-footer-none";
});
</script>
<template>
    <Modal
        :class-name="isShowFooter"
        v-bind="{ ...props.option }"
        :mask-closable="false"
        v-model="props.isShow"
        @on-cancel="handelCancel"
    >
        <slot  v-if="props.isShow"></slot>
        <template #footer>
            <Button @click="handelCancel">取消</Button>
            <Button type="primary" :loading="loading" @click="handelConfirm">确定</Button>
        </template>
    </Modal>
</template>

<style lang="less" scoped>
.ivu-modal-footer {
    .ivu-btn+.ivu-btn {
        margin-left: 8px;
    }
}
</style>
