<template>
    <eModal :option="{
        title: '批量导出',
        footerHide: true,
        width: 408,
    }" :is-show="exportLoading" @on-cancel="handleCancel">
        <div class="download">
            <Circle :percent="progress" :stroke-color="(progress >= 100 && '#5cb85c') || '#2db7f5'" size="80">
                <Icon v-if="progress == 101" type="ios-checkmark" size="60" style="color: #5cb85c"></Icon>
                <span v-if="progress <= 100" class="progress-num">{{ progress }}%</span>
            </Circle>
            <div class="tips">数据导出中，请勿关闭窗口</div>
        </div>
    </eModal>
</template>

<script>
import eModal from './index'
import { queryBatchExportPatrolReportUrl } from '@/api/safeManage/parkPatrolService';
export default {
    name: 'exportModal',
    components: {
        eModal
    },
    data() {
        return {
            exportLoading: false,
            progress: 0
        }
    },
    methods: {
        export(url, data) {
            if (this.exportLoading) {
                return
            }
            this.progress = 0
            this.exportLoading = true
            this.$request(url, data, 'post').then(res => {
                if (res.success) {
                    this.getExportResult(res.data)
                }
            })
        },
        async getExportResult(key) {
            const exportRes = await queryBatchExportPatrolReportUrl(key)
            const { success, data } = exportRes
            if (success && data.done == data.total) {
                this.progress = 100
                setTimeout(() => {
                    window.open(data.data);
                    this.exportLoading = false;
                }, 1000)
            } else {
                setTimeout(() => this.getExportResult(key), 1000)
                this.progress = Math.ceil((data.done / data.total) * 100);
            }
        },
        handleCancel() {}
    }
}
</script>

<style lang="less" scoped>
.download {
    text-align: center;
    padding: 28px 0;

    .tips {
        color: #798799;
        font-size: 14px;
        margin-top: 16px;
    }
}
</style>
