<template>
<div class="img-wrapper" @click="onOpenModal" :style="wrapperStyle">
    <img v-if="url" :src="url" v-defaultImg:cover :width="width" :height="height">
    <div class="img-mask">
        <Icon type="ios-eye-outline"></Icon>
    </div>
</div>
<!--点击放大弹窗-->
<NModal :is-show="isShowModal"
        v-if="isShowModal"
        @on-confirm="onConfirm"
        @on-cancel="onCloseModal"
        :option="{title: fileName || '图片预览', footerHide: true, width: 80, className: 'preview-modal'}">
    <div class="img-big-wrapper">
        <img class="modal-img" v-if="url" :src="url">
    </div>
</NModal>
</template>

<script setup lang="ts">
import { computed, defineProps, ref } from 'vue'
import NModal from './index.vue'

type Props = {
    url: string | undefined,
    width: string | number,
    height: string | number,
    styles: Object,
    imgName: string,
}
const props = withDefaults(defineProps<Props>(), {
    width: 64,
    height: 64
})
const emits = defineEmits(['open'])
const url = computed(() => props.url)
const width = computed(() => {
    return props.width ? props.width.toString().replace('px', '') : ''
})
const height = computed(() => {
    return props.height ? props.height.toString().replace('px', '') : ''
})

const wrapperStyle = computed(() => {
    return {
        width: width.value + 'px',
        height: height.value + 'px',
        ...props.styles
    }
})

const fileName = computed(() => {
    const url = props.url;
    if(props.imgName) return props.imgName;
    return url?.substring(url?.lastIndexOf('/') + 1)
})


const isShowModal = ref(false)

const onConfirm = () => {
    isShowModal.value = true;
}
const onCloseModal = () => {
    isShowModal.value = false;
}
const onOpenModal = () => {
    isShowModal.value = true
    emits('open')
}
</script>

<style lang="less" scoped>

.img-wrapper{
    cursor: pointer;
    position: relative;
    img{
        vertical-align: middle;
    }
}

.img-big-wrapper{
    width: 100%;
    height: 100%;
    text-align: center;
    .modal-img{
        object-fit: contain;
        width: 100%;
        height: 100%;
    }
}///
.img-mask{
    display: none;
    position: absolute;
    left: 0;
    top: 0;
    background: rgba(0,0,0,.6);
    width: 100%;
    height: 100%;
    align-items: center;
    justify-content: center;
}
.img-mask i{
    color: #fff;
    font-size: 20px;
    margin: 0 2px;
}
.img-wrapper:hover {
    .img-mask{
        display: flex;
    }
}
</style>
