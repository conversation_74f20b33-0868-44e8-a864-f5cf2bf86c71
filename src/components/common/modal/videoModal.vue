<template>
    <NModal :is-show="isShowModal" @on-confirm="onConfirm" @on-cancel="onCancel" :option="{footerHide: true, width: 720 }" :title="fileName || '视频'">
        <div class="wrapper">
            <video controls class="video-content" autoplay>
                <source :src="props.url" type="video/mp4">
            </video>
        </div>
    </NModal>
</template>

<script lang="ts" setup>
import NModal from './index.vue';
import { defineProps, defineEmits, computed } from 'vue';
import { useVModel } from '@/hooks/useVModel';
// https://www.w3school.com.cn/example/html5/mov_bbb.mp4
const props = defineProps<{modelValue: string, url: string, title: string}>()
const emits = defineEmits(['update:modelValue'])

const isShowModal = useVModel(props, 'modelValue', emits);

const fileName = computed(() => {
    const url = props.url;
    return url?.substring(url?.lastIndexOf('/') + 1)
})
const onConfirm = () => {
    isShowModal.value = false
}
const onCancel = () => {
    isShowModal.value = false
}

</script>

<style lang="less" scoped>
.wrapper{
    display: flex;
    align-items: center;
    height: 400px;
}
.video-content{
    width: 100%;
}
</style>
