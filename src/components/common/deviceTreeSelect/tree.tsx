import { Tree, Input } from "view-ui-plus";
import { defineComponent, ref, watch, onMounted } from "vue";
import { commonService } from "@/api/commonService";
import { treeParentChildLinkage, trace, treeToList, getParentItems } from "@/utils/tool";
import useDebounce from "@/hooks/useDebounce";
import { bfs, isEmpty } from "wei-util";
import "../mapAreaTreeSelect/index.less";
export default defineComponent({
    name: "areaTreeSelect",
    props: {
        checked: {
            type: Array,
            default: () => []
        },
        methodUrl: {
            type: String,
            default: "manholeAreaTree",
        },
        nodeKey: {
            type: String,
            default: "value",
        },
        nodeId: {
            type: String,
            default: "value",
        },
        splitKey: {
            type: String,
            default: "@",
        },
        searchKey: {
            type: String,
            default: "title",
        },
        methodType: {
            type: String,
            default: "get",
        },
        checkFun: {
            type: Function,
            default: (d: any) => {
                return d.deviceCode;
            },
        },
        checkStrictly: {
            default: true
        }
    },
    emits: ["updateAreaPath"],
    setup(props, ctx) {
        // 搜索树
        const searchTreeText = ref<string>("");
        // 显示的区域树
        const areaTreeList = ref<any>([]);
        // 查询区域树
        const getManholeAreaTree = async () => {
            const res = await commonService.getDeviceAndAreaTree(
                props.methodUrl,
                {},
                props.methodType
            );
            const { data, success }: { data: any; success: boolean } =
                res as unknown as HttpResponse<any>;
            if (success) {
                const areaPaths: string[] = [];
                trace(data, (d: any) => {
                    areaPaths.push(d[props.nodeKey] as string);
                    d.title = d.label || d.name;
                    d.key = d.code || `${d.id}`;
                    d.checked = false;
                    d.expand = true;
                });
                areaTreeList.value = deviceAddTree(data);
                updateAreaPath();
            }
        };
        // 将设备加入到区域树中
        const deviceAddTree = (data: any) => {
            data.forEach((item: any) => {
                if (item.children) {
                    item.children = deviceAddTree(item.children);
                }
                if (item.deviceList && item.deviceList.length > 0) {
                    item.deviceList.forEach((it: any) => {
                        it.title = it.sbmc + `(${it.deviceCode})`;
                        it.value = it.deviceCode;
                    });
                    if (!item.children) {
                        item.children = [];
                    }
                    item.children = item.children.concat(item.deviceList);
                }
            });
            return data;
        };
        // 勾选区域树
        const handleChangeCheck = (checkedKeys: any[], selectKey: any) => {
            if (!props.checkStrictly) {
                const _nodeIds = checkedKeys.map((d) => d[props.nodeId] as string)
                ctx.emit("updateAreaPath", _nodeIds, _nodeIds);
                return
            }
            treeParentChildLinkage(checkedKeys, selectKey, areaTreeList.value, props.nodeKey);
            updateAreaPath(false,selectKey);
        };
        //更新当前勾选的区域路径
        const updateAreaPath = (isShow = false,selectKey?:any) => { // isSHow为true只通知父组件回显，不绑定
            const areaPaths: string[] = [];
            trace(areaTreeList.value, (d: any) => {
                if (d.checked && props.checkFun(d)) areaPaths.push(d[props.nodeId] as string);
            });
            const valueArr: any = [];
            bfs(areaTreeList.value, "children", (node: any) => {
                if (node.checked && isEmpty(node.children)) {
                    valueArr.push(node[props.nodeId]);
                }
            });
            ctx.emit("updateAreaPath", areaPaths, valueArr, isShow,selectKey);
            // 查询当前区域的设施
        };
        watch(() => props.checked, (newVal: any[], oldVal: any[]) => {
            
            if (!isEmpty(newVal)) {
                const checkedItems: any[] = []
                newVal.forEach((item: any) => {
                    const parentItems = getParentItems(
                        areaTreeList.value,
                        item,
                        props.nodeKey
                    );
                    checkedItems.push(...parentItems)
                })
                treeParentChildLinkage(checkedItems, checkedItems[checkedItems.length - 1], areaTreeList.value, props.nodeKey);
                updateAreaPath(true)
            }


            // if (newVal.length > 0) {
            //     trace(areaTreeList.value, (d: any) => {
            //         const checkedToString = newVal.map((item: any) => String(item));
            //         if (checkedToString.includes(String(d[props.nodeId]))) {
            //             treeParentChildLinkage([d], d, areaTreeList.value, props.nodeKey);
            //         }
            //     });
            //     updateAreaPath(true)
            // } 
        }, { deep: true })
        // 取消
        const handleCancel = () => {
            searchTreeText.value = "";
            handleChangeCheck([], {});
        };
        // 搜索
        const handleChangeSearch = () => {
            const text = searchTreeText.value.toLocaleLowerCase();
            useDebounce(() => {
                const treeList = treeToList(areaTreeList.value);
                const searchTreelist = treeList
                    .filter((k) => k.title.toLocaleLowerCase().includes(text))
                    .map((k) => k[props.nodeKey].split(props.splitKey))
                    .flat();
                trace(areaTreeList.value, (d: any) => {
                    if (searchTreelist.includes(d[props.searchKey])) {
                        d.expand = true;
                    } else {
                        d.expand = false;
                    }
                });
            });
        };
        const preg_quote = (str: string) => {
            return (str + "").replace(/([\\\.\+\*\?\[\^\]\$\(\)\{\}\=\!\<\>\|\:])/g, "\\$1");
        };
        // 是否显示按钮
        const hideBtn = ref<boolean>(false);
        onMounted(() => {
            getManholeAreaTree();
        });
        const resetCheck = () => {
            bfs(areaTreeList.value, "children", (item: any) => {
                item.checked = false;
            });
            ctx.emit("updateAreaPath", [], []);
        };
        ctx.expose({
            resetCheck,
        });
        return () => (
            <>
                <div class="search-box">
                    <Input
                        suffix="ios-search"
                        placeholder="请输入区域"
                        clearable={!hideBtn.value}
                        v-model={searchTreeText.value}
                        onOnFocus={() => (hideBtn.value = true)}
                        onOnBlur={() => (hideBtn.value = false)}
                        onOnChange={handleChangeSearch}
                    />
                    <div class="cancel" onClick={handleCancel} v-show={!hideBtn.value}>
                        取消
                    </div>
                </div>
                <div class="scorll-map-cont map-tree-box">
                    <Tree
                        data={areaTreeList.value}
                        render={(h: any, { data }: { data: any }) => {
                            if (
                                searchTreeText.value &&
                                (data.title as string)
                                    .toLocaleLowerCase()
                                    .includes(searchTreeText.value.toLocaleLowerCase())
                            ) {
                                return (
                                    <tooltip-auto-show>
                                        <span
                                            v-html={(data.title as string).replace(
                                                new RegExp(
                                                    "(" + preg_quote(searchTreeText.value) + ")",
                                                    "gi"
                                                ),
                                                "<span class='text-blue'>$1</span>"
                                            )}
                                        ></span>
                                    </tooltip-auto-show>
                                );
                            }
                            return <tooltip-auto-show>{data.title}</tooltip-auto-show>;
                        }}
                        check-strictly={props.checkStrictly}
                        // check-strictly
                        show-checkbox
                        // @ts-ignore
                        onOnCheckChange={handleChangeCheck}
                    />
                </div>
            </>
        );
    },
});
