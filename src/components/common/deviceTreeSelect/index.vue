<template>
    <Poptip v-model="showPop" placement="bottom-start" popper-class="area-sel-pop">
        <div
            class="area-select ivu-select-selection"
            :style="selValueStyle"
            :class="[hasMouseHoverHead || showPop ? 'showPop' : '', showBorder ? '' : 'showBorder']"
            @mouseenter="hasMouseHoverHead = true"
            @mouseleave="hasMouseHoverHead = false"
        >
            <div class="sel-value" v-show="currentValue.length === 0">全部{{ title }}</div>
            <div class="sel-value" v-show="currentValue.length > 0">
                已选{{ currentValue.length }}个{{ title }}
            </div>
            <Icon
                type="ios-arrow-down"
                class="ivu-select-arrow"
                v-if="!currentValue.length || !hasMouseHoverHead"
            />
            <Icon
                custom="iconfont icon-close-circle"
                @click.stop="onClear"
                class="ivu-select-arrow"
                v-if="hasMouseHoverHead && currentValue.length && clearable"
            />
        </div>
        <template #content>
            <map-area-tree-select
                :checked="checked"
                ref="tree-select"
                :method-url="methodUrl"
                :node-key="nodeKey"
                :node-id="nodeId"
                :split-key="splitKey"
                :search-key="searchKey"
                @updateAreaPath="updateAreaPath"
                :check-fun="checkFun"
                :method-type="methodType"
                :check-strictly="checkStrictly"
            />
        </template>
    </Poptip>
</template>

<script>
import mapAreaTreeSelect from "./tree.tsx";
export default {
    name: "DeviceTreeSelect",
    components: {
        mapAreaTreeSelect,
    },
    props: {
        checked: {
            type: Array,
            default: () => [],
        },
        showBorder: { default: true },
        origin: { default: "" },
        clearable: { default: true },
        title: { default: "路灯" },
        methodUrl: {
            type: String,
            default: "/streetlight/getTreeIncludeDevice",
        },
        nodeKey: {
            type: String,
            default: "value",
        },
        nodeId: {
            type: String,
            default: "value",
        },
        splitKey: {
            type: String,
            default: "@",
        },
        searchKey: {
            type: String,
            default: "title",
        },
        checkFun: {
            type: Function,
            default: (d) => {
                return d.deviceCode;
            },
        },
        methodType: {
            type: String,
            default: "get",
        },
        checkStrictly: {
            default: true,
        },
    },
    emits: ["on-change"],
    data() {
        return {
            showPop: false,
            hasMouseHoverHead: false,
            currentValue: [],
        };
    },
    computed: {
        hoverBackGround() {
            return this.showBorder ? "" : "#F8FAFB";
        },
        selValueStyle() {
            return this.showBorder
                ? {}
                : { "font-weight": "600", "font-size": "16px", color: "#1E2A55", border: "none" };
        },
    },
    methods: {
        onClear() {
            this.currentValue = [];
            this.$refs["tree-select"].resetCheck();
        },
        updateAreaPath(val, valueArr, isShow,selectKey) {
            if (isShow) {
                if (this.origin) {
                    this.currentValue = valueArr;
                } else {
                    this.currentValue = val;
                }
                return;
            }
            if (this.origin) {
                this.currentValue = valueArr;
            } else {
                this.currentValue = val;
            }
            this.$emit("on-change", val, valueArr,selectKey);
        },
    },
};
</script>

<style lang="less" scoped>
.ivu-poptip {
    width: 100%;
    display: block;
    position: relative;

    /deep/.ivu-poptip-rel {
        width: 100%;
        display: block;
    }

    /deep/.area-sel-pop {
        max-width: 400px;
        padding-top: 5px;

        .ivu-poptip-arrow {
            display: none;
        }

        .ivu-poptip-body {
            max-height: 240px;
            overflow: auto;
        }
    }
}

.area-select {
    height: 32px;
    // border: none;
    border: 1px solid #e0e6f1;

    &:hover {
        background-color: #fff;
        border: 1px solid #165dff;
    }
    &.showBorder {
        &:hover {
            background-color: #f8fafb;
        }
    }
    .sel-value,
    .placeholder {
        position: relative;
        display: block;
        font-size: 14px;
        overflow: hidden;
        height: 30px;
        line-height: 30px;
        text-overflow: ellipsis;
        white-space: nowrap;
        padding-left: 8px;
        padding-right: 24px;
        // font-weight: 600;
        font-size: 14px;
        color: #1e2a55;
    }

    .placeholder {
        color: @text-3-1;
    }

    .icon-close-circle {
        color: @text-color;
        font-size: 16px;
    }

    &.showPop {
        .ivu-select-arrow {
            transform: translateY(-50%) rotate(180deg);
        }
    }
}
</style>
