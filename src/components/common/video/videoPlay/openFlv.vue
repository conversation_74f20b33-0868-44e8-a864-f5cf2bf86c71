<template>
        <div :style="styles" class="live-main">
        <div :id="mainId" ref="parentId" :style="styles" class="video-open-flv"></div>
    </div>

</template>

<script>
export default {
    name: 'openFlv',
    props: {
        styles: {
            type: [Object],
        },
        domId: {
            type: [String],
            default: 'flvDom'
        },
        mainId: {
            type: [String],
            default: 'flvMain'
        }
    },
    data() {
        return {
            hlsPlayer: '',
            videoUrl: '',
            containerDom:null
        }
    },
    mounted() {
    },
    beforeDestroy() {

        this.clear()
    },
    methods: {
        getVideoDom(){
           return this.containerDom ? this.containerDom.children[0] : null
        },
        // 传播放地址
        open(url) {
            if (!url) {
                return Promise.reject()
            }
            this.videoUrl = url
            // 使用Jessibuca播放器
            if (!this.hlsPlayer) {
                console.log('init hlsPlayer')
                let domEL = document.createElement('div')
                domEL.setAttribute('id', this.domId)
                domEL.className = 'video-open-flv-box'
                domEL.style = "width: 100%; height: 100%";
                document.getElementById(this.mainId).appendChild(domEL)
                this.containerDom = domEL
                this.hlsPlayer = new JessibucaPro(
                    {
                        container: domEL,
                        videoBuffer: 0.3, // 缓存时长
                        decoder: '/static/jessibuca-pro/decoder-pro.js',
                        isResize: false,
                        setScaleMode: 1,
                        controlAutoHide: true,
                        text: '',
                        // 视频加载转圈时的提示文字
                        loadingText: '加载中',
                        // 是否有音频，如果设置false，则不对音频数据解码，提升性能。
                        // hasAudio: false,
                        //  是否开启控制台调试打印
                        debug: false,
                        // debugLevel: 'debug',
                        isFlv: false,
                        // 是否显示网速
                        showBandwidth: true,
                        // heartTimeoutReplayTimes: -1,
                        // fullscreen 是否显示全屏按钮
                        // screenshot 是否显示截图按钮
                        // play 是否显示播放暂停按钮
                        // audio 是否显示声音按钮
                        // record 是否显示录制按钮
                        operateBtns: {
                            fullscreen: true,
                            screenshot: false,
                            play: true,
                            audio: true
                        },
                        // vod: this.vod,
                        // 是否不使用离屏模式（提升渲染能力）
                        // forceNoOffscreen: this.forceNoOffscreen,
                        // 是否开启声音，默认是关闭声音播放的。
                        isNotMute: false,
                        // wcsUseVideoRender: false,
                        useMSE: true,
                        useSIMD: false,
                        useWCS: true,
                        autoWasm: true,
                        supportHls265: true,
                        showPerformance: false, // 显示性能
                        timeout: 10000,
                        heartTimeoutReplayUseLastFrameShow: true,
                        audioEngine: 'worklet',
                        qualityConfig: ['普清', '高清', '超清', '4K', '8K'],
                        forceNoOffscreen: true,
                        heartTimeout: 10,
                        ptzClickType: 'mouseDownAndUp',
                        ptzZoomShow: true,
                        ptzMoreArrowShow: true,
                        ptzApertureShow: true,
                        ptzFocusShow: true,
                        // useCanvasRender: false,
                        // useWebGPU: true,
                        // demuxUseWorker: true
                        // controlHtml: '<div>我是 <span style="color: red">test</span>文案</div>'
                    }
                )
                this.initEvent()
            }
            return this.hlsPlayer.play(url)
        },
        initEvent() {
            this.hlsPlayer.on('error', res => {
                console.log('error', res)
                this.$emit('on-error')
            })
            this.hlsPlayer.on('play', res => {
                console.log('play on', res)
                this.$emit('on-error', true)
            })
            // let arr = ['loadingTimeoutRetryEnd', 'delayTimeoutRetryEnd', 'pause', 'crashLog', 'blur', 'websocketClose']
            // for(let i in arr) {
            //   this.hlsPlayer.on(arr[i], res => {
            //     console.log(arr[i] + ' on', res)
            //   })
            // }
            this.hlsPlayer.on('playFailedAndPaused', (error) => {
                console.log('playFailedAndPaused on', error)
                this.$emit('on-error')
            })


            this.hlsPlayer.on('timeout', (error) => {
                console.log('timeout on', error)
                this.$emit('on-error')
            })
        },
        clear() {
            console.log('clear open flv')
            if (this.hlsPlayer) {
                this.hlsPlayer.destroy()
                this.hlsPlayer = null
            }
            if (document.getElementById(this.mainId)) {
                document.getElementById(this.mainId).innerHTML = ''
            }
        }
    }
}
</script>
<style lang="less" scoped>
.video-open-flv{
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;
}
.video-open-flv-box{
    width: 100%;
    height: 100%;
}
.video-div {
    display: inline-block;
    width: auto;
    height: auto;
    object-fit: fill;

    /deep/ video {
        height: 100% !important;
    }

    /deep/ .jessibuca-container .jessibuca-controls {
        // background-color: none !important;
        height: 20px !important;
        display: none;
    }

    /deep/ .jessibuca-container:hover {
        // background-color: none !important;
        .jessibuca-controls {
            display: block;
        }
    }
}
.live-main{
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;


}

</style>
