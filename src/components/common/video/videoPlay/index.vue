<template>
    <div class="video-play" :style="styleVideo">
        <template v-if="videoType === 'ysy'">
            <ysy-model
                :mainId="`ysyMain${index}`"
                :domId="`ysyDom${index}`"
                :ref="'ysy' + index"
                :styles="{
                    width: styleVideo.width,
                    height: styleVideo.height,
                    position: 'relative',
                }"
                :controls="controls"
            />
        </template>
        <template v-if="videoType === 'flv'">
            <openFlv
                :ref="`flv${index}`"
                :mainId="`flvMain${index}`"
                :domId="`flvDom${index}`"
                @on-error="playError"
            />
        </template>
        <div class="tip-box" v-if="isShowPlay">
            <div class="err" v-if="errorMsg">{{ errorMsg }}</div>
            <div class="play" @click="replay">
                <Icon type="md-play" />
            </div>
        </div>

        <div class="my-loading" v-if="loading">
            <Icon type="ios-loading" size="18" class="spin-icon-load"></Icon>
            <div>加载中...</div>
        </div>
    </div>
</template>

<script>
import openFlv from "./openFlv.vue";
import YsyModel from "./YsyModel.vue";
import * as api from "@/api/videoService";
import { nextTick } from "vue";
export default {
    name: "videoPlayT",
    components: {
        openFlv,
        YsyModel,
    },
    props: {
        videoObj: {
            default() {
                return {};
            },
        },
        width: { default: "300px", type: [String, Number] },
        height: { default: "300px", type: [String, Number] },
        index: { type: [String, Number], default: 0 },
        controls: { default: true },
    },
    data() {
        return {
            curVideoObj: {},
            videoType: "", // 视频播放方式
            loading: false,
            isShowPlay: false,
            errorMsg: "",
            playObj: {}, // 接口请求的播放数据
        };
    },
    computed: {
        styleVideo() {
            let obj = {};
            if (typeof this.width === "number") {
                obj.width = this.width + "px";
            } else {
                obj.width = this.width;
            }
            if (typeof this.height === "number") {
                obj.height = this.height + "px";
            } else {
                obj.height = this.height;
            }
            return obj;
        },
    },
    beforeDestroy() {
        console.log("beforeDestroy clear");
        this.clear();
    },
    methods: {
        async open(data) {
            if (data) {
                this.curVideoObj = data;
            }
            this.errorMsg = "";
            this.loading = true;
            this.getVideoType();
            nextTick(() => {
                // this.curVideoObj.deviceCode = '42070300001316000001'
                if (this.videoType === "flv") {
                    this.initFlv();
                } else if (this.videoType === "ysy") {
                    this.initYsy();
                }
            });
        },
        getVideoDom(){
           return this.$refs["flv" + this.index].getVideoDom()
        },
        //flv
        async initFlv() {
            // const url = 'https://223.76.215.30:444/rtp/42010300521180000042_42010300521320042001.live.flv'
            // this.$refs["flv" + this.index].open(url);
            // return
            let res = await api.startPlay(this.curVideoObj.deviceCode);
            if (res.success) {
                this.playObj = res.data;
                let key = res.data.wvp_stream_prefix || "flv";
                let url = res.data[key];
                this.$nextTick(() => {
                    this.loading = false;
                    this.$refs["flv" + this.index].open(url);
                });
            } else {
                this.loading = false;
                this.isShowPlay = true;
                this.errorMsg = res.message;
            }
        },
        //ysy
        async initYsy() {
            this.loading = false;
            this.$refs["ysy" + this.index].init({
                accessToken: this.curVideoObj.ezopenObj.accessToken,
                id: this.curVideoObj.deviceCode,
                url: this.curVideoObj.url,
                width: this.styleVideo.width,
                height: this.styleVideo.height,
                autoplay: true,
                controls: this.controls,
            });
        },
        fullScreen() {
            let element = document.getElementById("fullVideoEl_" + this.index);
            if (element.requestFullscreen) {
                element.requestFullscreen();
            } else if (element.mozRequestFullScreen) {
                element.mozRequestFullScreen();
            } else if (element.msRequestFullscreen) {
                element.msRequestFullscreen();
            } else if (element.webkitRequestFullscreen) {
                element.webkitRequestFullScreen();
            }
            if (this.videoType === "ezopen") {
                this.$refs["ysy" + this.index].full();
            }
            element.addEventListener("fullscreenchange", (e) => {
                if (!this.isFullFun()) {
                    this.$refs.controlsR.setFullFlag(false);
                    this.v.isFull = false;
                }
            });
            this.v.isFull = true;
        },
        // 重新播放
        replay() {
            this.isShowPlay = false;
            this.open();
        },
        // 播放失败，显示按钮 flag: true-播放成功
        playError(flag) {
            if (flag) {
                this.isShowPlay = false;
            } else {
                this.errorMsg = "";
                this.isShowPlay = true;
            }
            this.loading = false;
        },
        clear() {
            if (this.videoType === "flv") {
                this.$refs["flv" + this.index].clear();
                api.stopPlay(this.curVideoObj.deviceCode);
            } else if (this.videoType === "ysy") {
                return this.$refs["ysy" + this.index].clear().then(() => {
                    this.$refs["ysy" + this.index].clearHtml();
                });
            }
        },
        getVideoType() {
            if (this.curVideoObj.propName === "ezopen") {
                this.videoType = "ysy";
            } else {
                this.videoType = "flv";
            }
        },
        clearHtml() {
            try {
                this.$refs["ysy" + this.index].clearHtml();
            } catch (e) {}
        },
    },
};
</script>

<style lang="less" scoped>
.video-play {
    position: relative;
    background: #000;
    .tip-box {
        position: absolute;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        width: 100%;
        height: 100%;
        top: 0;
        left: 0;
        z-index: 5;
        background: rgba(0, 0, 0, 0.3);
        color: #fff;
        .err {
            margin-bottom: 10px;
        }
        .play {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            z-index: 10;
            width: 48px;
            height: 48px;
            border-radius: 50%;
            background: #fff;
            font-size: 24px;
            cursor: pointer;
            padding-left: 5px;
            color: #333;
        }
    }

    .my-loading {
        position: absolute;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        z-index: 10;
        color: #fff;
        .spin-icon-load {
            animation: ani-demo-spin 1s linear infinite;
        }
    }
}
@keyframes ani-demo-spin {
    from {
        transform: rotate(0deg);
    }
    50% {
        transform: rotate(180deg);
    }
    to {
        transform: rotate(360deg);
    }
}
</style>
