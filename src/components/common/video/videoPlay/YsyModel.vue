<template>
    <data class="ysy-video" :id="mainId" :style="styles"></data>
</template>
<script>
import { nextTick } from "process";

// import EZUIKit from 'ezuikit-js';
export default {
    name: "YsyModel",
    props: {
        styles: {
            type: [Object],
            default: () => {
                return { width: "200px", height: "200px", position: "relative" };
            },
        },
        mainId: {
            type: [String],
            default: "",
        },
        domId: {
            type: [String],
            default: "",
        },
        controls: { default: true },
        autoplay: { default: false },
    },
    data() {
        return {
            videoObj: {},
            fullScreen: false,
            ezuiIns: null,
            isFullScreen: false,
        };
    },
    watch: {
        isFullScreen(newV) {
            if (!newV) this.reSize();
        },
        styles(newV) {
            if (newV) {
                this.reSize();
            }
        },
    },

    deactivated() {},
    beforeDestroy() {
        this.clear();
    },
    methods: {
        async init(data) {
            let autoplay = this.autoplay ? 1 : 0;
            if (this.ezuiIns && data.url === this.videoObj.url) {
                if (data.accessToken === this.videoObj.accessToken) {
                    return new Promise((resolve, reject) => {
                        resolve();
                    });
                } else {
                    autoplay = 1;
                }
            }

            this.videoObj = data;
            await this.clear();
            return new Promise((resolve, reject) => {
                console.log(data);
                let ysyvideo = document.createElement("div");
                ysyvideo.setAttribute("id", this.domId);
                let w = this.styles.width ? this.styles.width.replace("px", "") : 250;
                let h = this.styles.height ? this.styles.height.replace("px", "") : 200;
                ysyvideo.setAttribute("style", `width:${w};height:${h};position: relative;`);
                document.getElementById(this.mainId).appendChild(ysyvideo);
                console.log(this.styles);
                this.ezuiIns = new EZUIKit.EZUIKitPlayer({
                    id: this.domId,
                    accessToken: data.accessToken,
                    url: data.url,
                    plugin: [],
                    // template: 'standard', // 播放器模板，可以通过选定模板，使用内置的播放器样式，组件 simple：极简版;standard：标准版;security：安防版(预览回放);vioce：语音版
                    themeData: {
                        header: {
                            color: "#00E5FF",
                            activeColor: "#FFFFFF",
                            backgroundColor: "#000000",
                            btnList: [
                                // {
                                //   iconId: 'deviceID',
                                //   part: 'left',
                                //   defaultActive: 1,
                                //   memo: '顶部设备名称',
                                //   isrender: 1
                                // }
                            ],
                        },
                        footer: {
                            color: "#FFFFFF",
                            activeColor: "#00E5FF",
                            backgroundColor: "#00000021",
                            btnList: [
                                {
                                    iconId: "play",
                                    part: "left",
                                    defaultActive: this.autoplay ? 1 : 0,
                                    memo: "播放",
                                    isrender: 1,
                                },
                                {
                                    iconId: "sound",
                                    part: "left",
                                    defaultActive: 0,
                                    memo: "声音按钮",
                                    isrender: 1,
                                },
                                {
                                    iconId: "expend",
                                    part: "right",
                                    defaultActive: 0,
                                    memo: "全局全屏按钮",
                                    isrender: 1,
                                },
                            ],
                        },
                    },
                    handleSuccess: (res) => {
                        this.addEventlistenerFullScreen();
                    },
                    fullScreenCallBack: (res) => {
                        console.log("全屏", Date.now());
                        this.fullScreen = true;
                        console.log(this.fullScreen);
                        // setTimeout(() => {
                        // 	this.fullScreen = false;
                        // 	console.log(this.fullScreen);
                        // }, 3000);
                    },
                    // audio: 1, // 是否默认开启声音 0 - 关闭 1 - 开启
                    // openSoundCallBack: data => console.log("开启声音回调", data),
                    // closeSoundCallBack: data => console.log("关闭声音回调", data),
                    // startSaveCallBack: data => console.log("开始录像回调", data),
                    // stopSaveCallBack: data => console.log("录像回调", data),
                    // capturePictureCallBack: data => console.log("截图成功回调", data),
                    // fullScreenCallBack: data => console.log("全屏回调", data),
                    // getOSDTimeCallBack: data => console.log("获取OSDTime回调", data),
                });

                if (this.ezuiIns) {
                    this.ezuiIns.play();

                    setTimeout(() => {
                        console.log(this.ezuiIns);
                        resolve(this.ezuiIns);
                    }, 600);
                }

                // this.ezuiIns.on('log', this.log())
            });
        },
        addEventlistenerFullScreen() {
            const dom = document.getElementById(this.domId + "-expend-icon");
            dom.addEventListener("click", () => {
                this.isFullScreen = !this.isFullScreen;
                if (!this.isFullScreen) {
                    this.ezuiIns.cancelFullScreen();
                }
            });
            // 监听全屏事件
            document.addEventListener("fullscreenchange", () => {
                this.isFullScreen = document.fullscreen;
                console.log("全屏状态", this.isFullScreen);
            });
            document.addEventListener("webkitfullscreenchange", () => {
                this.isFullScreen = document.webkitIsFullScreen;
                console.log("全屏状态", this.isFullScreen);
            });
            document.addEventListener("mozfullscreenchange", () => {
                this.isFullScreen = document.mozFullScreen;
                console.log("全屏状态", this.isFullScreen);
            });
        },
        async clear() {
            console.log("clear ysy video");
            if (this.ezuiIns) {
                try {
                    await this.ezuiIns.stop().then(() => {
                        this.clearHtml();
                    });
                } catch (e) {
                    // statements
                    this.clearHtml();
                }
            }
        },
        clearHtml() {
            this.ezuiIns = null;
            console.log("clear");
            // document.getElementById(this.videoObj.id).remove()
            if (this.mainId && document.getElementById(this.mainId)) {
                document.getElementById(this.mainId).innerHTML = "";
            }
        },
        reSize() {
            if (this.ezuiIns) {
                let main = document.getElementById(this.mainId);
                let w = this.styles.width ? this.styles.width.replace("px", "") : 250;
                let h = this.styles.height ? this.styles.height.replace("px", "") : 200;
                if (this.ezuiIns.width != w || this.ezuiIns.height != h) {
                    if (!this.isFullScreen) {
                        console.log("当前全屏", this.fullScreen);
                        this.ezuiIns.reSize(w, h);
                    }
                }
            }
        },
        play() {
            if (this.ezuiIns) {
                this.ezuiIns.play();
            }
        },
        capturePicture(name) {
            if (this.ezuiIns) {
                this.ezuiIns.capturePicture(name);
            }
        },
        setVolumeVideo(volume) {
            if (this.ezuiIns) {
                this.ezuiIns.volume = volume;
            }
        },
        log(str, className) {
            console.log(str);
        },
        full() {
            if (this.ezuiIns) {
                this.ezuiIns.fullScreen();
            }
        },
    },
    // render(r, context) {
    // 	return r(
    // 		'div',
    // 		{
    // 			attrs: {
    // 				id: this.mainId
    // 			},
    // 			style: this.styles,
    // 			class: 'ysy-video'
    // 		},
    // 		[]
    // 	);
    // }
};
</script>

<style lang="less" scoped>
/deep/.loading-container {
    /* right: 0;
	  margin: auto;
	  white-space: nowrap; */
    width: 100% !important;
    height: 100% !important;
    .loading-item {
        width: 100% !important;
        height: 100% !important;
    }
}
.ysy-video {
    /deep/.ez-iframe-footer-container {
        display: none !important;
    }
}
.ysy-video:hover {
    /deep/.ez-iframe-footer-container {
        display: flex !important;
    }
}
</style>
