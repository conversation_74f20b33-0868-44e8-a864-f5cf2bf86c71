<template>
    <Modal
        v-model="showFlag"
        title="播放"
        :width="1100"
        :mask-closable="false"
        :footer-hide="true"
        @on-visible-change="visibleChange"
    >
        <videoPlay ref="videoRef" :width="1035" :height="540" />
    </Modal>
</template>

<script>
import videoPlay from './videoPlay/index'
export default {
    name: 'videoModal',
    components: { videoPlay },
    data() {
        return {
            showFlag: false
        }
    },
    methods: {
        init(data) {
            this.showFlag = true
            this.$nextTick(() => {
                this.$refs.videoRef.open(data)
            })
        },
        visibleChange(flag) {
           if (!flag) {
               this.$refs.videoRef.clear()
           }
        }
    }
}
</script>

<style lang="less" scoped>

</style>
