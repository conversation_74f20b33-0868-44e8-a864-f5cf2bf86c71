<template>
    <div class="wrapper">
        <FormItem label="经纬度" :prop="props.prop" :rules="{required: props.required, message: props.message}">
            <Input :model-value="formatPosition" placeholder="经纬度" disabled></Input>
        </FormItem>
        <span class="show-map" @click="openMapTapper" v-if="!props.disabled">
           <Icon type="ios-pin-outline" />
        </span>
    </div>
    <MapTapper v-if="isShowMapTapper" :is-show="isShowMapTapper" v-model="pointObj" @on-confirm="onMapTapperConfirm" @on-cancel="onMapCancel" />
</template>
<script lang="ts" setup>
import { computed, defineProps, withDefaults, defineEmits, ref } from 'vue';
import MapTapper from '@/components/common/mapTapper';

type Props = {
    objx: string;
    objy: string;
    disabled: boolean;
    prop: string;
    required: boolean;
    message: string
}

const props = withDefaults(defineProps<Props>(), {
    prop: 'objx',
    required: true,
    message: '经纬度必填'
})
const emits = defineEmits(['update:objx', 'update:objy', 'confirm'])

const formatPosition = computed(() => {
    return props.objx ? `${props.objx},${props.objy}` : ''
})

const isShowMapTapper = ref<boolean>(false);

const pointObj = computed({
    get() {
        return { lat: props.objy, lng: props.objx }
    },
    set(newValue) {
        emits('update:objx', newValue.lng)
        emits('update:objy', newValue.lat)
    }
})

const onMapTapperConfirm = (...value) => {
    isShowMapTapper.value = false;
    emits('confirm', ...value)
}

const onMapCancel = () => {
    isShowMapTapper.value = false;
}
const openMapTapper = () => {
    isShowMapTapper.value = true;
}

</script>

<style lang="less" scoped>
.wrapper{
    position: relative;
}
.show-map {
    position: absolute;
    top: 1px;
    left: 60px;
    transform: translate(0, -25%);
    font-size: 16px;
    color: #165DFF;
    cursor: pointer;
    z-index: 1000;
    .ivu-icon {
        font-weight: 700;
    }
}
</style>
