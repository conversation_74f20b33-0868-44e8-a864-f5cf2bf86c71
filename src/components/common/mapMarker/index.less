.cluster-marker {
  background-color: rgba(24, 144, 255, 0.8);
  border-radius: 50%;
  color: #fff;
  height: 36px;
  line-height: 36px;
  text-align: center;
  width: 36px;
  border: 2px solid #fff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.3);

  &-content {
    font-size: 14px;
  }
}

.container-map {
  width: 100%;
  height: 400px;

  .amap-maps {
    .amap-layers {
      .amap-markers {
        .amap-marker {
          .marker-icon {
            height: 40px;
            width: 40px;
            position: relative;
            background: url('./images/icon_map_001.png') no-repeat center;
            background-size: 40px 40px;
            display: flex;
            align-items: center;
            justify-content: center;

            &:hover {
              background: url('./images/icon_map_002.png') no-repeat center;
              background-size: 40px 40px;
            }

            &.actived {
              background: url('./images/icon_map_003.png') no-repeat center;
              background-size: 40px 40px;
            }

            &.alarm-state {
              background: url('./images/icon_map_004.png') no-repeat center;
              background-size: 40px 40px;

              &:hover {
                background: url('./images/icon_map_005.png') no-repeat center;
                background-size: 40px 40px;
              }

              &.actived {
                background: url('./images/icon_map_006.png') no-repeat center;
                background-size: 40px 40px;
              }
            }

            &.green-state {
              background: url('./images/icon_map_007.png') no-repeat center;
              background-size: 40px 40px;

              &:hover {
                background: url('./images/icon_map_008.png') no-repeat center;
                background-size: 40px 40px;
              }

              &.actived {
                background: url('./images/icon_map_009.png') no-repeat center;
                background-size: 40px 40px;
              }

            }

            &.hover:not(.actived) {
              background: url('./images/icon_map_002.png') no-repeat center;
              background-size: 40px 40px;
            }

            .marker-status {
              position: absolute;
              font-weight: 500;
              font-size: 12px;
              height: 20px;
              width: 20px;
              font-size: 12px;
              border-radius: 100%;
              border: 1px solid #FFFFFF;
              right: -3px;
              top: 0px;
              text-align: center;

              &.hide-tag {
                display: none;
              }

              &.open {
                background: #FFD233;
              }

              &.closed {
                background: #D9D9D9;
              }
            }

            img {
              // margin-top:-5px;
              height: 16px;
              width: 16px;
            }
          }

          .marker-label-box {
            max-width: 150px;
            height: 31px;
            border-radius: 15px;
            white-space: nowrap;
            min-width: 80px;
            transform: translate(-30px, 0);
            background: #FFFFFF;
            box-shadow: 0px 1px 6px rgba(61, 103, 175, 0.2);
            text-overflow: ellipsis;
            white-space: nowrap;
            overflow: hidden;
            line-height: 31px;
            // align-items: center;
            padding-left: 30px;
            font-size: 12px;
            padding-right: 8px;

            &.label-box {
              z-index: -1;
              position: absolute;
              transform: translate(10px, -35px) !important;
              display: flex;
              align-items: center;
              column-gap: 4px;

              span.name {
                flex: 1;
                max-width: 90px;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
              }

              span.num {
                height: 18px;
                width: 18px;
                background: #FFD233;
                border-radius: 100%;
                line-height: 18px;
                text-align: center;
              }
            }
          }
        }

      }
    }
  }

}