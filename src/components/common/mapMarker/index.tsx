import { defineComponent, nextTick, onMounted, ref, watch } from 'vue';
import AMapLoader from '@amap/amap-jsapi-loader';
import './index.less';
import { isEmpty } from 'wei-util';


export default defineComponent({
  name: 'MapMarker',
  props: {
    // 标记点数据
    markers: {
      type: Array as any,
      default: () => []
    },
    lngLat: {
      type: Array as any,
      default: () => []
    },
    // 当前选中的标记点id
    activeMarkerId: {
      type: [String, Number, Array],
      default: ''
    },
    // 标记点图标
    markerIcon: {
      type: String,
      required: true,
    },
    // 选中时的图标
    activeMarkerIcon: {
      type: String,
      default: ''
    },
    // 地图配置项
    mapOptions: {
      type: Object as any,
      default: () => ({
        zoom: 16,
        center: [114.61, 30.45]
      })
    },
    // 标识字段名
    idKey: {
      type: String,
      default: 'id'
    },
    // 名称字段
    nameKey: {
      type: String,
      default: 'name'
    },
    // 经度字段
    lngKey: {
      type: String,
      default: 'gdx'
    },
    // 纬度字段
    latKey: {
      type: String,
      default: 'gdy'
    },
    // 是否可以点击多个标记点
    clickMultiple: {
      type: Boolean,
      default: false
    },
    // 是否支持反选
    toggleSelect: {
      type: Boolean,
      default: false
    }
  },
  emits: ['markerClick', 'update:activeMarkerId'],
  setup(props, { emit, expose }) {
    const map = ref<any>(null);
    const AMap = ref<any>(null);
    const cluster = ref<any>(null);
    // 存储选中的标记点数据
    const selectedMarkers = ref<any[]>([]);

    // 添加变量保存初始渲染后的地图状态
    const initialMapState = ref({
      center: null as any,
      zoom: null as any
    });

    // 获取标记点内容
    const getMarkerContent = (data: any) => {
      // 检查是否在选中列表中或匹配 activeMarkerId
      const isActive = Array.isArray(props.activeMarkerId) ? props.activeMarkerId.includes(data[props.idKey]) : data[props.idKey] === props.activeMarkerId;
      return `<div class="marker-icon ${isActive ? 'actived' : ''}">
        <img src="${props.markerIcon}"></div>
        <div class="marker-label-box label-box">${data[props.nameKey] || ''}</div>`
    };

    // 初始化地图
    const initMap = async () => {
      AMap.value = await AMapLoader.load({
        key: 'ea53a5291f8f7c1f215ba20930ea9866',
        plugins: ['AMap.MarkerCluster'],
        version: '2.0'
      });
      map.value = new AMap.value.Map('map-container', {
        resizeEnable: true,
        ...props.mapOptions
      });
      map.value.setZoom(props.mapOptions.zoom || 16);
      // 地图事件监听
      map.value.on('movestart', () => {
        map.value?.clearInfoWindow();
      });

      map.value.on('zoomstart', () => {
        map.value?.clearInfoWindow();
      });
    };

    // 更新地图标记点
    const updateMarkers = () => {
      if (!map.value) return;
      // 清除已有的聚合点
      if (cluster.value) {
        cluster.value.setMap(null);
      }

      // 构建标记点数据
      const points = props.markers.map((item: any) => ({
        lnglat: [item[props.lngKey], item[props.latKey]],
        extData: item
      }));

      // 创建聚合点
      cluster.value = new AMap.value.MarkerCluster(map.value, points, {
        gridSize: 30,
        renderClusterMarker: renderClusterMarker,
        renderMarker: renderMarker
      });

      // 只在有多个点时才添加聚合点点击事件
      cluster.value.on('click', handleClusterClick);

      // 自适应地图视野
      if (points.length > 0 && !initialMapState.value.center) {
        // 设置自适应视野
        map.value.setFitView();
        
        // 使用 moveend 事件监听地图移动结束，此时动画已完成
        if (!initialMapState.value.center) {
          const moveEndListener = () => {
            // 保存初始渲染后的地图状态
            initialMapState.value.center = map.value.getCenter();
            initialMapState.value.zoom = map.value.getZoom();
            // 移除事件监听器，避免重复执行
            map.value.off('moveend', moveEndListener);
          };
          
          // 添加地图移动结束事件监听
          map.value.on('moveend', moveEndListener);
        }
      }
    };

    // 渲染聚合点
    const renderClusterMarker = (context: any) => {
      const { count, marker, clusterData } = context;
      const div = document.createElement('div');
      div.className = 'cluster-marker';
      div.innerHTML = `<div class="cluster-marker-content" > ${count}</ div> `;
      marker.setContent(div);
      marker.setExtData(clusterData)
    };
    let noUpdateMaker = false
    // 渲染单个标记点
    const renderMarker = (context: any) => {
      const { marker, data } = context;
      if (!data || !data[0]?.extData) return;
      const content = getMarkerContent(data[0].extData);
      marker.setContent(content);
      marker.setExtData(data[0].extData);

      // 添加点击事件监听
      marker.on('click', (e) => {
        noUpdateMaker = true
        const extData = marker.getExtData();
        if (extData) {
          if (props.clickMultiple) {
            // 多选模式
            const isCurrentlyActive = selectedMarkers.value.some(
              item => item[props.idKey] === extData[props.idKey]
            );
            if (props.toggleSelect && isCurrentlyActive) {
              // 反选：移除当前标记点
              selectedMarkers.value = selectedMarkers.value.filter(
                item => item[props.idKey] !== extData[props.idKey]
              );
            } else if (!isCurrentlyActive) {
              // 选中：添加当前标记点
              selectedMarkers.value.push(extData);
            }
            // 发送所有选中的数据
            emit('markerClick', selectedMarkers.value);
          } else {
            // 单选模式
            const isCurrentlyActive = selectedMarkers.value.length === 1
              && selectedMarkers.value[0][props.idKey] === extData[props.idKey];

            if (props.toggleSelect && isCurrentlyActive) {
              // 反选：清空选中
              selectedMarkers.value = [];
              emit('markerClick', null);
            } else {
              // 选中：更新为当前标记点
              selectedMarkers.value = [extData];
              emit('markerClick', extData);
            }
          }
        }
        const { lng, lat } = marker.getPosition();
        setZoomAndCenter(17, [lng, lat]);
      });
    };
    const setZoomAndCenter = (zoom: number, lngLat: number[]) => {
      map.value.setZoomAndCenter(zoom, lngLat)
    }
    // 处理聚合点点击
    const handleClusterClick = (e: any) => {
      const { clusterData } = e;
      // 如果是单个标记点，不在这里处理，让标记点自己的点击事件处理
      if (clusterData.length === 1) {
        return;
      }
      // 如果是聚合点（包含多个标记点），不触发选中
      if (clusterData.length > 1) {
        let alng = 0,
          alat = 0;
        for (const m of clusterData) {
          alng += m.lnglat.lng;
          alat += m.lnglat.lat;
        }
        const lat = alat / clusterData.length;
        const lng = alng / clusterData.length;
        //以中心点固定倍数放大地图，达到展开聚合点的效果
        setZoomAndCenter(17, [lng, lat])
      }
    };

    // 提取公共方法：根据ID获取标记点数据
    const getMarkersByIds = (ids: any) => {
      if (Array.isArray(ids)) {
        return props.markers.filter((item: any) => ids.includes(item[props.idKey]));
      } else {
        return props.markers.filter((item: any) => ids === item[props.idKey]);
      }
    };

    // 优化更新标记点状态方法
    const updateMakerState = (isWheel = false) => {
      // 清除标记点
      map.value.clearMap();
      // 重新绘制标记点
      updateMarkers();

      if (!noUpdateMaker) {
        const checkedPoints = getMarkersByIds(props.activeMarkerId);
        if (checkedPoints.length > 0) {
          const firstPoint = checkedPoints[0];
          setZoomAndCenter(17, [firstPoint[props.lngKey], firstPoint[props.latKey]]);
        }
      }
      noUpdateMaker = false;
    };

    // 监听地图缩放事件
    onMounted(() => {
      nextTick(() => {
        initMap()
      });
    });
    watch(() => props.markers, (newVal) => {
      if (newVal.length > 0) {
        updateMarkers();
      }
    }, { deep: true });
    // 优化监听 activeMarkerId 变化
    watch(() => props.activeMarkerId, (newVal: any[] | string | number) => {
      if (isEmpty(newVal)) {
        // 设置中心点为初始渲染标记点的样子
        if (map.value && initialMapState.value.center) {
          selectedMarkers.value = [];
          // 使用保存的初始状态
          map.value.setZoomAndCenter(
            initialMapState.value.zoom,
            initialMapState.value.center
          );
        }
        return;
      }

      selectedMarkers.value = getMarkersByIds(newVal);
      nextTick(() => {
        updateMakerState();
      });
    }, { deep: true });
    return () => (
      <div id="map-container" class="container-map" style="width: 100%; height: 100%;"></div>
    );
  }
}); 