<template>
    <div>
        <Cascader :data="newRegionData" :load-data="loadData" :model-value="regionCode" @on-change="handlerChange" clearable :disabled="props.disabled" />
    </div>
</template>

<script lang="ts" setup>
import { computed, defineProps, defineEmits, ref } from 'vue'
import { regionData, TextToCode } from 'element-china-area-data'
const props = defineProps({
    modelValue: {
        type: String
    },
    disabled: {
        type: Boolean
    }
})
const emits = defineEmits(['update:modelValue'])

const newRegionData = ref<Item[]>()

type Item = {
    label: string;
    value: string;
    loading: boolean;
    children: any[];
}

newRegionData.value = regionData.map((item:Item) => {
    return {
        label: item.label,
        value: item.value,
        loading: false,
        children: []
    }
});

const loadData = (item: Item, callback:() => void) => {
    item.loading = true;
    item.children = regionData.find((r: Item) => r.value === item.value).children
    item.loading = false;
    callback && callback();
}

// 对内 提供数组 接受数组
const regionCode = computed(() => {
        if (props.modelValue) {
            const [province, city, district] = props.modelValue?.split(',');
            const provinceId = TextToCode[province]?.code;
            const cityId = provinceId && TextToCode[province][city]?.code;
            const districtId = cityId && TextToCode[province][city][district]?.code;
            return [provinceId, cityId, districtId]
        } else {
            return [];
        }
})
const handlerChange = (val:any, val2:IdataItem[]) => {
    const labelStr = val2?.map(item => item.label)?.join(',')
    emits('update:modelValue', labelStr)
}

interface IdataItem{
    label: string;
    value: string;
}
</script>

<style lang="less" scoped>
:deep(.ivu-icon-ios-close-circle){
    font-family: "iconfont" !important;
    color: #4E627E;
    font-size: 16px;
    &:before{
        content: "\e705";
    }
}
</style>
