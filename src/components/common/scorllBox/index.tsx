import { defineComponent, nextTick, ref, watch } from "vue";
export default defineComponent({
    name: "scorllBox",
    props: {
        scorllId: {
            type: [String, Number],
            default: "",
        },
        key: {
            type: String,
            default: "",
        },
    },
    setup(props, ctx) {
        const handleScorll = (id: string | number) => {
            const activedChild =
                Array.from(scorllRef.value?.children).filter((k) =>
                    k!["className"].includes("actived")
                )[0] || {};
            console.log(activedChild!["offsetTop"], scorllRef.value.offsetTop);
            if (
                activedChild!["offsetTop"] >
                    scorllRef.value.scrollTop + scorllRef.value.offsetHeight ||
                activedChild!["offsetTop"] < scorllRef.value.scrollTop
            ) {
                scorllRef.value.scrollTop = activedChild["offsetTop"];
            }
        };
        watch(props, (val) => {
            nextTick(() => {
                handleScorll(props.scorllId);
            });
        });
        const scorllRef = ref();
        return () => (
            <>
                {ctx.slots!.cont && (
                    <div class="scorll-map-cont" ref={scorllRef}>
                        {ctx.slots!.cont()}
                    </div>
                )}
            </>
        );
    },
});
