import { defineComponent, nextTick, defineExpose } from "vue";
import { ref, watch, onMounted, computed } from "vue";
import * as echarts from "echarts";
import "./echart.less";
import { mapGetters, useStore } from "vuex";
export default defineComponent({
    name: "echart-item",
    props: {
        option: {
            type: Object,
            default: () => ({}),
        },
        afterResize: {
            type: Function,
        }
    },
    emits: ["initEchart", "sector-hover"],
    setup(props: any, ctx) {
        const echartsRef = ref<HTMLElement>();

        const store = useStore();
        const isCollapsed = computed(
            mapGetters(["isCollapsed"]).isCollapsed.bind({ $store: store })
        );

        onMounted(() => {
            const myChart = echarts.init(echartsRef.value!);

            myChart.setOption(props.option, true);
            ctx.emit("initEchart", myChart);

            // 新增，监听鼠标悬停时间
            myChart.on("mouseover", (params: any) => {
                if (params.componentType === 'series' && params.seriesType === 'pie') {
                    ctx.emit('sector-hover', params.dataIndex);
                }
            });
            myChart.on('mouseout', params => {
                if (params.componentType === 'series' && params.seriesType === 'pie') {
                    ctx.emit('sector-hover', -1);
                }
            });
            window.addEventListener("resize", () => {
                nextTick(() => {
                    setTimeout(() => {
                        echartsRef.value && echarts.getInstanceByDom(echartsRef.value)?.resize();
                        props.afterResize && props.afterResize()
                    }, 800);
                });
            });
            nextTick(() => {
                echartsRef.value && echarts.getInstanceByDom(echartsRef.value)?.resize();
            });
        });
        const handleResize = () => {
            nextTick(() => {
                echartsRef.value && echarts.getInstanceByDom(echartsRef.value)?.resize();
            });
        };
        ctx.expose({ handleResize });
        watch(
            () => isCollapsed,
            () => {
                setTimeout(() => handleResize(), 500);
            },
            { deep: true, immediate: true }
        );
        watch(
            () => props.option,
            () => {
                if (echartsRef.value) {
                    echarts.getInstanceByDom(echartsRef.value)?.setOption(props.option, true);
                    nextTick(() => {
                        echartsRef.value && echarts.getInstanceByDom(echartsRef.value)?.resize();
                    });
                }
            },
            { deep: true }
        );
        return () => <div ref={echartsRef} class="echart-cont"></div>;
    },
});
