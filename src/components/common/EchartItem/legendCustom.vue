<template>
    <div class="legend-swiper" :style="styles">
        <swiper direction="vertical" :modules="modules" :slides-per-view="6" :navigation="navigation" @swiper="onSwiper"
            @slideChange="onSlideChange">
            <swiper-slide v-for="item in legList" :key="item.id">
                <div class="legend-item">
                    <div class="yuan" :style="{ 'background': item.color }"></div>
                    <div class="name">
                        <TooltipAutoShow :content="item.name" />
                    </div>
                    <div class="per">{{ item.percent }}%</div>
                    <div class="num">{{ item.value }}{{ unit }}</div>
                </div>
            </swiper-slide>
        </swiper>

        <div class="swiper-button-prev" v-show="legList.length > 6">
            <Icon custom="iconfont icon-up" />
        </div>
        <div class="swiper-button-next" v-show="legList.length > 6">
            <Icon custom="iconfont icon-down1" />
        </div>

    </div>
</template>
<script>
import { ref, watch, computed } from 'vue'
import { toFixed } from 'wei-util'
import { Navigation } from 'swiper';
import { SwiperSlide } from 'swiper/vue/swiper-slide';
import { Swiper } from 'swiper/vue/swiper';
import 'swiper/swiper.css';

export default {
    name: 'legendCustom',
    props: {
        list: { default() { return [] } },
        color: { default() { return [] } },
        styles: { default() { return {} } },
        percentPrecision: { default: 0 },
        unit: { default: '吨' }
    },
    components: {
        Swiper,
        SwiperSlide
    },
    setup(props) {
        const navigation = ref({
            nextEl: '.swiper-button-next',
            prevEl: '.swiper-button-prev',
        })
        const swiperMy = ref(null)
        const onSwiper = (swiper) => {
            swiperMy.value = swiper
            console.log(props.list)
        };
        const legList = computed(() => {
            let total = 0
            props.list.forEach(item => total += item.value)
            let arr = []
            let len = props.color.length
            props.list.forEach((k, i) => {
                let percent = toFixed(((k.value * 100 / total) || 0), props.percentPrecision)
                arr.push({
                    name: k.name,
                    value: k.value,
                    percent,
                    color: props.color[i % len]
                })
            })
            return arr
        })
        watch(() => props.list, () => {
            swiperMy.value && swiperMy.value.updateSlides()
        })
        const onSlideChange = () => {
            console.log('slide change');
        };
        return {
            navigation,
            swiperMy,
            legList,
            onSwiper,
            onSlideChange,
            modules: [Navigation],
        };
    },
};
</script>
<style scoped lang="less">
.legend-swiper {
    position: absolute;
    right: 0px;
    height: 180px;
    top: 50%;
    transform: translateY(-50%);

    .swiper {
        width: 100%;
        height: 100%;
    }
}

.swiper-button-prev,
.swiper-button-next {
    position: absolute;
    width: 20px;
    height: 16px;
    bottom: -24px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: @fill-2;
    border-radius: 4px;
    color: @text-color;
    cursor: pointer;

    i {
        font-size: 14px;
    }

    &:hover {
        color: @primary-color;
    }

    &.swiper-button-disabled {
        background: @fill-1;
        color: @text-2-1;
        cursor: not-allowed;

        //display: none;
        &:hover {
            color: @text-2-1;
        }
    }
}

.swiper-button-prev {
    left: 30%;
}

.swiper-button-next {
    right: 30%;
}

.legend-item {
    display: flex;
    align-items: center;
    padding: 0 8px;
    height: 30px;
    font-size: 12px;
    color: @text-color;
    border-bottom: 1px solid @line-1;

    .yuan {
        width: 10px;
        height: 10px;
        border-radius: 50%;
        margin-right: 10px;
    }

    .name {
        flex: 1;
        overflow: hidden;
        margin-right: 8px;
    }

    .per {
        margin-right: 12px;
    }
}</style>
