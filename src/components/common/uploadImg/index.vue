<template>
    <div class="container">
        <div class="top">
            <div class="upload-wrapper" v-show="!disabled">
                <Upload
                    ref="upload"
                    :type="type"
                    :action="u.action"
                    :data="u.uploadData"
                    :before-upload="beforeUpload"
                    :on-success="uploadSuccess"
                    :on-error="errorUpload"
                    :show-upload-list="false"
                    :default-file-list="defaultList"
                    :multiple="multiple"
                >
                    <div class="upload-box">
                        <Icon type="md-add" size="20"></Icon>
                        <span>点击上传</span>
                    </div>
                </Upload>
            </div>
            <template v-for="(item, index) in m.uploadFileList">
                <div class="demo-upload-list" v-if="item.url" :key="item.url">
                    <Image :src="item.url" fit="cover" width="100%" height="100%" />
                    <div class="demo-upload-list-cover">
                        <Icon v-if="true" type="ios-eye-outline" @click="previewImg(item)"></Icon>
                        <Icon v-if="!disabled" type="ios-trash-outline" @click="handleRemove(item, index)"></Icon>
                    </div>
                </div>
            </template>
        </div>
        <!--        <ImagePreview v-model="previewVisible" :preview-list="[previewImgUrl]" />-->
        <div class="err-msg" v-if="m.errorMsg">{{ m.errorMsg }}</div>
        <div class="footer" v-else v-show="!disabled">
            仅支持上传{{ format.join(', ') }}文件格式,单个文件大小不超过 {{ maxSize }}MB{{ multiple && maxLength > 1 ? '，最多上传' + maxLength + '个文件' : '' }}
        </div>

    </div>
    <previewModal ref="preR" />
</template>

<script>
import { ossService } from '@/api/ossService';
export default {
    name: 'UploadFile',
    props: {
        modelValue: { type: [String, Object, Array] },
        disabled: { default: false }, // 是否禁止上传
        multiple: { default: false }, // 是否可以一次上传多个文件
        type: { default: 'drag' },
        format: { default: () => ['jpg', 'jpeg', 'png'] },
        maxSize: { default: 50 }, // 单位MB
        maxLength: { default: 1 }, // 最多上传个数
        showUploadList: { default: true } // 显示上传列表
    },
    data() {
        return {
            u: {
                action: '',
                uploadData: {},
                accessUrl: '',
                loading: false
            },
            m: {
                errorMsg: '',
                uploadFileList: [], // 当前的上传文件列表
                fileNameObj: {},
                publicValue: ''
            },
            s: {
                allNum: 0, // 上传的总数
                curNum: 0 // 已上传数量
            }
        }
    },
    watch: {
        modelValue(val) {
            if (val !== this.m.publicValue) {
                this.init()
            }
        },
        disabled() {
            this.m.uploadFileList.forEach(item => {
                if (this.disabled && item.state == 'success') {
                    item.state = 'preview'
                } else if (!this.disabled && item.state == 'preview') {
                    item.state = 'success'
                }
            })
            this.showErrMsg('')
        }
    },
    created() {
        this.init()
        if (!this.disabled) {
            this.getOss()
        }
    },
    methods: {
        init() {
            if (this.modelValue) {
                let arr = this.modelValue.split(',')
                this.m.uploadFileList = arr.map(item => {
                    let a1 = item.split('/')
                    return {
                        url: item,
                        name: a1[a1.length - 1],
                        state: this.disabled ? 'preview' : 'success',
                        format: this.getFormatByUrl(item)
                    }
                })
            } else {
                this.m.uploadFileList = []
            }
        },
        // 预览
        previewImg(item) {
            this.$refs.preR.show(item)
        },
        // 下载
        download(item) {
            this.$Util.download(item.url, item.name)
        },
        // 删除
        handleRemove(_item, index) {
            this.m.uploadFileList.splice(index, 1)
            this.changeValue()
            this.showErrMsg('')
        },
        // 批量上传时为循环执行
        beforeUpload(file) {
            if (this.m.uploadFileList.length >= this.maxLength) {
                // 当最多上传1个的时候，就自动替换
                if (this.maxLength == 1) {
                    debugger
                    this.m.uploadFileList = [];
                } else {
                    this.showErrMsg('最多上传' + this.maxLength + '个文件')
                    return false
                }
            }
            const { name } = file;
            const suffix = name.split('.')[name.split('.').length - 1];
            if (!this.format.includes(suffix)) {
                this.showErrMsg(`文件格式不正确，请上传${this.format.join('、')}`);
                return false;
            }
            let size = file.size / (1024 * 1024)
            if (size > this.maxSize) {
                this.showErrMsg(`请上传${this.maxSize}MB以下的文件`)
                return false;
            }
            if (!this.multiple) {
                this.u.loading = true
            }
            this.s.allNum += 1
            this.$emit('update:loading', true)
            let fileName = name
            let _name_arr = fileName.split(',')
            if (_name_arr.length > 1) {
                fileName = _name_arr.join('-')
            }
            this.u.uploadData.key = this.u.uploadData.path + fileName
            // this.m.fileNameObj['a' + file.name] = fileName
            file.state = 'loading'
            this.m.uploadFileList.push(file)
            this.showErrMsg('')
        },
        async uploadSuccess(response, file, fileList) {
            // console.log('response', response)
            if (response.success === true) {
                // let o = this.m.fileNameObj['a' + file.name]
                // 当前为从响应获取url
                file.url = response.data.accessUrl
                // file.name = o
                let index = this.m.uploadFileList.findIndex(item => item.uid === file.uid)
                file.format = this.getFormatByUrl(file.url)
                if (file.format.type === 'video') {
                    let duration = await this.getVideoTime(this.m.uploadFileList[index])
                    file.duration = duration
                }
                file.state = 'success'
                this.m.uploadFileList.splice(index, 1, file)

                this.$emit('on-success', file, fileList, this.u.uploadData, response)
                this.changeValue()
            } else {
                this.showErrMsg(`上传失败，请稍后重试`)
            }
            this.completeUpload()
        },
        changeValue() {
            let arr = this.m.uploadFileList.map(item => item.url)
            this.m.publicValue = arr.join(',') || ''
            this.$emit('update:modelValue', this.m.publicValue)
            if (!this.multiple) {
                this.u.loading = false
            }
        },
        errorUpload(_error, file, _fileList) {
            let index = this.m.uploadFileList.findIndex(item => item.uid === file.uid)
            this.m.uploadFileList.splice(index, 1)
            this.showErrMsg(`上传失败，请稍后重试`)
            this.completeUpload()
            if (!this.multiple) {
                this.u.loading = false
            }
        },
        // 上传计数
        completeUpload() {
            this.s.curNum += 1
            if (this.s.allNum === this.s.curNum) {
                this.s.allNum = 0
                this.s.curNum = 0
                this.$emit('update:loading', false)
            }
        },
        getOss() {
            return new Promise((resolve, reject) => {
                ossService.policy({}).then((data) => {
                    // console.log('查询密钥结束', data)
                    this.u.action = data.uploadHost;
                    this.u.accessUrl = data.accessHost;
                    this.u.uploadData.policy = data.policy;
                    this.u.uploadData.OSSAccessKeyId = data.accessId;
                    this.u.uploadData.Signature = data.signature;
                    this.u.uploadData.path = data.path ? data.path : '';
                    resolve();
                }, () => {
                    console.log('查询密钥失败')
                    this.loading = false;
                    // reject();
                    return false;
                });
            })
        },
        showErrMsg(msg) {
            this.m.errorMsg = msg
            // if (this.sitime) {
            //     clearTimeout(this.sitime)
            // }
            // this.sitime = setTimeout(() => {
            //     this.m.errorMsg = ''
            // }, 2000)
        },
        clearFiles() {
            this.$refs.upload.clearFiles()
        },
        // 获取视频时长
        getVideoTime(data) {
            return new Promise((resolve, reject) => {
                let url = URL.createObjectURL(data);
                let audioElement = new Audio(url);
                let duration;
                audioElement.addEventListener('loadedmetadata', (_event) => {
                    duration = audioElement.duration; // 时长为秒
                    resolve(duration);
                });
            })
        },
        // 获取上传的文件类型
        getFormatByUrl(url) {
            const format = url.split('.').pop().toLocaleLowerCase() || '';
            let icon = 'icon-file';
            let type = 'file'
            if (['gif', 'jpg', 'jpeg', 'png', 'bmp', 'webp'].indexOf(format) > -1) {
                type = 'img'
                icon = 'icon-file-image'
            }
            if (['mp4', 'm3u8', 'rmvb', 'avi', 'swf', '3gp', 'mkv', 'flv'].indexOf(format) > -1) {
                type = 'video'
                icon = 'icon-file-video'
            }
            if (['ppt', 'pptx'].indexOf(format) > -1) {
                type = 'ppt'
            }
            if (['pdf'].indexOf(format) > -1) {
                type = 'pdf'
                icon = 'icon-file-pdf'
            }
            return { icon, type };
        }
    }
}
</script>

<style lang="less" scoped>
.err-msg{
    color: #F53F3F;
    font-size: 12px;
    margin-bottom: 12px;
}
.container{
    display: flex;
    flex-direction: column;
}
.top{
    display: flex;
    align-content: center;
    flex-wrap: wrap;
}
.footer{
    color: #798799;
    justify-content: start;
    align-items: center;
    height: 24px;
    font-size: 12px;
    margin-top: 0;
}
.upload-box{
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background-color: #F3F7FB;
    border: 1px dashed #E0E6F1;
    font-style: normal;
    font-weight: 500;
    font-size: 14px;
    color: #4E5969;
    width: 80px;
    height:80px;
    cursor: pointer;
    .ivu-icon{
        color: #4E5969 !important;
    }
}
.upload-box:hover{
    background-color: #E5E6EB;
    border: 1px dashed #C9CDD4;
}
.demo-upload-list{
    display: inline-block;
    width: 80px;
    height: 80px;
    text-align: center;
    line-height: 80px;
    border: 1px solid transparent;
    border-radius: 4px;
    overflow: hidden;
    background: #fff;
    position: relative;
    box-shadow: 0 1px 1px rgba(0,0,0,.2);
    margin-right: 4px;
    margin-bottom: 8px;
}
.demo-upload-list img{
    width: 100%;
    height: 100%;
}
.demo-upload-list-cover{
    display: none;
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(0,0,0,.6);
}
.demo-upload-list:hover .demo-upload-list-cover{
    display: block;
}
.demo-upload-list-cover i{
    color: #fff;
    font-size: 20px;
    cursor: pointer;
    margin: 0 2px;
}
.upload-wrapper{
    margin-right: 16px;
    margin-bottom: 8px;
}
</style>
