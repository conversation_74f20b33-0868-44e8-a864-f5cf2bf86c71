<template>
    <Select :multiple="multiple" ref="select" @on-clear="clickClear" v-model="curValue" @on-change="changeValue" :clearable="clearable" :filterable="filterable" placeholder="请选择">
        <Option @click="clickItem(item)"  v-for="(item, index) in list" :value="nodeValue(item)" :key="item.code">
            {{ nodeLabel(item) }}
        </Option>
    </Select>
</template>

<script>
import { commonService } from '@/api/commonService'
export default {
    name: 'SelectAlarmType',
    props: {
        nodeLabel:{default:()=>(item)=>item.name},
        nodeValue:{ default:()=>(item)=>item.code},
        modelValue: { type: [Number, String, Array] },
        clearable: { default: true },
        filterable: { default: true },
        multiple:{default:false},
        param: {
            default() { return {} }
        }
    },
    emits: ['update:modelValue', 'on-change', 'on-list','on-select','on-clear'],
    data() {
        return {
            curValue: this.modelValue,
            list: [],
            isChangeValueIn: false
        }
    },
    watch: {
        modelValue(newVal) {
            if (this.isChangeValueIn) {
                this.isChangeValueIn = false
            } else {
                this.curValue = this.modelValue
            }
        }
    },
    mounted() {
        this.getDataList()
    },
    methods: {
        clickItem(item){
            this.$emit('on-select', item)
        },
        clickClear(){
            this.$emit('on-clear')
        },
        changeValue(val) {
            this.isChangeValueIn = true
            let obj = this.list.find(item => val == item.id)
            this.$emit('update:modelValue', val)
            this.$emit('on-change', val, obj)
        },
        getDataList() {
            if (Object.keys(this.param).length === 0) {
                return
            }
            commonService.getAlarmEventList(this.param).then(res => {
                if (res.success) {
                    this.list = res.data
                    this.$refs.select.lazyUpdateValue()
                    this.$emit('on-list', this.list)
                }
            })
        }
    }
}
</script>

<style lang="less" scoped>

</style>

