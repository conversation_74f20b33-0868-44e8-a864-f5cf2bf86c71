<template>
    <div class="flow-container" id="container">
    </div>
</template>
<script lang="ts">
import G6 from '@antv/g6';
import { defineComponent, onMounted, ref, } from 'vue'
export default defineComponent({
    props: {
        data: {
            type: Object,
            default: () => { }
        },
        showStatus: {
            type: Boolean,
            default: false
        }
    },
    setup(props, ctx) {
        const textColor = ['#4E627E', '#1E2A55', '#1E2A55']
        const borderColor = ['#BEDAFF', '#165DFF', '#165DFF']
        const textList = ['未处理', '处理中', '已处理']
        // 初始化dom
        const container = ref();
        const graph = ref();
        onMounted(() => {
            // console.log('onMounted')
            container.value = document.getElementById('container');
            register()
            setData()
        })
        const tooltip = new G6.Tooltip({
            offsetX: 10,
            offsetY: 10,
            itemTypes: ['node'],
            getContent: (e: any) => {
                const outDiv = document.createElement('div');
                outDiv.style.width = 'fit-content';
                outDiv.innerHTML = `
                    <h4>${e.item.getModel().name || e.item.getModel().id}</h4>
                    <p style="display:${props.showStatus ? 'block' : 'none'}">处理状态: ${textList[e.item.getModel().operateStatus || 0]}</p>`;
                return outDiv;
            },
            shouldBegin: (e: any) => {
                const _conf = e.item.getModel().conf as string
                if (_conf.length > 10 || props.showStatus) return true;
                return false
            }
        });
        // 注册样式
        const register = () => {
            G6.registerNode(
                'sql',
                {
                    drawShape(cfg, group) {
                        const _index = (cfg?.operateStatus || 0) as number;
                        const rect = group!.addShape('rect', {
                            attrs: {
                                x: -65,
                                y: -16,
                                width: 130,
                                height: 32,
                                radius: 0,
                                // stroke: '#165DFF',
                                stroke: props.showStatus ? borderColor[_index] : '#165DFF',
                                fill: '#E8F3FF',
                                lineWidth: 1,
                            },
                            name: 'rect-shape',
                        });
                        if (cfg?.name) {
                            group?.addShape('text', {
                                attrs: {
                                    text: (cfg?.name as string).length > 9 ? (cfg?.name as string).slice(0, 9) + '...' : cfg?.name,
                                    x: 0,
                                    y: 0,
                                    fill: props.showStatus ? textColor[_index] : '#1E2A55',
                                    fontSize: 12,
                                    textAlign: 'center',
                                    textBaseline: 'middle',
                                    // fontWeight: 'bold',
                                },
                                name: 'text-shape',
                            });
                        }
                        return rect;
                    },
                },
                'single-node',
            );
        }
        // 初始化画布
        const setData = () => {
            graph.value = new G6.Graph({
                container: 'container',
                width: container.value.scrollWidth,
                plugins: [tooltip],
                height: 600,
                maxZoom: 1,
                layout: {
                    type: 'dagre',
                    ranksep: 20,
                    controlPoints: true,
                },
                defaultNode: {
                    type: 'sql',
                },
                defaultEdge: {
                    type: 'polyline',
                    style: {
                        radius: 10,
                        offset: 25,
                        endArrow: true,
                        lineWidth: 1,
                        stroke: '#C2C8D5',
                    },
                },
                nodeStateStyles: {
                    selected: {
                        stroke: '#d9d9d9',
                        fill: '#5394ef',
                    },
                },
                modes: {
                    default: [
                        'drag-canvas',
                        'zoom-canvas',
                    ],
                },
                fitView: true,
            });
            graph.value.render();
        }
        const handleResize = () => {
            graph.value.clear();
            const edges = props.data.filter((k: any) => k.beforeTaskIds).map((k: any) => {
                const _b = k.beforeTaskIds.split(',');
                return _b.map((m: any) => ({ target: `${k.id}`, source: m }))
            }).flat()
            const _data = {
                nodes: props.data.map((k: any) => ({ id: `${k.id}`, name: k.taskName || '', operateStatus: k.operateStatus || 0, conf: k.taskName })),
                edges,
            }
            if (!_data.nodes.length) return;
            setTimeout(() => {
                graph.value.data(JSON.parse(JSON.stringify(_data)));
                graph.value.render();
                graph.value.changeSize(container.value.scrollWidth, container.value.scrollHeight);
            }, 100)
        }
        return {
            handleResize
        }
    },
})

</script>
<style lang="less" scoped>
.flow-container {
    width: 100%;
    height: 400px;

    /deep/canvas {
        cursor: grab !important;
    }
}
</style>
<style lang="less">
.g6-tooltip {
    border-radius: 6px;
    font-size: 12px;
    color: #fff;
    background-color: #000;
    padding: 2px 8px;
    text-align: center;
}
</style>
