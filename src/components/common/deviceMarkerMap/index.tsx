import { defineComponent, onMounted, watch, ref, nextTick } from "vue";
import { Mo<PERSON>, Button } from "view-ui-plus";
import useModal from "@/hooks/modal";
import AMapLoader from "@amap/amap-jsapi-loader";
import "./index.less";
export default defineComponent({
    name: "deviceDropDownSelect",
    props: {
        makers: {
            type: Array,
            default: () => [],
        },
        attrKey: {
            // 用那个字段标志图标类型
            type: String,
            default: "",
        },
    },
    emits: ["close-modal"],
    setup(props, ctx) {
        const { modalStatus } = useModal();
        watch(
            () => modalStatus.value,
            () => {
                if (!modalStatus.value) {
                    ctx.emit("close-modal");
                }
            }
        );
        // 初始化地图
        const map = ref<any>(null);
        const Amap = ref<any>(null);
        const ininMap = () => {
            AMapLoader.load({
                key: "ea53a5291f8f7c1f215ba20930ea9866",
                version: "2.0",
            }).then((AMap) => {
                Amap.value = AMap;
                map.value = new AMap.Map("map-cont", {
                    resizeEnable: true,
                    // mapStyle: 'https://geohub.amap.com/mapstyle/clone?id=20d3f4f1e43c80b9c68c30fb8a1ab94b',
                    zoom: 14,
                    center: [114.61, 30.45],
                });
                updateMapIcon();
            });
        };
        const updateMapIcon = () => {
            // 清除地图上的图标
            map.value.clearMap();
            props.makers.forEach((item: any) => {
                const icon = new Amap.value.Marker({
                    position: [item.objX, item.objY],
                    // offset: new Amap.value.Pixel(-26, -26),
                });
                map.value.add([icon]);
            });

            nextTick(() => {
                if (props.makers.length == 1) {
                    map.value.setCenter([
                        props.makers[0]!['objX'] || 114.61,
                        props.makers[0]!['objY'] || 30.45,
                    ]);
                } else {
                    map.value.setFitView();
                }
            });
        };
        onMounted(() => {
            ininMap();
        });

        return () => (
            <Modal footer-hide v-model={modalStatus.value} width={800} title="位置信息">
                <div class="map-container" id="map-cont"></div>
            </Modal>
        );
    },
});
