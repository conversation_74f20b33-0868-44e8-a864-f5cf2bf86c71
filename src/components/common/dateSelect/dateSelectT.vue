<template>
    <div class="search-info">
        <div class="today" v-show="showToday">
            <div :class="{ on: m.dateType === 3 }" @click="clickToday" class="box">今日</div>
        </div>
        <div class="date-type-sel" v-show="showTabSel">
            <div
                v-for="(item, index) in m.timeList"
                :class="{ on: m.dateType === index }"
                @click="changeDateType(index)"
                class="box"
            >
                {{ item }}
            </div>
        </div>
        <div class="time-range-box">
            <DatePicker
                :disabled="m.dateType === 3"
                :type="m.timeTypeList[m.dateType]"
                v-model="startTime[m.dateType]"
                placement="bottom-start"
                placeholder="开始日期"
                style="width: 120px"
                :clearable="false"
                @on-change="handleChangeTime"
            />
            <div class="heng">-</div>
            <DatePicker
                :disabled="m.dateType === 3"
                :type="m.timeTypeList[m.dateType]"
                v-model="endTime[m.dateType]"
                placement="bottom-start"
                placeholder="结束日期"
                style="width: 120px"
                :clearable="false"
                @on-change="handleChangeTime"
            />
        </div>
    </div>
</template>

<script>
import { getDate } from "wei-util";
export default {
    name: "dateSelectT",
    props: {
        showToday: { default: false },
        showTabSel: { default: true },
        defaultDate: {
            default: "nd",
        },
    },
    data() {
        return {
            m: {
                timeList: ["日", "月", "年"],
                timeTypeList: ["date", "month", "year"],
                dateType: 0,
            },
            startTime: {
                0: "",
                1: "",
                2: "",
                3:getDate({ timeType: "start" })
            },
            endTime: {
                0: "",
                1: "",
                2: "",
                3:getDate({ timeType: "end" })
            },
        };
    },
    created() {
        let arr = this.$Util.getDateRangeS(new Date(), this.defaultDate, "YYYY-MM-DD hh:mm:ss");
        this.startTime[this.m.dateType] = arr[0];
        this.endTime[this.m.dateType] = arr[1];
        let me = new Date();
        this.endTime[1] = new Date(me.setMonth(me.getMonth() + 1, 0));
        this.startTime[1] = new Date(me.setMonth(me.getMonth() - 5, 1));
        let ye = new Date();
        this.endTime[2] = new Date(ye.setMonth(12, 0));
        let year = ye.getFullYear();
        this.startTime[2] = new Date(ye.setFullYear(year - 1, 0, 1));
        this.emitChange(0);
    },
    methods: {
        clickToday() {
            this.m.dateType = 3;
            this.emitChange(3);
        },
        changeDateType(index) {
            this.todayActive = false;
            this.m.dateType = index;
            this.emitChange(index);
        },
        emitChange(index) {
            if (!this.startTime[this.m.dateType]) {
                return;
            }
            let obj = {
                startTime: this.$Util.formatDate(this.startTime[this.m.dateType], "yyyy-MM-DD") +
                      " 00:00:00",
                endTime: this.$Util.formatDate(this.endTime[this.m.dateType], "yyyy-MM-DD") +
                      " 23:59:59",
                dateType: this.m.dateType,
            };
            this.$emit("on-change", obj,index);
        },
        handleChangeTime() {
            let _s = this.startTime[this.m.dateType];
            let _e = this.endTime[this.m.dateType];
            if (!_s || !_e) {
                return;
            }
            if (new Date(_e).getTime() < new Date(_s).getTime()) {
                // 如果开始时间大于结束时间，将2个时间调换
                _e = _s;
                _s = this.endTime[this.m.dateType];
            }
            if (this.m.dateType == 1) {
                // 当月第一天，和当月最后一天
                this.startTime[this.m.dateType] = new Date(_s.setDate(1));
                this.endTime[this.m.dateType] = new Date(_e.setMonth(_e.getMonth() + 1, 0));
            } else if (this.m.dateType == 2) {
                // 当年第一个，和当年最后一天
                this.startTime[this.m.dateType] = new Date(_s.setMonth(0, 1));
                this.endTime[this.m.dateType] = new Date(_e.setMonth(12, 0));
            } else {
                this.startTime[this.m.dateType] = _s;
                this.endTime[this.m.dateType] = _e;
            }
            this.emitChange(this.m.dateType);
        },
    },
};
</script>

<style lang="less" scoped>
.search-info {
    display: flex;
    align-items: center;
    .today {
        cursor: pointer;
        background: @fill-2;
        padding: 3px;
        border-radius: 2px;
        height: 28px;
        margin-right: 12px;
        .box {
            flex: 1;
            text-align: center;
            line-height: 22px;
            color: rgba(78, 98, 126, 1);
            position: relative;
            cursor: pointer;
        }
        .on {
            background: #fff;
            border-radius: 2px;
            color: rgba(22, 93, 255, 1);

            &:before,
            & + .box:before {
                display: none;
            }
        }
    }
    .date-type-sel {
        display: flex;
        align-items: center;
        background: @fill-2;
        width: 120px;
        height: 28px;
        border-radius: 2px;
        padding: 3px;

        .box {
            flex: 1;
            text-align: center;
            line-height: 22px;
            color: rgba(78, 98, 126, 1);
            position: relative;
            cursor: pointer;

            &:before {
                position: absolute;
                content: "";
                width: 1px;
                height: 14px;
                background: rgba(224, 230, 241, 1);
                left: 0;
                top: 0;
                bottom: 0;
                margin: auto;
            }

            &:first-child:before {
                display: none;
            }

            &.on {
                background: #fff;
                border-radius: 2px;
                color: rgba(22, 93, 255, 1);

                &:before,
                & + .box:before {
                    display: none;
                }
            }
        }
    }

    .time-range-box {
        display: flex;
        align-items: center;
        margin-left: 16px;

        .heng {
            margin: 0 8px;
        }
    }
}
</style>
