<!-- eslint-disable vue/require-v-for-key -->
<template>
    <div class="search-info">
        <div class="time-range-box">
            <DatePicker
                :disabled="false"
                :type="m.timeTypeList[m.dateType]"
                v-model="startTime"
                placement="bottom-start"
                placeholder="开始日期"
                style="width: 120px"
                :clearable="false"
                @on-change="handleChangeTime"
            />
            <div class="heng">-</div>
            <DatePicker
                :disabled="false"
                :type="m.timeTypeList[m.dateType]"
                v-model="endTime"
                placement="bottom-start"
                placeholder="结束日期"
                style="width: 120px"
                :clearable="false"
                @on-change="handleChangeTime"
            />
        </div>
    </div>
</template>

<script>
import { getDate } from 'wei-util';
export default {
    name: "dateSelectM",
    props: {
        defaultDate: {
            default: 'nd',
        },
    },
    data() {
        const today = new Date();
        const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
        const endtOfMonth = new Date(today.getFullYear(), today.getMonth() + 1, 0);
        return {
            m: {
                timeList: ['月'],
                timeTypeList: ['month'],
                dateType: 0,
            },
            startTime: '',
            endTime: '',
        };
    },
    created() {
        let me = new Date();
        this.endTime = new Date(me.setMonth(me.getMonth() + 1, 0));
        this.startTime = new Date(me.setMonth(me.getMonth() - 5, 1));
        this.emitChange();
    },
    methods: {
        emitChange() {
            let obj = {
                startTime: this.$Util.formatDate(this.startTime, "yyyy-MM-DD") +
                      " 00:00:00",
                endTime: this.$Util.formatDate(this.endTime, "yyyy-MM-DD") +
                      " 23:59:59",
                dateType: this.m.dateType,
            };
            this.$emit("on-change", obj);
        },
        handleChangeTime() {
            let _s = this.startTime;
            let _e = this.endTime;
            if (!_s || !_e) {
                return;
            }
            if (new Date(_e).getTime() < new Date(_s).getTime()) {
                // 如果开始时间大于结束时间，将2个时间调换
                _e = _s;
                _s = this.endTime;
            }
            // 当月第一天，和当月最后一天
            this.startTime = new Date(_s.setDate(1));
            this.endTime = new Date(_e.setMonth(_e.getMonth() + 1, 0));
            this.emitChange(this.m.dateType);
        },
    },
};
</script>

<style lang="less" scoped>
.search-info {
    display: flex;
    align-items: center;
    .date-type-sel {
        display: flex;
        align-items: center;
        background: @fill-2;
        width: 120px;
        height: 28px;
        border-radius: 2px;
        padding: 3px;

        .box {
            flex: 1;
            text-align: center;
            line-height: 22px;
            color: rgba(78, 98, 126, 1);
            position: relative;
            cursor: pointer;

            &:before {
                position: absolute;
                content: "";
                width: 1px;
                height: 14px;
                background: rgba(224, 230, 241, 1);
                left: 0;
                top: 0;
                bottom: 0;
                margin: auto;
            }

            &:first-child:before {
                display: none;
            }

            &.on {
                background: #fff;
                border-radius: 2px;
                color: rgba(22, 93, 255, 1);

                &:before,
                & + .box:before {
                    display: none;
                }
            }
        }
    }

    .time-range-box {
        display: flex;
        align-items: center;
        margin-left: 16px;

        .heng {
            margin: 0 8px;
        }
    }
}
</style>
