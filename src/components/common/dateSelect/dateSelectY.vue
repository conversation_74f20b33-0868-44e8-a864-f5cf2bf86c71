<template>
    <div class="date-select" :class="{hideDate: hideDate, hideTab: m.hideTab }">
        <div class="tab-sel">
            <div
                v-for="(item, index) in tabList" @click="chooseTab(index)"
                :class="{on: m.curIndex === index}"
                class="box">{{ item.name }}</div>
        </div>
        <DatePicker
            :open="m.showDateFlag"
            v-model="m.chooseDate"
            type="daterange"
            clearable confirm
            placement="bottom-end"
            format="yyyy-MM-dd"
            @on-change="handleChange"
            @on-ok="handleOk"
            @on-clear="handleClear"
            @on-clickoutside="handleHide"
        >
            <div class="custom-box" @click="handleShow">
                <div class="val" v-if="m.chooseDate[0]">
                    <Time :time="m.chooseDate[0]" type="date" /> -
                    <Time :time="m.chooseDate[1]" type="date" />
                </div>
                <div class="placeholder" v-else>开始日期 - 结束日期</div>

                <div class="cal">
                    <Icon custom="iconfont icon-calendar" />
                </div>
                <div class="clear" v-if="m.chooseDate[0]" @click.stop="clearDate">
                    <Icon custom="iconfont icon-close-circle" />
                </div>
            </div>
        </DatePicker>
    </div>
</template>

<script>
const tabListDefault = [
    { name: '今日', key: 'today' },
    { name: '近7日', key: 'd7' },
    { name: '近30日', key: 'd30' }
]
export default {
    name: 'dateSelectY',
    props: {
        hideDate: { default: false }, // 隐藏时间选择框
        tabList: { default() { return tabListDefault } },
        defaultIndex: { default: 0 }
    },
    emits: ['on-change'],
    data() {
        let curIndex = this.defaultIndex
        return {
            m: {
                curIndex,
                curObj: {},
                showDateFlag: false,
                chooseDate: [],
                curDate: [],
                hideTab: false,
                isClear: false
            }
        }
    },
    created() {
        this.chooseTab(this.m.curIndex)
    },
    methods: {
        initTime() {
            let arr = []
            let d = new Date()
            d.setDate(d.getDate() - 1)
            switch (this.m.curObj.key) {
                case 'today':
                    // 今日
                    arr = this.$Util.getDateRangeS(new Date(), 'h', 'YYYY-MM-DD HH:mm:ss')
                    break;
                case 'd7':
                    // 近7日
                    arr = this.$Util.getDateRangeS(d, 'nd', 'YYYY-MM-DD HH:mm:ss')
                    break;
                case 'd30':
                    // 近30日
                    arr = this.$Util.getDateRangeS(d, 'nm', 'YYYY-MM-DD HH:mm:ss')
                    break;
                case 'd':
                    // 本周
                    arr = this.$Util.getDateRangeS(d, 'd', 'YYYY-MM-DD HH:mm:ss')
                    break;
                case 'm':
                    // 本月
                    arr = this.$Util.getDateRangeS(d, 'm', 'YYYY-MM-DD HH:mm:ss')
                    break;
                case 'y':
                    // 本年
                    arr = this.$Util.getDateRangeS(d, 'y', 'YYYY-MM-DD HH:mm:ss')
                    break;
            }
            this.m.curDate = arr
            if (arr.length > 0) {
                this.$emit('on-change', this.m.curDate, this.m.curObj)
            }
        },
        chooseTab(index) {
            this.m.curObj = this.tabList[index]
            this.m.curIndex = index
            this.initTime()
        },
        handleChange(val) {
            if (this.m.isClear) {
                this.m.isClear = false
                this.m.chooseDate = []
                this.handleHide()
                return
            }
            if (val && val[0]) {
                let sArr = val[0].split(' ')
                if (!sArr[1]) { // 如果为空，加上时分秒
                    val[0] = sArr[0] + ' 00:00:00'
                }
                let eArr = val[1].split(' ')
                if (!eArr[1] || (eArr[1] === '00:00:00')) {
                    // 如果为空或者通过日期修改时，时分秒改为' 23:59:59'
                    val[1] = eArr[0] + ' 23:59:59'
                }
            }
            this.m.chooseDate = val
        },
        handleOk() {
            this.handleHide()
        },
        clearDate() {
            this.m.chooseDate = []
            this.handleHide()
        },
        handleClear() {
            this.m.isClear = true
        },
        handleShow() {
            if (this.hideDate && !this.m.hideTab) {
                this.m.hideTab = true
            }
            this.m.showDateFlag = true
        },
        handleHide() {
            if (!this.m.chooseDate[0]) {
                this.m.hideTab = false
                this.chooseTab(0)
            } else {
                this.m.curIndex = ''
                this.m.curObj = {}
                this.$emit('on-change', this.m.chooseDate, this.m.curObj)
            }
            this.m.showDateFlag = false
        }
    }
}
</script>

<style lang="less" scoped>
.date-select{
    display: flex;
    align-items: center;

    .tab-sel{
        background: @fill-2;
        border-radius: 2px;
        display: flex;
        align-items: center;
        padding: 3px;
        margin-right: 16px;
        .box{
            padding: 0 12px;
            line-height: 22px;
            border-radius: 2px;
            cursor: pointer;
            color: @text-color;
            font-weight: 400;
            font-size: 14px;
            &.on{
                background: #fff;
                color: @primary-color;
                font-weight: 600;
            }
        }
    }
    .custom-box{
        width: 240px;
        display: flex;
        align-items: center;
        height: 28px;
        justify-content: center;
        border: 1px solid @line-2;
        border-radius: 2px;
        position: relative;
        font-size: 14px;
        .placeholder{
            color: @text-3-1;
            padding-right: 40px;
            padding-left: 10px;
        }
        .val{
            padding-right: 40px;
            padding-left: 10px;
        }
        .clear,
        .cal{
            position: absolute;
            width: 32px;
            text-align: center;
            height: 26px;
            top: 0px;
            right: 0;
            line-height: 26px;
        }
        .clear{
            display: none;
            background: #fff;
        }
        &:hover{
            .clear{
                display: block;
            }
        }
    }
    &.hideDate{
        .custom-box{
            width: 34px;
            cursor: pointer;
            // border-color: transparent;
            .placeholder{
                display: none;
            }
            .cal{
                background: @fill-2;
            }
        }
        &.hideTab{
            .tab-sel{
                display: none;
            }
            .custom-box{
                width: 240px;
                .placeholder{
                    display: block;
                }
                .cal{
                    background: none;
                }
            }
        }
    }
}
</style>
