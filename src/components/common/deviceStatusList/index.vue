<template>
    <div>
        <Row :gutter="gutter" :class="{vertical: vertical}">
            <Col span="8">
                <s-label label="在线状态">
                    <template #value>
                        <onlineStatus :value="status" />
                    </template>
                </s-label>
            </Col>
            <Col span="8" v-for="(item,index) in list" :key="index">
                <s-label :label="item.propName">
                    <template #value>
                        <div v-if="$slots[item.prop]">
                            <slot :name="item.prop" :item="item"></slot>
                        </div>
                        <div v-else class="ellipsis-1">
                            {{ getValue(item) || '--' }}
                        </div>
                    </template>
                </s-label>
            </Col>
            <Col span="8" v-if="updateTime">
                <s-label label="更新时间" :value="updateTime" />
            </Col>
            <slot name="_extra"></slot>
        </Row>
    </div>
</template>

<script>
/** 自定义显示状态使用： <template #has_car="{item}"></template> */
import { formatDeviceValue } from '@/utils/formatDeviceValue'
export default {
    name: 'DeviceStatusRealtime',
    props: {
        list: { type: Array, default() { return [] } },
        status: { type: [Number, String] },
        attrTime: { default: '' },
        vertical: { default: false }, // 是否垂直方向
        gutter: { default: 80 }
    },
    data() {
        return {
        }
    },
    computed: {
        updateTime() {
            if (this.attrTime) {
                return this.attrTime
            }
            if (this.list.length > 0) {
                return this.list[0].modifyTime
            }
            return ''
        }
    },
    methods: {
        getValue(item) {
            return formatDeviceValue(item, 2)
        }
    }
}
</script>

<style lang="less" scoped>
.vertical{
    .ivu-col-span-8{
        width: 100%;
        flex: inherit;
        max-width: 100%;
    }
}
</style>
