<script lang="ts" setup>
import { ref, watch, computed, onMounted } from 'vue'
import { debounce } from 'wei-util'
const props = defineProps({
  list: {
    type: Array,
    default: () => []
  },
  type: { default: 'height' },
  rows: { default: 3 }, // 竖向滚动必传
  columns: { default: 3 }, // 横向滚动必传
  itemHeight: {
    default: 200
  },
  itemWidth: {
    default: 0
  }
})
const height = ref<string>(props.rows * props.itemHeight + 'px')
const width = ref<string>(props.columns * props.itemWidth + 'px')
const heightWidthMap = {
  height,
  width
}
const heightOrWidthNumber = Number(heightWidthMap[props.type].value.replace('px', ''))
const contentHeightOrWidth = ref<string>('')
const pageSize = ref<number>(0)
watch(() => props.list, () => {
  setScrollBoxHeight()
}, { deep: true })
onMounted(() => {
  computedData()
})
const itemHWMap = {
  height: 'itemHeight',
  width: 'itemWidth'
}
const topOrLeftMap = {
  height: 'top',
  width: 'left'
}
const key = itemHWMap[props.type]
const topOrLeftKey = topOrLeftMap[props.type]
const setScrollBoxHeight = () => {
  contentHeightOrWidth.value = props[key] * props.list.length + 'px'
  pageSize.value = (heightOrWidthNumber / props[key]) || 0
}
const startIndex = ref<number>(0)
const onScroll = (e: any) => {
  console.log(e, '(e.target');
  const scrollTypeMap = {
    height: 'scrollTop',
    width: 'scrollLeft',
  }
  startIndex.value = Math.floor(e.target[scrollTypeMap[props.type]] / props[key]);
}
const sliceList = computed(() => {
  return props.list.slice(startIndex.value, startIndex.value + pageSize.value + 1).map((item: any, index: number) => {
    const originDataIndex = startIndex.value + index
    const itemTypeIsObject = toString.call(item) === '[object Object]'
    if (itemTypeIsObject) {
      return {
        ...item,
        originDataIndex, // 该项在源数据中的下标
        itemStyle: `height:${props.itemHeight}px;width:${props.itemWidth ? props.itemWidth + 'px' : '100%'};position:absolute; ${topOrLeftKey}:${startIndex.value * props[key] + index * props[key]}px;`,//left:${index % rowNumber.value * props.itemWidth}px
      }
    } else {
      return {
        data: item, //
        originDataIndex, // 该项在源数据中的下标
        itemStyle: `height:${props.itemHeight}px;width:${props.itemWidth ? props.itemWidth + 'px' : '100%'};position:absolute; ${topOrLeftKey}:${startIndex.value * props[key] + index * props[key]}px;`,//left:${index % rowNumber.value * props.itemWidth}px
      }
    }
  })
})
const rowNumber = ref<number>(0) //每行最多排放多少数量的数据
const computedData = () => {
  window.addEventListener('resize', debounce(() => {
    const contentDom = document.querySelector('.content')
    const contentWidth = contentDom?.clientWidth || 0
    const rowNum = Math.floor(contentWidth / props.itemWidth)
    rowNumber.value = rowNum
  }, 1000))
}
const handleStyle = computed(() => {
  return `${props.type}:${contentHeightOrWidth.value}`;
})
const handleContainerStyle = computed(() => {
  const scrollType = {
    height: 'overflow-y',
    width: 'overflow-x'
  }
  return `${scrollType[props.type]}:auto;${props.type}:${heightWidthMap[props.type].value};${props.type === 'width' ? 'height' : ''}:${props.itemWidth ? props.itemWidth + 'px' : '100%'}`
})
</script>

<template>
  <div class="container" :style="handleContainerStyle" @scroll="onScroll">
    <div class="scrollBar" :style="handleStyle"></div>
    <div class="content">
      <slot name="content" :data="sliceList"></slot>
    </div>
  </div>
</template>

<style lang="less" scoped>
.container {
  position: relative;

  .scrollBar {}

  .content {}
}
</style>