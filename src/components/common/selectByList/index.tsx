import { defineComponent, onUpdated, ref, nextTick, computed, watch, watchEffect } from "vue";
import { Select, Option } from "view-ui-plus";
import request from "@/utils/request";
import useDebounce from "@/hooks/useDebounce";

interface IList {
    value: string;
    label: string;
    todayCompleted?: string;
}

export default defineComponent({
    name: "selectedByList",
    props: {
        // 入参
        type: {
            type: [Number, String],
            default: 1,
        },
        // 查列表接口传的额外入参
        params: {
            type: Object,
        },
        // 与外部value双向绑定
        modelValue: {
            type: [Number, String, Array],
            default: 0,
        },
        // 查询列表url
        url: {
            type: String,
        },
        // 显示的字段名称
        labelName: {
            type: String,
        },
        // 绑定值的字段名称
        valueName: {
            type: String,
        },
        // 同select组件
        allowCreate: {
            type: Boolean,
        },
        // 多选
        multiple: {
            type: Boolean,
        },
        placeholder: {
            type: String,
        },
        // 列表过滤回调
        filterCb: {
            type: Function,
        },
        suffix: {
            default: `''`,
        },
    },
    emits: ["update:modelValue", "onChange"],
    setup(props: any, { emit, attrs, slots }) {
        const requestMethod = (data: any) => {
            return request({
                url: props.url,
                method: "post",
                data,
            });
        };
        // url
        const list = ref<any[]>([]);
        const getList = async () => {
            const params = {
                page: {
                    current: 1,
                    size: -1,
                },
                customQueryParams: {
                    ...props.params,
                    type: props.type,
                },
            };
            const res = await requestMethod(params);
            const { data, success }: { success: boolean; data: recordsResponse<any[]> } =
                res as unknown as HttpResponse<recordsResponse<any[]>>;
            // 成功
            if (success) {
                const labelName = props.labelName || "name";
                const valueName = props.valueName || "id";
                const oldResultList = data.records;
                const filterResult = props.filterCb && props.filterCb(oldResultList);
                if (filterResult) {
                    const curItem = oldResultList.find(
                        (item) => item[valueName] + "" == props.modelValue
                    );
                    if (curItem) {
                        filterResult?.unshift(curItem);
                    }
                }
                list.value = (filterResult || oldResultList).map((item: any) => {
                    return {
                        ...item,
                        label: item[labelName] + "",
                        value: item[valueName] + "",
                    };
                });
            }
        };
        // 当入参发生改变的时候重新拿数据
        watchEffect(getList);

        const selectVal = computed({
            get() {
                if (props.modelValue instanceof Array) {
                    return props.modelValue.map((item: any) => item + "");
                } else {
                    return props.modelValue + "";
                }
            },
            set(val) {
                let data;
                if (typeof val === "string") {
                    data = list.value.find((k) => k.value == val);
                } else if (val instanceof Array) {
                    data = [];
                    val?.forEach((item) => {
                        const resultVal = list.value.find((k) => k.value == item);
                        if (resultVal) {
                            data.push({ ...resultVal });
                        }
                    });
                }
                emit("update:modelValue", val ? val : "");
                emit("onChange", data || {});
            },
        });
        // 如果允许创建数据，就将props.modelValue的值与list同步
        const syncList = (value: string | number | unknown[]) => {
            if (!value) return;
            const data = list.value.find((item) => item.value == value);
            if (props.allowCreate) {
                if (!data) {
                    list.value.push({
                        label: value + "",
                        value: value + "",
                    });
                    return {
                        label: value + "",
                        value: value + "",
                    };
                }
            }
            return data;
        };
        onUpdated(() => {
            if (props.modelValue) syncList(props.modelValue);
        });
        const handlerOpenChange = (val: any) => {
            console.log("openChange", val);
        };
        const searchText = ref("");
        const handleSearch = (data: string) => {
            useDebounce(() => {
                searchText.value = data;
            });
        };
        return () => (
            <Select
                {...attrs}
                v-model={selectVal.value}
                placeholder={props.placeholder || "请输入"}
                allow-create={props.allowCreate}
                multiple={props.multiple}
                clearable
                filterable={true}
                filter-by-label={true}
                onOnQueryChange={handleSearch}
            >
                {list.value.map((item, index) => (
                    <Option
                        value={item.value}
                        label={item.label}
                        v-show={item.label.includes(searchText.value)}
                    >
                        {slots.default
                            ? slots.default({ item, index, suffix: eval(props.suffix) })
                            : item.label + eval(props.suffix)}
                    </Option>
                ))}
            </Select>
        );
    },
});
