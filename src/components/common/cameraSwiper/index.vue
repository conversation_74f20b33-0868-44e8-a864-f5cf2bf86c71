<template>
    <div class="camera-swiper">
        <swiper
            :modules="modules"
            :slides-per-view="4.5"
            :space-between="16"
            :navigation="navigation"
            @swiper="onSwiper"
            @slideChange="onSlideChange"
        >
            <swiper-slide v-for="item in cameraList" @click="handelVideo(item)" :key="item.id">
                <div class="camera-item">
                    <div class="img-box">
                        <img :src="item.src" alt="">
                        <div class="cover">
                            <Icon custom="iconfont icon-play-circle-fill1" />
                        </div>
                    </div>
                    <div class="bot-box">
                        <div class="name">{{ item.sbmc }}:</div>
                        <online-status :value="item.status" />
                    </div>

                </div>
            </swiper-slide>
        </swiper>
        <div class="swiper-button-prev" v-show="cameraList.length > 4">
            <Icon custom="iconfont icon-left" />
        </div>
        <div class="swiper-button-next" v-show="cameraList.length > 4">
            <Icon custom="iconfont icon-right" />
        </div>

    </div>
    <no-data v-show="cameraList.length === 0" />
    <videoModal ref="videoModalRef" />
</template>
<script>
import { ref, watch } from 'vue'
import { Navigation } from 'swiper';
import { SwiperSlide } from 'swiper/vue/swiper-slide';
import { Swiper } from 'swiper/vue/swiper';
import 'swiper/swiper.css';
import videoModal from '../video/videoModal'

export default {
    props: {
        cameraList: { default() { return [] } }
    },
    components: {
        Swiper,
        SwiperSlide,
        videoModal
    },
    setup(props) {
        const showVideoFlag = ref(false)
        const navigation = ref({
            nextEl: '.swiper-button-next',
            prevEl: '.swiper-button-prev',
        })
        const swiperMy = ref(null)
        const videoModalRef = ref()
        const onSwiper = (swiper) => {
            swiperMy.value = swiper
        };
        watch(() => props.cameraList, () => {
            swiperMy.value && swiperMy.value.updateSlides()
        })
        const onSlideChange = () => {
        };
        const handelVideo = (item) => {
            videoModalRef.value.init(item)
        }
        return {
            navigation,
            swiperMy,
            showVideoFlag,
            videoModalRef,
            onSwiper,
            onSlideChange,
            handelVideo,
            modules: [Navigation],
        };
    },
};
</script>
<style scoped lang="less">
.camera-swiper{
    padding: 0 40px;
    position: relative;
}
.swiper-button-prev,
.swiper-button-next{
    position: absolute;
    width: 24px;
    height: 40px;
    top: 67px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: @fill-2;
    border-radius: 4px;
    color: @text-color;
    cursor: pointer;
    &:hover{
        color: @primary-color;
    }
    &.swiper-button-disabled{
        background: @fill-1;
        color: @text-2-1;
        cursor: not-allowed;
        display: none;
        &:hover{
            color: @text-2-1;
        }
    }
}
.swiper-button-prev{
    left: 0;
}
.swiper-button-next{
    right: 0;
}
.camera-item{
    cursor: pointer;
    .img-box{
        width: 100%;
        height: 170px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 8px;
        position: relative;
        img{
            width: 100%;
            height: 100%;
            display: block;
        }
        .cover{
            position: absolute;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 100%;
            height: 100%;
            top: 0;
            left: 0;
            .ivu-icon{
                font-size: 28px;
                color: #fff;
            }
        }
    }
    .bot-box{
        display: flex;
        align-items: center;
        .name{
            line-height: 22px;
            color: @text-3-1;
            margin-right: 8px;
        }
    }
}
</style>
