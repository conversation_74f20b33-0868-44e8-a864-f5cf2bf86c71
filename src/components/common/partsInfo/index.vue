<template>
    <div :class="isEdit ? '' : 'view-form'">
        <Row :gutter="80">
            <Col span="8" v-if="!hiddenColumn.includes('objId')">
                <FormItem :label="label.bsm" v-if="monitorPoint && monitorPoint?.bsm">
                    <Input v-show="isEdit" :model-value="monitorPoint?.bsm" disabled :placeholder="placeholder"></Input>
                    <TooltipAutoShow v-show="!isEdit" :content="monitorPoint?.bsm" />
                </FormItem>
                <FormItem :label="label.objId?label.objId:label.bsm" v-else>
                    <Input v-show="isEdit" :model-value="objInfo?.objId" disabled :placeholder="placeholder"></Input>
                    <TooltipAutoShow v-show="!isEdit" :content="objInfo?.objId" />
                </FormItem>
            </Col>
            <Col span="8" v-if="!hiddenColumn.includes('objName')">
                <FormItem :label="label.dwmc" v-if="monitorPoint && monitorPoint?.dwmc">
                    <Input v-show="isEdit" :model-value="monitorPoint?.dwmc" disabled :placeholder="placeholder"></Input>
                    <TooltipAutoShow v-show="!isEdit" :content="monitorPoint?.dwmc" />
                </FormItem>
                <FormItem :label="label.objName?label.objName:label.dwmc" v-else>
                    <Input v-show="isEdit" :model-value="objInfo?.objName" disabled :placeholder="placeholder"></Input>
                    <TooltipAutoShow v-show="!isEdit" :content="objInfo?.objName" />
                </FormItem>
            </Col>
            <Col span="8" v-if="!hiddenColumn.includes('secondObjCategoryName')">
            <!-- <FormItem :label="label.dwlx">
                <Input v-show="isEdit" :model-value="$store.getters.dictionary.obj_category[monitorPoint?.dwlx]" disabled :placeholder="placeholder"></Input>
                <TooltipAutoShow v-show="!isEdit" :content="$store.getters.dictionary.obj_category[monitorPoint?.dwlx]" />
            </FormItem> -->
                <FormItem :label="label.dwlx" v-if="monitorPoint && monitorPoint?.dwlx">
                    <Input v-show="isEdit" :model-value="monitorPoint?.dwlx" disabled :placeholder="placeholder"></Input>
                    <TooltipAutoShow v-show="!isEdit" :content="monitorPoint?.dwlx" />
                </FormItem>
                <FormItem :label="label.categoryName?label.categoryName:label.dwlx" v-else>
                    <Input v-show="isEdit" :model-value="objInfo?.secondObjCategoryName" disabled :placeholder="placeholder"></Input>
                    <TooltipAutoShow v-show="!isEdit" :content="objInfo?.secondObjCategoryName" />
                </FormItem>
            </Col>
            <Col span="8" v-if="!hiddenColumn.includes('deptName')">
            <FormItem label="主管部门">
                <Input v-show="isEdit" :model-value="objInfo?.deptName" disabled :placeholder="placeholder"></Input>
                <TooltipAutoShow v-show="!isEdit" :content="objInfo?.deptName" />
            </FormItem>
            </Col>
            <Col span="8" v-if="!hiddenColumn.includes('ownerEnterpriseName')">
            <FormItem label="权属单位">
                <Input v-show="isEdit" :model-value="objInfo?.ownerEnterpriseName" disabled :placeholder="placeholder"></Input>
                <TooltipAutoShow v-show="!isEdit" :content="objInfo?.ownerEnterpriseName" />
            </FormItem>
            </Col>
            <Col span="8" v-if="!hiddenColumn.includes('opEnterpricseName')">
            <FormItem label="养护单位">
                <Input v-show="isEdit" :model-value="objInfo?.opEnterpricseName" disabled :placeholder="placeholder"></Input>
                <TooltipAutoShow v-show="!isEdit" :content="objInfo?.opEnterpricseName" />
            </FormItem>
            </Col>
            <Col span="8" v-if="!hiddenColumn.includes('objState')">
            <FormItem label="部件状态">
                <Input :model-value="$enumeration.objState[objInfo?.objState]" disabled :placeholder="placeholder"></Input>
            </FormItem>
            </Col>
            <Col span="8" v-if="!hiddenColumn.includes('contactPerson')">
            <FormItem label="联 系 人" >
                <Input v-show="isEdit" :model-value="objInfo?.contactPerson" disabled :placeholder="placeholder"></Input>
                <TooltipAutoShow v-show="!isEdit" :content="objInfo?.contactPerson" />
            </FormItem>
            </Col>
            <Col span="8" v-if="!hiddenColumn.includes('contactPhone')">
            <FormItem label="联系电话" >
                <Input v-show="isEdit" :model-value="objInfo?.contactPhone" disabled :placeholder="placeholder"></Input>
                <TooltipAutoShow v-show="!isEdit" :content="objInfo?.contactPhone" />
            </FormItem>
            </Col>
            <Col span="8" v-if="!hiddenColumn.includes('initDate')">
            <FormItem label="初始时间" >
                <Input v-show="isEdit" :model-value="objInfo?.initDate" disabled :placeholder="placeholder"></Input>
                <TooltipAutoShow v-show="!isEdit" :content="objInfo?.initDate" />
            </FormItem>
            </Col>
            <Col span="8" v-if="!hiddenColumn.includes('modifyDate')">
            <FormItem label="变更时间" >
                <Input v-show="isEdit" :model-value="objInfo?.modifyDate" disabled :placeholder="placeholder"></Input>
                <TooltipAutoShow v-show="!isEdit" :content="objInfo?.modifyDate" />
            </FormItem>
            </Col>
            <Col span="8" v-if="!hiddenColumn.includes('objX')">
            <FormItem label="经 纬 度" @click="setModal" class="pointer">
                <div class="show-map" v-if="!isEdit">
                    <Icon type="ios-pin-outline" v-if="monitorPoint && monitorPoint?.zbx" />
                    <Icon type="ios-pin-outline" v-if="!(monitorPoint && monitorPoint?.zbx) && objInfo?.objX" />
                </div>
                <Input v-show="isEdit" :model-value="objX" disabled :placeholder="placeholder"></Input>
                <TooltipAutoShow v-show="!isEdit" :content="objX" />
            </FormItem>
            </Col>
            <Col span="8" v-if="!hiddenColumn.includes('areaPath')">
                <FormItem label="区域位置">
                    <Input v-show="isEdit" :model-value="areaPath" disabled :placeholder="placeholder"></Input>
                    <TooltipAutoShow v-show="!isEdit" :content="areaPath" />
                </FormItem>
                </Col>
            <Col span="8" v-for="(item, index) in addColumns" v-show="!isEdit">
                <FormItem :label="item.label">
                    <TooltipAutoShow v-show="!isEdit" :content="item.value" />
                </FormItem>
            </Col>
            <Col span="24" v-if="!hiddenColumn.includes('remark')">
            <FormItem label="备注">
                <sTextarea v-show="isEdit" :model-value="objInfo?.remark" :disabled="true" :placeholder="placeholder"></sTextarea>

                <div v-show="!isEdit" style="padding: 0 7px;">{{ objInfo?.remark }}</div>
            </FormItem>
            </Col>
        </Row>
        <!-- 动态组件 START -->
        <component :is="componentName" @close-modal="emitCloseModal()" :makers="[getZb()]" />
        <!-- 动态组件 END -->
    </div>
</template>

<script>
import deviceMarkerMap from '../deviceMarkerMap'
import { useStore } from 'vuex';
const $store = useStore();
// 部件信息
const defaultLabel = {
    bsm: '点位标识码',
    dwmc: '点位名称',
    dwlx: '点位类型'
}
export default {
    name: 'PartsInfo',
    components: {
        deviceMarkerMap
    },
    props: {
        objInfo: { default() { return {} } },
        monitorPoint:{ default() { return {} } },// 监测点位
        isEdit: { default: false },
        label: { default() { return defaultLabel } }, // 显示的名称
        isAdd: { default: true }, // 是否是新增时显示
        hiddenColumn:{default:[]},
        addColumns:{default:[]}
    },
    data() {
        return {
            componentName: ''
        }
    },
    computed: {
        placeholder() {
            if (this.isAdd) {
                return '通过监测点位获取'
            } else {
                return ''
            }
        },
        areaPath() {
            if (this.objInfo && this.objInfo.areaPath) {
                return this.objInfo.areaPath.replace(/@/g, '/')
            }
            return '--'
        },
        objX() {
            //先判断有没有监测点位
            if(this.monitorPoint && this.monitorPoint.gdx){
                return this.monitorPoint?.gdx ? `${this.monitorPoint?.gdx},${this.monitorPoint?.gdy}` : ''
            }else{
                //没有就返回部件
                return this.objInfo?.gdx ? `${this.objInfo?.gdx},${this.objInfo?.gdy}` : ''
            }
        }
    },
    created() {
    },
    methods: {
        setModal() {
            this.componentName = 'deviceMarkerMap';
        },
        emitCloseModal() {
            this.componentName = '';
        },
        //获取坐标
        getZb(){
            //先判断有没有监测点位
            if(this.monitorPoint && this.monitorPoint.zbx){
                return {objX:this.monitorPoint.zbx,objY:this.monitorPoint.zby}
            }else{
                //没有就返回部件
                return this.objInfo
            }
        }
    }
}
</script>

<style lang="less" scoped>
.view-form {
    /deep/ .ivu-form-item {
        display: flex;
        align-content: center;
        line-height: 32px;
        margin-bottom: 0;

        &.pointer {
            cursor: pointer;
        }

        .ivu-form-item-label {
            line-height: 32px;
            padding: 0 0 0px 0;
            position: relative;
            padding-right: 9px;
            white-space: nowrap;

            &:after {
                position: absolute;
                content: ':';
                right: 3px;
            }
        }

        .ivu-form-item-content {
            flex: 1;
            display: flex;
            overflow: hidden;

            .show-map {
                font-size: 16px;
                color: #165DFF;


                .ivu-icon {
                    font-weight: 700;
                }
            }
        }

        textarea.ivu-input {
            padding-top: 7px;
        }

        .ivu-input[disabled] {
            color: #1E2A55;
            cursor: auto;
            background-color: #fff;
            border: none;
            resize: none;
        }

        .fontWeight {

            .ivu-input[disabled] {
                font-size: 16px;
                font-weight: bolder;
            }
        }
        .ivu-tooltip{
            flex: 1;
            overflow: hidden;
        }
    }
}
</style>
