

const imgUrl = require('@/assets/images/icon_map_001.png')
// deviceInfo - 设备信息
// activedObjId 当前选中的id 用于设置高亮状态
// hideTag 隐藏角标的类名 hide-tag
// fun 返回中间图标的方法
// key 标识唯一性的主键
// 如果有角标 角标显示的数字
export const getMarkerContent = (deviceInfo, activedObjId, hideTag, fun, key = 'id', num = 0, option = { iconClassName: '' }) => {
    return `
    <div class="marker-icon
    ${deviceInfo.alarmState == 1 ? 'alarm-state' : ''}
    ${activedObjId == deviceInfo[key] ? 'actived' : ''}
    ${option['iconClassName']}"><div class="marker-status
    ${(num || (+(deviceInfo.switchState || '')) ? 'open' : 'closed')}
    ${hideTag}">
    ${!num ? (+(deviceInfo.switchState || '') ? '开' : '关') : (num > 99 ? '99+' : num)}
    </div>
    <img src="${fun(deviceInfo)}"></div>`
}

export const renderClusterMarker = (context, activedObjId, setContent) => {
    const { clusterData = [] } = context
    const _data = clusterData?.map((k) => k._amapMarker.originData).flat(2).map((k) => k.exData.split('@'))
    const _name = _data.find(k => k[0] == activedObjId)?.[2]
    const content = setContent(_data, imgUrl)
    const lable = `${content}<div class="marker-label-box label-box"><span class="name">${_name || _data[0][2]}</span><span class="num">${_data.length}</span></div>`
    context.marker.setContent(lable)
    context.marker.setExtData(clusterData?.map((k) => k._amapMarker.originData).flat(2).map((k) => k.exData))
}
export const renderMarker = (context, setContent) => {
    const { data = [] } = context
    const _data = data[0]?.exData.split('@')
    console.log('_renderMarker------>', _data[0])
    const content = setContent(_data[0])
    const lable = `${content}<div class="marker-label-box label-box">${_data[2]}</div>`
    context.marker.setExtData(_data[0])
    context.marker.setContent(lable)

}

export const showInfoWindow = (data, e, map, activedObjId, setId) => {
    const info = []

    info.push('<div class="input-card content-window-card" >')
    window.setId = setId
    data.forEach(ele => {
        const { exData } = ele
        const _exData = exData.split('@')
        info.push(`<div class="window-card-item ${activedObjId + '' == _exData[0] ? 'actived' : ''}" onClick="setId('${_exData[0]}')"><div class="name">${_exData[2]}</div>`)
        info.push(`<div class="code">${_exData[1]}</div></div>`)
    })
    info.push('</div>')
    // 创建浮窗
    const infoWindow = new window.AMap.InfoWindow({
        isCustom: true,
        content: info.join(''),
        anchor: 'bottom-left'
    })
    infoWindow.open(map, e.marker._position) // 打开弹窗
}
