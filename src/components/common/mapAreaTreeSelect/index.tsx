import { Tree, Input } from "view-ui-plus";
import { defineComponent, ref, watch, onMounted, h } from "vue";
import { manholeCoverService, areaTree } from "@/api/manholeCoverService";
import { treeParentChildLinkage, trace, treeToList } from "@/utils/tool";
import useDebounce from "@/hooks/useDebounce";
import "./index.less";
export default defineComponent({
    name: "mapAreaTreeSelect",
    props: {
        type: {
            type: String,
            default: "1",
        },
        table: {
            type: String,
            default: "",
        },
        modelIds: {
            type: String,
            default: "",
        },
        methodUrl: {
            type: String,
            default: "manholeAreaTree",
        },
        nodeKey: {
            type: String,
            default: "value",
        },
        nodeId: {
            type: String,
            default: "value",
        },
        splitKey: {
            type: String,
            default: "@",
        },
        searchKey: {
            type: String,
            default: "title",
        },
        isNewInterFace: {
            type: Boolean,
            default: false
        },
        level: {
            type: [String, Number],
            default: 4
        }
    },
    emits: ["updateAreaPath"],
    setup(props, ctx) {
        // 搜索树
        const searchTreeText = ref<string>("");
        // 显示的区域树
        const areaTreeList = ref<areaTree[]>([]);
        // 查询区域树
        const getManholeAreaTree = async () => {
            const res = await manholeCoverService[props.methodUrl]({
                type: props.type,
                table: props.table,
                modelIds: props.modelIds,
            });
            const { data, success }: { data: areaTree[]; success: boolean } =
                res as unknown as HttpResponse<areaTree[]>;
            if (success) {
                const areaPaths: string[] = [];
                trace(data, (d: areaTree) => {
                    areaPaths.push(d[props.nodeKey] as string);
                    d.title = d.label || d.name;
                    d.key = d.code || `${d.id}`;
                    d.checked = false;
                    d.expand = true;
                });
                areaTreeList.value = data;
                updateAreaPath();
            }
        };
        const transformData = (data:areaTree[]) => {
            const map = {}; // hash表
            data.forEach(node => {
                node.children = [];
                node.expand = true;
                node.selected = false;
                node.checked = false;
                node.title = node.name;
                node.value = node.name
                map[node.code!] = node;
            })

            const result:areaTree[] = [];

            data.forEach(element => {
                const parent = map[element.parentCode!];
                if (parent) {
                    element.value = parent.value + '@' + element.value
                    parent.children.push(element)
                } else {
                    result.push(element)
                }
            });
            return result;
        }
        const getManholeAreaTreeNoDevice = async () => {
            const res = await manholeCoverService[props.methodUrl]({
                type: props.type,
                table: props.table,
                modelIds: props.modelIds,
                level: props.level
            });
            const { data, success }: { data: areaTree[]; success: boolean } =
                res as unknown as HttpResponse<areaTree[]>;
            if (success) {
                areaTreeList.value = transformData(data)
                updateAreaPath();
            }
        };

        // 勾选区域树
        const handleChangeCheck = (checkedKeys: any[], selectKey: any) => {
            treeParentChildLinkage(checkedKeys, selectKey, areaTreeList.value, props.nodeKey);
            updateAreaPath();
        };
        //更新当前勾选的区域路径
        const updateAreaPath = () => {
            const areaPaths: string[] = [];
            trace(areaTreeList.value, (d: areaTree) => {
                if (d.checked) areaPaths.push(d[props.nodeId] as string);
            });
            ctx.emit("updateAreaPath", areaPaths);
            // 查询当前区域的设施
        };
        // 取消
        const handleCancel = () => {
            searchTreeText.value = "";
            handleChangeCheck([], {});
        };
        // 搜索
        const handleChangeSearch = () => {
            const text = searchTreeText.value.toLocaleLowerCase();
            useDebounce(() => {
                const treeList = treeToList(areaTreeList.value);
                const searchTreelist = treeList
                    .filter((k) => k.title.toLocaleLowerCase().includes(text))
                    .map((k) => k[props.nodeKey].split(props.splitKey))
                    .flat();
                trace(areaTreeList.value, (d: areaTree) => {
                    if (searchTreelist.includes(d[props.searchKey])) {
                        d.expand = true;
                    } else {
                        d.expand = false;
                    }
                });
            });
        };
        const preg_quote = (str: string) => {
            return (str + "").replace(/([\\\.\+\*\?\[\^\]\$\(\)\{\}\=\!\<\>\|\:])/g, "\\$1");
        };
        // 是否显示按钮
        const hideBtn = ref<boolean>(false);
        onMounted(() => {
            props.isNewInterFace ? getManholeAreaTreeNoDevice() : getManholeAreaTree();
        });
        return () => (
            <>
                <div class="search-box">
                    <Input
                        suffix="ios-search"
                        placeholder="请输入区域"
                        clearable={!hideBtn.value}
                        v-model={searchTreeText.value}
                        onOnFocus={() => (hideBtn.value = true)}
                        onOnBlur={() => (hideBtn.value = false)}
                        onOnChange={handleChangeSearch}
                    />
                    <div class="cancel" onClick={handleCancel} v-show={!hideBtn.value}>
                        取消
                    </div>
                </div>
                <div class="scorll-map-cont map-tree-box">
                    <Tree
                        data={areaTreeList.value}
                        render={(h: any, { data }: { data: areaTree }) => {
                            if (
                                searchTreeText.value &&
                                (data.title as string)
                                    .toLocaleLowerCase()
                                    .includes(searchTreeText.value.toLocaleLowerCase())
                            ) {
                                return (
                                    <tooltip-auto-show>
                                        <span
                                            v-html={(data.title as string).replace(
                                                new RegExp(
                                                    "(" + preg_quote(searchTreeText.value) + ")",
                                                    "gi"
                                                ),
                                                "<span class='text-blue'>$1</span>"
                                            )}
                                        ></span>
                                    </tooltip-auto-show>
                                );
                            }
                            return <tooltip-auto-show>{data.title}</tooltip-auto-show>;
                        }}
                        check-strictly
                        show-checkbox
                        // @ts-ignore
                        onOnCheckChange={handleChangeCheck}
                    />
                </div>
            </>
        );
    },
});
