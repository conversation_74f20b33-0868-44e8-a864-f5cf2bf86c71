import { defineComponent, nextTick, ref, watch } from "vue";
export default defineComponent({
    name: "mapDeviceList",
    props: {
        deviceList: {
            type: Array,
            default: () => [],
        },
        searchText: {
            type: String,
            default: "",
        },
        lableKey: {
            type: String,
            default: "",
        },
        valueKey: {
            type: String,
            default: "",
        },
        activedObjId: {
            type: [String, Number],
            default: "",
        },
        primaryKey: {
            type: String,
            default: "id",
        },
        showTag: {
            type: Array,
            default: () => [1, 2],
        },
    },
    emits: ["actived-obj"],
    setup(props, ctx) {
        const handleActivedObj = (item: any) => {
            ctx.emit("actived-obj", item);
        };
        return () => (
            <>
                {props.deviceList.map((item: any) => {
                    const _val = `${item[props.valueKey]}`;
                    return (
                        <div
                            class={
                                props.activedObjId == item[props.primaryKey]
                                    ? "device-info actived"
                                    : "device-info"
                            }
                            onClick={() => handleActivedObj(item)}
                            v-show={
                                !props.searchText ||
                                item[props.lableKey]?.includes(props.searchText) ||
                                _val?.includes(props.searchText)
                            }
                        >
                            <div class="device-name">
                                <div class="name">{item[props.lableKey] || "-"}</div>
                                <div class="device-status">
                                    <s-tag
                                        v-show={props.showTag.includes(1)}
                                        background={item.status == 1 ? "#E8FFEA" : "#F2F3F5"}
                                        color={item.status == 1 ? "#00B42A" : "#4E627E"}
                                    >
                                        {item.status == 1 ? "在线" : "离线"}
                                    </s-tag>
                                    <s-tag
                                        v-show={props.showTag.includes(2)}
                                        background={item.alarmState == 1 ? "#FFF7E8" : "#E8F3FF"}
                                        color={item.alarmState == 1 ? "#FF7D00" : "#165DFF"}
                                    >
                                        {item.alarmState == 1 ? "告警" : "正常"}
                                    </s-tag>
                                </div>
                            </div>
                            {props.valueKey ? (<div class="device-code">{item[props.valueKey] || "-"}</div>) : '' }
                            
                        </div>
                    );
                })}
            </>
        );
    },
});
