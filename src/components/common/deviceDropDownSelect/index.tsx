import { defineComponent, onMounted, watch, ref, nextTick } from "vue";
import { Select, Option } from "view-ui-plus";
import { monitorService } from "@/api/livableManage/monitorService";
import { ComponentInfo } from "@/api/manholeCoverService";
import sSelect from '@/components/global/select/index.vue'
import sOption from '@/components/global/select/sOption.vue'

export default defineComponent({
    name: "deviceDropDownSelect",
    props: {
        modelId: {
            type: Number,
            default: 1,
        },
        firstSelect: {
            type: Boolean,
            default: false,
        },
        initSelectData: {
            type: Array,
            default: () => [],
        },
        valueKey: {
            type: String,
            default: "deviceCode",
        },
        labelKey: {
            type: String,
            default: "sbmc",
        },
        showKey: {
            type: String,
            default: "valueKey",
        },
    },
    components: { sSelect, sOption },
    emits: ["update:modelValue", "initSelect", "onChange"],
    setup(props, ctx) {
        // 设备类型url
        const modelIdUrl = [
            "",
            "", //1井盖设备管理
            "", //2道路管理
            "", //3桥梁管理
            "", //4灌溉电磁阀控制管理
            "", //5灌溉电磁阀管理
            "", //6智慧座椅管理
            "", //7智慧路灯
            "", //8消防栓管理
            "gardenSoilDeviceList", //9园林土壤设备
            "", //10绿地管理
            "airQualityDeviceList", //11空气质量
            "noiseDeviceList", //12噪声环境
            "meteorologicalDeviceList", //13气象环境
            "waterQualityDeviceList", //14水质监测
            "", //15电子围栏
            "pondingMonitoringDeviceList", //16积水监测
            "rainfallMonitoringDeviceList", //17雨情监测
            "lakeMonitoringDeviceList", //18湖渠监测
            "", // 19
            "", // 20
            "", // 21
            "", // 22
            "", // 23
            "", // 24
            "", // 25
            "", // 26
            "", // 27
            "", // 28
            "", // 29
            "", // 30
            "", // 31
            "", // 32
            "", // 33
            "", // 34火情监控
            "hazardousgDeviceList", // 35危废监测
            "deepFoundationDeviceList", // 36深基监测
            "",
            "smartTravelGuidanceList", // 38 诱导屏列表

        ];
        const deviceList = ref<ComponentInfo[]>([]);
        const gardenSoilDeviceList = async () => {
            const params = {
                page: {
                    current: 1,
                    size: -1,
                },
                customQueryParams: {},
            };
            if (!monitorService[modelIdUrl[+props.modelId as number]]) return;
            const res = await monitorService[modelIdUrl[+props.modelId as number]](params);
            const { data, success }: { success: boolean; data: recordsResponse<ComponentInfo[]> } =
                res as unknown as HttpResponse<recordsResponse<ComponentInfo[]>>;
            // 成功
            if (success) {
                initSelect(data.records);
            }
        };
        const initSelect = (data: any[]) => {
            deviceList.value = data;
            if (props.firstSelect) {
                selectVal.value = data[0][props.valueKey] || "";
                ctx.emit("update:modelValue", data[0]?.[props.valueKey]);
                ctx.emit("onChange", data[0] || {}); // 完整的选中数据
                ctx.emit("initSelect"); // 初始化选中后执行的方法
            }
        };
        const handleSelect = (val: any) => {
            ctx.emit("update:modelValue", val || "");
            const data = deviceList.value.filter((k) => k[props.valueKey] == val)[0];
            ctx.emit("onChange", data || {});
        };
        const selectVal = ref("");
        onMounted(() => {
            nextTick(() => {
                if (props.modelId) {
                    gardenSoilDeviceList();
                }
            });
        });
        watch(
            () => props.initSelectData,
            () => {
                if (props.initSelectData?.length) {
                    initSelect(props.initSelectData);
                }
            },
            {
                immediate: true,
                deep: true,
            }
        );
        return () => (
            <s-select v-model={selectVal.value} onOnChange={handleSelect} clearable filterable>
                {deviceList.value.map((item, index) => (
                    <sOption
                        value={item[props.valueKey]}
                        // label={item[props.showKey]}
                    >{`${props.showKey == "valueKey" ? item[props.valueKey]+'('+(item[props.labelKey] || '-')+')' : item[props.labelKey]}`}</sOption>
                ))}
            </s-select>
        );
    },
});
