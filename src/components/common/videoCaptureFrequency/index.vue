<template>
    <Form :model="form" ref="formRef" :rules="rules" label-width="0px">
        <div class="video-capture-frequency">
            <videoPlay
                v-if="showVideo"
                :loop="true"
                :autoplay="true"
                :styles="{ width: '100%' }"
                ref="videoPlayCom"
            >
            </videoPlay>
            <div class="btn">
                <Button type="success" @click="() => takeSnapshot(false)">异常抓拍</Button>
                <Button
                    type="error"
                    @click="startRecordingOrStopRecording(loading ? false : true)"
                    >{{ loading ? "停止录制" : uploading ? "正在生成..." : "异常录制" }}</Button
                >
                <Button v-if="showNormalBtn" type="primary" @click="normalTakeSnapshot"
                    >正常</Button
                >
            </div>
            <div class="video-capture-list">
                <div
                    class="name"
                    @click="preview(item.accessUrl)"
                    v-for="(item, index) in takeSnapshotList"
                    :key="index"
                >
                    {{ item.fileName }}
                </div>
            </div>
            <FormItem v-if="!isNormalTakeSnapshot" label="异常原因" prop="contentValue">
                <Input
                    placeholder="请输入"
                    v-model="form.contentValue"
                    type="textarea"
                    :rows="3"
                    :show-word-limit="true"
                    :maxlength="200"
                ></Input>
            </FormItem>
            <div class="submit-btn" v-show="!isEmpty(takeSnapshotList)">
                <Button type="primary" @click="submit">提交</Button>
                <Button @click="cancel">取消</Button>
            </div>
        </div>
    </Form>
    <AModal title="预览" :is-show="isPreView" :show-btn="false" @on-cancel="isPreView = false">
        <img
            style="width: 100%; height: 450px"
            v-if="judgeUrlType(previewUrl) === 'image'"
            :src="previewUrl"
        />
        <video style="width: 100%; height: 450px" v-else :src="previewUrl" controls></video>
    </AModal>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import { MessageSuccess, MessageInfo } from "@/hooks/message";
import videoPlay from "@/components/common/videoPlay/index.vue";
import AModal from "@/components/common/modal/index.vue";
import moment from "moment";
import { ossService } from "@/api/ossService";
import { addPatrolResult, PatrolResult } from "@/api/safeManage/implement.ts";
import { validateform } from "@/utils/validateform";
import { isEmpty } from "wei-util";
const timeFormat = "YYYY-MM-DD HH:mm:ss";
const videoPlayCom = ref<InstanceType<typeof videoPlay>>();
const loading = ref<boolean>(false);
const emit = defineEmits(["cancel", "success"]);
const props = defineProps({
    showVideo: {
        default: true,
    },
    currentObj: {
        default: () => ({}),
        type: Object as PropType<any>,
    },
    videoDom: {
        type: Object as PropType<HTMLVideoElement>,
    },
    submitFun: {
        type: Function as PropType<() => void>,
        default: null,
    },
    showNormalBtn: {
        type: Boolean,
        default: true,
    },
});
const form = ref({
    contentValue: "",
});
const rules = computed(() => {
    if (!isNormalTakeSnapshot.value) {
        return {
            contentValue: validateform.required,
        };
    }
});
let chunks: any[] = [];
let mediaRecorder: any;
const startRecording = (): void => {
    const video: any = (videoPlayCom.value?.getDom() as HTMLVideoElement) || props.videoDom;
    if (!video) {
        return console.error("请配置videoDom");
    }
    // 将video的视频转成流stream
    const stream = video.captureStream();
    // 开始录屏
    mediaRecorder = new MediaRecorder(stream, {
        mimeType: "video/webm;codecs=vp9",
    });
    mediaRecorder.ondataavailable = (e) => {
        chunks.push(e.data);
    };
    mediaRecorder.start();
    MessageSuccess("开始录屏");
    loading.value = true;
    isNormalTakeSnapshot.value = false;
};
const startRecordingOrStopRecording = (isStart: boolean): void => {
    isStart ? startRecording() : stopRecording();
};
const uploading = ref<boolean>(false);
// 点击停止录制
const stopRecording = (): void => {
    if (!mediaRecorder) return;
    mediaRecorder?.stop();
    uploading.value = true;
    loading.value = false;
    // 录屏结束后，生成视频文件
    mediaRecorder.onstop = () => {
        const blob = new Blob(chunks, { type: "video/mp4" });
        // 将blob放到video标签中播放
        const fileName = "录制视频" + moment().format(timeFormat) + ".mp4";
        chunks = []; // 清空 chunks
        ossService.uploadFiles(blob, fileName).then((res: any) => {
            if (res.code === "success") {
                takeSnapshotList.value.push({
                    ...res.data,
                    fileName,
                });
                MessageSuccess("录制成功");
                uploading.value = false;
            }
        });
    };
};
// 视频播放过程之中截取视频帧
const getFrame = (video: HTMLVideoElement, canvas: HTMLCanvasElement) => {
    const ctx = canvas.getContext("2d");
    if (ctx) {
        ctx.drawImage(video, 0, 0, canvas.width, canvas.height);
        const dataUrl = canvas.toDataURL("image/png");
        return dataUrl;
    }
};
const takeSnapshotList = ref<any[]>([]);
const isNormalTakeSnapshot = ref<boolean>(true);
const takeSnapshot = (isNormal: boolean): void => {
    if (isNormalTakeSnapshot.value !== !!isNormal) {
        takeSnapshotList.value = [];
        form.value.contentValue = "";
    }
    isNormalTakeSnapshot.value = !!isNormal;
    const video: HTMLVideoElement =
        (videoPlayCom.value?.getDom() as HTMLVideoElement) || props.videoDom;
    if (!video) {
        return console.error("请配置videoDom");
    }
    const canvas = document.createElement("canvas");
    const base64Img = getFrame(video, canvas);
    // 设置该文件的文件名为当前时间，时间格式为 yyyy-MM-dd hh:m:ss格式的png图片文件
    const fileName = "抓拍图片" + moment().format(timeFormat) + ".png";
    ossService.uploadBase64Image(base64Img, fileName).then((res: any) => {
        if (res.code === "success") {
            takeSnapshotList.value.push(res.data || {});
            MessageSuccess("抓拍成功");
        }
    });
};
const formRef = ref<any>();
const submit = (): void => {
    if (isEmpty(takeSnapshotList.value)) return MessageInfo("没有可提交的信息，请先进行抓拍或录制");
    formRef.value?.validate((valid: boolean) => {
        if (valid) {
            if (!props.submitFun) {
                const params: PatrolResult = {
                    alarmUrl: takeSnapshotList.value.map((item) => item.accessUrl).join(),
                    deviceCode: props.currentObj.deviceCode,
                    prowlDeviceRefId: props.currentObj.id,
                    resultType: isNormalTakeSnapshot.value ? 1 : 3,
                    alarmContent: form.value.contentValue,
                };
                addPatrolResult(params)
                    .then((res: any) => {
                        if (res.code === "success") {
                            MessageSuccess("提交成功");
                            emit("success");
                            cancel();
                        }
                    })
                    .catch((err) => {});
            } else {
                const params: any = {
                    takeSnapshotList: takeSnapshotList.value,
                    errorReason: form.value.contentValue,
                };
                props.submitFun(params);
            }
        }
    });
};
// 预览
const previewUrl = ref<string>("");
const isPreView = ref<boolean>(false);
const preview = (url: string) => {
    isPreView.value = true;
    previewUrl.value = url;
};
const normalTakeSnapshot = (): void => {
    takeSnapshot(true);
};
const initData = () => {
    takeSnapshotList.value = [];
    form.value.contentValue = "";
    isNormalTakeSnapshot.value = true;
    uploading.value = false;
};
const judgeUrlType = (url: string) => {
    const type = url.split(".").pop();
    return type === "jpg" || type === "jpeg" || type === "png" ? "image" : "video";
};
const cancel = (): void => {
    initData();
    emit("cancel");
};
defineExpose({
    cancel,
});
</script>

<style scoped lang="less">
/* 添加样式 */
.video-capture-frequency {
    display: flex;
    flex-direction: column;
    row-gap: 10px;

    .video-capture-list {
        display: flex;
        flex-direction: column;
        row-gap: 4px;

        .name {
            cursor: pointer;

            &:hover {
                color: #1890ff;
            }
        }
    }
}
</style>
