<template>
    <Select v-model="curValue" @on-change="changeSelect" :multiple="multiple" clearable :placeholder="placeholder">
        <OptionGroup label="视频">
            <Option v-for="item in videoList" :disabled="disabledFun(item)" :value="item.id" :key="item.id">{{ item.name }}</Option>
        </OptionGroup>
        <OptionGroup label="图片">
            <Option v-for="item in imageList" :disabled="disabledFun(item)" :value="item.id" :key="item.id">{{ item.name }}</Option>
        </OptionGroup>
    </Select>
</template>

<script>
export default {
    name: 'index',
    props: {
        modelValue: {
            type: [String, Number, Array],
            default: ''
        },
        multiple: { default: false },
        placeholder: { default: '请选择' },
        sourceIds: { default() { return [] } }, // 已选择的素材id
        imageList: { default() { return [] } },
        videoList: { default() { return [] } }
    },
    data() {
        let curValue = this.multiple ? [] : ''
        return {
            curValue,
            isChangeValueIn: false
        }
    },
    watch: {
        modelValue() {
            if (this.isChangeValueIn) {
                this.isChangeValueIn = false
            } else {
                this.curValue = this.modelValue
            }
        }
    },
    methods: {
        changeSelect(val) {
            if (!this.multiple) {
                // 单选
                let obj = {}
                if (val) {
                    obj = this.imageList.find(k => k.id == val)
                    if (!obj) {
                        obj = this.videoList.find(k => k.id == val)
                    }
                }
                this.isChangeValueIn = true
                this.$emit('update:modelValue', val)
                this.$emit('update:type', obj.fileType)
                this.$emit('on-change', val, obj)
                console.log(val, obj)
            }
        },
        disabledFun(item) {
            if (this.sourceIds.indexOf(item.id) > -1 && item.id != this.curValue) {
                return true
            }
            return false
        }
    }
}
</script>

<style lang="less" scoped>

</style>
