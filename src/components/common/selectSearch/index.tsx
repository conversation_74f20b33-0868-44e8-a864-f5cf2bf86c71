import { Dropdown, DropdownMenu, DropdownItem, Input, Icon, FormItem, Content } from 'view-ui-plus'
import { ref, defineComponent, computed } from "vue";
import { isNullOrEmpty } from '@/utils/tool';
import './index.less'
export default defineComponent({
  name: 'selectSearch',
  props: {
    label: {
      default: ''
    },
    data: {
      default: () => []
    },
    contentLabel: {
      default: ''
    }
  },
  setup(props: any, ctx: any) {
    const { emit } = ctx
    const showList = ref<boolean>(false)
    function handleFocus() {
      showList.value = true
    }
    const InputDom = ref()
    function handleBlur(e:any) {
      showList.value = false
      e.target?.focus()
      InputDom.value = e.target
    }
    const dataList = computed(() => {
      if (isNullOrEmpty(content.value)) return props.data
      return props.data.filter((item: any) => {
        return item[props.contentLabel].indexOf(content.value) !== -1
      })
    })
    const content = ref<string>('')
    function handleChange() {
      emit('update:modelValue', content.value)
    }
    function handleClickList(item: any) {
      content.value = item[props.contentLabel]
      emit('on-select-change', item)
      handleBlur(null)
      showList.value = false
    }
    // 清除
    function handleClear(){
      emit('on-clear')
    }
    return () => (
      <>
        <FormItem label={props.label}>
          <div class="Input-div-1">
            <Input onOnClear={handleClear} clearable v-model={content.value} onOnChange={handleChange} onOnFocus={handleFocus} onOnBlur={handleBlur} placeholder="请选择人员账号">
              {{
                suffix: () => <Icon type="ios-arrow-down" />
              }}
            </Input>
              <div v-show={showList.value} class="ivu-select-dropdown down-card-1">
                <ul class="ivu-dropdown-menu">
                  {
                    dataList.value.map((item: any) => {
                      return <li onClick={() => handleClickList(item)} class="ivu-dropdown-item">
                        {item[props.contentLabel]}
                      </li>
                    })
                  }
                </ul>
              </div>
          
          </div>
        </FormItem>
      </>

    )
  }
})