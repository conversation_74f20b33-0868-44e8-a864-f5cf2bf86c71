<template>
    <div v-if="value > 0 || value === 0" class="per">
        <span class="red">{{ $Util.formatNum(value) }}%</span>
        <img src="@/assets/images/上涨.png" alt="">
    </div>
    <div v-else-if="value < 0" class="per">
        <span class="green">{{ -$Util.formatNum(value) }}%</span>
        <img src="@/assets/images/下降.png" alt="">
    </div>
    <div v-else class="per">--</div>
</template>

<script>
export default {
    name: 'compareBox',
    props: {
        value: { default: '' }
    },
    data() {
        return {}
    },
    methods: {}
}
</script>

<style lang="less" scoped>
.per{
    display: flex;
    align-items: center;
    margin-left: 8px;
    img{
        width: 12px;
        margin-left: 4px;
    }
    .red{
        color: rgba(245, 63, 63, 1);
    }
    .green{
        color: rgba(0, 180, 42, 1);
    }
}

</style>
