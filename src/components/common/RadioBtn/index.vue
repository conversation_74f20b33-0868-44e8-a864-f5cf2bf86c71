<template>
    <RadioGroup v-model="curValue" @on-change="changeHandler" type="button" class="radio-btn">
        <slot></slot>
    </RadioGroup>
</template>

<script>
export default {
    name: 'RadioBtn',
    props: {
        modelValue: { type: [Number, String] },
    },
    emits: ['update:modelValue', 'on-change'],
    data() {
        return {
            curValue: '',
            isChangeValueIn: false
        }
    },
    watch: {
        modelValue(val) {
            if (this.isChangeValueIn) {
                this.isChangeValueIn = false
            } else {
                this.curValue = val
            }
        }
    },
    mounted() {
        this.curValue = this.modelValue
    },
    methods: {
        changeHandler(val) {
            this.isChangeValueIn = true
            this.$emit('update:modelValue', this.curValue)
            this.$emit('on-change', this.curValue)
        }
    }
}
</script>

<style lang="less" scoped>
.radio-btn{
    background: rgba(22, 93, 255, 0.3);
    border-radius: 4px;
    /deep/ .ivu-radio-wrapper{
        background: none;
        border: none;
        height: 24px;
        line-height: 24px;
        color: #FFFFFF;
        padding: 0 10px;

        &:before,
        &:after{
            display: none;
        }
        clip-path: polygon(10% 0, 100% 0, 90% 100%, 0 100%);
        &:first-child{
            clip-path: polygon(0 0, 100% 0, 90% 100%, 0 100%);
        }
        &:last-child{
            clip-path: polygon(10% 0, 100% 0, 100% 100%, 0 100%);
        }
        &.ivu-radio-wrapper-checked{
            background: @primary-color;

        }
    }
}
</style>
