<template>
    <Modal
        v-model="showFlag"
        title="播放"
        :width="1100"
        :mask-closable="false"
        :footer-hide="true"
    >
        <videoPlay :styles="{ width: 1035, height: 540 }" />
    </Modal>
</template>

<script>
import videoPlay from './index'
export default {
    name: 'videoModal',
    components: { videoPlay },
    data() {
        return {
            showFlag: false
        }
    },
    methods: {
        init() {
            this.showFlag = true
        }
    }
}
</script>

<style lang="less" scoped>

</style>
