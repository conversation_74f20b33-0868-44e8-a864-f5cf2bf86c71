<template>
    <div class="con-video" :id="'fullVideoEl_' + index" :style="stylesVideo">
        <div class="device-name">{{ curVideoObj.extendInfo && curVideoObj.extendInfo.sbmc }}</div>
        <video :loop="loop" crossOrigin="anonymous" ref="videoCom"
            src="https://linkthings.oss-cn-hangzhou.aliyuncs.com/common/video/linkos.mp4" :autoplay="autoplay" controls
            :style="stylesVideo"></video>
    </div>
</template>

<script>
export default {
    name: 'VideoPlay',
    props: {
        styles: {type: Object, default: () => ({ width: 300, height: 300 }) },
        index: { type: [String, Number], default: 0 },
        controls: { default: true },
        autoplay: { default: false },
        loop: { default: false }
    },
    data() {
        return {
            curVideoObj: {},
            v: {
                isFull: false
            },
            isShowPlay: false,
            loading: true
        }
    },
    computed: {
        stylesVideo() {
            let obj = this.$Util.objClone(this.styles)
            if (this.v.isFull) {
                obj.width = screen.width
                obj.height = screen.height
            }
            if (isNaN(obj.width)) {
                return {
                    width: obj.width,
                    height: obj.height
                }
            }
            return {
                width: obj.width + 'px',
                height: obj.height + 'px'
            }
        }
    },
    methods: {
        open(data) {
            if (!data) {
                return
            }
            this.curVideoObj = data
            return new Promise((resolve, reject) => {
            })
        },
        clear() {
            return new Promise((resolve, reject) => {
            })
        },
        getDom() {
            return this.$refs.videoCom
        }
    }
}
</script>

<style lang="less" scoped>
.con-video {
    position: relative;
    background: #000;

    .device-name {
        position: absolute;
        z-index: 100;
        top: 0;
        left: 0;
        color: #fff;
        padding: 4px;
    }
}
</style>
