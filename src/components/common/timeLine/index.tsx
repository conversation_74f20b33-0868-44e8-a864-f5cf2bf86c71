import { defineComponent, ref, computed,onUpdated } from 'vue'
import { Timeline, TimelineItem, Icon } from 'view-ui-plus'
import './index.less'
export default defineComponent({
  props: {
    timeKey: {
      type: String,
      default: 'modifyTime'
    },
    data: {
      type: Array,
      default: () => [
      ]
    }
  },
  setup(props, { slots }: any) {
    const { timeKey }: { data: any[], timeKey: string } = props
    const list = computed<any[]>(() => {
      return [...props.data,{}]
    })
    const showMore = ref<boolean>(false)
    const clickExpand = () => {
      showMore.value = !showMore.value
    }
    return () => (
      <div class="s-time-line">
        <Timeline class="right-box">
          {
            list.value.map((item, index, arr) => {
              if (index < 4) {
                return (
                  <TimelineItem>
                    <span class="left-box">{item[timeKey]}</span>
                    {index < arr.length - 1 ? slots.content(item, index) : ''}
                  </TimelineItem>
                )
              } else if (index === 4 && arr.length > 5) {
                return <>
                  <div class="expand" onClick={clickExpand}>
                    <Icon v-show={!showMore.value} class="iconfont icon-right" color='#165DFF' size={7}></Icon>
                    <Icon v-show={showMore.value} class="iconfont icon-down1" color='#165DFF' size={7}></Icon>
                    <span>{showMore.value ? '收起' : '展开'}</span>
                  </div>
                  <TimelineItem v-show={showMore.value}>
                    <span class="left-box">{item[timeKey]}</span>
                    {index < arr.length - 1 ? slots.content(item, index) : ''}
                  </TimelineItem>
                </>

              }
              return <>
                <TimelineItem v-show={showMore.value}>
                  <span class="left-box">{item[timeKey]}</span>
                  {index < arr.length - 1 ? slots.content(item, index) : ''}
                </TimelineItem>
              </>
            })
          }
        </Timeline>
      </div>
    )
  }
})