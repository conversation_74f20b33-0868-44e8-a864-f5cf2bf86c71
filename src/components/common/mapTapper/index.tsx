import { defineComponent, onMounted, watch, ref, nextTick } from 'vue';
import { Mo<PERSON>, Button } from 'view-ui-plus';
import useModal from '@/hooks/modal';
import AMapLoader from '@amap/amap-jsapi-loader';
import './index.less';

interface IMarker {
    lng: number; // 经度
    lat: number; // 纬度
}

export default defineComponent({
    name: 'MapTapper',
    props: {
        attrKey: {
            // 用那个字段标志图标类型
            type: String,
            default: '',
        },
        isShow: {
            type: Boolean,
        },
        modelValue: {
            type:Object,
            default: () => ({
                lng: 114.61,
                lat: 30.45
            })
        }
    },
    emits: ['on-confirm', 'update:modelValue', 'on-cancel'],
    setup(props, ctx) {
        const { modalStatus } = useModal();
        const { lng, lat } = props.modelValue
        // 初始化地图
        const map = ref<any>(null);
        const Amap = ref<any>(null);
        const marker = ref();
        const point = ref({
            lng,
            lat,
        });
        const ininMap = () => {
            AMapLoader.load({
                key: 'ea53a5291f8f7c1f215ba20930ea9866',
                version: '2.0',
                plugins: ['AMap.Geocoder']
            }).then((AMap) => {
                Amap.value = AMap;

                map.value = new AMap.Map('map-cont', {
                    resizeEnable: true,
                    zoom: 14,
                    center: lng ? [lng, lat] : [114.61, 30.45],
                });
                map.value.on('click', handlerClick)
                // map.value.clearMap();
                // updateMapIcon();
                updateMapIcon();
            });
        };
        const updateMapIcon = () => {
            // 清除地图上的图标
            map.value.clearMap();
            const { lng, lat } = props.modelValue
            if (lng) {
                const icon = new Amap.value.Marker({
                    position: [lng, lat],
                });
                map.value.add(icon);
            }
            nextTick(() => {
                map.value.setCenter([
                    lng || 114.61,
                    lat || 30.45,
                ]);
            });
        };
        const handlerClick = (e: any) => {
            const lat = e.lnglat.lat;
            const lng = e.lnglat.lng;
            map.value.clearMap();
            point.value.lat = lat?.toFixed(5);
            point.value.lng = lng?.toFixed(5);
            marker.value = new Amap.value.Marker({
                position: [lng, lat],
            });
            map.value.add(marker.value);
        }
        const onCancel = () => {
            ctx.emit('on-cancel')
        }
        const onConfirm = () => {
            if (props.isShow) {
                const geocoder = new Amap.value.Geocoder({})
                geocoder.getAddress(point.value, function (status: any, result: any) {
                    if (status === 'complete' && result.regeocode) {
                        const { province, city, district, township, street, streetNumber } = result.regeocode.addressComponent;
                        ctx.emit('on-confirm', point.value, { province, city, district, township, street, streetNumber });
                    } else {
                    }
                });
                ctx.emit('update:modelValue', point.value)
            }
        }
        onMounted(() => {
            ininMap();
        });

        return () => (
            <Modal footer-hide v-model={props.isShow} onOnCancel={onCancel} width={800} mask-closable={false} title='位置信息'>
                <div class='map-container' id='map-cont'></div>
                <div class='footer-map-1'>
                    <Button onClick={onCancel}>取消</Button>
                    <Button type='primary' onClick={onConfirm}>确定</Button>
                </div>
            </Modal>
        );
    },
});
