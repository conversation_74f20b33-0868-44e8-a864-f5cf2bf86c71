<template>
    <div class="choose-res" :class="[labelPosition]">
        <slot name="title">
            <div class="name" :class="{required: required}" v-if="title">{{ title }}</div>
        </slot>

        <div class="choose-device">
            <slot></slot>
        </div>
    </div>
</template>

<script>
export default {
    name: 'ChooseDeviceBox',
    props: {
        title: { default: '已选设备' },
        labelPosition: { default: 'right' },
        required: { default: true }
    },
    data() {
        return {}
    },
    methods: {}
}
</script>

<style lang="less" scoped>
.choose-res{

    margin-bottom: 16px;
    .name{
        line-height: 22px;
        margin-right: 8px;
        color: @text-color;
        &.required:before{
            content: '*';
            color: @error-color;
            font-size: 16px;
            margin-right: 4px;
            line-height: 22px;
            vertical-align: middle;
        }
    }
    .choose-device{
        height: 132px;
        border: 1px solid @line-2;
        border-radius: 2px;
        flex: 1;
        padding: 8px 12px;
        overflow: auto;
        /deep/.placeholder{
            color: @input-placeholder-color;
        }
        /deep/.ivu-tag{
            margin: 0 8px 12px 0;
            border-color: @primary-color;
            background: #E8F3FF;
            height: 24px;
            line-height: 22px;
            position: relative;
            padding: 0 12px;
           .ivu-tag-text{
                color: @primary-color !important;
            }
            .ivu-icon-ios-close{
                color: @primary-color !important;
                top: 2px;
                right: 1px;
                position: absolute;
            }
        }
    }

    &.right{
        display: flex;
        align-items: flex-start;
    }
    &.top{
        .name{
            padding-bottom: 10px;
            line-height: 1;
            &.required:before{
                line-height: 1;
            }
        }
    }
}
/deep/.ivu-tooltip p{
    color: #fff;
    font-size: 14px;
}
</style>
