<template>
    <div class="device-select" :class="[multiple ? 'button' : '']">
      <template v-if="multiple">
        <Button type="primary" v-if="buttonType == 'button'"  @click="showModal">
            <i class="iconfont">&#xe6b1;</i>
            <!-- 关联设备 -->
             {{ buttonName }}
        </Button>
        <link-btn v-if="buttonType == 'link'" size="small" v-auth="'drainageControl:operationStaff:association'" @click="showModal">
          {{ buttonName }}
        </link-btn>
      </template>
        
        <div
            class="select-box"
            @mouseenter="mouseenter"
            @mouseleave="mouseleave"
            v-else
            @click="showModal"
            :style="{ display: allNotShow ? 'none' : 'block' }"
        >
            <div class="name" v-if="device.deviceId">
                {{ device.deviceId }}（{{ device.sbmc }}）
            </div>
            <div class="name" v-if="device.objId">{{ device.objId }}</div>
            <div class="ivu-select-placeholder" v-else>请选择</div>
            <Icon type="ios-arrow-down" class="ivu-select-arrow" />
            <Icon
                v-show="device.objId && showDeleteIcon"
                @click.prevent.stop="deleteObjId"
                type="ios-close-circle"
                class="ivu-select-arrow"
            />
        </div>
        <component
            :is="componentName"
            ref="device"
            :title="title"
            :searchTitle="searchTitle"
            :multiple="multiple"
            :model-id="modelId"
            :default-sel-list="defaultSelList"
            @on-change="changeDevice"
        />
    </div>
</template>

<script>
import deviceModal from "./deviceModal";
import componentModal from "./componentModal";
import partsModal from './partsModal'
import drainageModal from "./drainageModal";
export default {
    name: "DeviceSelect",
    components: {
        deviceModal,
        componentModal,
        partsModal,
        drainageModal
    },
    props: {
        buttonName: { default: "关联设备" }, // 默认按钮名称
        type: { default: "device" }, // device：设备  component：部件（点位） parts:部件
        multiple: { default: false },
        modelId: { default: "" }, // 模块id
        allNotShow: { default: false },
        buttonType: { default: "button" }, // 按钮类型
        defaultSelList: { type: Array, default: () => [] }, // 默认选中列表
        // modelValue: { default: "" },
    },
    data() {
        return {
            componentName: "",
            device: {},
            showDeleteIcon: false,
            title: "请选择设备进行关联",
            searchTitle: "请输入名称或设备编号",
        };
    },
    created() {
        this.initCom();
    },
    methods: {
        initCom() {
            if (this.type == "device") {
                this.componentName = "deviceModal";
            } else if (this.type == "component") {
                this.componentName = "componentModal";
                this.title = "请选择监测点位进行关联";
                this.searchTitle = "请输入点位名称或点位标识码";
                // if (this.modelValue) {
                //     this.device.objId = this.modelValue;
                // }
                // if (this.type == "device") {
                //     this.componentName = "deviceModal";
                // } else if (this.type == "component") {
                //     this.componentName = "componentModal";
                //     this.title = "请选择监测点位进行关联";
                //     this.searchTitle = "请输入点位名称或点位标识码";
                // }
            } else if (this.type == "parts") {
                this.componentName = "partsModal";
                this.title = "请选择部件进行关联";
                this.searchTitle = "请输入部件名称或部件标识码";
            } else if (this.type == "drainage") {
                this.componentName = "drainageModal";
            }
        },
        showModal() {
            this.$refs.device.init(this.modelId);
        },
        changeDevice(data) {
            // eslint-disable-next-line no-empty
            if (this.multiple) {
            } else {
                this.device = data;
            }
            this.$emit("on-change", data);
            if (this.device.objId) this.$emit("update:modelValue", this.device.objId);
        },
        mouseenter() {
            this.showDeleteIcon = true;
        },
        mouseleave() {
            this.showDeleteIcon = false;
        },
        deleteObjId() {
            this.device = {};
            this.$emit("on-change", this.device);
        },
    },
};
</script>

<style lang="less" scoped>
.device-select {
    &.button {
        display: inline-block;
        margin-right: 8px;
        margin-bottom: 16px;
        .ivu-btn{
            margin-bottom: 0;
            margin-right: 0;
        }
    }
}

.select-box {
    border: 1px solid #dcdee2;
    height: 32px;
    position: relative;
    cursor: pointer;

    .name,
    .ivu-select-placeholder {
        height: 30px;
        line-height: 30px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        padding-left: 8px;
        padding-right: 24px;
    }

    .ivu-select-placeholder {
        color: @input-placeholder-color;
    }
}
</style>
