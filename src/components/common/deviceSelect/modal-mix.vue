
<script>
import useDebounce from "@/hooks/useDebounce";
import chooseDeviceBox from './chooseDeviceBox'
export default {
    name: 'modal-mix',
    props: {
        searchTitle: { default: '请输入名称或设备编号' },
        multiple: { default: false },
        modelId: { default: '' }, // 模块id
        defaultSelList: { type: Array, default: () => [] },
    },
    components: {
        chooseDeviceBox
    },
    data() {
        return {
            showFlag: false,
            m: {
                loading: false,
                deviceUnitList: [],
                selDevice: [], // 已选择设备
                selectList: [] // 当前表格的选择数据
            }
        }
    },
    computed: {
        selIdList() {
            return this.m.selDevice.map(item => item[this.keyName])
        }
    },
    methods: {
        confirm() {
            if (this.multiple) {
                if (this.m.selDevice.length === 0) {
                    this.$Message.warning('最少选择一条数据')
                    return
                }
                this.$emit('on-change', this.m.selDevice)
                this.showFlag = false
            } else {
                this.showFlag = false
            }
        },
        init(modelId) {
            this.showFlag = true
            this.m.selDevice = []
            this.m.selectList = []
            if(this.defaultSelList && this.defaultSelList.length > 0) {
              this.$nextTick(() => {              
                this.m.selectList = this.defaultSelList
                this.chooseMore()
              })
            }
            this.getData(modelId)
        },
        selectionChange(list) {
            // this.m.selectList = list || []
            let ids = []
            list.forEach(item => {
                let id = item[this.keyName]
                if (this.selIdList.indexOf(id) < 0) {
                    this.m.selDevice.push(item)
                    ids.push(id)
                }
            })
            if (ids.length > 0) {
                this.changeData(ids)
            }
        },
        // 删除已选择的设备
        delChoose(row, index) {
            this.m.selDevice.splice(index, 1)
            this.changeData([row[this.keyName]], false)
        },
        // 选择多个
        chooseMore() {
            if (this.m.selectList.length === 0) {
                this.$Message.warning('最少选择一条数据')
                return
            }
            let ids = []
            this.m.selectList.forEach(item => {
                this.m.selDevice.push(item)
                ids.push(item[this.keyName])
            })
            this.changeData(ids)
        },
        // 多选下选择单个
        selectOne(list, row) {
        },
        // 选择单个
        chooseOne(row) {
            if (this.multiple) {
                this.m.selDevice.push(row)
                this.changeData([row[this.keyName]])
            } else {
                this.$emit('on-change', row)
                this.showFlag = false
            }
        },
        searchInput(delay = 1000) {
            useDebounce(() => {
                this.search()
            }, delay)
        },
        search() {
            this.searchObj.modelId = this.modelId
            this.$refs.deviceTb.search(this.searchObj)
        },
        // 改变表格数据
        changeData(ids, dis = true) {
            this.$refs.deviceTb.changeData(list => {
                list.forEach(item => {
                    if (ids.indexOf(item[this.keyName]) >= 0) {
                        item._disabled = dis
                        item._checked = dis
                    }
                })
                return list
            })
        },
        // 初始化表格数据
        loadDone(data, list) {
            list.forEach(item => {
                if (this.selIdList.indexOf(item[this.keyName]) >= 0) {
                    item._disabled = true
                    item._checked = true
                }
                item.areaPath = this.$Util.getAddressPath(item) || item.areaPath
            })
            return list
        },
    }
}
</script>

<style lang="less" scoped>

</style>
