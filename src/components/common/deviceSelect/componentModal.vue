<template>
    <Modal
        v-model="showFlag"
        :title="title"
        :width="80"
        :mask-closable="false"
        class-name="fill-page-modal"
        transfer
        :footer-hide="!multiple"
        @on-visible-change="resetStatus"
    >
        <chooseDeviceBox v-if="multiple" title="已选部件">
            <div class="placeholder" v-show="m.selDevice.length === 0">请在下方选择部件</div>
            <Tooltip v-for="(item, index) in m.selDevice" :max-width="300">
                <Tag color="blue" closable @on-close="delChoose(item, index)">{{
                    item.objName
                }}</Tag>
                <template #content>
                    <p>部件名称: {{ item.objName }}</p>
                    <p>部件类型: {{ item.secondObjCategoryName }}</p>
                    <p>部件标识码: {{ item.objId }}</p>
                    <p>区域位置: {{ item.areaPath }}</p>
                </template>
            </Tooltip>
        </chooseDeviceBox>
        <Form :model="searchObj" :label-width="90" @submit.native.prevent>
            <Row>
                <Col span="8">
                    <FormItem label="名称编号" prop="objName">
                        <Input
                            v-model="searchObj.objName"
                            @on-change="() => searchInput()"
                            @on-enter="() => searchInput(100)"
                            @on-clear="() => searchInput(100)"
                            clearable
                            :placeholder="searchTitle"
                        ></Input>
                    </FormItem>
                </Col>
                <!-- <Col span="8">
                    <FormItem label="部件状态" prop="objState">
                        <Select
                            v-model="searchObj.objState"
                            @on-change="search"
                            clearable
                            placeholder="请选择"
                        >
                            <Option
                                v-for="(item, index) in $enumeration.objState"
                                :value="index"
                                :key="index"
                            >
                                {{ item }}
                            </Option>
                        </Select>
                    </FormItem>
                </Col> -->
                <Col span="8">
                    <FormItem label="点位类型" prop="objCategory2">
                        <Select
                            v-model="searchObj.objCategory2"
                            @on-change="search"
                            :disabled="objCategory2_disabled"
                            placeholder="请选择"
                        >
                            <Option
                                v-for="item in m.deviceUnitList"
                                :value="item.code"
                                :key="item.code"
                                >{{ item.name }}（{{ item.code }}）</Option
                            >
                        </Select>
                    </FormItem>
                </Col>
            </Row>
        </Form>
        <base-table
            ref="deviceTb"
            :columns="columns"
            url="/monitorPoint/selectPage"
            @on-selection-change="selectionChange"
            :loadDone="loadDone"
            size="small"
            :pageSize="15"
        >
            <!-- <template #objState="{ row }">
                <span>{{ $enumeration.objState[row.objState] }}</span>
            </template> -->
            <!-- <template #dwlx="{ row }">
                <span>{{ $store.getters.dictionary.obj_category[row.dwlx || ''] }}</span>
            </template> -->
            <template #action="{ row }">
                <link-btn size="small" @click="chooseOne(row)" :disabled="row._disabled"
                    >选择</link-btn
                >
            </template>
        </base-table>
        <template #footer>
            <div class="btn-box">
                <Button @click="showFlag = false">取消</Button>
                <Button type="primary" @click="confirm" :loading="m.loading">确定</Button>
            </div>
        </template>
    </Modal>
</template>

<script>
import { commonService } from "@/api/commonService";
import modalMix from "./modal-mix";
export default {
    name: "ComponentModal",
    mixins: [modalMix],
    props: {
        title: { default: "请选择监测点位进行关联" },
        searchTitle: { default: "请输入点位名称或点位标识码" },
    },
    emits: ["on-change"],
    data() {
        let columns = [];
        if (this.multiple) {
            columns.push({ type: "selection", width: 40, align: "center" });
        }
        columns = columns.concat([
            { title: "点位名称", key: "objName", tooltip: true },
            { title: "点位标识码", key: "objId", tooltip: true },
            { title: "区域位置", key: "areaPath", tooltip: true },
            // { title: "部件状态", slot: "objState", width: 80, tooltip: true },
            { title: "点位类型", key: "secondObjCategoryName", tooltip: true },
            { title: "操作", slot: "action", width: 80 },
        ]);
        return {
            columns,
            searchObj: {
                objName: "",
                objState: "",
                objCategory1: "",
                objCategory2: "",
                modelId: this.modelId,
            },
            objCategory2_disabled: false,
            keyName: "objId",
        };
    },

    mounted() {},
    methods: {
        resetStatus() {
            this.searchObj = {
                objName: "",
                objState: "",
                objCategory1: "",
                objCategory2: "",
                modelId: this.modelId,
            };
        },
        getData() {
            this.getDeviceUnitList();
        },
        // 设备型号
        getDeviceUnitList() {
            commonService.getObjCategoryConfig(this.modelId).then((res) => {
                if (res.success) {
                    this.searchObj.objCategory1 = res.data.objCategory1;
                    this.m.deviceUnitList = res.data.objCategoryList || [];
                    if (this.m.deviceUnitList.length > 0) {
                        this.searchObj.objCategory2 = this.m.deviceUnitList[0].code;
                        if (this.m.deviceUnitList.length == 1) {
                            this.objCategory2_disabled = true;
                        }
                    }
                    this.search();
                }
            });
        },
    },
};
</script>

<style lang="less" scoped></style>
