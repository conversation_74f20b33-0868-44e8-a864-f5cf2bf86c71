<template>
    <Row class="stat-list" :gutter="8">
        <Col v-for="item in statList" span="6">
            <div class="box">
                <div class="tit">{{item.title}}{{ title }}</div>
                <div class="stand-res"><span>{{ item.bqResult }}</span>{{ unit }}</div>
                <div class="bot-box">
                    <div class="td">
                        <div class="name">{{ item.before }}</div>
                        <div class="val"><span>{{ item.tqResult }}</span>{{ unit }}</div>
                    </div>
                    <compareBox :value="item.radio" />
                </div>
            </div>
        </Col>
    </Row>
</template>

<script>
import compareBox from '@/components/common/compareBox/index'
export default {
    name: 'titleCard',
    components: {
        compareBox
    },
    props: {
        title: { default: '发布信息' },
        unit: { default: '个' },
        stat: { default: () => {} }
    },
    data() {
        return {
            statList: [
                { title: '今日', before: '昨日同期', key: 'today', id: 1 },
                { title: '本周', before: '上周同期', key: 'week', id: 2 },
                { title: '本月', before: '上月同期', key: 'month', id: 3 },
                { title: '本年', before: '去年同期', key: 'year', id: 4 }
            ]
        }
    },
    watch: {
        stat: {
            handler(newVal) {
                if (!newVal) {
                    return
                }
                this.statList = this.statList.map((item, index) => {
                    let o = newVal[item.id] || {}
                    return { ...item, ...o }
                })
            },
            deep: true,
            immediate: true
        }
    },
    created() {
    },
    methods: {
    }
}
</script>

<style lang="less" scoped>
.stat-list{
    margin-bottom: 8px;
    .box{
        background: #fff;
        padding: 8px 16px;
        .tit{
            font-weight: 600;
            line-height: 24px;
            margin-bottom: 4px;
        }
        .stand-res{
            margin-bottom: 12px;
            span{
                font-size: 24px;
                line-height: 28px;
                font-weight: 700;
            }
        }
        .bot-box{
            display: flex;
            align-items: center;
            justify-content: space-between;
            .td{
                display: flex;
                align-items: center;
            }
            .name{
                color: rgba(78, 98, 126, 1);
                margin-right: 8px;
                font-size: 12px;
                line-height: 20px;
            }
            .val{
                color: rgba(78, 98, 126, 1);
                font-size: 12px;
                line-height: 20px;
                span{
                    font-size: 14px;
                }
            }
        }
    }
}
</style>
