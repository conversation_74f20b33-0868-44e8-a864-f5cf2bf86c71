import { Row, Col, Input, FormItem, Form, DatePicker, Select } from 'view-ui-plus'
import { ref, defineComponent, onUpdated, computed, watch,useSlots } from "vue";
import detailCard from '@/components/global/ContentCard/detailCard.vue'
import { isNullOrEmpty } from '@/utils/tool';
import { useRouter } from 'vue-router';
import Util from '@/utils'
import './index.less'
export default defineComponent({
  name: 'safeBaseInfo',
  components: {
    detailCard
  },
  props: {
    title: {
      default: '基础信息'
    },
    showBackBtn: {
      default: true
    },
    src: {
      default: require('@/assets/images/icon_detail.png'),//标题icon
    },
    editAuth: {
      default: 'a',//权限
    },
    data: {
      type:Object,
      default: () => { return {} }
    },
    rules: {
      default: () => {
        return {}
      }
    },
    modelForm: {
      default: () => {
        return {}
      }
    },
    gutter:{
      default:80
    },
    class:{
      default:''
    },
    list: {
      type:Array,
      default: () => {
        return [
          { label: "人员账号", key: "personnelAccount", disabled: true },
          { label: "姓名", key: "name", disabled: true },
          { label: "性别", key: "sex", disabled: true },
          { label: "联系电话", key: "mobile", disabled: true },
          { label: "电子邮箱", key: "email", disabled: true },
          { label: "岗位", key: "job", disabled: true },
          { label: "部门", key: "deptName", disabled: true },
          { label: "出生日期", key: "birthday", disabled: true },
          { label: "身份证号码", key: "idCard", disabled: true },
          { label: "健康状态", key: "health", disabled: false },
          { label: "备注", key: "remark", disabled: false },
        ]
      }
    }
  },
  setup(props, ctx) {
    const { emit, slots, expose }: any = ctx
    const isEdit = ref<boolean>(false)
    const form = ref<any>({})
    const router = useRouter()
    onUpdated(() => {
      form.value = props.data || {}
    })
    function handleEdit(flag: boolean) {
      isEdit.value = flag
      emit('on-change', flag, form.value)
    }
    const detailCardCom = ref()
    const formCom = ref()
    function handleSubmit() {
      // isEdit.value = false
      formCom.value.validate((flag: boolean) => {
        if (flag) {
          emit('on-submit', form.value)
        }
      })
    }
    watch(()=>form.value,()=>{
      emit('update:modelValue',form.value)
    },{deep:true})
    expose({
      closeEdit: () => detailCardCom.value.handleEdit(false),
      handleEdit,
      handleSubmit,
      form: computed(() => form.value),
      isEdit: isEdit.value
    })
    return () => (
      <>
        <detailCard class={props.class} onOn-edit={handleEdit} ref={detailCardCom} onOn-submit={handleSubmit} src={props.src}
          is-back-btn={props.showBackBtn} is-edit-btn={Util.checkAuth(props.editAuth)} onOn-back={() => router.back()}
          title={props.title}>
              <div class="otherBtn">
               { slots.otherBtn && slots.otherBtn()}
              </div>
          {
            !isEdit.value ? <div class="label-1">
              <Row>
                {
                  props.list.map((item: any) => {
                    if (item.slot) {
                      if (item.span) {
                        return <Col span={item.span}>
                          {
                            slots[item.slot] ? slots[item.slot](form.value,item) : ''
                          }
                        </Col>
                      } else {
                        return <Col span="8">
                          {
                            slots[item.slot] ? slots[item.slot](form.value,item) : ''
                          }
                        </Col>
                      }

                    }
                    if (item.span) {
                      return <Col span={item.span}>
                        <s-label {...item} tooltip={!item.tooltip ? item.tooltip : true} bold={item.bold || false} label={item.label} value={form.value[item.key]} />
                      </Col>
                    } else {
                      return <Col span="8">
                        <s-label  {...item} bold={item.bold || false} label={item.label} value={form.value[item.key]} />
                      </Col>
                    }
                  })
                }

              </Row>
            </div>
              :
              <div class="form-1">
                <Form model={form.value} ref={formCom} rules={props.rules} label-position="top">
                  <Row gutter={props.gutter}>
                    {
                      props.list.map((item: any) => {
                        if (item.hidden) return;
                        if (item.slot) {
                          if (item.span) {
                            return <Col span={item.span}>
                              <FormItem style={`width:${item.width}`}  label={item.label} prop={item.slot}>
                                {
                                  slots[item.slot] ? slots[item.slot](form.value,item) : ''
                                }
                              </FormItem>

                            </Col>
                          } else {
                            return <Col span="8">
                              <FormItem style={`width:${item.width}`} label={item.label} prop={item.slot}>
                                {
                                  slots[item.slot] ? slots[item.slot](form.value,item) : ''
                                }
                              </FormItem>
                            </Col>
                          }
                        }
                        if (item.span) {
                          if (item.type === 'textarea') {
                            return <Col span={item.span}>
                              {/* maxlength={item.maxlength || 200} show-word-limit={item.showWordLimit || false} type={item.type} rows={item.rows} disabled={item.disabled} */}
                              <FormItem style={`width:${item.width}`} label={item.label} prop={item.key}>
                                <Input clearable v-model={form.value[item.key]} placeholder="请输入" {...item}></Input>
                              </FormItem>
                            </Col>
                          }
                          return <Col span={item.span} >
                            <FormItem style={`width:${item.width}`} label={item.label} prop={item.key}>
                              <Input clearable v-model={form.value[item.key]} placeholder="请输入"  {...item}></Input>
                            </FormItem>
                          </Col>
                        } else {
                          return <Col span="8">
                            <FormItem style={`width:${item.width}`} label={item.label} prop={item.key} >
                              <Input clearable v-model={form.value[item.key]} placeholder="请输入"  {...item}></Input>
                            </FormItem>
                          </Col>
                        }
                      })
                    }
                  </Row>
                  <Row v-if={slots?.default}>
                  {slots.default ? slots.default() : ''}
                  </Row>
                </Form>
              </div>
          }
        </detailCard >
      </>

    )
  }
})
