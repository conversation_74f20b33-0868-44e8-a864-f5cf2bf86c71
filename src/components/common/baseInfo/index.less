.label-1 {
  .ivu-row {
    .s-label {
      .value {
        padding-right: 12px;
      }
    }
  }
}


.center-1 {
  .ivu-row {
    .s-label {
      .value {
        padding-right: 12px;
      }
    }
  }
}

.step,
.step2 {
  height: 40px;
  display: flex;
  position: relative;

  .number {
    position: absolute;
    left: -28px;
    height: 28px;
    width: 28px;
    border-radius: 50%;
    font-size: 16px;
    text-align: center;
    line-height: 28px;
    background: #F2F3F5;
    color: #4E5969;
  }

  .active {
    background: @primary-color ;
    color: #fff;
  }

  .title-objId {
    padding-left: 12px;
    font-size: 16px;
    font-weight: 500;
    line-height: 28px;
    color: #4E5969;
  }

  .title-objId-active {
    color: #1D2129;

  }
}

/deep/ .ivu-form-item-content .device-select {
  .select-box {
    width: 100%;
  }
}

.detail-card {
  position: relative;

  .otherBtn {
    position: absolute;
    top: 17px;
    right: 25px;
  }
}