import { Row, Col, Input, FormItem, Form, Button, Select, Option } from 'view-ui-plus'
import { ref, defineComponent, watch, computed } from "vue";
import detailCard from '@/components/global/ContentCard/detailCard.vue'
import { useRouter } from 'vue-router';
import { ComponentInfo } from '@/api/fireHydrantService'
import { handleCoordinate, isNullOrEmpty } from '@/utils/tool'
import './index.less'
export default defineComponent({
  name: 'addForm',
  components: {
    detailCard
  },
  props: {
    src: {
      default: require('@/assets/images/icon_detail.png'),//标题icon
    },
    title: {
      default: '选择部件',
    },
    rules: {
      default: () => {
        return {}
      }
    },
    showBtn: {
      default: true//显示确认取消按钮
    },
    ruleMessage: {
      default: '请选择储备库编号'
    },
    showObj: {
      default: false //显示选择部件
    },
    objLabel: {
      default: () => {
        return {
          label: '储备库编号',
          key: 'libraryCode'
        }
      }
    },
    modelValue: {
      default: () => { return {} }
    },
    showBackBtn: {
      default: true
    },
    modelId: {
      default: 0
    },
    modelForm: {
      default: () => {
        return {}
      }
    },
    gutter: {
      default: 80
    },
    list: {
      type: Array,
      default: () => {
        return [
          { label: "人员账号", key: "personnelAccount", disabled: true, },
          { label: "姓名", key: "name", disabled: true },
          { label: "性别", key: "gender", disabled: true },
          { label: "联系电话", key: "phone", disabled: true },
          { label: "电子邮箱", key: "email", disabled: true },
          { label: "岗位", key: "job", disabled: true },
          { label: "部门", key: "department", disabled: true },
          { label: "出生日期", key: "birthDate", disabled: false },
          { label: "身份证号码", key: "ID", disabled: false },
          { label: "健康状态", key: "health", disabled: false },
          { label: "备注", key: "remark", disabled: false },
        ]
      }
    }
  },
  setup(props, ctx) {
    const router = useRouter()
    const { emit, expose, slots }: any = ctx
    const step = ref<number>(0)
    const form = ref<any>({})
    function back() {
      router.back()
    }
    // 提交表单得到表单对象
    function addSubmit(objInfo: ComponentInfo) {
      formCom.value.resetFields()
      if (!isNullOrEmpty(objInfo)) {
        step.value = 1
        form.value = objInfo
      } else {
        step.value = 0
        form.value = objInfo
        emit('update:modelValue', form.value)
      }
    }
    // 提交添加
    const formCom = ref()
    function handleSubmit() {
      if (props.showObj) {
        formCom.value.validate((flag: boolean) => {
          console.log(flag, 'flag');
          if (flag) {
            emit('on-submit', form.value)
            return true
          }
        })
      } else {
        formCom.value.validate((flag: boolean) => {
          if (flag) {
            emit('on-submit', form.value)
            return true
          }
        })
      }
    }
    expose({ handleSubmit })
    watch(() => form.value, () => {
      emit('update:modelValue', form.value)
    }, { deep: true })
    watch(() => props.modelValue, (newVal, oldVal) => {
      form.value = newVal
    }, { deep: true, immediate: true })
    return () => (
      <>
        <Form class="device-obj" rules={props.rules} model={form.value} label-position='top' ref={formCom}>
          <Row gutter={props.gutter}>
            {
              props.list.map((item: any) => {
                if (item.slot) {
                  if (item.span) {
                    return <Col span={item.span} {...item}>
                      <FormItem style={`width:${item.width}`} label={item.label} prop={item.slot}>
                        {
                          slots[item.slot] ? slots[item.slot](form.value, item) : ''
                        }
                      </FormItem>

                    </Col>
                  } else {
                    return <Col span="8" {...item}>
                      <FormItem style={`width:${item.width}`} label={item.label} prop={item.slot}>
                        {
                          slots[item.slot] ? slots[item.slot](form.value, item) : ''
                        }
                      </FormItem>
                    </Col>
                  }
                }
                if (item.span) {
                  if (item.type === 'textarea') {
                    return <Col span={item.span} {...item}>
                      <FormItem style={`width:${item.width}`} label={item.label} prop={item.key}>
                        <Input clearable v-model={form.value[item.key]} placeholder="请输入" {...item}></Input>
                      </FormItem>
                    </Col>
                  }
                  return <Col span={item.span} {...item}>
                    <FormItem style={`width:${item.width}`} label={item.label} prop={item.key}>
                      <Input clearable v-model={form.value[item.key]} placeholder="请输入"  {...item}></Input>
                    </FormItem>
                  </Col>
                } else {
                  return <Col span="8" {...item}>
                    <FormItem style={`width:${item.width}`} label={item.label} prop={item.key} >
                      <Input clearable v-model={form.value[item.key]} placeholder="请输入"  {...item}></Input>
                    </FormItem>
                  </Col>
                }
              })
            }
          </Row>
          {slots.default ? slots.default() : ''}
          <div v-show={props.showBtn} class="footer-btn">
            <Button type="primary" onClick={handleSubmit}>提交</Button>
            <Button onClick={back}>取消</Button>
          </div>
        </Form >
      </>

    )
  }
})
