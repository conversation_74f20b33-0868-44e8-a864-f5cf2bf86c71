import sLabel from '@/components/global/BaseLabel';
import detailCard from '@/components/global/ContentCard/detailCard.vue'
import onlineStatus from '@/components/global/status/onlineStatus.vue'
import useStatus from '@/components/global/status/useStatus.vue'
import pushStatus from '@/components/global/pushstatus/pushStatus.vue'
import switchStatus from '@/components/global/switchStatus/index.vue'
import LinkBtn from '@/components/global/LinkBtn/index.vue'
import { Row, Col, FormItem, Form, Input } from "view-ui-plus";
import { ref, defineComponent, Slots, computed, h, watch, reactive, onMounted, onUpdated } from 'vue'
import { useRouter, useRoute } from 'vue-router';
import { enumeration } from '@/config/enumeration'
import { isNullOrEmpty } from '@/utils/tool';
import { formatDeviceValue } from '@/utils/formatDeviceValue'
import Util from '@/utils'
import request from '@/api/request';
import './index.less'
import { title } from 'process';
interface SetupContext {
  slots: Slots;
}
interface SlotList {
  title: string,
  value: number | string,
  slot: string
}

export default defineComponent({
  name: 'baseInfo',
  components: {
    sLabel,
    detailCard,
    onlineStatus,
    useStatus,
    pushStatus,
    switchStatus,
  },
  props: {
    src: {
      default: require('@/assets/images/icon_detail.png'),//标题icon
    },
    editAuth: {
      default: 'a',//权限
    },
    url: {
      default: '/garbageHouseDevice', //查询单条Url
    },
    isEdit:{
      default:false
    },
    centerCard: {
      default: () => {
        return {
          title: '设备属性',
          src: require('@/assets/images/icon-soil.png'),
          slotList: [
            { title: '在线状态', value: '', slot: 'status' }
          ],
          hideList: [
            { title: 'accessToken', value: '' }
          ]
        }
      }
    }
  },

  setup(props: any, ctx) {
    const { slots, emit,expose }: any = ctx
    const isEdit = ref<boolean>(props.isEdit)
    onUpdated(()=>{
      isEdit.value = props.isEdit
    })
    const route = useRoute()
    const router = useRouter()
    // 点击编辑或取消
    function handleEdit(flag: boolean) {
      isEdit.value = flag
      emit('on-edit', flag)
    }
    function back() {
      router.back()
    }
    const form = ref<any>({
      centerList: []
    })
    // 获取详情信息
    async function getDetailData() {
      let res: any = await request(`${props.url}/${route.query.id}`, null, 'get')
      if (res.success) {
        form.value = res.data
        if (!isNullOrEmpty(res.data.devicePropertyStatusList)) {
          form.value.centerList = res.data.devicePropertyStatusList.filter((item) => item.propName !== 'accessToken').map((item: any) => {
            return {
              title: item.propName,
              value: formatDeviceValue(item),
              key: item.prop
            }
          })
          props.centerCard.slotList.forEach((item: SlotList) => {
            item.value = form.value[item['slot']]
          })
          form.value.centerList = [...props.centerCard.slotList, ...form.value.centerList]
        }
      }
    }
    getDetailData()

    function handleSubmit() {
      emit('on-submit', form.value)
    }
    const values = reactive({
      '在线状态': '',
      '使用状态': '',
      '是否推送': '',
      '开关状态': ''
    })
    watch(values, () => {
      renderMap.value = {
        '在线状态': h(onlineStatus, {
          value: values['在线状态'],
        }),
        '使用状态': h(useStatus, {
          value: values['使用状态'],
        }),
        '是否推送': h(pushStatus, {
          value: values['是否推送'],
        }),
        '开关状态': h(switchStatus, {
          value: values['开关状态'],
        })

      }
    })
    const renderMap = ref({
      '在线状态': h(onlineStatus, {
        value: values['在线状态'],
      }),
      '使用状态': h(useStatus, {
        value: values['使用状态'],
      }),
      '是否推送': h(pushStatus, {
        value: values['是否推送'],
      }),
      '开关状态': h(switchStatus, {
        value: values['开关状态'],
      })
    })
    const textMap = reactive({
      '在线状态': enumeration.deviceOnlineState,
      '使用状态': enumeration.useStatus,
      '开关状态': enumeration.switchState,
      '是否推送': enumeration.isPush
    })
    function handleMap(item: SlotList) {
      if (Object.keys(item).includes('slot')) {
        if (!textMap[item.title]) return
        return textMap[item.title][item.value]
      }
    }
    // 设备属性详情
    const slotContent = computed(() => {
      if (isNullOrEmpty(form.value.centerList)) return
      return form.value.centerList.map((item: any) => {
        if (Object.keys(item).includes('slot')) {
          return <Col span="8">
            <s-label label={item.title}>
              {{
                value: () => {
                  // if(isNullOrEmpty(item.value)) return '--'
                  if (item.html) {
                    return slots[item.slot] ? slots[item.slot]() : '' //自定义label内容
                  }

                  values[item.title] = item.value
                  return renderMap.value[item.title]
                }
              }}
            </s-label>
          </Col>
        }
        if (item.title === '视频监控' || item.title === '播放地址') {
          return <Col span="8">
            <s-label label={item.title}>
              {{
                value: () => {
                  return h(LinkBtn, {
                    size: "small",
                    onClick: () => emit('on-look', item)
                  }, '查看')
                }
              }}
            </s-label>
          </Col>
        }
        return <Col span="8">
          <s-label label={item.title} value={item.value} />
        </Col>
      })
    })
    // 设备属性编辑
    const editContent = computed(() => {
      return <div class="form-2">
        <Form>
          <Row gutter={80}>
            {
              form.value.centerList.map((item: any) => {
                if (Object.keys(item).includes('slot')) {
                  return <Col span="8" >
                    <FormItem label={item.title}>
                      <Input model-value={handleMap(item)} disabled></Input>
                    </FormItem>
                  </Col>
                }
                return <Col span={item.span || 8} >
                  <FormItem label={item.title}>
                    <Input v-model={item.value} disabled></Input>
                  </FormItem>
                </Col>
              })
            }
          </Row>
        </Form>
      </div>
    })
    expose({
      isEdit:isEdit.value
    })
    return () => (
      <>
        <detailCard onOn-edit={handleEdit} onOn-submit={handleSubmit} src={props.src}
          is-back-btn={true} is-edit-btn={Util.checkAuth(props.editAuth)} onOn-back={back}
          title="基础信息">
          {
            !isEdit.value ? <div class="label-1">
              <Row gutter={80}>
                <Col span="8">
                  <s-label bold={true} class="code" label="设备编号" value={form.value.extendInfo?.deviceId || ''} />
                </Col>
                <Col span="16">
                  <s-label bold={true} label="区域位置" value={form.value.extendInfo?.areaPath} />
                </Col>
                <Col span="8">
                  <s-label label="设备名称" value={form.value.extendInfo?.sbmc || ''} />
                </Col>
                <Col span="8">
                  <s-label label="设备型号" value={form.value.extendInfo?.sbxh || ''} />
                </Col>
                <Col span="8">
                  <s-label label="设备类型" value={form.value.extendInfo?.deviceSecondTypeName || ''} />
                </Col>
                <Col span="8">
                  <s-label label="设备标识码" value={form.value.extendInfo?.bsm || ''} />
                </Col>
                <Col span="8">
                  <s-label label="设备状态" value={enumeration.deviceStateList[form.value.extendInfo?.sbzt] || ''} />
                </Col>
              </Row>
            </div>
              :
              <div class="form-1">
                <Form>
                  <Row gutter={80}>
                    <Col span="8">
                      <FormItem label="设备编号">
                        <Input model-value={form.value.extendInfo?.bsm} disabled></Input>
                      </FormItem>
                    </Col>
                   </Row>
                   <Row gutter={80}>
                    <Col span="8">
                      <FormItem label="设备名称">
                        <Input model-value={form.value.extendInfo?.sbmc} disabled></Input>
                      </FormItem>
                    </Col>
                    <Col span="8">
                      <FormItem label="设备型号">
                        <Input model-value={form.value.extendInfo?.sbxh} disabled></Input>
                      </FormItem>
                    </Col>
                    <Col span="8">
                      <FormItem label="设备类型">
                        <Input model-value={form.value.extendInfo?.deviceSecondTypeName} disabled></Input>
                      </FormItem>
                    </Col>
                    <Col span="8">
                      <FormItem label="设备标识码">
                        <Input model-value={form.value.extendInfo?.bsm} disabled></Input>
                      </FormItem>
                    </Col>
                    <Col span="8">
                      <FormItem label="设备状态">
                        <Input model-value={enumeration.deviceStateList[form.value.extendInfo?.sbzt]} disabled></Input>
                      </FormItem>
                    </Col>
                    <Col span="8">
                      <FormItem label="区域位置">
                        <Input model-value={form.value.extendInfo?.areaPath} disabled></Input>
                      </FormItem>
                    </Col>
                  </Row>
                </Form>
              </div>
          }
        </detailCard>
        <detailCard title={props.centerCard.title} src={props.centerCard.src}>
          {
            !isEdit.value ? <Row class="center-1" gutter={80}>
              {slotContent.value}
            </Row> : editContent.value
          }

        </detailCard>
      </>
    )
  }

})
