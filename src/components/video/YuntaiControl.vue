<template>

	<div class="">
		<Row type="flex" style="" class="main-con">
			<Col class="flex-auto">
				<div class="video" id="video-box">
					<div >
						<JudgeVideo ref="vidc" :video-obj="ysy.obj" :index="ysy.index" :styles="ysy.styles" />
					</div>

				</div>
			</Col>
			<Col class="m-right">
				<imouControl ref="imouC" />
			</Col>
		</Row>
	</div>

</template>

<script>
import Util from '@/libs/util'
import imouControl from './imouControl';
export default {
	name: 'imouYuntaiControl',
	components: {
		imouControl
	},
	props: {
		windowWidth: { default() { return window.innerWidth * 0.65 - 10 } },
		mainId: { default: 'imouConId' }
	},
	data() {
		return {
			ysy: {
				obj: {},
				styles: {},
				index: -1
			}

		}
	},
	computed: {

	},
	mounted() {
		this.ysy.styles = this.getStyles()
		console.log(this.windowWidth)
	},
	methods: {
		init(item, index) {
			// console.log(item)
			let data = Util.objClone(item)
			this.ysy.obj = data;
			this.ysy.index = index;
			this.$refs.imouC.init(data)
			this.clear()
			data.mainId = this.mainId
			setTimeout(() => {
				this.$refs.vidc.open(data)
			}, 100)
		},
		clear() {
			this.$refs.vidc.clear()
		},
		getStyles() {
			let el = document.getElementById('video-box')
			var w = this.windowWidth - 150 - 20,
				h = 500;
			if (el) {
				w = el.offsetWidth || w
				h = el.offsetHeight || h
			}
			console.log(el.offsetWidth)
			return {
				width: parseInt(w) + 'px',
				height: parseInt(h) + 'px',
				position: 'relative'
			}
		}
	}
}
</script>

<style lang="less" scoped>

.main-con{
	background: #414141;
	min-height: 500px;
	align-items: stretch;

	.flex-auto{
		flex: 1;
		padding: 10px;
	}
	.m-right{
		width: 150px;
		background: #5C5C5C;
	}

	.video{
		width: 100%;
		height: 100%;
		background: #000;
		position: relative;
	}

}
</style>



