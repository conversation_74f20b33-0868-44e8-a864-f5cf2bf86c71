<script>
import {h} from 'vue'
export default {

	name: 'imouOpen',
	props: {
		styles: {
			type: [Object],
			default: () => {
				return { width: '250px', height: '250px', position: 'relative' }
			}
		},
		domId: {
			type: [String],
			default: 'imouP'
		},
		mainId: {
			type: [String],
			default: 'imouMain'
		},
		controls: { default: true }
	},
	data() {
		return {
			videoObj: {}
		}
	},
	methods: {
		init(data) {
			return new Promise((resolve, reject) => {
				if (document.getElementById(this.domId)) {
					document.getElementById(this.domId).remove()
				}
				document.getElementById(this.mainId).innerHTML = ''
				console.log(data)
				let videoEL = document.createElement('div')
				videoEL.setAttribute('id', this.domId)
				videoEL.setAttribute('style', `width:${this.styles.width};height:${this.styles.height};position: relative;`)
				document.getElementById(this.mainId).appendChild(videoEL);
				this.player = new ImouPlayer('#' + this.domId);
				// 播放器初始化
				this.player.setup({
					src: data, // 播放地址
					width: this.styles.width ? this.styles.width.replace('px', '') : '250', // 播放器宽度
					height: this.styles.height ? this.styles.height.replace('px', '') : '250', // 播放器高度
					poster: '', // 封面图url
					autoplay: false, // 是否自动播放
					controls: false, // 是否展示控制栏
					decoderPath: '/dist/imou/static' // 路径，如有变动，同时需要手动修改workerManager.js里 videoWorker.js和audioWorker.js的引入地址
				});
				console.log(this.player)
				if (this.player) {
					setTimeout(() => {
						resolve(this.player)
					}, 600)
				}
			})
		},
		play(index) {
			if (this.player) {
				if (index || index === 0) {
					this.player.playerSet[index].play()
				} else {
					this.player.play()
				}
			}
		},
		pause(index) {
			if (this.player) {
				if (index || index === 0) {
					this.player.playerSet[index].pause()
				} else {
					this.player.pause()
				}
			}
		},
		stop(index) {
			if (this.player) {
				if (index || index === 0) {
					this.player.playerSet[index].stop()
				} else {
					this.player.stop()
				}
			}
		},
		setMultiScreen(num) {
			if (this.player) {
				this.player.setMultiScreen(num)
			}
		},
		setVolumeVideo(volume, index) {
			if (this.player) {
				if (index || index === 0) {
					this.player.playerSet[index].setVolume(volume)
				} else {
					this.player.setVolume(volume)
				}
			}
		},
		full() {
			if (this.player) {
				this.player.setFullScreen()
			}
		},
		exitFull() {
			if (this.player) {
				this.player.exitFullScreen()
			}
		},
		clear(index) {
			if (this.player) {
				if (index || index === 0) {
					this.player.playerSet[index].destroy()
				} else {
					this.player.destroy()
					this.player = null
				}
			}
		}
	},
	render(r, context) {
		return h('div', {
			style: this.styles,
			id: this.mainId
		}, [])
	}
}
</script>

<style lang="less" scoped>
</style>
