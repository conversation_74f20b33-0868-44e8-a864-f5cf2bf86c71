<template>

	<div class="im-con">
		<div class="tit"><span>云台控制</span></div>
		<div class="control" v-if="y_c.show">
			<div class="direction">
				<div class="box" :class="item.class" v-for="item in y_c.c.directionArr" :style="'visibility: ' + (item.show ? 'visible':'hidden')" >
					<div class="btn" v-if="item.class === 'p'" @click="chooseControl(item)">
						<Icon type="md-locate" color="#000" size="18" />
					</div>
					<div class="btn" v-else @click="chooseControl(item)">
						<Icon type="md-navigate" color="#000" size="15" />
					</div>
				</div>
			</div>
			<div class="step" v-if="y_c.duration.show">
				<p >时长</p>
				<Select v-model="y_c.duration.value" style="width: 55px;margin: 0 5px">
					<Option :value="n" v-for="n in y_c.duration.arr" :key="n">{{ n }}</Option>
				</Select>
				<span>秒</span>
			</div>
			<div class="zooms">
				<div class="box" v-for="item in y_c.c.zoomsArr" :key="item.id" :style="'visibility: ' + (item.show ? 'visible':'hidden')">
					<div class="sub btn" @click="chooseControl(item, item.value[0])"><Icon type="md-remove" color="#000" size="18" /></div>
					<p>{{ item.name }}</p>
					<div class="add btn" @click="chooseControl(item, item.value[1])"><Icon type="md-add" color="#000" size="18" /></div>
				</div>
			</div>
		</div>
		<div class="tit"><span>图像抓拍</span></div>
		<div class="snap" v-if="y_c.snap.show">
			<span @click="pictureCapture"><Icon type="ios-camera-outline" color="#fff" size="30" /></span>
		</div>
	</div>

</template>

<script>
import Util from '@/libs/util'

export default {
	name: 'imouControl',
	components: {},
	props: {
	},
	data() {
		return {
			y_c: {
				show: false,
				c: {
					directionArr: [
						{ name: '左上', class: 'lt', value: '4', show: false },
						{ name: '上', class: 't', value: '0', show: false },
						{ name: '右上', class: 'rt', value: '6', show: false },
						{ name: '左', class: 'l', value: '2', show: false },
						{ name: '定位', class: 'p', value: '10', show: false },
						{ name: '右', class: 'r', value: '3', show: false },
						{ name: '左下', class: 'lb', value: '5', show: false },
						{ name: '下', class: 'b', value: '1', show: false },
						{ name: '右下', class: 'rb', value: '7', show: false }
					],
					zoomsArr: [
						{ name: '变倍', id: 1, value: ['9', '8'] }
					]
				},
				duration: {
					show: false,
					value: 1,
					arr: [0.5, 1, 2, 3, 4, 5]
				},
				snap: {
					show: false
				}
			},
			ysy: {
				obj: {},
				index: -1
			}

		}
	},
	computed: {

	},
	mounted() {
	},
	methods: {
		init(item, index) {
			// console.log(item)
			let data = Util.objClone(item)
			this.ysy.obj = data;
			this.getData()
		},
		getData() {
			Util.request('/deviceService/getDeviceServicesList', { deviceUnitId: this.ysy.obj.deviceUnitId }, 'post').then(resp => {
				let data = resp.data.data;
				let yun = this.$_.find(data, { identifier: 'ptz_control' })
				if (yun) {
					yun.paramList.forEach(o => {
						let specs = JSON.parse(o.specs)
						if (o.identifier == 'rotation_direction') {
							this.y_c.c.directionArr.forEach((item) => {
								if (specs[item.value]) {
									item.show = true
									item.identifier = o.identifier
									item.service_id = yun.identifier
								}
							})
							this.y_c.c.zoomsArr.forEach((item) => {
								if (specs[item.value[0]] || specs[item.value[1]]) {
									item.show = true
									item.identifier = o.identifier
									item.service_id = yun.identifier
								}
							})
						} else if (o.identifier == 'duration') {
							this.y_c.duration.identifier = o.identifier
							this.y_c.duration.show = true
							this.y_c.duration.service_id = yun.identifier
						}
					})
					this.y_c.show = true
				}
				let pic = this.$_.find(data, { identifier: 'picture_capture' })
				if (pic) {
					this.y_c.snap.show = true
					this.y_c.snap.service_id = pic.identifier
					this.y_c.snap.identifier = pic.paramList[0].identifier
				}
				console.log(this.y_c)
			})
		},
		chooseControl(item, value, noMsg) {
			let duration = this.y_c.duration
			let parameter = {};
			parameter[item.identifier] = value || item.value
			parameter[duration.identifier] = duration.value

			let param = {
				downParameter: {
					device_id: this.ysy.obj.deviceCode,
					service_id: item.service_id,
					parameter: parameter
				}
			}
			this.send(param, noMsg)
		},
		pictureCapture() {
			let item = this.y_c.snap
			let parameter = {};
			parameter[item.identifier] = 1
			let param = {
				downParameter: {
					device_id: this.ysy.obj.deviceCode,
					service_id: item.service_id,
					parameter: parameter
				}
			}
			this.send(param)
		},
		send(param, noMsg = false) {
			console.log(param)
			Util.request('/deviceManage/send', param, 'post').then(resp => {
				if (resp.data.data.code == '0') {
					if (!noMsg) {
						this.$Message.success(resp.data.data.msg || '命令下发成功')
					}
				} else {
					if (!noMsg) {
						this.$Message.error(resp.data.data.msg)
					}
				}
			})
		}
	}
}
</script>

<style lang="less" scoped>
.im-con{
	.tit{
		color: #fff;
		line-height: 32px;
		font-size: 13px;
		border-bottom: 1px solid #fff;
		span{
			display: inline-block;
			padding: 0 10px;
		}
	}
	.control{
		padding: 10px;
		color: #fff;
		margin-bottom: 15px;

		.btn{
			width: 30px;
			height: 30px;
			background: #BCBCBD;
			border-radius: 50%;
			text-align: center;
			line-height: 30px;
			cursor: pointer;
			.ivu-icon{
				vertical-align: middle;
			}
		}

		.direction{
			display: flex;
			flex-wrap: wrap;
			justify-content: space-between;
			margin-bottom: 15px;

			.box{
				margin: 6px;
			}
			.lt i{transform: rotate(-45deg);}
			.rt i{transform: rotate(45deg);}
			.l i{transform: rotate(-90deg);}
			.r i{transform: rotate(90deg);}
			.lb i{transform: rotate(-135deg);}
			.b i{transform: rotate(180deg);}
			.rb i{transform: rotate(135deg);}
		}

		.step{
			display: flex;
			align-items: center;
			justify-content: center;
			font-size: 13px;
			margin-bottom: 10px;

			.ivu-select{
				width: 60px;
				line-height: 20px;
				margin-left: 5px;

				/deep/ .ivu-select-selection{
					height: 20px;

					.ivu-select-placeholder,
					.ivu-select-selected-value{
						line-height: 20px;
						height: 20px;
						font-size: 13px;
					}
				}
				.ivu-select-item{
					padding: 2px 10px;
					font-size: 13px;
				}
			}
		}

		.zooms{
			.box{
				padding: 6px;
				display: flex;
				align-items:center;
				justify-content: space-between;

				p{
					padding:0 5px;
					text-align:center;
					font-size: 13px;
				}
			}
		}
	}

	.snap{
		padding: 8px 15px;
		span{
			cursor: pointer;
		}
	}
}
</style>



