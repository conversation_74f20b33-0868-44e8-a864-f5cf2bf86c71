<template>
	<div class="video-mp4" :id="mainId" :style="styles">
		<video ref="videoPlayer" class="video-js" :style="styles"></video>
	</div>
</template>

<script>
import videoJs from 'video.js'
import 'video.js/dist/video-js.css'
export default {
	name: 'videoJsPlay',
	props: {
		styles: {
			type: [Object],
			default: () => {
				return { width: '100%', height: '100%' }
			}
		},
		domId: {
			type: [String],
			default: 'videoMp4'
		},
		mainId: {
			type: [String],
			default: 'videoMp4Main'
		},
	},
	data() {
		return {}
	},
	methods: {
		open(url) {
			return new Promise((resolve, reject) => {
				if (!this.player && url) {
					console.log('url', url)
					this.player = videoJs(this.$refs.videoPlayer, {
						playbackRates: [0.5, 1, 1.5, 2],
						autoplay: true,
						controls: true,
						sources: [
							{
								type: "video/mp4",
								src: url
							}
						]
					}, function onPlayerReady() {
						console.log('onPlayerReady', this);
					})
					resolve()
				}
			})

		}
	}
}
</script>

<style lang="less" scoped>
.video-mp4{
	position: relative;
	width: 100%;
	height: 100%;
}
</style>
