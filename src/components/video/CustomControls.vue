<template>
	<!-- 自定义控件 -->
	<div class="cuscon-main">
		<div class="controls">
			<Row justify="space-between" class="action">
				<Col class="row">
					<div class="box">
						<Icon type="md-play" v-show="!v.isPlay" @click="playVideo" />
						<Icon type="md-pause" v-show="v.isPlay" @click="pauseVideo" />
					</div>
					<div class="box">
						<Icon type="md-volume-up" size="16" v-show="v.volume > 0" @click="offVolumeMute" />
						<Icon type="md-volume-off" size="16" v-show="v.volume === 0" @click="upVolumeMute" />
					</div>
					<div class="box volume">
						<Slider v-model="v.volume" @on-change="changeVolumeMute" :min="0" :max="100" show-tip="never"></Slider>
					</div>
				</Col>
				<Col class="row">
					<!-- <div class="box">
						<Icon type="md-camera" size="18" />
					</div>
					<div class="box">
						<Icon type="ios-videocam" size="18" />
					</div> -->
					<div class="box" v-if="hasFullBtn">
						<Icon type="md-expand" v-show="!v.isFull" @click="fullScreen" size="16" />
						<Icon type="md-contract" v-show="v.isFull" @click="exitScreen" size="16" />
					</div>
				</Col>
			</Row>
		</div>
		<div class="play-loading" v-show="v.isPlayLoading">
			<Icon type="ios-loading" />
		</div>
	</div>
</template>

<script>
export default {

	name: 'CustomControls',
	props: {
		hasFullBtn: { default: true },
		defaultPlayFlag: { default: true },
		fullElementId: { default: '' }
	},
	data() {
		return {
			v: {
				isPlay: true,
				isMute: true,
				volume: 0, // 音量
				isFull: false,
				isPlayLoading: false
			}
		}
	},
	watch: {
		defaultPlayFlag(newV) {
			this.v.isPlay = newV
		}
	},
	mounted() {
		this.v.isPlay = this.defaultPlayFlag
	},
	methods: {
		init() {
			this.v.isPlay = true
			this.v.isMute = true
			this.v.volume = 0
		},
		playVideo() {
			this.v.isPlay = true
			this.$emit('on-play')
		},
		pauseVideo() {
			this.v.isPlay = false
			this.$emit('on-pause')
		},
		upVolumeMute() {
			this.v.volume = 30
			this.setVolumeVideo()
		},
		offVolumeMute() {
			this.v.volume = 0
			this.setVolumeVideo()
		},
		changeVolumeMute(value) {
			this.v.volume = value
			this.setVolumeVideo()
		},
		setVolumeVideo() {
			let num = this.v.volume / 100
			this.$emit('on-set-volume', num)
		},
		setFullFlag(flag) {
			this.v.isFull = flag
		},
		fullScreen() {
			if (this.fullElementId) {
				let element = document.getElementById(this.fullElementId)
				if (element.requestFullscreen) {
					element.requestFullscreen()
				} else if (element.mozRequestFullScreen) {
					element.mozRequestFullScreen()
				} else if (element.msRequestFullscreen) {
					element.msRequestFullscreen()
				} else if (element.webkitRequestFullscreen) {
					element.webkitRequestFullScreen()
				}
				element.addEventListener('fullscreenchange', (e) => {
					if (!this.isFullFun()) {
						this.$refs.controlsR.setFullFlag(false)
						this.v.isFull = false
					}
				})
			}
			this.v.isFull = true
			this.$emit('on-full-screen')
		},
		exitScreen() {
			if (document.exitFullscreen) {
				document.exitFullscreen()
			} else if (document.msExitFullscreen) {
				document.msExitFullscreen()
			} else if (document.mozCancelFullScreen) {
				document.mozCancelFullScreen()
			} else if (document.webkitExitFullscreen) {
				document.webkitExitFullscreen()
			}
			this.v.isFull = false
			this.$emit('on-exit-screen')
		}
	}
}
</script>

<style lang="less" scoped>
.cuscon-main{
	.controls{
		position: absolute;
		width: 100%;
		padding-left:5px ;
		padding-right:5px ;
		bottom: 2px;
		left: 0;
		z-index: 99;
		// padding: 0 14px;
		color: #fff;
		background: rgba(0,0,0,0.5);
		.row{
			display: flex;
			align-items: center;
		}
		.action{
			.box{
				position: relative;
				height: 30px;
				line-height: 30px;
				margin: 0 2px;
				i{
					height: 30px;
					line-height: 30px;
					min-width: 30px;
					text-align: center;
					cursor: pointer;
				}
			}
			.volume{
				width: 100px;
				margin-left: 10px;
			}
		}

	}
	/deep/.ivu-progress{
		padding: 0 8px;
		.ivu-progress-inner{
			background: #565656;
		}
	}
	/deep/.ivu-slider-wrap{
		margin: 13px 0;
	}

	.play-loading{
		position: absolute;
		top: 0;
		left: 0;
		bottom: 0;
		right: 0;
		margin: auto;
		z-index: 10;
		color: #fff;
		font-size: 60px;
		width: 100px;
		height: 100px;
		text-align: center;
		line-height: 100px;
		i{
			animation: ani-demo-spin 1s linear infinite;
		}
	}
}
@keyframes ani-demo-spin {
	from { transform: rotate(0deg);}
	50%  { transform: rotate(180deg);}
	to   { transform: rotate(360deg);}
}
</style>
