<template>
	<div class="time-slider">
		<div class="main-slider" id="main-slider">
			<Slider v-model="curMax" disabled :marks="marks" :min="0" :max="daySecond" class="bg-slider"></Slider>
			<Slider v-model="timeVal" :max="curMax" @on-change="changeTime" :tip-format="getTimeBySeconds" :style="sliderStyle" range :min="0" class="box-slider"></Slider>
		</div>
	</div>
</template>

<script>
const hourSecond = 60 * 60; // 一小时有多少秒
const daySecond = 24 * hourSecond; // 一天有多少秒
export default {
	name: 'timeSlider',
	data() {
		return {
			timeVal: [0, 0],
			daySecond,
			curMax: daySecond,
			isToday: false, // 是否今天
			curObj: {}
		}
	},
	computed: {
		marks() {
			let obj = {}
			for (let i = 0; i <= 24; i++) {
				obj[i * hourSecond] = (i < 10 ? '0' + i : i) + ':00'
			}
			return obj
		},
		sliderStyle() {
			if (!this.isToday) {
				return { width: '100%' }
			}
			if (this.curMax) {
				let w = (this.curMax / this.daySecond) * 100
				return { width: w + '%' }
			}
			return ''
		}
	},
	mounted() {
	},
	methods: {
		changeTime(val) {
			let obj = {
				startTime: this.curObj.day + ' ' + this.getTimeBySeconds(val[0]),
				endTime: this.curObj.day + ' ' + this.getTimeBySeconds(val[1])
			}
			console.log(obj)
			this.$emit('on-change', obj)
		},
		init(data) {
			this.curObj = data
			let todayT = this.$Util.formatDate(new Date(), 'yyyy-MM-dd')
			this.isToday = todayT === data.day
			if (this.isToday) {
				// 今天的秒数
				this.curMax = this.getSecondByTime(this.$Util.formatDate(new Date(), 'hh:mm:ss'))
			} else {
				this.curMax = daySecond
			}
			this.timeVal[0] = this.getSecondByTime(data.startTime)
			this.timeVal[1] = this.getSecondByTime(data.endTime)
			console.log(this.timeVal, this.curMax)
		},
		getSecondByTime(time) {
			let arr = time.split(':')
			let second = arr[0] * hourSecond + arr[1] * 60 + arr[2] * 1
			return second
		},
		// 通过秒得到时分秒
		getTimeBySeconds(time) {
			let h = Math.floor(time / (60 * 60))
			let m = Math.floor(time / 60 % 60)
			let s = time % 60
			let arr = []
			h = h > 9 ? h : '0' + h
			arr.push(h)
			m = m > 9 ? m : '0' + m
			s = s > 9 ? s : '0' + s
			arr.push(m)
			arr.push(s)
			return arr.join(':')
		}
	}
}
</script>

<style lang="less" scoped>
.time-slider{
	padding: 1px 24px;
	height: 60px;
	.main-slider{
		position: relative;
	}
	.bg-slider{
		/deep/.ivu-slider-wrap{
			background: #8091b7;
			.ivu-slider-bar{
				background: #5987f1;
			}
			.ivu-slider-marks-item{
				color: #fff;
			}
		}
		/deep/.ivu-slider-button-wrap{
			display: none;
		}
	}
	.box-slider{
		position: absolute;
		top: -16px;
		left: 0;
		width: 100%;
		/deep/.ivu-slider-wrap{
			background: none;
			.ivu-slider-bar{
				background: none;
			}
		}
		/deep/.ivu-tooltip-inner{
			white-space: nowrap;
		}
	}
}
</style>
