import { defineComponent, Slots } from "vue";
import "./tag.less";
interface SetupContext {
    slots: Slots;
}
export default defineComponent({
    name: "sTag",
    props: {
        color: {
            type: String,
            default: "#00B42A",
        },
        background: { type: String, default: "#E8FFEA" },
        size: { default: 'small' } // large small
    },
    setup(props, { slots }: SetupContext) {
        return () => (
            <div style={{ ...props }} class={'tag-cont ' + props.size}>
                {slots?.default!()}
            </div>
        );
    },
});
