<template>
    <Transfer
        :data="data"
        :target-keys="targetKeys"
        :titles="titles"
        filterable
        :render-format="renderFormat"
        @on-change="handleChange">
        <div class="transfer-footer">
            <Button size="small" type="grey" @click="refresh">重置</Button>
        </div>
    </Transfer>
</template>

<script>
export default {
    name: 'sTransfer',
    props: {
        data: { default() { return [] } },
        titles: { default() { return ['未选择', '已选择'] } },
        defaultTargetKeys: { default() { return [] } }, // 默认已选择
    },
    data() {
        return {
            targetKeys: this.$Util.objClone(this.defaultTargetKeys)
        }
    },
    mounted() {
    },
    watch: {
        defaultTargetKeys() {
            this.targetKeys = this.$Util.objClone(this.defaultTargetKeys)
        }
    },
    methods: {
        refresh() {
            this.targetKeys = this.$Util.objClone(this.defaultTargetKeys)
            this.$emit('on-reset',this.targetKeys)
        },
        handleChange(keys, direction, moveKeys) {
            this.targetKeys = keys
            this.$emit('on-change', keys, direction, moveKeys)
        },
        renderFormat(item) {
            return item.label || item.key || '--'
        }
    }
}
</script>

<style lang="less" scoped>

</style>
