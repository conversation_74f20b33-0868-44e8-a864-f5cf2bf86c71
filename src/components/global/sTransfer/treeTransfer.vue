<template>
  <div class="ivu-transfer-list ivu-transfer-list-with-footer">
    <div class="ivu-transfer-list-header">
      <Checkbox v-model="unselectedAll" :disabled="!unselectedData.length" @on-change="clickUnSelectedAll" /><span
        class="ivu-transfer-list-header-title">{{
          titles[0] }}</span><span class="ivu-transfer-list-header-count">{{
            isTree?unselectedData.length:( data.length - defaultTargetKeys.length)
  }}</span>
    </div>
    <div class="ivu-transfer-list-body ivu-transfer-list-body-with-search ivu-transfer-list-body-with-footer">
      <div class="ivu-transfer-list-body-search-wrapper">
        <div class="ivu-transfer-list-search">
          <div class="ivu-input-wrapper ivu-input-wrapper-small ivu-input-type-text">
<!----><i
              class="ivu-icon ivu-icon-ios-search ivu-input-icon ivu-input-icon-normal"></i><!---->
            <input @change="searchFilter($event, 'unselected')" autocomplete="off" spellcheck="false" type="text"
              class="ivu-input ivu-input-small search-input" placeholder="请输入搜索内容" number="false"><!---->
          </div>
        </div>
      </div>
      <ul class="ivu-transfer-list-content">
        <Tree :data="unselectedData" @on-check-change="selectedDataChange" show-checkbox class="tree-box">
        </Tree>
        <li class="ivu-transfer-list-content-not-found">列表为空</li>
      </ul>
    </div>
    <div class="ivu-transfer-list-footer">
      <div class="transfer-footer">
<button @click="reset('unselected')" class="ivu-btn ivu-btn-grey ivu-btn-small"
          type="button">
<span></span>重置
</button>
</div>
    </div>
  </div>
  <div class="ivu-transfer-operation">
    <Button @click="toLeft" :disabled="!unselected.length" class="ivu-btn ivu-btn-primary ivu-btn-small"
      type="button">
<span></span><i class="ivu-icon ivu-icon-ios-arrow-back"></i>
</Button>
    <Button @click="toRight" :disabled="!selected.length" class="ivu-btn ivu-btn-primary ivu-btn-small"
      type="button">
<span></span><!----><i class="ivu-icon ivu-icon-ios-arrow-forward"></i>
</Button>
  </div>

  <div class="ivu-transfer-list ivu-transfer-list-with-footer">
    <div class="ivu-transfer-list-header">
      <Checkbox v-model="selectedAll" @on-change="clickSelectedAll" :disabled="selectedAll.length === 0" /><span
        class="ivu-transfer-list-header-title">{{ titles[1] }}</span><span class="ivu-transfer-list-header-count">{{
          selectedData.length
        }}</span>
    </div>
    <div class="ivu-transfer-list-body ivu-transfer-list-body-with-search ivu-transfer-list-body-with-footer">
      <div class="ivu-transfer-list-body-search-wrapper">
        <div class="ivu-transfer-list-search">
          <div class="ivu-input-wrapper ivu-input-wrapper-small ivu-input-type-text">
<!----><i
              class="ivu-icon ivu-icon-ios-search ivu-input-icon ivu-input-icon-normal"></i><!---->
            <input @change="searchFilter($event, 'selected')" autocomplete="off" spellcheck="false" type="text"
              class="ivu-input ivu-input-small search-input" placeholder="请输入搜索内容" number="false"><!---->
          </div>
        </div>
      </div>
      <ul class="ivu-transfer-list-content">
        <Tree :data="selectedData" @on-check-change="unselectedDataChange" show-checkbox class="tree-box">
        </Tree>
        <li class="ivu-transfer-list-content-not-found">列表为空</li>
      </ul>
    </div>
    <div class="ivu-transfer-list-footer">
      <div class="transfer-footer">
<button @click="reset('selected')" class="ivu-btn ivu-btn-grey ivu-btn-small"
          type="button">
<span></span>重置
</button>
</div>
    </div>
  </div>
</template>

<script>
import { Button, Checkbox } from 'view-ui-plus'
import { deepClone, deleteItem, deleteRepeat, dfs, findValue, getValue, isEmpty } from 'wei-util'

export default {
  name: 'TreeTransfer',
  components: { Button, Checkbox },
  props: {
    width: { default: '333px', type: String },
    height: { default: '277px', type: String },
    data: { default() { return []; } },
    isTree: { default: true },
    titles: { default() { return ['未选择', '已选择']; } },
    defaultTargetKeys: { default() { return []; } }, // 默认已选择
  },
  data() {
    return {
      treeData: [],
      selected: [],
      copyDefaultTargetKeys: [],
      unselected: [],
      unselectedAll: false,
      selectedAll: false,
      unselectedData: [],
      selectedData: [],
      copyUnselectedData: [],
      copySelectedData: [],
    };
  },
  watch: {
    data: {
      deep: true,
      immediate: true,
      handler() {
        this.observeDataChange()
      }
    },
    defaultTargetKeys: {
      deep: true,
      immediate: true,
      handler() {
        this.observeDataChange()
      }
    },
  },
  mounted() {
    this.copyDefaultTargetKeys = deepClone(this.defaultTargetKeys)
  },
  methods: {
    isAllSelected() {
      let flag = true
      dfs(this.unselectedData, 'children', item => {
        if (!item.checked) flag = false
      })
      return flag
    },
    observeDataChange() {
      let treeData = deepClone(this.data);
      const transformData = (data, lastId) => {
        const result = [];
        data.forEach((item, index, arr) => {
          const newItem = {
            ...item,
            parentId: lastId || item.id,
            id: item.id,
            expand: true,
            selected: false,
            checked: false,
            title: item.label && item.label + (getValue(item, 'account') && `(${getValue(item, 'account')})` || '') || getValue(item, 'account'),
            value: item.id,
          }
          if (item.children && item.children.length) {
            newItem.children = transformData(item.children, newItem.parentId);
          }
          result.push(newItem);

        })
        return result;
      }
      treeData = transformData(treeData)
      const flatTree = []
      const selectedData = [] // 已选择的数据
      const handleIsSelected = (data, lastId) => {
        const copyData = deepClone(data)
        copyData.forEach((item, index) => {
          flatTree.push(item)
          const hasChildren = !isEmpty(getValue(item, 'children'))
          const isChecked = this.defaultTargetKeys.includes(item.id)
          const isAllChildrenChecked = hasChildren ? getValue(item, 'children', []).every((i) => this.defaultTargetKeys.includes(i.id)) : hasChildren
          if (this.isTree) {
            if (isAllChildrenChecked || isChecked) {
              deleteItem(data, item.id, 'id')
              if (isAllChildrenChecked) {
                dfs(item.children, 'children', i => {
                  if (this.defaultTargetKeys.includes(i.id)) {
                    selectedData.push(i)
                  }
                })
              }
            }
            const isSomeChildrenChecked = hasChildren ? getValue(item, 'children', []).some((i) => this.defaultTargetKeys.includes(i.id)) : hasChildren
            if (isSomeChildrenChecked && !isAllChildrenChecked) {
              dfs(item.children, 'children', i => {
                if (this.defaultTargetKeys.includes(i.id)) {
                  selectedData.push(i)
                  deleteItem(findValue(data, item.id, 'id', 'children'), i.id, 'id')
                }
              })
            }
          } else {
            if (isChecked) {
              deleteItem(data, item.id, 'id')
              selectedData.push(item)
            }
          }

        })
        this.unselectedAll = flatTree.every(item => this.defaultTargetKeys.includes(item.id))
        return data;
      }
      const unselectData = handleIsSelected(treeData)

      this.unselectedData = unselectData;
      this.selectedData = deleteRepeat(selectedData, 'id');
      this.copyUnselectedData = deepClone(unselectData)
      this.copySelectedData = deepClone(selectedData)
    },
    // 搜索过滤
    searchFilter(e, origin) {
      const { value = '' } = e.target
      const filterDataHandle = (data, copData) => {
        const filterData = this[copData].map(item => {
          if (item.title.includes(value)) {
            return item
          } else {
            if (!isEmpty(item.children)) {
              if (item.children.some(i => i.title.includes(value))) {
                const newItem = deepClone(item)
                newItem.children = newItem.children.filter(i => i.title.includes(value))
                return newItem
              }
            }
          }
        }).filter(Boolean)
        this[data] = (isEmpty(filterData) && !value) ? this[copData] : filterData
      }
      if (origin === 'unselected') {
        filterDataHandle('unselectedData', 'copyUnselectedData')
      } else {
        filterDataHandle('selectedData', 'copySelectedData')
      }

    },
    // 重置
    reset(origin) {
      this.$emit('on-reset', this.copyDefaultTargetKeys)
    },
    clickSelectedAll(flag) {
      dfs(this.selectedData, 'children', item => {
        this.unselected.push(item)
        item.checked = flag
      })
    },
    clickUnSelectedAll(flag) {
      dfs(this.unselectedData, 'children', item => {
        this.selected.push(item)
        item.checked = flag
      })
    },
    toLeft() {
      let arr = []
      dfs(this.unselected, 'children', item => {
        arr.push(item)
      })
      arr = [...new Set(arr)]
      this.$emit('to-left', arr)
      this.unselected = []
      this.selectedAll = false
    },
    toRight() {
      this.$emit('to-right', this.selected)
      this.selected = []
      this.unselectedAll = false
    },
    selectedDataChange(allData, nowData) {
      this.unselectedAll = this.isAllSelected()
      this.selected = allData;
    },
    unselectedDataChange(allData, nowData) {
      this.unselected = allData;
    },
    refresh() {
      this.targetKeys = this.defaultTargetKeys;
      this.$emit('on-reset', this.targetKeys);
    },
    handleChange(keys, direction, moveKeys) {
      this.targetKeys = keys;
      this.$emit('on-change', keys, direction, moveKeys);
    }
  }
}
</script>

<style lang="less" scoped>
.ivu-transfer-list {
  width: v-bind(width);
  height: v-bind(height);
}

.search-input {
  z-index: 100;
}
</style>
