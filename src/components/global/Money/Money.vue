<template>
    <Numeral v-if="value || value === 0" :value="value" format="0,0[.]00" >
        <template #suffix>元</template>
    </Numeral>
    <span v-else>--元</span>
</template>

<script>
export default {
    name: 'Money',
    props: {
        value: { default: '', type: [Number, String] }
    },
    data() {
        return {}
    },
    methods: {}
}
</script>

<style lang="less" scoped>

</style>
