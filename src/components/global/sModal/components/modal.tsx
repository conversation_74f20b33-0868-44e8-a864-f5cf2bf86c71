import { defineComponent, Slots, ref, watch, provide } from "vue";
import { <PERSON><PERSON>, But<PERSON> } from "view-ui-plus";
export default defineComponent({
    name: "sModal",
    props: {
        title: {
            type: String,
            default: "",
        },
        width: {
            type: Number,
            default: 800,
        },
        footerHide: {
            type: Boolean,
            default: false,
        },
        refBox: {
            type: Object,
            default: () => ({}),
        },
        styles: {
            type: Object,
        },
        defaultVal: {
            type: Boolean,
            default: true,
        },
        transfer: {
            type: Boolean,
            default: false,
        }
    },
    emits: ["closeModal", "handleSubmit"],
    setup(props, ctx) {
        const modalStatus = ref<boolean>(props.defaultVal);
        const loading = ref<boolean | undefined>(false);
        const modalRef = ref();
        const handleCancel = () => {
            modalStatus.value = false;
        };
        const handleOpen = () => {
            modalStatus.value = true;
        };
        const handleConfirm = async () => {
            loading.value = true;
            const res = await props.refBox.handleConfirm();
            loading.value = false;
            if (res) {
                handleCancel();
                ctx.emit("handleSubmit");
            }
        };
        watch(
            () => modalStatus.value,
            () => {
                if (!modalStatus.value) {
                    ctx.emit("closeModal");
                }
            }
        );
        ctx.expose({ modalStatus, handleOpen, handleCancel });
        provide("handleSubmit", handleConfirm);
        return () => (
            <Modal
                v-model={modalStatus.value}
                width={props.width}
                title={props.title}
                styles={props.styles}
                loading={true}
                footer-hide={props.footerHide}
                ref={modalRef}
                mask-closable={false}
                transfer={props.transfer}
                v-slots={{
                    footer: () => (
                        <>
                            <Button onClick={handleCancel}>取消</Button>
                            <Button type="primary" onClick={handleConfirm} loading={loading.value}>
                                确认
                            </Button>
                        </>
                    ),
                }}
            >
                <>{ctx.slots.default && ctx.slots.default()}</>
            </Modal>
        );
    },
});
