import { defineComponent, defineAsyncComponent, ref, h, resolveComponent, watch } from "vue";
// import modalComponent from "./components/modal";
import { emit, nextTick } from "process";
export default defineComponent({
    name: "sModal",
    components: {
        modalComponent: defineAsyncComponent(() => import("./components/modal")),
    },
    props: {
        title: {
            type: String,
            default: "",
        },
        width: {
            type: Number,
            default: 800,
        },
        componentName: {
            type: String,
            default: "",
        },
        styles: {
            type: Object
        },
        footerHide: {
            type: Boolean,
            default: false,
        },
        transfer: {
            type: Boolean,
            default: false,
        }
    },
    emits: ["handleSubmit", "emitClose"],
    setup(props, ctx) {
        const componentName = ref("");
        const modalComponent = ref();
        // 开启弹窗
        const handleOpen = () => {
            componentName.value = "modalComponent";
        };
        // 关闭弹窗
        const handleClose = () => {
            nextTick(() => {
                componentName.value = "";
                ctx.emit("emitClose");
            });
        };
        // 提交
        const handleSubmit = () => {
            ctx.emit("handleSubmit");
        };
        watch(
            () => props.componentName,
            () => {
                if (props.componentName) handleOpen();
            },
            { deep: true, immediate: true }
        );
        ctx.expose({ handleOpen, handleClose });
        return () =>
            h(
                resolveComponent(componentName.value),
                {
                    ...props,
                    onCloseModal: handleClose,
                    onHandleSubmit: handleSubmit,
                    ref: modalComponent,
                },
                () => ctx.slots.default && ctx.slots.default()
            );
    },
});
