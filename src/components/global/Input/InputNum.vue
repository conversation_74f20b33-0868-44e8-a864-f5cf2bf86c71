<template>
    <div class="input-num">
        <InputNumber
            v-model="curValue"
            @on-change="changeValue"
            @on-blur="handleBlur"
            :disabled="disabled"
            clearable :min="min"
            :max="max"
            :="{...option}"
            :placeholder="placeholder" />
        <div class="append" v-if="append">{{ append }}</div>
    </div>
</template>

<script>
export default {
    name: 'InputNum',
    props: {
        modelValue: { type: [Number] },
        append: { default: '元' },
        min: { type: [Number] },
        max: { type: Number },
        disabled: { default: false },
        placeholder: { default: '请输入' },
        option:{default:()=>{return{}}}
    },
    emits: ['on-blur', 'on-change', 'update:modelValue'],
    data() {
        return {
            curValue: ''
        }
    },
    watch: {
        modelValue(val) {
            if (val !== this.curValue) {
                this.curValue = val
            }
        }
    },
    mounted() {
        this.curValue = this.modelValue
    },
    methods: {
        changeValue(val) {
            this.$emit('update:modelValue', this.curValue)
            this.$emit('on-change', val);
        },
        handleBlur() {
            this.$emit('on-blur',this.curValue);
        }
    }
}
</script>

<style lang="less" scoped>
.input-num{
    display: flex;
    align-items: center;
    position: relative;
    .ivu-input-number{
        flex: 1;
        border-top-right-radius: 0px;
        border-bottom-right-radius: 0px;
    }
    .append{
        padding: 0px 7px;
        height: 32px;
        line-height: 30px;
        font-size: inherit;
        font-weight: normal;
        color: #1E2A55;
        text-align: center;
        background-color: #f8f8f9;
        border: 1px solid #E5E6EB;
        border-top-right-radius: 2px;
        border-bottom-right-radius: 2px;
        border-left: none;
    }
}
</style>
