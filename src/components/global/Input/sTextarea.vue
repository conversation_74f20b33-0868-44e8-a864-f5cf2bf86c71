<template>
    <Input v-model="curValue" type="textarea" :show-word-limit="showWordLimit" :maxlength="maxlength"
           :clearable ="clearable" :disabled="disabled"
           :autosize="curAutosize" :placeholder="placeholder"
           @on-change="changeValue"
           @on-blur="handleBlur"
    ></Input>
</template>

<script>
const defaultAutosize = { maxRows: 5, minRows: 3 }
export default {
    name: 'sTextarea',
    props: {
        modelValue: { type: [String] },
        maxlength: { type: [Number, String], default: 200 },
        showWordLimit: { default: true },
        disabled: { default: false },
        placeholder: { default: '请输入' },
        autosize: { default() { return { } } },
        clearable: { default: true }
    },
    emits: ['on-blur', 'on-change', 'update:modelValue'],
    data() {
        const curAutosize = { ...defaultAutosize, ...this.autosize }
        return {
            curValue: this.modelValue,
            isChangeValueIn: false,
            curAutosize: curAutosize
        }
    },
    watch: {
        modelValue(val) {
            if (val !== this.curValue) {
                this.curValue = val
            }
        }
    },
    methods: {
        changeValue(val) {
            this.$emit('update:modelValue', this.curValue)
            this.$emit('on-change', val);
        },
        handleBlur(e) {
            this.$emit('on-blur', e);
        }
    }
}
</script>

<style lang="less" scoped>

</style>
