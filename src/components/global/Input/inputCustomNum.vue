<template>
    <div>
        <Input v-model="curValue" @on-keydown="keyDown" :placeholder="placeholder">
            <template #append>
                {{ append }}
            </template>
            <template #suffix>
                <div class="ivu-input-number-handler-wrap">
                    <a class="ivu-input-number-handler ivu-input-number-handler-up">
                        <span class="ivu-input-number-handler-up-inner ivu-icon ivu-icon-ios-arrow-up"></span>
                    </a>
                    <a class="ivu-input-number-handler ivu-input-number-handler-down">
                        <span class="ivu-input-number-handler-down-inner ivu-icon ivu-icon-ios-arrow-down"></span>
                    </a>
                </div>
            </template>
        </Input>
    </div>
</template>

<script>
export default {
    name: 'inputCustomNum',
    props: {
        modelValue: { type: [Number] },
        append: { default: '吨' },
        min: { default: 0, type: [Number] },
        max: { type: Number },
        disabled: { default: false },
        placeholder: { default: '请输入' },
        format: { default() { return ['/'] } }
    },
    data() {
        return {
            curValue: ''
        }
    },
    methods: {
        keyDown(e) {
            console.log(e)
            return false
        }
    }
}
</script>

<style lang="less" scoped>
/deep/.ivu-input-group-append{
    position: relative;
}
/deep/.ivu-input-suffix{
    z-index: 9;
}
.ivu-input-wrapper:hover{
    .ivu-input-number-handler-wrap{
        opacity: 1;
    }
}
.ivu-input-number-handler-wrap{
    right: 100%;
    z-index: 9;
    background: transparent;
}
</style>
