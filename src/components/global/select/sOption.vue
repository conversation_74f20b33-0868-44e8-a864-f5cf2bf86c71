<template>
    <li
        :class="classes"
        @click.stop="select"
        v-show="isShow"
        @mousedown.prevent
    ><slot>{{ showLabel }}</slot></li>
</template>

<script>
import { getCurrentInstance } from 'vue';
const prefixCls = 'ivu-select-item';
export default {
    name: 'sOption',
    inject: {
        sSelectInstance: {
            default: null
        }
    },
    props: {
        value: {
            type: [String, Number],
            required: true
        },
        label: {
            type: [String, Number]
        },
        disabled: {
            type: Boolean,
            default: false
        }
    },
    emits: ['on-select-selected'],
    data() {
        return {
            instance: null
        }
    },
    computed: {
        itemDisabled () {
            let state = this.disabled;
            return state ? true : null;
        },
        classes() {
            return [
                `${prefixCls}`,
                {
                    [`${prefixCls}-disabled`]: this.itemDisabled,
                    [`${prefixCls}-selected`]: this.selected && !this.autoComplete,
                    [`${prefixCls}-focus`]: this.isFocused
                }
            ];
        },
        showLabel() {
            return (this.label) ? this.label : this.value;
        },
        optionLabel(){
            return this.label || (this.$el && this.$el.textContent);
        },
        isShow(){
            const SelectInstance = this.sSelectInstance;
            const query = SelectInstance.query.toLowerCase().trim();
            let label = this.label || (this.$el && this.$el.textContent);
            let filterOption = (label || '').toLowerCase();
            const showFilterOption = filterOption.includes(query);
            return showFilterOption
        },
        selected(){
            const SelectInstance = this.sSelectInstance;
            const values = SelectInstance.values || [];
            return values.find(item => item.value === this.value)
        }
    },
    created() {
        this.instance = getCurrentInstance();
    },
    mounted () {
        this.addOption();
    },
    beforeUnmount () {
        this.$nextTick(() => {
            this.removeOption();
            this.instance = null;
        })
    },
    methods: {
        select () {
            if (this.itemDisabled) return false;
            this.sSelectInstance.handleOnSelectSelected({
                value: this.value,
                label: this.optionLabel,
                tag: this.tag
            });
        },
        addOption () {
            const select = this.sSelectInstance;
            const {id, value, instance} = this;
            if (select){
                select.slotOptions.push({
                    ...instance,
                    id,
                    tag: 'option'
                });
                select.slotOptionsMap.set(value, instance)
                // fix Option hide, the modalValue cannot selected
                const { modelValue } = select;
                modelValue && modelValue == value && select.lazyUpdateValue();
            }
        },
        removeOption () {
            const select = this.sSelectInstance;
            const {id, value} = this;
            if( select ){
                const index = select.slotOptions.findIndex(item => item.id === id);
                index !== -1 && select.slotOptions.splice(index, 1);
                select.slotOptionsMap.has(value) && select.slotOptionsMap.delete(value);
            }
        }
    }
}
</script>

<style lang="less" scoped>

</style>
