<template>
    <Dropdown
        ref="dropdown"
        class="sel-custom" trigger="custom" :placement="placement"
        :class="{'ivu-select-visible': dropVisible}"
        :visible="dropVisible"
        @on-clickoutside="handlerClickOutside"
    >
        <div
            class="ivu-select-selection"
            @mouseenter="hasMouseHoverHead = true"
            @mouseleave="hasMouseHoverHead = false"
        >
            <input
                ref="input"
                type="text" class="ivu-select-input"
                v-model="query"
                :placeholder="curPlaceholder"
                @focus="onInputFocus"
                @blur="onInputBlur"
            />
            <Icon type="ios-arrow-down" class="ivu-select-arrow" v-if="!showClear"></Icon>
            <Icon type="ios-close-circle" @click.stop="onClear" v-if="showClear" class="ivu-select-arrow" />
        </div>
        <template #list>
            <ul v-show="showNotFoundLabel" class="ivu-select-not-found"><li>无匹配数据</li></ul>
            <ul>
                <slot></slot>
            </ul>
        </template>
    </Dropdown>
</template>

<script>
import mixinsForm from 'view-ui-plus/src/mixins/form';
export default {
    name: 'sSelect',
    mixins: [mixinsForm],
    props: {
        modelValue: {
            type: [String, Number],
            default: ''
        },
        placeholder: { default: '请选择' },
        disabled: {
            type: Boolean,
            default: false
        },
        clearable: { default: true },
        placement: { default: 'bottom-start' }
    },
    emits: ['on-clear', 'on-change', 'update:modelValue'],
    provide () {
        return {
            sSelectInstance: this
        }
    },
    data() {
        return {
            values: [],
            labelVal: '',
            query: '',
            queryCopy: '',
            slotOptions: [],
            slotOptionsMap: new Map(),
            dropVisible: false,
            hasMouseHoverHead: false,
            hasChangeIn: false,
            isLocking: false,
            focusIndex: -1,
        }
    },
    computed: {
        curPlaceholder() {
            return (this.dropVisible && this.queryCopy) ? this.queryCopy : this.placeholder
        },
        showNotFoundLabel () {
            const { slotOptions } = this;
            const options = slotOptions || [];
            const filterOptions = options.find(item => item.proxy.isShow);
            return (options.length === 0 || !filterOptions);
        },
        showClear() {
            return this.clearable && this.hasMouseHoverHead && this.labelVal
        },
        publicValue(){
            return (this.values[0] || {}).value;
        },
    },
    watch: {
        modelValue(value) {
            this.checkUpdateStatus()
            if (this.hasChangeIn) {
                this.hasChangeIn = false
            } else {
                if (value) {
                    this.lazyUpdateValue()
                } else {
                    this.values = []
                    this.query = ''
                    this.labelVal = ''
                }
            }
        },
        values(now, before) {
            const newValue = JSON.stringify(now);
            const oldValue = JSON.stringify(before);
            let vModelValue = this.publicValue;
            const shouldEmitInput = newValue !== oldValue && vModelValue !== this.modelValue;
            if (shouldEmitInput) {
                let obj = this.values[0]
                let val = obj ? obj.value : ''
                this.$emit('update:modelValue', val)
                this.$emit('on-change', val)
                this.handleFormItemChange('change', val);
                this.hasChangeIn = true
            }
        }
    },
    mounted() {
        if (this.slotOptions.length > 0 && this.modelValue) {
            this.lazyUpdateValue()
        }
    },
    methods: {
        visibleChange(flag) {
            this.showFlag = flag
        },
        onClear() {
            this.queryCopy = ''
            this.query = ''
            this.labelVal = ''
            this.values = []
            this.$emit('on-clear')
        },
        handlerClickOutside() {
            this.hideDrop()
        },
        onInputFocus() {
            this.showDrop()
            this.queryCopy = this.query
            this.query = ''
        },
        onInputBlur() {
            this.query = this.labelVal
        },
        showDrop() {
            this.dropVisible = true
        },
        hideDrop() {
            this.dropVisible = false
            if (this.$refs.input) {
                this.$refs.input.blur()
            }
        },
        onOptionClick(option) {
            this.query = String(option.label).trim();
            this.labelVal = this.query
            this.values = [option];
            this.focusIndex = this.slotOptions.findIndex((opt) => {
                if (!opt) return false;
                return opt.props.value === option.value;
            });

        },
        handleOnSelectSelected (options) {
            this.hideDrop()
            this.onOptionClick(options);
        },
        lazyUpdateValue(checked) {
            const { getInitialValue, isLocking, modelValue, values } = this;
            const hasModelValue = !!(modelValue);
            if ((hasModelValue || values.length) && checked) return;
            if (isLocking) return;
            this.isLocking = true;
            this.$nextTick(() => {
                this.values = getInitialValue().map(this.getOptionData).filter(Boolean)
                if (this.values.length > 0) {
                    this.query = String(this.values[0].label).trim();
                    this.labelVal = this.query
                }
                this.isLocking = false;
            });
        },
        getOptionData(value){
            const optionItem = this.slotOptions.find(({props}) => props.value === value);
            if (!optionItem) return null;
            const { optionLabel, disabled } = optionItem.proxy || {};
            return {
                value,
                label: optionLabel,
                disabled
            };
        },
        checkUpdateStatus() {
            if (this.getInitialValue().length > 0 && this.slotOptions.length === 0 ) {
                this.hasExpectedValue = true;
            }
        },
        getInitialValue(){
            const {modelValue} = this;
            let initialValue = Array.isArray(modelValue) ? modelValue : [modelValue];
            if ((typeof initialValue[0] === 'undefined' || (String(initialValue[0]).trim() === '' && !Number.isFinite(initialValue[0])))){
                initialValue = [];
            }
            return initialValue.filter((item) => {
                return Boolean(item) || item === 0;
            });
        },
    }
}
</script>

<style lang="less" scoped>
.sel-custom{
    display: block;
    position: relative;
    .ivu-select-input{
        width: 100%;
    }
    /deep/.ivu-select-dropdown{
        width: auto;
        min-width: 100%;
        max-height: 200px;
        overflow: auto;
    }
    .ivu-dropdown-menu{
        min-width: 100%;
    }
}
</style>
