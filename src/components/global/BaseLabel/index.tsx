import { h, defineComponent, watch, ref, renderSlot, onMounted, onUpdated, computed } from "vue";
import "./label.less";
import { nextTick } from "process";
export default defineComponent({
    name: "s<PERSON><PERSON><PERSON>",
    props: {
        label: {
            type: String,
            required: true,
            default: "",
        },
        value: {
            type: [String, Number],
            required: false,
            default: "",
        },
        labelWidth: {
            type: [String, Number],
            required: false,
            default: "",
        },
        tooltip: {
            type: Boolean,
            required: false,
            default: true, // 是否显示气泡
        },
        valueStyle: {
            type: String,
            default:'margin-bottom:12px',
            required: false, // 值的样式
        },
        titleStyle:{
            default:''
        },
        bold: {
            default: false
        }
    },
    setup(props, ctx) {
        const valueDom = ref()
        const tooltipMaxWidth = ref<string>()
        onMounted(() => {
            tooltipMaxWidth.value = `${valueDom.value?.clientWidth || 300}`
        })
        watch(() => props.value, () => {
            tooltipMaxWidth.value = `${valueDom.value?.clientWidth || 300}`
        })
        return () => (
            <div class="s-label">
                <div class="title" style={[props.labelWidth ? props.labelWidth + "px" : "",props.titleStyle ? props.titleStyle :'']}>
                    {ctx.slots.label ? ctx.slots.label() : (props.label + ':')}
                </div>
                {props.tooltip ? (
                    <div class="value" ref={valueDom} style={props.valueStyle}>
                        <tooltip-auto-show bold={props.bold} maxWidth={tooltipMaxWidth.value}>
                            {ctx.slots.value ? ctx.slots.value() : props.value || "--"}
                        </tooltip-auto-show>
                    </div>
                ) : (
                    <div class="value" style={props.valueStyle}>{ctx.slots.value ? ctx.slots.value() : props.value || "--"}</div>
                )}
            </div>
        );
    },
});
