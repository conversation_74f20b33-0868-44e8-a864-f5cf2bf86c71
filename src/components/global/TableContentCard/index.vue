<script setup name="TableContentCard">
import { defineProps, ref } from 'vue'
const props = defineProps({
  baseBtn: {
    type: Boolean,
    default: true
  },
  addTitle: {
    default: '新建'
  },
  addAuth: {
    default: 'A'
  },
  deleteAuth: {
    default: 'A'
  },
  showDel: {
    default: true
  }
})

</script>
<template>
  <Card dis-hover :bordered="false">
    <template #title>
      <Button v-show="props.baseBtn" v-auth="props.addAuth" icon="md-add" type="primary" @click="$emit('on-add')">{{ addTitle }}</Button>
      <slot name="btn"></slot>
      <Button v-show="props.baseBtn&&props.showDel" v-auth="props.deleteAuth" icon="ios-trash" @click="$emit('on-delete')">删除</Button>
    </template>
    <slot></slot>
  </Card>
</template>

<style lang="less" scoped>
/deep/ .ivu-card-head {
  padding: 0 !important;
  border-bottom: none !important;
  display: flex;
  column-gap: 8px;
  button{
    margin: 0;
  }

  & > button, & > div {
      margin-bottom: 16px;
  }
    .ivu-dropdown{
        text-align: center;
    }
}

/deep/ .ivu-card-body {
  padding: 0 !important;
}
</style>
