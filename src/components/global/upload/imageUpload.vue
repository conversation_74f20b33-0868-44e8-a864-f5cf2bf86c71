<template>
    <div>
        <div class="upload-file">
            <!-- :max-size="maxSize * 1024"  -->
            <Upload
                v-if="!disabled"
                ref="upload"
                :multiple="multiple"
                :type="type"
                :action="u.action"
                :data="u.uploadData"
                :show-upload-list="false"
                :before-upload="beforeUpload"
                :on-success="uploadSuccess"
                :on-error="errorUpload"
                :disabled="u.loading"
            >
                <div class="upload-box">
                    <Icon type="md-add" size="20"></Icon>
                    <span>点击上传</span>
                </div>
            </Upload>
            <div
                class="demo-upload-list"
                @mouseleave="handleLeave(index)"
                @mouseenter="handleMouseEnter(index)"
                v-for="(item, index) in m.uploadFileList"
                :key="index"
            >
                <img :src="item.url" />
                <div v-show="activeIndex === index" class="demo-upload-list-cover">
                    <Icon
                        size="18"
                        color="#fff"
                        type="ios-eye-outline"
                        @click="previewImg(item)"
                    ></Icon>
                    <Icon
                        size="18"
                        color="#fff"
                        v-if="!disabled"
                        type="ios-trash-outline"
                        @click="handleRemove(item, index)"
                    ></Icon>
                </div>
                <div :class="{ mask: index === activeIndex }"></div>
            </div>
            <previewModal ref="preR" />
        </div>
    </div>
    <div class="ts-box" v-if="!disabled">
        <span v-if="promptMsg">{{promptMsg}}</span>
        <span v-else>
            仅支持上传{{ format.join(', ') }}文件格式,单个文件大小不超过 {{ maxSize }}MB，{{ multiple && maxLength > 1 ? '最多上传' + maxLength + '个文件' : '' }}
        </span>

        <div class="err-msg" v-if="m.errorMsg">{{ m.errorMsg }}</div>
    </div>
</template>

<script>
import { ossService } from "@/api/ossService";
export default {
    name: "ImageUpload",
    props: {
        modelValue: { type: [String, Object, Array] },
        promptMsg: { default: '' }, // 自定义提示文字
        disabled: { default: false }, // 是否禁止上传
        multiple: { default: true }, // 是否可以一次上传多个文件
        format: { default: () => ["pdf", "jpg", "jpeg", "png"] },
        maxSize: { default: 50 }, // 单位MB
        maxLength: { default: 10 }, // 最多上传个数
        showUploadList: { default: true }, // 显示上传列表
        urlSplit: { default: ',' } // 默认路径分割字符
    },
    data() {
        return {
            activeIndex: "",
            isChangeIn: false,
            u: {
                action: "",
                uploadData: {},
                accessUrl: "",
                loading: false,
            },
            m: {
                errorMsg: "",
                uploadFileList: [], // 当前的上传文件列表
                fileNameObj: {},
                publicValue: "",
            },
            s: {
                allNum: 0, // 上传的总数
                curNum: 0, // 已上传数量
            },
        };
    },
    watch: {
        modelValue(val) {
            if (this.isChangeIn) {
                this.isChangeIn = false
            } else {
                this.init();
            }
        },
        disabled() {
            this.m.uploadFileList.forEach((item) => {
                if (this.disabled && item.state == "success") {
                    item.state = "preview";
                } else if (!this.disabled && item.state == "preview") {
                    item.state = "success";
                }
            });
            this.showErrMsg("");
        },
    },
    created() {
        this.init();
        this.getOss();
    },
    methods: {
        init() {
            if (this.modelValue) {
                let arr = this.modelValue.split(this.urlSplit);
                this.m.uploadFileList = arr.map((item) => {
                    let a1 = item.split("/");
                    return {
                        url: item,
                        name: a1[a1.length - 1],
                        state: "preview",
                        format: this.getFormatByUrl(item),
                    };
                });
            } else {
                this.m.uploadFileList = [];
            }
        },
        // 鼠标移入图片
        handleMouseEnter(index) {
            this.activeIndex = index;
        },
        handleLeave(index) {
            this.activeIndex = "";
        },
        // 预览
        previewImg(item) {
            this.$refs.preR.show(item);
        },
        // 下载
        download(item) {
            this.$Util.download(item.url, item.name);
        },
        // 删除
        handleRemove(_item, index) {
            this.m.uploadFileList.splice(index, 1);
            this.changeValue();
            this.showErrMsg("");
        },
        // 批量上传时为循环执行
        beforeUpload(file) {
            const { name } = file;
            const suffix = name.split(".")[name.split(".").length - 1];
            if (!this.format.includes(suffix)) {
                this.showErrMsg(`文件格式不正确，请上传${this.format.join("、")}`);
                return false;
            }
            let size = file.size / (1024 * 1024);
            if (size > this.maxSize) {
                this.showErrMsg(`请上传${this.maxSize}MB以下的文件`);
                return false;
            }
            if (this.m.uploadFileList.length == this.maxLength) {
                if (this.maxLength === 1) {
                    this.m.uploadFileList = [];
                } else {
                    this.showErrMsg("最多上传" + this.maxLength + "个文件");
                    return false;
                }
            }
            if (!this.multiple) {
                this.u.loading = true;
            }
            this.s.allNum += 1;
            this.$emit("update:loading", true);
            let fileName = name;
            let _name_arr = fileName.split(",");
            if (_name_arr.length > 1) {
                fileName = _name_arr.join("-");
            }
            this.u.uploadData.key = this.u.uploadData.path + fileName;
            // this.m.fileNameObj['a' + file.name] = fileName
            file.state = "loading";
            this.m.uploadFileList.push(file);
            this.showErrMsg("");
        },
        async uploadSuccess(response, file, fileList) {
            // console.log('response', response)
            if (response.success === true) {
                // let o = this.m.fileNameObj['a' + file.name]
                // 当前为从响应获取url
                file.url = response.data.accessUrl;
                // file.name = o
                let index = this.m.uploadFileList.findIndex((item) => item.uid === file.uid);
                file.format = this.getFormatByUrl(file.url);
                file.state = "success";
                this.m.uploadFileList.splice(index, 1, file);

                this.$emit("on-success", file, fileList, this.u.uploadData, response);
                this.changeValue();
            } else {
                this.showErrMsg(`上传失败，请稍后重试`);
            }
            this.completeUpload();
        },
        changeValue() {
            let arr = this.m.uploadFileList.map((item) => item.url);
            this.m.publicValue = arr.join(this.urlSplit) || "";
            this.$emit("update:modelValue", this.m.publicValue);
            this.isChangeIn = true
            if (!this.multiple) {
                this.u.loading = false;
            }
        },
        errorUpload(_error, file, _fileList) {
            let index = this.m.uploadFileList.findIndex((item) => item.uid === file.uid);
            this.m.uploadFileList.splice(index, 1);
            this.showErrMsg(`上传失败，请稍后重试`);
            this.completeUpload();
            if (!this.multiple) {
                this.u.loading = false;
            }
        },
        // 上传计数
        completeUpload() {
            this.s.curNum += 1;
            if (this.s.allNum === this.s.curNum) {
                this.s.allNum = 0;
                this.s.curNum = 0;
                this.$emit("update:loading", false);
            }
        },
        getOss() {
            return new Promise((resolve, reject) => {
                ossService.policy({}).then(
                    (data) => {
                        // console.log('查询密钥结束', data)
                        this.u.action = data.uploadHost;
                        this.u.accessUrl = data.accessHost;
                        this.u.uploadData.policy = data.policy;
                        this.u.uploadData.OSSAccessKeyId = data.accessId;
                        this.u.uploadData.Signature = data.signature;
                        this.u.uploadData.path = data.path ? data.path : "";
                        resolve();
                    },
                    () => {
                        console.log("查询密钥失败");
                        this.loading = false;
                        // reject();
                        return false;
                    }
                );
            });
        },
        showErrMsg(msg) {
            this.m.errorMsg = msg;
            // if (this.sitime) {
            //     clearTimeout(this.sitime)
            // }
            // this.sitime = setTimeout(() => {
            //     this.m.errorMsg = ''
            // }, 2000)
        },
        clearFiles() {
            this.$refs.upload.clearFiles();
        },
        // 获取视频时长
        getVideoTime(data) {
            return new Promise((resolve, reject) => {
                let url = URL.createObjectURL(data);
                let audioElement = new Audio(url);
                let duration;
                audioElement.addEventListener("loadedmetadata", (_event) => {
                    duration = audioElement.duration; // 时长为秒
                    resolve(duration);
                });
            });
        },
        // 获取上传的文件类型
        getFormatByUrl(url) {
            const format = url.split(".").pop().toLocaleLowerCase() || "";
            let icon = "icon-file";
            let type = "file";
            if (["gif", "jpg", "jpeg", "png", "bmp", "webp"].indexOf(format) > -1) {
                type = "img";
                icon = "icon-file-image";
            }
            if (["mp4", "m3u8", "rmvb", "avi", "swf", "3gp", "mkv", "flv"].indexOf(format) > -1) {
                type = "video";
                icon = "icon-file-video";
            }
            if (["ppt", "pptx"].indexOf(format) > -1) {
                type = "ppt";
            }
            if (["pdf"].indexOf(format) > -1) {
                type = "pdf";
                icon = "icon-file-pdf";
            }
            return { icon, type };
        },
    },
};
</script>

<style lang="less" scoped>
.mask {
    position: absolute;
    width: 100%;
    top: 0;
    height: 100%;
    background: #33333380;
}
.hint-msg {
    font-size: 12px;
    color: #798799;
}
.upload-box {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background-color: #f3f7fb;
    border: 1px dashed #e5e6eb;
    font-style: normal;
    font-weight: 500;
    font-size: 14px;
    color: #4e5969;
    width: 80px;
    height: 80px;
    cursor: pointer;
    .ivu-icon {
        color: #4e5969 !important;
    }
    &:hover{
        background-color: #E5E6EB;
        border: 1px dashed #C9CDD4;
    }
}
.ts-box{
    font-family: 'PingFang SC';
    font-style: normal;
    font-weight: 400;
    font-size: 12px;
    line-height: 20px;
    color: @text-3-1;
    padding-top: 10px;
}
.demo-spin-icon-load {
    animation: ani-demo-spin 1s linear infinite;
}
@keyframes ani-demo-spin {
    from {
        transform: rotate(0deg);
    }
    50% {
        transform: rotate(180deg);
    }
    to {
        transform: rotate(360deg);
    }
}
.err-msg{
    color: #F53F3F;
    margin-bottom: 12px;
}
.upload-file{
    display: flex;
    align-items: center;

    .list{
        .box{
            background: #F8FAFB;
            border: 1px solid #F8FAFB;
            border-radius: 2px;
            height: 36px;
            display: flex;
            align-items: center;
            position: relative;
            margin-bottom: 12px;
            padding: 0 12px;
            .ia {
                margin-right: 12px;
            }
            .name {
                flex: 1;
                line-height: 22px;
                margin-right: 12px;
                overflow: hidden;
            }
            .action {
                display: flex;
                align-items: center;
                .del {
                    position: absolute;
                    top: 50%;
                    transform: translateY(-50%);
                    right: -28px;
                    font-size: 16px;
                    cursor: pointer;
                }
                .down {
                    cursor: pointer;
                }
                .loading {
                    animation: rotate 1s infinite linear;
                }
            }
            &:hover {
                border: 1px solid #165dff;
                background: #f3f7fb;
            }
            &.error {
                .name,
                .action {
                    color: #f53f3f;
                }
                &:hover {
                    background: #f8fafb;
                    border: 1px solid #f8fafb;
                }
            }
        }
        &.dis {
            display: flex;
            flex-wrap: wrap;
            .box {
                width: 40%;
                margin-right: 10%;
            }
        }
    }
}
@keyframes rotate {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}
.demo-upload-list {
    width: 80px;
    height: 80px;
    margin-left: 16px;
    text-align: center;
    line-height: 80px;
    border: 1px solid transparent;
    border-radius: 4px;
    overflow: hidden;
    background: #fff;
    position: relative;
    box-shadow: 0 1px 1px rgba(0, 0, 0, 0.2);
    margin-right: 4px;
    display: inline-flex;
}
.demo-upload-list img {
    max-height: 100%;
    max-width: 100%;
    margin: auto;
    width: auto;
    height: auto;
}
.demo-upload-list-cover {
    z-index: 10;
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
}
</style>
