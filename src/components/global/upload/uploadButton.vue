<template>
    <div class="upload-file">
        <!-- :max-size="maxSize * 1024"  -->
        <Upload v-show="false" ref="upload" v-if="!disabled" :multiple="multiple" :type="type" :accept="format.join()"
            :format="format" :action="u.action" :data="u.uploadData" :show-upload-list="false" :before-upload="beforeUpload"
            :on-success="uploadSuccess" :on-error="errorUpload" :disabled="u.loading">
        </Upload>
        <slot>
            <div class="upload">
                <Button type="primary" @click="openUploadFile">
                    <Icon class="iconfont icon-upload" />
                    点击上传
                </Button>
                <div>仅支持上传{{format.join(',')}}文件格式，最多上传{{ maxLength }}个文件，每个文件大小为{{ maxSize }}M</div>
            </div>
        </slot>
        <div class="err-msg" v-if="m.errorMsg">{{ m.errorMsg }}</div>
        <div class="list" v-if="showUploadList && m.uploadFileList.length > 0" :class="[disabled ? 'dis' : '']">
            <div class="box" v-for="(item, index) in m.uploadFileList" :key="index" :class="[item.state]">
                <Icon v-if="item.format && item.format.icon" :custom="'iconfont ' + item.format.icon" class="ia" />
                <Icon v-else custom="iconfont icon-file" class="ia" />

                <div class="name">
                    <tooltip-auto-show>{{ item.name }}</tooltip-auto-show>
                </div>
                <div class="action" v-if="item.state == 'loading'">
                    <Icon type="ios-loading" class="loading" />
                </div>
                <div class="action" v-else-if="item.state == 'success'">
                    <Icon custom="iconfont icon-download" @click="download(item)" size="16" class="down" title="下载" />
                    <Icon custom="iconfont icon-delete" @click="handleRemove(item, index)" class="del" title="删除" />
                </div>
                <div class="action" v-else-if="item.state == 'preview'">
                    <Icon custom="iconfont icon-eye" @click="previewImg(item)" size="16" class="del" title="预览" />
                    <Icon custom="iconfont icon-download" @click="download(item)" size="16" class="down" title="下载" />
                </div>
            </div>
        </div>
        <!-- <div class="list" v-if="viewList.length > 0" :class="[disabled ? 'dis' : '']">
            <div class="box" v-for="(item, index) in fileViewList" :key="index" :class="[item.state]">
                <Icon custom="iconfont icon-file" class="ia" />
                <div class="name">
                    <tooltip-auto-show>{{ item.name }}</tooltip-auto-show>
                </div>
                <div class="action">
                    <Icon custom="iconfont icon-download" @click="download(item)" size="16" class="down" title="下载" />
                    <Icon custom="iconfont icon-delete" @click="handleRemove(item, index)" class="del" title="删除" />
                </div>
            </div>
        </div> -->
        <no-data v-if="m.uploadFileList.length === 0 && disabled" />
        <previewModal ref="preR" />
    </div>
</template>

<script>
import { ossService } from '@/api/ossService';
export default {
    name: 'uploadCustom',
    props: {
        modelValue: { type: [String, Object, Array] },
        disabled: { default: false }, // 是否禁止上传
        multiple: { default: true }, // 是否可以一次上传多个文件
        type: { default: 'drag' },
        format: { default: () => ['pdf', 'jpg', 'jpeg', 'png'] },
        maxSize: { default: 50 }, // 单位MB
        maxLength: { default: 10 }, // 最多上传个数
        showUploadList: { default: true }, // 显示上传列表
        height: { default: 184 },
        tipMsg:{default:''},
        placeholder: {
            default: ''
        },
        viewList: {
            default: () => [] //['url','url']
        }
    },
    data() {
        return {
            fileViewList:[],
            isChangeIn: false,
            u: {
                action: '',
                uploadData: {},
                accessUrl: '',
                loading: false
            },
            m: {
                errorMsg: '',
                uploadFileList: [], // 当前的上传文件列表
                fileNameObj: {},
                publicValue: ''
            },
            s: {
                allNum: 0, // 上传的总数
                curNum: 0 // 已上传数量
            }
        }
    },
    watch: {
        modelValue(val) {
            if (this.isChangeIn) {
                this.isChangeIn = false
            } else {
                this.init();
            }
        },
        viewList: {
            deep: true,
            immediate: true,
            handler(newVal) {
                this.fileViewList = newVal.map(url => {
                    return {
                        url: url,
                        name: url.substring(url.lastIndexOf('/') + 1),
                        state: 'success'
                    }
                })
            }
        },
        disabled() {
            this.m.uploadFileList.forEach(item => {
                if (this.disabled && item.state == 'success') {
                    item.state = 'preview'
                } else if (!this.disabled && item.state == 'preview') {
                    item.state = 'success'
                }
            })
            this.showErrMsg('')
        }
    },
    created() {
        this.init()
        this.getOss()
    },
    methods: {
        openUploadFile() {
            this.$refs.upload.$el.children[0].children[0].click()
        },
        init() {
            if (this.modelValue) {
                let arr = this.modelValue.split(',')
                this.m.uploadFileList = arr.map(item => {
                    let a1 = item.split('/')
                    return {
                        url: item,
                        name: a1[a1.length - 1],
                        state: this.disabled ? 'preview' : 'success',
                        format: this.getFormatByUrl(item)
                    }
                })
            } else {
                this.m.uploadFileList = []
            }
        },
        // 预览
        previewImg(item) {
            this.$refs.preR.show(item)
        },
        // 下载
        download(item) {
            this.$Util.download(item.url, item.name)
        },
        // 删除
        handleRemove(_item, index) {
            this.m.uploadFileList.splice(index, 1)
            this.$emit('on-delete', _item)
            this.changeValue()
            this.showErrMsg('')
        },
        // 批量上传时为循环执行
        beforeUpload(file) {
            const { name } = file;
            const suffix = name.split('.')[name.split('.').length - 1];
            if (!this.format.includes(suffix)) {
                this.showErrMsg(`文件格式不正确，请上传${this.format.join('、')}`);
                return false;
            }
            let size = file.size / (1024 * 1024)
            if (size > this.maxSize) {
                this.showErrMsg(`请上传${this.maxSize}MB以下的文件`)
                return false;
            }
            if (this.m.uploadFileList.length === this.maxLength) {
                if (this.maxLength === 1) {
                    this.m.uploadFileList = [];
                } else {
                    this.showErrMsg('最多上传' + this.maxLength + '个文件')
                    return false
                }
            }
            if (!this.multiple) {
                this.u.loading = true
            }
            this.s.allNum += 1
            this.$emit('update:loading', true)
            let fileName = name
            let _name_arr = fileName.split(',')
            if (_name_arr.length > 1) {
                fileName = _name_arr.join('-')
            }
            this.u.uploadData.key = this.u.uploadData.path + fileName
            // this.m.fileNameObj['a' + file.name] = fileName
            file.state = 'loading'
            this.m.uploadFileList.push(file)
            this.showErrMsg('')
        },
        async uploadSuccess(response, file, fileList) {
            // console.log('response', response)
            if (response.success === true) {
                // let o = this.m.fileNameObj['a' + file.name]
                // 当前为从响应获取url
                file.url = response.data.accessUrl
                // file.name = o
                let index = this.m.uploadFileList.findIndex(item => item.uid === file.uid)
                file.format = this.getFormatByUrl(file.url)
                if (file.format.type === 'video') {
                    let duration = await this.getVideoTime(this.m.uploadFileList[index])
                    file.duration = duration
                }
                file.state = 'success'
                this.m.uploadFileList.splice(index, 1, file)

                this.$emit('on-success', file, fileList, this.u.uploadData, response)
                this.changeValue()
            } else {
                this.showErrMsg(`上传失败，请稍后重试`)
            }
            this.completeUpload()
        },
        changeValue() {
            let arr = this.m.uploadFileList.map(item => item.url)
            this.m.publicValue = arr.join(',') || ''
            this.isChangeIn = true
            this.$emit('update:modelValue', this.m.publicValue)
            if (!this.multiple) {
                this.u.loading = false
            }
        },
        errorUpload(_error, file, _fileList) {
            let index = this.m.uploadFileList.findIndex(item => item.uid === file.uid)
            this.m.uploadFileList.splice(index, 1)
            this.showErrMsg(`上传失败，请稍后重试`)
            this.completeUpload()
            if (!this.multiple) {
                this.u.loading = false
            }
        },
        // 上传计数
        completeUpload() {
            this.s.curNum += 1
            if (this.s.allNum === this.s.curNum) {
                this.s.allNum = 0
                this.s.curNum = 0
                this.$emit('update:loading', false)
            }
        },
        getOss() {
            return new Promise((resolve, reject) => {
                ossService.policy({}).then((data) => {
                    // console.log('查询密钥结束', data)
                    this.u.action = data.uploadHost;
                    this.u.accessUrl = data.accessHost;
                    this.u.uploadData.policy = data.policy;
                    this.u.uploadData.OSSAccessKeyId = data.accessId;
                    this.u.uploadData.Signature = data.signature;
                    this.u.uploadData.path = data.path ? data.path : '';
                    resolve();
                }, () => {
                    console.log('查询密钥失败')
                    this.loading = false;
                    // reject();
                    return false;
                });
            })
        },
        showErrMsg(msg) {
            this.m.errorMsg = msg
            // if (this.sitime) {
            //     clearTimeout(this.sitime)
            // }
            // this.sitime = setTimeout(() => {
            //     this.m.errorMsg = ''
            // }, 2000)
        },
        clearFiles() {
            this.$refs.upload.clearFiles()
        },
        // 获取视频时长
        getVideoTime(data) {
            return new Promise((resolve, reject) => {
                let url = URL.createObjectURL(data);
                let audioElement = new Audio(url);
                let duration;
                audioElement.addEventListener('loadedmetadata', (_event) => {
                    duration = audioElement.duration; // 时长为秒
                    resolve(duration);
                });
            })
        },
        // 获取上传的文件类型
        getFormatByUrl(url) {
            const format = url.split('.').pop().toLocaleLowerCase() || '';
            let icon = 'icon-file';
            let type = 'file'
            if (['gif', 'jpg', 'jpeg', 'png', 'bmp', 'webp'].indexOf(format) > -1) {
                type = 'img'
                icon = 'icon-file-image'
            }
            if (['mp4', 'm3u8', 'rmvb', 'avi', 'swf', '3gp', 'mkv', 'flv'].indexOf(format) > -1) {
                type = 'video'
                icon = 'icon-file-video'
            }
            if (['ppt', 'pptx'].indexOf(format) > -1) {
                type = 'ppt'
            }
            if (['pdf'].indexOf(format) > -1) {
                type = 'pdf'
                icon = 'icon-file-pdf'
            }
            return { icon, type };
        }
    }
}
</script>

<style lang="less" scoped>
.demo-spin-icon-load {
    animation: ani-demo-spin 1s linear infinite;
}

@keyframes ani-demo-spin {
    from {
        transform: rotate(0deg);
    }

    50% {
        transform: rotate(180deg);
    }

    to {
        transform: rotate(360deg);
    }
}

.default-box {
    height: 184px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;

    .ivu-icon {
        margin-bottom: 24px;
    }

    .t1 {
        line-height: 22px;
        margin-bottom: 4px;
    }

    .t2 {
        color: #86909C;
        font-size: 12px;
        line-height: 20px;
    }
}

.upload-file {
    .ivu-upload {
        margin-bottom: 24px;
    }

    .err-msg {
        color: #F53F3F;
        margin-bottom: 12px;
    }

    .list {
        .box {
            background: #F8FAFB;
            border: 1px solid #F8FAFB;
            border-radius: 2px;
            height: 36px;
            display: flex;
            align-items: center;
            position: relative;
            margin-bottom: 12px;
            padding: 0 12px;

            .ia {
                margin-right: 12px;
            }

            .name {
                flex: 1;
                line-height: 22px;
                margin-right: 12px;
                overflow: hidden;
            }

            .action {
                display: flex;
                align-items: center;
                justify-content: space-between;

                .del {
                    //position: absolute;
                    //top: 50%;
                    //transform: translateY(-50%);
                    //right: -28px;
                    margin-left: 8px;
                    font-size: 16px;
                    cursor: pointer;
                }

                .down {
                    cursor: pointer;
                }

                .loading {
                    animation: rotate 1s infinite linear;
                }
            }

            &:hover {
                border: 1px solid #165DFF;
                background: #F3F7FB;
            }

            &.error {

                .name,
                .action {
                    color: #F53F3F;
                }

                &:hover {
                    background: #F8FAFB;
                    border: 1px solid #F8FAFB;
                }
            }
        }

        &.dis {
            display: flex;
            flex-wrap: wrap;

            .box {
                width: 40%;
                margin-right: 10%;
            }
        }
    }
}

@keyframes rotate {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}
</style>
