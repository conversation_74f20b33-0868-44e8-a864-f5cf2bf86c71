<template>
    <SModal :is-show="isShowModal"
            @on-confirm="nextStep"
            @on-cancel="closeModal"
            :confirm-info="confirmInfo"
            :cancel-info="cancelInfo"
            :mask-closable="false"
            :footer-hide="stepFlag !== STEP_ENUMS.STEP_ONE"
            width="410"
    >
        <div class="wrapper">
            <div class="header">
                <Steps :current="stepFlag" size="small">
                    <Step title="选择文件"></Step>
                    <Step title="数据导入"></Step>
                    <Step title="结果查看"></Step>
                </Steps>
            </div>
            <div class="main">
                <!--    步骤1-->
                <template v-if="stepFlag === STEP_ENUMS.STEP_ONE">
                    <div class="step-1">
                        <Row justify="start">
                            <Col>
                                <Button icon="ios-cloud-download-outline"
                                        @click="downLoadTemplate"
                                        type="primary"
                                        style="margin-bottom: 24px"
                                >
                                    下载导入模板
                                </Button>
                            </Col>
                        </Row>
                        <Row>
                            <Col span="24">
                                <Upload
                                    :multiple="false"
                                    :show-upload-list="false"
                                    type="drag"
                                    ref="uploadDevice"
                                    action=""
                                    :before-upload="beforeUpload"
                                    :on-success="handleSuccess"
                                    :on-error="handleError"
                                    :format="format"
                                >
                                    <slot>
                                        <div class="default-box">
                                            <Icon type="md-add" size="18"></Icon>
                                            <div class="t1">
                                                点击或拖拽文件到此处上传
                                            </div>
                                            <div class="t2">
                                                仅支持上传{{
                                                    format.join(', ')
                                                }}文件格式
                                            </div>
                                        </div>
                                    </slot>
                                </Upload>
                            </Col>
                        </Row>
                        <Row>
                            <Col span="24">
                                <div class="list" v-if="file">
                                    <div class="box" :class="[fileState]">
                                        <Icon v-if="file.format && file.format.icon"
                                              :custom="'iconfont '+ file.format.icon" class="ia"
                                        />
                                        <Icon v-else custom="iconfont icon-file" class="ia"/>
                                        <div class="name">
                                            <tooltip-auto-show>{{ file.name }}</tooltip-auto-show>
                                        </div>
                                        <div class="action" v-if="fileState == 'loading'">
                                            <Icon type="ios-loading" class="loading"/>
                                        </div>
                                        <div class="action" v-else-if="fileState == 'success'">
                                            <Icon custom="iconfont icon-download" @click="download"
                                                  size="16" class="down" title="下载"
                                            />
                                            <Icon custom="iconfont icon-delete"
                                                  @click="handleRemove" class="del" title="删除"
                                            />
                                        </div>
                                    </div>
                                </div>
                            </Col>
                        </Row>
                    </div>
                </template>
                <!--    步骤2-->
                <template v-else-if="stepFlag === STEP_ENUMS.STEP_TWO">
                    <div class="step-2">
                        <Circle :percent="progress" size="80">
                            <Icon v-if="percent == 100" type="ios-checkmark" size="60"
                                  style="color:#5cb85c"
                            ></Icon>
                            <span v-else class="progress-num">{{ progress }}%</span>
                        </Circle>
                        <div class="tips">
                            数据导入中，请勿关闭窗口
                        </div>
                    </div>
                </template>
                <!--    步骤3-->
                <template v-else-if="stepFlag === STEP_ENUMS.STEP_THREE">
                    <div class="step-3">
                        <!--                        导入成功-->
                        <template v-if="importStatus === IMPORT_STATUS.FULL_IMPORT">
                            <Row>
                                <Result type="success" title="导入完成">
                                    <template #desc>
                                        表格导入成功
                                    </template>
                                </Result>
                            </Row>
                            <Row gutter="22">
                                <Col>
                                    <Button @click="closeModal">关闭弹窗</Button>
                                </Col>
                                <Col>
                                    <Button type="primary" @click="reImport">再次导入</Button>
                                </Col>
                            </Row>
                        </template>
                        <!--                        部分导入成功-->
                        <template v-else-if="importStatus === IMPORT_STATUS.PARTIAL_IMPORT">
                            <Row>
                                <Result type="warning" title="部分导入成功">
                                    <template #desc>
                                        请核对修改下列失败信息
                                    </template>
                                </Result>
                            </Row>
                            <Row gutter="22" justify="center">
                                <Col>
                                    <Button @click="closeModal">
                                        关闭弹窗
                                    </Button>
                                </Col>
                                <Col>
                                    <Button type="primary" @click="downloadFailListFile">
                                        下载失败列表
                                    </Button>
                                </Col>
                            </Row>
                            <div class="error-table" v-if="errorList.length > 0">
                                <base-table
                                    ref="errorListRef"
                                    :columns="columns"
                                    height="300"
                                    :data="errorList"
                                    :show-page="false"
                                >
                                </base-table>
                            </div>
                        </template>
                        <!--                        导入失败-->
                        <template v-else>
                            <Row>
                                <Result type="error" title="导入失败">
                                    <template #desc>
                                        {{ failedMessage }}
                                    </template>
                                </Result>
                            </Row>
                            <Row gutter="22">
                                <Col>
                                    <Button @click="closeModal">关闭弹窗</Button>
                                </Col>
                                <Col>
                                    <Button type="primary" @click="reImport">重新导入</Button>
                                </Col>
                            </Row>
                        </template>
                    </div>
                </template>
            </div>
        </div>
    </SModal>
</template>

<script setup>
import SModal from '@/components/common/modal/index.vue'
import { computed, ref, defineProps, getCurrentInstance, watch, defineExpose, defineEmits } from 'vue';
import request from '@/utils/request.js';
import BaseTable from '@/components/global/baseTable/index.vue';

const that = getCurrentInstance()?.appContext.config.globalProperties
// 步骤一、二、三
const STEP_ENUMS = {
    STEP_ONE: 0,
    STEP_TWO: 1,
    STEP_THREE: 2
}
// 导入状态
const IMPORT_STATUS = {
    FULL_IMPORT: 1, // 完全导入
    PARTIAL_IMPORT: 2, // 部分导入
    IMPORT_FAILED: 3 // 导入失败
}
const props = defineProps({
    // 下载模板url
    templateDowloadUrl: {
        default: '',
        type: String
    },
    // 上传文件接口(异步)
    uploadUrl: {
        type: String
    },
    // 查询解析进度接口（异步）
    fetchProgressUrl: {
        default: '/excelTask/getTaskInfo',
        type: String
    },
    getErrorListUrl: {
        type: String
    },
    isShow: {
        type: Boolean
    }
})
const emits = defineEmits(['update:isShow', 'onClose', 'on-update'])
// 是否显示弹窗
// const isShowModal = ref(false);
const isShowModal = computed({
    get() {
        return props.isShow
    },
    set(newVal) {
        emits('update:isShow', newVal)
    }
})

// 步骤标记
const stepFlag = ref(STEP_ENUMS.STEP_ONE)


const uploadMethod = async (data) => {
    return request({
        url: props.uploadUrl,
        method: 'post',
        data,
        headers: {
            'Content-Type': 'multipart/form-data'
        }
    })
}
const fetchProgressMethod = async (taskId) => {
    return request({
        url: props.fetchProgressUrl,
        method: 'get',
        params: {
            taskId
        }
    })
}
const getErrorListMethod = (data) => {
    return request({
        url: props.getErrorListUrl,
        method: 'get',
        params: data
    })
}

// step === 0 步骤一

// 导入文件类型
const format = ['xlsx', 'xls']
const file = ref();
const fileState = ref();
const taskId = ref(); // 上传任务id

// 下载导入模板
const downLoadTemplate = () => {
    window.open(props.templateDowloadUrl);
}

const beforeUpload = (newFile) => {
    const {name} = newFile;
    const suffix = name.split('.')[name.split('.').length - 1];
    if (!format.includes(suffix)) {
        that.$Message.warning({
            title: '提示',
            content: '导入的文件格式只能为xls,xlsx'
        })
        return false;
    }
    fileState.value = 'success'
    file.value = newFile;
    return false;
}
const handleSuccess = (resp, file, fileList) => {

}
const handleError = (error) => {
    file.value = null
    that.$Message.error({
        title: '系统异常',
        content: '批量导入失败，请联系管理员'
    })
}
const handleFormatError = (error) => {
    that.$Message.warning({
        title: '提示',
        content: '导入的文件格式只能为xls,xlsx'
    })
}
const onUploadFile = async() => {
    if (!file.value) {
        that.$Message.warning({
            title: '提示',
            content: '请上传后再点击确定'
        })
        return
    }
    fileState.value = 'loading'
    const res = uploadMethod && await uploadMethod({file: file.value})
    const { success } = res
    console.log('上传结果', res)
    if (success) {
        taskId.value = res.data;
        that.$Message.info('任务已提交');
    }
    return success
}
const download = () => {
    const blob = new Blob([file.value]);
    // 创建a标签，通过a标签实现下载
    const dom = document.createElement('a');
    dom.download = file.value.name;
    dom.href = URL.createObjectURL(blob);
    dom.id = 'upload-file-dom';
    dom.style.display = 'none';
    document.body.appendChild(dom);
    dom.click();
    // 释放资源
    URL.revokeObjectURL(dom.href);
    document.getElementById('upload-file-dom')?.remove();
}
const handleRemove = () => {
    file.value = null;
}

// step === 1 步骤二
// 进度
const progress = ref('0');
// 轮询句柄
const pollingTimer = ref()
// 轮询loading
const isFetchProgress = ref(false);
// 导入失败文件下载列表
const failedFileUrl = ref();
// 导入完成情况
const importStatus = ref(-1);
const failedMessage = ref('')
// 获取进度
const parsingCompleted = () => {
    clearInterval(pollingTimer.value);
    nextStep()
};
const fetchProgress = async() => {
    if (isFetchProgress.value) return;
    isFetchProgress.value = true;
    const result = fetchProgressMethod && await fetchProgressMethod(taskId.value);

    if (result.success) {
        if (!result.data) return;
        console.log('轮询结果:', result.data)
        const {
            totalCount,
            estimateCount,
            successCount,
            status,
            failedFileUrl: newFailedFileUrl,
        } = result.data;
        if (+status === 1) {
            // 导入中， 更新进度，继续轮询
            progress.value = ((totalCount / estimateCount) * 100).toFixed(1)
            isFetchProgress.value = false;
            return;
        }
        // 导入完成
        if (+status === 2) {
            if (successCount === totalCount) {
                importStatus.value = IMPORT_STATUS.FULL_IMPORT;
            } else {
                importStatus.value = IMPORT_STATUS.PARTIAL_IMPORT
                failedFileUrl.value = newFailedFileUrl;
            }
            progress.value = '100';
            emits('on-update')
            parsingCompleted()
            return;
        }
        // 导入失败
        if (+status === 3) {
            importStatus.value = IMPORT_STATUS.IMPORT_FAILED;
            failedMessage.value = result.data.failedMessage;
            parsingCompleted()
            return;
        }
    }
}
const startPolling = () => {
    pollingTimer.value = setInterval(() => fetchProgress(), 600)
}
// 关闭窗口的时候清除轮询, 触发关闭事件
watch(() => isShowModal, (newVal) => {
    if (!newVal) {
        emits('onClose')
        console.log(1111)
        clearInterval(pollingTimer.value);
    }
})


// step === 2 步骤三

const errorListRef = ref(null);
const errorList = ref([]);
const fetchResult = async() => {
    if (props.getErrorListUrl) {
        const condition = {
            taskId: taskId.value
        }
        const result = await getErrorListMethod(condition)
        errorList.value = result.data;
    }
}
const downloadFailListFile = () => {
    if (failedFileUrl.value) {
        window.open(failedFileUrl.value);
    }
}

const nextStep = async() => {
    // 0->1
    if (stepFlag.value === STEP_ENUMS.STEP_ONE) {
        const result = await onUploadFile()
        if (result) {
            // 开启轮询
            stepFlag.value = STEP_ENUMS.STEP_TWO
            startPolling();
            return;
        }
    }
    // 1->2
    if (stepFlag.value === STEP_ENUMS.STEP_TWO) {
        stepFlag.value = STEP_ENUMS.STEP_THREE
        fetchResult()
        return;
    }
    // end
    if (stepFlag.value === STEP_ENUMS.STEP_THREE) {
        closeModal();
        return;
    }
}
const clear = () => {
    file.value = null;
    fileState.value = null;
    taskId.value = null;
    progress.value = '0';
    pollingTimer.value = null;
    isFetchProgress.value = false
    failedFileUrl.value = null;
    importStatus.value = -1;
    failedMessage.value = ''
    errorListRef.value = null;
    errorList.value = []
}

const reImport = async() => {
    clear();
    stepFlag.value = STEP_ENUMS.STEP_ONE;
}
const closeModal = () => {
    isShowModal.value = false;
}

const confirmOption = [{
    text: '下一步',
    disabled: false
}, {
    text: '下一步',
    disabled: true
}, {
    text: '下载失败列表',
    disabled: false
}]

const calcelOption = [{
    text: '取消',
    disabled: false
}, {
    text: '取消',
    disabled: true
}, {
    text: '取消',
    disabled: false
}]

const confirmInfo = computed(() => {
    return confirmOption[stepFlag.value]
})
const cancelInfo = computed(() => {
    return calcelOption[stepFlag.value]
})

const columns = [
    {title: '行号', key: 'row'},
    {title: '失败原因', key: 'rowFailMessage'},
]
const openModal = () => {
    isShowModal.value = true;
}

defineExpose({openModal, closeModal})
</script>

<style lang="less" scoped>
.wrapper {
    padding: 12px 0;
    min-height: 400px;

    .header {
        margin: 24px 0;
        display: flex;
        justify-content: center;
        //:deep(.ivu-steps){
        //    .ivu-steps-item{
        //        display: flex;
        //        align-items: center;
        //        flex-direction: column;
        //        .ivu-steps-head-inner{
        //            margin-right: 0;
        //        }
        //        .ivu-steps-title{
        //            padding: 0;
        //        }
        //    }
        //}
    }

    .main {
        height: ~"calc(100% - 46px)";

        .step-1 {
            .default-box {
                height: 184px;
                display: flex;
                align-items: center;
                justify-content: center;
                flex-direction: column;

                .ivu-icon {
                    margin-bottom: 24px;
                }

                .t1 {
                    line-height: 22px;
                    margin-bottom: 4px;
                }

                .t2 {
                    color: #86909C;
                    font-size: 12px;
                    line-height: 20px;
                }
            }

            .list {
                margin-top: 10px;
                width: 90%;

                .box {
                    background: #F8FAFB;
                    border: 1px solid #F8FAFB;
                    border-radius: 2px;
                    height: 36px;
                    display: flex;
                    align-items: center;
                    position: relative;
                    margin-bottom: 12px;
                    padding: 0 12px;

                    .ia {
                        margin-right: 12px;
                    }

                    .name {
                        flex: 1;
                        line-height: 22px;
                        margin-right: 12px;
                        overflow: hidden;
                    }

                    .action {
                        display: flex;
                        align-items: center;

                        .del {
                            position: absolute;
                            top: 50%;
                            transform: translateY(-50%);
                            right: -28px;
                            font-size: 16px;
                            cursor: pointer;
                        }

                        .down {
                            cursor: pointer;
                        }

                        .loading {
                            animation: rotate 1s infinite linear;
                        }
                    }

                    &:hover {
                        border: 1px solid #165DFF;
                        background: #F3F7FB;
                    }

                    &.error {
                        .name,
                        .action {
                            color: #F53F3F;
                        }

                        &:hover {
                            background: #F8FAFB;
                            border: 1px solid #F8FAFB;
                        }
                    }
                }

                &.dis {
                    display: flex;
                    flex-wrap: wrap;

                    .box {
                        width: 40%;
                        margin-right: 10%;
                    }
                }
            }
        }

        .step-2 {
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;

            .progress-num {
                font-family: 'PingFang SC';
                font-style: normal;
                font-weight: 500;
                font-size: 14px;
            }

            .tips {
                margin-top: 8px;
                font-family: 'PingFang SC';
                font-style: normal;
                font-weight: 400;
                font-size: 14px;
                line-height: 22px;
                color: #798799;
            }
        }

        .step-3 {
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
            align-items: center;

            .error-table {
                margin-top: 20px;
                overflow-y: scroll;
                width: 100%;
                height: 100%;
            }

            :deep(.ivu-result) {
                width: 100%;

                .ivu-result-icon {
                    width: 40px;
                    margin-bottom: 20px;

                    .ivu-icon {
                        font-size: 40px;
                    }
                }

                .ivu-result-title {
                    font-size: 14px;
                    line-height: 22px;
                    color: #1E2A55;
                    margin-bottom: 0;
                }
            }
        }
    }
}
</style>
