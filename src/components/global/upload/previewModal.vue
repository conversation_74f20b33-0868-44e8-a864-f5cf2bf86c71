<template>
    <Modal
        v-model="showModal"
        :title="title"
        :mask-closable="false"
        footer-hide
        width="80"
        :class-name="'preview-modal ' + (title ? '' : 'no-title')"
    >
        <div class="img" v-if="type == 'img'">
            <img :src="url" alt="">
        </div>
        <div class="iframe" v-else-if="type == 'pdf'">
            <iframe :src="url" frameborder="0"></iframe>
        </div>
    </Modal>
</template>

<script>
export default {
    name: 'previewModal',
    data() {
        return {
            showModal: false,
            url: '',
            title: '',
            type: ''
        }
    },
    methods: {
        show(item) {
            const format = item.url.split('.').pop().toLocaleLowerCase() || '';
            this.title = item.name
            if (format === 'pdf') {
                this.type = 'pdf'
                this.url = 'static/pdf/web/viewer.html?file='+ item.url
            } else {
                this.type = 'img'
                this.url = item.url
            }

            this.showModal = true
        },
        hide() {
            this.showModal = false
        }
    }
}
</script>

<style lang="less" scoped>

.img{
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    img{
        max-height: 100%;
        max-width: 100%;
    }
}
.iframe{
    height: 100%;
    iframe{
        width: 100%;
        height: 100%;
    }
}
</style>
