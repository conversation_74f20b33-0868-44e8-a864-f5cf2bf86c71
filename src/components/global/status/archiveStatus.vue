<template>
    <div class="useStatus">
        <s-tag v-if="value == 1" background="#E8F3FF" color="#165DFF">{{ normalTxt }}</s-tag>
        <s-tag v-else-if="value == 0" background="#FFF7E8" color="#FF7D00">{{ errTxt }}</s-tag>
    </div>
</template>

<script>
export default {
    name: 'ArchiveStatus',
    props: {
        value: { default: '' },
        errTxt: { default: '待归档' },
        normalTxt: { default: '已归档' },
    },
    data() {
        return {}
    },
    methods: {}
}
</script>

<style lang="less" scoped>
.useStatus{
    display: inline-block;
}
</style>
