<template>
    <div class="useStatus">
        <s-tag v-if="value == 1" background="#E8F3FF" color="#165DFF">启用</s-tag>
        <s-tag v-else-if="value == 0" background="#FFF7E8" color="#FF7D00">停用</s-tag>
    </div>
</template>

<script>
export default {
    name: 'useStatus',
    props: {
        value: { default: '' }
    },
    data() {
        return {}
    },
    methods: {}
}
</script>

<style lang="less" scoped>
.useStatus{
    display: inline-block;
}
</style>
