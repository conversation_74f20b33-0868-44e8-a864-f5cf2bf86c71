<template>
    <div class="onlineStatus">
        <s-tag v-if="value == 0">正常</s-tag>
        <s-tag v-else-if="value == 1 " background="#F2F3F5" color="#4E627E">异常</s-tag>
        <span v-else>--</span>
    </div>
</template>

<script>
export default {
    name: 'checkStatus',
    props: {
        value: { default: '' }
    },
    data() {
        return {}
    },
    methods: {}
}
</script>

<style lang="less" scoped>
.onlineStatus{
    display: inline-block;
}
</style>
