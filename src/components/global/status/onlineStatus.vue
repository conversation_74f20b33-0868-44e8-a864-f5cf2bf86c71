<template>
    <div class="onlineStatus">
        <s-tag v-if="value == 1">在线</s-tag>
        <s-tag v-else-if="value == 0 || value == 2" background="#F2F3F5" color="#4E627E">离线</s-tag>
    </div>
</template>

<script>
export default {
    name: 'onlineStatus',
    props: {
        value: { default: '' }
    },
    data() {
        return {}
    },
    methods: {}
}
</script>

<style lang="less" scoped>
.onlineStatus{
    display: inline-block;
}
</style>
