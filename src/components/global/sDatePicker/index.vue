<template>
    <DatePicker v-model="timeValue" @on-change="changeTime" @on-clear="clearTime" @on-ok="handleOk" :transfer="transfer"
                :type="type" :placeholder="placeholder" :format="format" :placement="placement" :confirm="confirm" :editable="false" />
</template>

<script>

export default {
    name: 'sDatePicker',
    props: {
        modelValue: { type: [String, Array] },
        transfer: { default: true },
        placement: { default: 'bottom' },
        placeholder: { default: '请选择' },
        type: { default: 'datetimerange' },
        confirm: { default: true }
    },
    emits: ['update:modelValue', 'on-change', 'on-ok', 'on-clear'],
    data() {
        let timeValue = []
        if (this.modelValue) {
            timeValue = this.modelValue
        }
        return {
            timeValue: timeValue,
            isChangeValueIn: false,
            isClear: false
        }
    },
    computed: {
        format() {
            if (this.type == 'daterange') {
                return 'yyyy-MM-dd'
            }
            return 'yyyy-MM-dd HH:mm:ss'
        }
    },
    watch: {
        modelValue(newVal) {
            this.timeValue = newVal
        }
    },
    methods: {
        changeTime(val) {
            if (this.isClear) {
                this.isClear = false
                this.isChangeValueIn = true
                this.$emit('update:modelValue', '');
                this.$emit('on-clear')
                this.$emit('on-change', '')
                return
            }
            if (val && val[0]) {
                let sArr = val[0].split(' ')
                if (!sArr[1]) { // 如果为空，加上时分秒
                    val[0] = sArr[0] + ' 00:00:00'
                }
                let eArr = val[1].split(' ')
                if (!eArr[1] || (eArr[1] === '00:00:00')) {
                    // 如果为空或者通过日期修改时，时分秒改为' 23:59:59'
                    val[1] = eArr[0] + ' 23:59:59'
                }
            }
            this.timeValue = val
            this.isChangeValueIn = true
            this.$emit('update:modelValue', val);
            this.$emit('on-change', val)
        },
        handleOk() {
            this.$emit('on-ok')
        },
        clearTime() {
            this.isClear = true
        }
    }
}
</script>

<style lang="less" scoped>

</style>
