<script>
import { h, resolveComponent } from 'vue';
import { Table, Page } from 'view-ui-plus';
import tooltipAutoShow from '@/components/global/tooltipAutoShow/index.vue';
import { deepClone, isZeroTrue,isEmpty } from 'wei-util';

export default {
    name: 'BaseTable',
    components: [tooltipAutoShow],
    props: {
        columns: { default: () => [] },
        data: { default: undefined, type: Array }, // 全量数据，不通过接口请求进行分页
        url: { default: '' }, // 接口请求地址
        method: { default: 'post' }, // 接口请求方式
        height: { type: [String, Number] },
        maxHeight: { type: [String, Number] },
        loadDone: { default: undefined, type: Function }, // 请求完成后调用的方法
        showPage: { default: true }, // 是否显示分页
        showHeader: { default: true }, // 是否显示表头
        maxTotal: { default: null }, // 是否限制最大总数
        pageSize: { default: 10 }, // 分页的默认size
        pageOpts: { default: () => [5, 10, 15, 20, 30, 50, 100] },
        showSizer: { default: true },
        showElevator: { default: true },
        showTotal: { default: true },
        simple: { default: false },
        size: { default: 'default' },
        emptyBlockStr: { type: String }, // 单元格为空的时候显示的默认内容
        rowKey: { type: [Boolean, String] }, // 同 :key ，用于解决改变数组顺序时，diff算错的问题
        selectedId: { default: () => [] },
        spanMethod: { type: Function, default: undefined },
        openSuffixCheck: { default: false }, // 打开后缀检查,默认为不打开，在columns中添加后缀属性,可省一些插槽代码
        border: { default: false }
    },
    data() {
        return {
            list: [],
            loading: false,
            searchObj: {
                // search的条件，默认只根据name查询
                page: { current: 1, size: this.pageSize },
                customQueryParams: {},
                sorts: [],
            },
            total: 0, // 分页时显示的总条数
        };
    },
    computed: {
        tableList() {
            const handleSuffix = (data) => {
                const deepCloneData = deepClone(data)
                const isHasSuffix = (obj) => obj.hasOwnProperty('suffix')
                this.columns.forEach(item => {
                    if (isHasSuffix(item)) {
                        deepCloneData.forEach(obj => {
                            obj[item.key] = isZeroTrue(obj[item.key]) ? obj[item.key] + item.suffix : obj[item.key] || this.emptyBlockStr ? this.emptyBlockStr : obj[item.key]
                        })
                    }else if(!isEmpty(item.children)){
                        item.children.forEach(i=>{
                            if (isHasSuffix(i)) {
                                deepCloneData.forEach(obj => {
                                    obj[i.key] = isZeroTrue(obj[i.key]) ? obj[i.key] + i.suffix : obj[i.key] || this.emptyBlockStr ? this.emptyBlockStr : obj[i.key]
                                })
                            }
                        })

                    }
                }
                )
                return deepCloneData
            }
            if (this.data) {
                this.loading = false;
                if (this.showPage) {
                    // 内存分页
                    const start = (this.searchObj.page.current - 1) * this.searchObj.page.size;
                    const end = start + this.searchObj.page.size;
                    const pageSizeData = this.data.slice(start, end) // 页码数据
                    const data = this.openSuffixCheck && handleSuffix(pageSizeData) || pageSizeData
                    return data;
                }
                const data = this.openSuffixCheck && handleSuffix(this.data) || this.data
                return data;
            }
            const data = this.openSuffixCheck && handleSuffix(this.list) || this.list
            return data;
        },
        totalSize() {
            if (!this.data || this.data.length === 0) {
                return this.total;
            }
            return this.data.length;
        },
        columnsWithDefault() {
            if (this.emptyBlockStr) {
                return this.columns.map((item) => {
                    if (item.slot || item.render) return item;
                    return {
                        ...item,
                        render: (h, params) => {
                            return h(
                                item.tooltip ? tooltipAutoShow : 'span',
                                params.row[item.key] ?? this.emptyBlockStr
                            );
                        },
                    };
                });
            } else {
                return this.columns;
            }
        },
    },
    watch: {
        selectedId: {
            handler() {
                const _data = this.$refs.tb.objData;
                Object.keys(_data).forEach((key) => {
                    _data[key]._isChecked = this.selectedId.includes(
                        _data[key].id || _data[key].deviceId
                    );
                });
            },
            deep: true,
        },
    },
    mounted() { },
    methods: {
        search(model = {}) {
            this.searchObj.customQueryParams = model;

            this.pageChange(1);
        },
        // 改变数据
        changeData(callback) {
            if (!this.url || !callback) {
                return;
            }
            this.list = callback(this.list);
        },
        getData() {
            if (!this.url) {
                this.loading = false;
                return;
            }
            this.list = [];
            this.loading = true;
            this.$request(this.url, this.searchObj, this.method)
                .then((resp) => {
                    const data = resp.data;
                    if (data) {
                        this.list = Array.isArray(data) && data || data.records || [];
                        if (this.loadDone) {
                            const resData = this.loadDone(data, data.records);
                            if (resData) {
                                this.list = resData;
                            } else {
                                this.list = data.records;
                            }
                        }
                        this.total = data.total;
                        this.$nextTick(() => {
                            this.$emit('tableData', data);
                            const _data = this.$refs.tb.objData;
                            Object.keys(_data).forEach((key) => {
                                _data[key]._isChecked = this.selectedId.includes(
                                    _data[key].id || _data[key].deviceId
                                );
                            });
                            // this.$refs.tb.objData.forEach(ele=>ele._isChecked=true)
                        });
                    } else {
                        this.list = [];
                        this.total = 0;
                        this.$nextTick(() => {
                            this.$emit('tableData', []);
                        });
                    }
                    this.loading = false;
                })
                .catch((err) => {
                    this.loading = false;
                });
        },
        pageChange(page) {
            // console.log(page)
            this.searchObj.page.current = page;
            this.$emit('update:changePage');
            this.getData();
            this.$emit('on-changePage', page);
        },
        pageSizeChange(size) {
            this.searchObj.page = { current: 1, size: size };
            this.$emit('update:pageSize', size);
            this.getData();
        },
        exportCsv(name = 'file') {
            this.$refs.tb.exportCsv({ filename: name });
        }
    },
    render() {
        let propCus = {
            ref: 'tb',
            data: this.tableList,
            loading: this.loading,
            onOnSelectionChange: (selection) => this.$emit('on-selection-change', selection),
            onOnSelectionAllCancel: (selection) => this.$emit('on-selection-all-cancel', selection),
            onOnSelectionCancel: (selection) => this.$emit('on-selection-cancel', selection),
            onOnSelection: (selection) => this.$emit('on-selection', selection),
            onOnSelectionAll: (selection) => this.$emit('on-selection-all', selection),
            onOnExpand: (row, status) => this.$emit('on-expand', row, status),
            onOnRowClick: (row, index) => this.$emit('on-row-click', row, index),
        };
        this.columns &&
            this.columns.forEach((item, index) => {
                if (item.slot === 'action' && index === this.columns.length - 1) {
                    item.fixed = 'right';
                }
                if (!item.width && !item.minWidth) {
                    item.minWidth = 80;
                }
            });
        // console.log(this.columns)
        const r = [
            h(Table, { ...this.$props, columns: this.columnsWithDefault, ...propCus }, this.$slots),
        ];
        if (this.showPage) {
            r.push(
                h(Page, {
                    total: this.maxTotal != null && this.maxTotal < this.totalSize ? this.maxTotal : this.totalSize,
                    modelValue: this.searchObj.page.current,
                    showElevator: this.showElevator,
                    showTotal: this.showTotal,
                    simple: this.simple,
                    pageSize: this.searchObj.page.size,
                    size: 'small',
                    pageSizeOpts: this.pageOpts,
                    showSizer: this.showSizer,
                    transfer: true,
                    style: {
                        'text-align': 'right',
                    },
                    onOnChange: this.pageChange,
                    onOnPageSizeChange: this.pageSizeChange,
                })
            );
        }
        return h('div', { class: 'base-table ' + (this.size ? this.size : '') }, r);
    },
};
</script>

<style lang="less" scoped>
.base-table {}
</style>
