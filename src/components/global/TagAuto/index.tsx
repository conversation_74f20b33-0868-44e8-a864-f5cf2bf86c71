import { defineComponent, Slots, computed } from "vue";
import "./tag.less";
interface SetupContext {
  slots: Slots;
}
const onLineFetch = {
  0: 'offline',
  1: 'online',
}
const useStateFetch = {
  0: 'stop',
  1: 'normal'
}
const onLineFetchName = {
  0: '离线',
  1: '在线'
}
const useStateFetchName = {
  0: '停用',
  1: '启用'
}

const switchStateFetch = {
  0: '关闭',
  1: '开启'
}
const deviceState = {
  1: '正常',
  2: '异常'
}
// 井盖状态
const wellState = {
  0:'正常',
  1:'异常'
}
// 类型枚举
const TYPE_MAPPER = {
    ONE: 1,
    TWO: 2,
    THREE: 3,
    FOUR: 4,
    FIVE: 5, // 井盖
    SIX: 6,
    SEVEN: 7,
    EIGHT: 8,
    NINE: 9,
}

// type --> Content 映射
const TYPE_CONTENT_MAPPER = {
    [TYPE_MAPPER.ONE]: onLineFetchName,
    [TYPE_MAPPER.TWO]: useStateFetchName,
    [TYPE_MAPPER.THREE]: switchStateFetch,
    [TYPE_MAPPER.FOUR]: deviceState,
    [TYPE_MAPPER.FIVE]: wellState,
}
// type --> ClassName 映射
const TYPE_CLASS_NAME_MAPPER = {
    [TYPE_MAPPER.ONE]: onLineFetch,
    [TYPE_MAPPER.TWO]: useStateFetch,
    [TYPE_MAPPER.THREE]: useStateFetch,
    [TYPE_MAPPER.FOUR]: useStateFetch,
    [TYPE_MAPPER.FIVE]: useStateFetch,
}
export default defineComponent({
  name: 'autoTag',
  props: {
    type: {
      default: 1 // 1在线状态，2使用状态 3开关状态
    },
    value: {
      default: ''
    },
    size: {
      default: 'small'
    }
  },
  setup(props, { slots }: SetupContext) {
    const content = computed(() => TYPE_CONTENT_MAPPER[props.type][props.value])
    const className = computed(() => TYPE_CLASS_NAME_MAPPER[props.type][props.value])
    return () => (
      <div class={className.value ? `tag-cont ${props?.size} ${className.value}` : `tag-cont ${props.size}`}>
        {content.value}
      </div>
    );
  },
});
