// @ts-nocheck
import { CellGroup, Icon } from 'view-ui-plus'
import { handleCoordinate } from '@/utils/tool'
import { ComponentInfo } from '@/api/fireHydrantService'
import DeviceMarkerMap from '@/components/common/deviceMarkerMap'
import { defineComponent, ref, Slots, markRaw,resolveComponent,defineAsyncComponent,h } from "vue";
interface SetupContext {
  slots: Slots;
}
interface JwdList {
  objX: number,
  objY: number
}
interface Props {
  objInfo: ComponentInfo
}
export default defineComponent({
  name: 'jwdMap',
  props: {
    objInfo: {
      default: () => ({})
    },
    isShowJwd:{
      default:true
    }
  },
  // components:{
  //   DeviceMarkerMap:defineAsyncComponent(()=>import('@/components/common/deviceMarkerMap'))
  //  },
  setup(props: Props, { slots }: SetupContext) {
    console.log(props.objInfo,'jwdObjb');
    // 点击经纬度
    const componentId = ref<string>('')
    const showMap = ref<boolean>(false)
    const jwdList = ref<Array<JwdList> | Array<any>>([])
    function openMap() {
      // componentId.value = 'DeviceMarkerMap'
      showMap.value = true
      jwdList.value.push({
        objX: props.objInfo.gdx && props.objInfo.gdx || 0,
        objY: props.objInfo.gdy && props.objInfo.gdy || 0
      })
      console.log(componentId.value,jwdList.value);
    }
    function closeModal() {
      // componentId.value = ''
      jwdList.value = []
      showMap.value = false
    }
    return () => (
      <div>
        {
          handleCoordinate(props.objInfo) ? <Icon type="ios-pin-outline" onClick={openMap} style = 'cursor:pointer;font-weight: 600;' color="#165DFF" size="16" /> : ''
        }
        {
        props.isShowJwd ? (handleCoordinate(props.objInfo) || '--') : ''
        }
       { showMap.value ? <DeviceMarkerMap makers={jwdList.value} onClose-modal={closeModal} /> : ''}
      </div>
    );
  },
});
