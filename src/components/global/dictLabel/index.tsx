import { defineComponent, Slots } from "vue";
import { useStore } from 'vuex'
export default defineComponent({
    name: "dict<PERSON><PERSON><PERSON>",
    props: {
        code: {
            type: String,
            default: "#00B42A",
        },
        value: { type: String, default: "#E8FFEA" },
    },
    setup(props) {
        const $store=useStore()
        return () => (
            <><tooltipAutoShow>{$store.getters.dictionary[props.code][props.value]||'--'}</tooltipAutoShow></>
        );
    },
});
