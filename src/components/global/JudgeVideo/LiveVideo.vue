<style lang="less" scoped>
.live-main {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;
    object-fit: fill;

    /deep/ video {
        height: 100% !important;
    }

    /deep/ .jessibuca-container {
        .jessibuca-controls {
            height: 30px !important;
        }

        .jessibuca-speed-menu-list {
            background: #000;
        }

        .jessibuca-speed-menu-item {
            min-width: 50px;
            width: auto;
            color: #fff;
        }
        .custom-progress {
            height: 6px;
            position: relative;
            width: 100px;
            border-radius: 3px;
            background: rgba(6, 176, 222, 0.2);
            .progress-bar {
                position: absolute;
                height: 100%;
                border-radius: 3px;
                width: 50%;
                background: #05a9d7;
            }
            .yuan {
                position: absolute;
                width: 12px;
                height: 12px;
                border-radius: 50%;
                background: #fff;
                right: 0;
                top: 50%;
                transform: translateY(-50%);
            }
        }
    }
}
</style>
<template>
    <div :style="styles" class="live-main">
        <div :id="mainId" ref="parentId" :style="styles" class="video-div"></div>
    </div>
</template>

<script>
import { Slider } from "view-ui-plus";
import { h } from "vue";

export default {
    name: "LiveVideo",
    components: { Slider },
    props: {
        path: {
            type: String,
            default: null,
        },
        styles: {
            type: [String],
            default: () => {
                return "width: 100%; height: 100%;object-fit:fill";
            },
        },
        domId: {
            type: [String],
            default: "wsFlv",
        },
        mainId: {
            type: [String],
            default: "wsFlvMain",
        },
        isShowAudioBtn: { default: true },
    },
    data() {
        return {
            flvInstance: "",
            hlsPlayer: "",
            loading: true,
            sitimer: null,
            videoUrl: "",
            playback: {},
            schedule: 0,
        };
    },
    watch: {
        path(newVal) {
            this.create(this.path);
        },
        isShowAudioBtn() {
            this.toggleAudioBtn();
        },
    },
    mounted() {
        if (this.path) {
            this.$nextTick(() => {
                this.create(this.path);
            });
        }
    },
    beforeDestroy() {
        console.log("beforeDestroy live video");
        this.dispose();
    },
    methods: {
        // 传播放地址
        create(url, playback) {
            console.log("url", url, this.domId);
            this.dispose();
            if (!url) {
                return new Promise((resolve, reject) => {
                    reject();
                });
            }
            this.videoUrl = url;
            let domUrl = document.createElement("div");
            domUrl.setAttribute("id", this.domId);
            domUrl.style = "width: 100%; height: 100%";
            document.getElementById(this.mainId).appendChild(domUrl);
            if (!this.hlsPlayer) {
                this.hlsPlayer = new JessibucaPro({
                    container: domUrl,
                    videoBuffer: 0.3, // 缓存时长
                    // decoder: "/dist/jessibuca/decoder.js",
                    decoder: "/dist/jessibuca-pro/decoder-pro.js",
                    isResize: false,
                    setScaleMode: 1,
                    controlAutoHide: true,
                    text: "",
                    // 视频加载转圈时的提示文字
                    loadingText: "加载中",
                    // 是否有音频，如果设置false，则不对音频数据解码，提升性能。
                    hasAudio: true,
                    //  是否开启控制台调试打印
                    debug: false,
                    // 是否显示网速
                    showBandwidth: true,
                    heartTimeoutReplayTimes: -1,
                    // fullscreen 是否显示全屏按钮
                    // screenshot 是否显示截图按钮
                    // play 是否显示播放暂停按钮
                    // audio 是否显示声音按钮
                    // record 是否显示录制按钮
                    operateBtns: {
                        fullscreen: true,
                        screenshot: false,
                        play: true,
                        audio: true,
                    },
                    // vod: this.vod,
                    // 是否不使用离屏模式（提升渲染能力）
                    // forceNoOffscreen: this.forceNoOffscreen,
                    // 是否开启声音，默认是关闭声音播放的。
                    isNotMute: false,
                    wcsUseVideoRender: false,
                });
                this.initEvent();
                this.toggleAudioBtn();
                if (playback) {
                    this.playback = playback;
                    return this.initPlayback(playback);
                } else {
                    return this.hlsPlayer.play(url);
                }
            }
        },
        mousemoveFun(e) {
            console.log(e);
        },
        // 回看
        initPlayback(data) {
            let playTimesArray = [
                {
                    start: new Date(data.startTime).getTime(),
                    end: new Date(data.endTime).getTime(),
                },
            ];
            console.log(playTimesArray, data);
            this.playbackEvent();
            return new Promise((resolve, reject) => {
                this.hlsPlayer.playback(this.videoUrl, {
                    playList: playTimesArray,
                    showRateBtn: true,
                    rateConfig: [
                        { value: 0.5, label: "0.5倍" },
                        { value: 1, label: "1倍" },
                        { value: 2, label: "2倍" },
                        { value: 4, label: "4倍" },
                    ],
                    showControl: false,
                });
                resolve();
            });
        },
        playbackEvent() {
            this.hlsPlayer.on("playbackPreRateChange", (value) => {
                console.log("倍率", value);
                this.playback.rate = value;
                this.changeSpeed();
                this.hlsPlayer.playbackForward(value);
            });
        },
        // 请求倍速接口
        changeSpeed() {
            let param = {
                downParameter: {
                    device_id: this.playback.deviceCode,
                    service_id: "playback_speed",
                    parameter: { streamId: this.playback.stream, speed: this.playback.rate },
                },
            };
            this.$Util
                .request({ url: "/deviceManage/send", data: param, method: "post" })
                .then((res) => {});
        },
        toggleAudioBtn() {
            if (!this.isShowAudioBtn) {
                document.getElementById(this.mainId).className = "hide-audio-btn";
            } else {
                document.getElementById(this.mainId).className = "";
            }
        },
        // 倒计时一小时后重新播放一次
        downTime() {
            let time = 61 * 60 * 1000;
            this.sitimer = setTimeout(() => {
                this.create(this.videoUrl);
            }, time);
        },
        initEvent() {
            // this.hlsPlayer.on('error', res => {
            // 	console.log('error', res)
            // 	if (res != 'simdDecodeError') {
            // 		this.$emit('on-error', false)
            // 	}
            // })
            this.hlsPlayer.on("play", (res) => {
                console.log("play on", res);
                this.$emit("on-error", true);
            });
            // let arr = ['loadingTimeoutRetryEnd', 'delayTimeoutRetryEnd', 'pause', 'crashLog', 'blur', 'websocketClose']
            // for(let i in arr) {
            //   this.hlsPlayer.on(arr[i], res => {
            //     console.log(arr[i] + ' on', res)
            //   })
            // }
            this.hlsPlayer.on("playFailedAndPaused", (error) => {
                console.log("playFailedAndPaused on", error);
                this.$emit("on-error", false, "播放报错，请稍后重试");
            });

            this.hlsPlayer.on("timeout", (error) => {
                console.log("timeout on", error);
                this.$emit("on-error", false, "设备播放超时，请稍后重试");
            });
        },
        clear() {
            this.dispose();
        },
        dispose() {
            if (this.sitimer) {
                clearTimeout(this.sitimer);
            }
            this.loading = false;
            if (this.hlsPlayer) {
                this.hlsPlayer.destroy();
                this.hlsPlayer = null;
            }
            if (document.getElementById(this.mainId)) {
                document.getElementById(this.mainId).innerHTML = "";
            }
        },
    },
};
</script>
