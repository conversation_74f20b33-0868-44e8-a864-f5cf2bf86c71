<template>
    <div class="con-video" :id="'fullVideoEl_' + index" :style="stylesVideo">
        <div v-show="videoType === 'ezopen'">
            <ysy-model
                :main-id="mainId"
                :ref="'ysy' + index"
                :styles="{
                    width: stylesVideo.width,
                    height: stylesVideo.height,
                    position: 'relative',
                }"
                :controls="controls"
                :is-show-audio-btn="isShowAudioBtn"
            />
        </div>
        <div v-show="videoType === 'flv'">
            <OpenFlv
                :main-id="`flvMain${index}`"
                :dom-id="`flv${index}`"
                :url="curVideoObj.url"
                :ref="`flv${index}`"
                :styles="
                    'width:' +
                    stylesVideo.width +
                    ';height:' +
                    stylesVideo.height +
                    ';object-fit:fill;'
                "
                :controls="controls"
                @on-play="v.isPlayLoading = false"
            />
        </div>
        <div v-show="videoType === 'hlsUrl'">
            <LiveVideo
                :ref="`m3R${index}`"
                :main-id="`m3RMain${index}`"
                :url="curVideoObj.url"
                :dom-id="`m3R${index}`"
                :controls="controls"
                @on-play="v.isPlayLoading = false"
                :styles="'width:' + stylesVideo.width + ';height:' + stylesVideo.height + ';'"
                :is-show-audio-btn="isShowAudioBtn"
            />
        </div>
        <div v-show="videoType === 'wsflvUrl'">
            <LiveVideo
                :ref="`wsFlv${index}`"
                :main-id="`wsFlvMain${index}`"
                :dom-id="`wsFlv${index}`"
                :controls="controls"
                @on-play="v.isPlayLoading = false"
                @on-error="playError"
                :styles="'width:' + stylesVideo.width + ';height:' + stylesVideo.height + ';'"
                :is-show-audio-btn="isShowAudioBtn"
            />
        </div>
        <div v-show="videoType === 'imou'">
            <imouOpen
                :main-id="`imouMain${index}`"
                :dom-id="`imou${index}`"
                :ref="`imou${index}`"
                :styles="{
                    width: stylesVideo.width,
                    height: stylesVideo.height,
                    position: 'relative',
                }"
                :controls="controls"
                @on-play="v.isPlayLoading = false"
            />
        </div>
        <div v-show="videoType === 'mp4'">
            <videoJs
                :main-id="`mp4Main${index}`"
                :dom-id="`mp4${index}`"
                :ref="`mp4${index}`"
                :styles="{ width: stylesVideo.width, height: stylesVideo.height }"
                :controls="controls"
            />
        </div>
        <div v-if="!controls || videoType === 'imou'">
            <CustomControls
                ref="controlsR"
                @on-play="playVideo"
                @on-pause="pauseVideo"
                @on-set-volume="setVolumeVideo"
                @on-full-screen="fullScreen"
                @on-exit-screen="exitScreen"
            />
        </div>
        <div class="play-info" v-if="isShowPlay">
            <div class="msg" v-if="m.errorMsg">{{ m.errorMsg }}</div>
            <div class="play" @click="replay">
                <Icon type="md-play" />
            </div>
        </div>
        <div class="my-loading" v-if="loading">
            <Icon type="ios-loading" size="18" class="demo-spin-icon-load"></Icon>
            <div>加载中...</div>
        </div>
    </div>
</template>

<script>
import Util from "@/libs/util.js";
import YsyModel from "@/views/camera/ysyModel";
import OpenFlv from "@/views/distributionRoom/openFlv";
import imouOpen from "@/components/video/imouOpen";
import CustomControls from "@/components/video/CustomControls";
import LiveVideo from "./LiveVideo";
import videoJs from "@/components/video/videoJs";

export default {
    name: "JudgeVideo",
    components: {
        YsyModel,
        OpenFlv,
        imouOpen,
        CustomControls,
        LiveVideo,
        videoJs,
    },
    props: {
        videoObj: {
            default() {
                return {};
            },
        },
        styles: { default: { width: "300px", height: "300px" } },
        index: { type: [String, Number], default: 0 },
        controls: { default: true },
        isShowAudioBtn: { default: true },
    },
    data() {
        return {
            curVideoObj: this.videoObj,
            v: {
                isFull: false,
            },
            isShowPlay: false,
            loading: true,
            m: {
                errorMsg: "",
                maxResNum: 3, // 超时最大请求次数
                curResNum: 0,
            },
        };
    },
    computed: {
        mainId() {
            return this.curVideoObj.mainId ? this.curVideoObj.mainId : "main" + this.index;
        },
        videoType() {
            // this.videoObj.propName === 'flvUrl' // 补加
            if (this.curVideoObj.propName === "flvUrl") {
                return "flv";
            } else if (this.curVideoObj.propName === "ezopen") {
                return "ezopen";
            } else if (this.curVideoObj.propName === "live_address") {
                return "imou";
            } else if (this.curVideoObj.propName === "hlsUrl") {
                return "hlsUrl";
            } else if (
                this.curVideoObj.propName === "wsflvUrl" ||
                this.curVideoObj.propName === "ws_flv"
            ) {
                return "wsflvUrl";
            } else if (this.curVideoObj.propName === "mp4") {
                return "mp4";
            } else {
                return "";
            }
        },
        stylesVideo() {
            let obj = Util.objClone(this.styles);
            if (this.v.isFull) {
                obj.width = screen.width + "px";
                obj.height = screen.height + "px";
            }
            return obj;
        },
    },
    watch: {
        videoObj(val) {
            if (val) {
                console.log(val);
                this.curVideoObj = val;
            }
        },
    },
    mounted() {
        // this.curVideoObj = this.videoObj
    },
    methods: {
        open(data) {
            if (data) {
                this.curVideoObj = data;
                this.m.curResNum = 0;
            }
            this.loading = true;
            this.isShowPlay = false;
            let isInternalIP = Util.isInternalIP();
            return new Promise((resolve, reject) => {
                console.log(data, this.index, this.videoType, this.curVideoObj.propName);
                if (this.curVideoObj.propName === "flvUrl") {
                    if (this.curVideoObj.deviceCode) {
                        this.$Util
                            .request({
                                url: "/imageCapture/getVideoFlow/" + this.curVideoObj.deviceCode,
                                method: "get",
                            })
                            .then((resp) => {
                                if (resp.success) {
                                    this.loading = false;
                                    let url = resp.data.flvUrl;
                                    if (isInternalIP) {
                                        if (resp.data.flvUrl2) {
                                            url = resp.data.flvUrl2;
                                            console.info(url, "采用内网地址");
                                        }
                                    }
                                    this.$refs["flv" + this.index].open(url);
                                } else {
                                    this.playError();
                                    this.$Message.warning("没有视频地址");
                                    this.$emit("on-flv-fail");
                                }
                                resolve();
                            })
                            .catch(() => {
                                this.loading = false;
                                this.isShowPlay = true;
                            });
                    } else {
                        this.loading = false;
                        this.$refs["flv" + this.index].open(this.curVideoObj.videoUrl);
                        resolve();
                    }
                } else if (this.curVideoObj.propName === "ezopen") {
                    let obj = {
                        id: "video" + this.index,
                        url: this.curVideoObj.videoUrl,
                        accessToken: this.curVideoObj.ezopenObj.accessToken,
                    };
                    if (!this.curVideoObj.mainId) {
                        obj.mainId = "main" + this.index;
                    } else {
                        obj.mainId = this.curVideoObj.mainId;
                    }
                    this.loading = false;
                    this.$refs["ysy" + this.index]
                        .init(obj)
                        .then((re) => {
                            re.play();
                            resolve();
                        })
                        .catch(() => {
                            resolve();
                        });
                } else if (this.curVideoObj.propName === "live_address") {
                    let src = {
                        url: this.curVideoObj.videoUrl,
                        kitToken: this.curVideoObj.ezopenObj.live_token,
                    };
                    this.$refs["imou" + this.index].init([src]).then((re) => {
                        re.play();
                        resolve();
                    });
                } else if (this.curVideoObj.propName === "hlsUrl") {
                    console.log(this.curVideoObj);
                    let code = this.curVideoObj.code || this.curVideoObj.deviceCode;
                    if (code) {
                        this.getPlayUrlByCode(code)
                            .then((data) => {
                                this.loading = false;
                                let videoUrl = data.hlsUrl || data.https_hls;
                                if (isInternalIP) {
                                    if (data.hlsUrl2) {
                                        videoUrl = data.hlsUrl2;
                                        console.info(videoUrl, "采用内网地址");
                                    }
                                }
                                this.$refs["m3R" + this.index].create(videoUrl);
                            })
                            .catch(() => {
                                this.$refs["m3R" + this.index].dispose();
                            });
                    } else {
                        this.loading = false;
                        this.$refs["m3R" + this.index]
                            .create(this.curVideoObj.videoUrl, this.curVideoObj.playback)
                            .then(() => {
                                resolve();
                            });
                    }
                } else if (
                    this.curVideoObj.propName === "wsflvUrl" ||
                    this.curVideoObj.propName === "ws_flv"
                ) {
                    let code = this.curVideoObj.code || this.curVideoObj.deviceCode;
                    if (code) {
                        this.getPlayUrlByCode(code)
                            .then((data) => {
                                let videoUrl =
                                    data.wsflvUrl || data[data.wvp_stream_prefix] || data.ws_flv;
                                if (isInternalIP) {
                                    if (data.wsflvUrl2) {
                                        videoUrl = data.wsflvUrl2;
                                        console.info(videoUrl, "采用内网地址");
                                    }
                                }
                                this.loading = false;
                                this.$refs["wsFlv" + this.index]
                                    .create(videoUrl)
                                    .then(() => {
                                        console.log("play success");
                                        resolve();
                                    })
                                    .catch(() => {
                                        console.log("play error");
                                        this.playError();
                                        resolve();
                                    });
                            })
                            .catch(() => {
                                this.$refs["wsFlv" + this.index].dispose();
                                resolve();
                            });
                    } else {
                        this.loading = false;
                        this.$refs["wsFlv" + this.index]
                            .create(this.curVideoObj.videoUrl)
                            .then(() => {
                                resolve();
                            });
                    }
                } else if (this.curVideoObj.propName === "mp4") {
                    this.loading = false;
                    this.$refs["mp4" + this.index]
                        .open(this.curVideoObj.videoUrl, this.curVideoObj.playback)
                        .then(() => {
                            resolve();
                        });
                }
            });
        },
        // 得到播放地址
        getPlayUrlByCode(code) {
            return new Promise((resolve, reject) => {
                this.$Util
                    .request({
                        url: `/deployCamera/play?deviceCode=${code}`,
                        method: "POST",
                    })
                    .then((resp) => {
                        if (resp.success) {
                            resolve(resp.data);
                        } else {
                            if (resp.message.indexOf("503, Service Unavailable") >= 0) {
                                this.m.errorMsg = "设备服务不可达，请检查设备";
                                this.playError();
                            } else if (
                                resp.message.indexOf("timeout") >= 0 ||
                                resp.message.indexOf("收流超时") >= 0
                            ) {
                                this.timeoutPlay(code);
                            } else {
                                this.m.errorMsg = "没有视频地址";
                                this.playError();
                            }
                            reject();
                            this.$emit("on-flv-fail");
                        }
                    })
                    .catch(() => {
                        this.playError();
                        this.clear();
                        reject();
                    });
            });
        },
        // 超时重新请求
        timeoutPlay(code) {
            if (this.m.curResNum < this.m.maxResNum) {
                this.m.curResNum++;
                this.open();
            } else {
                this.m.errorMsg = "设备播放超时，请稍后重试";
                this.playError();
            }
        },
        // 播放失败，显示按钮 flag: true-播放成功
        playError(flag, msg) {
            if (flag) {
                this.isShowPlay = false;
            } else {
                this.isShowPlay = true;
            }
            this.loading = false;
            if (msg) {
                this.m.errorMsg = msg;
            }
        },
        // 重新播放
        replay() {
            this.m.curResNum = 0;
            this.isShowPlay = false;
            this.loading = true;
            this.open();
        },
        pauseVideo() {
            this.v.isPlay = false;
            if (this.videoType === "flv") {
                this.$refs["flv" + this.index].pauseVideo();
            } else if (this.videoType === "ezopen") {
                this.$refs["ysy" + this.index].pauseVideo();
            } else if (this.videoType === "imou") {
                this.$refs["imou" + this.index].pause();
            }
        },
        capturePicture(name) {
            if (this.videoType === "ezopen") {
                this.$refs["ysy" + this.index].capturePicture(name);
            }
        },
        playVideo() {
            if (this.videoType === "flv") {
                this.$refs["flv" + this.index].playVideo();
            } else if (this.videoType === "ezopen") {
                this.$refs["ysy" + this.index].play();
            } else if (this.videoType === "imou") {
                this.$refs["imou" + this.index].play();
            }
        },
        setVolumeVideo(num) {
            if (this.videoType === "flv") {
                this.$refs["flv" + this.index].setVolumeVideo(num);
            } else if (this.videoType === "ezopen") {
                this.$refs["ysy" + this.index].setVolumeVideo(num);
            } else if (this.videoType === "imou") {
                this.$refs["imou" + this.index].setVolumeVideo(num);
            }
        },
        fullScreen() {
            let element = document.getElementById("fullVideoEl_" + this.index);
            if (element.requestFullscreen) {
                element.requestFullscreen();
            } else if (element.mozRequestFullScreen) {
                element.mozRequestFullScreen();
            } else if (element.msRequestFullscreen) {
                element.msRequestFullscreen();
            } else if (element.webkitRequestFullscreen) {
                element.webkitRequestFullScreen();
            }
            if (this.videoType === "ezopen") {
                this.$refs["ysy" + this.index].full();
            } else if (this.videoType === "imou") {
                this.$refs["imou" + this.index].full();
            }
            element.addEventListener("fullscreenchange", (e) => {
                if (!this.isFullFun()) {
                    this.$refs.controlsR.setFullFlag(false);
                    this.v.isFull = false;
                }
            });
            this.v.isFull = true;
        },
        exitScreen() {
            this.v.isFull = false;
        },
        isFullFun() {
            var fullscreenElement =
                document.fullscreenElement ||
                document.mozFullscreenElement ||
                document.webkitFullscreenElement;
            if (fullscreenElement == null) {
                return false;
            } else {
                return true;
            }
        },
        clear() {
            if (this.videoType === "flv") {
                this.$refs["flv" + this.index].clear();
            } else if (this.videoType === "ezopen") {
                return this.$refs["ysy" + this.index].clear().then(() => {
                    this.$refs["ysy" + this.index].clearHtml();
                });
            } else if (this.videoType === "imou") {
                this.$refs["imou" + this.index].clear();
            } else if (this.videoType === "wsflvUrl") {
                this.$refs["wsFlv" + this.index].clear();
            } else if (this.videoType === "hlsUrl") {
                try {
                    this.$refs["m3R" + this.index].clear();
                } catch (error) {}
            }
        },
        clearHtml() {
            try {
                this.$refs["ysy" + this.index].clearHtml();
            } catch (e) {}
        },
    },
};
</script>

<style lang="less" scoped>
#main1 {
    width: 100%;
    height: 100%;
}
.con-video {
    position: relative;
    background: #000;
    .play-info {
        position: absolute;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        width: 100%;
        height: 100%;
        top: 0;
        left: 0;
        z-index: 10;
        .play {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            background: #fff;
            color: #000;
            font-size: 24px;
            cursor: pointer;
            padding-left: 5px;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        .msg {
            color: #fff;
            margin-bottom: 8px;
        }
    }

    .my-loading {
        position: absolute;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        z-index: 10;
        color: #fff;
    }
    .demo-spin-icon-load {
        animation: ani-demo-spin 1s linear infinite;
    }
}
@keyframes ani-demo-spin {
    from {
        transform: rotate(0deg);
    }
    50% {
        transform: rotate(180deg);
    }
    to {
        transform: rotate(360deg);
    }
}
</style>
