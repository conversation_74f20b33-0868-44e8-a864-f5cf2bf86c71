<template>
    <!--  时间类型 - 可选择日期区间  -->
    <div class="time-type">
        <RadioGroup v-model="s.type" @on-change="changeType">
            <Radio :label="1">
                <span>按季节</span>
            </Radio>
            <Radio :label="2">
                <span>按日期</span>
            </Radio>

        </RadioGroup>
        <div class="time-main">
            <div class="time-interval" v-if="s.type == 2">
                <div class="box" v-for="(item, index) in s.week">
                    <DatePicker v-model="item.value" type="daterange" @on-change="changeData" placeholder="选择开始时间和结束时间" format="MM-dd" :editable="false" style="width: 300px" />
                    <div class="add" v-if="index === 0" @click="addDate()">
                        <Icon type="md-add-circle" />
                        <span>添加</span>
                    </div>
                    <div class="del" v-else @click="dalDate(index)">
                        <Icon type="md-remove-circle" @click="delDate(index)" />
                        <span>删除</span>
                    </div>
                </div>
            </div>
            <div class="season" v-if="s.type == 1">
                <!--  季度  -->
                <CheckboxGroup v-model="s.week" @on-change="changeWeek">
                    <Checkbox v-for="(item, index) in $enumeration.seasonList" :label="index + 1" :key="index">{{ item }}</Checkbox>
                </CheckboxGroup>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: 'timeTypeDate',
    props: {
        type: { default: '', type: [Number, String] }, // 时间类型，1季节，2时间区间-日期
        value: { default: '' }
    },
    data() {
        return {
            isChangeTypeIn: false, // 如果是组件内部改变则为true
            isChangeValueIn: false, // 如果是组件内部改变则为true
            s: {
                type: 1,
                week: [] // 选择的
            }
        }
    },
    watch: {
        type() {
            if (this.isChangeTypeIn) {
                this.isChangeTypeIn = false
            } else {
                this.init()
            }
        },
        value() {
            if (this.isChangeValueIn) {
                this.isChangeValueIn = false
            } else {
                this.init()
            }
        }
    },
    mounted() {
        this.init()
    },
    methods: {
        init() {
            if (!this.type) {
                return
            }
            let arr = []
            if (this.value) {
                arr = this.value.split(',')
                if (this.type == 1) {
                    arr = arr.map(n => parseInt(n))
                } else {
                    arr = arr.map(n => {
                        return {
                            value: n.split('~')
                        }
                    })
                }
            }
            this.s.week = arr
            // 时间类型，1季节，2时间区间-日期
            this.s.type = this.type
        },
        handleChange() {
            this.isChangeTypeIn = true
            this.isChangeValueIn = true
            let obj = this.$Util.objClone(this.s)
            if (this.s.type == 1) {
                obj.week.sort((a, b) => {
                    return a - b
                })
            } else {
                let week = []
                obj.week.forEach(item => {
                    if (item.value[0]) {
                        item.value[0] = this.$Util.formatDate(item.value[0], 'MM-DD')
                        item.value[1] = this.$Util.formatDate(item.value[1], 'MM-DD')
                        week.push(item.value.join('~'))
                    }
                })
                obj.week = week
            }
            // console.log(obj, this.s)
            this.$emit('on-change', obj)
        },
        // 星期选择
        changeWeek(val) {
            this.handleChange()
        },
        changeData(val) {
            this.handleChange()
        },
        // 选择时间类型
        changeType(val) {
            this.s.week = []
            if (val == 2) {
                this.s.week.push({
                    value: []
                })
            }
            // console.log(this.s)
            this.handleChange()
        },
        addDate() {
            this.s.week.push({
                value: []
            })
        },
        dalDate(index) {
            this.s.week.splice(index, 1)
        }
    }
}
</script>

<style lang="less" scoped>
.time-main{
    padding: 4px 16px 0px;
    line-height: 24px;
    .time-interval{
        .box{
            display: flex;
            align-items: center;
            margin-bottom: 20px;
            .add,
            .del{
                color: @primary-color;
                line-height: 32px;
                cursor: pointer;
                margin-left: 10px;
                .ivu-icon{
                    margin-right: 8px;
                }
            }
            &:last-child{
                margin-bottom: 0;
            }
        }
    }
}
</style>
