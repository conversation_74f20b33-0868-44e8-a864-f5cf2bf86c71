<template>
<!--  时间类型  -->
    <div class="time-type">
        <RadioGroup v-model="s.type" @on-change="changeType">
            <Radio :label="1">
                <span>时间区间</span>
            </Radio>
            <Radio :label="2">
                <span>季节控制</span>
            </Radio>
        </RadioGroup>
        <div class="time-main">
            <div class="time-interval" v-show="s.type == 1">
                <div class="time-tab">
                    <div class="box" :class="[s.timeType == 1 ? 'on' : '']" @click="changeTimeType(1)">按星期设置</div>
                    <div class="box" :class="[s.timeType == 2 ? 'on' : '']" @click="changeTimeType(2)">按月设置</div>
                </div>
                <div class="week" v-show="s.timeType == 1">
                    <!--  星期  -->
                    <CheckboxGroup v-model="s.week" @on-change="changeWeek">
                        <Checkbox v-for="(item, index) in $enumeration.weekList" :label="index + 1" :key="index">{{ item }}</Checkbox>
                    </CheckboxGroup>
                </div>
                <div class="month" v-show="s.timeType == 2">
                    <!--  月  -->
                    <CheckboxGroup v-model="s.week" @on-change="changeWeek">
                        <div class="box">
                            <Checkbox v-for="n in 10" :label="n" :key="n">{{ n }}</Checkbox>
                        </div>
                        <div class="box">
                            <Checkbox v-for="n in [11, 12, 13, 14, 15, 16, 17, 18, 19, 20]" :label="n" :key="n">{{ n }}</Checkbox>
                        </div>
                        <div class="box">
                            <Checkbox v-for="n in [21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31]" :label="n" :key="n">{{ n }}</Checkbox>
                        </div>
                    </CheckboxGroup>
                </div>
            </div>
            <div class="season" v-show="s.type == 2">
                <!--  季度  -->
                <CheckboxGroup v-model="s.week" @on-change="changeWeek">
                    <Checkbox v-for="(item, index) in $enumeration.seasonList" :label="index + 1" :key="index">{{ item }}</Checkbox>
                </CheckboxGroup>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: 'timeType',
    props: {
        type: { default: '', type: [Number, String] }, // 时间类型，1季节，2时间区间-月，3时间区间-星期
        value: { default: '' }
    },
    data() {
        return {
            isChangeValueInTree: false, // 如果是组件内部改变则为true
            s: {
                type: 1,
                timeType: 1, // 1:按星期设置  2: 按月设置
                week: [] // 选择的
            }
        }
    },
    watch: {
        type() {
            if (this.isChangeValueInTree) {
                this.isChangeValueInTree = false
            } else {
                this.init()
            }
        },
        value() {
            if (this.isChangeValueInTree) {
                this.isChangeValueInTree = false
            } else {
                this.init()
            }
        }
    },
    mounted() {
        this.init()
    },
    methods: {
        init() {
            if (!this.type) {
                return
            }
            let arr = []
            if (this.value) {
                arr = this.value.split(',')
                arr = arr.map(n => parseInt(n))
            }
            this.s.week = arr
            // 时间类型，1季节，2时间区间-月，3时间区间-星期
            if (this.type == 1) {
                this.s.type = 2
            } else {
                this.s.type = 1
                if (this.type == 2) {
                    this.s.timeType = 2
                } else {
                    this.s.timeType = 1
                }
            }
        },
        handleChange() {
            this.isChangeValueInTree = true
            this.s.week.sort((a, b) => {
                return a - b
            })
            this.$emit('on-change', this.s)
        },
        // 星期选择
        changeWeek(val) {
            this.handleChange()
        },
        // 选择时间类型
        changeType() {
            this.s.week = []
            this.handleChange()
        },
        // 选择日期设置
        changeTimeType(timeType) {
            this.s.timeType = timeType
            this.changeType()
        }
    }
}
</script>

<style lang="less" scoped>
.time-main{
    padding: 8px 16px 0;
    .time-interval{
        .time-tab{
            display: inline-flex;
            background: @fill-2;
            border: 1px solid @line-2;
            border-radius: 2px;
            margin-bottom: 6px;
            .box{
                padding: 4px 12px;
                line-height: 22px;
                border-radius: 2px;
                cursor: pointer;
                &.on{
                    background: #fff;
                    color: @primary-color;
                    font-weight: 600;
                }
            }
        }
        .week,
        .month{
            border-radius: 2px;
            background: @fill-1;
            padding: 16px 24px;
        }
        .month{
            .ivu-checkbox-wrapper{
                width: 50px;
            }
            .box{
                margin-bottom: 24px;
                &:last-child{
                    margin-bottom: 0;
                }
            }
        }
    }
    .season{
        line-height: 20px;
    }
}
</style>
