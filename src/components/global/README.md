# sanzhi-traffic-front - 公共组件

此目录存放公共业务组件。

## 列表页面表单组件 BaseForm

|属性|类型|默认值|必填|说明|
|:-|:-|:-|:-|:-|
- |labelWidth|Number|80| 否 | label宽度 |
- |model|Object|是|{}| 表单数据 |
- |handleSubmit|Function|是||表单提交回调

示例代码：
`<BaseForm :model="formData" :label-width="90" @handle-submit="handleSubmit"></BaseForm>`


## 列表table组件 baseTable

|属性|类型|默认值|必填|说明|
|:-|:-|:-|:-|:-|
- |columns|Array|[]| 是 | 列头 |
- |data|Object|[]|否| 默认数据 |
- |url|String|''|是|接口请求地址
- |method|String|否|'post'|接口请求方式
- |height|Number|否||表格高度
- |maxHeight|Number|否||表格最大高度
- |loadDone|Function|否||请求完成后调用的方法
- |showPage|Boolean|否|true|是否显示分页
- |showHeader|Boolean|否|true|是否显示表头
- |pageSize|Number|否|10|分页的默认size
- |pageOpts|Array|否|[5, 10, 15, 20, 30, 50, 100]|表格分页选项

## 标签组件 sTag

|属性|类型|默认值|必填|说明|
|:-|:-|:-|:-|:-|
- |color|String|#00B42A| 是 | 字体颜色 |
- |background|String|#E8FFEA|是| 标签背景颜色 |

示例代码：
`<s-tag background="#FFF7E8" color="#FF7D00">告警</s-tag>`


## 文字提示组件 tooltipAutoShow
文字没有省略时，自动隐藏提示

|属性|类型|默认值|必填|说明|
|:-|:-|:-|:-|:-|
- |content|String| | 是 | 显示的文字 |
- |maxWidth|String| 200 | 否 | 最大宽度 |

示例代码：
`<tooltipAutoShow content="名称" />`

## 金额格式化 Money
|属性|类型|默认值|必填|说明|
|:-|:-|:-|:-|:-|
- |value|String/Number| | 是 | 金额 |

示例代码：
`<Money :value="10000" />`

## 暂无数据 NoData
|属性|类型|默认值|必填|说明|
|:-|:-|:-|:-|:-|
- |value|String| | 否 | 文字 |

示例代码：
`<no-data />`

## 在线、离线 onlineStatus
|属性|类型|默认值|必填|说明|
|:-|:-|:-|:-|:-|
- |value|String/Number| | 是 | 状态 |

示例代码：
`<onlineStatus value="1" />`

## 启用、停用 useStatus
|属性|类型|默认值|必填|说明|
|:-|:-|:-|:-|:-|
- |value|String/Number| | 是 | 状态 |

示例代码：
`<useStatus value="1" />`
