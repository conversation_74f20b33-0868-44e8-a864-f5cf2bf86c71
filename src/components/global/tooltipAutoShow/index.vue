<template>
    <Tooltip @mouseenter="mouseenterFun" :transfer="transfer" :disabled="disabled" :max-width="maxWidth" :placement="placement" :options="options">
        <div :class="{ 'ellipsis-1': true, 'bold-font': bold }">
            <span>
                <slot>{{ content }}</slot>
            </span>
        </div>
        <template #content>
            <slot>{{ content }}</slot>
        </template>
    </Tooltip>
</template>

<script>
export default {
    name: 'TooltipAutoShow',
    props: {
        content: { default: '' },
        maxWidth: { default: '200' },
        bold: {
            default: false
        },
        placement: { default: 'bottom' },
        transfer: { default: true },
        options: { type: [Object]}
    },
    data() {
        return {
            disabled: true
        }
    },
    mounted() {
    },
    methods: {
        mouseenterFun(e) {
            let el = e.toElement
            const range = document.createRange();
            range.setStart(el, 0);
            range.setEnd(el, el.childNodes.length);
            const rangeWidth = Math.round(range.getBoundingClientRect().width);
            // console.log(rangeWidth)
            if (rangeWidth > el.offsetWidth || el.scrollWidth > el.offsetWidth) {
                this.disabled = false;
            }
        },
        createdFun(popperJS) {
            console.log(popperJS)
            setTimeout(() => {
                popperJS.popperJS.update()
            }, 500)
        }
    }
}
</script>

<style lang="less" scoped>
.ivu-tooltip {
    max-width: 100%;
    position: relative;

    /deep/.ivu-tooltip-rel {
        max-width: 100%;
    }
}

.bold-font {
    font-weight: 600;
    font-size: 16px;
}

.ellipsis-1 {
    display: flex;
    align-items: center;

    span {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
}</style>
