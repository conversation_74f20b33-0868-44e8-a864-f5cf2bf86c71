<script lang="ts" setup>
import './index.less';
import {
    h,
    defineEmits,
    defineProps,
    reactive,
    watch,
    ref,
    getCurrentInstance,
    useSlots,
    computed,
} from 'vue';
import { Drawer, Button, FormItem, Form, TimelineItem, Timeline, Icon } from 'view-ui-plus';
import pushStatus from '../pushstatus/pushStatus.vue';
import { useStore } from 'vuex';
import { commonService } from "@/api/commonService";
import { findValue } from '@/utils/tool';
const $store = useStore();
const that = getCurrentInstance()?.appContext.config.globalProperties;
const props = defineProps({
    show: {
        type: Boolean,
        default: false,
    },
    modelId: {
        type: Number,
        default: 1,
    },
    title: {
        default: '告警详情',
    },
    closable: {
        default: true,
    },
    width: {
        default: 320,
    },
    data: {
        default() { return {} },
    },
    showBtn: {
        default: false,
    },
    contentList: {
        default() {
            return [
                { label: '告警编号：', content: '', key: 'code' },
                { label: '设备标识码：', content: '', key: 'bsm' },
                { label: '设备编号：', content: '', key: 'deviceCode' },
                { label: '设备名称：', content: '', key: 'sbmc' },
                { label: '告警类型：', content: '', key: 'alarmType' },
                { label: '告警等级：', content: '', key: 'level' },
                { label: '告警详情：', content: '', key: 'content' },
                { label: '是否推送：', content: '', key: 'pushStatus' },
                { label: '区域位置：', content: '', key: 'areaPath' },
                { label: '告警时间：', content: '', key: 'alarmTime' },
            ];
        },
    },
    // 是否需要工单处理状态
    needProcessHandleStatus: {
        default: false
    }
});

const timeLineList = ref<any>([
    // { label: '处理人：李四', content: '处理完成，更换设备', time: '2022-10-10 18:58:58' },
    // { label: '处理人：张三', content: '工单指派李四', time: '2022-10-14 20:45:32' },
    // { label: '', content: '告警推送，生成处理工单', time: '2022-11-23 19:32:14' },
    // { label: '', content: '', time: '' },
]);
function getTimeList() {
    if (!props.data.code) {
        return
    }

    commonService.getProcessByAlarmNo(props.data.code).then((res: any) => {
        if (res.success) {
            timeLineList.value = (res.data || []).map((item: any) => {
                item.label = item.flowOperator
                item.content = item.flowDesc
                item.time = item.createTime
                return item
            })
            console.log(timeLineList.value)
        }
    })

}
const isShow = ref<Boolean>(false);
watch(
    () => props.show,
    (newVal, _oldVal) => {
        isShow.value = newVal;
    }
);
const handleStatus = computed(() => {
    return props.data.processHandleStatusName || '待处理'
})
function getAlarmValue(id: any, type: any, item: any) {
    switch (id) {
        case 31:
            return $store.getters.dictionary.traffic_bus_alarm_type[type];
        default:
            if (item.alarmTypeName) {
                return item.alarmTypeName
            } else {
                return '告警'
            }
    }
    // switch (id) {
    //     case 1:
    //         return $store.getters.dictionary.manhole_alarm_type[type];
    //     case 2:
    //         return $store.getters.dictionary.street_light_alarm_type[type];
    //     case 4:
    //         return $store.getters.dictionary.auto_irrigate_alarm_type[type];
    //     case 8:
    //         return $store.getters.dictionary.fire_hydrant_alarm_type[type];
    //     case 9:
    //         return $store.getters.dictionary.soil_alarm_type[type];
    //     case 11:
    //         return $store.getters.dictionary.air_alarm_type[type];
    //     case 12:
    //         return $store.getters.dictionary.noise_alarm_type[type];
    //     case 13:
    //         return $store.getters.dictionary.meteorological_alarm_type[type];
    //     case 14:
    //         return $store.getters.dictionary.water_alarm_type[type];
    //     case 15:
    //         return $store.getters.dictionary.ele_fence_alarm_type[type];
    //     case 16:
    //         return $store.getters.dictionary.ponding_alarm_type[type];
    //     case 17:
    //         return $store.getters.dictionary.rainfall_alarm_type[type];
    //     case 18:
    //         return $store.getters.dictionary.lake_alarm_type[type];
    //     case 19:
    //         return item.alarmTypeName;
    //     case 20:
    //         return $store.getters.dictionary.garbage_house__alarm_type[type];
    //     case 22:
    //         return $store.getters.dictionary.energy_consumption_alarm_type[type];
    //     case 24:
    //         return $store.getters.dictionary.smart_net_alarm_type[type];
    //     case 26:
    //         return item.alarmTypeName;
    //     case 29:
    //         return $store.getters.dictionary.traffic_parking_lot_alarm_type[type];
    //     case 31:
    //         return $store.getters.dictionary.traffic_bus_alarm_type[type];
    //     default:
    //         if (item.alarmTypeName) {
    //             return item.alarmTypeName
    //         } else {
    //             return '告警'
    //         }
    // }
}
watch(
    () => props.data,
    (newVal, _oldVal) => {
        props.contentList.forEach((item: any) => {
            if (item.key === 'alarmType') {
                item.content = getAlarmValue(props.modelId, newVal[item.key], newVal);
            } else if (item.key === 'level') {
                item.content = that?.$enumeration.alarmGrade[newVal[item.key]];
            } else if (item.key === 'pushStatus') {
                item.content = that?.$enumeration.isPush[newVal[item.key]];
            } else {
                item.content = getProp(newVal, item.key);
            }
        });
    }
);
function getProp(obj: any, keyStr: any) {
    let keyArr: any = keyStr.split('.');
    for (let i in keyArr) {
        obj = obj[keyArr[i]] || null;
    }
    return obj;
}

const emit: any = defineEmits(['on-cancel', 'on-confirm']);
const slots: Object = useSlots();
const Render = () => {
    return h(
        Drawer,
        {
            ...props,
            modelValue: isShow.value,
            onOnClose: handleCancel,
            onOnVisibleChange: visibleChange
        },
        () => [
            h(
                'div',
                {
                    style: {
                        display: 'flex',
                        paddingBottom: '6px',
                    },
                },
                [
                    h(
                        'div',
                        {
                            style: {
                                width: '24px',
                                height: '24px',
                            },
                        },
                        h('img', {
                            src: require('@/assets/images/icon-基础信息.png'),
                            style: {
                                width: '100%',
                                height: '100%',
                            },
                        })
                    ),
                    h(
                        'h3',
                        {
                            style: {
                                marginLeft: '5px',
                            },
                        },
                        '设备详情'
                    ),
                ]
            ),
            h(
                Form,
                {
                    id: 'Form',
                },
                () =>
                    props.contentList.map((i: any) =>
                        h(
                            FormItem,
                            {
                                label: i.label,
                            },
                            () => {
                                if (i.label === '是否推送：') {
                                    if (i.content === '是') {
                                        return h(pushStatus, { value: 1 });
                                    } else {
                                        return h(pushStatus, { value: 0 });
                                    }
                                } else {
                                    return h('span', {}, i.content);
                                }
                            }
                        )
                    )
            ),
            h(
                'div',
                {
                    style: {
                        display: 'flex',
                        padding: '16px 0',
                    },
                },
                [
                    h(
                        'div',
                        {
                            style: {
                                width: '24px',
                                height: '24px',
                            },
                        },
                        h('img', {
                            src: require('./images/Group 427318821.png'),
                            style: {
                                width: '100%',
                                height: '100%',
                            },
                        })
                    ),
                    h(
                        'h3',
                        {
                            style: {
                                marginLeft: '5px',
                            },
                        },
                        '工单详情'
                    ),
                ]
            ),
            props.needProcessHandleStatus
            ? h(
                'div', {}, '处理状态：' + handleStatus.value
            ) : h('', {}, ''),
            timeLineList.value.length > 0 ? h(
                'div',
                {
                    style: {
                        display: 'flex',
                    },
                },
                [
                    h(
                        'div',
                        {
                            style: {
                                width: '100px',
                            },
                        },
                        ''
                    ),
                    h(
                        Timeline,
                        {
                            id: 'right-box',
                            hideLastNode: true,
                            pending: true,
                        },
                        () =>
                            timeLineList.value.map((i: any) =>
                                h(
                                    TimelineItem,
                                    {
                                        style: {
                                            position: 'relative',
                                        },
                                    },
                                    () => [
                                        h(
                                            'span',
                                            {
                                                class: 'left-box',
                                            },
                                            i.time
                                        ),
                                        h('p', {}, i.content),
                                        h('p', { style: { color: '#798799' } }, i.label),
                                    ]
                                )
                            )
                    ),
                ]
            ) : h(
                'div',
                {
                    class: 'no-data'
                },
                '暂无工单详情'
            ),
            h(
                // 其它内容插槽
                'div',
                {
                    style: {
                        marginTop: '12px',
                    },
                },
                slots.default?.()
            ),
            h(
                'div',
                {
                    class: 'demo-drawer-footer',
                    style: {
                        display: (props.showBtn && 'block') || 'none',
                    },
                },
                [
                    h(
                        Button,
                        {
                            onClick: handleCancel,
                        },
                        () => '取消'
                    ),
                    h(
                        Button,
                        {
                            type: 'primary',
                            onClick: handleConfirm,
                        },
                        () => '确定'
                    ),
                ]
            ),
        ]
    );
};
function handleCancel() {
    emit('on-cancel');
}
function handleConfirm() {
    emit('on-confirm');
}
function visibleChange(val:any) {
    console.info('显示值为===' + val)
    if (val) {
        getTimeList()
    }
}
</script>
<template>
    <Render> </Render>
</template>
