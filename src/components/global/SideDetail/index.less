
/deep/  .ivu-drawer-wrap{
  .ivu-drawer-right{
    .ivu-drawer-content{
       .ivu-drawer-body {
        padding: 0 !important;
      }
    }
  }
}



#Form {
  .ivu-form-item {
    margin-bottom: 0;

    .ivu-form-item-label {
      font-family: 'PingFang SC';
      font-style: normal;
      font-weight: 500;
      font-size: 14px;
      color: #798799;
        float: left;
        padding: 10px 12px 10px 0;
    }

    .ivu-form-item-content {
      display: flex;
      line-height: 34px;
    }
  }
}

.ivu-timeline-item-content {
  span{
    color: #798799;
  }
  p:last-child {
    font-size: 12px;
  }
}

.demo-drawer-footer {
  width: 100%;
  position: sticky;
  bottom: 0;
  left: 0;
  border-top: 1px solid #e8e8e8;
  padding: 16px;
  text-align: right;
  background: #fff;

  .ivu-btn {
    margin-right: 10px;
  }
}

.left-box {
  display: inline-block;
  width: 85px;
  position: absolute;
  top: 0px;
  left: -85px;
}
.no-data{
    text-align: center;
    padding: 4px 0;
}
#right-box {
  .ivu-timeline-item:last-child {
    .ivu-timeline-item-head-blue {
      display: none;
    }
  }

  /deep/ .ivu-timeline-item-tail {
    border-left: 2px solid #E5E6EB;
  }

  .ivu-timeline-item-head-blue {
    width: 6px;
    height: 6px;
    background: #165DFF;
    border-color: #165DFF;
    color: #165DFF;
    margin-left: 4px;

  }


}

// 鼠标悬停关闭按钮样式
.ivu-drawer-close .ivu-icon-ios-close:hover {
  color: #165DFF !important;
}
