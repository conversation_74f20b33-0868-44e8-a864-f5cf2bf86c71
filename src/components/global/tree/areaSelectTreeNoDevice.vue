<template>
    <Poptip
        v-model="showPop"
        placement="bottom-start"
        popper-class="area-sel-pop"
    >
        <div
            class="area-select ivu-select-selection"
            :class="{showPop: hasMouseHoverHead || showPop, dropdown: isDropdownStyle}"
            @mouseenter="hasMouseHoverHead = true"
            @mouseleave="hasMouseHoverHead = false"
        >
            <!-- 单选 -->
            <div class="sel-value" v-if="currentValue">{{ currentValue }}</div>
            <div class="placeholder" v-if="!currentValue">{{ placeholder }}</div>
            <Icon type="ios-arrow-down" class="ivu-select-arrow" v-if="!hasMouseHoverHead || !currentValue" />
            <Icon custom="iconfont icon-close-circle" @click.stop="onClear" v-if="hasMouseHoverHead && currentValue" class="ivu-select-arrow" />
        </div>
        <template #content>
            <div @click.stop>
                <Input v-model="searchVal" search @on-change="changeSearch" clearable placeholder="搜索" />
            </div>
            <tree
                :data="treeList"
                :multiple="false"
                :render="renderTree"
                @on-select-change="handleSelectNode"
            />
        </template>
    </Poptip>
</template>

<script>
import { trace, treeToList } from "@/utils/tool";
import useDebounce from "@/hooks/useDebounce";
export default {
    name: 'AreaSelectTreeNoDevice',
    props: {
        modelValue: { type: [Number, String, Array] },
        isAlarm: { // true：告警树；false：设备/部件树
            default: false
        },
        modelIds: { // 模块ids，告警树需要
            default: [1]
        },
        isDropdownStyle: { default: false }, // 是否是下拉菜单风格（没选择框）
        url:{default:''},
        type: { default: '1' }, // 等于1 设备扩展信息表  不等于1 部件码信息表
        tableName: { type: String }, // 必要，业务数据库表名称
        placeholder: { default: '请选择' },
        maxTagCount: { default: 3 },
        level: { default: 4 }
    },
    data() {
        let value = this.modelValue;
        if (value === null || value === '') {
            value = '';
        }
        return {
            showPop: false,
            isChangeValueInTree: false, // 如果是组件内部改变则为true
            hasMouseHoverHead: false,
            treeList: [],
            currentValue: value,
            searchVal: ''
        }
    },
    computed: {
        valueToArray() {
            return (typeof this.currentValue === 'object') ? this.currentValue : [this.currentValue];
        }
    },
    watch: {
        modelValue(newVal, oldVal) {
            if (this.isChangeValueInTree) {
                this.isChangeValueInTree = false;
            } else {
                let value = newVal;
                if (value === null) {
                    value = '';
                }
                if (!newVal && oldVal) {
                    this.resetData()
                }
                this.currentValue = value;
            }
        }
    },
    mounted() {
        this.getTreeData()
    },
    methods: {
        changeSearch() {
            const text = this.searchVal.toLocaleLowerCase();
            useDebounce(() => {
                const treeList = treeToList(this.treeList);
                const searchTreelist = treeList
                    .filter((k) => k.title.toLocaleLowerCase().includes(text))
                    .map((k) => k.value.split("/"))
                    .flat();
                trace(this.treeList, (d) => {
                    if (searchTreelist.includes(d.title)) {
                        d.expand = true;
                    } else {
                        d.expand = false;
                    }
                });
            });
        },
        clearSearch() {
            console.log(this.showPop)
        },
        onClear() {
            this.isChangeValueInTree = true;
            this.currentValue = '';
            this.resetData()
            this.$emit('update:modelValue', this.currentValue);
            this.$emit('on-change', this.currentValue);
            this.$emit('on-clear');
        },
        renderTree(h, { data }) {
            if (
                this.searchVal &&
                this.searchVal.indexOf('/') < 0 &&
                data.title.toLocaleLowerCase().includes(this.searchVal.toLocaleLowerCase())
            ) {
                let html = data.title.replace(
                    new RegExp(
                        "(" + this.preg_quote(this.searchVal) + ")",
                        "gi"
                    ),
                    "<span class='text-blue'>$1</span>"
                )
                return h('span', {
                    innerHTML: html
                })
            }
            return h('span', { innerHTML: data.title });
        },
        resetData() {
            this._resetData(this.treeList)
            console.log(this.treeList)
        },
        preg_quote(str) {
            return (str + "").replace(/([\\\.\+\*\?\[\^\]\$\(\)\{\}\=\!\<\>\|\:])/g, "\\$1");
        },
        _resetData(data) {
            data.forEach(item => {
                item.selected = false
                item.checked = false
                if (item.children) {
                    this._resetData(item.children)
                }
            })
        },
        handleSelectNode(selectedNodes, currentNode) {
            if (selectedNodes.length) {
                const node = selectedNodes[0];
                this.currentValue = node.value;
            } else {
                this.currentValue = '';
            }
            this.showPop = false;
            console.log(this.currentValue)
            // this.searchVal = this.currentValue
            this.isChangeValueInTree = true;
            this.$emit('update:modelValue', this.currentValue);
            this.$emit('on-change', this.currentValue,this.treeList);
        },
        getTreeData() {
            const params = {
                level: this.level, // 地区层级，1：省，2：市，3：区县，4：街道，5：社区，6：单元网格
                tableName: this.tableName // 表名，绿地:livable_green_land
            }
            this.$request(`/area/getTree`, params, 'POST').then(res => {
                if (res.success) {
                    this.treeList = this.transformData(res.data)
                }
            })
        },
        listToTree(oldArr) {
            oldArr.forEach((element => {
                element.expand = true;
                element.selected = false;
                element.checked = false;
                element.title = element.name;
                element.value = element.name

                let parentId = element.parentId;
                if (parentId) {
                    oldArr.forEach(ele => {
                        if(ele.id == parentId){
                            if (!ele.children) {
                                ele.children = [];
                            }
                            element.value = ele.name + '/' + element.value;
                            ele.children.push(element);
                        }
                    })
                }
            }));
            oldArr = oldArr.filter( ele => ele.parentId == null )
            return oldArr
        },
        // 转换数据格式
        transformData(data) {
            const map = {}; // hash表
            data.forEach(node => {
                node.children = [];
                node.expand = true;
                node.selected = false;
                node.checked = false;
                node.title = node.name;
                node.value = node.name
                map[node.code] = node;
            })

            const result = [];

            data.forEach(element => {
                const parent = map[element.parentCode];
                if (parent) {
                    element.value = parent.value + '/' + element.value
                    parent.children.push(element)
                } else {
                    result.push(element)
                }
            });
            return result;
        }
    }
}
</script>

<style lang="less" scoped>
.ivu-poptip{
    width: 100%;
    display: block;
    /deep/.ivu-poptip-rel{
        width: 100%;
        display: block;
    }
    /deep/.area-sel-pop{
        width: 100%;
        padding-top: 5px;
        .ivu-poptip-arrow{
            display: none;
        }
        .ivu-poptip-body{
            max-height: 240px;
            overflow: auto;
        }
    }
}
.area-select{
    height: 32px;
    .sel-value,
    .placeholder{
        position: relative;
        display: block;
        font-size: 14px;
        overflow: hidden;
        height: 30px;
        line-height: 30px;
        text-overflow: ellipsis;
        white-space: nowrap;
        padding-left: 8px;
        padding-right: 24px;
    }
    .placeholder{
        color: @text-3-1;
    }
    .icon-close-circle{
        color: @text-color;
        font-size: 16px;
    }
    .tab-list{
        display: flex;
        flex-wrap: wrap;
        padding: 2px 24px 2px 4px;
        max-height: 64px;
        overflow: auto;

        .ivu-tag {
            height: 24px;
            line-height: 22px;
            margin-right: 4px;
            position: relative;
        }
    }

    &.showPop{
        .ivu-select-arrow{
            transform: translateY(-50%) rotate(180deg);
        }
    }
    &.multiple{
        min-height: 32px;
        height: auto;
    }
}
.dropdown {
    border-color: transparent;
    cursor: pointer;

        &:hover {
            background: @fill-1;
        }

        .sel-value,
        .placeholder {
            font-weight: 600;
            font-size: 16px;
            color: #1E2A55;

        }
}
</style>
