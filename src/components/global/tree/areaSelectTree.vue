<template>
    <Poptip v-model="showPop" placement="bottom-start" popper-class="area-sel-pop">
        <div class="area-select ivu-select-selection"
            :class="{ showPop: hasMouseHoverHead || showPop, dropdown: isDropdownStyle }"
            @mouseenter="hasMouseHoverHead = true" @mouseleave="hasMouseHoverHead = false">
            <!-- 单选 -->
            <div class="single" v-if="!multiple">
                <div class="sel-value" v-if="currentValue">{{ currentValue }}</div>
                <div class="placeholder" v-if="!currentValue">{{ placeholder }}</div>
            </div>
            <!-- 多选 -->
            <div class="multiple" v-else>
                <div class="sel-value" v-if="currentValue">
                    <Tag class="title-item" v-for="item in currentValue" :key="item.nodeKey" closable
                        @on-close="handleClose" :name="item.title">
                        {{ item.title }}
                    </Tag>
                </div>
                <div class="placeholder" v-if="!currentValue">{{ placeholder }}</div>
            </div>
            <Icon type="ios-arrow-down" class="ivu-select-arrow" v-if="!hasMouseHoverHead || !currentValue" />
            <Icon type="ios-arrow-down" class="ivu-select-arrow" v-if="hasMouseHoverHead && currentValue" />
            <Icon custom="iconfont icon-close-circle" @click.stop="onClear" class="ivu-select-arrow"
                v-if="hasMouseHoverHead && currentValue && clearable" />
        </div>
        <Button v-if="showReset" type="primary" @click.stop="onClear">重置</Button>
        <template #content>
            <div @click.stop>
                <Input v-model="searchVal" search @on-change="changeSearch" placeholder="搜索" />
            </div>
            <tree :check-strictly="true"
                @on-check-change="(selectedNodes, currentNode) => handleSelectNode(selectedNodes, currentNode, 'check')"
                :data="treeList" :show-checkbox="showCheckbox" :multiple="multiple" :render="renderTree"
                @on-select-change="handleSelectNode" />
        </template>
    </Poptip>
</template>

<script>
import { trace, treeToList } from '@/utils/tool';
import { findIndex, getValue, bfs, isEmpty, dfs, findValue, deepClone } from 'wei-util'
import useDebounce from '@/hooks/useDebounce';
import { listToTree } from '@/utils/listToTree'
export default {
    name: 'AreaSelectTree',
    props: {
        modelValue: { type: [Number, String, Array] },
        isAlarm: { // true：告警树；false：设备/部件树
            default: false
        },
        valueKey: { default: 'value' },
        clearCurrentValue: { default: '' },
        modelIds: { // 模块ids，告警树需要
            default: [1]
        },
        multiple: { default: false },
        isDropdownStyle: { default: false }, // 是否是下拉菜单风格（没选择框）
        showCheckbox: { default: false },
        clearable: { default: true },
        url: { default: '' },
        type: { default: '1' }, // 等于1 设备扩展信息表  不等于1 部件码信息表
        tableName: { required: false }, // 必要，业务数据库表名称
        placeholder: { default: '请选择' },
        maxTagCount: { default: 3 },
        data: { default: () => ([]) }, // 数据来源不是请求url的数据
        isRequestUrl: { default: true }, // 数据来源是不是请求url
        hasDevice: { default: false }, // 是否有设备
        isInitSelect: { default: false }, // 初始化时是否默认选中
        showReset: { default: false }, // 是否显示重置按钮
    },
    data() {
        let value = this.modelValue;
        if (value === null || value === '') {
            value = '';
        }
        return {
            showPop: false,
            isChangeValueInTree: false, // 如果是组件内部改变则为true
            hasMouseHoverHead: false,
            treeList: [],
            currentValue: value,
            searchVal: ''
        }
    },
    computed: {
        valueToArray() {
            return (typeof this.currentValue === 'object') ? this.currentValue : [this.currentValue];
        },

    },
    watch: {
        data: {
            deep: true,
            immediate: true,
            handler(newVal) {
                this.treeList = deepClone(newVal || []);
            }
        },
        modelValue: {
            immediate: true,
            handler(newVal, oldVal) {
                if (this.isChangeValueInTree) {
                    this.isChangeValueInTree = false;
                } else {
                    let value = newVal;
                    if (value === null) {
                        value = '';
                    }
                    if (!newVal && oldVal) {
                        this.resetData()
                    }
                    this.currentValue = value;
                    this.showSelectNode()
                }
            }
        }
    },
    mounted() {
        this.isRequestUrl && this.getTreeData()
    },
    methods: {
        showSelectNode() {
            if (!this.currentValue || !this.treeList.length) return
            bfs(this.treeList, 'children', item => {
                console.log(item.value, this.currentValue, 'sadas');
                if (item.value === this.currentValue) {
                    item.checked = true
                    item.selected = true
                }
            })
        },
        changeSearch() {
            const text = this.searchVal.toLocaleLowerCase();
            useDebounce(() => {
                // const treeList = treeToList(this.treeList);
                // const searchTreelist = treeList
                //     .filter((k) => k.title.toLocaleLowerCase().includes(text))
                //     .map((k) => k.value.split("/"))
                //     .flat();
                // trace(this.treeList, (d) => {
                //     if (searchTreelist.includes(d.title)) {
                //         console.log(d);
                //         d.expand = true;
                //     } else {
                //         d.expand = false;
                //     }
                // });
                const allTitle = []
                bfs(this.treeList, 'children', item => {
                    item.expand = false
                    allTitle.push(item.title.toLocaleLowerCase())
                })
                bfs(this.treeList, 'children', item => {
                    if (!text) return item.expand = false
                    function isExpand(obj, lastObj) {
                        if (~obj.title.toLocaleLowerCase().indexOf(text)) {
                            obj.expand = true
                            lastObj && (lastObj.expand = true)
                            return item.expand = true
                        } else {
                            bfs(obj.children, 'children', v => {
                                isExpand(v, obj)
                            })

                        }
                    }
                    isExpand(item)
                    if (!allTitle.some(i => i.includes(text))) {
                        item.expand = false
                    }
                })
            });
        },
        clearSearch() {
            console.log(this.showPop)
        },

        handleClose(event, name) {
            const index = findIndex(this.currentValue, name, 'title')
            bfs(this.treeList, 'children', item => {
                if (item.title === name) {
                    const children = getValue(item, 'children')
                    item.checked = false
                    if (isEmpty(children)) return
                    bfs(children, 'children', item2 => {
                        item2.checked = false
                    })
                }

            })
            this.currentValue.splice(index, 1)
            this.$emit('on-change', this.currentValue);
        },
        onClear() {
            this.isChangeValueInTree = true;
            this.currentValue = this.clearCurrentValue;
            this.resetData()
            this.$emit('update:modelValue', this.currentValue);
            this.$emit('on-change', this.currentValue);
            this.$emit('on-clear');
        },
        renderTree(h, { data }) {
            if (
                this.searchVal &&
                this.searchVal.indexOf('/') < 0 &&
                data.title.toLocaleLowerCase().includes(this.searchVal.toLocaleLowerCase())
            ) {
                let html = data.title.replace(
                    new RegExp(
                        '(' + this.preg_quote(this.searchVal) + ')',
                        'gi'
                    ),
                    "<span class='text-blue'>$1</span>"
                )
                return h('span', {
                    innerHTML: html
                })
            }
            return h('span', { innerHTML: data.title });
        },
        resetData() {
            this._resetData(this.treeList)
            console.log(this.treeList)
        },
        preg_quote(str) {
            return (str + '').replace(/([\\\.\+\*\?\[\^\]\$\(\)\{\}\=\!\<\>\|\:])/g, '\\$1');
        },
        _resetData(data) {
            data.forEach(item => {
                item.selected = false
                item.checked = false
                if (item.children) {
                    this._resetData(item.children)
                }
            })
        },
        handleSelectNode(selectedNodes, currentNode, origin) {
            if (this.multiple) {
                if (!origin) return currentNode.selected = false;
                const type = toString.call(this.currentValue)
                if (type !== '[object Array]') this.currentValue = []
                bfs([currentNode], 'children', item => {
                    item.checked = currentNode.checked
                    const children = getValue(item, 'children')
                    if (isEmpty(children)) return
                    bfs(children, 'children', item2 => {
                        const index = findIndex(this.currentValue, item2.value, 'value')
                        if (index !== -1) this.currentValue.splice(index, 1)
                    })

                })
                const findItem = findValue(this.currentValue, currentNode.forefathersName, 'forefathersName')
                const index = findIndex(this.currentValue, currentNode.value, 'value')
                index !== -1 ? this.currentValue.splice(index, 1) : (currentNode.checked === true && !findItem) ? this.currentValue.push(currentNode) : ''
                // console.log(this.currentValue)
            } else {
                if (selectedNodes.length) {
                    const node = selectedNodes[0];
                    console.log(node[this.valueKey], 'sadas');
                    this.currentValue = node[this.valueKey];
                } else {
                    this.currentValue = '';
                }
            }
            !this.multiple && (this.showPop = false);
            // this.searchVal = this.currentValue
            this.isChangeValueInTree = true;
            this.$emit('update:modelValue', this.currentValue);
            this.$emit('on-change', this.currentValue, currentNode);
        },
        getTreeData() {
            if (this.isAlarm) {
                this.$request(`/deviceAreaTree/getAlarmTree?type=${this.type}&table=${this.tableName}&modelIds=${this.modelIds}`).then(res => {
                    if (res.success) {
                        this.treeList = this.transformData(res.data)
                    }
                })
            } else if (this.hasDevice) {
                this.$request('/area/getTreeAndTableData', { tableName: this.tableName }, 'post').then(res => {
                    if (res.success) {
                        res.data.forEach(item => {
                            item.label = item.name
                            item.title = item.name
                            item.expand = true
                            item.selected = false
                            item.checked = false
                            item._parentCode = item.parentCode
                            if (item.level === 4) {
                                item.parentCode = null
                            }
                        })
                        let list = listToTree(res.data, 'parentCode', 'code')
                        list.forEach(item => {
                            item.value = item.name
                            this.transformValue(item)
                        })
                        if (this.isInitSelect && list.length > 0) {
                            list[0].selected = true
                            this.handleSelectNode([list[0]], list[0])
                        }
                        this.treeList = list
                    }
                })
            } else if (this.url) {
                if (this.type === 'url') {
                    return this.$request(this.url).then(res => {
                        if (res.success) {
                            this.treeList = this.multipleData(res.data)
                        }
                    })
                }
                this.$request(`${this.url}?type=${this.type}`).then(res => {
                    if (res.success) {
                        this.treeList = this.transformData(res.data)
                    }
                })

            } else {
                this.$request(`/deviceAreaTree/getTree?type=${this.type}&table=${this.tableName}`).then(res => {
                    if (res.success) {
                        this.treeList = this.transformData(res.data)
                    }
                })
            }

        },
        sfs(arr, node, callBack) {
            try {
                arr.forEach(item => {
                    callBack(item)
                    item[node] && this.sfs(item[node], node, callBack)
                })
            } catch (error) {
                console.warn('遍历数组出错，请检查数组是否正确');
            }
        },
        multipleData(data) {
            const result = this.transformData(data)
            if (this.multiple) {
                let forefathersName = ''
                this.sfs(result, 'children', item => {
                    if (!~getValue(item, 'value').indexOf('/') && getValue(item, 'type') == 1) forefathersName = getValue(item, 'title')
                    if (isEmpty(getValue(item, 'children'))) {
                        item.forefathersName = forefathersName || getValue(item, 'title')
                    }
                    item.forefathersName = forefathersName
                })
            }
            return result
        },
        transformValue(data) {
            if (data.children) {
                data.children.forEach(item => {
                    item.value = data.value + '/' + item.label
                    this.transformValue(item)
                })
            }
        },
        // 转换数据格式
        transformData(data) {
            const result = [];
            data.forEach((item, index, arr) => {
                item.value = (item.value || '').replace(/@/g, '/')
                let label = item.label || item.name
                const newItem = {
                    ...item,
                    id: item.code,
                    expand: true,
                    selected: item.value === this.currentValue,
                    checked: item.value === this.currentValue,
                    title: label,
                    label: label,
                    value: item.value,
                    children: [],
                };
                if (item.children && item.children.length) {
                    newItem.children = this.transformData(item.children);
                }
                result.push(newItem);
            })
            return result;
        }
    }
}
</script>

<style lang="less" scoped>
.ivu-poptip {
    width: 100%;
    display: block;

    /deep/.ivu-poptip-rel {
        width: 100%;
        display: flex;
        column-gap: 12px;
    }

    /deep/.area-sel-pop {
        width: 100%;
        padding-top: 5px;

        .ivu-poptip-arrow {
            display: none;
        }

        .ivu-poptip-body {
            max-height: 240px;
            overflow: auto;
        }
    }
}

.area-select {
    min-height: 32px;
    flex: 1;

    .sel-value,
    .placeholder {
        position: relative;
        font-size: 14px;
        overflow: hidden;
        height: 32px;
        line-height: 32px;
        text-overflow: ellipsis;
        white-space: nowrap;
        padding-left: 8px;
        padding-right: 24px;

    }

    .multiple {

        .sel-value,
        .placeholder {
            all: unset;
            display: flex;
            flex-wrap: wrap;
            align-items: center;
            column-gap: 5px;
            position: relative;
            font-size: 14px;
            min-height: 32px;
            padding: 0 8px;
            max-height: 120px;
            overflow: hidden;
            overflow-y: auto;
        }
    }

    .placeholder {
        color: @text-3-1;
    }

    .icon-close-circle {
        color: @text-color;
        font-size: 16px;
        background: #fff;
    }

    .tab-list {
        display: flex;
        flex-wrap: wrap;
        padding: 2px 24px 2px 4px;
        max-height: 64px;
        overflow: auto;

        .ivu-tag {
            height: 24px;
            line-height: 22px;
            margin-right: 4px;
            position: relative;
        }
    }

    &.showPop {
        .ivu-select-arrow {
            transform: translateY(-50%) rotate(180deg);
        }
    }

    &.multiple {
        min-height: 32px;
        height: auto;
    }
}

.dropdown {
    border-color: transparent;

    .single {
        cursor: pointer;

        &:hover {
            background: @fill-1;
        }

        .sel-value,
        .placeholder {
            font-weight: 600;
            font-size: 16px;
            color: #1E2A55;

        }
    }
}
</style>
