<template>
    <Dropdown style="cursor:pointer" transfer>
        <Button icon="ios-arrow-down">批量操作</Button>
        <template #list>
            <DropdownItem name="batchExport" @click.native="batchExport">批量导出</DropdownItem>
            <DropdownItem name="batchImport" @click.native="batchImport">批量导入</DropdownItem>
            <DropdownItem name="batchUpdate" @click.native="batchUpdate">批量修改</DropdownItem>
            <DropdownItem name="batchUpdate" @click.native="jumpToTasklist">任务列表</DropdownItem>
        </template>
    </Dropdown>
    <!-- 导入弹窗 -->
    <Upload ref="Upload"></Upload>
    <TaskList ref="TaskList" :business-code="businessCode"></TaskList>
</template>

<script>
// 引入uplpoad组件
import Upload from './components/upload.vue'
import TaskList from './components/asyncTaskList.vue'
export default {
    name: 'AsyncTask',
    // 注册组件
    components: {
        Upload,
        TaskList
    },
    props: {
        businessCode: {
            type: String,
            default: null
        }
    },
    methods: {
        batchExport() {
            console.log('batchExport')
            this.$request('/linkappUser/exports', {}, 'post').then(res => {
                if (res.success) {
                    this.$Message.success('任务提交成功')
                }
            })
        },
        batchImport() {
            this.$refs.Upload.openImportDialog()
        },
        batchUpdate() {
            // 批量修改 实现逻辑与批量导入一致
            this.$Message.success('任务提交成功')
        },
        jumpToTasklist(name) {
            // 跳转到任务列表
            this.$refs.TaskList.setIsShow(true)
        }
    }
}
</script>
