import { Notice } from 'view-ui-plus';
export const moduleList = [
    { code: 'maintenanceInfo', name: '养护台账' },
    { code: 'detectReport', name: '检测报告' },
    { code: 'energyConsumption', name: '能耗记录' },
];
export const notice = {
    // 任务失败
    taskError(task) {
        let moduleName = moduleList.find((item) => item.code === task.businessCode).name;
        let typeName = task.type === 1 ? '导入' : '导出';
        let name = moduleName + typeName;
        let desc = moduleName + typeName + '任务失败：' + task.failedMessage;
        Notice.error({
            title: name + '任务失败',
            desc: desc,
            duration: 0,
        });
    },
    // 任务成功
    taskSuccess(task) {
        let moduleName = moduleList.find((item) => item.code === task.businessCode).name;
        let typeName = task.type === 1 ? '导入' : '导出';
        let name = moduleName + typeName;
        let desc = moduleName + typeName + '任务完成';
        let detail =
            '成功数：' +
            (task.successCount ? task.successCount : '0') +
            '，失败数：' +
            (task.failedCount ? task.failedCount : '0');
        let url = task.type === 1 ? task.failedFileUrl : task.fileUrl;
        Notice.success({
            title: name + '任务完成',
            duration: 0,
            render: (h) => {
                if (task.type === 1) {
                    return h('span', [
                        desc + detail,
                        h(
                            'a',
                            {
                                href: url,
                                target: '_blank',
                            },
                            ' 详情请下载链接'
                        ),
                    ]);
                }
                if (task.type === 2) {
                    return h('span', [
                        desc,
                        h(
                            'a',
                            {
                                href: url,
                                target: '_blank',
                            },
                            ' 详情请下载链接'
                        ),
                    ]);
                }
            },
        });
    },
};

// export default {notice, moduleList};
