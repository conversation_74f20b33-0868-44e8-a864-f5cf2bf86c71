<template>
    <div>
        <Modal v-model="isShow" width="70%">
            <div class="modal">
                <div class="search-container">
                    <Form :model="searchObj" :label-width="90">
                        <Row>
                            <Col span="8">
                            <FormItem label="操作模块">
                                <Spin size="small" fix v-if="!moduleList"></Spin>
                                <Select v-model="searchObj.businessCode" filterable clearable>
                                    <Option v-for="item in moduleList" :value="item.code" :key="item.code">
                                        {{ item.name }}
                                    </Option>
                                </Select>
                            </FormItem>
                            </Col>

                            <Col span="8">
                            <FormItem label="任务类型">
                                <Select v-model="searchObj.type" clearable>
                                    <Option value="1" key="import">
                                        导入
                                    </Option>
                                    <Option value="2" key="export">
                                        导出
                                    </Option>
                                </Select>
                            </FormItem>
                            </Col>
                            <Col span="8">
                            <FormItem label="任务状态">
                                <Select v-model="searchObj.status" clearable>
                                    <Option value="0" key="init">
                                        初始
                                    </Option>
                                    <Option value="1" key="doing">
                                        进行中
                                    </Option>
                                    <Option value="2" key="success">
                                        完成
                                    </Option>
                                    <Option value="3" key="fail">
                                        失败
                                    </Option>
                                </Select>
                            </FormItem>
                            </Col>
                        </Row>
                        <Row>
                            <Col span="8">
                            <Button type="primary" @click="getData">
                                搜索
                            </Button>
                            <Button @click="resetSearch">
                                重置
                            </Button>
                            </Col>
                        </Row>
                    </Form>
                </div>
                <div class="table-container">
                    <base-table :columns="columns" :height="tableHeight" ref="LogTb" size="small"
                        url="/excelTask/selectPage"
>
                        <template #progress="{ row }">
                            <div style="padding: 7px; 5px">
                                <div><span class="f-right">{{ progress(row) }}%</span></div>
                                <Progress :percent="progress(row)" hide-info :stroke-width="5" />
                            </div>
                        </template>
                        <template #action="{ row }">
                            <LinkBtn size="small" @click="download(row)" :disabled="row.failedCount == 0">
                                下载
                            </LinkBtn>
                        </template>
                        <template #businessCode="{ row }">
                            {{ getModuleName(row) }}
                        </template>
                        <!-- <template #tenantCode="{ row }">
                            {{ getTenantIdName(row) }}
                        </template> -->
                    </base-table>
                </div>
            </div>
        </Modal>
    </div>
</template>
<script>
import { moduleList } from '@/components/global/asyncTask/notice' // 任务通知
// 状态：0-初始,1-进行中,2-完成,3-失败
const statusList = ['初始', '进行中', '完成', '失败']
// 操作平台 base-三智管理后台 traffic-园区畅行 livable-生态宜居 safe-安全守护
const platformList = [{ code: 'base', name: '三智管理后台' }, { code: 'traffic', name: '园区畅行' }, { code: 'livable', name: '生态宜居' }, { code: 'safe', name: '安全守护' }]
export default {
    name: 'AsyncTaskList',
    props: {
        businessCode: {
            type: String,
            default: null
        }
    },
    data() {
        return {
            isShow: false,
            searchObj: {
                type: null, // 任务类型 1-导入 2-导出
                status: null, // 任务状态 0-初始,1-进行中,2-完成,3-失败
                tenantCode: this.$store.getters.pageName, // 操作平台 base-三智管理后台 traffic-园区畅行 livable-生态宜居 safe-安全守护
                createUserCode: null, // 用户Id
                businessCode: null// 操作模块 user-用户 role-角色 等
            },
            columns: [
                { title: '任务ID', key: 'id', tooltip: true, width: 100 },
                {
                    title: '任务类型', key: 'type', tooltip: true, width: 100,
                    render: (h, params) => {
                        if (params.row.type == 1) {
                            return h('span', '导入')
                        } else if (params.row.type == 2) {
                            return h('span', '导出')
                        }
                    }
                },
                {
                    title: '任务状态', key: 'status', tooltip: true, width: 100,
                    render: (h, params) => {
                        return h('span', statusList[params.row.status])
                    }
                },
                // 计算进度progress
                { title: '任务进度', slot: 'progress', width: 100 },
                // { title: '预计记录数', key: 'estimateCount', width: 100 },
                { title: '总记录数', key: 'totalCount', width: 100 },
                { title: '成功数', key: 'successCount', width: 100 },
                { title: '失败数', key: 'failedCount', width: 100 },
                { title: '开始时间', key: 'startTime', width: 200 },
                { title: '结束时间', key: 'endTime', width: 200 },
                // { title: '操作平台', slot: 'tenantCode', width: 100},
                { title: '操作模块', slot: 'businessCode', width: 100 },
                // { title: '操作人', key: 'createUserCode', width: 100 },
                { title: '异常说明', key: 'failedMessage', tooltip: true, width: 250 },
                { title: '操作', width: 100, slot: 'action', resizable: true }
            ],
            tableHeight: window.innerHeight - 370,
            moduleList: [],
            data: []
        }
    },
    created() {
        window.addEventListener('resize', () => {
            this.tableHeight = window.innerHeight - 370
        })
        console.log('============' + this.businessCode)
        console.log(this.$store.getters.loginUser)
        console.log('============' + JSON.stringify(this.$store.getters.loginUser))
        this.$store.getters.loginUser && (this.searchObj.createUserCode = this.$store.getters.loginUser.id)
        this.searchObj.businessCode = this.businessCode
        this.getModule()
    },
    methods: {
        // 计算进度progress
        progress(row) {
            return (!row.estimateCount || row.estimateCount == 0) ? 0 : parseInt(((row.successCount + row.failedCount) / row.estimateCount) * 100)
        },
        resetSearch() {
            this.searchObj = {
                type: null, // 任务类型 1-导入 2-导出
                status: null, // 任务状态 0-初始,1-进行中,2-完成,3-失败
                tenantCode: this.$store.getters.pageName, // 操作平台 base-三智管理后台 traffic-园区畅行 livable-生态宜居 safe-安全守护
                // createUserCode: null,// 用户Id
                businessCode: null// 操作模块 user-用户 role-角色 等
            }
            this.getData()
        },
        download(row) {
            console.log(row)
            if (row.status == 2) {
                this.$Message.success('任务已完成')
                if (row.type == 1 && row.failedFileUrl) {
                    window.open(row.failedFileUrl)
                    return
                }
                if (row.type == 2 && row.fileUrl) {
                    window.open(row.fileUrl)
                    return
                }
            } else {
                this.$Message.error('任务未完成')
            }
        },
        getModuleName(row) {
            let obj = this.moduleList.find(item => item.code == row.businessCode)
            return obj ? obj.name : ''
        },
        getTenantIdName(row) {
            console.log(row)
            console.log(platformList)
            let obj = platformList.find(item => item.code == row.tenantCode)
            return obj ? obj.name : ''
        },
        getModule() {
            this.moduleList = moduleList;
        },
        getData() {
            this.$nextTick(() => {
                this.$refs.LogTb.search(this.searchObj)
            })
        },
        setIsShow(status) {
            this.isShow = status
            if (status) {
                this.getData()
            }
        }
    }
}
</script>
<style lang="less" scoped>
.modal {
    margin-top: 20px;
}</style>
