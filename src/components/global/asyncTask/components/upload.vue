<template>
  <Modal v-model="isShowImport" >
      <p slot="header">
          <Icon type="ios-cloud-upload-outline" />
          <span>批量导入</span>
      </p>
      <div class="uploadDiv" style="margin-left:14px; margin-right:14px">
          <Upload
              :show-upload-list="false"
              type="drag"
              ref="uploadDevice"
              action="/api/linkappUser/imports"
              :before-upload="handleUpload"
              :on-success="handleSuccess"
              :on-error="handleError"
              :on-format-error="handleFormatError"
              :format="['xlsx','xls']">
              <div style="padding: 10px ; margin:20px; display: grid;grid-template-columns: 40% 60%;">
                  <div style="margin-left:40px"><Icon type="md-cloud-upload" size="40" style="color: #3262FA"></Icon></div>
                  <div style="text-align: left;">
                  <p style="font-size: 14px;margin-bottom:5px">单击或拖动文件上传</p>
                  <p style="font-size: 12px;color: rgba(0, 0, 0, 0.24);margin-left:10px">仅支持.xlsx格式文件</p>
                  </div>
              </div>
          </Upload>
          <br>
      <br>
      <div style="display: grid;grid-template-columns: 50% 50%;" >
            <div style="text-align: left;">
                <Button icon="ios-cloud-download-outline" @click="downLoadTemplate" style="color:#3262FA">下载导入模板</Button>
            </div>
      </div>

      </div>
  </Modal>
</template>

<script>
export default {
    data() {
        return {
            isShowImport: false
        }
    },
    methods: {
        cancel() {
            this.$emit('getData');
        },
        openImportDialog() {
            this.isShowImport = true;
        },
        downLoadTemplate() {
            window.open('/api/linkappUser/downloadExcelTempplate');
            this.$Message.info('下载成功');
        },
        cancelImport() {
            this.isShowImport = false;
        },
        handleUpload(file) {
            this.isShowImport = false;
            this.$Message.info('任务已提交');
            return true;
        },
        handleSuccess(resp, file, fileList) {

        },
        handleError(error) {
            this.file = null
            this.$Modal.error({
                title: '系统异常',
                content: '批量导入失败，请联系管理员'
            })
        },
        handleFormatError(error) {
            this.$Modal.warning({
                title: '提示',
                content: '导入的文件格式只能为xls,xlsx'
            })
        },

    }
}
</script>

<style lang="less" scoped>
.ivu-upload{
  /deep/ .ivu-upload-drag {
    border: 2px dashed #7690E4;
    // height: 100px;
    // background-image: linear-gradient(to right, #E7C737 0%, #28B35B 50%, transparent 0%);
    // background-size: 14px 1px;
    // background-repeat: repeat-x;
  }
  /deep/.ivu-modal-body {
    padding: 24px;
  }
}
</style>
