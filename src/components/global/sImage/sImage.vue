<template>
    <div class="img-list" v-if="src">
        <div class="img-box" v-for="(item, index) in list" :style="imageStyles" :class="{error: errorInfo[index]}" :key="index">
            <img
                :src="item" alt=""
                @load="handleImageLoad(index)"
                @error="handleImageError(index)" :data-index="index" :style="[fitStyle]">
            <div class="cover">
                <Icon
                    size="18"
                    color="#fff"
                    type="ios-eye-outline"
                    @click="previewImg(item)"
                ></Icon>
            </div>
            <div class="image-error" v-if="errorInfo[index]">
                <img src="@/assets/images/icon-图片占位.png" alt="">
            </div>
        </div>
        <previewModal ref="preR" />
    </div>
</template>

<script>
export default {
    name: 'sImage',
    props: {
        src: { default: '' },
        fit: {
            type: String, // 'fill' | 'contain' | 'cover' | 'none' | 'scale'-down'
            default: 'cover'
        },
        width: {
            type: [String, Number],
            default: ''
        },
        height: {
            type: [String, Number],
            default: ''
        },
        title: { default: '' },
    },
    computed: {
        list() {
            if (this.src) {
                return this.src.split(',')
            } else {
                return []
            }
        },
        fitStyle() {
            const fitContains = ['fill', 'contain', 'cover', 'none', 'scale-down'];
            const { fit } = this;
            return fitContains.includes(fit) ? `object-fit:${fit};` : '';
        },
        imageStyles() {
            return {
                width: typeof this.width === 'number' ? `${this.width}px` : this.width,
                height: typeof this.height === 'number' ? `${this.height}px` : this.height
            };
        }
    },
    data() {
        return {
            errorInfo: {}
        }
    },
    methods: {
        handleImageLoad(index) {
            this.errorInfo[index] = false;
        },
        handleImageError(index) {
            this.errorInfo[index] = true;
        },
        previewImg(item) {
            this.$refs.preR.show({
                url: item,
                name: this.title
            });
        }
    }
}
</script>

<style lang="less" scoped>
.img-list{
    display: inline-flex;
    flex-wrap: wrap;
}
.img-box{
    width: 80px;
    height: 80px;
    border-radius: 4px;
    overflow: hidden;
    background: #fff;
    position: relative;
    margin-right: 8px;
    margin-bottom: 4px;
    margin-top: 4px;
    display: inline-flex;
    img{
        width: 100%;
        height: 100%;
    }
    .cover {
        z-index: 10;
        position: absolute;
        top: 0;
        bottom: 0;
        left: 0;
        right: 0;
        background: rgba(0, 0, 0, 0.4);
        display: none;
        border-radius: 4px;
        .ivu-icon{
            cursor: pointer;
        }
    }
    .image-error{
        position: absolute;
        top: 0;
        bottom: 0;
        left: 0;
        right: 0;
        background: @fill-2;
        display: flex;
        img{
            margin: auto;
            width: 90%;
            height: auto;
        }
    }
    &:not(.error):hover{
        .cover{
            display: flex;
            justify-content: center;
            align-items: center;

        }
    }
}
</style>
