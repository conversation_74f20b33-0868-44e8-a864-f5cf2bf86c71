<template>
    <Select v-model="curValue" @on-change="changeHandler" :clearable="clearable" :placeholder="placeholder" :placement="placement">
        <template v-if="type == 1">
            <Option v-for="item in $enumeration[code]" :value="item.value" :key="item.value">{{ item.name || item.title }}</Option>
        </template>
        <template v-else-if="type == 2">
            <Option v-for="(item, index) in $enumeration[code]" :value="index" :key="index">{{ item }}</Option>
        </template>
        <template v-else-if="type == 3">
            <Option v-for="(item, index) in $enumeration[code]" :value="item" :key="index">{{ item }}</Option>
        </template>
    </Select>
</template>

<script>
// 本地枚举通用选择
export default {
    name: 'enumerateSelect',
    props: {
        type: { default: 1, type: [Number, String] }, // type：2 格式是['离线', '在线']  type:3 格式[12, 16]
        modelValue: { type: [Number, String] },
        code: { default: '', required: true },
        placement: { default: 'bottom-start' },
        clearable: { default: false },
        placeholder: { default: '请选择' }
    },
    emits: ['on-change', 'update:modelValue'],
    data() {
        return {
            curValue: this.modelValue
        }
    },
    watch: {
        modelValue(val) {
            if (val !== this.curValue) {
                this.curValue = val
            }
        }
    },
    methods: {
        changeHandler(val) {
            this.$emit('update:modelValue', val)
            this.$emit('on-change', val)
        }
    }
}
</script>

<style lang="less" scoped>

</style>
