<template>
    <div class="img-card" :class="{horizontal: horizontal}">
        <div class="img" :style="imgWidth ? 'width:' + imgWidth + 'px' : ''">
            <slot name="img">
                <img :src="img" alt="">
            </slot>
        </div>
        <div class="right">
            <div class="name" :style="'font-size:' + labelFontSize + 'px'">{{ label }}</div>
            <div class="num">
                <span :style="fontSize ? 'font-size:' + fontSize + 'px' : ''">{{ value || value === 0 ? value : '--' }}</span>{{ unit || '' }}
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: 'imgCard',
    props: {
        img: { default: '' },
        label: { default: '' },
        value: { default: '' },
        unit: { default: '' },
        imgWidth: { default: '', type: [Number, String] },
        horizontal: { default: false }, // 是否一行显示
        fontSize: { type: [Number, String] },
        labelFontSize: { default: 12 }
    },
    data() {
        return {}
    },
    methods: {}
}
</script>

<style lang="less" scoped>
.img-card{
    display: flex;
    align-items: center;
    position: relative;
    flex: 1;
    padding: 8px 24px;
    .img{
        width: 54px;
        margin-right: 8px;
        img{
            width: 100%;
            display: block;
        }
    }
    .right{
        flex: 1;
        .name{
            font-size: 12px;
            line-height: 20px;
            margin-bottom: 8px;
        }
        .num{
            color: @text-color;
            font-size: 12px;
            line-height: 20px;
            span{
                font-weight: 720;
                font-size: 22px;
                line-height: 24px;
                color: @title-color;
                margin-right: 8px;
            }
        }
    }
    &.horizontal{
        .right{
            display: flex;
            align-items: center;
            .name{
                flex: 1;
                overflow: hidden;
                margin-bottom: 0;
            }
        }
    }
}
</style>
