<template>
    <div class="content-card">
        <Title :level="5" v-if="title">{{ title }}</Title>
        <div class="tab" v-show="showTab">
            <div
                :class="{ active: label === item.name || index == label }"
                @click="handleClick(item)"
                v-for="(item, index) in tabList"
                :key="index"
                v-auth="item.auth"
            >
                {{ item.name }}
            </div>
        </div>
        <slot></slot>
    </div>
</template>

<script>
export default {
    name: "ContentCard",
    props: {
        title: { default: "" },
        showTab: { default: false },
        tabList: { default: () => [] },
        sessionLabel: { default: "" },
    },
    data() {
        return {
            label: "0",
        };
    },
    mounted() {
        if (this.sessionLabel) this.label = this.sessionLabel;
    },
    methods: {
        handleClick(item) {
            this.label = item.name;
            this.$emit("on-change", item.name);
        },
    },
};
</script>

<style lang="less" scoped>
@tab-border: #dcdee2;
@activeBg:#f3f7fb;
.content-card {
    background: #fff;
    padding: 16px 24px;
    // min-height: ~'calc(100vh - 100px)';

    .title {
        font-weight: 500;
        font-size: 16px;
        line-height: 24px;
    }
}

.tab {
    height: 32px;
    display: flex;
    cursor: pointer;
    margin-bottom: 12px;
    column-gap: 12px;

    div {
        text-align: center;
        line-height: 18px;
        font-size: 14px;
        padding: 7px 16px;
        border-radius: 20px;
        background: #ffffff;
        &:hover {
            background-color: @activeBg;
            color: @primary-color;
        }
    }



    .active {
        background-color: @activeBg;
        color: @primary-color;
    }
}
</style>
