<template>
    <div class="detail-card">
        <div class="back" @click="$emit('on-back')" v-if="isBackBtn">
            <slot name="backIcon">
                <Icon custom="iconfont icon-arrow-left" />
            </slot>
        </div>
        <div class="edit">
            <slot name="otherBtn"></slot>
        </div>
        <div v-if="isEditBtn">
            <Button type="primary" size="small" v-if="!isEditFlag && !$route.query.type" class="edit"
                @click="handleEdit(true)">
编辑
</Button>
            <div class="btn-submit" v-if="isEditFlag" :class="isCollapsed ? 'collapsed-btn' : ''">
                <Button @click="handleEdit(false)">取消</Button>
                <Button type="primary" @click="submit" :disabled="subDisabled" :loading="loading">提交</Button>
            </div>
        </div>

        <div class="title-card" :class="[!title ? 'no-title' : '']">
            <slot name="title">
                <div class="title-icon-img" v-if="src">
                    <img :src="src" alt="" style="width: 100%;height: 100%;">
                </div>
                <div class="title-icon" v-else-if="titleIcon">
                    <Icon :custom="'iconfont ' + titleIcon" />
                </div>
                <Title :level="5">{{ title }}</Title>
            </slot>
        </div>
        <slot></slot>
        <div class="add-btn" v-show="isAdd">
            <Button type="primary" @click="$emit('on-submit')">提交</Button>
            <Button @click="$emit('on-cancel')">取消</Button>
        </div>
    </div>
</template>

<script>

import { mapGetters } from 'vuex';
export default {
    name: 'DetailCard',
    props: {
        isAdd: { default: false }, // 是不是新增，是新增页面就显示提交和取消按钮
        isBackBtn: { default: false }, // 是否显示返回按钮
        isEditBtn: { default: false }, // 是否显示编辑按钮
        title: { default: '' },
        titleIcon: { default: '' },
        src: { default: '' },
        loading: { default: false },
        subDisabled: { default: false }
    },
    data() {
        return {
            isEditFlag: false // 是否编辑
        }
    },
    computed: {
        ...mapGetters(['isCollapsed'])
    },
    watch: {
        isEditFlag(newVal, oldVal) {
            this.$emit('on-change', newVal)
        }
    },
    methods: {
        handleEdit(flag) {
            this.isEditFlag = flag
            this.$emit('on-edit', this.isEditFlag)
        },
        submit() {
            this.$emit('on-submit')
        }
    }
}
</script>

<style lang="less" scoped>
.detail-card {
    background: #fff;
    padding: 16px 72px;
    position: relative;
    margin-bottom: 8px;

    .back {
        position: absolute;
        top: 16px;
        left: 24px;
        border: 1px solid #E5E6EB;
        border-radius: 6px;
        width: 24px;
        height: 24px;
        text-align: center;
        cursor: pointer;

        &:hover {
            border-color: #165DFF;
            box-shadow: 0px 1px 6px rgba(61, 103, 175, 0.2);
        }
    }

    .edit {
        position: absolute;
        top: 16px;
        right: 24px;
        cursor: pointer;
    }

    .title-card {
        display: flex;
        align-items: center;
        margin-bottom: 8px;
        padding-bottom: 8px;

        .title-icon {
            background: #6AA1FF;
            border-radius: 2px;
            width: 24px;
            height: 24px;
            margin-right: 8px;
            color: #fff;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .title-icon-img {
            width: 24px;
            height: 24px;
            margin-right: 8px;
        }

        h5 {
            margin-bottom: 0;
        }

        &.no-title {
            margin-bottom: 0;
            padding-bottom: 0;
        }
    }

    .btn-submit {
        &.collapsed-btn {
            width: ~'calc(100vw - 48px)';
        }

        transition: width 200ms;
        position: fixed;
        width:~'calc(100vw - 200px)';
        bottom: 0;
        right: 0;
        background: #fff;
        z-index: 99;
        height: 48px;
        justify-content: flex-end;
        padding: 0 40px;
        box-shadow: 10px -3px 12px rgba(45, 75, 103, 0.1);
    }

    &.edit-card {
        margin-bottom: 48px;
    }

    .add-btn {
        margin-top: 18px;


    }
}</style>
