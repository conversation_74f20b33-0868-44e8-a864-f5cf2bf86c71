.tab-title {
    display: flex;
    justify-content: space-evenly;
    height: 32px;
    font-size: 14px;
    line-height: 32px;
    position: relative;
    .tab-list {
        display: flex;
        align-items: center;
        column-gap: 8px;
        justify-content: space-evenly;
    }
    .tab-item {
        padding: 0 24px;
        border-radius: 100px;
        cursor: pointer;
        color: @normal-color;
        height: 32px;
        display: flex;
        align-items: center;
        column-gap: 8px;

        &:hover {
            background: #F3F7FB;
        }

        &.actived {
            background: #F3F7FB;
            color: @primary-color;
        }
    }

    &.start {
        justify-content: flex-start;
        margin-bottom: 16px;

        .tab-item {
            margin-right: 12px;
        }
    }

    &.center {
        justify-content: center;

        .tab-item {
            margin: 0 8px;
            padding: 0 16px;
        }
    }
}

.tab-body {
    .tab-cont {
        display: none;

        &.actived {
            display: block;
        }
    }
}

.energy-consumption-report-tab {
    margin: 10px 0;
}

.wrap-tab-title {
    flex-wrap: wrap;
    row-gap: 8px;
    .tab-item {
        width: max-content;
        flex-shrink: 0;
    }
}