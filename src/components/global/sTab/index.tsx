import { defineComponent, Slots, ref, watch, getCurrentInstance } from "vue";
import SvgIcon from "@/components/SvgIcon/index.vue";
import "./tab.less";

interface TabContext {
    name: string;
    key: string;
    icon?: string;
    auth?: string;
}
interface propsContent {
    tabList: TabContext[];
}
export default defineComponent({
    name: "sTab",
    props: {
        tabList: {
            type: Array,
            default: () => [{}],
        },
        tabNum: {
            type: Array,
            default: () => [],
        },
        titClass: {
            type: String,
            default: "",
        },
        bodyClass: {
            type: String,
            default: "",
        },
        defaultActive: {
            type: Number,
            default: 0,
        },
        justify: { default: '', type: String }
    },
    emits: ["handleChange"],
    setup(props, ctx) {
        const slots: Slots = ctx.slots;
        const tabList = ref<TabContext[]>()
        const acticveIndex = ref(0);
        const that = getCurrentInstance()?.appContext.config.globalProperties;
        const handleSelect = (i: number,item?:any) => {
            acticveIndex.value = i;
            ctx.emit("handleChange", i,item);
        };
        watch(
            () => props.defaultActive,
            (val) => {
                acticveIndex.value = val;
            },{immediate: true}
        );

        watch(
            () => props.tabList,
            (val) => {
                tabList.value = val as TabContext[];
                let index: any = props.defaultActive;
                let auth: any = []
                for (let i in tabList.value) {
                    let o: any = tabList.value[i];
                    if (o.auth && that?.$Util.checkAuth(o.auth)) {
                        auth.push(parseInt(i))
                    } else if (!o.auth) {
                        auth.push(parseInt(i))
                    }
                }
                if (auth.indexOf(index) < 0 && auth.length > 0) {
                    handleSelect(auth[0])
                }
                // console.log(auth)
            },
            {
                immediate: true,
                deep: true
            }
        );
        return () => (
            <>
                <div class={["tab-title", props.titClass, props.justify]}>
                    <div class="tab-list">
                    {tabList.value?.map((item: TabContext, i: number) =>
                        { return item.auth ? (
                                <div
                                    class={["tab-item", acticveIndex.value == i ? "actived" : ""]}
                                    onClick={() => handleSelect(i,item)}
                                    v-auth={item.auth}
                                >
                                    {item.icon && <icon custom={"iconfont icon-" + item.icon} />}
                                    {item.name}
                                    {(props.tabNum[i] || props.tabNum[i] === 0) ? `(${props.tabNum[i]})` : ''}
                                </div>
                            ) : (
                                <div
                                    class={["tab-item", acticveIndex.value == i ? "actived" : ""]}
                                    onClick={() => handleSelect(i,item)}
                                >
                                    {item.icon && <icon custom={"iconfont icon-" + item.icon} />}
                                    {item.name}
                                    {(props.tabNum[i] || props.tabNum[i] === 0) ? `(${props.tabNum[i]})` : ''}
                                </div>
                            )}

                    )}
                    </div>
                    <>{slots.expand && slots["expand"]!()}</>
                </div>
                <div class={["tab-body", props.bodyClass]}>
                    {tabList.value?.map((item: TabContext, i: number) => (
                        <div class={["tab-cont", acticveIndex.value == i ? "actived" : ""]}>
                            {slots[item.key] ? slots[item.key]!() : ''}
                        </div>
                    ))}
                </div>
            </>
        );
    },
});
