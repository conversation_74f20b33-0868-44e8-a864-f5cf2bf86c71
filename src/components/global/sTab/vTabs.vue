<template>
    <Tabs v-model="activeIndex" type="card" @on-click="handleClick" class="v-tabs-main">
        <TabPane v-for="(item, index) in tabList" :label="item.name" :name="index" :key="index">
            <slot v-if="$slots[item.key]" :name="item.key"></slot>
        </TabPane>
    </Tabs>
</template>

<script>
export default {
    name: 'vTabs',
    props: {
        tabList: { default() { return [] } },
        defaultActive: {
            type: Number,
            default: 0
        }
    },
    data() {
        return {
            activeIndex: this.defaultActive
        }
    },
    watch: {
        defaultActive() {
            this.activeIndex = this.defaultActive
        }
    },
    methods: {
        handleClick(index) {
            this.$emit('on-change', index)
        }
    }
}
</script>

<style lang="less" scoped>

</style>
