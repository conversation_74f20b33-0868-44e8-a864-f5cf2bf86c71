<template>
    <a href="javascript:void(0);" @click="onClick" :class="{ disabled: disabled, small: size === 'small' }" class="btn-action">
        <slot></slot>
    </a>
</template>

<script>
export default {
    name: 'LinkBtn',
    props: {
        disabled: { default: false },
        size: { default: '' }
    },
    emits: ['click'],
    data() {
        return {}
    },
    methods: {
        onClick(e) {
            if (this.disabled) return
            this.$emit('click', e)
        }
    }
}
</script>

<style lang="less" scoped>

.small{
    font-size: 12px;
}
</style>
