import { defineComponent, onMounted, Slots, ref, watch } from "vue";
import { Select, Option } from "view-ui-plus";
import { useStore } from 'vuex'


export default defineComponent({
    name: "dictDropDownSelect",
    props: {
        modelValue: {
            type: [String, Number]
        },
        code: {
            type: String,
            default: '',
        },
        filter: {
            type: Boolean,
            default: false,
        },
        sortFn: {
            type: Function,
            default: (a:string, b:string) => 0
        }
    },
    emits: ["update:modelValue", "initSelect", "onChange"],
    setup(props, ctx) {
        let selectVal = ref<any>('');
        let selectR = ref();
        const $store = useStore()
        let isChangeIn = false
        const selectList = $store.getters.dictionary[props.code] || {}

        // console.log(selectList)
        const handleSelect = ()=>{
            isChangeIn = true
            ctx.emit("update:modelValue", selectVal.value);
            ctx.emit("onChange",selectVal.value, selectList[selectVal.value])
        }
        onMounted(() => {
            if (typeof props.modelValue === 'number') {
                selectVal.value = props.modelValue.toString()
            } else {
                selectVal.value = props.modelValue
            }
        })
        watch(() => props.modelValue, (val) => {
            if (isChangeIn) {
                isChangeIn = false
            } else {
                if (typeof props.modelValue === 'number') {
                    selectVal.value = props.modelValue.toString()
                } else {
                    selectVal.value = props.modelValue
                }
            }
        })
        type SortFn = (a: string, b: string) => number
        const sortFn = props.sortFn as SortFn
        return () => (
            <Select v-model={selectVal.value} filterable={props.filter} ref={selectR} clearable onOnChange={handleSelect}>
                {
                    Object.keys(selectList).sort(sortFn).map((key) => (
                        <Option value={key}>{selectList[key]}</Option>
                    ))
                }
            </Select>
        );
    },
});
