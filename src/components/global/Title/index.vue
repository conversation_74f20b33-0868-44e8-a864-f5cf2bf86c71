<template>
    <div class="title-custom">
        <div class="left">
            <div class="line" :style="background ? `background: ${background}`: ''"></div>
            <slot>
                <div class="title" :style="titleStyle">{{ title }}</div>
            </slot>
        </div>
        <div class="right">
            <slot name="right"></slot>
        </div>
    </div>
</template>

<script>
export default {
    name: 'TitleCustom',
    props: {
        title: { default: '' },
        background: { default: '' },
        color: { default: '' },
        size: { default: '', type: [Number, String] },
        bold: { default: false }
    },
    data() {
        return {}
    },
    computed: {
        titleStyle() {
            let obj = {}
            if (this.color) {
                obj.color = this.color
            }
            if (this.size) {
                obj['font-size'] = this.size + 'px'
            }
            if (this.bold) {
                obj['font-weight'] = 700
            }
            return obj
        }
    },
    methods: {}
}
</script>

<style lang="less" scoped>
.title-custom{
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 8px;
    .left{
        display: flex;
        align-items: center;
        .line{
            width: 2px;
            height: 12px;
            border-radius: 2px;
            background: #1890FF;
            margin-right: 10px;
        }
        .title{
            color: #323B6E;
            font-size: 12px;
            line-height: 20px;
        }
    }
    .right{
        display: flex;
        align-items: center;
    }
}
</style>
