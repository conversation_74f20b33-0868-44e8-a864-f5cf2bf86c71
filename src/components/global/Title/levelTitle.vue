<template>
    <div class="level-title" :class="[active ? 'on' : '']">
        <div class="level">{{ level }}</div>
        <div class="name">{{ title }}</div>
    </div>
</template>

<script>
export default {
    name: 'levelTitle',
    props: {
        level: { default: 1, type: [Number, String]},
        title: { default: '填写监测点位' },
        active: { default: false }
    },
    data() {
        return {}
    },
    methods: {}
}
</script>

<style lang="less" scoped>
.level-title{
    margin-bottom: 24px;
    position: relative;
    .level{
        position: absolute;
        top: -2px;
        left: -32px;
        background: #F2F3F5;
        color: #4E5969;
        height: 28px;
        width: 28px;
        border-radius: 100%;
        text-align: center;
        line-height: 28px;
    }
    .name{
        padding-left: 8px;
        color: #4E5969;
        font-weight: 600;
        font-size: 16px;
        line-height: 24px;
    }
    &.on{
        .name{
            color: #1D2129;
        }
        .level{
            background: #165DFF;
            color: #fff;
        }
    }
}
</style>
