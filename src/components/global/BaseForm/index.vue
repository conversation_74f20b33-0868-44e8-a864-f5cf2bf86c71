<script lang="ts">
import { h, defineComponent, watch, ref, renderSlot, onMounted, withModifiers } from 'vue';
import { Form, FormItem, Button } from 'view-ui-plus'
import exp from 'constants';
export default defineComponent({
    name: 'BaseForm',
    props: {
        labelWidth: {
            default: 90,
            type: Number
        },
        model: {
            type: Object,
            default: () => ({})
        },
        rules: {
            type: Object,
            default: () => ({})
        },
        initFormData: {
            type: Boolean,
            default: false
        }
    },
    emits: ['handleSubmit', 'handleReset'],
    setup(props, ctx) {
        const { slots, expose } = ctx
        const formRef = ref<any>();
        const toggleBtnRef = ref<any>();
        const toggle = ref<Boolean>(false);
        const showToggle = ref<Boolean>(false);
        // 提交表单
        const onSubmitClick = async() => {
            if (props.rules && Object.keys(props.rules).length) {
                const res = await formRef.value?.validate()
                if (!res) {
                    return
                }
            }
            ctx.emit('handleSubmit', props.model);
        }
        // 重置表单
        const onResetClick = async() => {
            if (props.initFormData) {
                ctx.emit('handleReset');
                return;
            }
            await formRef.value?.resetFields();
            onSubmitClick();
        }
        // 监听窗口变化
        window.addEventListener('resize', () => {
            checkFormWidth(window.innerWidth);
        });
        // 监听展开收起
        watch(toggle, (val) => {
            checkFormWidth(window.innerWidth);
        })
        // 判断哪些form需要隐藏
        const checkFormWidth = (width: number) => {
            const len = formRef.value?.$el?.children?.length || 0;
            // 如果form的个数小于5，则隐藏展开按钮
            if (len < 6 || (len < 8 && width >= 992)) {
                showToggle.value = false;
            } else {
                showToggle.value = true;
            }
            const doms = Array.from(formRef.value?.$el?.children || [])
            if (toggle.value) {
                // 全量展开
                doms.forEach((element: any) => {
                    element.style.display = 'block';
                });
            } else {
                let max = 3;
                if (width > 992) {
                    max = 5
                }
                doms.forEach((element: any, index: number) => {
                    if (index >= max && index != doms.length - 1) {
                        element.style.display = 'none';
                    } else {
                        element.style.display = 'block';
                    }

                });

            }

        }
        onMounted(() => {
            checkFormWidth(window.innerWidth);
        })
        expose({ checkFormWidth })
        // 构造dom
        const r = () => [h(Form, {
            class: 'base-form',
            ref: formRef,
            onSubmit: withModifiers(() => {}, [ 'prevent']),
            ...props
        }, () => [
            renderSlot(slots, 'formitem'),
            h(FormItem, {
                class: 'space-box'
            }, () => [h('div', { class: 'occ' })]),
            h(FormItem, {
                class: 'operation-btn'
            }, () => [
                h(Button, {
                    type: 'primary',
                    icon: 'ios-search',
                    onClick: onSubmitClick
                }, () => '查询'),
                h(Button, {
                    icon: 'ios-refresh',
                    onClick: onResetClick
                }, () => '重置'),
                h(Button, {
                    icon: toggle.value ? 'ios-arrow-down' : 'ios-arrow-up',
                    onClick: () => toggle.value = !toggle.value,
                    type: 'text',
                    ref: toggleBtnRef,
                    style: {
                        display: showToggle.value ? 'block' : 'none',
                        padding: 0,
                        width: 'auto'
                    }
                }, () => toggle.value ? '收起' : '展开')
            ])
        ])]
        return r

    }
});
</script>

<style lang="less" scoped>
.base-form {
    display: flex;
    flex-flow: row wrap;
    border-bottom: 1px solid #F2F3F5;
    margin-bottom: 16px;
    position: relative;

    /deep/.ivu-btn-text {
        border: none;
        box-shadow: none;
    }
}

.space-box {
    .occ {
        height: 32px;
    }
}

.operation-btn {
    position: absolute;
    right: 0;
    bottom: 0;

    /deep/.ivu-form-item-content {
        justify-content: flex-end;
    }
}

@media (max-width: 991px) {
    .operation-btn {
        width: 50%;
    }

    /deep/ .ivu-form-item {
        display: block;
        flex: 0 0 50%;
        max-width: 50%;
    }
}

@media (min-width: 992px) {
    .operation-btn {
        width: 33.33333333%;
    }

    /deep/ .ivu-form-item {
        display: block;
        flex: 0 0 33.33333333%;
        max-width: 33.33333333%;
    }
}
</style>
