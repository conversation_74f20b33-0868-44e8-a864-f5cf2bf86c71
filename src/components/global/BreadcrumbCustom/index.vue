<template>
    <Breadcrumb :separator="separator" class="bread-custom">
        <slot></slot>
    </Breadcrumb>
</template>

<script>
export default {
    name: 'BreadcrumbCustom',
    props: {
        separator: { default: '/' }
    },
    data() {
        return {}
    },
    methods: {}
}
</script>

<style lang="less" scoped>
.bread-custom{
    margin-bottom: 8px;
    .bread-icon{
        color: #4E5969;
    }
}
</style>
