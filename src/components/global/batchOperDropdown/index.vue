<template>
    <Dropdown placement="bottom-start" v-auth="auth">
        <Button custom-icon="iconfont icon-caret-down">批量操作</Button>
        <template #list>
            <DropdownMenu style="width: 111px">
                <slot></slot>
            </DropdownMenu>
        </template>
    </Dropdown>
</template>

<script>
/*
* 批量操作
* */
export default {
    name: 'batchOperDropdown',
    props: {
        auth: { default: () => [] }
    },
    data() {
        return {}
    },
    methods: {}
}
</script>

<style lang="less" scoped>

</style>
