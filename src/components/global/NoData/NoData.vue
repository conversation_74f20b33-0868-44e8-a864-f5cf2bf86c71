<template>
    <div class="no-data">
        <div class="img">
            <slot name="img">
                <img src="@/assets/images/no_data.png" alt="">
            </slot>
        </div>
        <slot>
            <p>{{ value }}</p>
        </slot>

    </div>
</template>

<script>
export default {
    name: 'NoData',
    props: {
        value: { default: '暂无数据' }
    },
    data() {
        return {}
    },
    methods: {}
}
</script>

<style lang="less" scoped>
.no-data{
    text-align: center;
    padding: 30px 0;
    color: #86909C;
    .img{
        margin-bottom: 10px;
        img{
            width: 80px;
            display: inline-block;
        }
    }
}
</style>
