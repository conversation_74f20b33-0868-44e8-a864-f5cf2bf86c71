<template>
  <div class="onlineStatus">
      <s-tag v-if="value == 1" background="#E8FFFB" color="#0FC6C2"><i class="iconfont" style="color:#0FC6C2;font-size:10px;margin-right:4px;border: 2px solid #E8FFFB;">&#xe71d;</i>开</s-tag>
      <s-tag v-else-if="value == 0" background="#F2F3F5" color="#4E627E"><i class="iconfont" style="color:#4E627E;font-size:10px;margin-right:4px;border: 2px solid #F2F3F5;">&#xe715;</i>关</s-tag>
  </div>
</template>

<script>
export default {
  name: 'switchStatus',
  props: {
      value: { default: '' }
  },
  data() {
      return {}
  },
  methods: {}
}
</script>

<style lang="less" scoped>
.onlineStatus{
  display: inline-block;
}
</style>
