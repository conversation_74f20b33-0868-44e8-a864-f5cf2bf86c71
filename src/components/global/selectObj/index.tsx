import { defineComponent, Slots, ref, reactive, watch } from "vue";
import { Input, FormItem, Row, Col, Form } from 'view-ui-plus'
import deviceSelect from '@/components/common/deviceSelect/index.vue'
import detailCard from '@/components/global/ContentCard/detailCard.vue'
import { ComponentInfo } from '@/api/fireHydrantService'
import { handleCoordinate, isNullOrEmpty } from '@/utils/tool'
import { enumeration } from '@/config/enumeration'
import Util from "@/utils";
import { useRouter } from "vue-router";
import './index.less'
interface SetupContext {
  slots: Slots;
}
interface Props {
  objInfo: ComponentInfo
}
export default defineComponent({
  name: 'selectObj',
  components: {
    deviceSelect,
    detailCard
  },
  props: {
    labelList: {
      default: () => [
        { label: '权属单位', params: 'ownerEnterpriseName' },
        { label: '坐标', params: 'coordinate' },
        { label: '管理部门', params: 'deptName' },
        { label: '区域位置', params: 'areaPath' },
        { label: '部件状态', params: 'objState' },
        { label: '养护单位', params: 'opEnterpricseName' },
        { label: '联系人', params: 'contactPerson' },
        { label: '联系电话', params: 'contactPhone' },
      ]
    },
    // objInfo: {
    //   default: () => [{}] 
    // },
    list: { //不同场景替换的表单
      type: Array,
      default: () => [{ label: '绿地名称', params: 'objName' }]
    },
    src: {
      default: '' //图标
    },
    modelId: { //绑定设备modelId
      type: String,
      default: '10'
    },
    ruleMessage: {
      type: String,
      default: '请选择绑定设备'
    },
  },
  setup(props, ctx) {
    const router = useRouter()
    const { emit, expose, slots } = ctx
    const step = ref<number>(0)
    const form = ref<any>({})
    // if (Object.keys(props.objInfo).length) {
    //   form.value = props.objInfo
    // }
    const list = ref<Array<any>>([])
    list.value = [...props.list, ...props.labelList]
    // 提交表单得到表单对象
    function addSubmit(objInfo: ComponentInfo) {
      if (!isNullOrEmpty(objInfo)) {
        step.value = 1
        form.value = objInfo
      } else {
        step.value = 0
        form.value = objInfo
      }
      emit('on-change', form.value)
    }
    const ruleMessage = ref<string>('')
    watch(() => form.value, (newVal: ComponentInfo | {}, oldVal: ComponentInfo | {}) => {
      console.log(newVal);
      if (isNullOrEmpty(newVal)) {
        ruleMessage.value = props.ruleMessage
      } else {
        ruleMessage.value = ''
      }
    })
    // 表单验证
    function validate() {
      if (isNullOrEmpty(ruleMessage.value) && !isNullOrEmpty(form.value)) {
        ruleMessage.value = ''
        return true
      } else {
        ruleMessage.value = props.ruleMessage
        return false
      }
    }
    const deviceObj = ref({
      objId:{ required: true},
    })
    expose({ validate })
    return () => (
      <>
        <Form class="device-obj" rules={deviceObj.value}>
          <detailCard src={props.src} isBackBtn={true} onOnBack={router.back} title="选择部件">
            <div class="step">
              <span class={['number', !step.value ? 'active' : '']}>1</span>
              <span class={['title-objId',!step.value ? 'title-objId-active' : '']}>填写部件码</span>
            </div>
            <Row >
              <Col span="8">
                <FormItem  prop="objId" label="设施编号" >
                  <deviceSelect ref="deviceSelect" class="device-select" type="component" modelId={props.modelId} onOnChange={addSubmit} />
                </FormItem>
                <span style={'position:absolute;color:#F53F3F;top:55px'}>{ruleMessage.value}</span>
              </Col>
            </Row>
            <div class="step2">
              <span class={['number', step.value ? 'active' : '']}>2</span>
              <span class={['title-objId',step.value ? 'title-objId-active' : '']}>确定并填写信息</span>
            </div>
            <Row>
              {list.value.map((i: any) => {
                if (i.label === '坐标') {
                  return <Col span="8">
                    <FormItem label={i.label} prop={i.params}>
                      <Input model-value={handleCoordinate(form.value)} disabled placeholder="通过部件码获取"></Input>
                    </FormItem>
                  </Col>
                }
                if (i.label === '部件状态') {
                  return <Col span="8">
                    <FormItem label={i.label} prop={i.params}>
                      <Input model-value={enumeration.objState[form.value[i.params]]} disabled placeholder="通过部件码获取"></Input>
                    </FormItem>
                  </Col>
                }
                if (i.label === '区域位置') {
                  const cloneForm = Util.objClone(form.value)
                  if (form.value.areaPath) {
                    if (form.value.areaPath?.indexOf('@') !== -1) {
                      cloneForm.value.areaPath?.replace(/@/g, '/')
                    }
                  }

                  return <Col span="8">
                    <FormItem label={i.label} prop={i.params}>
                      <Input model-value={cloneForm[i.params]} disabled placeholder="通过部件码获取"></Input>
                    </FormItem>
                  </Col>
                }
                return <Col span="8">
                  <FormItem label={i.label} prop={i.params}>
                    <Input model-value={form.value[i.params]} disabled placeholder="通过部件码获取"></Input>
                  </FormItem>
                </Col>
              })
              }
            </Row>
            {slots.baseInfo ? slots.baseInfo() : ''}
          </detailCard>
        </Form>
      </>
    )
  },
})
