.step,.step2 {
  height: 60px;
  display: flex;
  position: relative;

  .number {
    position: absolute;
    left: -28px;
    height: 28px;
    width: 28px;
    border-radius: 50%;
    font-size: 16px;
    text-align: center;
    line-height: 28px;
    background: #F2F3F5;
    color: #4E5969;
  }

  .active {
    background: @primary-color  ;
    color: #fff;
  }

  .title-objId {
    padding-left: 12px;
    font-size: 16px;
    font-weight: 500;
    line-height: 28px;
    color: #4E5969;
  }
  .title-objId-active{
    color: #1D2129;

  }
}
//  .device-obj{
//   .ivu-row{
//     .ivu-form-item{
//       width: 80% ;
//     }
//   }

// }

/deep/ .ivu-form-item-content .device-select{
  .select-box{
    width: 100%;
  }
  
}


