import moment from "moment";
import { findIndex } from "wei-util";


// 变更记录
const changeRecordData = (oldData, newOld) => {
    let changeRecord = []
  const changeRecordTime =  moment().format('YYYY-MM-DD HH:mm:ss')
    for (const key in oldData) {
        if (oldData[key] != newOld[key]) {
            changeRecord.push({
                key,
                oldValue: oldData[key],
                newValue: newOld[key],
                timeType: newOld.timeType || oldData.timeType,
                changeRecordTime
            })
        }
    }
    return changeRecord
}
const state = {
    droneManageData: [
        {
            id: 1,
            deviceCode: 'DJFE0001',
            deviceName: '无人机1',
            deviceType: 'DJI Mavic 3 Classice',
            brand: '大疆',
            deviceUnit: 'DJI Mavic 3 Pro',
            unit: '园区物业公司',
            maintenancePerson: '张三',
            droneStatus: 0,
            useStatus: 0,
            lastLocation: '114.227587 30.497174 4.3m 123°',
            contactPhone: '132546488484',
            createTime: '2024-11-02 11:00:00',
            lastPushTime: '2023-11-02 11:00:00'
        }, {
            id: 2,
            deviceCode: 'DJFE0002',
            deviceName: '无人机2',
            deviceType: 'DJI Mavic 3 Classice',
            brand: '大疆',
            deviceUnit: 'DJI Air 3',
            unit: '园区物业公司',
            maintenancePerson: '张三',
            droneStatus: 1,
            useStatus: 1,
            lastLocation: '114.227587 30.497174 4.3m 123°',
            contactPhone: '132546488484',
            createTime: '2024-11-03 11:00:00',
            lastPushTime: '2024-11-03 11:00:00'
        }, {
            id: 3,
            deviceCode: 'DJFE0003',
            deviceName: '无人机3',
            deviceType: 'DJI Mavic 3 Classice',
            brand: '大疆',
            unit: '园区物业公司',
            deviceUnit: 'DJI Mini 4 Pro',
            maintenancePerson: '张三',
            droneStatus: 2,
            useStatus: 0,
            lastLocation: '114.227587 30.497174 4.3m 123°',
            contactPhone: '132546488484',
            createTime: '2024-11-04 11:00:00',
            lastPushTime: '2024-11-04 11:00:00'
        }, {
            id: 4,
            deviceCode: 'DJFE0004',
            deviceName: '无人机4',
            deviceType: 'DJI Mavic 3 Classice',
            brand: '大疆',
            deviceUnit: 'DJI Matrice 350 RTK',
            unit: '园区物业公司',
            maintenancePerson: '张三',
            droneStatus: 0,
            useStatus: 1,
            lastLocation: '114.227587 30.497174 4.3m 123°',
            contactPhone: '132546488484',
            createTime: '2024-11-05 11:00:00',
            lastPushTime: '2024-11-05 11:00:00'
        }, {
            id: 5,
            deviceCode: 'DJFE0005',
            deviceName: '无人机5',
            deviceType: 'DJI Mavic 3 Classice',
            brand: '大疆',
            deviceUnit: 'DJI Avata',
            unit: '园区物业公司',
            maintenancePerson: '张三',
            droneStatus: 2,
            useStatus: 1,
            lastLocation: '114.227587 30.497174 4.3m 123°',
            contactPhone: '132546488484',
            createTime: '2024-11-06 11:00:00',
            lastPushTime: '2024-11-06 11:00:00'
        }
    ],
    inspectionPlanData: [
        {
            id: 1,
            planCode: 'NO0001',
            planName: '巡检计划1',
            deviceCode: 'DJFE0001',
            creatTime: '2023-11-03 11:24:39',
            maintenancePerson: '张三',
            planStatus: 0,
            timeType: 2,
            patrolPerson: 1,
            patrolTime: '2023-11-03 11:24:39',
            operationPerson: 2,
            timeValues: '1,3,5',
            startTime: '10:00',
            note:'',
            pointList: [],
            changeRecord:[]
        },
        {
            id: 2,
            planCode: 'NO0002',
            planName: '巡检计划2',
            deviceCode: 'DJFE0002',
            creatTime: '2023-11-03 11:24:39',
            maintenancePerson: '张三',
            planStatus: 1,
            timeType: 3,
            patrolPerson: 3,
            patrolTime: '2023-11-03 11:24:39',
            operationPerson: 4,
            timeValues: '1,3,5',
            startTime: '10:00',
            note:'',
            pointList: [],
            changeRecord:[]
        },
        {
            id: 3,
            planCode: 'NO0003',
            planName: '巡检计划3',
            deviceCode: 'DJFE0003',
            creatTime: '2023-11-03 11:24:39',
            maintenancePerson: '张三',
            planStatus: 0,
            timeType: 3,
            patrolPerson: 5,
            patrolTime: '2023-11-03 11:24:39',
            operationPerson: 6,
            timeValues: '1,3,5,7,8',
            startTime: '10:00',
            pointList: [],
            note:'',
            changeRecord:[]
        },
        {
            id: 4,
            planCode: 'NO0004',
            planName: '巡检计划4',
            deviceCode: 'DJFE0004',
            creatTime: '2023-11-03 11:24:39',
            maintenancePerson: '张三',
            planStatus: 0,
            timeType: 2,
            patrolPerson: 7,
            patrolTime: '2023-11-03 11:24:39',
            operationPerson: 8,
            timeValues: '1,3,5',
            startTime: '10:00',
            pointList: [],
            note:'',
            changeRecord:[]
        },
        {
            id: 5,
            planCode: 'NO0005',
            planName: '巡检计划5',
            deviceCode: 'DJFE0005',
            creatTime: '2023-11-03 11:24:39',
            maintenancePerson: '张三',
            planStatus: 1,
            timeType: 2,
            patrolPerson: 9,
            patrolTime: '2023-11-03 11:24:39',
            operationPerson: 10,
            timeValues: '1,3,5',
            startTime: '10:00',
            note:'',
            pointList: [],
            changeRecord:[]
        },
    ],
    userList: [
        {
            id: 1,
            name: '张明轩',
        },
        {
            id: 2,
            name: '李雨桐',
        },
        {
            id: 3,
            name: '王浩然',
        },
        {
            id: 4,
            name: '刘佳怡',
        },
        {
            id: 5,
            name: '陈志远',
        },
        {
            id: 6,
            name: '赵雅琪',
        },
        {
            id: 7,
            name: '吴宇航',
        },
        {
            id: 8,
            name: '周晓琳',
        },
        {
            id: 9,
            name: '黄俊杰',
        },
        {
            id: 10,
            name: '林思涵',
        },
    ]
}
const mutations = {
    HANDEL_DRONE_MANAGE_DATA:
        (state, data) => {
            state.droneManageData.filter(i => i.id == data.id)[0].useStatus = data.status
        },
    DEL_DRONE_MANAGE_DATA: (state, data) => {
        state.droneManageData = state.droneManageData.filter((item) => !data.includes(item.deviceName));
    },
    EDIT_DRONE_MANAGE_DATA: (state, data) => {
        let index = findIndex(state.droneManageData, data.id, 'id')
        state.droneManageData.splice(index, 1, data)
        console.log(state.droneManageData, 'state.droneManageData');
    },
    FILTER_DRONE_MANAGE_DATA: (state, data) => {
        state.droneManageData = state.droneManageData.filter((item) => {
            if (
                data.deviceCode &&
                data.deviceName &&
                data.droneStatus != undefined
            ) {
                return (
                    item.deviceCode == data.deviceCode &&
                    item.deviceName.includes(data.deviceName) &&
                    item.droneStatus == data.droneStatus
                );
            }
            if (
                data.deviceCode &&
                data.deviceName &&
                data.droneStatus == undefined
            ) {
                return (
                    item.deviceCode == data.deviceCode &&
                    item.deviceName.includes(data.deviceName)
                );
            }
            if (
                data.deviceCode &&
                !data.deviceName &&
                data.droneStatus != undefined
            ) {
                return (
                    item.deviceCode == data.deviceCode &&
                    item.droneStatus == data.droneStatus
                );
            }
            if (
                !data.deviceCode &&
                data.deviceName &&
                data.droneStatus != undefined
            ) {
                return (
                    item.deviceName.includes(data.deviceName) &&
                    item.droneStatus == data.droneStatus
                );
            }
            if (
                !data.deviceCode &&
                !data.deviceName &&
                data.droneStatus != undefined
            ) {
                return item.droneStatus == data.droneStatus;
            }
            if (
                !data.deviceCode &&
                data.deviceName &&
                data.droneStatus == undefined
            ) {
                return item.deviceName.includes(data.deviceName);
            }
            if (
                data.deviceCode &&
                !data.deviceName &&
                data.droneStatus == undefined
            ) {
                return item.deviceCode == data.deviceCode;
            }
        });
    },
    DEL_INSPECTION_PLANE_DATA: (state, data) => {
        // state.inspectionPlanData = state.inspectionPlanData.filter((item) => !data.includes(item.id))
        state.inspectionPlanData.splice(data._index, 1)
    },
    ADD_INSPECTION_PLANE_DATA: (state, data) => {
        state.inspectionPlanData.unshift(data)
    },
    EDIT_INSPECTION_PLANE_DATA: (state, data) => {
        if (data.index) {
            state.inspectionPlanData.splice(data.index, 1, data.params)
        } else {
            let index = findIndex(state.inspectionPlanData, data.id, 'id')
            let changeRecord = changeRecordData(state.inspectionPlanData[index], data)
            data.changeRecord.unshift({
                maintenanceTime: moment().format('YYYY-MM-DD HH:mm:ss'),
                planTitle: data.planName,
                changeRecord: changeRecord
            })
            state.inspectionPlanData.splice(index, 1, data)
        }
    },

}
const actions = {


}
export default {
    namespaced: true,
    state,
    mutations,
    actions
};
