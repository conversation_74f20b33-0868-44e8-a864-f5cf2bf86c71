// import { constantRoutes } from '@/router';
import store from '@/store';
import { listToTree, treeParentMenuName } from '@/utils/listToTree'

const setMenuCode = ['businessLog']

const state = {
    routes: [],
    addRoutes: [],
    firstRouteName: '',
    settingMenu: [], // 设置里面显示的菜单
    currentRoute: {}, // 当前页面路由
    routeCode: {} // 所有路由对应的code
};

const mutations = {
    SET_ROUTES: (state, routes) => {
        state.routes = routes;
    },
    SET_FIREST_ROUTES: (state, name) => {
        state.firstRouteName = name || 'mainHomePage';
    },
    SET_SETTING_MENU: (state, menu) => {
        state.settingMenu = menu;
    },
    SET_CURRENT_ROUTE: (state, route) => {
        state.currentRoute = route;
    },
    SET_ROUTE_CODE: (state, route) => {
        state.routeCode = route;
    },
};

const actions = {
    generateRoutes({ commit }, { authCodes, menu }) {
        return new Promise((resolve) => {
            // 遍历路由文件
            let routes = []
            let settingMenu = []
            menu.forEach(item => {
                if (setMenuCode.indexOf(item.code) >= 0) {
                    settingMenu.push(item)
                } else {
                    routes.push(item)
                }
            })
            // 非后端传递，应为前端所有的（e.g.：详情页没在后端定义）
            let routeCode = {}
            const pageName = store.getters.pageName;
            const routers = require(`@/router/${pageName}`).default.getRoutes();
            routers.map((item) => {
                routeCode[item.path] = item.meta?.authCode
            })
            let accessedRoutes = treeParentMenuName(listToTree(routes))
            accessedRoutes.unshift({
                code: 'mainHomePage',
                level: 1,
                type: 1,
                name: '系统首页',
                iconName: 'icon-apps',
                accessType: 1,
                url: '/mainHomePage'
            })
            console.log(accessedRoutes)
            commit('SET_ROUTES', accessedRoutes);
            commit('SET_SETTING_MENU', settingMenu);
            commit('SET_ROUTE_CODE', routeCode);
            resolve(accessedRoutes);
        });
    },
    setCurrentRoute({ commit }, route) {
        commit('SET_CURRENT_ROUTE', route);
    },
    clear({ commit }) {
        commit('SET_ROUTES', []);
        commit('SET_SETTING_MENU', []);
    }
};

export default {
    namespaced: true,
    state,
    mutations,
    actions
};
