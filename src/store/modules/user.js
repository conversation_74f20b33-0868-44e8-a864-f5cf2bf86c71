import { loginService } from '@/api/loginService';
import { getToken, setToken, removeToken } from '@/utils/auth';


import websocket from '@/utils/websocket'
import { notice } from '@/components/global/asyncTask/notice' // 任务通知
import { setPeerStore } from '@/utils/peer'
const application = {
    traffic: 2,
    livable: 3,
    safe: 4
}
let pageName = location.pathname.split('/')[location.pathname.split('/').length - 1].split('.')[0]
const state = {
    token: getToken(),
    name: '',
    avatar: '',
    introduction: '',
    roles: [],
    loginUser: {},
    authCodes: [],
    pageName: pageName,
    applicationId: application[pageName],
    applicationAuth: [], // 应用权限
    peerStore: '',
    meetingStatus: false,
    meetingInfo: {},
    voiceComName: ''
};

const mutations = {
    SET_TOKEN: (state, token) => {
        state.token = token;
    },
    SET_INTRODUCTION: (state, introduction) => {
        state.introduction = introduction;
    },
    SET_NAME: (state, name) => {
        state.name = name;
    },
    SET_AVATAR: (state, avatar) => {
        state.avatar = avatar;
    },
    SET_ROLES: (state, roles) => {
        state.roles = roles;
    },
    SET_USER: (state, info) => {
        state.loginUser = info;
    },
    SET_AUTH: (state, info) => {
        state.authCodes = info;
    },
    SET_APPLICATION_AUTH: (state, auth) => {
        state.applicationAuth = auth;
    },
    SET_PEER: (state, peer) => {
        // console.log(peer)
        state.peerStore = peer;
    },
    SET_MEETINGSTATUS: (state, status) => {
        state.meetingStatus = status;
    },
    SET_MEETINGINFO: (state, val) => {
        state.meetingInfo = val;
    },
    SET_COMNAME: (state, val) => {
        state.voiceComName = val;
    }
};

const actions = {
    // user login
    login({ commit }, userInfo) {
        const { username, password } = userInfo;
        return new Promise((resolve, reject) => {
            document.cookie = ''
            let url = `?username=${username}&password=${password}`
            loginService
                .login({
                    username: username.trim(),
                    password
                }, url)
                .then(
                    (res) => {
                        if (res.success) {
                            let token = 1
                            commit('SET_TOKEN', token);
                            setToken(token);
                            resolve();
                        } else {
                            reject();
                        }
                    },
                    (err) => {
                        reject();
                    }
                );
        });
    },

    updateMeeting({ commit, state, dispatch }, val) {
        commit('SET_MEETINGSTATUS', val); // 设置当前会议状态

    },
    updateMeetingInfo({ commit, state, dispatch }, val) {
        commit('SET_MEETINGINFO', val); // 当前会议人员
    },
    updateVoiceComName({ commit, state, dispatch }, val) {
        commit('SET_COMNAME', val);
    },
    // get user info
    getInfo({ commit, state, dispatch }) {
        return new Promise((resolve, reject) => {

            loginService.getLoginUser().then(async(res) => {
                if (res && res.success) {
                    // console.log('SET_USER=============' + JSON.stringify(res))
                    commit('SET_ROLES', 'admin');
                    let { roles = [] } = res.data
                    roles = roles.map(k => {
                        if (k.applicationId == 1) { k.sort = 5 } else {
                            k.sort = k.applicationId
                        }
                        return k
                    })
                    roles.sort((a, b) => (a.sort - b.sort))

                    commit('SET_USER', { ...res.data, roles }); // 用户信息
                    if (res.data.token) {
                        commit('SET_TOKEN', res.data.token);
                    }
                    if (process.env.NODE_ENV === 'production') {
                        const peerStore = await setPeerStore()
                        loginService.saveUserPeerId(peerStore.localPeer._id)
                        commit('SET_PEER', peerStore); // 用户信息
                    }

                    // 开始监听是否有视频消息
                    let user = res.data;
                    if (user) {
                        // let topic = '/user/'
                        // let subDestination = user.phone + '/callList'
                        // // console.log(websocket)
                        // websocket.initWebSocket(topic, subDestination, function(data) {
                        //     console.log(subDestination + '===========' + JSON.stringify(data))
                        //     // // 成功通知
                        //     // if (data.status == 2) {
                        //     //     notice.taskSuccess(data)
                        //     // }
                        //     // // 失败通知
                        //     // if (data.status == 3) {
                        //     //     notice.taskFail(data)
                        //     // }
                        // });
                    }
                    resolve({ success: true });
                }
            }, (err) => {
                reject(err);
            });
        });
    },
    getUserCode({ commit, state, dispatch }) {
        return new Promise((resolve, reject) => {
            loginService.getUserCode().then(async(res) => {
                if (res && res.success) {
                    let _privilegeCode = []
                    let _menu = []
                    let data = res.data
                    data.forEach(item => {
                        _privilegeCode.push(item.code)
                        if (item.type === 1) {
                            if (item.accessType == 3) {
                                item.url += '?token=' + state.token
                            }
                            _menu.push(item)
                        }
                    })
                    commit('SET_AUTH', _privilegeCode) // 权限code
                    await dispatch('permission/generateRoutes', { authCodes: _privilegeCode, menu: _menu }, { root: true })
                    resolve({ authCodes: _privilegeCode });
                }
            });
        });
    },
    getApplicationAuth({ commit, state, dispatch }) {
        return new Promise((resolve, reject) => {
            loginService.getApplicationAuth().then(async(res) => {
                if (res && res.success) {
                    let data = res.data || []
                    commit('SET_APPLICATION_AUTH', data) // 权限code
                    resolve();
                }
            });
        });
    },

    // user logout
    logout({ commit, state, dispatch }) {
        return new Promise((resolve, reject) => {
            loginService.logout().then(async(res) => {
                await dispatch('resetToken')
                resolve();
            })
        });
    },

    // remove token
    resetToken({ commit, dispatch }) {
        return new Promise((resolve) => {
            commit('SET_TOKEN', '');
            commit('SET_ROLES', []);
            commit('SET_APPLICATION_AUTH', [])
            dispatch('permission/clear', {}, { root: true })
            removeToken();
            resolve();
        });
    }
};

export default {
    namespaced: true,
    state,
    mutations,
    actions
};
