import { dictionary } from '@/api/dictionary';
const state = {
    dictionary: {},
};
const mutations = {
    SET_DIC: (state, dic) => {
        state.dictionary = dic;
    },
}
const actions = {
    setDictionary({ commit }) {
        return new Promise((resolve, reject) => {
            dictionary.getDictionarys({
                page: { size: -1, current: 1 },
                customQueryParams: {}
            }).then(res => {
                if (res.success) {
                    const { records } = res.data
                    const list = Array.from(new Set([...records.filter(k => k.categoryCode).map(k => k.categoryCode)]))
                    // console.log(list)
                    const dictionaryObj = {}
                    list.forEach(key => {
                        // dictionaryObj[key] = records.filter(k => k.categoryCode == key).map(k=>({name:k.dictDesc,value:k.dictCode}))
                        dictionaryObj[key] = {}
                        records.filter(k => k.categoryCode == key).map(k => {
                            dictionaryObj[key][k.dictCode] = k.dictDesc;
                        })
                    })
                    commit('SET_DIC', dictionaryObj);
                    // console.log(dictionaryObj)
                    resolve()
                }
            }).catch(() => {
                reject()
            })
        })
    }

}
export default {
    namespaced: true,
    state,
    mutations,
    actions
};
