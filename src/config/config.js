import Env from './env'

const themeDark = {
	chart: {
		title: {
			textStyle: {
				color: '#9ba4c5',
				fontFamily: 'Microsoft YaHei'
			},
			subtextStyle: {
				color: '#9ba4c5',
				fontFamily: 'Microsoft YaHei'
			}
		},
		legend: {
			textStyle: {
				color: '#9ba4c5',
				fontFamily: 'Microsoft YaHei'
			}
		},
		grid: {
			show: true,
			borderColor: '#29345b'
		},
		xAxis: {
			nameTextStyle: {
				color: '#9ba4c5'
			},
			axisLine: {
				lineStyle: {
					color: '#29345b'
				}
			},
			axisLabel: {
				color: '#9ba4c5'
			}
		},
		yAxis: {
			nameTextStyle: {
				color: '#9ba4c5'
			},
			axisLine: {
				lineStyle: {
					color: '#29345b'
				}
			},
			splitLine: {
				lineStyle: {
					color: '#29345b'
				}
			},
			axisLabel: {
				color: '#9ba4c5'
			}
		}
	},
	areaColor: {
		type: 'linear',
		x: 0,
		y: 0,
		x2: 0,
		y2: 1,
		colorStops: [{
			offset: 0,
			color: '#000' // 0% 处的颜色
		}, {
			offset: 1,
			color: '#0e153c' // 100% 处的颜色
		}]
	},
	Chartprimary: '#9ba4c5',
	primary: '#27aac1',
	maskColor: 'rgba(23,29,64, 0.8)'
}
// #495060
const themeLight = {
	chart: {
		title: {
			textStyle: {
				color: '#333',
				fontFamily: 'Microsoft YaHei'
			},
			subtextStyle: {
				color: '#aaa',
				fontFamily: 'Microsoft YaHei'
			}
		},
		legend: {
			textStyle: {
				color: '#333',
				fontFamily: 'Microsoft YaHei'
			}
		},
		grid: {
			show: true,
			borderColor: '#ccc'
		},
		xAxis: {
			nameTextStyle: {
				color: '#333'
			},
			axisLine: {
				lineStyle: {
					color: '#ccc'
				}
			},
			axisLabel: {
				color: '#333'
			}
		},
		yAxis: {
			nameTextStyle: {
				color: '#333'
			},
			axisLine: {
				lineStyle: {
					color: '#ccc'
				}
			},
			splitLine: {
				lineStyle: {
					color: '#ccc'
				}
			},
			axisLabel: {
				color: '#333'
			}
		}
	},
	areaColor: {
		type: 'linear',
		x: 0,
		y: 0,
		x2: 0,
		y2: 1,
		colorStops: [{
			offset: 0,
			color: '#25afc2' // 0% 处的颜色
		}, {
			offset: 1,
			color: '#fff' // 100% 处的颜色
		}]
	},
	Chartprimary: '#333',
	primary: '#2d8cf0',
	maskColor: 'rgba(255, 255, 255, 0.8)'
}
const config = {
	env: Env,
	themeDark,
	themeLight,
	// devProject: 'emp', // 此处是为了配置后台项目访问名，在开发模式下为了便于调试，接口配置，一定配置。
	devProject: '/api', // 此处是为了配置后台项目访问名，在开发模式下为了便于调试，接口配置，一定配置。
	proProject: '/api' // 此处是为了配置生产模式后台项目访问名
}
export default config
