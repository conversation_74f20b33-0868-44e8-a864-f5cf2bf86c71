export const enumeration = {
    roleScopeList: [
        { value: '1', name: '园区畅行' }, // 角色范围
        { value: '2', name: '生态宜居' },
        { value: '3', name: '安全守护' },
        { value: '4', name: '三智管理后台' }
    ],
    // 智慧道桥养护设备类型
    bridgeMaintainDeviceType: [
        { value: 2, name: '道路' },
        { value: 3, name: '桥梁' }
    ],
    // 智慧道桥检测类型
    bridgeTestType: [
        { value: 1, name: '经常性检测' },
        { value: 2, name: '常规定期检测' },
        { value: 3, name: '结构定期检测' },
        { value: 4, name: '动静载试验' },
        { value: 5, name: '其他特殊检测' }
    ],
    // 智慧桥梁等级
    bridgeLevel: ['', '特大桥', '大桥', '中桥', '小桥'],
    // 智慧道路等级
    roadGradeLevel: ['', '快速路', '主干路', '次干路', '支路', '区间道路', '高速'],
    // 路面结构：
    roadStructure: [
        { key: '1', title: '沥青路面' },
        { key: '2', title: '水泥混凝土路面' },
        { key: '3', title: '砌块路面' }
    ],
    // 道路等级
    roadGrade: [
        { key: '1', title: '快速路' },
        { key: '2', title: '主干路' },
        { key: '3', title: '次干路' },
        { key: '4', title: '支路' },
        { key: '5', title: '区间道路' },
        { key: '6', title: '高速' },
    ],
    // 养护等级：
    maintenanceLevel: [
        { key: '1', title: '小修保养' },
        { key: '2', title: '中修保养' },
        { key: '3', title: '大修保养' },
        { key: '4', title: '改扩建' }
    ],
    // 部件状态
    objState: [
        '完好', '破损', '丢失', '废弃', '移除'
    ],
    // 桥梁等级
    bridgeGrade: [
        { key: '1', title: '特大桥' },
        { key: '2', title: '大桥' },
        { key: '3', title: '中桥' },
        { key: '4', title: '小桥' }
    ],
    // 警告告警是否推送
    isPush: ['否', '是'],
    // 告警等级
    alarmGrade: ['', '紧急告警', '重要告警', '次要告警', '提示告警'],
    // 井盖告警类型
    alarmType: ['', '井盖状态异常', '井盖设备离线', '井盖设备低电量告警'],
    // 消防栓告警类型
    fireHydrantAlertAlarmType: ['', '消防栓水压不足', '消防栓发生倾斜', '消防栓设备离线', '消防栓设备低电量告警'],
    // 水质监测告警类型
    waterQualityAlarmType: ['', '含氧量偏低', 'pH过高'],
    // 气象环境告警类型
    meteorologicalAlarmType: ['', '风速过高', '雨量过大'],
    // 噪声环境告警类型
    noiseAlarmType: ['', '分贝过大', '分贝过小'],
    // 空气质量告警类型
    airQualityAlarmType: ['', 'PM2.5过高', 'CO2过高'],
    // 设备状志
    deviceState: [
        { value: 0, name: '未使用' },
        { value: 1, name: '正常' },
        { value: 2, name: '试运行' },
        { value: 3, name: '故障' },
        { value: 4, name: '维修' },
        { value: 5, name: '报废' }
    ],
    // 设备在线状态
    deviceOnlineState: ['离线', '在线'],
    energyDeviceOnlineState: ['断闸', '通闸'],
    // 设备使用状态
    deviceUesState: ['停用', '启用'],
    deviceStateList: ['未使用', '正常', '试运行', '故障', '维修', '报废'],
    // 亮灯状态
    streetLightState: ['关灯', '开灯'],
    // 照明类型
    lightingType: [
        { value: 1, name: '单灯' },
        { value: 2, name: '双灯' },
        { value: 3, name: '灯带' }
    ],
    // 照明控制类型
    lightingControlType: [
        { value: 1, name: '开灯' },
        { value: 2, name: '关灯' },
        { value: 3, name: '调光' }
    ],
    // 灯带控制类型
    lightBeltControlType: [
        { value: 1, name: '开灯' },
        { value: 2, name: '关灯' },
        { value: 3, name: '调色' }
    ],
    // 养护类型
    lightMaintenanceType: [
        { value: 1, name: '临时养护' },
        { value: 2, name: '日常养护' }
    ],
    // 设备在线状态
    onlineStatus: [{ title: '离线', color: '#165DFF' }, { title: '在线', color: '#00B42A' }],
    // 使用状态
    useStatus: ['停用', '启用'],
    // 开关状态
    switchState: ['关闭', '开启'],
    controlSource: ['单控', '分组控制', '自动控制', '批量控制'],
    weekList: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
    seasonList: ['春', '夏', '秋', '冬'],
    // 设备类型列表
    modelTypeList: [
        '',
        '井盖设备管理', // 1
        '道路管理', // 2
        '桥梁管理', // 3
        '灌溉电磁阀控制管理',
        '灌溉电磁阀管理',
        '智慧座椅管理',
        '智慧路灯',
        '消防栓管理',
        '园林土壤设备',
        '绿地管理', // 10
        '空气质量',
        '噪声环境',
        '气象环境',
        '水质监测',
        '电子围栏',
        '积水监测', // 16
        '雨情监测', // 17
        '湖渠监测', // 18
    ],
    // 车辆状态类型
    carStatusList: [
        '停用',
        '启用'
    ],
    // 车辆类型
    carCategoryList: [
        '',
        '洒水车',
        '吸污车',
        '吸粪车',
        '垃圾车',
        '高压清洗车',
        '扫路车',
        '其他'
    ],
    // 电子围栏告警类型
    eleAlarmTypeList: ['车辆进入报警', '车辆驶出报警', '定位设备离线', '定位设备故障'],
    garbageDisposalMethod: ['餐厨垃圾', '垃圾运收'],
    gender: ['女', '男'],
    // 算术符
    arithmeticChar: ['>', '<', '>=', '<=', '==', '='],
    // 健康状态
    healthStatus: [
        '',
        '健康',
        '良好',
        '一般',
        '较差'
    ],
    teamType: ['', '日常运维', '专项运维'],
    // 专家类别 1-事故灾难类专家 2-公共卫生类专家 3-社会安全类专家 4-综合类专家 5-其它专家
    expertCategory: [
        '事故灾难类专家',
        '公共卫生类专家',
        '社会安全类专家',
        '综合类专家',
        '其它专家'
    ],
    // 最高学历 1-博士、2-硕士、3-本科、4-大专、5-中专
    highestDegree: [
        '博士',
        '硕士',
        '本科',
        '大专',
        '中专'
    ],
    // 挑拨方向
    provokeDirection: ['入库', '出库'],
    // 停车场类型
    wisdomParkingType: [
        { value: 1, name: '封闭型停车场' }
    ],
    // 车位状态
    parkingStatus: ['无车', '有车'],
    // 车位关联
    parkingCorrelation: ['未关联', '已关联'],
    // 车辆颜色
    carColor: [
        { value: 1, name: '白色' },
        { value: 2, name: '黑色' },
        { value: 3, name: '银色' },
        { value: 4, name: '红色' },
        { value: 5, name: '蓝色' },
        { value: 6, name: '金色' },
        { value: 7, name: '灰色' },
        { value: 8, name: '绿色' },
        { value: 9, name: '棕色' },
        { value: 10, name: '粉色' },
        { value: 11, name: '其它' }
    ],
    // 车辆类型
    carType: [
        { value: 1, name: '内部车辆' },
        { value: 2, name: '临时车辆' }
    ],
    // 费用类型 临停收费、月卡续费、消费、充值
    parkingFeeType: ['临停收费', '月卡续费', '消费', '充值'],
    // 付款方式
    paymentWay: ['线下付款', '微信付款', '支付宝付款'],
    // 账单状态
    billStatus: ['未付款', '已付款'],
    // 泵站类型
    pumpStationType: ['污水泵站', '雨水泵站', '合流泵站', '供水泵站', '其他'],
    // 水泵类型
    waterPumpType: ['离心泵', '轴流泵', '混流泵', '潜水电泵', '其他'],
    energyConsumptionType: ['', '电', '水', '气'],
    energyConsumptionTypeUnit: ['', 'kwh', 't', 'm³'],
    // 时间类型
    timeType: ['时间区间-日期', '指定日期-按星期', '指定日期-按月'],
    // 公交站点状态
    busList: ['完好', '破损', '丢失', '废弃', '移除'],
    // 逻辑符
    logicCode: ['&&', '||'],
    // 巡检状态 1未巡检2巡检完成3巡检异常
    inspectionStatus: ['未巡检', '巡检完成', '巡检异常'],
    // 停车时长类型，0: <30min、1:30-60min、2:1-3h、3:3-6h、4:6-10h、5:>10h
    parkingTimeType: ['<30min', '30-60min', '1-3h', '3-6h', '6-10h', '>10h'],
    // 水质分类
    waterQualityClassify: {
        1: 'I类',
        2: 'II类',
        3: 'III类',
        4: 'IV类',
        5: 'V类',
        6: '劣五类'
    },
    // 告警工单状态
    alarmWorkOrderStatus: {
        0: { name: '工单生成', color: '#249EFF' },
        1: { name: '待派遣', color: '#9391FF' },
        2: { name: '处置中', color: '#21CCFF' },
        3: { name: '待审核', color: '#0E42D2' },
        4: { name: '返工中', color: '#FF649C' },
        5: { name: '已结办', color: '#FE7B32' },
        6: { name: '已关闭', color: '#FFBA09' },
        7: { name: '整改中', color: '#F366FF' },
        8: { name: '已整改', color: '#86DF6C' }
    },
    // 值班类型
    dutyType: ['', '临时值班', '长期值班'],

    // 无人机状态
    droneStatus: ['飞行中', '异常', '待命中'],
    // 实时路况
    realtimeTrafficStatus: [
        { name: '拥堵', value: 4, num: 4, color: '#B20909', background: '#FFCABF' },
        { name: '较堵', value: 3, num: 2, color: '#F53F3F', background: '#FFECE8' },
        { name: '一般', value: 2, num: 1.5, color: '#F7BA1E', background: '#FFFCE8' },
        { name: '畅通', value: 1, num: 0, color: '#00B42A', background: '#E8FFEA' }
    ],
    // 无人机预警类型
    droneAlarmTypes: [
        '突发事件', '异常事件'
    ],
    // 节目类型
    programType: ['简易节目', '高级节目', '复杂节目', '网页节目'],
    // 消息类型
    messageType: [
        { name: '告警事件', value: 0 },
        { name: '工作任务', value: 1 },
        { name: '消息提醒', value: 2 }
    ],
    // 智慧出行 - 对齐方式
    smartTravelImgAlign: [
        { name: '左对齐', value: 1 },
        { name: '右对齐', value: 2 }
    ],
    // 智慧出行 - 文字对齐方式
    smartTravelWordAlign: [
        { name: '居中', value: 1, align: 'center', justify: 'center' },
        { name: '左对齐', value: 2, align: 'flex-start', justify: 'flex-start' },
        { name: '右对齐', value: 3, align: 'flex-start', justify: 'flex-end' },
        { name: '居中居左对齐', value: 4, align: 'center', justify: 'flex-start' },
        { name: '居中居右对齐', value: 5, align: 'center', justify: 'flex-end' }
    ],
    // 智慧出行 - 显示效果
    smartTravelWordDisplay: [
        { name: '立即显示', value: 1 },
        { name: '连续左移', value: 2 }
    ],
    // 智慧出行 - 字体大小
    smartTravelWordSize: [16, 24, 32, 48],
    // 智慧出行 - 字体样式
    smartTravelWordStyle: [
        { name: '宋体', value: 1, text: 'SimSun' }
    ],
    // 智慧出行 - 字体间距
    smartTravelWordSpace: [0, 1, 2],
    // 智慧出行 - 字体颜色
    smartTravelFontColor: [
        { name: '黄色', value: 1, color: 'yellow' },
        { name: '红色', value: 2, color: 'red' },
        { name: '绿色', value: 3, color: '#0ece0e' }
    ],
    // 智慧出行 - 发布渠道
    smartTravelReleaseChannel: [
        { name: '诱导屏', value: 1 },
        { name: 'IOC', value: 2 },
        { name: '公众app', value: 3 }
    ],
    // 智慧出行 - 审核状态
    smartTravelReviewStatus: [
        { name: '未审核', value: 0 },
        { name: '通过', value: 1 },
        { name: '作废', value: 2 }
    ],
    qualityGradeList: [
        { name: 'Ⅰ类', value: '1' },
        { name: 'Ⅱ类', value: '2' },
        { name: 'Ⅲ类', value: '3' },
        { name: 'Ⅳ类', value: '4' },
        { name: 'Ⅴ类', value: '5' }
    ],
    wxdjList: [
        { name: '一级', value: '1' },
        { name: '二级', value: '2' },
        { name: '三级', value: '3' },
        { name: '四级', value: '4' },
        { name: '五级', value: '5' },
    ],
    // 开关计划 - 通电断电状态
    switchPlanStatus: [
        { name: '通电', value: '1' },
        { name: '断电', value: '0' },
    ],
    // 告警等级
    alarmLevel: [
        { value: 1, name: '紧急告警' },
        { value: 2, name: '重要告警' },
        { value: 3, name: '次要告警' },
        { value: 4, name: '提示告警' }
    ],
    // 工单状态
    tickets: [
        { value: 0, name: '工单生成' },
        { value: 1, name: '待派遣' },
        { value: 2, name: '处置中' },
        { value: 3, name: '待审核' },
        { value: 4, name: '返工中' },
        { value: 5, name: '已结办' },
        { value: 6, name: '已关闭' },
        { value: 7, name: '整改中' },
        { value: 8, name: '已整改' }
    ],
    // 工单处理状态
    processHandleStatus: [
        { value: 1, name: '待处理' },
        { value: 2, name: '处理中' },
        { value: 3, name: '已处理' }
    ],
    // 巡逻状态
    patrolStatus: [
        { value: 1, name: '待巡逻' },
        { value: 2, name: '巡逻中' },
        { value: 3, name: '巡逻完成' },
    ],
    // 处理状态
    handleStatus: ['待处理', '已处理'],
    // 能耗告警处理状态
    energyConsumptionHandleStatus: [
        {
            value: 1,
            name: '待处理'
        },
        {
            value: 2,
            name: '已完成'
        },
        {
            value: 3,
            name: '处理中'
        }
    ]
}
