[{"codeValue": "-1", "codeName": "事件类型", "fieldOrder": 1, "parentCodeValue": "", "remark": null, "children": [{"codeValue": "01", "codeName": "市政公用", "fieldOrder": 1, "parentCodeValue": "-1", "remark": null, "children": [{"codeValue": "01-01", "codeName": "其它", "fieldOrder": 1, "parentCodeValue": "01", "remark": null, "children": [{"codeValue": "01-01-01", "codeName": "其他市政设施设备养护异常问题", "fieldOrder": 1, "parentCodeValue": "01-01", "remark": null, "children": null}]}, {"codeValue": "01-02", "codeName": "道路管养", "fieldOrder": 2, "parentCodeValue": "01", "remark": null, "children": [{"codeValue": "01-02-01", "codeName": "车行道路面裂缝、坑槽、车辙等破损需修复", "fieldOrder": 1, "parentCodeValue": "01-02", "remark": null, "children": null}, {"codeValue": "01-02-02", "codeName": "车行道井盖、护栏移位、破损需修复", "fieldOrder": 2, "parentCodeValue": "01-02", "remark": null, "children": null}, {"codeValue": "01-02-03", "codeName": "交通标志牌立柱缺失、破损需修复", "fieldOrder": 3, "parentCodeValue": "01-02", "remark": null, "children": null}, {"codeValue": "01-02-04", "codeName": "交通标志牌污浊、遮挡、褪色需维护", "fieldOrder": 4, "parentCodeValue": "01-02", "remark": null, "children": null}, {"codeValue": "01-02-05", "codeName": "标线污浊、褪色、模糊需维护", "fieldOrder": 5, "parentCodeValue": "01-02", "remark": null, "children": null}, {"codeValue": "01-02-06", "codeName": "标线错误、缺失、损坏需修复", "fieldOrder": 6, "parentCodeValue": "01-02", "remark": null, "children": null}, {"codeValue": "01-02-07", "codeName": "人行道铺装、缘石缺失、破损需修复", "fieldOrder": 7, "parentCodeValue": "01-02", "remark": null, "children": null}, {"codeValue": "01-02-08", "codeName": "人行栏杆污浊、移位、破损需修复", "fieldOrder": 8, "parentCodeValue": "01-02", "remark": null, "children": null}, {"codeValue": "01-02-09", "codeName": "道路其他设施需维护", "fieldOrder": 9, "parentCodeValue": "01-02", "remark": null, "children": null}, {"codeValue": "01-02-10", "codeName": "隧道结构破损需修复", "fieldOrder": 10, "parentCodeValue": "01-02", "remark": null, "children": null}, {"codeValue": "01-02-11", "codeName": "隧道机电故障需修复", "fieldOrder": 11, "parentCodeValue": "01-02", "remark": null, "children": null}, {"codeValue": "01-02-12", "codeName": "隧道其他设施损坏需修复", "fieldOrder": 12, "parentCodeValue": "01-02", "remark": null, "children": null}, {"codeValue": "01-02-13", "codeName": "违章占道、无手续打围、无手续挖掘、占道公示不规范等需处理", "fieldOrder": 13, "parentCodeValue": "01-02", "remark": null, "children": null}, {"codeValue": "01-02-14", "codeName": "止车石、球缺失、移位、破损需修复", "fieldOrder": 14, "parentCodeValue": "01-02", "remark": null, "children": null}, {"codeValue": "01-02-15", "codeName": "井盖、雨水蓖子移位、缺失、破损需修复", "fieldOrder": 15, "parentCodeValue": "01-02", "remark": null, "children": null}, {"codeValue": "01-02-16", "codeName": "坐凳缺失、移位、破损需修复", "fieldOrder": 16, "parentCodeValue": "01-02", "remark": null, "children": null}, {"codeValue": "01-02-17", "codeName": "绿道铺装、缘石缺失、破损需修复", "fieldOrder": 17, "parentCodeValue": "01-02", "remark": null, "children": null}, {"codeValue": "01-02-18", "codeName": "造景缺失、移位、破损需修复", "fieldOrder": 18, "parentCodeValue": "01-02", "remark": null, "children": null}, {"codeValue": "01-02-19", "codeName": "路面养护问题（龟裂、裂缝、沉陷、车辙、拥包、坑槽、松散、泛油、标线模糊）", "fieldOrder": 19, "parentCodeValue": "01-02", "remark": null, "children": null}, {"codeValue": "01-02-20", "codeName": "道路施工安全规范（警示标识缺失、围挡设置不合理、施工防护不规范）", "fieldOrder": 20, "parentCodeValue": "01-02", "remark": null, "children": null}]}]}, {"codeValue": "02", "codeName": "综合巡查", "fieldOrder": 2, "parentCodeValue": "-1", "remark": null, "children": [{"codeValue": "02-01", "codeName": "门店管理", "fieldOrder": 1, "parentCodeValue": "02", "remark": null, "children": [{"codeValue": "02-01-01", "codeName": "沿街店铺出店经营", "fieldOrder": 1, "parentCodeValue": "02-01", "remark": null, "children": null}, {"codeValue": "02-01-02", "codeName": "店外违规广告", "fieldOrder": 2, "parentCodeValue": "02-01", "remark": null, "children": null}, {"codeValue": "02-01-03", "codeName": "店外非法搭建、接坡", "fieldOrder": 3, "parentCodeValue": "02-01", "remark": null, "children": null}, {"codeValue": "02-01-04", "codeName": "违规倾倒厨余垃圾、排放生活废水、油烟", "fieldOrder": 4, "parentCodeValue": "02-01", "remark": null, "children": null}, {"codeValue": "02-01-05", "codeName": "违规占道经营", "fieldOrder": 5, "parentCodeValue": "02-01", "remark": null, "children": null}, {"codeValue": "02-01-06", "codeName": "临街晾晒物品", "fieldOrder": 6, "parentCodeValue": "02-01", "remark": null, "children": null}, {"codeValue": "02-01-07", "codeName": "门店责任区域地面油渍、污垢需清理", "fieldOrder": 7, "parentCodeValue": "02-01", "remark": null, "children": null}]}, {"codeValue": "02-02", "codeName": "施工管理", "fieldOrder": 2, "parentCodeValue": "02", "remark": null, "children": [{"codeValue": "02-02-01", "codeName": "违规施工（占道、无证、乱堆物料、安全防护缺失）", "fieldOrder": 1, "parentCodeValue": "02-02", "remark": null, "children": null}, {"codeValue": "02-02-02", "codeName": "违规破坏市政设施设备（管道、电路）", "fieldOrder": 2, "parentCodeValue": "02-02", "remark": null, "children": null}, {"codeValue": "02-02-03", "codeName": "制造施工噪音、扬尘扰民行为", "fieldOrder": 3, "parentCodeValue": "02-02", "remark": null, "children": null}, {"codeValue": "02-02-04", "codeName": "建筑垃圾非法堆放，工地渣土违规倾倒、抛洒", "fieldOrder": 4, "parentCodeValue": "02-02", "remark": null, "children": null}]}]}, {"codeValue": "03", "codeName": "城市应急", "fieldOrder": 3, "parentCodeValue": "-1", "remark": null, "children": [{"codeValue": "03-01", "codeName": "应急管理", "fieldOrder": 1, "parentCodeValue": "03", "remark": null, "children": [{"codeValue": "03-01-01", "codeName": "严重交通事故", "fieldOrder": 1, "parentCodeValue": "03-01", "remark": null, "children": null}, {"codeValue": "03-01-02", "codeName": "活动保障任务", "fieldOrder": 2, "parentCodeValue": "03-01", "remark": null, "children": null}, {"codeValue": "03-01-03", "codeName": "暴雨天气应急", "fieldOrder": 3, "parentCodeValue": "03-01", "remark": null, "children": null}, {"codeValue": "03-01-04", "codeName": "大雪天气应急", "fieldOrder": 4, "parentCodeValue": "03-01", "remark": null, "children": null}, {"codeValue": "03-01-05", "codeName": "高温天气应急", "fieldOrder": 5, "parentCodeValue": "03-01", "remark": null, "children": null}, {"codeValue": "03-01-06", "codeName": "火险应急", "fieldOrder": 6, "parentCodeValue": "03-01", "remark": null, "children": null}]}, {"codeValue": "03-02", "codeName": "其它", "fieldOrder": 2, "parentCodeValue": "03", "remark": null, "children": [{"codeValue": "03-02-01", "codeName": "其他紧急应急情况报送", "fieldOrder": 1, "parentCodeValue": "03-02", "remark": null, "children": null}]}]}, {"codeValue": "04", "codeName": "城市应急管理", "fieldOrder": 4, "parentCodeValue": "-1", "remark": null, "children": [{"codeValue": "04-01", "codeName": "台风预警", "fieldOrder": 1, "parentCodeValue": "04", "remark": null, "children": [{"codeValue": "04-01-01", "codeName": "在建工地巡查", "fieldOrder": 1, "parentCodeValue": "04-01", "remark": null, "children": null}, {"codeValue": "04-01-02", "codeName": "红色、橙色台风预警人员准备", "fieldOrder": 2, "parentCodeValue": "04-01", "remark": null, "children": null}, {"codeValue": "04-01-03", "codeName": "蓝色台风预警人员准备", "fieldOrder": 3, "parentCodeValue": "04-01", "remark": null, "children": null}, {"codeValue": "04-01-04", "codeName": "蓝色台风预警应急物资检查", "fieldOrder": 4, "parentCodeValue": "04-01", "remark": null, "children": null}, {"codeValue": "04-01-05", "codeName": "黄色台风预警-公房巡查", "fieldOrder": 5, "parentCodeValue": "04-01", "remark": null, "children": null}, {"codeValue": "04-01-06", "codeName": "黄色台风预警临时搭建物检查", "fieldOrder": 6, "parentCodeValue": "04-01", "remark": null, "children": null}, {"codeValue": "04-01-07", "codeName": "黄色台风预警人员准备", "fieldOrder": 7, "parentCodeValue": "04-01", "remark": null, "children": null}, {"codeValue": "04-01-08", "codeName": "黄色台风预警商业街检查", "fieldOrder": 8, "parentCodeValue": "04-01", "remark": null, "children": null}, {"codeValue": "04-01-09", "codeName": "黄色台风预警市政设施检查", "fieldOrder": 9, "parentCodeValue": "04-01", "remark": null, "children": null}, {"codeValue": "04-01-10", "codeName": "黄色台风预警环卫设施设备检查", "fieldOrder": 10, "parentCodeValue": "04-01", "remark": null, "children": null}, {"codeValue": "04-01-11", "codeName": "黄色台风预警绿化植被检查", "fieldOrder": 11, "parentCodeValue": "04-01", "remark": null, "children": null}]}, {"codeValue": "04-02", "codeName": "暴雨预警", "fieldOrder": 2, "parentCodeValue": "04", "remark": null, "children": [{"codeValue": "04-02-01", "codeName": "公房重点区域巡查", "fieldOrder": 1, "parentCodeValue": "04-02", "remark": null, "children": null}, {"codeValue": "04-02-02", "codeName": "红色、橙色暴雨预警人员准备", "fieldOrder": 2, "parentCodeValue": "04-02", "remark": null, "children": null}, {"codeValue": "04-02-03", "codeName": "红色、橙色暴雨预警重点区域值守", "fieldOrder": 3, "parentCodeValue": "04-02", "remark": null, "children": null}, {"codeValue": "04-02-04", "codeName": "蓝色暴雨预警应急物资检查", "fieldOrder": 4, "parentCodeValue": "04-02", "remark": null, "children": null}, {"codeValue": "04-02-05", "codeName": "黄色暴雨重点区域检查", "fieldOrder": 5, "parentCodeValue": "04-02", "remark": null, "children": null}, {"codeValue": "04-02-06", "codeName": "黄色暴雨预警人员准备", "fieldOrder": 6, "parentCodeValue": "04-02", "remark": null, "children": null}, {"codeValue": "04-02-07", "codeName": "黄色暴雨预警市政排水设施检查", "fieldOrder": 7, "parentCodeValue": "04-02", "remark": null, "children": null}, {"codeValue": "04-02-08", "codeName": "黄色暴雨预警应急物资检查", "fieldOrder": 8, "parentCodeValue": "04-02", "remark": null, "children": null}, {"codeValue": "04-02-09", "codeName": "黄色暴雨预警建筑工地检查", "fieldOrder": 9, "parentCodeValue": "04-02", "remark": null, "children": null}, {"codeValue": "04-02-10", "codeName": "黄色暴雨预警树木检查", "fieldOrder": 10, "parentCodeValue": "04-02", "remark": null, "children": null}, {"codeValue": "04-02-11", "codeName": "黄色暴雨预警重点区域巡查", "fieldOrder": 11, "parentCodeValue": "04-02", "remark": null, "children": null}]}]}, {"codeValue": "05", "codeName": "园林绿化", "fieldOrder": 5, "parentCodeValue": "-1", "remark": null, "children": [{"codeValue": "05-01", "codeName": "植物养护", "fieldOrder": 1, "parentCodeValue": "05", "remark": null, "children": [{"codeValue": "05-01-01", "codeName": "绿化植物需浇水", "fieldOrder": 1, "parentCodeValue": "05-01", "remark": null, "children": null}, {"codeValue": "05-01-02", "codeName": "绿化植物发生病虫害", "fieldOrder": 2, "parentCodeValue": "05-01", "remark": null, "children": null}, {"codeValue": "05-01-03", "codeName": "绿化植物需施肥", "fieldOrder": 3, "parentCodeValue": "05-01", "remark": null, "children": null}, {"codeValue": "05-01-04", "codeName": "绿化植物需补植", "fieldOrder": 4, "parentCodeValue": "05-01", "remark": null, "children": null}, {"codeValue": "05-01-05", "codeName": "绿化植物需修剪", "fieldOrder": 5, "parentCodeValue": "05-01", "remark": null, "children": null}, {"codeValue": "05-01-06", "codeName": "节庆鲜花需更换", "fieldOrder": 6, "parentCodeValue": "05-01", "remark": null, "children": null}, {"codeValue": "05-01-07", "codeName": "绿化植物需加固", "fieldOrder": 7, "parentCodeValue": "05-01", "remark": null, "children": null}, {"codeValue": "05-01-08", "codeName": "绿化植物需补植（乔灌木）", "fieldOrder": 8, "parentCodeValue": "05-01", "remark": null, "children": null}, {"codeValue": "05-01-09", "codeName": "绿化植物需补植（草坪、地被补植）", "fieldOrder": 9, "parentCodeValue": "05-01", "remark": null, "children": null}]}, {"codeValue": "05-02", "codeName": "绿化环境保洁", "fieldOrder": 2, "parentCodeValue": "05", "remark": null, "children": [{"codeValue": "05-02-01", "codeName": "绿化垃圾、弃料需清运", "fieldOrder": 1, "parentCodeValue": "05-02", "remark": null, "children": null}, {"codeValue": "05-02-02", "codeName": "绿地脏乱需清理", "fieldOrder": 2, "parentCodeValue": "05-02", "remark": null, "children": null}, {"codeValue": "05-02-03", "codeName": "绿地病虫害消杀", "fieldOrder": 3, "parentCodeValue": "05-02", "remark": null, "children": null}, {"codeValue": "05-02-04", "codeName": "水域不洁", "fieldOrder": 4, "parentCodeValue": "05-02", "remark": null, "children": null}]}]}, {"codeValue": "06", "codeName": "市容环卫", "fieldOrder": 6, "parentCodeValue": "-1", "remark": null, "children": [{"codeValue": "06-01", "codeName": "清扫保洁", "fieldOrder": 1, "parentCodeValue": "06", "remark": null, "children": [{"codeValue": "06-01-01", "codeName": "道路不洁需处理", "fieldOrder": 1, "parentCodeValue": "06-01", "remark": null, "children": null}, {"codeValue": "06-01-02", "codeName": "水域不洁需处理", "fieldOrder": 2, "parentCodeValue": "06-01", "remark": null, "children": null}, {"codeValue": "06-01-03", "codeName": "沙（河）滩不洁需处理", "fieldOrder": 3, "parentCodeValue": "06-01", "remark": null, "children": null}, {"codeValue": "06-01-04", "codeName": "建筑物外立面不洁需处理", "fieldOrder": 4, "parentCodeValue": "06-01", "remark": null, "children": null}, {"codeValue": "06-01-05", "codeName": "道路积雪、结冰清理", "fieldOrder": 5, "parentCodeValue": "06-01", "remark": null, "children": null}, {"codeValue": "06-01-06", "codeName": "违规喷绘、张贴小广告清理", "fieldOrder": 6, "parentCodeValue": "06-01", "remark": null, "children": null}, {"codeValue": "06-01-07", "codeName": "地面垃圾、纸屑需要清理打扫", "fieldOrder": 7, "parentCodeValue": "06-01", "remark": null, "children": null}, {"codeValue": "06-01-08", "codeName": "下水道口垃圾堵塞、有异味", "fieldOrder": 8, "parentCodeValue": "06-01", "remark": null, "children": null}, {"codeValue": "06-01-09", "codeName": "工业垃圾、建筑垃圾处理", "fieldOrder": 9, "parentCodeValue": "06-01", "remark": null, "children": null}, {"codeValue": "06-01-10", "codeName": "公交站台清洗", "fieldOrder": 10, "parentCodeValue": "06-01", "remark": null, "children": null}, {"codeValue": "06-01-11", "codeName": "绿化带有白飘", "fieldOrder": 11, "parentCodeValue": "06-01", "remark": null, "children": null}, {"codeValue": "06-01-12", "codeName": "路面不洁需冲洗", "fieldOrder": 12, "parentCodeValue": "06-01", "remark": null, "children": null}, {"codeValue": "06-01-13", "codeName": "垃圾桶乱摆放，需处理", "fieldOrder": 13, "parentCodeValue": "06-01", "remark": null, "children": null}, {"codeValue": "06-01-14", "codeName": "杂草需清理", "fieldOrder": 14, "parentCodeValue": "06-01", "remark": null, "children": null}, {"codeValue": "06-01-15", "codeName": "动物粪便需要处理", "fieldOrder": 15, "parentCodeValue": "06-01", "remark": null, "children": null}, {"codeValue": "06-01-16", "codeName": "灭烟处需清理", "fieldOrder": 16, "parentCodeValue": "06-01", "remark": null, "children": null}, {"codeValue": "06-01-17", "codeName": "卫生死角处理", "fieldOrder": 17, "parentCodeValue": "06-01", "remark": null, "children": null}, {"codeValue": "06-01-18", "codeName": "乱扔烟头", "fieldOrder": 18, "parentCodeValue": "06-01", "remark": null, "children": null}]}, {"codeValue": "06-02", "codeName": "垃圾收运及处理", "fieldOrder": 2, "parentCodeValue": "06", "remark": null, "children": [{"codeValue": "06-02-01", "codeName": "垃圾暴露、满溢问题处理", "fieldOrder": 1, "parentCodeValue": "06-02", "remark": null, "children": null}, {"codeValue": "06-02-02", "codeName": "垃圾乱投放、混装问题处理", "fieldOrder": 2, "parentCodeValue": "06-02", "remark": null, "children": null}, {"codeValue": "06-02-03", "codeName": "垃圾运输车辆二次污染（遗撒、滴漏、噪声、不密闭等）处理", "fieldOrder": 3, "parentCodeValue": "06-02", "remark": null, "children": null}, {"codeValue": "06-02-04", "codeName": "老旧小区垃圾清运", "fieldOrder": 4, "parentCodeValue": "06-02", "remark": null, "children": null}, {"codeValue": "06-02-05", "codeName": "大件废弃物料、家私摆放堆积", "fieldOrder": 5, "parentCodeValue": "06-02", "remark": null, "children": null}, {"codeValue": "06-02-06", "codeName": "垃圾箱满溢、堆积未清运", "fieldOrder": 6, "parentCodeValue": "06-02", "remark": null, "children": null}, {"codeValue": "06-02-07", "codeName": "生活垃圾、装潢垃圾、建筑渣土、道路施工等暴露垃圾裸露堆积", "fieldOrder": 7, "parentCodeValue": "06-02", "remark": null, "children": null}, {"codeValue": "06-02-08", "codeName": "随意倾倒、抛洒、堆放生活打包垃圾", "fieldOrder": 8, "parentCodeValue": "06-02", "remark": null, "children": null}]}]}, {"codeValue": "07", "codeName": "河流水质", "fieldOrder": 7, "parentCodeValue": "-1", "remark": null, "children": [{"codeValue": "07-01", "codeName": "设备维护", "fieldOrder": 1, "parentCodeValue": "07", "remark": null, "children": [{"codeValue": "07-01-01", "codeName": "返厂维修", "fieldOrder": 1, "parentCodeValue": "07-01", "remark": null, "children": null}, {"codeValue": "07-01-02", "codeName": "项目维修", "fieldOrder": 2, "parentCodeValue": "07-01", "remark": null, "children": null}, {"codeValue": "07-01-03", "codeName": "设备断网", "fieldOrder": 3, "parentCodeValue": "07-01", "remark": null, "children": null}, {"codeValue": "07-01-04", "codeName": "校准", "fieldOrder": 4, "parentCodeValue": "07-01", "remark": null, "children": null}, {"codeValue": "07-01-05", "codeName": "维护", "fieldOrder": 5, "parentCodeValue": "07-01", "remark": null, "children": null}]}, {"codeValue": "07-02", "codeName": "河流巡查", "fieldOrder": 2, "parentCodeValue": "07", "remark": null, "children": [{"codeValue": "07-02-01", "codeName": "河面漂浮物", "fieldOrder": 1, "parentCodeValue": "07-02", "remark": null, "children": null}, {"codeValue": "07-02-02", "codeName": "排水口溢流", "fieldOrder": 2, "parentCodeValue": "07-02", "remark": null, "children": null}, {"codeValue": "07-02-03", "codeName": "河岸垃圾", "fieldOrder": 3, "parentCodeValue": "07-02", "remark": null, "children": null}, {"codeValue": "07-02-04", "codeName": "闸口溢流", "fieldOrder": 4, "parentCodeValue": "07-02", "remark": null, "children": null}, {"codeValue": "07-02-05", "codeName": "岸堤塌陷", "fieldOrder": 5, "parentCodeValue": "07-02", "remark": null, "children": null}, {"codeValue": "07-02-06", "codeName": "河道上游巡查", "fieldOrder": 6, "parentCodeValue": "07-02", "remark": null, "children": null}, {"codeValue": "07-02-07", "codeName": "河道中游巡查", "fieldOrder": 7, "parentCodeValue": "07-02", "remark": null, "children": null}, {"codeValue": "07-02-08", "codeName": "河道下游巡查", "fieldOrder": 8, "parentCodeValue": "07-02", "remark": null, "children": null}, {"codeValue": "07-02-09", "codeName": "河道上中下游巡查", "fieldOrder": 9, "parentCodeValue": "07-02", "remark": null, "children": null}]}]}, {"codeValue": "08", "codeName": "园区畅行", "fieldOrder": 8, "parentCodeValue": "-1", "remark": null, "children": [{"codeValue": "08-01", "codeName": "智慧畅行", "fieldOrder": 1, "parentCodeValue": "08", "remark": null, "children": [{"codeValue": "08-01-01", "codeName": "违规使用远光灯告警", "fieldOrder": 1, "parentCodeValue": "08-01", "remark": null, "children": null}, {"codeValue": "08-01-02", "codeName": "闯红灯违章告警", "fieldOrder": 2, "parentCodeValue": "08-01", "remark": null, "children": null}, {"codeValue": "08-01-03", "codeName": "逆行违章告警", "fieldOrder": 3, "parentCodeValue": "08-01", "remark": null, "children": null}, {"codeValue": "08-01-04", "codeName": "闯禁令违章告警", "fieldOrder": 4, "parentCodeValue": "08-01", "remark": null, "children": null}, {"codeValue": "08-01-05", "codeName": "不按所需行进方向驶入导向车道违章告警", "fieldOrder": 5, "parentCodeValue": "08-01", "remark": null, "children": null}, {"codeValue": "08-01-06", "codeName": "不按规定车道行驶违章告警", "fieldOrder": 6, "parentCodeValue": "08-01", "remark": null, "children": null}, {"codeValue": "08-01-07", "codeName": "违章变道告警", "fieldOrder": 7, "parentCodeValue": "08-01", "remark": null, "children": null}, {"codeValue": "08-01-08", "codeName": "违章停车告警", "fieldOrder": 8, "parentCodeValue": "08-01", "remark": null, "children": null}, {"codeValue": "08-01-09", "codeName": "骑线压线告警", "fieldOrder": 9, "parentCodeValue": "08-01", "remark": null, "children": null}, {"codeValue": "08-01-10", "codeName": "假牌套牌报警告警", "fieldOrder": 10, "parentCodeValue": "08-01", "remark": null, "children": null}, {"codeValue": "08-01-11", "codeName": "超速告警", "fieldOrder": 11, "parentCodeValue": "08-01", "remark": null, "children": null}, {"codeValue": "08-01-12", "codeName": "违反低速行驶告警", "fieldOrder": 12, "parentCodeValue": "08-01", "remark": null, "children": null}, {"codeValue": "08-01-13", "codeName": "未停车让行告警", "fieldOrder": 13, "parentCodeValue": "08-01", "remark": null, "children": null}, {"codeValue": "08-01-14", "codeName": "禁止鸣笛告警", "fieldOrder": 14, "parentCodeValue": "08-01", "remark": null, "children": null}, {"codeValue": "08-01-15", "codeName": "事故事件监测告警", "fieldOrder": 15, "parentCodeValue": "08-01", "remark": null, "children": null}, {"codeValue": "08-01-16", "codeName": "急停事件告警", "fieldOrder": 16, "parentCodeValue": "08-01", "remark": null, "children": null}, {"codeValue": "08-01-17", "codeName": "车流量大事件告警", "fieldOrder": 17, "parentCodeValue": "08-01", "remark": null, "children": null}, {"codeValue": "08-01-18", "codeName": "排队事件告警", "fieldOrder": 18, "parentCodeValue": "08-01", "remark": null, "children": null}, {"codeValue": "08-01-19", "codeName": "拥堵事件告警", "fieldOrder": 19, "parentCodeValue": "08-01", "remark": null, "children": null}]}, {"codeValue": "08-02", "codeName": "智慧停车", "fieldOrder": 2, "parentCodeValue": "08", "remark": null, "children": [{"codeValue": "08-02-01", "codeName": "地磁设备离线", "fieldOrder": 1, "parentCodeValue": "08-02", "remark": null, "children": null}, {"codeValue": "08-02-02", "codeName": "地磁设备低电量告警", "fieldOrder": 2, "parentCodeValue": "08-02", "remark": null, "children": null}, {"codeValue": "08-02-03", "codeName": "车辆违停", "fieldOrder": 3, "parentCodeValue": "08-02", "remark": null, "children": null}, {"codeValue": "08-02-04", "codeName": "道闸设备故障", "fieldOrder": 4, "parentCodeValue": "08-02", "remark": null, "children": null}, {"codeValue": "08-02-05", "codeName": "停车系统通讯失败", "fieldOrder": 5, "parentCodeValue": "08-02", "remark": null, "children": null}]}, {"codeValue": "08-03", "codeName": "智慧公交站", "fieldOrder": 3, "parentCodeValue": "08", "remark": null, "children": [{"codeValue": "08-03-01", "codeName": "摄像头离线", "fieldOrder": 1, "parentCodeValue": "08-03", "remark": null, "children": null}, {"codeValue": "08-03-02", "codeName": "摄像头设备故障", "fieldOrder": 2, "parentCodeValue": "08-03", "remark": null, "children": null}, {"codeValue": "08-03-03", "codeName": "人流量统计异常", "fieldOrder": 3, "parentCodeValue": "08-03", "remark": null, "children": null}, {"codeValue": "08-03-04", "codeName": "车流量统计异常", "fieldOrder": 4, "parentCodeValue": "08-03", "remark": null, "children": null}, {"codeValue": "08-03-05", "codeName": "公交站异常事件告警", "fieldOrder": 5, "parentCodeValue": "08-03", "remark": null, "children": null}]}, {"codeValue": "08-04", "codeName": "车路协同", "fieldOrder": 4, "parentCodeValue": "08", "remark": null, "children": [{"codeValue": "08-04-01", "codeName": "前方道路临时施工预警", "fieldOrder": 1, "parentCodeValue": "08-04", "remark": null, "children": null}, {"codeValue": "08-04-02", "codeName": "前方突发事件预警", "fieldOrder": 2, "parentCodeValue": "08-04", "remark": null, "children": null}, {"codeValue": "08-04-03", "codeName": "前方拥堵预警", "fieldOrder": 3, "parentCodeValue": "08-04", "remark": null, "children": null}, {"codeValue": "08-04-04", "codeName": "路面积水预警", "fieldOrder": 4, "parentCodeValue": "08-04", "remark": null, "children": null}, {"codeValue": "08-04-05", "codeName": "车道临时占用检测/预警", "fieldOrder": 5, "parentCodeValue": "08-04", "remark": null, "children": null}, {"codeValue": "08-04-06", "codeName": "车辆违停", "fieldOrder": 6, "parentCodeValue": "08-04", "remark": null, "children": null}, {"codeValue": "08-04-07", "codeName": "异常停车事件检测", "fieldOrder": 7, "parentCodeValue": "08-04", "remark": null, "children": null}, {"codeValue": "08-04-08", "codeName": "车辆逆行检测", "fieldOrder": 8, "parentCodeValue": "08-04", "remark": null, "children": null}, {"codeValue": "08-04-09", "codeName": "非机动车闯禁", "fieldOrder": 9, "parentCodeValue": "08-04", "remark": null, "children": null}, {"codeValue": "08-04-10", "codeName": "行人闯禁", "fieldOrder": 10, "parentCodeValue": "08-04", "remark": null, "children": null}, {"codeValue": "08-04-11", "codeName": "非机动车占用机动车道检测", "fieldOrder": 11, "parentCodeValue": "08-04", "remark": null, "children": null}, {"codeValue": "08-04-12", "codeName": "机动车占用非机动车道检测", "fieldOrder": 12, "parentCodeValue": "08-04", "remark": null, "children": null}, {"codeValue": "08-04-13", "codeName": "平均车速", "fieldOrder": 13, "parentCodeValue": "08-04", "remark": null, "children": null}, {"codeValue": "08-04-14", "codeName": "车流量", "fieldOrder": 14, "parentCodeValue": "08-04", "remark": null, "children": null}]}, {"codeValue": "08-05", "codeName": "智慧出行", "fieldOrder": 5, "parentCodeValue": "08", "remark": null, "children": [{"codeValue": "08-05-01", "codeName": "车辆违停", "fieldOrder": 1, "parentCodeValue": "08-05", "remark": null, "children": null}, {"codeValue": "08-05-02", "codeName": "异常停车事件", "fieldOrder": 2, "parentCodeValue": "08-05", "remark": null, "children": null}, {"codeValue": "08-05-03", "codeName": "车辆逆行", "fieldOrder": 3, "parentCodeValue": "08-05", "remark": null, "children": null}, {"codeValue": "08-05-04", "codeName": "交通事故", "fieldOrder": 4, "parentCodeValue": "08-05", "remark": null, "children": null}, {"codeValue": "08-05-05", "codeName": "交通拥堵", "fieldOrder": 5, "parentCodeValue": "08-05", "remark": null, "children": null}, {"codeValue": "08-05-06", "codeName": "停车场负荷度过高", "fieldOrder": 6, "parentCodeValue": "08-05", "remark": null, "children": null}, {"codeValue": "08-05-07", "codeName": "停车位不足", "fieldOrder": 7, "parentCodeValue": "08-05", "remark": null, "children": null}]}, {"codeValue": "08-06", "codeName": "智慧道桥", "fieldOrder": 6, "parentCodeValue": "08", "remark": null, "children": [{"codeValue": "08-06-01", "codeName": "道路清洁", "fieldOrder": 1, "parentCodeValue": "08-06", "remark": null, "children": null}, {"codeValue": "08-06-02", "codeName": "道路养护", "fieldOrder": 2, "parentCodeValue": "08-06", "remark": null, "children": null}, {"codeValue": "08-06-03", "codeName": "桥梁清洁", "fieldOrder": 3, "parentCodeValue": "08-06", "remark": null, "children": null}, {"codeValue": "08-06-04", "codeName": "桥梁养护", "fieldOrder": 4, "parentCodeValue": "08-06", "remark": null, "children": null}]}, {"codeValue": "08-07", "codeName": "智慧路灯", "fieldOrder": 7, "parentCodeValue": "08", "remark": null, "children": [{"codeValue": "08-07-01", "codeName": "路灯设备离线", "fieldOrder": 1, "parentCodeValue": "08-07", "remark": null, "children": null}, {"codeValue": "08-07-02", "codeName": "路灯设备故障", "fieldOrder": 2, "parentCodeValue": "08-07", "remark": null, "children": null}, {"codeValue": "08-07-03", "codeName": "路灯电力参数异常", "fieldOrder": 3, "parentCodeValue": "08-07", "remark": null, "children": null}, {"codeValue": "08-07-04", "codeName": "路灯白天异常开启", "fieldOrder": 4, "parentCodeValue": "08-07", "remark": null, "children": null}]}, {"codeValue": "08-08", "codeName": "园区畅行统计分析", "fieldOrder": 8, "parentCodeValue": "08", "remark": null, "children": [{"codeValue": "08-08-01", "codeName": "交通拥堵", "fieldOrder": 1, "parentCodeValue": "08-08", "remark": null, "children": null}, {"codeValue": "08-08-02", "codeName": "交通事故", "fieldOrder": 2, "parentCodeValue": "08-08", "remark": null, "children": null}, {"codeValue": "08-08-03", "codeName": "停车场负荷度过高", "fieldOrder": 3, "parentCodeValue": "08-08", "remark": null, "children": null}, {"codeValue": "08-08-04", "codeName": "停车位不足", "fieldOrder": 4, "parentCodeValue": "08-08", "remark": null, "children": null}, {"codeValue": "08-08-05", "codeName": "无人车故障", "fieldOrder": 5, "parentCodeValue": "08-08", "remark": null, "children": null}, {"codeValue": "08-08-06", "codeName": "渣土车告警", "fieldOrder": 6, "parentCodeValue": "08-08", "remark": null, "children": null}]}]}, {"codeValue": "09", "codeName": "生态宜居", "fieldOrder": 9, "parentCodeValue": "-1", "remark": null, "children": [{"codeValue": "09-01", "codeName": "自动灌溉", "fieldOrder": 1, "parentCodeValue": "09", "remark": null, "children": [{"codeValue": "09-01-01", "codeName": "灌溉设备离线", "fieldOrder": 1, "parentCodeValue": "09-01", "remark": null, "children": null}, {"codeValue": "09-01-02", "codeName": "灌溉设备故障", "fieldOrder": 2, "parentCodeValue": "09-01", "remark": null, "children": null}, {"codeValue": "09-01-03", "codeName": "水表通讯失败", "fieldOrder": 3, "parentCodeValue": "09-01", "remark": null, "children": null}, {"codeValue": "09-01-04", "codeName": "雨量通讯失败", "fieldOrder": 4, "parentCodeValue": "09-01", "remark": null, "children": null}, {"codeValue": "09-01-05", "codeName": "设备电流异常", "fieldOrder": 5, "parentCodeValue": "09-01", "remark": null, "children": null}, {"codeValue": "09-01-06", "codeName": "实时流量异常", "fieldOrder": 6, "parentCodeValue": "09-01", "remark": null, "children": null}, {"codeValue": "09-01-07", "codeName": "浇水时长异常", "fieldOrder": 7, "parentCodeValue": "09-01", "remark": null, "children": null}, {"codeValue": "09-01-08", "codeName": "降雨时浇水", "fieldOrder": 8, "parentCodeValue": "09-01", "remark": null, "children": null}, {"codeValue": "09-01-09", "codeName": "浇水后土壤湿度不变", "fieldOrder": 9, "parentCodeValue": "09-01", "remark": null, "children": null}, {"codeValue": "09-01-10", "codeName": "灌溉区域养护", "fieldOrder": 10, "parentCodeValue": "09-01", "remark": null, "children": null}]}, {"codeValue": "09-02", "codeName": "餐厨垃圾管理", "fieldOrder": 2, "parentCodeValue": "09", "remark": null, "children": [{"codeValue": "09-02-01", "codeName": "车辆称重设备离线", "fieldOrder": 1, "parentCodeValue": "09-02", "remark": null, "children": null}, {"codeValue": "09-02-02", "codeName": "车辆程总数据异常", "fieldOrder": 2, "parentCodeValue": "09-02", "remark": null, "children": null}, {"codeValue": "09-02-03", "codeName": "餐厨车辆定位终端离线", "fieldOrder": 3, "parentCodeValue": "09-02", "remark": null, "children": null}, {"codeValue": "09-02-04", "codeName": "餐厨车辆区域超限告警", "fieldOrder": 4, "parentCodeValue": "09-02", "remark": null, "children": null}, {"codeValue": "09-02-05", "codeName": "地磅设备离线", "fieldOrder": 5, "parentCodeValue": "09-02", "remark": null, "children": null}, {"codeValue": "09-02-06", "codeName": "地磅数据异常", "fieldOrder": 6, "parentCodeValue": "09-02", "remark": null, "children": null}, {"codeValue": "09-02-07", "codeName": "企业申报审核超期", "fieldOrder": 7, "parentCodeValue": "09-02", "remark": null, "children": null}, {"codeValue": "09-02-08", "codeName": "日收运路线未完成", "fieldOrder": 8, "parentCodeValue": "09-02", "remark": null, "children": null}]}, {"codeValue": "09-03", "codeName": "垃圾收运管理", "fieldOrder": 3, "parentCodeValue": "09", "remark": null, "children": [{"codeValue": "09-03-01", "codeName": "车辆称重设备离线", "fieldOrder": 1, "parentCodeValue": "09-03", "remark": null, "children": null}, {"codeValue": "09-03-02", "codeName": "垃圾收运车定位终端离线", "fieldOrder": 2, "parentCodeValue": "09-03", "remark": null, "children": null}, {"codeValue": "09-03-03", "codeName": "垃圾收运车区域超限告警", "fieldOrder": 3, "parentCodeValue": "09-03", "remark": null, "children": null}, {"codeValue": "09-03-04", "codeName": "地磅设备离线", "fieldOrder": 4, "parentCodeValue": "09-03", "remark": null, "children": null}, {"codeValue": "09-03-05", "codeName": "地磅数据异常", "fieldOrder": 5, "parentCodeValue": "09-03", "remark": null, "children": null}, {"codeValue": "09-03-06", "codeName": "收运计划未完成", "fieldOrder": 6, "parentCodeValue": "09-03", "remark": null, "children": null}]}, {"codeValue": "09-04", "codeName": "智能垃圾桶", "fieldOrder": 4, "parentCodeValue": "09", "remark": null, "children": [{"codeValue": "09-04-01", "codeName": "垃圾桶满溢告警", "fieldOrder": 1, "parentCodeValue": "09-04", "remark": null, "children": null}, {"codeValue": "09-04-02", "codeName": "垃圾屋烟雾报警", "fieldOrder": 2, "parentCodeValue": "09-04", "remark": null, "children": null}, {"codeValue": "09-04-03", "codeName": "垃圾屋温湿度异常", "fieldOrder": 3, "parentCodeValue": "09-04", "remark": null, "children": null}, {"codeValue": "09-04-04", "codeName": "垃圾屋设备破坏告警", "fieldOrder": 4, "parentCodeValue": "09-04", "remark": null, "children": null}, {"codeValue": "09-04-05", "codeName": "垃圾屋设备离线", "fieldOrder": 5, "parentCodeValue": "09-04", "remark": null, "children": null}, {"codeValue": "09-04-06", "codeName": "垃圾屋摄像头离线", "fieldOrder": 6, "parentCodeValue": "09-04", "remark": null, "children": null}]}, {"codeValue": "09-05", "codeName": "智慧公厕", "fieldOrder": 5, "parentCodeValue": "09", "remark": null, "children": [{"codeValue": "09-05-01", "codeName": "气体超标报警", "fieldOrder": 1, "parentCodeValue": "09-05", "remark": null, "children": null}, {"codeValue": "09-05-02", "codeName": "用水异常报警", "fieldOrder": 2, "parentCodeValue": "09-05", "remark": null, "children": null}, {"codeValue": "09-05-03", "codeName": "用电异常报警", "fieldOrder": 3, "parentCodeValue": "09-05", "remark": null, "children": null}, {"codeValue": "09-05-04", "codeName": "考勤异常报警", "fieldOrder": 4, "parentCodeValue": "09-05", "remark": null, "children": null}, {"codeValue": "09-05-05", "codeName": "公厕网络异常报警", "fieldOrder": 5, "parentCodeValue": "09-05", "remark": null, "children": null}, {"codeValue": "09-05-06", "codeName": "智能联动杀菌设备故障", "fieldOrder": 6, "parentCodeValue": "09-05", "remark": null, "children": null}, {"codeValue": "09-05-07", "codeName": "气体监测设备故障", "fieldOrder": 7, "parentCodeValue": "09-05", "remark": null, "children": null}, {"codeValue": "09-05-08", "codeName": "公厕设备离线", "fieldOrder": 8, "parentCodeValue": "09-05", "remark": null, "children": null}]}, {"codeValue": "09-06", "codeName": "电子围栏", "fieldOrder": 6, "parentCodeValue": "09", "remark": null, "children": [{"codeValue": "09-06-01", "codeName": "车辆进入XXX区域报警", "fieldOrder": 1, "parentCodeValue": "09-06", "remark": null, "children": null}, {"codeValue": "09-06-02", "codeName": "车辆驶出XXX区域报警", "fieldOrder": 2, "parentCodeValue": "09-06", "remark": null, "children": null}, {"codeValue": "09-06-03", "codeName": "定位设备离线", "fieldOrder": 3, "parentCodeValue": "09-06", "remark": null, "children": null}, {"codeValue": "09-06-04", "codeName": "定位设备故障", "fieldOrder": 4, "parentCodeValue": "09-06", "remark": null, "children": null}]}, {"codeValue": "09-07", "codeName": "智慧座椅", "fieldOrder": 7, "parentCodeValue": "09", "remark": null, "children": [{"codeValue": "09-07-01", "codeName": "亮灯时长异常", "fieldOrder": 1, "parentCodeValue": "09-07", "remark": null, "children": null}, {"codeValue": "09-07-02", "codeName": "USB无法充电异常", "fieldOrder": 2, "parentCodeValue": "09-07", "remark": null, "children": null}, {"codeValue": "09-07-03", "codeName": "无线充电异常", "fieldOrder": 3, "parentCodeValue": "09-07", "remark": null, "children": null}, {"codeValue": "09-07-04", "codeName": "WIFI无法连接异常", "fieldOrder": 4, "parentCodeValue": "09-07", "remark": null, "children": null}, {"codeValue": "09-07-05", "codeName": "座椅设备离线", "fieldOrder": 5, "parentCodeValue": "09-07", "remark": null, "children": null}, {"codeValue": "09-07-06", "codeName": "电池电量不足", "fieldOrder": 6, "parentCodeValue": "09-07", "remark": null, "children": null}, {"codeValue": "09-07-07", "codeName": "灯带故障", "fieldOrder": 7, "parentCodeValue": "09-07", "remark": null, "children": null}, {"codeValue": "09-07-08", "codeName": "远程控制异常", "fieldOrder": 8, "parentCodeValue": "09-07", "remark": null, "children": null}]}, {"codeValue": "09-08", "codeName": "绿地管理", "fieldOrder": 8, "parentCodeValue": "09", "remark": null, "children": [{"codeValue": "09-08-01", "codeName": "绿地破坏", "fieldOrder": 1, "parentCodeValue": "09-08", "remark": null, "children": null}, {"codeValue": "09-08-02", "codeName": "绿地养护", "fieldOrder": 2, "parentCodeValue": "09-08", "remark": null, "children": null}]}, {"codeValue": "09-09", "codeName": "环境监测", "fieldOrder": 9, "parentCodeValue": "09", "remark": null, "children": [{"codeValue": "09-09-01", "codeName": "土壤温度过低", "fieldOrder": 1, "parentCodeValue": "09-09", "remark": null, "children": null}, {"codeValue": "09-09-02", "codeName": "土壤温度过高", "fieldOrder": 2, "parentCodeValue": "09-09", "remark": null, "children": null}, {"codeValue": "09-09-03", "codeName": "土壤湿度过低", "fieldOrder": 3, "parentCodeValue": "09-09", "remark": null, "children": null}, {"codeValue": "09-09-04", "codeName": "土壤湿度过高", "fieldOrder": 4, "parentCodeValue": "09-09", "remark": null, "children": null}, {"codeValue": "09-09-05", "codeName": "空气PM2.5超标", "fieldOrder": 5, "parentCodeValue": "09-09", "remark": null, "children": null}, {"codeValue": "09-09-06", "codeName": "空气PM10超标", "fieldOrder": 6, "parentCodeValue": "09-09", "remark": null, "children": null}, {"codeValue": "09-09-07", "codeName": "环境噪声超标", "fieldOrder": 7, "parentCodeValue": "09-09", "remark": null, "children": null}, {"codeValue": "09-09-08", "codeName": "环境温度过高", "fieldOrder": 8, "parentCodeValue": "09-09", "remark": null, "children": null}, {"codeValue": "09-09-09", "codeName": "环境温度过低", "fieldOrder": 9, "parentCodeValue": "09-09", "remark": null, "children": null}, {"codeValue": "09-09-10", "codeName": "环境湿度过高", "fieldOrder": 10, "parentCodeValue": "09-09", "remark": null, "children": null}, {"codeValue": "09-09-11", "codeName": "环境湿度过低", "fieldOrder": 11, "parentCodeValue": "09-09", "remark": null, "children": null}, {"codeValue": "09-09-12", "codeName": "降雨量预警", "fieldOrder": 12, "parentCodeValue": "09-09", "remark": null, "children": null}, {"codeValue": "09-09-13", "codeName": "风速预警", "fieldOrder": 13, "parentCodeValue": "09-09", "remark": null, "children": null}, {"codeValue": "09-09-14", "codeName": "紫外线预警", "fieldOrder": 14, "parentCodeValue": "09-09", "remark": null, "children": null}, {"codeValue": "09-09-15", "codeName": "渣土车告警", "fieldOrder": 15, "parentCodeValue": "09-09", "remark": null, "children": null}]}, {"codeValue": "09-10", "codeName": "生态宜居统计分析", "fieldOrder": 10, "parentCodeValue": "09", "remark": null, "children": [{"codeValue": "09-10-01", "codeName": "空气质量连续超标", "fieldOrder": 1, "parentCodeValue": "09-10", "remark": null, "children": null}, {"codeValue": "09-10-02", "codeName": "水污染物连续超标", "fieldOrder": 2, "parentCodeValue": "09-10", "remark": null, "children": null}, {"codeValue": "09-10-03", "codeName": "环境噪声超标", "fieldOrder": 3, "parentCodeValue": "09-10", "remark": null, "children": null}, {"codeValue": "09-10-04", "codeName": "夜间噪声超标", "fieldOrder": 4, "parentCodeValue": "09-10", "remark": null, "children": null}, {"codeValue": "09-10-05", "codeName": "连续高温告警", "fieldOrder": 5, "parentCodeValue": "09-10", "remark": null, "children": null}, {"codeValue": "09-10-06", "codeName": "智能垃圾屋连续多日满溢告警", "fieldOrder": 6, "parentCodeValue": "09-10", "remark": null, "children": null}, {"codeValue": "09-10-07", "codeName": "智能回收屋连续多日满溢告警", "fieldOrder": 7, "parentCodeValue": "09-10", "remark": null, "children": null}]}]}, {"codeValue": "10", "codeName": "安全守护", "fieldOrder": 10, "parentCodeValue": "-1", "remark": null, "children": [{"codeValue": "10-01", "codeName": "安全控制管理", "fieldOrder": 1, "parentCodeValue": "10", "remark": null, "children": [{"codeValue": "10-01-01", "codeName": "泵站设备离线", "fieldOrder": 1, "parentCodeValue": "10-01", "remark": null, "children": null}, {"codeValue": "10-01-02", "codeName": "格栅机异常", "fieldOrder": 2, "parentCodeValue": "10-01", "remark": null, "children": null}, {"codeValue": "10-01-03", "codeName": "排水泵异常", "fieldOrder": 3, "parentCodeValue": "10-01", "remark": null, "children": null}, {"codeValue": "10-01-04", "codeName": "湖泊水位异常", "fieldOrder": 4, "parentCodeValue": "10-01", "remark": null, "children": null}, {"codeValue": "10-01-05", "codeName": "积水异常", "fieldOrder": 5, "parentCodeValue": "10-01", "remark": null, "children": null}, {"codeValue": "10-01-06", "codeName": "水质监测异常", "fieldOrder": 6, "parentCodeValue": "10-01", "remark": null, "children": null}, {"codeValue": "10-01-07", "codeName": "泵站水流量过快", "fieldOrder": 7, "parentCodeValue": "10-01", "remark": null, "children": null}, {"codeValue": "10-01-08", "codeName": "泵站管道压力值超阈值", "fieldOrder": 8, "parentCodeValue": "10-01", "remark": null, "children": null}, {"codeValue": "10-01-09", "codeName": "泵站水池水位过高", "fieldOrder": 9, "parentCodeValue": "10-01", "remark": null, "children": null}, {"codeValue": "10-01-10", "codeName": "积水监测设备离线", "fieldOrder": 10, "parentCodeValue": "10-01", "remark": null, "children": null}, {"codeValue": "10-01-11", "codeName": "湖泊水位监测设备离线", "fieldOrder": 11, "parentCodeValue": "10-01", "remark": null, "children": null}, {"codeValue": "10-01-12", "codeName": "湖泊水速监测设备离线", "fieldOrder": 12, "parentCodeValue": "10-01", "remark": null, "children": null}]}, {"codeValue": "10-02", "codeName": "智慧消火栓", "fieldOrder": 2, "parentCodeValue": "10", "remark": null, "children": [{"codeValue": "10-02-01", "codeName": "消防栓水压不足", "fieldOrder": 1, "parentCodeValue": "10-02", "remark": null, "children": null}, {"codeValue": "10-02-02", "codeName": "消防栓发生倾斜", "fieldOrder": 2, "parentCodeValue": "10-02", "remark": null, "children": null}, {"codeValue": "10-02-03", "codeName": "消防栓设备离线", "fieldOrder": 3, "parentCodeValue": "10-02", "remark": null, "children": null}, {"codeValue": "10-02-04", "codeName": "消防栓设备低电量告警", "fieldOrder": 4, "parentCodeValue": "10-02", "remark": null, "children": null}]}, {"codeValue": "10-03", "codeName": "布控管理", "fieldOrder": 3, "parentCodeValue": "10", "remark": null, "children": [{"codeValue": "10-03-01", "codeName": "人员布控告警", "fieldOrder": 1, "parentCodeValue": "10-03", "remark": null, "children": null}, {"codeValue": "10-03-02", "codeName": "车辆布控告警", "fieldOrder": 2, "parentCodeValue": "10-03", "remark": null, "children": null}]}, {"codeValue": "10-04", "codeName": "智慧井盖", "fieldOrder": 4, "parentCodeValue": "10", "remark": null, "children": [{"codeValue": "10-04-01", "codeName": "井盖状态异常", "fieldOrder": 1, "parentCodeValue": "10-04", "remark": null, "children": null}, {"codeValue": "10-04-02", "codeName": "井盖设备离线", "fieldOrder": 2, "parentCodeValue": "10-04", "remark": null, "children": null}, {"codeValue": "10-04-03", "codeName": "井盖设备低电量告警", "fieldOrder": 3, "parentCodeValue": "10-04", "remark": null, "children": null}]}, {"codeValue": "10-05", "codeName": "智慧管网", "fieldOrder": 5, "parentCodeValue": "10", "remark": null, "children": [{"codeValue": "10-05-01", "codeName": "排水户许可过期", "fieldOrder": 1, "parentCodeValue": "10-05", "remark": null, "children": null}, {"codeValue": "10-05-02", "codeName": "排水户排水量异常", "fieldOrder": 2, "parentCodeValue": "10-05", "remark": null, "children": null}, {"codeValue": "10-05-03", "codeName": "管网水位异常", "fieldOrder": 3, "parentCodeValue": "10-05", "remark": null, "children": null}, {"codeValue": "10-05-04", "codeName": "管网水速异常", "fieldOrder": 4, "parentCodeValue": "10-05", "remark": null, "children": null}, {"codeValue": "10-05-05", "codeName": "供水管网流量异常", "fieldOrder": 5, "parentCodeValue": "10-05", "remark": null, "children": null}, {"codeValue": "10-05-06", "codeName": "供水管网压力异常", "fieldOrder": 6, "parentCodeValue": "10-05", "remark": null, "children": null}, {"codeValue": "10-05-07", "codeName": "管网气体指标过高", "fieldOrder": 7, "parentCodeValue": "10-05", "remark": null, "children": null}, {"codeValue": "10-05-08", "codeName": "水位设备离线", "fieldOrder": 8, "parentCodeValue": "10-05", "remark": null, "children": null}, {"codeValue": "10-05-09", "codeName": "水速设备离线", "fieldOrder": 9, "parentCodeValue": "10-05", "remark": null, "children": null}, {"codeValue": "10-05-10", "codeName": "可燃气体设备离线", "fieldOrder": 10, "parentCodeValue": "10-05", "remark": null, "children": null}]}, {"codeValue": "10-06", "codeName": "园区巡查", "fieldOrder": 6, "parentCodeValue": "10", "remark": null, "children": [{"codeValue": "10-06-01", "codeName": "设备云巡检状态异常", "fieldOrder": 1, "parentCodeValue": "10-06", "remark": null, "children": null}, {"codeValue": "10-06-02", "codeName": "设备云巡检数据异常", "fieldOrder": 2, "parentCodeValue": "10-06", "remark": null, "children": null}]}, {"codeValue": "10-07", "codeName": "国土巡检", "fieldOrder": 7, "parentCodeValue": "10", "remark": null, "children": [{"codeValue": "10-07-01", "codeName": "无人机离线", "fieldOrder": 1, "parentCodeValue": "10-07", "remark": null, "children": null}, {"codeValue": "10-07-02", "codeName": "无人机设备故障", "fieldOrder": 2, "parentCodeValue": "10-07", "remark": null, "children": null}, {"codeValue": "10-07-03", "codeName": "无人机飞行异常报警", "fieldOrder": 3, "parentCodeValue": "10-07", "remark": null, "children": null}, {"codeValue": "10-07-04", "codeName": "巡检异常事件报警", "fieldOrder": 4, "parentCodeValue": "10-07", "remark": null, "children": null}]}, {"codeValue": "10-08", "codeName": "应急处置管理", "fieldOrder": 8, "parentCodeValue": "10", "remark": null, "children": [{"codeValue": "10-08-01", "codeName": "管网设施预警", "fieldOrder": 1, "parentCodeValue": "10-08", "remark": null, "children": null}, {"codeValue": "10-08-02", "codeName": "泵站安全预警", "fieldOrder": 2, "parentCodeValue": "10-08", "remark": null, "children": null}, {"codeValue": "10-08-03", "codeName": "污水监测预警", "fieldOrder": 3, "parentCodeValue": "10-08", "remark": null, "children": null}, {"codeValue": "10-08-04", "codeName": "降雨量预警", "fieldOrder": 4, "parentCodeValue": "10-08", "remark": null, "children": null}, {"codeValue": "10-08-05", "codeName": "火情预警", "fieldOrder": 5, "parentCodeValue": "10-08", "remark": null, "children": null}, {"codeValue": "10-08-06", "codeName": "应急能力不足预警", "fieldOrder": 6, "parentCodeValue": "10-08", "remark": null, "children": null}]}, {"codeValue": "10-09", "codeName": "能源能耗", "fieldOrder": 9, "parentCodeValue": "10", "remark": null, "children": [{"codeValue": "10-09-01", "codeName": "能耗设备故障", "fieldOrder": 1, "parentCodeValue": "10-09", "remark": null, "children": null}, {"codeValue": "10-09-03", "codeName": "能耗超标异常报警", "fieldOrder": 3, "parentCodeValue": "10-09", "remark": null, "children": null}, {"codeValue": "10-09-04", "codeName": "能耗设备参数异常告警", "fieldOrder": 4, "parentCodeValue": "10-09", "remark": null, "children": null}]}, {"codeValue": "10-10", "codeName": "安全守护统计分析", "fieldOrder": 10, "parentCodeValue": "10", "remark": null, "children": [{"codeValue": "10-10-01", "codeName": "安防事件过多", "fieldOrder": 1, "parentCodeValue": "10-10", "remark": null, "children": null}, {"codeValue": "10-10-02", "codeName": "安防事件连续递增", "fieldOrder": 2, "parentCodeValue": "10-10", "remark": null, "children": null}, {"codeValue": "10-10-03", "codeName": "未处理防事件过多", "fieldOrder": 3, "parentCodeValue": "10-10", "remark": null, "children": null}, {"codeValue": "10-10-04", "codeName": "多个消火栓水压不足", "fieldOrder": 4, "parentCodeValue": "10-10", "remark": null, "children": null}, {"codeValue": "10-10-05", "codeName": "治安事件过多", "fieldOrder": 5, "parentCodeValue": "10-10", "remark": null, "children": null}, {"codeValue": "10-10-06", "codeName": "治安事件连续递增", "fieldOrder": 6, "parentCodeValue": "10-10", "remark": null, "children": null}, {"codeValue": "10-10-07", "codeName": "井盖连续异常", "fieldOrder": 7, "parentCodeValue": "10-10", "remark": null, "children": null}]}]}, {"codeValue": "11", "codeName": "视频中台", "fieldOrder": 11, "parentCodeValue": "-1", "remark": null, "children": [{"codeValue": "11-01", "codeName": "交通事件", "fieldOrder": 1, "parentCodeValue": "11", "remark": null, "children": [{"codeValue": "11-01-01", "codeName": "车辆违停", "fieldOrder": 1, "parentCodeValue": "11-01", "remark": null, "children": null}, {"codeValue": "11-01-02", "codeName": "异常停车事件", "fieldOrder": 2, "parentCodeValue": "11-01", "remark": null, "children": null}, {"codeValue": "11-01-03", "codeName": "车辆逆行", "fieldOrder": 3, "parentCodeValue": "11-01", "remark": null, "children": null}, {"codeValue": "11-01-04", "codeName": "非机动车闯禁", "fieldOrder": 4, "parentCodeValue": "11-01", "remark": null, "children": null}, {"codeValue": "11-01-05", "codeName": "行人闯禁", "fieldOrder": 5, "parentCodeValue": "11-01", "remark": null, "children": null}, {"codeValue": "11-01-06", "codeName": "交通事故", "fieldOrder": 6, "parentCodeValue": "11-01", "remark": null, "children": null}, {"codeValue": "11-01-07", "codeName": "交通拥堵", "fieldOrder": 7, "parentCodeValue": "11-01", "remark": null, "children": null}, {"codeValue": "11-01-08", "codeName": "路口打结", "fieldOrder": 8, "parentCodeValue": "11-01", "remark": null, "children": null}, {"codeValue": "11-01-09", "codeName": "机动车倒车", "fieldOrder": 9, "parentCodeValue": "11-01", "remark": null, "children": null}, {"codeValue": "11-01-10", "codeName": "机动车不按规定车道行驶", "fieldOrder": 10, "parentCodeValue": "11-01", "remark": null, "children": null}, {"codeValue": "11-01-11", "codeName": "机动车占用应急车道", "fieldOrder": 11, "parentCodeValue": "11-01", "remark": null, "children": null}, {"codeValue": "11-01-12", "codeName": "机动车加塞", "fieldOrder": 12, "parentCodeValue": "11-01", "remark": null, "children": null}, {"codeValue": "11-01-13", "codeName": "机动车骑/轧车行道分界线", "fieldOrder": 13, "parentCodeValue": "11-01", "remark": null, "children": null}, {"codeValue": "11-01-14", "codeName": "机动车超速", "fieldOrder": 14, "parentCodeValue": "11-01", "remark": null, "children": null}, {"codeValue": "11-01-15", "codeName": "非机动超速", "fieldOrder": 15, "parentCodeValue": "11-01", "remark": null, "children": null}, {"codeValue": "11-01-16", "codeName": "机动车连续变道", "fieldOrder": 16, "parentCodeValue": "11-01", "remark": null, "children": null}, {"codeValue": "11-01-17", "codeName": "机动车违章掉头", "fieldOrder": 17, "parentCodeValue": "11-01", "remark": null, "children": null}, {"codeValue": "11-01-18", "codeName": "非机动车占用机动车道", "fieldOrder": 18, "parentCodeValue": "11-01", "remark": null, "children": null}, {"codeValue": "11-01-19", "codeName": "机动车占用非机动车道", "fieldOrder": 19, "parentCodeValue": "11-01", "remark": null, "children": null}]}, {"codeValue": "11-02", "codeName": "交通流量", "fieldOrder": 2, "parentCodeValue": "11", "remark": null, "children": [{"codeValue": "11-02-01", "codeName": "平均车速", "fieldOrder": 1, "parentCodeValue": "11-02", "remark": null, "children": null}, {"codeValue": "11-02-02", "codeName": "车流量", "fieldOrder": 2, "parentCodeValue": "11-02", "remark": null, "children": null}]}, {"codeValue": "11-03", "codeName": "路面状况", "fieldOrder": 3, "parentCodeValue": "11", "remark": null, "children": [{"codeValue": "11-03-01", "codeName": "路面积水", "fieldOrder": 1, "parentCodeValue": "11-03", "remark": null, "children": null}, {"codeValue": "11-03-02", "codeName": "路面不洁", "fieldOrder": 2, "parentCodeValue": "11-03", "remark": null, "children": null}, {"codeValue": "11-03-03", "codeName": "路面抛撒物", "fieldOrder": 3, "parentCodeValue": "11-03", "remark": null, "children": null}, {"codeValue": "11-03-04", "codeName": "路面破损", "fieldOrder": 4, "parentCodeValue": "11-03", "remark": null, "children": null}]}, {"codeValue": "11-04", "codeName": "渣土车治理", "fieldOrder": 4, "parentCodeValue": "11", "remark": null, "children": [{"codeValue": "11-04-01", "codeName": "渣土车黑车识别", "fieldOrder": 1, "parentCodeValue": "11-04", "remark": null, "children": null}, {"codeValue": "11-04-02", "codeName": "渣土车辆六统一违规识别", "fieldOrder": 2, "parentCodeValue": "11-04", "remark": null, "children": null}, {"codeValue": "11-04-03", "codeName": "渣土车营运未苫盖识别", "fieldOrder": 3, "parentCodeValue": "11-04", "remark": null, "children": null}]}, {"codeValue": "11-05", "codeName": "道路扬尘治理", "fieldOrder": 5, "parentCodeValue": "11", "remark": null, "children": [{"codeValue": "11-05-01", "codeName": "扬尘区域检测", "fieldOrder": 1, "parentCodeValue": "11-05", "remark": null, "children": null}, {"codeValue": "11-05-02", "codeName": "二次扬尘识别", "fieldOrder": 2, "parentCodeValue": "11-05", "remark": null, "children": null}]}, {"codeValue": "11-06", "codeName": "黑烟排放监测", "fieldOrder": 6, "parentCodeValue": "11", "remark": null, "children": [{"codeValue": "11-06-01", "codeName": "柴油货车车辆检测", "fieldOrder": 1, "parentCodeValue": "11-06", "remark": null, "children": null}, {"codeValue": "11-06-02", "codeName": "黑烟区域检测", "fieldOrder": 2, "parentCodeValue": "11-06", "remark": null, "children": null}, {"codeValue": "11-06-03", "codeName": "黑烟黑度识别", "fieldOrder": 3, "parentCodeValue": "11-06", "remark": null, "children": null}, {"codeValue": "11-06-04", "codeName": "黑烟车车牌识别", "fieldOrder": 4, "parentCodeValue": "11-06", "remark": null, "children": null}]}, {"codeValue": "11-07", "codeName": "街道环境秩序治理", "fieldOrder": 7, "parentCodeValue": "11", "remark": null, "children": [{"codeValue": "11-07-01", "codeName": "垃圾乱堆放识别", "fieldOrder": 1, "parentCodeValue": "11-07", "remark": null, "children": null}, {"codeValue": "11-07-02", "codeName": "机动车乱停放识别", "fieldOrder": 2, "parentCodeValue": "11-07", "remark": null, "children": null}, {"codeValue": "11-07-03", "codeName": "店外经营识别", "fieldOrder": 3, "parentCodeValue": "11-07", "remark": null, "children": null}, {"codeValue": "11-07-04", "codeName": "游商摊贩识别", "fieldOrder": 4, "parentCodeValue": "11-07", "remark": null, "children": null}, {"codeValue": "11-07-05", "codeName": "非机动车聚集识别", "fieldOrder": 5, "parentCodeValue": "11-07", "remark": null, "children": null}, {"codeValue": "11-07-06", "codeName": "人员绿地踩踏", "fieldOrder": 6, "parentCodeValue": "11-07", "remark": null, "children": null}, {"codeValue": "11-07-07", "codeName": "占道经营", "fieldOrder": 7, "parentCodeValue": "11-07", "remark": null, "children": null}, {"codeValue": "11-07-08", "codeName": "非法小广告", "fieldOrder": 8, "parentCodeValue": "11-07", "remark": null, "children": null}, {"codeValue": "11-07-09", "codeName": "积存垃圾渣土", "fieldOrder": 9, "parentCodeValue": "11-07", "remark": null, "children": null}, {"codeValue": "11-07-10", "codeName": "公共设施损坏", "fieldOrder": 10, "parentCodeValue": "11-07", "remark": null, "children": null}, {"codeValue": "11-07-11", "codeName": "水域秩序", "fieldOrder": 11, "parentCodeValue": "11-07", "remark": null, "children": null}, {"codeValue": "11-07-12", "codeName": "乱搭乱放", "fieldOrder": 12, "parentCodeValue": "11-07", "remark": null, "children": null}, {"codeValue": "11-07-13", "codeName": "道路垃圾识别", "fieldOrder": 13, "parentCodeValue": "11-07", "remark": null, "children": null}, {"codeValue": "11-07-14", "codeName": "建筑工地土方未苫盖识别", "fieldOrder": 14, "parentCodeValue": "11-07", "remark": null, "children": null}]}, {"codeValue": "11-08", "codeName": "行为监管类", "fieldOrder": 8, "parentCodeValue": "11", "remark": null, "children": [{"codeValue": "11-08-01", "codeName": "越界(绊线)检测", "fieldOrder": 1, "parentCodeValue": "11-08", "remark": null, "children": null}, {"codeValue": "11-08-02", "codeName": "区域入侵", "fieldOrder": 2, "parentCodeValue": "11-08", "remark": null, "children": null}, {"codeValue": "11-08-03", "codeName": "区域离开", "fieldOrder": 3, "parentCodeValue": "11-08", "remark": null, "children": null}, {"codeValue": "11-08-04", "codeName": "区域停留", "fieldOrder": 4, "parentCodeValue": "11-08", "remark": null, "children": null}, {"codeValue": "11-08-05", "codeName": "值岗（离岗）检测", "fieldOrder": 5, "parentCodeValue": "11-08", "remark": null, "children": null}, {"codeValue": "11-08-06", "codeName": "逆行检测", "fieldOrder": 6, "parentCodeValue": "11-08", "remark": null, "children": null}, {"codeValue": "11-08-07", "codeName": "攀高检测", "fieldOrder": 7, "parentCodeValue": "11-08", "remark": null, "children": null}, {"codeValue": "11-08-08", "codeName": "起身检测（离床）", "fieldOrder": 8, "parentCodeValue": "11-08", "remark": null, "children": null}, {"codeValue": "11-08-09", "codeName": "人员逗留（徘徊）检测", "fieldOrder": 9, "parentCodeValue": "11-08", "remark": null, "children": null}, {"codeValue": "11-08-10", "codeName": "单人独处", "fieldOrder": 10, "parentCodeValue": "11-08", "remark": null, "children": null}]}, {"codeValue": "11-09", "codeName": "行为安全类", "fieldOrder": 9, "parentCodeValue": "11", "remark": null, "children": [{"codeValue": "11-09-01", "codeName": "玩手机检测", "fieldOrder": 1, "parentCodeValue": "11-09", "remark": null, "children": null}, {"codeValue": "11-09-02", "codeName": "睡岗检测", "fieldOrder": 2, "parentCodeValue": "11-09", "remark": null, "children": null}, {"codeValue": "11-09-03", "codeName": "物品搬移", "fieldOrder": 3, "parentCodeValue": "11-09", "remark": null, "children": null}, {"codeValue": "11-09-04", "codeName": "物品遗留", "fieldOrder": 4, "parentCodeValue": "11-09", "remark": null, "children": null}, {"codeValue": "11-09-05", "codeName": "口罩检测", "fieldOrder": 5, "parentCodeValue": "11-09", "remark": null, "children": null}, {"codeValue": "11-09-06", "codeName": "安全帽检测", "fieldOrder": 6, "parentCodeValue": "11-09", "remark": null, "children": null}, {"codeValue": "11-09-07", "codeName": "工作服检测", "fieldOrder": 7, "parentCodeValue": "11-09", "remark": null, "children": null}, {"codeValue": "11-09-08", "codeName": "吸烟检测", "fieldOrder": 8, "parentCodeValue": "11-09", "remark": null, "children": null}, {"codeValue": "11-09-09", "codeName": "打电话检测", "fieldOrder": 9, "parentCodeValue": "11-09", "remark": null, "children": null}]}, {"codeValue": "11-10", "codeName": "人群统计类", "fieldOrder": 10, "parentCodeValue": "11", "remark": null, "children": [{"codeValue": "11-10-01", "codeName": "区域人数统计", "fieldOrder": 1, "parentCodeValue": "11-10", "remark": null, "children": null}, {"codeValue": "11-10-02", "codeName": "绊线人数统计", "fieldOrder": 2, "parentCodeValue": "11-10", "remark": null, "children": null}, {"codeValue": "11-10-03", "codeName": "人群聚集", "fieldOrder": 3, "parentCodeValue": "11-10", "remark": null, "children": null}, {"codeValue": "11-10-04", "codeName": "人群发散", "fieldOrder": 4, "parentCodeValue": "11-10", "remark": null, "children": null}]}, {"codeValue": "11-11", "codeName": "特殊事件类", "fieldOrder": 11, "parentCodeValue": "11", "remark": null, "children": [{"codeValue": "11-11-01", "codeName": "剧烈运动（打架）", "fieldOrder": 1, "parentCodeValue": "11-11", "remark": null, "children": null}, {"codeValue": "11-11-02", "codeName": "跌倒检测", "fieldOrder": 2, "parentCodeValue": "11-11", "remark": null, "children": null}, {"codeValue": "11-11-03", "codeName": "消防通道阻塞", "fieldOrder": 3, "parentCodeValue": "11-11", "remark": null, "children": null}, {"codeValue": "11-11-04", "codeName": "消防车通道占用", "fieldOrder": 4, "parentCodeValue": "11-11", "remark": null, "children": null}, {"codeValue": "11-11-05", "codeName": "电瓶车禁入（电瓶车的区域入侵）", "fieldOrder": 5, "parentCodeValue": "11-11", "remark": null, "children": null}, {"codeValue": "11-11-06", "codeName": "烟火检测", "fieldOrder": 6, "parentCodeValue": "11-11", "remark": null, "children": null}]}]}]}]