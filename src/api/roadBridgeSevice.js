import request from '@/utils/request';
// 新增道路
export function roadNewAdd(data) {
  return request({
    url: '/trafficRoad',
    method: 'post',
    data
  })
}
// 编辑道路
export function roadEdit(data) {
  return request({
    url: '/trafficRoad',
    method: 'put',
    data
  })
}
// 删除道路
export function roadDelete(data) {
  return request({
    url: `/trafficRoad?idList=${data}`,
    method: 'delete',
  })
}
// 编辑桥梁
export function bridgeEdit(data) {
  return request({
    url: '/trafficBridge',
    method: 'put',
    data
  })
}
// 桥梁新增
export function bridgeNewAdd(data) {
  return request({
    url: '/trafficBridge',
    method: 'post',
    data
  })
}
// 桥梁删除
export function bridgeDelete(data) {
  return request({ url: `/trafficBridge?idList=${data}`, method: 'delete' })
}
// 获取区域树
export function getAreaLocationTree(type, table) {
  return request({ url: `/deviceAreaTree/getTree?type=${type}&table=${table}`, method: 'get' })
}
// 获取对应区域设施
export function roadBridgeMapList(data) {
    return request({ url: '/roadBridgeMap/list', method: 'post', data })
}
// 获取对应设施详情
export function roadBridgeMapDetailInfo(data) {
    return request({ url: '/roadBridgeMap/detailInfo', method: 'post', data })
}
// 获取区域设施概览
export function roadBridgeMapCreaCount(data) {
    return request({ url: '/roadBridgeMap/areaCount', method: 'post', data })
}
// 道桥等级
export function countGroupByGrade(data) {
    return request({ url: '/roadBridgeMap/countGroupByGrade', method: 'post', data })
}
// 道桥护养
export function maintenanceTrendCount(data) {
    return request({ url: '/roadBridgeMap/maintenanceTrendCount', method: 'post', data })
}
// 道路部件码获取
export function roadGetValidObjInfo(data) {
  return request({
    url: '/trafficRoad/getValidObjInfo',
    method: 'post',
    data
  })
}
// 桥梁部件码获取
export function bridgeGetValidObjInfo(data) {
  return request({
    url: '/trafficBridge/getValidObjInfo',
    method: 'post',
    data
  })
}
// 按照等级统计道路/桥梁的数量与长度
export function staticsticalByFacilityType(facilityType) {
    return request({
        url: `/roadBridgeStatistical/staticsticalByFacilityType/${facilityType}`,
        method: 'POST'
    })
}
// 按照类型统计养护监测次数与花费
export function staticsticalMaintenanceAndReportByFacilityType(facilityType) {
    return request({
        url: `/roadBridgeStatistical/staticsticalMaintenanceAndReportByFacilityType/${facilityType}`,
        method: 'GET',
    })
}
// 按照类型统计花费排行(总)
export function staticsticalMaintenanceAndReportRank(facilityType) {
    return request({
        url: `/roadBridgeStatistical/staticsticalMaintenanceAndReportRank/${facilityType}`,
        method: 'GET',
    })
}
// 按照类型统计花费排行(平均)
export function staticsticalAvgMaintenanceAndReportRank(facilityType) {
    return request({
        url: `/roadBridgeStatistical/staticsticalAvgMaintenanceAndReportRank/${facilityType}`,
        method: 'GET',
    })
}

// 按照类型统计去年与今年的养护检测趋势
export function staticsticalMaintenanceAndReportByMonth() {
    return request({
        url: `/roadBridgeStatistical/staticsticalMaintenanceAndReportByMonth`,
        method: 'GET',
    })
}
// 道路分页
export function getRoadList(objId) {
  return request({
    url: '/trafficRoad/getPage',
    method: 'post',
   data: {
      'page': {
          'current': 0,
          'size': 5
      },
      'customQueryParams': {
          'objInfo': {
              'objId': objId,
          }
      }
  }
  })
}



// 按照类型统计建成率
export function statisticalCompletionPart(facilityType) {
  return request({
      url: `/roadBridgeStatistical/statisticalCompletionPart/${facilityType}`,
      method: 'GET',
  })
}
