import request from '@/utils/request';
/*
* hideMsg: 是否隐藏错误提示框，默认不隐藏
* */
export default function (url, data, method = 'get', headers = {}, responseType, hideMsg = false) {
    const req = {
        url,
        method,
        headers: Object.assign({ 'X-Requested-With': 'XMLHttpRequest' }, headers),
        responseType,
        hideMsg
    }
    if (method === 'get' || method === 'delete') {
        req.params = data
    } else if (method === 'deleteP') {
        req.method = 'delete'
        req.data = data
    } else {
        req.data = data
    }
    return request(req)
}
