
import request from '@/utils/request';
import { ListAllReqBody } from '../common';
import { deepClone } from 'wei-util';

// 智慧停车接口
export const wisdomParkingService = {
    /**
     * 今日收费统计
     */
    getDailyStatistics(data) {
        return request({
            url: '/feeStatistics/dailyStatistics',
            method: 'post',
            data
        })
    },
    /**
     * 收费统计管理曲线
     */
    feeManagementTrends(data) {
        return request({
            url: '/feeStatistics/feeManagementTrends',
            method: 'post',
            data
        })
    },
    /**
     * 查询车位使用趋势
     */
    selectParkingSpaceUsePeriod(data) {
        return request({
            url: '/trafficParkingSpaceOverview/selectParkingSpaceUsePeriod',
            method: 'post',
            data
        })
    },
    /**
     * 查询车位空余信息
     */
    selectParkingSpaceUseInfo(data) {
        return request({
            url: '/trafficParkingSpaceOverview/selectParkingSpaceUseInfo',
            method: 'post',
            data
        })
    },
    /**
     * 查询车位使用排行
     */
    selectParkingSpaceUseRank(data) {
        return request({
            url: '/trafficParkingSpaceOverview/selectParkingSpaceUseRank',
            method: 'post',
            data
        })
    },
    /**
     * 查询车位使用时长分布
     */
    selectParkingSpaceUseDistribution(data) {
        return request({
            url: '/trafficParkingSpaceOverview/selectParkingSpaceUseDistribution',
            method: 'post',
            data
        })
    },
    /**
     * 查询车位使用时长分布
     */
    selectSumUseDistribution(data) {
        return request({
            url: '/trafficParkingSpaceOverview/selectSumUseDistribution',
            method: 'post',
            data
        })
    },
    /**
     * 分页查询停车场列表
     */
    getParkingPage(data) {
        return request({
            url: '/trafficParkingLot/getPage',
            method: 'post',
            data
        })
    },
    /**
     * 得到所有停车场列表
     */
    getParkingList() {
        return request({
            url: '/trafficParkingLot/getPage',
            method: 'post',
            data: {
                'customQueryParams': {
                },
                'page': {
                    'current': 1,
                    'size': -1
                }
            }
        })
    },
    /**
     * 得到所有停车场所有楼层列表
     */
    getFloorAllList(data = {}) {
        return request({
            url: '/magneticDevice/relatedFloors',
            method: 'post',
            data
        })
    },
    /**
     * 得到停车场楼层列表
     */
    getParkingFloorList(parkingLotId) {
        return request({
            url: '/trafficParkingLotFloor/getPage',
            method: 'post',
            data: {
                'customQueryParams': {
                    parkingLotId: parkingLotId
                },
                'page': {
                    'current': 1,
                    'size': -1
                }
            }
        })
    },
    // 收费汇总统计
    feeStatisticsSummary(data) {
        return request({
            url: '/feeStatistics/summary',
            method: 'post',
            data
        })
    },
    // 收费趋势
    feeStatisticsTrends(data) {
        return request({
            url: '/feeStatistics/trends',
            method: 'post',
            data
        })
    },
    // 收费排名
    feeStatisticsRank(data) {
        return request({
            url: '/feeStatistics/rank',
            method: 'post',
            data
        })
    },
    // 收费与停车分析
    feeStatisticsFeeAndPark(data) {
        return request({
            url: '/feeStatistics/feeAndPark',
            method: 'post',
            data
        })
    },
    // 收费来源
    feeStatisticsFeePart(data) {
        return request({
            url: '/feeStatistics/feePart',
            method: 'post',
            data
        })
    },
    // 收费明细(列表数据)
    feeStatisticsCountTable(data) {
        return request({
            url: '/feeStatistics/countTable',
            method: 'post',
            data
        })
    },
    // 列表数据导出
    feeStatisticsExport(data) {
        return request({
            url: '/feeStatistics/countTable/export',
            method: 'post',
            data
        })
    },
    // 24小时停车数与进出车辆趋势
    selectDayUseInfo(data) {
        return request({
            url: '/trafficParkingSpaceOverview/selectDayUseInfo',
            method: 'post',
            data
        })
    },
    /**
     * 得到所有泊位场列表
     */
    getParkingLotSpaceList(data) {
        return request({
            url: '/parkingLotSpace/getList',
            method: 'post',
            data
        })
    },
    // 新增成本
    addCost(data) {
        return request({
            url: '/trafficParking/cost/add',
            method: 'post',
            data
        })
    }
}
// 黑名单
/**
 * 新增黑名单
 * @param {*} data
 * @returns
 */
function addBlacklist(data) {
    return request({
        url: '/trafficInternalCar',
        method: 'post',
        data
    })
}
/**
 * 获取黑名单详情信息
 * @param {*} id
 * @returns
 */
function getBlacklistDetail(id) {
    return request({
        url: `/trafficInternalCar/${id}`,
        method: 'get',
    })
}
/**
 * 编辑黑名单
 * @param {*} data
 * @returns
 */
function editBlackList(data) {
    return request({
        url: '/trafficInternalCar',
        method: 'put',
        data
    })
}
/**
 * 删除黑名单
 * @param {*} idList
 * @returns
 */
function deleteBlackList(idList) {
    return request({
        url: `/trafficInternalCar?idList=${idList}`,
        method: 'delete',
    })
}
/**
 * 获取所有停车场列表
 */
function getAllParkingList() {
    return wisdomParkingService.getParkingPage(ListAllReqBody)
}
/**
 * 获取所有白名单列表
 * @returns
 */
function getAllWhiteList() {
    return request({
        url: '/trafficInternalCar/getPage',
        method: 'post',
        data: ListAllReqBody
    })
}
/**
 * 获取所有黑名单列表
 * @returns
 */
function getAllBlackList() {
    const data = deepClone(ListAllReqBody)
    data.customQueryParams.carType = '3'
    return request({
        url: '/trafficInternalCar/getPage',
        method: 'post',
        data
    })
}

// 违停记录
/**
 * 违停记录详情
 * @param {*} id
 * @returns
 */
function getParkingIllegalRecordDetail(id) {
    return request({
        url: `/parkingIllegalRecord/${id}`,
        method: 'get',
    })
}

// 停车场分析
/**
 * 汇总统计
 * @param {*} data
 * @returns
 */
function getSummary(data) {
    return request({
        url: '/parkingStatistics/summary',
        method: 'post',
        data
    })
}
/**
 * 汇总统计
 * @param {*} data
 * @returns
 */
function getDayParkingData(data) {
    return request({
        url: '/parkingStatistics/getDayParkingData',
        method: 'post',
        data
    })
}
/**
 * 汇总统计
 * @param {*} data
 * @returns
 */
function getCurrentMonthParking(data) {
    return request({
        url: '/parkingStatistics/getCurrentMonthParkingData',
        method: 'post',
        data: data || {}

    })
}
/**
 * 停车位曲线
 * @param {*} data
 * @returns
 */
function getParkingSpaceData(data) {
    return request({
        url: '/parkingStatistics/parkingSpace',
        method: 'post',
        data
    })
}
/**
 * 停车率排名
 * @param {*} data
 * @returns
 */
function getParkingRank(data) {
    return request({
        url: '/parkingStatistics/parkingRank',
        method: 'post',
        data
    })
}
/**
 * 获取车流量曲线
 * @param {*} data
 * @returns
 */
function getCarFlow(data) {
    return request({
        url: '/parkingStatistics/carFlow',
        method: 'post',
        data
    })
}
/**
 * 获取车流量排名
 * @param {*} data
 * @returns
 */
function getCarFlowRank(data) {
    return request({
        url: '/parkingStatistics/carFlowRank',
        method: 'post',
        data
    })
}
/**
 * 获取1停车时长分布
 * @param {*} data
 * @returns
 */
function getParkingTime(data) {
    return request({
        url: '/parkingStatistics/parkingTime',
        method: 'post',
        data
    })
}
/**
 * 获取平均停车时长曲线
 * @param {*} data
 * @returns
 */
function getAvgParkingTime(data) {
    return request({
        url: '/parkingStatistics/avgParkingTime',
        method: 'post',
        data
    })
}

export {
    addBlacklist,
    getBlacklistDetail,
    editBlackList,
    deleteBlackList,
    getAllParkingList,
    getAllWhiteList,
    getAllBlackList,
    getParkingIllegalRecordDetail,
    getSummary,
    getParkingSpaceData,
    getParkingRank,
    getCarFlow,
    getCarFlowRank,
    getParkingTime,
    getAvgParkingTime,
    getDayParkingData,
    getCurrentMonthParking
}
