import request from "@/utils/request";

/**
 * 智慧畅行
 */
// common
// 获取保存期限
export function getSaveDeadline(type:number) {
    return request({
        url: '/passCommon/term',
        method: "get",
        params:{
            type
        }
    });
}
// 修改保存期限
interface SaveDeadline {
    term?: number;
    type?: number;
  }
export function editSaveDeadline(data:SaveDeadline) {
    return request({
        url: '/passCommon/term',
        method: "put",
        data
    });
}

/**
 * 路口管理管理
 */
// 信号灯详情
export function getPassLampDetail(id: string) {
    return request({
        url: `/passLamp/${id}`,
        method: "get",
    });
}
/**
 * 路口自适应
 */
//自适应详情
export function getAutoEventsDetail(id: string) {
    return request({
        url: `/passIntersection/${id}`,
        method: "get",
    });
}
/**
 * 电警违规
 */
//电警违规详情
export function getElectricDetail(id: string) {
    return request({
        url: `/passViolation/${id}`,
        method: "get",
    });
}
// 信号灯列表
export function getPassLampList(data: recordsRequest) {
    return request({
        url: "/passLamp/list",
        method: "post",
        data,
    });
}
