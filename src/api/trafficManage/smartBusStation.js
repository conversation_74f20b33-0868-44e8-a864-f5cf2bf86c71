import request from '@/utils/request';

export const smartBusStation = {
    // 智慧公交站
    // 分页查询列表数据
    getInfoForMap(data) {
        return request({
            url: '/trafficBusStop/getInfoForMap',
            method: 'post',
            data,
        });
    },
    // 分页查询列表数据
    getTrafficBusStop(data) {
        return request({
            url: '/trafficBusStop/getPage',
            method: 'post',
            data,
        });
    },
    // 新增
    addtrafficBusStop(data = {}) {
        return request({
            url: `/trafficBusStop`,
            method: 'post',
            data,
        });
    },
    // 编辑
    edittrafficBusStop(data = {}) {
        return request({
            url: `/trafficBusStop`,
            method: 'put',
            data,
        });
    },
    // 批量删除
    deletestation(idList) {
        return request({
            url: `/trafficBusStop?idList=${idList}`,
            method: 'delete',
        });
    },
    /**
     * 分页查询公交站点列表
     */
    getBusStopPage(data) {
        return request({
            url: '/trafficBusStop/getPage',
            method: 'post',
            data
        })
    },
    /**
     * 通过站点id得到站点的线路信息
     */
    getLineInfoByBusStopId(data) {
        return request({
            url: '/trafficBusRoute/getBusInfoMonitor',
            method: 'post',
            data
        })
    },
    // 查询公交站点
    gettrafficBusStop(id) {
        return request({
            url: `/trafficBusStop/${id}`,
            method: 'get'
        })
    },
    // 查询公交站点广告信息
    gettrafficBusStopAd(objId) {
        return request({
            url: `/trafficBusStopAd/objId/${objId}`,
            method: 'get'
        })
    },
    operateVideo(data) {
        return request({
            url: `/busVideoMonitor/operateVideo`,
            method: 'POST',
            data
        })
    },
    getBusStopVideoInfo(data) {
        return request({
            url: `/busVideoMonitor/getBusStopVideoInfo`,
            method: 'POST',
            data
        })
    },
    topStatisticsData(data) {
        return request({
            url: `/busStopSnapshot/topStatisticsData`,
            method: 'POST',
            data
        })
    },
    // 统计分析
    topAnalyzeData(data) {
        return request({
            url: `/busStopStatisticalAnalyze/topAnalyzeData`,
            method: 'POST',
            data
        })
    },
    // 公交车分布
    busDistrPieChartData(data) {
        return request({
            url: `/busStopStatisticalAnalyze/busDistrPieChartData`,
            method: 'POST',
            data
        })
    },
    // 抓拍监控分布
    busSnapshotDistrChartData(data) {
        return request({
            url: `/busStopStatisticalAnalyze/busSnapshotDistrChartData`,
            method: 'POST',
            data
        })
    },
    // 抓拍监控趋势图
    busSnapshotTrendData(data) {
        return request({
            url: `/busStopStatisticalAnalyze/busSnapshotTrendData`,
            method: 'POST',
            data
        })
    },

};

/**
 * @Description: 公交线路接口
 */
export class BusLineListService {
    // 分页查询列表数据
    getTrafficBusRoute(data) {
        return request({
            url: 'trafficBusRoute/getPage',
            method: 'post',
            data,
        });
    }
    // 增
    add(data) {
        return request({
            url: `/trafficBusRoute`,
            method: 'POST',
            data
        })
    }
    // 删
    delete(idList) {
        return request({
            url: `/trafficBusRoute`,
            method: 'DELETE',
            params: {
                idList
            }
        })
    }
    // 改
    edit(data) {
        return request({
            url: '/trafficBusRoute',
            method: 'PUT',
            data
        })
    }
    // 查
    getDetail(id) {
        return request({
            url: `/trafficBusRoute/${id}`,
            method: 'GET'
        })
    }
    // 公交线路 -- 启用 -- 停用
    updateUseStatus(data) {
        return request({
            url: `/trafficBusRoute/updateUseStatus`,
            method: 'PUT',
            data
        })
    }
}

/* 数据统计分析 */
export const dataStatistics = {
  // 站台总数（当前）
  getNumCount() {
      return request({
          url: `/busStopDataStatistical/numCount`,
          method: 'GET'
      })
  },
  // 今年新增站台
  currentYearCount(data) {
      return request({
          url: `/busStopDataStatistical/currentYearCount`,
          method: 'POST',
          data
      })
  },
  // 今年人流量
  currentYearPedestrianFlowCount(data) {
      return request({
          url: `/busStopDataStatistical/currentYearPedestrianFlowCount`,
          method: 'POST',
          data
      })
  },
  // 告警分类统计
  alarmTypeCount(data) {
      return request({
          url: `/busStopDataStatistical/alarmTypeCount`,
          method: 'POST',
          data
      })
  },
  // 告警排行图
  alarmRank(data) {
      return request({
          url: `/busStopDataStatistical/alarmRank`,
          method: 'POST',
          data
      })
  },
  // 告警情况柱状图
  alarmCount(data) {
      return request({
          url: `/busStopDataStatistical/alarmCount`,
          method: 'POST',
          data
      })
  },
  // 告警处理情况对比图
  alarmHandleCount(data) {
      return request({
          url: `/busStopDataStatistical/alarmHandleCount`,
          method: 'POST',
          data
      })
  },
  // 最新异常日志
  alarmInfoPage(data) {
      return request({
          url: `/trafficBusAlarmExtend/getPage`,
          method: 'POST',
          data
      })
  }
}
