import request from "@/utils/request";
import { streetlightInfo } from "./streetlightMapType";
export const smartStreetLampService = {
    /**
     * 根据区域查询路灯在线统计
     */
    online(data: streetlightInfo) {
        return request({
            url: "/deviceStatistics/online",
            method: "post",
            data,
        });
    },
    /**
     * 根据区域查询路灯在线统计
     */
    day(data: streetlightInfo) {
        return request({
            url: "/deviceStatistics/day",
            method: "post",
            data,
        });
    },
    /**
     * 根据条件，分页(不分页)查询
     */
    list(data: recordsRequest) {
        return request({
            url: "/streetlight/list",
            method: "post",
            data,
        });
    },
    /**
     * 根据条件，分页(不分页)查询
     */
    alarms(data: streetlightInfo) {
        return request({
            url: "/streetlightMap/alarms",
            method: "post",
            data,
        });
    },
    /**
     * 根据id查询路灯详情
     */
    streetlightById(id: number) {
        return request({
            url: `/streetlight/${id}`,
            method: "get",
        });
    },
    /**
     * 根据id查询路灯详情
     */
    streetlightMapAlarm(params: any) {
        return request({
            url: "/streetlightMap/alarm",
            method: "get",
            params,
        });
    },
};
