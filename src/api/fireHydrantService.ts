import request from '@/utils/request';

// 设备汇总数据
export interface creaCountInfo {
    roadTotalNum?: number;
    roadTotalLength?: number;
    totalMain?: number;
    totalDetect?: number;
}
// 设备概览
export interface creaCountResInfo {
    countKey: string;
    countName: string;
    countValue: number;
}
// 公共部件对象
export interface ComponentInfo {
    id?: number;
    deviceId?: string;
    sbmc?: string; // 设备名称
    bsm?: string; // 部件标识码
    status?: number; // 状态
    useStatus?: number; // 部件使用状态
    objId?: string; // 部件ID
    sbxh?: string; // 设备型号
    contactPhone?: string; // 联系电话
    contactPerson?: string; // 联系人
    alarmState?: number; // 告警状态
    deviceSecondTypeName?: string; // 设备类型
    objState?: number; // 部件状态
    secondObjCategoryName?: string; // 部件类型
    initDate?: string; // 安装时间
    modifyDate?: string; // 修改时间
    remark?: string; // 备注
    sbzt?: string; // 设备状态
    deviceCode?: string; // 设备编号
    objName?: string;
    szjd?: string;// 所在街道
    szsq?: string;// 所在社区
    szdywg?: string;// 所在单元网格
    ownerEnterpriseName?: string;
    opEnterpricseName?: string;
    deptName?: string;
    areaPath?: string;
    objX?: number;
    objY?: number;
    gdx?: number;
    gdy?: number;
}
// 设施信息
export interface facilityInfo {
    id: number;
    maintenanceUnit?: string;
    length?: number;
    grade?: number;
    designYear?: number;
    totalArea?: number;
    roadGrade?: string;
}
// 部件对象数组
export interface installationInfo {
    objInfo: ComponentInfo;
    facilityInfo: facilityInfo;
    lastDetectTime?: string;
    lastMaintenanceTime?: string;
    maintenanceNum?: number;
    detectNum?: number;
}
/**
 *  消防栓数据
 */
// 消防栓在线
export interface FireHydrantOnline {
    sum: number;
    online: number;
    recordTime?: string;
}
export interface FireHydrantInfo {
    input?: string;
    objInfo?: ComponentInfo;
    extendInfo?: ComponentInfo;
    devicePropertyStatusList?: devicePropertyStatus[];
    status?: number;
    useStatus?: number;
    deviceCode?: string; // 设备编码
    id?: number;
}
// 消防栓设备状态
export interface devicePropertyStatus {
    name: string;
    value: string;
    propName?: string;
    prop?: string;
}
// 警告告警对象
export interface FireHydrantAlarm {
    alarmTime: string;
    status: number;
    sbmc?: string;
    content?: string;
    code?: string; // 告警编号
    level?: number; // 告警级别
    pushStatus?: number; // 推送状态
    areaPath?: string; // 区域路径
    alarmType?: number; // 告警类型
}
// 消防栓告警数据
export interface FireHydrantAlarmList {
    alarm: number;
    alarmTotal: number;
    alarmList: FireHydrantAlarm[];
}
// 区域树
export interface areaTree {
    value: string;
    label: string;
    code: string;
    title?: string;
    key?: string;
    checked?: boolean;
    expand?: boolean;
    children?: areaTree[];
}
export interface treeData {
    id: string,
    value: string;
    title: string;
    selected?: boolean,
    checked?: boolean;
    expand?: boolean;
    children?: treeData[];
}
// 对外暴露
export const fireHydrantService = {
    /**
     * 获取区域树
     */
    fireHydrantAreaTree(params: { table:string, type: string }) {
        return request({
            url: '/deviceAreaTree/getTree',
            method: 'get',
            params
        });
    },
    /**
     * 条件，分页(不分页)查询消防栓
     */
    fireHydrantList(data: recordsRequest) {
        return request({
            url: '/fireHydrant/list',
            method: 'post',
            data
        });
    },
    /**
     * 根据区域查询消防栓在线统计
     */
    fireHydrantMapOnline(data: Object) {
        return request({
            url: '/deviceStatistics/online',
            method: 'post',
            data
        });
    },
    /**
     * 根据区域查询消防栓在线情况
     */
    fireHydrantMapDay(data: Object) {
        return request({
            url: '/deviceStatistics/day',
            method: 'post',
            data
        });
    },
    /**
     * 根据区域查询告警情况
     */
    fireHydrantMapAlarms(data: Object) {
        return request({
            url: '/fireHydrantMap/alarms',
            method: 'post',
            data
        });
    },
    /**
     * 根据区域查询告警情况
     */
    fireHydrantDetail(id: number) {
        return request({
            url: `/fireHydrant/${id}`,
            method: 'get'
        });
    },
    /**
     * 根据设备编码查询消防栓告警日志
     */
    fireHydrantMapAlarm(params: { deviceCode: string }) {
        return request({
            url: '/fireHydrantMap/alarm',
            method: 'get',
            params
        });
    },
    /**
     * 根据设备编码查询设备信息
     */
    getDeviceByCode(deviceCode: string) {
        return request({
            url: `/fireHydrant/device/${deviceCode}`,
            method: 'get'
        });
    },
    /**
     * 消防栓新增（post），编辑（put），删除（delete）
     */
    fireHydrantAction(data: Object, method: string) {
        return request({
            url: `/fireHydrant`,
            method: method,
            data
        });
    },
    /**
     * 消防栓批量新增（post）
     */
    addFireHydrants(data: Object) {
        return request({
            url: `/fireHydrant/batch`,
            method: 'post',
            data
        });
    },
    /**
     * 根据id查询消防栓详情
     */
    getFireHydrantDetailById(id: string) {
        return request({
            url: `/fireHydrant/${id}`,
            method: 'get'
        });
    },
    editFireHydrant(data: FireHydrantInfo) {
        return request({
            url: '/fireHydrant',
            method: 'put',
            data
        });
    },
    getAlarmDetail(id: String) {
        return request({
            url: `/fireHydrantAlarm/${id}`,
            method: 'get'
        });
    },
    /**
     * 根据id查询消防栓详情
     */
    getFireHydrantAlarmTypeList(data: any) {
        return request({
            url: '/monitor/getPhysicModel',
            method: 'post',
            data
        });
    },
    alarmCountListStatus(data: any) {
        return request({
            url: '/fireHydrantAlarm/countListStatus',
            method: 'post',
            data
        });
    },
};
