import request from "@/utils/request";
import { DevicePropertyStatus } from '@/views/safeManage/wisdomManholeCover/manholeCoverManage/data/type'

// 设备汇总数据
export interface creaCountInfo {
    roadTotalNum?: number;
    roadTotalLength?: number;
    totalMain?: number;
    totalDetect?: number;
}
// 设备概览
export interface creaCountResInfo {
    countKey: string;
    countName: string;
    countValue: number;
}
// 公共部件对象
export interface ComponentInfo {
    type?: number;
    checked?: boolean;
    licenseNo?: string;
    id?: number;
    deviceId?: string;
    name?: string;
    sbmc?: string; // 设备名称
    bsm?: string; // 部件code
    status?: number; // 状态
    useStatus?: number; // 部件使用状态
    firstObjCategoryName?: string; // 部件类型名称
    objId?: string; // 部件ID
    sbxh?: string; // 设备型号
    contactPhone?: string; // 联系电话
    contactPerson?: string; // 联系人
    alarmState?: number; // 告警状态
    deviceSecondTypeName?: string; // 设备类型
    objState?: number; // 部件状态
    secondObjCategoryName?: string; // 部件类型
    initDate?: string; // 安装时间
    modifyDate?: string; // 修改时间
    remark?: string; // 备注
    sbzt?: string; // 设备状态
    deviceCode?: string; // 设备编号
    switchState?: string; // 开关状态
    light?: string; // 灯光状态
    objName?: string;
    szdywg?: string;
    szjd?: string;
    szsq?: string;
    ownerEnterpriseName?: string;
    opEnterpricseName?: string;
    deptName?: string;
    areaPath?: string;
    objX?: number;
    objY?: number;
    geometry?: string;
    gdx?: number;
    gdy?: number;
    pipeType?: number;
    lastPushTime?: string;
}
// 设施信息
export interface facilityInfo {
    id: number;
    maintenanceUnit?: string;
    length?: number;
    grade?: number;
    designYear?: number;
    totalArea?: number;
    roadGrade?: string;
}
// 部件对象数组
export interface installationInfo {
    objInfo: ComponentInfo;
    facilityInfo: facilityInfo;
    lastDetectTime?: string;
    lastMaintenanceTime?: string;
    maintenanceNum?: number;
    detectNum?: number;
    id?: number;
}

/**
 * 井盖数据
 */
// 井盖在线
export interface ManholeCoverOnline {
    sum: number;
    online: number;
    recordTime?: string;
    offline?: number;
    alarm?: number;
    normal?: number;
}
export interface ManholeCoverInfo {
    points?: string;
    archivesName?: string;
    archivesNo?: string;
    input?: string;
    objName?: string;
    objId?: string;
    objInfo?: ComponentInfo;
    extendInfo?: ComponentInfo;
    devicePropertyStatusList?: DevicePropertyStatus[];
    physicModel?: devicePropertyStatus[];
    status?: number;
    useStatus?: number;
    device?: ComponentInfo;
    deviceCode?: string; // 设备编码
    id?: number;
    typeName?: string; // 设备类型
    name?: string; // 设备名称
    notPushAlarmNum?: number; // 未推送告警数量
    monitorDeviceCode?: string; // 视频监控
}
// 井盖设备状态
export interface devicePropertyStatus {
    name: string;
    value: string;
    propName?: string;
    prop?: string;
    specs?: string;
    unit?: string;
    identifier?: string;
    dataType?: {
        specs?: {
            unit?: string;
        };
    };
}
// 警告告警对象
export interface ManholeCoverAlarm {
    alarmTime?: string;
    status?: number;
    sbmc?: string;
    content?: string;
    code?: string; // 告警编号
    level?: number; // 告警级别
    pushStatus?: number; // 推送状态
    areaPath?: string; // 区域路径
    alarmType?: number | string; // 告警类型
    alarmTypeName?: string; // 告警类型名称
}
// 井盖告警数据
export interface ManholeCoverAlarmList {
    alarm: number;
    alarmTotal: number;
    alarmList: ManholeCoverAlarm[];
}
// 区域树
export interface areaTree {
    value?: string;
    label?: string;
    code?: string;
    title?: string;
    key?: string;
    checked?: boolean;
    expand?: boolean;
    selected?: boolean;
    name?: string;
    id?: number | string;
    fullId?: string;
    children?: areaTree[];
    parentCode?: string;
    valueType?: number;
}
export interface treeData {
    id: string;
    value: string;
    title: string;
    selected?: boolean;
    checked?: boolean;
    expand?: boolean;
    children?: treeData[];
}
export interface emitType {
    (xx: string, ...arg: any): void;
}
export const manholeCoverService = {
    /**
     * 获取区域树
     */
    areaTree(params: { type: string }) {
        return request({
            url: "/passCommon/tree",
            method: "get",
            params,
        });
    },
    /**
     * 树形采集点
     */
    collectionTree(data: any) {
        return request({
            url: "/energyConsumption/collectionPoint/tree",
            method: "post",
            data,
        });
    },
    /**
     * 获取区域树
     */
    manholeAreaTree(params: { type: string; table?: string; modelIds?: string }) {
        return request({
            url: "/deviceAreaTree/getTree",
            method: "get",
            params,
        });
    },
    /**
     * 条件，分页(不分页)查询井盖
     */
    manholeList(data: recordsRequest) {
        return request({
            url: "/manhole/list",
            method: "post",
            data,
        });
    },
    /**
     * 根据区域查询井盖在线统计
     */
    manholeMapOnline(data: Object) {
        return request({
            url: "/deviceStatistics/online",
            method: "post",
            data,
        });
    },
    /**
     * 根据区域查询井盖在线情况
     */
    manholeMapDay(data: Object) {
        return request({
            url: "/deviceStatistics/day",
            method: "post",
            data,
        });
    },
    /**
     * 根据区域查询告警情况
     */
    manholeMapAlarms(data: Object) {
        return request({
            url: "/manholeMap/alarms",
            method: "post",
            data,
        });
    },
    /**
     * 根据区域查询告警情况
     */
    manholeDetail(id: number) {
        return request({
            url: `/manhole/${id}`,
            method: "get",
        });
    },
    /**
     * 根据设备编码查询井盖告警日志
     */
    manholeMapAlarm(params: { deviceCode: string }) {
        return request({
            url: "/manholeMap/alarm",
            method: "get",
            params,
        });
    },
    /**
     * 根据设备编码查询设备信息
     */
    getDeviceByCode(deviceCode: string) {
        return request({
            url: `/manhole/device/${deviceCode}`,
            method: "get",
        });
    },
    /**
     * 井盖新增，编辑，删除
     */
    manholeAction(data: Object, method: string) {
        return request({
            url: `/manhole`,
            method: method,
            data,
        });
    },
    /**
     * 批量新增（post）
     */
    addManholes(data: Object) {
        return request({
            url: `/manhole/batch`,
            method: 'post',
            data,
        });
    },
    /**
     * 根据id查询井盖详情
     */
    getManholeDetailById(id: string) {
        return request({
            url: `/manhole/${id}`,
            method: "get",
        });
    },
    editManhole(data: ManholeCoverInfo) {
        return request({
            url: "/manhole",
            method: "put",
            data,
        });
    },
    getAlarmDetail(id: String) {
        return request({
            url: `/manholeAlarm/${id}`,
            method: "get",
        });
    },
    countListStatus(data: any) {
        return request({
            url: `/manhole/countListStatus`,
            method: 'POST',
            data,
        });
    },
    alarmCountListStatus(data: any) {
        return request({
            url: `/manholeAlarm/countProcessHandleStatus`,
            method: 'POST',
            data,
        });
    },
    /**
     * @Description: 获取区域树(不关联设施)
     */
    getTreeNoDevice(data: any) {
        return request({
            url: `/area/getTree`,
            method: "POST",
            data: {
                tableName: data.table,
                level: data.level,
            },
        });
    },
    /**
     * @Description: 收运获取区域树
     */
    getGarbagePointTree(params: any) {
        return request({
            url: "/garbagePoint/getTree",
            method: "get",
            params,
        });
    },
     /**
     * 井盖部件删除
     */
     manholeComponentDelete(data: Object, method: string) {
        return request({
            url: `/manholeObj`,
            method: method,
            data,
        });
    },
};
