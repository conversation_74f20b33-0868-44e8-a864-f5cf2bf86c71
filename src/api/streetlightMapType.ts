import { ManholeCoverInfo,devicePropertyStatus } from "@/api/manholeCoverService";
// 路灯设备对象
export interface streetlightInfo {
    sbmc?: string;
    bsm?: string;
    areaPath?: string;
    areaPaths?: string[];
    status?: number;
    alarmState?: number;
    objX?: number;
    objY?: number;
    ownerEnterpriseName?: string;
    light?: string;
    id?: number;
    modelId?: number;
}
export interface streetlightDetaillInfo extends ManholeCoverInfo {
    lightingType: number;
    createTime?: string;
    drainageDeviceDTOS?: ManholeCoverInfo[];
}
