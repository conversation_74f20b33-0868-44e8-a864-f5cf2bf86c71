import request from '@/utils/request';

// 开始点播接口
export const startPlay = (deviceCode) => {
    console.log('startPlay:'+deviceCode)
    return request({
        url: `/wvp/play/start?deviceCode=${deviceCode}`,
        method: 'get',
        hideMsg: true
    })
}
// 停止点播接口
export const stopPlay = (deviceCode) => {
    return request({
        url: `/wvp/play/stop?deviceCode=${deviceCode}`,
        method: 'get',
        hideMsg: true
    })
}

// 开始视频回放
export const startPlayBack = (deviceCode, startTime, endTime) => {
    return request({
        url: `/wvp/playback/start?deviceCode=${deviceCode}&startTime=${startTime}&endTime=${endTime}`,
        method: 'get',
        hideMsg: true
    })
}
// 停止视频回放 stream:流ID
export const stopPlayBack = (deviceCode, stream) => {
    return request({
        url: `/wvp/playback/stop?deviceCode=${deviceCode}&stream=${stream}`,
        method: 'get',
        hideMsg: true
    })
}
