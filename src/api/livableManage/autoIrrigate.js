import request from '@/utils/request';
// 设备详情
export function getIrrigateDeviceDetail(id) {
    return request({
        url: `/irrigateDevice/selectDtoOne/${id}`,
        method: 'get'
    })
}
// 新增
export function newAddIrrigateDevice(data) {
    return request({
        url: '/irrigateDevice/batchInsert',
        method: 'post',
        data
    })
}
// 修改设备
export function editIrrigateDevice(data) {
    return request({
        url: '/irrigateDevice',
        method: 'put',
        data
    })
}
// 删除设备
export function deleteIrrigateDevice(idList) {
    return request({
        url: `/irrigateDevice?idList=${idList}`,
        method: 'delete'
    })
}
// 启用停用设备
export function enabledDisableIrrigateDevice(data) {
    return request({
        url: '/irrigateDevice/batchStartAndStop',
        method: 'post',
        data
    })
}

// 查询分页
export function selectDtoPage(data) {
    return request({
        url: '/irrigateDevice/selectDtoPage',
        method: 'post',
        data
    })
}

// 根据区域查询灌溉在线统计
export function day(data) {
    return request({
        url: '/deviceStatistics/day',
        method: 'post',
        data
    })
}


// 根据区域查询灌溉在线统计
export function online(data) {
    return request({
        url: '/deviceStatistics/online',
        method: 'post',
        data
    })
}

// 根据区域查询告警情况
export function alarms(data) {
    return request({
        url: '/irrigateMap/alarms',
        method: 'post',
        data
    })
}

// 根据设备编码查询灌溉告警日志
export function alarm(params) {
    return request({
        url: '/irrigateMap/alarm',
        method: 'get',
        params
    })
}
// 查询传感器
export function getSensorDtoList(data) {
    return request({
        url: '/irrigateDeviceRef/selectSensorDtoList',
        method: 'post',
        data
    })
}

// 关联传感器
export function correlationSensor(data) {
    return request({
        url: '/irrigateDeviceRef/joinSensorList',
        method: 'post',
        data
    })
}
// 顶部用水量统计
export function topWaterUseStatistics(data) {
    return request({
        url: '/irrigateStatisticalAnalyze/topWaterUseStatistics',
        method: 'post',
        data
    })
}

// 灌溉趋势分析
export function trendDataStatistics(data) {
    return request({
        url: '/irrigateStatisticalAnalyze/trendDataStatistics',
        method: 'post',
        data
    })
}

// 表格信息说明
export function tableStatisticsInfo(data) {
    return request({
        url: '/irrigateStatisticalAnalyze/tableStatisticsInfo',
        method: 'post',
        data
    })
}
