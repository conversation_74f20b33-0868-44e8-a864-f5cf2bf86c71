import request from '@/utils/request.js';

// /**
//  * @Description: 新增车辆
//  */
// export function addCar(data) {
//     return request({
//         url: '/livableCar',
//         method: 'post',
//         data,
//         headers: {
//             'Content-Type': 'application/json'
//         }
//     })
// }
/**
 * @Description: 关联
 */
export function linkVehicle(data) {
    return request({
        url: '/livableCar',
        method: 'post',
        data
    })
}

/**
 * @Description: 查所有定位设备信息
 */
export function queryDeviceInformation(data) {
    return request({
        url: '/livablePositionDevice/getPage',
        method: 'post',
        data,
        headers: {
            'Content-Type': 'application/json'
        }
    })
}
/**
 * @Description: 删除车辆
 */

export function deleteCar(data) {
    return request({
        url: '/livableCar',
        method: 'delete',
        params: data,
        headers: {
            'Content-Type': 'application/json'
        }
    })
}

/**
 * @Description: 编辑车辆
 */

export function modifyCar(data) {
    return request({
        url: '/livableCar',
        method: 'put',
        data,
        headers: {
            'Content-Type': 'application/json'
        }
    })
}
/**
 * @Description: 查询单个车辆信息
 */

export function querySingleCarInfo(data) {
    return request({
        url: '/livableCar/queryOneWithDevice',
        method: 'post',
        data,
        headers: {
            'Content-Type': 'application/json'
        }
    })
}
/**
 * @Description: 删除人员（包含批量删除
 */

export function deletePeople(data) {
    return request({
        url: '/electronicfence/people',
        method: 'delete',
        data,
    })
}
/**
 * @Description: 查询单个车辆信息
 */

export function querySinglePeopleInfo(id) {
    return request({
        url: `/electronicfence/people/${id}`,
        method: 'get'
    })
}

/**
 * @Description: 查询单个车辆信息
 */

export function linkPeople(data) {
    return request({
        url: '/electronicfence/people',
        method: 'post',
        data
    })
}

