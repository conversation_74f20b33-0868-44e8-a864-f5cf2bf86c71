import request from '@/utils/request';

// 流水记录
export interface historicDataPageFromEs {
    createTime?: string;
    humidity?: number;
    temperature?: number;
}

export interface monitorData {
    createTime?: string;
    deviceName?: string;
    deviceCode?: string;
    waterPressure?: number;
    waterLevel?: number;
    stress?: number;
    tilt?: number;
    settlement?: number;
}

export const monitorService = {
    /**
     * 查询折线图数据，假数据
     */
    queryHistoricDataPageFromEs(data: recordsRequest) {
        return request({
            url: '/monitor/queryHistoricDataPageFromEs',
            method: 'post',
            data,
        });
    },
    /**
     * 查询折线图数据，从es
     */
    queryHistoricDataFromEs(data: recordsRequest) {
        return request({
            url: '/monitor/queryHistoricDataFromEs',
            method: 'post',
            data,
        });
    },
    /**
     * 土壤设备列表
     */
    gardenSoilDeviceList(data: recordsRequest) {
        return request({
            url: '/gardenSoilDevice/list',
            method: 'post',
            data,
        });
    },
    /**
     * 水质设备列表
     */
    waterQualityDeviceList(data: recordsRequest) {
        return request({
            url: '/waterQualityDevice/list',
            method: 'post',
            data,
        });
    },
    /**
     * 气象环境
     */
    meteorologicalDeviceList(data: recordsRequest) {
        return request({
            url: '/meteorologicalDevice/list',
            method: 'post',
            data,
        });
    },
    /**
     * 气象环境
     */
    meteorologicalDeviceListAll(monitoringSiteId: number) {
        return request({
            url: '/meteorologicalDevice/listAll',
            method: 'post',
            data: { monitoringSiteId },
        });
    },
    /**
     * 噪声环境设备列表
     */
    noiseDeviceList(data: recordsRequest) {
        return request({
            url: '/noiseDevice/list',
            method: 'post',
            data,
        });
    },
    /**
     * 空气质量设备列表
     */
    airQualityDeviceList(data: recordsRequest) {
        return request({
            url: '/airQualityDevice/list',
            method: 'post',
            data,
        });
    },
    /**
     * 积水监测设备列表
     */
    pondingMonitoringDeviceList(data: recordsRequest) {
        return request({
            url: '/pondingMonitoringDevice/list',
            method: 'post',
            data,
        });
    },
    /**
     * 雨情监测设备列表
     */
    rainfallMonitoringDeviceList(data: recordsRequest) {
        return request({
            url: 'rainfallMonitoringDevice/list',
            method: 'post',
            data,
        });
    },
    /**
     * 湖渠监测设备列表
     */
    lakeMonitoringDeviceList(data: recordsRequest) {
        return request({
            url: '/lakeMonitoringDevice/list',
            method: 'post',
            data,
        });
    },
    /**
     * 危废监测设备列表
     */
    hazardousgDeviceList(data: any) {
        return request({
            url: '/hazardousDevice/list',
            method: 'post',
            data,
        });
    },
    /**
     * 深基监测设备列表
     */
    deepFoundationDeviceList(data: any) {
        return request({
            url: '/deepFoundationDevice/list',
            method: 'post',
            data,
        });
    },  
    /**
     * 湖渠监测设备列表
     */
    muckAlarmMapStatistics() {
        return request({
            url: '/muckMonitoring/muckAlarmMap/statistics',
            method: 'post',
            data: {},
        });
    },
    /**
     * 诱导屏列表
     */
    smartTravelGuidanceList(data: any) {
        return request({
            url: '/smartTravelGuidance/list',
            method: 'post',
            data,
        });
    },
    /**
     * 噪音检测统计汇总
     */
    noiseEnvAnalysisSummary(data: any) {
      return request({
          url: '/noiseEnvAnalysis/summary',
          method: 'get',
          params: data,
      });
    },
    /**
     * 深基坑监测数据列表   
     */
    monitoringDataList(data: any) {
        return request({
            url: '/monitoringData/list',
            method: 'post',
            data,
        });
    },
    // 添加新增监测数据接口
    addMonitoringData(data: any) {
        return request({
            url: '/monitoringData/add',
            method: 'post',
            data
        })
    }
};
