import request from '@/utils/request';

/**
 * 火情监控
 */
// 新增
// export function garbageAdd(data) {
//     return request({
//         url: '/garbageHouseDevice/batch',
//         method: 'post',
//         data
//     })
// }
// // 删除
// export function garbageDelete(ids) {
//     return request({
//         url: `/garbageHouseDevice`,
//         method: 'delete',
//         data: {
//             ids
//         }
//     })
// }
// // 修改
// export function garbageEdit(data) {
//     return request({
//         url: '/garbageHouseDevice',
//         method: 'put',
//         data
//     })
// }
// // 详情
// export function garbageDetail(id) {
//     return request({
//         url: `/garbageHouseDevice/${id}`,
//         method: 'get',
//     })
// }
// 列表
export function garFireDeviceList(data) {
    return request({
        url: '/fireMonitoring/deviceManagement/list',
        method: 'post',
        data
    })
}

// 告警统计
export function deviceStatisticsAlarm(data) {
    // return request({
    //     url: '/muckMonitoring/muckAlarmMap/statistics',
    //     method: 'post',
    //     data: {},
    // })
    return request({
        url: '/fireMonitoring/alarmManagement/statistics',
        method: 'post',
        data
    })
}

// 告警趋势
export function alarmStatisticsDay(data) {
    return request({
        url: '/fireMonitoring/alarmManagement/day',
        method: 'post',
        data
    })
}

//告警列表
export function alarmList(data) {
    return request({
        url: '/fireMonitoring/alarmManagement/list',
        method: 'post',
        data
    })
}

export function getDeviceDetail(id) {
    return request({
        url: `/fireMonitoring/deviceManagement/${id}`,
        method: 'get'
    })
}

// 告警单条
export function deviceAlarmByCode(data) {
    return request({
        url: `/fireMonitoring/alarmManagement/alarm`,
        method: 'post',
        data
    })
}

// // 在线情况
// export function deviceStatisticsDay(data) {
//     return request({
//         url: '/deviceStatistics/day',
//         method: 'post',
//         data
//     })
// }
// // 告警
// export function deviceAlarms(data) {
//     return request({
//         url: '/garbageHouseDeviceMap/alarms',
//         method: 'post',
//         data
//     })
// }
// // 告警单条
// export function deviceAlarmByCode(deviceCode) {
//     return request({
//         url: `/garbageHouseDeviceMap/alarm?deviceCode=${deviceCode}`,
//         method: 'get',
//     })
// }

