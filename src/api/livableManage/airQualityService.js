import request from '@/utils/request';

// 设备详情
export function getDetailInfo(id) {
    return request({
        url: `/airQualityDevice/${id}`,
        method: 'get'
    })
}
// 新增空气质量设备
export function newAddAirDevice(data) {
    return request({
        url: '/airQualityDevice',
        method: 'post',
        data,
        headers: {
            'Content-Type': 'application/json'
        }
    })
}
// 获取区域树
export function getAreaTree(type, table) {
    return request({
        url: `/deviceAreaTree/getTree?type=${type}&table=${table}`,
        method: 'get',
        headers: {
            'Proxy-Connection': 'keep-alive'
        }
    })
}
// 删除空气质量管理设备/deleteAirDevice
export function deleteAirDevice(ids) {
    return request({
        url: '/airQualityDevice',
        method: 'delete',
        data: {
            ids
        },
        headers: {
            'Content-Type': 'application/json'
        }
    })
}
// 编辑原理空气质量设备/editAirDevice
export function editAirDevice(data) {
    return request({
        url: '/airQualityDevice',
        method: 'put',
        data,
        headers: {
            'Content-Type': 'application/json'
        }
    })
}

// 查询环境概览设备列表
export function queryDevices(data) {
    return request({
        url: '/environmentOverview/queryDevices',
        method: 'post',
        data
    })
}

// 查询环境概览设备列表
export function queryDeviceOnlineAlarmCount(data) {
    return request({
        url: '/environmentOverview/queryDeviceOnlineAlarmCount',
        method: 'post',
        data
    })
}
// 根据站点id查询设备属性
export function getDeviceInfoById(data) {
    return request({
        url: '/meteorologicalDevice/getDevicePropertyStatusListByMonitorStationId',
        method: 'GET',
        params: data
    })
}

// 根据站点id查询设备趋势
export function getDeviceInfoTrendById(data) {
  return request({
    url: '/meteorologicalDevice/getDevicePropertyStatusListTrendByMonitorStationId',
    method: 'GET',
    params: data
  })
}

// 查询空气质量设备属性值
export function getAirQualityDeviceInfo(id) {
    return request({
        url: `/airQualityDevice/getDevicePropertyStatusListByDeviceId/${id}`,
        method: 'GET',
    })
}

// 查询所有空气质量设备列表和属性值
export function getAirQualityDeviceListAndInfo() {
    return request({
        url: `/airQualityDevice/queryListAndDevicePropertyStatusList`,
        method: 'GET',
    })
}

// 6因子统计
export function getAirEnvStatistics(data) {
    return request({
        url: `/airEnvAnalysis/airEnvStatistics`,
        method: 'POST',
        data
    })
}
// pm趋势
export function getPmTrend(data) {
    return request({
        url: `/airEnvAnalysis/pmTrend`,
        method: 'POST',
        data
    })
}
// 噪音趋势
export function getNoiceTrend(data) {
    return request({
        url: `/airEnvAnalysis/noiceTrend`,
        method: 'POST',
        data
    })
}
//获取最新配置
export function getNewConfig(){
    return request({
        url:'/airStandard/getLastConfig',
        method:'get'
    })
}
// 空气质量达标污染物补偿计算
export function airQualityAddCalculation(data){
    return request({
        url: '/openapi/task/callback/airQualityAddCalculation',
        method: 'post',
        data
    })
}
// 保存或更新配置
export function saveOrUpdateConfig(data){
    return request({
        url: '/airStandard/saveOrUpdateConfig',
        method: 'post',
        data
    })
}
// 达标K线数据分析
export function reachKxDataAnalysis(data){
    return request({
        url:'/airEnvironmentQualityStatistics/reachKxDataAnalysis',
        method:'post',
        data
    })
}
// 属性浓度K线数据
export function propDepthKxData(data){
    return request({
        url:'/airEnvironmentQualityStatistics/propDepthKxData',
        method:'post',
        data
    })
}
// 达标数据分析
export function reachDataAnalysis(data){
    return request({
        url:'/airEnvironmentQualityStatistics/reachDataAnalysis',
        method:'post',
        data
    })
}
// 空气质量趋势K线数据
export function qualityTrendKxData(data){
    return request({
        url:'/airEnvironmentQualityStatistics/qualityTrendKxData',
        method:'post',
        data
    })
}
// 查询空气质量趋势设备
export function airQualityDevice3(data) {
    return request({
        url: '/airQualityDevice/list',
        method: 'post',
        data: {
            "page": {
                "current": -1,
                "size": -1
            },
            "customQueryParams": data,
            "sorts": []
        }
    })
}
// 查询下拉框数据
export function airQualityDevice(data) {
    return request({
        url: '/airEnvironmentQualityStatistics/unitDeviceTreeData',
        method: 'post',
        data: {
            "page": {
                "current": -1,
                "size": -1
            },
            "customQueryParams": data,
            "sorts": []
        }
    })
}
// 明细表
export function propDetailData(data){
    return request({
        url: '/airEnvironmentQualityStatistics/propDetailData',
        method: 'post',
        data: {
            "page": {
                "current": 1,
                "size": 9999999999
            },
            "customQueryParams": data,
            "sorts": []
        }
    })
}
// 空气导出
export function exportAirStatistics(data){
    return request({
        url:'/airEnvironmentQualityStatistics/reachKxDataAnalysis/export',
        method:'post',
        data
    })
}

// 明细导出
export function propDetailDataExport(data){
    return request({
        url:'/airEnvironmentQualityStatistics/propDetailData/export',
        method:'post',
        data
    })
}
// 汇总导出
export function propSummaryDataExport(data){
    return request({
        url:'/airEnvironmentQualityStatistics/propSummaryData/export',
        method:'post',
        data
    })
}
// 空气质量导出
export function qualityStatisticsDataExport(data){
    return request({
        url:'/airEnvironmentQualityStatistics/qualityStatisticsData/export',
        method:'post',
        data
    })
}
// 空气质量趋势中显示数据
export function qualityCountData(data){
    return request({
        url:'/airEnvironmentQualityStatistics/qualityCountData',
        method:'post',
        data
    })
}