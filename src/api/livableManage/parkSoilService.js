import request from '@/utils/request';

// 设备详情
export function getDetailInfo(id) {
    return request({
        url: `/gardenSoilDevice/${id}`,
        method: 'get'
    })
}
// 新增土壤设备
export function newAddSoliDevice(data) {
    return request({
        url: '/gardenSoilDevice',
        method: 'post',
        data,
        headers: {
            'Content-Type': 'application/json'
        }
    })
}
// 获取区域树
export function getAreaTree(type, table) {
    return request({
        url: `/deviceAreaTree/getTree?type=${type}&table=${table}`,
        method: 'get',
        headers: {
            'Proxy-Connection': 'keep-alive'
        }
    })
}
// 删除土壤管理设备/gardenSoilDevice
export function deleteSoilDevice(ids) {
    return request({
        url: '/gardenSoilDevice',
        method: 'delete',
        data: {
            ids
        },
        headers: {
            'Content-Type': 'application/json'
        }
    })
}
// 编辑原理土壤设备/gardenSoilDevice
export function editSoilDevice(data) {
    return request({
        url: '/gardenSoilDevice',
        method: 'put',
        data,
        headers: {
            'Content-Type': 'application/json'
        }
    })
}

// 查询环境概览设备列表
export function queryDevices(data) {
    return request({
        url: '/environmentOverview/queryDevices',
        method: 'post',
        data
    })
}

// 查询环境概览设备列表
export function queryDeviceOnlineAlarmCount(data) {
    return request({
        url: '/environmentOverview/queryDeviceOnlineAlarmCount',
        method: 'post',
        data
    })
}

// 查询设备在线 根据model分组统计
export function queryDeviceOnlineAlarmCountGroupByModel(data) {
    return request({
        url: '/environmentOverview/queryDeviceOnlineAlarmCountGroupByModel',
        method: 'post',
        data
    })
}

// 查询设备在线 根据model分组统计
export function getDeviceObjInfoDevicePropertyStatusListInfo(params) {
    return request({
        url: '/deviceStatus/getDeviceObjInfoDevicePropertyStatusListInfo',
        method: 'get',
        params
    })
}

// 查询设备在线 根据model分组统计
export function alarmList(data) {
    return request({
        url: '/alarm/list',
        method: 'post',
        data
    })
}
