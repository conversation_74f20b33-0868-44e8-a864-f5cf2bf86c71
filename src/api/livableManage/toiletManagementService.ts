import request from "@/utils/request";
import { ComponentInfo } from "@/api/manholeCoverService";
export const toiletManagementService = {
    /**
     * 厕所设备 - 查询分页
     */
    selectDtoPage(data: recordsRequest) {
        return request({
            url: "/toiletInfo/selectDtoPage",
            method: "post",
            data,
        });
    },
    /**
     * 蹲位统计
     */
    spaceCount(data: any) {
        return request({
            url: "/toiletInfo/spaceCount",
            method: "post",
            data,
        });
    },
    /**
     * 环境监测信息
     */
    environmentInfo(data: any) {
        return request({
            url: "/toiletInfo/environmentInfo",
            method: "post",
            data,
        });
    },
    /**
     * 能耗
     */
    energyCount(data: any) {
        return request({
            url: "/toiletInfo/energyCount",
            method: "post",
            data,
        });
    },
    /**
     * 公厕单条
     */
    selectDtoOne(data: ComponentInfo) {
        return request({
            url: "/toiletInfo/selectDtoOne",
            method: "post",
            data,
        });
    },
    /**
     * 公厕报警
     */
    selectAlarmDtoPage(data: recordsRequest) {
        return request({
            url: "/toiletAlarm/selectAlarmDtoPage",
            method: "post",
            data,
        });
    },
    /**
     * 今日数据汇总统计
     */
    getTodayDataSummary(params) {
        return request({
            url: "/toiletTodayStatistics/summary",
            method: "get",
            params
        });
    },
    /**
     * 客流量统计
     */
    getToiletFlow() {
        return request({
            url: "/toiletTodayStatistics/toiletFlow/stats",
            method: "get",
        });
    },
    /**
     * 客流量折线图统计
     */
    getToiletFlowLine(data: any) {
        return request({
            url: "/toiletTodayStatistics/toiletFlow/lineChart",
            method: "post",
            data,
        });
    },
    /**
     * 客流量折线图统计
     */
    getToiletFlowQueryPage(data: any) {
        return request({
            url: "/toiletTodayStatistics/toiletFlow/queryPage",
            method: "post",
            data,
        });
    },
    /**
     * 今日客流量排名
     */
    getToiletFlowRank(params) {
        return request({
            url: "/toiletTodayStatistics/toiletFlow/rank",
            method: "get",
            params
        });
    },
    /**
     * 今日流量时段客流量
     */
    getToiletFlowTime() {
        return request({
            url: "/toiletTodayStatistics/toiletFlow/timeStats",
            method: "get",
        });
    },
    /**
     * 环境趋势折线图统计
     */
    getEnvLineChart(data: any) {
        return request({
            url: "/toiletEnvironmentStatistic/env/lineChart",
            method: "post",
            data,
        });
    },
    /**
     * 环境汇总统计
     */
    getStat() {
        return request({
            url: "/toiletEnvironmentStatistic/stat",
            method: "get",
        });
    },
    /**
     * 达标分析折线图统计
     */
    getStandard(data: any) {
        return request({
            url: "/toiletEnvironmentStatistic/env/standard/lineChart",
            method: "post",
            data,
        });
    },
    /**
     * 达标明细表
     */
    standardQueryPage(data: any) {
        return request({
            url: "/toiletEnvironmentStatistic/env/standard/queryPage",
            method: "post",
            data,
        });
    },
    /**
     * 达标统计
     */
    queryStatByDate(data: any) {
        return request({
            url: "/toiletEnvironmentStatistic/env/standard/queryStatByDate",
            method: "post",
            data,
        });
    },
};
