import request from '@/utils/request';
/**
 * 绿地类型管理
 * */
// 分页
export function getGreenLandTypeList(data) {
    return request({
        url: '/greenLand/greenLandType/getPage',
        method: 'post',
        data,
        headers: {
            'Content-Type': 'application/json'
        }
    })
}
// 所有绿地类型
export function getAllGreenLandType() {
    return request({
        url: `/greenLand/greenLandType/findAll`,
        method: 'post',
    })
}
// 详情
export function getGreenLandTypeDetail(id) {
    return request({
        url: `/greenLand/greenLandType/${id}`,
        method: 'get',
    })
}
// 新增
export function addGreenLandType(data) {
    return request({
        url: '/greenLand/greenLandType',
        method: 'post',
        data,
        headers: {
            'Content-Type': 'application/json'
        }
    })
}
// 编辑
export function editGreenLandType(data) {
    return request({
        url: '/greenLand/greenLandType',
        method: 'put',
        data,
        headers: {
            'Content-Type': 'application/json'
        }
    })
}
// 删除
export function deleteGreenLandType(idList) {
    return request({
        url: `/greenLand/greenLandType?idList=${idList}`,
        method: 'delete',
    })
}
greenlandManagementDelete
/**
 * 绿地管理
 */
// 分页列表
export function getGreenManagementList(data) {
    return request({
        url: '/greenLand/greenLand/getPage',
        method: 'post',
        data,
        headers: {
            'Content-Type': 'application/json'
        }
    })
}
// 单条详情
export function getGreenManagementDetail(id) {
    return request({
        url: `/greenLand/greenLand/${id}`,
        method: 'get',
    })
}
// 新增
export function greenManagementNewAdd(data) {
    return request({
        url: '/greenLand/greenLand',
        method: 'post',
        data,
        headers: {
            'Content-Type': 'application/json'
        }
    })
}
// 编辑
export function greenManagementEdit(data) {
    return request({
        url: '/greenLand/greenLand',
        method: 'put',
        data,
        headers: {
            'Content-Type': 'application/json'
        }
    })
}
// 删除
export function greenlandManagementDelete(idList) {
    return request({
        url: `/greenLand/greenLand?idList=${idList}`,
        method: 'delete',
    })
}
// 获取有效的设备信息
export function getValidObjInfo(data) {
    return request({
        url: '/greenLand/greenLand/getValidObjInfo',
        method: 'post',
        data,
        headers: {
            'Content-Type': 'application/json'
        }
    })
}
// 区域数据设施总览统计
export function greenLandMapAreaCount(data) {
    return request({
        url: '/greenLand/greenLandMap/areaCount',
        method: 'post',
        data
    })
}
// 绿地按照绿地类型分组统计
export function countGroupByType(data) {
    return request({
        url: '/greenLand/greenLandMap/countGroupByType',
        method: 'post',
        data
    })
}
// 绿地运营统计
export function operatortionRecordsCount(data) {
    return request({
        url: '/greenLand/greenLandMap/operatortionRecordsCount',
        method: 'post',
        data
    })
}
// 新增运营
export function addGreenLandOperationRecords(data) {
    return request({
        url: '/greenLand/greenLandOperationRecords',
        method: 'post',
        data,
        headers: {
            'Content-Type': 'application/json'
        }
    })
}
// 删除运营
export function deleteGreenLandOperationRecords(idList) {
    return request({
        url: `/greenLand/greenLandOperationRecords?idList=${idList}`,
        method: 'delete',
    })
}
