import request from "@/utils/request";

export const electronicFenceService = {
    /**
     * 查询电子围栏车辆列表
     */
    livableCarGetPage(data: recordsRequest) {
        return request({
            url: "/livableCar/getPageWithDevice",
            method: "post",
            data,
        });
    },
    /**
     * 查询电子围栏人员列表
     */
    peopleGetPage(data: recordsRequest) {
        return request({
            url: "/electronicfence/peoplePositionDevice/getPage",
            method: "post",
            data,
        });
    },
    /**
     * 告警列表
     */
    carAlarmList(data: recordsRequest) {
        return request({
            url: "/carAlarm/list",
            method: "post",
            data,
        });
    },
    /**
     * 告警列表
     */
    carTrackList(params: string, data: object) {
        return request({
            url: `/carTrack/list?carNo=${params}`,
            method: "post",
            data,
        });
    },
    // 人员轨迹
    peopleTrack(params: string, data: object) {
        return request({
            url: `/peopleTrack/list?mobile=${params}`,
            method: "post",
            data,
        });
    },
    /**
     * 围栏列表
     */
    electronicFenceList(data: recordsRequest) {
        return request({
            url: "/electronicFence/list",
            method: "post",
            data,
        });
    },
    /**
     * 围栏范围
     */
    electronicFenceRegion(ids: string) {
        return request({
            url: `/electronicFence/region?ids=${ids}`,
            method: "get",
        });
    },
    /**
     * 围栏范围
     */
    addElectronicFenceRegion(data: any) {
        return request({
            url: "/electronicFence/region",
            method: "put",
            data,
        });
    },
    /**
     * 围栏详情
     */
    getElectronicFence(id: string) {
        return request({
            url: `/electronicFence/${id}`,
            method: "get",
        });
    },
    /**
     * 围栏绑定的车辆
     */
    electronicFenceCar(id: string) {
        return request({
            url: `/electronicFence/car?id=${id}`,
            method: "get",
        });
    },
    /**
     * 新增围栏
     */
    addElectronicFence(data: any) {
        return request({
            url: "electronicFence",
            method: "post",
            data,
        });
    },
    /**
     * 编辑围栏
     */
    editElectronicFence(data: any) {
        return request({
            url: "electronicFence",
            method: "put",
            data,
        });
    },
    /**
     * 删除围栏
     */
    delElectronicFence(ids: string[]) {
        return request({
            url: "/electronicFence",
            method: "delete",
            data: {
                ids,
            },
        });
    },
};
