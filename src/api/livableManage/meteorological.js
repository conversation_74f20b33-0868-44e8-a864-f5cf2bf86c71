import request from '@/utils/request';

// 设备详情
export function getDetailInfo(id) {
    return request({
        url: `/meteorologicalDevice/${id}`,
        method: 'get'
    })
}
// 新增土壤设备
export function newAddWaterDevice(data) {
    return request({
        url: '/meteorologicalDevice',
        method: 'post',
        data,
        headers: {
            'Content-Type': 'application/json'
        }
    })
}
// 获取区域树
export function getAreaTree(type, table) {
    return request({
        url: `/deviceAreaTree/getTree?type=${type}&table=${table}`,
        method: 'get',
        headers: {
            'Proxy-Connection': 'keep-alive'
        }
    })
}
// 删除土壤管理设备/gardenSoilDevice
export function deleteWaterDevice(ids) {
    return request({
        url: '/meteorologicalDevice',
        method: 'delete',
        data: {
            ids
        },
        headers: {
            'Content-Type': 'application/json'
        }
    })
}
// 编辑原理土壤设备/gardenSoilDevice
export function editDevice(data) {
    return request({
        url: '/meteorologicalDevice',
        method: 'put',
        data,
        headers: {
            'Content-Type': 'application/json'
        }
    })
}

// 查询环境概览设备列表
export function queryDevices(data) {
    return request({
        url: '/environmentOverview/queryDevices',
        method: 'post',
        data
    })
}

// 查询环境概览设备列表
export function queryDeviceOnlineAlarmCount(data) {
    return request({
        url: '/environmentOverview/queryDeviceOnlineAlarmCount',
        method: 'post',
        data
    })
}

// 不分页查询
export function getDeviceList(obj) {
    return request({
        url: '/meteorologicalDevice/list',
        method: 'post',
        data: {
            page: {
                current: 1,
                size: -1
            },
            customQueryParams: obj
        }
    })
}

// 增加监测站点
export function insertMonitoringSite(data) {
    return request({
        url: `/meteorological/meteorologicalMonitoringSite`,
        method: 'post',
        data
    })
}
// 删除监测站点
export function deleteMonitoringSite(data) {
    return request({
        url: `/meteorological/meteorologicalMonitoringSite`,
        method: 'delete',
        data
    })
}
// 编辑监测站点
export function updateMonitoringSite(data) {
    return request({
        url: `/meteorological/meteorologicalMonitoringSite`,
        method: 'put',
        data
    })
}
// 查询监测站点列表
export function api_getMonitoringSiteList() {
    return request({
        url: `/meteorological/meteorologicalMonitoringSite/list`,
        method: 'post'
    })
}

// 查询所有有设备的监测站点列表
export function api_getMonitoringSiteHasDeviceList() {
    return request({
        url: `/meteorological/meteorologicalMonitoringSite/listHasDevice`,
        method: 'post'
    })
}

//  查询空气质量设备列表
export function getAirQualityDeviceList() {
    return request({
        url: `/airQualityDevice/listAirQualityDevice`,
        method: 'POST'
    })
}

// 获取噪音设备列表
export function api_getNoiseDeviceList(data) {
    return request({
        url: '/noiseDevice/list',
        method: 'post',
        data
    });
}

// 获取噪音设备详情
export function api_getNoiseDeviceDetail(id) {
    return request({
        url: `/noiseDevice/${id}`,
        method: 'get'
    });
}

