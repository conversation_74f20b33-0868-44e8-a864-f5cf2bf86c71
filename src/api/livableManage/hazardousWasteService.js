/**
 * 危废监测
 */
import request from '@/utils/request'


/**
 * 危险源管理api
 */
const url = '/hazardous'
export const hazardManageService = {
    // 批量增
    addBatch(data) {
        return request({
            url: `${url}`,
            method: 'post',
            data
        })
    },
    // 批量删
    delBatch(data) {
        return request({
            url: `${url}`,
            method: 'delete',
            data
        })
    },
    // id查详情
    getDetailInfo: (id) => {
        return request({
            url: `${url}/${id}`,
            method: 'get'
        })
    },
    // 根据id编辑
    edit(data) {
        return request({
            url: `${url}`,
            method: 'put',
            data
        })
    },
    // 绑定设备
    addDevice(data) {
        return request({
            url: `${url}/device`,
            method: 'post',
            data
        })
    },
    // 获取危险源点位列表（用于下拉选择）
    getHazardousPointList(data = {"page":{"current":1,"size":-1},"customQueryParams":{},"sorts":[]}) {
        return request({
            url: `${url}/hazardousPoint`,
            method: 'post',
            data
        })
    },
}

/**
 * 危险源设备
 */
const deviceUrl = '/hazardousDevice'
export const hazardDeviceService = {
    editDevice(data) {
        return request({
            url: `${deviceUrl}`,
            method: 'put',
            data
        })
    },
    // deviceCode查详
    getDetailInfo(deviceCode) {
        return request({
            url: `${deviceUrl}?deviceCode=${deviceCode}`,
            method: 'get'
        })
    },
    // 新增设备
    newAddDevice(data) {
        return request({
            url: `${deviceUrl}`,
            method: 'post',
            data,
            headers: {
                'Content-Type': 'application/json'
            }
        })
    },
    // 查询设置/根据条件(统计分析/污染源监测统计分析)
    getDeviceByUnit(code) {
        return request({
            url: `${deviceUrl}/getDeviceByUnit/${code}`,
            method: 'post',
            data,
            headers: {
                'Content-Type': 'application/json'
            }
        })
    }
}

/**
 * 危险源统计
 */
const data = {
    HazardousBsms: [''],
    deviceCodes: [''],
    dateType: '', // 1:小时 2:天 3:月 4:年
    startTime: '',
    endTime: '',
}
const statisticsUrl = '/hazardousStatistics'
export const hazardStatisticsService = {
    // 获取树
    getTree() {
        return request({
            url: `${statisticsUrl}/tree`,
            method: 'get',
        })
    },
    // 监测点数量统计
    getPoints(data) {
        return request({
            url: `${statisticsUrl}/points`,
            method: 'post',
            data
        })
    },
    // 超标次数统计
    getStandardNum(data) {
        return request({
            url: `${statisticsUrl}/standardNum`,
            method: 'post',
            data
        })
    },
    // 趋势图
    getTrend(data) {
        return request({
            url: `${statisticsUrl}/trend`,
            method: 'post',
            data
        })
    },
    // 排行
    getRank(data) {
        return request({
            url: `${statisticsUrl}/rank`,
            method: 'post',
            data
        })
    },
    // 表
    getTable(requestModel) {
        return request({
            url: `${statisticsUrl}/table`,
            method: 'post',
            data: requestModel
        })
    },
    // 导出表
    export(data) {
        return request({
            url: `${statisticsUrl}/table/export`,
            method: 'post',
            data
        })
    },
}

