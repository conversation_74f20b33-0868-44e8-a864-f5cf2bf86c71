import { ManholeCoverInfo, ComponentInfo } from "@/api/manholeCoverService";
// 设备详细信息
export interface environmentDevicesDetail extends ManholeCoverInfo {
    device: environmentDevices;
    modelIds?: string;
}
// 设备信息
export interface environmentDevices {
    code?: string;
    name?: string;
    latitude?: number;
    longitude?: number;
    alarmState?: number;
    useStatus?: number;
    modelId?: string;
    lastPushTime?: string;
    status?: number | string;
    gdx?: number;
    gdy?: number;
}

// 设施概览
export interface environmentDevicesOverview {
    alarmDeviceCount?: number;
    deviceCount?: number;
    normalDeviceCount?: number;
    offlineCount?: number;
    onlineCount?: number;
    onlineRate?: number;
    modelId?: string | number;
}
// 路灯详细
export interface deviceExtendInfo extends ManholeCoverInfo {
    deviceExtendInfo: ComponentInfo;
    type?: number;
    device?: ComponentInfo;
    createTime?: string;
    physicModel?: any;
}
// 车辆详情
export interface livableCarInfo {
    carBrand?: string;
    carCategory?: string;
    carColor?: string;
    carFactory?: string;
    carNo?: string;
    carStatus?: number;
    carType?: string;
    id?: number;
    device?: environmentDevices;
    status?: number;
    alarmCount?: number;
    choose?: boolean;
    longitude?:string;
    latitude?:string;
    name?:string;
    description?:string;
    gdx?:string;
    gdy?:string;
}
// 收运点
export interface garbagePointInfo {
    address?: string;
    id?: number;
    name?: string;
    checked?: boolean;
    objy?: number;
    objx?: number;
    code?: string; // 收运点编号
    area?: string; // 区域位置
    num?: number; // 垃圾桶数量
    enterpriseName?: string; // 企业名称
    planName?: string; // 关联计划名称
    planStatus?: number; // 关联计划状态
    garbageTaskRecordsVos?: any[];
    pointName?: string;
    pointId?: number;
    areaPath?: string;
    pointCode?: string;
    gdy?: number;
    gdx?: number;
    deviceCode?:any;
}
// 收运计划
export interface garbagePlanInfo {
    address?: string;
    id?: number;
    planName?: string;
    code?: string | string[];
    note?: string;
    planStatus?: number;
    timeType?: number | string;
    name?: string;
    timeValues?: string[];
    companyId?: number | string;
    carId?: number;
    contactPhone?: string;
    contactPerson?: string;
    factoryId?: number;
    garbagePointList?: garbagePointInfo[];
    garbagePlanRefList?: number[] | string[];
    contactId?: number | string;
    companyName?: string;
    deviceCodeList?:number[];
    startTime?: string;
    endTime?: string;
    type?: string;
    collectStartTime?: any;
    collectEndTime?: any;
    collectTime?: any;
}
// 收运任务
export interface garbageTaskInfo {
    name?: string;
    time?: string;
    planName?: string;
    contactPerson?: string;
    carLicenseNo?: string;
    factoryName?: string;
    weight?: number;
    garbageTaskRecordsVos?: any[];
    sumPointNum?: number;
    donePointNum?: number;
    pointNum?: number;
    garbageNum?: number;
    doneTaskNum?: number;
    taskNum?: number;
}
// 收运任务
export interface garbageRecordInfo {
    planName?: string;
    taskName?: string;
    taskTime?: string;
    contactPerson?: string;
    carLicenseNo?: string;
    companyName?: string;
    pointName?: string;
    enterpriseName?: string;
    time?: string;
    garbageCans?: any[];
}
// 流水属性
export interface deviceStatusInfo {
    prop: string;
    value?: string;
}
export interface deviceStatusInfo {
    deviceStatusList: deviceStatusInfo[];
}
export interface ContactPersonList {
    userId: number;
    userName: string;
    contactPhone: string;
    companyName: string;
}
//围栏信息
export interface livableElectronicFenceInfo {
    id?: number;
    name?: string;
    type?: string;
    note?: string;
    fenceIn?: number | boolean; //进入围栏是否告警(1是0否)
    inTime?: number; // 进入围栏告警时间，正整数
    fenceOut?: number | boolean; // 离开围栏是否告警(1是否)
    outTime?: number; // 离开围栏告警时间，正整数
    carIds?: number[]; //
    timeValues?: string;
    timeValueArray?: any[];
}

// 坐标
export interface coordinate {
    positionX: number;
    positionY: number;
}
// 电子围栏范围
export interface electronicFenceRegion {
    id?: number;
    coordinateList: coordinate[];
}
