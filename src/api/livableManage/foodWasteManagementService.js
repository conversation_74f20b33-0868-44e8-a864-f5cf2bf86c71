import request from '@/utils/request';
import { garbagetransBdMap } from '@/utils/map';

/**
 * @Description: 垃圾处理厂相关接口
 */

/**
 * @Description: 新增垃圾处理厂
 */
export function addWasteTreatmentPlant(data) {
    garbagetransBdMap(data)
    return request({
        url: '/garbageFactory',
        method: 'post',
        data,
        headers: {
            'Content-Type': 'application/json'
        }
    })
}
/**
 * @Description: 编辑垃圾处理厂
 */
export function editWasteTreatmentPlant(data) {
    garbagetransBdMap(data)
    return request({
        url: '/garbageFactory',
        method: 'put',
        data,
        headers: {
            'Content-Type': 'application/json'
        }
    })
}

/**
 * @Description: 删除垃圾处理厂
 */
export function deleteWasteTreatmentPlant(data) {
    return request({
        url: '/garbageFactory',
        method: 'delete',
        data,
        headers: {
            'Content-Type': 'application/json'
        }
    })
}
/**
 * @Description: id查垃圾处理厂详情数据
 */
export async function getGarbageInfo(id) {
    let res = await request({
        url: `/garbageFactory/${id}`,
        method: 'get',
        headers: {
            'Content-Type': 'application/json'
        }
    })
    if (res.data) {
        res.data.objx = res.data.gdx
        res.data.objy = res.data.gdy
    }
    return res
}

/**
 * @Description: 车辆管理相关接口
 */

/**
 * @Description: 新增车辆
 */
export function addCar(data) {
    return request({
        url: '/garbageCar',
        method: 'post',
        data,
        headers: {
            'Content-Type': 'application/json'
        }
    })
}
/**
 * @Description: 编辑车辆
 */
export function editCar(data) {
    return request({
        url: '/garbageCar',
        method: 'put',
        data,
        headers: {
            'Content-Type': 'application/json'
        }
    })
}

/**
 * @Description: 删除车辆
 */
export function delCar(data) {
    return request({
        url: '/garbageCar',
        method: 'delete',
        data,
        headers: {
            'Content-Type': 'application/json'
        }
    })
}
/**
 * @Description: id查车辆详情数据
 */
export function getCarInfo(id) {
    return request({
        url: `/garbageCar/${id}`,
        method: 'get',
        headers: {
            'Content-Type': 'application/json'
        }
    })
}


/**
 * @Description: 收运公司相关接口
 */

/**
 * @Description: 新增收运公司
 */
export function addCompany(data) {
    garbagetransBdMap(data)
    return request({
        url: '/garbageCollectCompany',
        method: 'post',
        data,
        headers: {
            'Content-Type': 'application/json'
        }
    })
}
/**
 * @Description: 编辑收运公司
 */
export function editCompany(data) {
    garbagetransBdMap(data)
    return request({
        url: '/garbageCollectCompany',
        method: 'put',
        data,
        headers: {
            'Content-Type': 'application/json'
        }
    })
}

/**
 * @Description: 删除收运公司
 */
export function delCompany(data) {
    return request({
        url: '/garbageCollectCompany',
        method: 'delete',
        data,
        headers: {
            'Content-Type': 'application/json'
        }
    })
}
/**
 * @Description: id查收运公司详情数据
 */
export async function getCompany(id) {
    let res = await request({
        url: `/garbageCollectCompany/${id}`,
        method: 'get',
        headers: {
            'Content-Type': 'application/json'
        }
    })
    if (res.data) {
        res.data.objx = res.data.gdx
        res.data.objy = res.data.gdy
    }
    return res
    // return request({
    //     url: `/garbageCollectCompany/${id}`,
    //     method: 'get',
    //     headers: {
    //         'Content-Type': 'application/json'
    //     }
    // })
}
/**
 * @Description: 收运公司列表
 */
export function garbageCollectCompanyList(data) {
    return request({
        url: '/garbageCollectCompany/list',
        method: 'post',
        data
    })
}
/**
 * @Description: 根据条件，分页(不分页)查询收运计划
 */
export function garbagePlanList(data) {
    return request({
        url: '/garbagePlan/list',
        method: 'post',
        data
    })
}
/**
 * 收运人下拉查询/garbagePlan/contactPerson
 */
export function getContactPersonList() {
    return request({
        url: '/garbagePlan/contactPerson',
        method: 'get',
    })
}
/**
 * @Description: 启用/禁用计划
 */
export function garbagePlanStatus(data) {
    return request({
        url: '/garbagePlan/status',
        method: 'put',
        data
    })
}
/**
 * @Description: 启用/禁用计划
 */
export function garbagePlanEdit(data) {
    return request({
        url: '/garbagePlan',
        method: 'put',
        data
    })
}
/**
 * @Description: 删除收运计划（包含批量删除）
 */
export function addGarbagePlan(data) {
    return request({
        url: '/garbagePlan',
        method: 'post',
        data
    })
}
/**
 * @Description: 删除收运计划（包含批量删除）
 */
export function garbagePlanDel(data) {
    return request({
        url: '/garbagePlan',
        method: 'delete',
        data
    })
}
/**
 * @Description: 收运点列表
 */
export function garbagePoint(data) {
    return request({
        url: '/garbagePlan/garbagePoint',
        method: 'post',
        data
    })
}
/**
 * @Description: 收运点列表
 */
export function garbagePointList(data) {
    return request({
        url: '/garbagePlan/garbagePoint',
        method: 'post',
        data
    })
}
/**
 * @Description: 根据条件，分页(不分页)查询收运点
 */
export function garbagePointQuery(data) {
    return request({
        url: '/garbageMap/mapPoint',
        method: 'post',
        data
    })
}
/**
 * @Description: 根据条件，分页(不分页)查询收运点
 */
export function garbageCarList(data) {
    return request({
        url: '/garbageCar/list',
        method: 'post',
        data
    })
}
/**
 * @Description: 根据id查询收运计划详情
 */
export function garbagePlanById(id) {
    return request({
        url: `/garbagePlan/${id}`,
        method: 'get',
    })
}

/**
* @Description: 根据条件，分页(不分页)查询收运任务
*/
export function garbagePlanTaskList(data) {
    return request({
        url: '/garbagePlanTask/list',
        method: 'post',
        data
    })
}

/**
 * @Description: 根据条件，分页(不分页)查询处理厂
 */
export function garbageFactoryList(data) {
    return request({
        url: '/garbageFactory/list',
        method: 'post',
        data
    })
}

/**
 * @Description: 地图计划列表
 */
export function garbageMapPlan(type) {
    return request({
        url: `/garbageMap/plan/${type}`,
        method: 'get'
    })
}

/**
 * @Description: 根据计划id查询最新任务和收运点
 */
export function garbageCarTaskPlan(planId) {
    return request({
        url: `/garbageMap/task/${planId}`,
        method: 'get'
    })
}

/**
 * @Description: 根据收运点id查询收运点信息和收运点最近收运记录
 */
export function garbageCarPointId(id) {
    return request({
        url: `/garbageMap/point/${id}`,
        method: 'get'
    })
}
/**
 * @Description: 根据收运点id查询收运点信息和收运点最近收运记录
 */
export function garbageMapOverview(data) {
    return request({
        url: '/garbageMap/overview',
        method: 'post',
        data
    })
}

/**
 * @Description: 企业管理：新增
 */
export function addEnterprise(data) {
    garbagetransBdMap(data)
    return request({
        url: '/garbageEnterprise',
        method: 'post',
        data
    })
}
/**
 * @Description: 企业管理：删除
 */
export function delEnterprise(data) {
    return request({
        url: '/garbageEnterprise',
        method: 'delete',
        data
    })
}
/**
 * @Description: 企业管理：编辑
 */
export function editEnterprise(data) {
    garbagetransBdMap(data)
    return request({
        url: '/garbageEnterprise',
        method: 'put',
        data
    })
}
/**
 * @Description: 企业管理：根据id查详情
 */
export async function getEnterpriseInfo(id) {
    let res = await request({
        url: `/garbageEnterprise/${id}`,
        method: 'get',
    })
    if (res.data) {
        res.data.objx = res.data.gdx
        res.data.objy = res.data.gdy
    }
    return res
    // return request({
    //     url: `/garbageEnterprise/${id}`,
    //     method: 'get',
    // })
}

/**
 * @Description: 收运点管理：新增
 */
export function addPoint(data) {
    garbagetransBdMap(data)
    return request({
        url: '/garbagePoint',
        method: 'post',
        data
    })
}
/**
 * @Description: 收运点管理：删除
 */
export function delPoint(data) {
    return request({
        url: '/garbagePoint',
        method: 'delete',
        data
    })
}
/**
 * @Description: 收运点管理：编辑
 */
export function editPoint(data) {
    garbagetransBdMap(data)
    return request({
        url: '/garbagePoint',
        method: 'put',
        data
    })
}
/**
 * @Description: 收运点管理：根据id查详情
 */
export async function getPointInfo(id) {
    let res = await request({
        url: `/garbagePoint/${id}`,
        method: 'get',
    })
    if (res.data) {
        res.data.objx = res.data.gdx
        res.data.objy = res.data.gdy
    }
    return res
    // return request({
    //     url: `/garbagePoint/${id}`,
    //     method: 'get',
    // })
}

/**
 * @Description: 根据id查收运记录详情
 */
export function getGarbageTaskInfo(id) {
    return request({
        url: `/garbageTaskRecords/detail/${id}`,
        method: 'get',
    })
}

/**
 * @Description: 删除收运记录
 */
export function delReceiptAndShipmentRecords(data) {
    return request({
        url: '/garbageTaskRecords',
        method: 'delete',
        data
    })
}
/**
 * @Description: 编辑收运记录
 */
export function editReceiptAndShipmentRecords(data) {
    return request({
        url: '/garbageTaskRecords',
        method: 'put',
        data
    })
}
export function newAddShipmentRecords(data, method = 'post') {
    return request({
        url: '/garbageTaskRecords',
        method,
        data
    })
}


/**
 * @Description:  企业申报管理相关接口
 */

/**
 * @Description: 收运点管理：新增
 */
export function addEnterpriseReport(data) {
    //data 地图转换
    console.info(data)
    console.info("===================================================")
    garbagetransBdMap(data)
    console.info(data)
    return request({
        url: '/garbageEnterpriseReport',
        method: 'post',
        data
    })
}
/**
 * @Description: 收运点管理：删除
 */
export function delEnterpriseReport(data) {
    return request({
        url: '/garbageEnterpriseReport',
        method: 'delete',
        data
    })
}
/**
 * @Description: 收运点管理：编辑 （已废弃）
 */
export function editEnterpriseReport(data) {
    return request({
        url: '/garbageEnterpriseReport',
        method: 'put',
        data
    })
}
export function garbageEnterpriseReportStatus(data) {
    return request({
        url: '/garbageEnterpriseReport/status',
        method: 'put',
        data
    })
}
/**
 * @Description: 收运点管理：根据id查详情
 */
export async function getEnterpriseReportInfo(id) {
    let res = await request({
        url: `/garbageEnterpriseReport/${id}`,
        method: 'get',
    })
    if (res.data) {
        res.data.objx = res.data.gdx
        res.data.objy = res.data.gdy
    }
    return res
    // return request({
    //     url: `/garbageEnterpriseReport/${id}`,
    //     method: 'get',
    // })
}
/**
 * @Description: 根据名称查企业list
 */

export function getEnterpriseList(params) {
    return request({
        url: '/garbageEnterprise/list',
        method: 'post',
        data: params
    })
}

/**
 * @Description: 收运任务
 */


/**
 * @Description: 根据id查任务详情
 */
export function getCollectionJobInfo(id) {
    return request({
        url: `/garbagePlanTask/${id}`,
        method: 'get'
    })
}

/**
 * @Description: 根据条件查收运记录
 */
export function getCollectionRecordList(data) {
    return request({
        url: '/garbageTaskRecords/list',
        method: 'post',
        data
    })
}
/**
 * @Description: 根据车牌号搜车辆列表
 */

export function gerCarRecordList(data) {
    return request({
        url: '/garbageCar/list',
        method: 'post',
        data
    })
}

/**
 * @Description: 数据填报相关接口
 */

/**
 * @Description: 新增
 */
export function addGarbageData(data) {
    return request({
        url: '/garbageData',
        method: 'post',
        data
    })
}
/**
 * @Description: 删除
 */

export function delGarbageData(data) {
    return request({
        url: '/garbageData',
        method: 'delete',
        data
    })
}
/**
 * @Description: 编辑
 */
export function editGarbageData(data) {
    return request({
        url: '/garbageData',
        method: 'put',
        data
    })
}
/**
 * @Description: 根据id查询数据填报详情
 */
export function getGarbageDataDetail(id) {
    return request({
        url: `/garbageData/${id}`,
        method: 'get',
    })
}
/**
 * 配置标准运收数量
 * @param data {type:类型(1餐厨垃圾，2垃圾运收) ，standard 日标准收运量}
 * @returns 
 */
export function standardAmountConfig(data) {
    return request({
        url: '/garbagePoint/standard',
        method: 'post',
        data
    })
}
/**
 * 获取标准运收数量
 * @param type type:类型(1餐厨垃圾，2垃圾运收) 
 * @returns 
 */
export function getStandardAmount(type) {
    return request({
        url: `/garbagePoint/standard/${type}`,
        method: 'get',
    })
}
/**
 * 总体概览
 * @param type type:类型(1餐厨垃圾，2垃圾运收) 
 * @returns 
 */
export function getOverview(type) {
    return request({
        url: `/garbageStatistical/overview/${type}`,
        method: 'get',
    })
}
/**
 * 垃圾收运量
 * @param data 
 * @returns 
 */
export function getGarbageAmount(data) {
    return request({
        url: '/garbageStatistical/records',
        method: 'post',
        data
    })
}
/**
 * 垃圾收运量导出
 * @param data 
 * @returns 
 */
export function garbageRecordExport(data) {
    return request({
        url: '/garbageStatistical/records/export',
        method: 'post',
        data
    })
}
/**
 * 收运点完成情况
 * @param data
 * @returns 
 */
export function getGarbageCollectionDetail(data) {
    return request({
        url: '/garbageStatistical/point',
        method: 'post',
        data
    })
}
/**
 * 垃圾任务完成情况
 * @param data  
 * @returns 
 */
export function getGarbageTaskDetail(data) {
    return request({
        url: '/garbageStatistical/task',
        method: 'post',
        data
    })
}
/**
 * 获取收运点排名
 * @returns 
 */
export function getRank(data) {
    return request({
        url: '/garbageStatistical/rank',
        method: 'post',
        data
    })
}
/**
 * 查询收运点
 * @param type 1餐厨垃圾，2垃圾运收
 * @returns 
 */
export function getGarbagePoint(type = 1) {
    return request({
        url: '/garbagePoint/list',
        method: 'post',
        data: {
            "page": {
                "current": 1,
                "size": -1
            },
            "customQueryParams":  {
                "type": type, // 1餐厨垃圾，2垃圾运收
            },
            "sorts": []
        }
    })
}
