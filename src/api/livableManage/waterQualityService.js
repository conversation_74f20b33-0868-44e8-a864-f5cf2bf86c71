import request from '@/utils/request';

// 设备详情
export function getDetailInfo(id) {
    return request({
        url: `/waterQualityDevice/${id}`,
        method: 'get'
    })
}
// 新增土壤设备
export function newAddWaterDevice(data) {
    return request({
        url: '/waterQualityDevice',
        method: 'post',
        data,
        headers: {
            'Content-Type': 'application/json'
        }
    })
}
// 获取区域树
export function getAreaTree(type, table) {
    return request({
        url: `/deviceAreaTree/getTree?type=${type}&table=${table}`,
        method: 'get',
        headers: {
            'Proxy-Connection': 'keep-alive'
        }
    })
}
// 删除土壤管理设备/gardenSoilDevice
export function deleteWaterDevice(ids) {
    return request({
        url: '/waterQualityDevice',
        method: 'delete',
        data: {
            ids
        },
        headers: {
            'Content-Type': 'application/json'
        }
    })
}
// 编辑原理土壤设备/gardenSoilDevice
export function editWaterDevice(data) {
    return request({
        url: '/waterQualityDevice',
        method: 'put',
        data,
        headers: {
            'Content-Type': 'application/json'
        }
    })
}

// 查询环境概览设备列表
export function queryDevices(data) {
    return request({
        url: '/environmentOverview/queryDevices',
        method: 'post',
        data
    })
}

// 查询环境概览设备列表
export function queryDeviceOnlineAlarmCount(data) {
    return request({
        url: '/environmentOverview/queryDeviceOnlineAlarmCount',
        method: 'post',
        data
    })
}
