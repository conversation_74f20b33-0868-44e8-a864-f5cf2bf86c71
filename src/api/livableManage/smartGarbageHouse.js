import request from '@/utils/request';

/**
 * 垃圾屋管理
 */
// 新增
export function garbageAdd(data) {
    return request({
        url: '/garbageHouseDevice/batch',
        method: 'post',
        data
    })
}
// 删除
export function garbageDelete(ids) {
    return request({
        url: `/garbageHouseDevice`,
        method: 'delete',
        data: {
            ids
        }
    })
}
// 修改
export function garbageEdit(data) {
    return request({
        url: '/garbageHouseDevice',
        method: 'put',
        data
    })
}
// 详情
export function garbageDetail(id) {
    return request({
        url: `/garbageHouseDevice/${id}`,
        method: 'get',
    })
}
// 列表
export function garbageHouseDeviceList(data) {
    return request({
        url: '/garbageHouseDevice/list',
        method: 'post',
        data
    })
}
// 在线
export function deviceStatisticsOnline(data) {
    return request({
        url: '/deviceStatistics/online',
        method: 'post',
        data
    })
}
// 在线情况
export function deviceStatisticsDay(data) {
    return request({
        url: '/deviceStatistics/day',
        method: 'post',
        data
    })
}
// 告警
export function deviceAlarms(data) {
    return request({
        url: '/garbageHouseDeviceMap/alarms',
        method: 'post',
        data
    })
}
// 告警单条
export function deviceAlarmByCode(deviceCode) {
    return request({
        url: `/garbageHouseDeviceMap/alarm?deviceCode=${deviceCode}`,
        method: 'get',
    })
}

