import request from '@/utils/request';

// 设备详情
export function getDetailInfo(id) {
    return request({
        url: `/noiseDevice/${id}`,
        method: 'get'
    })
}
// 新增土壤设备
export function newAddDevice(data) {
    return request({
        url: '/noiseDevice',
        method: 'post',
        data,
        headers: {
            'Content-Type': 'application/json'
        }
    })
}
// 获取区域树
export function getAreaTree(type, table) {
    return request({
        url: `/deviceAreaTree/getTree?type=${type}&table=${table}`,
        method: 'get',
        headers: {
            'Proxy-Connection': 'keep-alive'
        }
    })
}
// 删除土壤管理设备/gardenSoilDevice
export function deleteDevice(ids) {
    return request({
        url: '/noiseDevice',
        method: 'delete',
        data: {
            ids
        },
        headers: {
            'Content-Type': 'application/json'
        }
    })
}
// 编辑原理土壤设备/gardenSoilDevice
export function editDevice(data) {
    return request({
        url: '/noiseDevice',
        method: 'put',
        data,
        headers: {
            'Content-Type': 'application/json'
        }
    })
}

// 查询环境概览设备列表
export function queryDevices(data) {
    return request({
        url: '/environmentOverview/queryDevices',
        method: 'post',
        data
    })
}

// 查询环境概览设备列表
export function queryDeviceOnlineAlarmCount(data) {
    return request({
        url: '/environmentOverview/queryDeviceOnlineAlarmCount',
        method: 'post',
        data
    })
}
