import request from '@/utils/request'

const url = '/lakeMonitoringDevice'

// 设备详情
export function getDetailInfo(id) {
    return request({
        url: `${url}/${id}`,
        method: 'get'
    })
}
// 新增设备
export function newAddDevice(data) {
    return request({
        url: `${url}`,
        method: 'post',
        data,
        headers: {
            'Content-Type': 'application/json'
        }
    })
}
// 删除设备
export function deleteDevice(ids) {
    return request({
        url: `${url}`,
        method: 'delete',
        data: {
            ids
        },
        headers: {
            'Content-Type': 'application/json'
        }
    })
}
// 编辑设备
export function editDevice(data) {
    return request({
        url: `${url}`,
        method: 'put',
        data,
        headers: {
            'Content-Type': 'application/json'
        }
    })
}

// 获取区域树
export function getAreaTree(type, table) {
    return request({
        url: `/deviceAreaTree/getTree?type=${type}&table=${table}`,
        method: 'get',
        headers: {
            'Proxy-Connection': 'keep-alive'
        }
    })
}

// 查询环境概览设备列表
export function queryDevices(data) {
    return request({
        url: '/environmentOverview/queryDevices',
        method: 'post',
        data
    })
}

// 查询环境概览设备列表
export function queryDeviceOnlineAlarmCount(data) {
    return request({
        url: '/environmentOverview/queryDeviceOnlineAlarmCount',
        method: 'post',
        data
    })
}
