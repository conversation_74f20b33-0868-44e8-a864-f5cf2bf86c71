import request from "@/utils/request";
export interface AddSeat {
  objId: string;
  seatCode: string;
  seatName: string;
  inspectionCycle: string;
  factoryName: string;
  seatUnit: string;
  contactPhone: string;
  seatMaterial: string;
  simCode: string;
  factoryContact: string;
  installTime: string;
  factoryTime: string;
  remark: string;
  objName: string;
}
// 新建座椅
export function addSeat(data: AddSeat) {
  return request({
    url: '/seatInfo/addSeat',
    method: 'POST',
    data
  })
}
// 座椅详情/seatInfo/{id}
export function getSeatDetail(id: string | number) {
  return request({
    url: `/seatInfo/selectDtoOne`,
    method: 'POST',
    data: {
      id
    }
  })
}
// 编辑座椅/seatInfo/editSeat
export function editSeat(data: AddSeat) {
  return request({
    url: '/seatInfo/editSeat',
    method: 'PUT',
    data
  })
}
//删除座椅
export function deleteSeat(idList: number[]) {
  return request({
    url: '/seatInfo/deleteSeat',
    method: 'DELETE',
    params: { idList: idList.toString() }
  })
}
// 获取座椅全量数据 
export interface Params {
  szsq?: string, //所属社区
  szdywg?: string, //所属单元网格
  szjd?: string, //所属街道
}
export function getSeatList(data:Params) {
  return request({
    url: '/seatInfo/selectDtoPage',
    method: 'POST',
    data: {
      "page": {
        "current": -1,
        "size": 10
      },
      "customQueryParams": {
        "objInfo": data
      },
      "sorts": []
    }
  })
}