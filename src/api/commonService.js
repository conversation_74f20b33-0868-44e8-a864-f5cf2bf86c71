
import request from '@/utils/request';
// 通用调项目级接口

export const commonService = {
    /**
     * 告警事件查询
     */
    getDeviceAndAreaTree(url, data, method = 'get') {
        return request({
            url: url,
            method,
            data,
        });
    },
    /**
     * 告警事件查询
     */
    getAlarmEventList(data) {
        return request({
            url: '/alarmEvent/eventType/selectAllByCondition',
            method: 'post',
            data,
        });
    },
    /**
     * 查询历史流水并分页
     */
    queryHistoricDataFromEsPage(data) {
        return request({
            url: '/monitor/queryHistoricDataFromEsPage',
            method: 'post',
            data,
        });
    },
    /**
     * 查询物模型并过滤属性
     */
    getPhysicModelPropAndFilter(data) {
        return request({
            url: '/monitor/getPhysicModelPropAndFilter',
            method: 'post',
            data,
        });
    },
    /**
     * 查询设备部件和属性状态
     */
    getDevicePropList(params) {
        return request({
            url: '/deviceStatus/getDeviceObjInfoDevicePropertyStatusListInfo',
            method: 'get',
            params,
        });
    },
    /**
     * 根据应用，模块，查设备型号的接口
     */
    getConfigDeviceUnit(data) {
        return request({
            url: '/deviceExtendInfo/getConfigDeviceUnit',
            method: 'post',
            data,
        });
    },
    /**
     * 根据应用，模块，查部件类型的接口
     */
    getObjCategoryConfig(modelId) {
        return request({
            url:
                '/device/objApplicationModelObjCategoryConfig/getObjCategoryConfig?modelId=' +
                modelId,
            method: 'get',
        });
    },
    // 获取区域树
    getAreaLocationTree(tableName, type) {
        return request({
            url: `/deviceAreaTree/getTree?table=${tableName}&type=${type}`,
            method: 'get',
        });
    },
    // 获取消息通知列表
    getMessageList(current = 1) {
        return request({
            url: '/baseMessage/list',
            method: 'post',
            data: {
                page: {
                    current: current,
                    size: 5,
                },
                customQueryParams: {
                    readStatus: current === -1 ? 1 : 0,
                },
            },
        });
    },
    // 获取未读消息条数
    getUnreadMessages() {
        return request({ url: '/baseMessage/num', method: 'get' });
    },
    // 读取消息
    readMessage(id) {
        return request({ url: '/baseMessage/read', method: 'put', data: { id }});
    },
    // 一键读取消息
    oneReadMessage() {
        return request({ url: '/baseMessage/readBatch', method: 'get' });
    },
    // 根据id查询消息通知详情
    getMessageDetail(id) {
        return request({ url: `/baseMessage/${id}`, method: 'get' });
    },
    // 新增消息通知
    addMessage(data) {
        return request({ url: '/baseMessage', method: 'post', data });
    },
    // 查询模块下拉
    queryModelSelect(data) {
        return request({ url: '/alarmEvent/eventType/selectAllByCondition', method: 'post', data });
    },
    /**
     * 根据告警编号查询工单详情
     */
    getProcessByAlarmNo(data) {
        return request({
            url: `/alarm/process/${data}`,
            method: 'get',
        });
    },
    /**
     * 根据deviceCode修改useStatus
     */
    updateUseStatus(deviceCode, useStatus) {
        let data = {
            deviceId: deviceCode,
            useStatus,
        };
        return request({
            url: `/deviceExtendInfo/useStatus`,
            method: 'put',
            data,
        });
    },
    allLinkappUser() {
        return request({
            url: `/common/user/userList`,
            method: 'post',
            data: {}
        });
    },
    /**
     * 业务模块
     */
    selectAllByCondition(data) {
        return request({
            url: '/ruleEngine/eventType/selectAllByCondition',
            method: 'post',
            data,
            baseURL: '/api'
        })
    },
    /**
     * 得到设备型号
     */
    getDeviceUnitList(data = {}) {
        return request({
            url: '/deviceUnit/selectByCondition',
            method: 'post',
            data
        })
    },
    /**
     * 得到设备列表
     */
    getdeviceManageList(data = {}) {
        return request({
            url: 'deviceManage/selectPage',
            method: 'post',
            data
        })
    },


    /**
     * 按照型号获取设备
     */
    getExtendInfoList(data = {}) {
        return request({
            url: '/deviceExtendInfo/getDeviceBySbxh',
            method: 'post',
            data
        })
    },
    /**
     * 得到视频监控名字
     */
    getVideoMonitorName(data = {}) {
        return request({
            url: 'deviceExtendInfo/findNotSelectByModelId',
            method: 'post',
            data
        })
    },
    /**
     * 新增告警处理
     */
    addAlarmHandle(data) {
        return request({ url: '/alarm/dispose', method: 'post', data });
    },
    /**
     * 获取告警处理详情
     */
    getAlarmHandleDetail(alarmId) {
        return request({ url: `/alarm/dispose/${alarmId}`, method: 'get' });
    },
};
