import request from "@/utils/request";
/**
 * 园区巡查
 */
// 类型列表
interface List {
    page: Page;
    customQueryParams: CustomQueryParams;
}
interface CustomQueryParams {}
interface Page {
    size: number;
    current: number;
}
export function getPatrolCategoryList(data: List) {
    return request({
        url: "/patrolCategory/list",
        method: "post",
        data,
    });
}
// 类型新增
interface addCategory {
    name: string;
    note: string;
}
export function addPatrolCategory(data: addCategory) {
    return request({
        url: "/patrolCategory",
        method: "post",
        data,
    });
}
// 类型编辑
interface editCategory {
    id: number;
    name: string;
    note: string;
}
export function editPatrolCategory(data: editCategory) {
    return request({
        url: "/patrolCategory",
        method: "put",
        data,
    });
}
// 类型删除
interface deleteCategory {
    ids: number[];
}
export function deletePatrolCategory(data: deleteCategory) {
    return request({
        url: "/patrolCategory",
        method: "delete",
        data,
    });
}
/**
 * 巡检方案管理
 */
// 巡检方案列表
export function getPatrolProgrammeList(data: List) {
    return request({
        url: "/patrolProgramme/list",
        method: "post",
        data,
    });
}

// 巡检方案管理新增
interface addProgramme {
    name: string;
    categoryId: number;
    src: number;
    status: number;
    expressions: Expression[];
}

interface Expression {
    logicCode: string;
    attribute: string;
    calculateSign: string;
    value: string;
}
export function addProgramme(data: addProgramme) {
    return request({
        url: "/patrolProgramme",
        method: "post",
        data,
    });
}
// 巡检方案编辑
interface editProgramme extends addProgramme {
    id: number;
}
export function editProgramme(data: editProgramme) {
    return request({
        url: "/patrolProgramme",
        method: "put",
        data,
    });
}
// 巡检方案删除
interface deleteProgramme extends deleteCategory {}
export function deleteProgramme(data: deleteProgramme) {
    return request({
        url: "/patrolProgramme",
        method: "delete",
        data,
    });
}
// 巡检方案详情
export function getProgrammeDetail(id: string) {
    return request({
        url: `/patrolProgramme/${id}`,
        method: "get",
    });
}
// 巡检方案启用禁用
interface editStatus {
    id: number;
    status: number;
}
export function editStatus(data: editStatus) {
    return request({
        url: "/patrolProgramme/status",
        method: "put",
        data,
    });
}
/**
 * 云巡检计划管理
 */
// 获取云巡检计划管理设备类型下拉
export function planDeviceSecondTypeNames() {
    return request({
        url: "/deviceExtendInfo/deviceSecondTypeNames",
        method: "get",
    });
}
//增加云巡检计划
interface addPatrolPlan {
    planName?: string;
    planNo?: string;
    programmeId?: number;
    timeType?: number | string;
    timeValues?: string | string[];
    actionTime?: string;
    note?: string;
    deviceRefVoList?: DeviceRefVoList[];
}

interface DeviceRefVoList {
    deviceCode: string;
}
export function addPatrolPlan(data: addPatrolPlan) {
    return request({
        url: "/patrolPlan",
        method: "post",
        data,
    });
}
// 查询云巡检计划管理详情
export function getPatrolPlanDetail(id: string) {
    return request({
        url: `/patrolPlan/${id}`,
        method: "get",
    });
}
// 获取设备型号下拉接口
export function getConfigDeviceUnit() {
    return request({
        url: "/deviceExtendInfo/getConfigDeviceUnit",
        method: "post",
        data: {},
    });
}
// 获取设备类型下拉
export function getDeviceSecondTypeNames() {
    return request({
        url: "/deviceExtendInfo/deviceSecondTypeNames",
        method: "get",
    });
}
// 巡检方案启用禁用
interface editPatrolPlanStatus extends Omit<editStatus, "status"> {
    planStatus: number;
}
export function editPatrolPlanStatus(data: editPatrolPlanStatus) {
    return request({
        url: "/patrolPlan/status",
        method: "put",
        data,
    });
}
// 删除云巡检计划
interface deletePatrolPlan {
    ids: number[];
}
export function deletePatrolPlan(data: deletePatrolPlan) {
    return request({
        url: "/patrolPlan",
        method: "delete",
        data,
    });
}
//编辑云巡检计划
interface editPatrolPlan {
    id: number;
    planName: string;
    planNo: string;
    programmeId: number;
    timeType: number;
    timeValues: string;
    actionTime: string;
    note: string;
    deviceRefVoList: DeviceRefVoList[];
}

interface DeviceRefVoList {
    deviceCode: string;
}
export function editPatrolPlan(data: editPatrolPlan) {
    return request({
        url: "/patrolPlan",
        method: "put",
        data,
    });
}
/**
 * 云巡检报告
 */
//云巡检报告详情
export function getPatrolReportDetail(id: string) {
    return request({
        url: `/patrolReport/${id}`,
        method: "get",
    });
}
//云巡检报告删除
interface deletePatrolReport {
    ids: number[];
}
export function deletePatrolReport(data: deletePatrolReport) {
    return request({
        url: "/patrolReport",
        method: "delete",
        data,
    });
}

//运检报告批量导出获取key
export interface BatchExportPatrolReport {
    planName?: any;
    planNo?: any;
    status?: any;
    ids: any[];
}
export function batchExportPatrolReport(data: BatchExportPatrolReport) {
    return request({
        url: "/patrolReport/exportZip",
        method: "post",
        data,
    });
}
//查询批量导出路径
export function queryBatchExportPatrolReportUrl(key: string) {
    return request({
        url: `/sync/getRedisResult/${key}`,
        method: "get",
    });
}

// 新增巡逻计划
export function addProwlPlan(data: addPatrolPlan) {
    return request({
        url: "/prowlPlan",
        method: "post",
        data,
    });
}
// 删除巡逻计划
export function delProwlPlan(data: addPatrolPlan) {
    return request({
        url: "/prowlPlan",
        method: "delete",
        data,
    });
}
// 编辑巡逻计划
export function editProwlPlan(data: addPatrolPlan) {
    return request({
        url: "/prowlPlan",
        method: "put",
        data,
    });
}
// 查看巡逻计划
export function detailProwlPlan(id: string) {
    return request({
        url: "/prowlPlan/" + id,
        method: "get",
    });
}
// 巡逻计划 - 状态
export function statusProwlPlan(data: addPatrolPlan) {
    return request({
        url: "/prowlPlan/status",
        method: "put",
        data,
    });
}
// 设置巡逻计划
export function prowlPlanDeviceRef(data: addPatrolPlan) {
    return request({
        url: "/prowlPlan/deviceRef",
        method: "post",
        data,
    });
}

// 巡逻归档 - 状态
export function statusProwlTask(data: addPatrolPlan) {
    return request({
        url: "/prowlTask",
        method: "put",
        data,
    });
}

// 巡逻归档 - 详情
export function detailProwlTask(id: string) {
    return request({
        url: `/prowlTask/result/${id}`,
        method: "get",
    });
}

// 巡逻归档 - 编辑
export function editProwlResult(data: addPatrolPlan) {
    return request({
        url: `/prowlResult`,
        method: "put",
        data
    });
}
