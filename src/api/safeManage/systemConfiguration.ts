
import request from '@/utils/request';
/**
 * 系统配置管理
 */
// 查询系统配置

export function getType(type:string){
  return request({
    url:'/system/systemConfig/getType',
    method:'get',
    params:{
      type
    }
  })
}
interface editFormat {
  sysValue: string;
  sysKey: string;
  description: string;
  type: number;
}
export function editFormat(data:Array<editFormat>){
  return request({
    url:'/system/systemConfig',
    method:'put',
    data
  })
}
