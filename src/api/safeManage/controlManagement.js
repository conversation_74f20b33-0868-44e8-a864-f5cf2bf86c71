import request from '@/utils/request';
import { exportService } from '@/api/exportService';

/**
 * @Description: 布控管理 -- 地图
 */

// 人车摄像头数量统计
export function countNum(data) {
    return request({
        url: `/deployMap/countNum`,
        method: 'POST',
        data
    })
}
// 人车库数据
export function selectPersonCarStoreDtoPage(data) {
    return request({
        url: '/deployMap/selectPersonCarStoreDtoPage',
        method: 'POST',
        data
    })
}
// 人车库布控识别记录数据
export function selectPersonCarRecordDtoPage(data) {
    return request({
        url: '/deployMap/selectPersonCarRecordDtoPage',
        method: 'POST',
        data
    })
}
// 查询摄像头识别记录
export function selectCameraRecordDtoPage(data) {
    return request({
        url: '/deployMap/selectCameraRecordDtoPage',
        method: 'POST',
        data
    })
}
/**
 * @Description: 布控管理 -- 分组管理
 */

// 分组管理 -- 新增分组
export function addGroup(data) {
    return request({
        url: `/deployGroup/addGroup`,
        method: 'POST',
        data
    })
}
// 分组管理 -- 删除分组
export function delGroup(ids) {
    return request({
        url: `/deployGroup`,
        method: 'DELETE',
        params: { idList: ids }
    })
}
// 分组管理 -- 修改分组
export function editGroup(data) {
    return request({
        url: `/deployGroup/updateGroup`,
        method: 'PUT',
        data
    })
}
// 分组管理 -- 根据id查分组详情

export function groupDetail(id) {
    return request({
        url: `/deployGroup/selectOne`,
        method: 'POST',
        data: { id }
    })
}

/**
 * @Description: 布控管理 -- 摄像头管理
 */

// 分组管理 -- 批量新增摄像头
export function addCamera(data) {
    return request({
        url: `/deployCamera/batchInsert`,
        method: 'POST',
        data
    })
}
// 分组管理 -- 删除摄像头
export function delCamera(data) {
    return request({
        url: `/deployCamera`,
        method: 'DELETE',
        params: data
    })
}
// 分组管理 -- 修改摄像头
export function editCamera(data) {
    return request({
        url: `/deployCamera/updateDtoOne`,
        method: 'PUT',
        data
    })
}
// 分组管理 -- 根据id查摄像头详情

export function cameraDetail(id) {
    return request({
        url: `/deployCamera/selectDtoOne`,
        method: 'POST',
        data: { id }
    })
}

/**
 * @Description: 布控人员库
 */

// 布控人员库 -- 分页查分组布控人员

export function selectGroupPersonPage(data) {
    return request({
        url: `/deployPerson/selectGroupPersonPage`,
        method: 'POST',
        data
    })
}

// 布控人员库 -- 新增布控人员

export function addGroupPerson(data) {
    return request({
        url: `/deployPerson/addPerson`,
        method: 'POST',
        data
    })
}

// 布控人员库 -- 删除布控人员
export function delGroupPerson(data) {
    return request({
        url: `/deployPerson`,
        method: 'DELETE',
        params: {
            idList: data
        }
    })
}
// 布控人员库 -- 编辑布控人员
export function editGroupPerson(data) {
    return request({
        url: `/deployPerson/updatePerson`,
        method: 'PUT',
        data
    })
}

// 布控人员库 -- 根据id查询布控人员
export function groupPersonDetail(id) {
    return request({
        url: `/deployPerson/selectDtoOne`,
        method: 'POST',
        data: {
            id
        }
    })
}
// 布控人员库 -- 布控控制
export function personJoinDeploy(data) {
    return request({
        url: '/deployPerson/joinDeploy',
        method: 'POST',
        data
    })
}


/**
 * @Description: 布控车辆库
 */

// 分页查询分组车辆
export function selectGroupCarPage(data) {
    return request({
        url: `/deployCar/selectGroupCarPage`,
        method: 'POST',
        data
    })
}

// 布控车辆库 -- 新增
export function addGroupCar(data) {
    return request({
        url: `/deployCar/addCar`,
        method: 'POST',
        data
    })
}
// 布控车辆库 -- 删除
export function delGroupCar(data) {
    return request({
        url: `/deployCar`,
        method: 'DELETE',
        params: {
            idList: data
        }
    })
}
// 布控车辆库 -- 编辑
export function editGroupCar(data) {
    return request({
        url: `/deployCar/updateCar`,
        method: 'PUT',
        data
    })
}
// 布控车辆库 -- 根据id查详情
export function groupCarDetail(id) {
    return request({
        url: `/deployCar/selectDtoOne`,
        method: 'POST',
        data: {
            id
        }
    })
}

// 布控管理
export function carJoinDeploy(data) {
    return request({
        url: '/deployCar/joinDeploy',
        method: 'POST',
        data
    })
}

/**
 * @Description: 布控任务相关接口
 * @date 2023-05-10 10:41:35
 */

// 新增布控任务
export function addStrategy(data) {
    return request({
        url: `/deployStrategy/addStrategy`,
        method: 'POST',
        data
    })
}

// 删除布控任务

export function delDeployStrategy(data) {
    return request({
        url: `/deployStrategy`,
        method: 'DELETE',
        params: {
            idList: data
        }
    })
}

// 编辑布控任务
export function editStrategy(data) {
    return request({
        url: `/deployStrategy/updateStrategy`,
        method: 'PUT',
        data
    })
}

// 根据id查布控任务详情
export function strategyDetail(id) {
    return request({
        url: `/deployStrategy/selectDtoOne`,
        method: 'POST',
        data: {
            id
        }
    })
}
// 获取布控任务的分组列表
export function getStrategyGroupList(data) {
    return request({
        url: `/deployStrategy/getStrategyGroupList`,
        method: 'POST',
        data
    })
}

// 获取任务数量统计信息
export function countStrategyRecordNum(data) {
    return request({
        url: '/deployRecord/countStrategyRecordNum',
        method: 'POST',
        data
    })
}

// 根据id查识别记录信息
export function getDeployRecordDetail(id) {
    return request({
        url: `/deployRecord/selectDtoOne`,
        method: 'POST',
        data: {
            id
        }
    })
}

// 根据id查识别记录信息
export function getDeployTraceRecord(data) {
    return request({
        url: '/deployTraceRecord/selectDtoPage',
        method: 'POST',
        data
    })
}

/**
 * @Description: 追溯任务相关接口
 */
// 追溯任务轨迹
export function deployTraceById(id) {
    return request({
        url: `/deployTrace/${id}`,
        method: 'get',
    })
}
// 新增追溯任务
export function addDeployTrace(data) {
    return request({
        url: `/deployTrace/addTrace`,
        method: 'POST',
        data
    })
}
// 删除追溯任务
export function delDeployTrace(idList) {
    return request({
        url: `/deployTrace`,
        method: 'DELETE',
        params: {
            idList
        }
    })
}

// 追溯任务 开始追溯
export function startTrace(id) {
    return request({
        url: `/deployTrace/startTrace`,
        method: 'POST',
        data: {
            id
        }
    })
}

/**
 * 更新布控任务状态
 * @param {string} id 任务ID
 * @param {number} status 状态（1: 启用, 0: 停用）
 */
export function updateDeployStrategyStatus(id, status) {
    return request({
        url: `/deployStrategy/updateStatus/${id}/${status}`,
        method: 'PUT'
    })
}

/**
 * 更新布控记录状态
 * @param {string} id 任务ID
 * @param {number} status 状态（1: 启用, 0: 停用）
 */
export function updateRecordStatus(id, status) {
    return request({
        url: `/deployRecord/updateProcessStatus/${id}/${status}`,
        method: 'PUT'
    })
}

/**
 * 批量导出识别信息
 * @param {Object} params 查询条件
 */
export function exportIdentificationInfo(data) {
    return request({
        url: "/deployRecord/exportPhotos",
        method: "post",
        data
    });
    // return exportService('/deployRecord/exportPhotos', params);
}

/**
 * 批量导出记录
 * @param {Object} params 查询条件
 */
export function exportRecord(data) {
    // return exportService('/deployRecord/export', params);
    return request({
        url: "/deployRecord/export",
        method: "post",
        data,
    });
}
