import request from "@/utils/request";
import { ListAllReqBody } from '../common'


// 应急监测

export interface EmergencyGuard {
    id?: number;
    code?: string;
    name?: string; //值班名称
    type?: number; //值班类型(1临时值班，2长期值班)
    timeRange?: any[];
    timeValues?: string; //值班时间段，多个逗号分隔
    timeValueArray?: any[];
    startTime?: string;
    endTime?: string;
    mainName?: string; //主班人姓名
    mainPhone?: string; //主班人联系电话
    deputyName?: string; //副班人姓名
    deputyPhone?: string; //副班人联系电话
    companyName?: string; //单位名称
    fax?: string; //传真
    note?: string; //备注
}
interface EventSearch {
    event: {
        disposeStatus: number;
        eventType?: string;
        eventCode: string;
        eventLevel: number;
        eventSource: number;
    };
}
interface EditEventStatusParams {
    disposeStatus: number;
    id: number;
}
interface DisposeParam {
    operateStatus: number;
    eventId: number;
    fileUrls: string;
    eventTaskId: number;
    operateTime: string;
    operateContent: string;
}
export const emergencyMonitoringService = {
    /**
     * 增加应急值守
     */
    addEmergencyGuard(data: EmergencyGuard) {
        return request({
            url: "/emergencyGuard",
            method: "post",
            data,
        });
    },
    /**
     * 删除应急值守
     */
    delEmergencyGuard(ids: number[]) {
        return request({
            url: `/emergencyGuard`,
            method: "delete",
            data: {
                ids,
            },
        });
    },
    /**
     * 编辑应急值守
     */
    editEmergencyGuard(data: EmergencyGuard) {
        return request({
            url: "/emergencyGuard",
            method: "put",
            data,
        });
    },
    /**
     * 查询应急值守详情
     */
    getEmergencyGuard(id: string) {
        return request({
            url: `/emergencyGuard/${id}`,
            method: "get",
        });
    },
    /**
     * 应急事件导出
     * @param data 请求体
     * @returns
     */
    exportEmergencyEvent(data: EventSearch) {
        return request({
            url: "/safeEmergencyEvent/selectEventDtoPageExport",
            method: 'post',
            data,
        })
    },
    /**
     * 应急事件详情
     * @param {string} id
     * @returns
     */
    getEmergencyEventDetail(id: string) {
        return request({
            url: `/safeEmergencyEvent/eventDetail/${id}`,
            method: "get",
        });
    },
    /**
     * 删除应急事件
     * @param {number[]} idList
     * @returns
     */
    delEmergencyEvent(idList: number[]) {
        return request({
            url: `/safeEmergencyEvent/eventBatchDelete`,
            method: "delete",
            params: { idList: idList.toString() }
        });
    },
    /**
     * 启动应急事件告警
     * @param data
     * @returns
     */
    emergencyEventTriggerAlarm(data: { idList: string }) {
        return request({
            url: "/safeEmergencyEvent/alarmTriggerEventPlan",
            method: 'post',
            data
        })
    },
    /**
     * 编辑事件状态
     * @param data
     * @returns
     */
    editEventStatus(data: EditEventStatusParams) {
        return request({
            url: "/safeEmergencyEvent/updateEventStatus",
            method: 'put',
            data
        })
    },
    /**
     * 事件任务处理
     * @param data
     * @returns
     */
    taskExecuteDisposeEventTask(data: DisposeParam) {
        return request({
            url: "/safeEmergencyEventTask/eventTaskOperateDeal",
            method: 'post',
            data
        })
    },
    /**
     * 事件任务处理记录
     * @param id
     * @returns
     */
    taskExecuteRecord(id: string) {
        return request({
            url: `/safeEmergencyEventTask/eventTaskOperate/${id}`,
            method: "get",
        });
    },
    /**
     * 事件任务详情
     * @param id
     * @returns
     */
    gerTaskExecuteDetail(id: string) {
        return request({
            url: `/safeEmergencyEventTask/eventTaskDetail/${id}`,
            method: "get",
        });
    },
    /**
     * 事件下的预案
     * @param eventId
     * @returns
     */
    getPlanList(eventId: string) {
        return request({
            url: `/safeEmergencyEvent/selectEventPlan/${eventId}`,
            method: "post",
        })
    },
    getAllEventList(){
        return request({
            url:'/safeEmergencyEvent/selectEventDtoPage',
            method:'post',
            data:ListAllReqBody
        })
    },
    selectEventTaskDtoPage(data:recordsRequest){
        return request({
            url:'/safeEmergencyEvent/selectEventTaskDtoPage',
            method:'post',
            data
        })
    },
    topNumCount(data:any){
        return request({
            url:'/emergencyEventOverview/topNumCount',
            method:'post',
            data
        })
    },
    eventNumCountKx(data:any){
        return request({
            url:'/emergencyEventOverview/eventNumCountKx',
            method:'post',
            data
        })
    },
    eventDistrNumCount(data:recordsRequest){
        return request({
            url:'/emergencyEventOverview/eventDistrNumCount',
            method:'post',
            data
        })
    },
    eventAvgDealTimeCount(data:recordsRequest){
        return request({
            url:'/emergencyEventOverview/eventAvgDealTimeCount',
            method:'post',
            data
        })
    }
};
