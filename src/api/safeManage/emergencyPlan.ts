import request from "@/utils/request";
import { ListAllReqBody } from '../common'
/**
 * 任务方案管理
 */
// 方案新增
interface AddParams {
  emergencyTaskVoList?: EmergencyTaskVoList[];
  taskScheme: TaskScheme;
}

interface TaskScheme {
  remark: string;
  taskNum: number;
  schemeName: string;
  executorType: number;
}

interface EmergencyTaskVoList {
  id?: number;
  areaPoints?: string;
  taskExecutorList: TaskExecutorList[];//
  taskType?: number;//
  szdywgCode?: string;
  taskName: string;//
  szdywg?: string;
  areaPath?: string;
  taskDuration: number;//
  taskContent: string;//
  szjdCode?: string;
  beforeTaskNames?: string;
  remark?: string;
  szjd?: string;
  szsqCode?: string;
  szsq?: string;
  executorType: number;
}

interface TaskExecutorList {
  executorTeamId?: number;
  executorId?: number;
  executorName?: string;
  emergencyPeopleType?: number;
}
function addTaskPlan(data: AddParams) {
  return request({
    url: '/safeEmergencyTaskScheme/addTaskScheme',
    method: 'post',
    data
  })
}
//方案删除
function deleteTaskPlan(idList: number[]) {
  return request({
    url: '/safeEmergencyTaskScheme/batchDelete',
    method: 'delete',
    params: { idList: idList.toString() }
  })
}
//  方案获取详情
function getTaskPlanDetail(id: string) {
  return request({
    url: `/safeEmergencyTaskScheme/taskSchemeDetail/${id}`,
    method: 'get',
  })
}
//  编辑任务方案

function editTaskPlan(data: any) {
  return request({
    url: '/safeEmergencyTaskScheme/updateTaskScheme',
    method: 'post',
    data
  })
}
//获取任务类型列表
interface TaskTypeList {
  id: number;
  name: string;
  creatorId: number;
  creatorName: string;
  createTime: string;
  modifyId: number;
  modifyTime: string;
  remark: string;
  deleted: number;
}
function getTaskTypeList() {
  return request({
    url: '/safeEmergencyTaskType/getPage',
    method: 'post',
    data: ListAllReqBody
  })
}
//人员队伍
function getPersonTeam() {
  return request({
    url: '/safeEmergencyTaskScheme/taskExecutorTeamAndPerson',
    method: 'post'
  })
}
// 查询所有任务方案列表
function getAllTaskPlanList() {
  return request({
    url: '/safeEmergencyTaskScheme/getDtoPage',
    method: 'post',
    data: ListAllReqBody
  })
}
/**
 * 删除应急预案
 * @param idList
 * @returns
 */
function deleteEmergencyPlan(idList: number[]) {
  return request({
    url: '/safeEmergencyPlan/batchDelete',
    method: 'delete',
    params: { idList: idList.toString() }
  })
}
/**
 * 添加应急预案
 * @param data
 * @returns
 */
function addEmergencyPlan(data: any) {
  return request({
    url: '/safeEmergencyPlan/addPlan',
    method: 'post',
    data
  })
}
/**
 * 编辑应急预案
 * @param data
 * @returns
 */
function editEmergencyPlan(data: any) {
  return request({
    url: '/safeEmergencyPlan/updatePlan',
    method: 'put',
    data
  })
}
/**
 * 获取应急预案详情
 * @param id
 * @returns
 */
function getEmergencyPlanDetail(id: string) {
  return request({
    url: `/safeEmergencyPlan/planDetail/${id}`,
    method: 'get',
  })
}

function getPlanByEventTypeLevel(data:any) {
    return request({
      url: '/safeEmergencyPlan/getPlanByEventTypeLevel',
      method: 'post',
      data
    })
  }

function addSimulationExerciseEvent(data:any) {
    return request({
      url: '/safeEmergencyEvent/addSimulationExerciseEvent',
      method: 'post',
      data
    })
  }
/**
 * 获取应急预案详情
 * @param id
 * @returns
 */
function getEventPlanDetail(id: string) {
    return request({
      url: `/safeEmergencyEventPlan/getEventPlanDetail/${id}`,
      method: 'get',
    })
  }

// 任务列表
function selectEventPlanTaskDtoPage(data: any) {
    return request({
      url: '/safeEmergencyEventPlan/selectEventPlanTaskDtoPage',
      method: 'post',
      data
    })
  }

export {
  getTaskTypeList,
  addTaskPlan,
  deleteTaskPlan,
  getTaskPlanDetail,
  editTaskPlan,
  getPersonTeam,
  AddParams,
  EmergencyTaskVoList,
  TaskTypeList,
  getAllTaskPlanList,
  deleteEmergencyPlan,
  addEmergencyPlan,
  editEmergencyPlan,
  getEmergencyPlanDetail,
  getPlanByEventTypeLevel,
  addSimulationExerciseEvent,
  getEventPlanDetail,
  selectEventPlanTaskDtoPage
}
