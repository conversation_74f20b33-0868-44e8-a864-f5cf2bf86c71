import request from "@/utils/request";

const energyConsumptionRecordsUrl = "/energyConsumption/energyConsumptionRecords";
export interface subitemInfo {
    id?: number;
    code?: string;
    name?: string;
    remark?: string;
    createTime?: string;
    parentName?: string;
    parentId?: number;
}
export interface energyConsumptionReportList {
    page?: Page;
    customQueryParams?: CustomQueryParams;
    sorts?: any[];
}

interface CustomQueryParams {
    startTime?: string;
    endTime?: string;
    id?: number;
}

interface Page {
    current?: number;
    size?: number;
}
interface reqQueryParams {
    id: number;
    type: number;
}

export const energyConsumptionService = {
    /**
     * 新建分项
     */
    addSubItem(data: subitemInfo) {
        return request({
            url: "/energyConsumption/subitem",
            method: "post",
            data,
        });
    },
    /**
     * 编辑分项
     */
    getPageSubItem(data: recordsRequest) {
        return request({
            url: "/energyConsumption/subitem/getPage",
            method: "post",
            data,
        });
    },
    /**
     * 编辑分项
     */
    editSubItem(data: subitemInfo) {
        return request({
            url: "/energyConsumption/subitem",
            method: "put",
            data,
        });
    },
    /**
     * 删除分项
     */
    delSubItem(idList: number) {
        return request({
            url: `/energyConsumption/subitem?idList=${idList}`,
            method: "delete",
        });
    },
    /**
     * 新建采集点
     */
    addCollectionPoint(data: subitemInfo) {
        return request({
            url: "/energyConsumption/collectionPoint",
            method: "post",
            data,
        });
    },
    /**
     * 编辑采集点
     */
    editCollectionPoint(data: subitemInfo) {
        return request({
            url: "/energyConsumption/collectionPoint",
            method: "put",
            data,
        });
    },
    /**
     * 删除采集点
     */
    delCollectionPoint(idList: number) {
        return request({
            url: `/energyConsumption/collectionPoint?idList=${idList}`,
            method: "delete",
        });
    },
    /**
     * 树形采集点
     */
    collectionTree(data: any) {
        return request({
            url: "/energyConsumption/collectionPoint/tree",
            method: "post",
            data,
        });
    },
    // 分区
    /**
     * 根据条件，树形结构查询
     */
    energyPartitionTree(data: any) {
        return request({
            url: "/energyConsumption/energyPartition/tree",
            method: "post",
            data,
        });
    },
    /**
     * 根据条件，分页(不分页)查询
     */
    energyPartitionList(data: recordsRequest) {
        return request({
            url: "/energyConsumption/energyPartition/list",
            method: "post",
            data,
        });
    },
    /**
     * 根据条件，删除分区
     */
    delEnergyPartition(idList: number) {
        return request({
            url: `/energyConsumption/energyPartition?idList=${idList}`,
            method: "delete",
        });
    },
    /**
     * 增加能耗分区
     */
    addEnergyPartition(data: subitemInfo) {
        return request({
            url: "/energyConsumption/energyPartition",
            method: "post",
            data,
        });
    },
    /**
     * 编辑能耗分区
     */
    editEnergyPartition(data: subitemInfo) {
        return request({
            url: "/energyConsumption/energyPartition",
            method: "put",
            data,
        });
    },
    /**
     * 围栏范围
     */
    configElectronicFence(data: any) {
        return request({
            url: "/energyConsumption/energyPartition/configElectronicFence",
            method: "post",
            data,
        });
    },
    /**
     * 查询所有能源消耗设备（设备编号、设备型号、设备类型）
     */
    energyConsumptionDeviceAllList() {
        return request({
            url: "/energyConsumption/energyConsumptionDevice/listAll",
            method: "post",
        });
    },
    /**
     * 分页 查询能源消耗设备
     */
    energydetailById(data: any) {
        return request({
            url: `/energyConsumption/map/detailById?type=${data.type}`,
            method: "post",
            data: data.ids,
        });
    },
    /**
     * 分页 查询能源消耗设备
     */
    energyConsumptionDeviceList(data: recordsRequest) {
        return request({
            url: "/energyConsumption/energyConsumptionDevice/list",
            method: "post",
            data,
        });
    },
    /**
     * 增加能源消耗设备
     */
    energyConsumptionDevice(data: any) {
        return request({
            url: "/energyConsumption/energyConsumptionDevice",
            method: "post",
            data,
        });
    },
    /**
     * 修改表计状态
     */
    updateStatus(data: any) {
        return request({
            url: "/energyConsumption/energyConsumptionDevice/updateStatus",
            method: "post",
            data,
        });
    },
    /**
     * 根据id查询能源消耗设备详情
     */
    energyDeviceById(id: string | number) {
        return request({
            url: `/energyConsumption/energyConsumptionDevice/${id}`,
            method: "get",
        });
    },
    /**
     * 修改设备
     */
    editEnergyConsumptionDevice(data: any) {
        return request({
            url: "/energyConsumption/energyConsumptionDevice",
            method: "put",
            data,
        });
    },
    /**
     * 删除设备
     */
    delEnergyConsumptionDevice(data: any) {
        return request({
            url: "/energyConsumption/energyConsumptionDevice",
            method: "delete",
            data,
        });
    },

    // =========能耗记录=========
    /**
     * 新建记录
     */
    addRecord(data: any) {
        return request({
            url: `${energyConsumptionRecordsUrl}`,
            method: "post",
            data,
        });
    },
    /**
     * 批量导入
     */
    importRecords(data: any) {
        return request({
            url: `${energyConsumptionRecordsUrl}/imports`,
            method: "post",
            data,
            headers: {
                "Content-Type": "multipart/form-data",
            },
        });
    },
    /**
     * 能耗报表分页
     */
    getEnergyConsumptionReportList(data: energyConsumptionReportList) {
        return request({
            url: `/energyConsumption/statisticsReport/list`,
            method: "post",
            data,
        });
    },
    /**
     * 能耗报表详情
     */
    getEnergyConsumptionReportDetail(id: string) {
        return request({
            url: `/energyConsumption/statisticsReport/${id}`,
            method: "get",
        });
    },
    /**
     * 统计能耗分区分析TOP
     */
    partitionAnalysisTop() {
        return request({
            url: "/energyConsumption/analysis/partitionAnalysisTop",
            method: "post",
            data: {},
        });
    },
    /**
     * 统计能耗分区分析详情
     */
    detailById(params: reqQueryParams) {
        return request({
            url: "/energyConsumption/analysis/detailById",
            method: "get",
            params,
        });
    },
    /**
     * 根据id单个表计分析详情
     */
    findByDeviceId(id: number) {
        return request({
            url: `/energyConsumption/analysis/findByDeviceId/${id}`,
            method: "get",
        });
    },
    /**
     * 能耗报表导出
     */
    exportEnergyConsumptionReport(id: string) {
        return request({
            url: `/energyConsumption/statisticsReport/export/${id}`,
            method: "post",
            responseType: "blob",
        });
    },
    /**
     * 默认能耗报表模板导出/energyConsumption/statisticsReport/exportTemplateByType/{type}
     */
    exportStatisticsReport(type: string) {
        return request({
            url: `/energyConsumption/statisticsReport/exportTemplateByType/${type}`,
            method: "post",
            responseType: "blob",
        });
    },
    //能源概览echarts
    getechatsdata(dateType: any, type: any) {
        return request({
            url: `/energyConsumption/overview/overview?dateType=${dateType}&type=${type}`,
            method: "get",
        });
    },
    // =========统计分析=========

    //水气电标准煤总统计
    statisticalCount() {
        return request({
            url: "/energyConsumption/statistical/count",
            method: "get",
            params: {},
        });
    },
    // 查询采集点or设备树
    statisticalPointTree(type: number) {
        return request({
            url: `/energyConsumption/statistical/pointTree/${type}`,
            method: "get",
            params: {},
        });
    },
    // 查询区域or设备树
    statisticalPartitionTree(type: number) {
        return request({
            url: `/energyConsumption/statistical/partitionTree/${type}`,
            method: "get",
            params: {},
        });
    },
    // 统计分析 - 查询折线数据
    getKxData(data: any) {
        return request({
            url: "/energyConsumption/statistical/kxData",
            method: "post",
            data,
        });
    },
    // 统计分析 - 查询列表数据
    getTableData(data: any) {
        return request({
            url: "/energyConsumption/statistical/tableData",
            method: "post",
            data,
        });
    },
    // 统计分析 - 列表数据导出
    exportTableData(data: any) {
        return request({
            url: "/energyConsumption/statistical/tableData/export",
            method: "post",
            data,
        });
    },
    // 获取能源流向分析
    getEnergyFlowAnalysis(data:{
        type: number;
        startTime: string;
        endTime: string;
      }){
        return request({
            url:'/energyConsumption/statistical/energyFlowAnalysis',
            method: "post",
            data,
        })
    }
};
