import request from '@/utils/request';
// 新增档案
export function archivesAdd(data) {
    return request({
        url: '/draingeHouseholdArchives',
        method: 'post',
        data
    })
}
// 编辑档案
export function archivesEdit(data) {
    return request({
        url: '/draingeHouseholdArchives',
        method: 'put',
        data
    })
}

// 获取信息
export function archivesGet(id) {
    return request({
        url: `/draingeHouseholdArchives/${id}`,
        method: 'get'
    })
}

// 删除档案
export function archivesDelete(data) {
    return request({
        url: `/draingeHouseholdArchives?idList=${data}`,
        method: 'delete',
    })
}
// 获取管道干线
export function getPipeLines(data) {
    return request({
        url: `/pipelineArchives/getPage`,
        method: 'post',
        data
    })
}
// 获取管道干线详情
export function getPipeLineDetail(id) {
    return request({
        url: `/pipelineArchives/${id}`,
        method: 'get',
    })
}
// 获取管道干线
export function pipelineStatic(data) {
    return request({
        url: '/pipelineArchivesMap/pipelineStatic',
        method: 'post',
        data
    })
}
// 获取当前区域监控设备
export function getMonitorDevicePage(data) {
    return request({
        url: '/pipelineArchivesMap/getMonitorDevicePage',
        method: 'post',
        data
    })
}
// 获取管道干线
export function getSelectedDevice(data) {
    return request({
        url: '/pipelineMonitorDevice/getSelectedDevice',
        method: 'post',
        data
    })
}

// 获取许可证内容
export function getLicenseContent(data, id) {
    return request({
        url: `/draingeHouseholdArchives/getLicenseContent/${id}`,
        method: 'post',
        data
    })
}

// 获取许可证内容
export function getPollutant(data, id) {
    return request({
        url: `/draingeHouseholdArchives/getPollutant/${id}`,
        method: 'post',
        data
    })
}

// // 编辑桥梁
// export function bridgeEdit(data) {
//   return request({
//     url: '/trafficBridge',
//     method: 'put',
//     data
//   })
// }
// // 桥梁新增
// export function bridgeNewAdd(data) {
//   return request({
//     url: '/trafficBridge',
//     method: 'post',
//     data
//   })
// }
// // 桥梁删除
// export function bridgeDelete(data) {
//   return request({ url: `/trafficBridge?idList=${data}`, method: 'delete' })
// }
// // 获取区域树
// export function getAreaLocationTree(type, table) {
//   return request({ url: `/deviceAreaTree/getTree?type=${type}&table=${table}`, method: 'get' })
// }
// // 获取对应区域设施
// export function roadBridgeMapList(data) {
//     return request({ url: '/roadBridgeMap/list', method: 'post', data })
// }
// // 获取对应设施详情
// export function roadBridgeMapDetailInfo(data) {
//     return request({ url: '/roadBridgeMap/detailInfo', method: 'post', data })
// }
// // 获取区域设施概览
// export function roadBridgeMapCreaCount(data) {
//     return request({ url: '/roadBridgeMap/areaCount', method: 'post', data })
// }
// // 道桥等级
// export function countGroupByGrade(data) {
//     return request({ url: '/roadBridgeMap/countGroupByGrade', method: 'post', data })
// }
// // 道桥护养
// export function maintenanceTrendCount(data) {
//     return request({ url: '/roadBridgeMap/maintenanceTrendCount', method: 'post', data })
// }
// // 道路部件码获取
// export function roadGetValidObjInfo(data) {
//   return request({
//     url:'/trafficRoad/getValidObjInfo',
//     method:'post',
//     data
//   })
// }
// // 桥梁部件码获取
// export function bridgeGetValidObjInfo(data) {
//   return request({
//     url: '/trafficBridge/getValidObjInfo',
//     method: 'post',
//     data
//   })
// }
