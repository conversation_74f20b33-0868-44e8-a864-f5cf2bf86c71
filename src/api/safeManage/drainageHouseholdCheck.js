import request from '@/utils/request';
// 新增检查
export function checkAdd(data) {
    return request({
        url: '/draingeHouseholdCheck',
        method: 'post',
        data
    })
}
// 编辑检查
export function checkEdit(data) {
    return request({
        url: '/draingeHouseholdCheck',
        method: 'put',
        data
    })
}

// 获取信息
export function checkGet(id) {
    return request({
        url: `/draingeHouseholdCheck/${id}`,
        method: 'get'
    })
}

// 删除检查
export function checkDelete(data) {
    return request({
        url: `/draingeHouseholdCheck?idList=${data}`,
        method: 'delete',
    })
}

// 获取排水户
export function getArchives(data) {
    return request({
        url: `/draingeHouseholdArchives/getPage`,
        method: 'post',
        data
    })
}

// 获取排水概览
export function getInstallOver(data) {
    return request({
        url: '/draingeHouseholdArchives/getInstallOver',
        method: 'post',
        data
    })
}
