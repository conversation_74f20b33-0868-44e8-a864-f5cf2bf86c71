import request from '@/utils/request';
export interface QueryPlanData {
  page: Page;
  customQueryParams: CustomQueryParams;
}

interface CustomQueryParams {
  planTitle: string;
  isTodayAndEnable: boolean;
}

interface Page {
  current: number;
  size: number;
}
// 巡逻计划列表
export function queryPlanList(data: QueryPlanData) {
  return request({
    url: '/prowlTask/list',
    method: 'post',
    data
  })
}
// 巡逻详情
export function queryPlanDetail(id: number) {
  return request({
    url: `/prowlTask/result/${id}`,
    method: 'get',
  })
}
