import request from '@/utils/request';
import { Page } from 'view-ui-plus';
/**
 * 应急资源管理
 */
// 应急人员
// 应急人员列表
export function getPersonList(data) {
    return request({
        url: '/safeEmergencyPerson/list',
        method: 'post',
        data
    })
}
// 新增
export function addPerson(data) {
    return request({
        url: '/safeEmergencyPerson',
        method: 'post',
        data
    })
}
// 详情
export function getPersonDetail(id) {
    return request({
        url: `/safeEmergencyPerson/${id}`,
        method: 'get'
    })
}
// 编辑
export function editPerson(data) {
    return request({
        url: '/safeEmergencyPerson',
        method: 'put',
        data
    })
}
// 删除
export function deletePersonDetail(idList) {
    return request({
        url: `/safeEmergencyPerson`,
        method: 'delete',
        params: {
            idList: idList.toString()
        }
    })
}
// 人员下拉查询
export function queryPerson(data) {
    return request({
        url: '/safeEmergencyPerson/getNotRelatedWorkerPage',
        method: 'post',
        data
    })
}
// 查询岗位
export function queryListJob() {
    return request({
        url: '/safeEmergencyPerson/listJobTitle',
        method: 'post',
        data: {}
    })
}
// 应急队伍
// 新增
export function addTeam(data) {
    return request({
        url: '/safeEmergencyTeam',
        method: 'post',
        data
    })
}
// 详情
export function getTeamDetail(data) {
    return request({
        url: `/safeEmergencyTeam/${data.id}`,
        method: 'get',
        params: {
            needPersonsDetail: data.needPersonsDetail
        }
    })
}
// 编辑
export function editTeam(data) {
    return request({
        url: '/safeEmergencyTeam',
        method: 'put',
        data
    })
}
// 删除
export function deleteTeam(idList) {
    return request({
        url: `/safeEmergencyTeam`,
        method: 'delete',
        params: {
            idList: idList.toString()
        }
    })
}

// 查询人员分页
export function queryPersonPage(data) {
    return request({
        url: `/safeEmergencyTeam/getPersonPage`,
        method: 'post',
        data
    })
}

/**
 * 应急专家
 */
// 应急专家列表
export function getExpertList(data) {
    return request({
        url: '/safeEmergencyExpert/getPage',
        method: 'post',
        data
    })
}
// 新增
export function addExpert(data) {
    return request({
        url: '/safeEmergencyExpert',
        method: 'post',
        data
    })
}
// 详情
export function getExpertDetail(id) {
    return request({
        url: `/safeEmergencyExpert/${id}`,
        method: 'get',
    })
}
// 编辑
export function editExpert(data) {
    return request({
        url: '/safeEmergencyExpert',
        method: 'put',
        data
    })
}
// 删除
export function deleteExpert(idList) {
    return request({
        url: '/safeEmergencyExpert',
        method: 'delete',
        params: {
            idList: idList.toString()
        }
    })
}
/**
 * 应急专家组
 */
// 新增
export function addExpertGroup(data) {
    return request({
        url: '/safeEmergencyExpertGroup',
        method: 'post',
        data
    })
}
// 详情
export function getExpertGroupDetail(id) {
    return request({
        url: `/safeEmergencyExpertGroup/${id}`,
        method: 'get',
    })
}
// 编辑
export function editExpertGroup(data) {
    return request({
        url: '/safeEmergencyExpertGroup',
        method: 'put',
        data
    })
}
// 删除
export function deleteExpertGroup(idList) {
    return request({
        url: '/safeEmergencyExpertGroup',
        method: 'delete',
        params: {
            idList: idList.toString()
        }
    })
}
// 获取绑定所有专家
export function getExpertBindList(data) {
    return request({
        url: '/safeEmergencyExpertGroup/getMemberPage',
        method: 'post',
        data
    })
}
/**
 * 应急物资
 */
// 新增
export function addSupply(data) {
    return request({
        url: '/safeEmergencySupply',
        method: 'post',
        data
    })
}
// 详情
export function getSupplyDetail(id) {
    return request({
        url: `/safeEmergencySupply/${id}`,
        method: 'get',
    })
}
// 编辑
export function editSupply(data) {
    return request({
        url: '/safeEmergencySupply',
        method: 'put',
        data
    })
}
// 删除
export function deleteSupply(idList) {
    return request({
        url: '/safeEmergencySupply',
        method: 'delete',
        params: {
            idList: idList.toString()
        }
    })
}
// 出入库
export function insertInOutRecord(data) {
    return request({
        url: '/safeEmergencySupply/insertInOutRecord',
        method: 'post',
        data
    })
}
// 删除出入库记录
export function deleteOutInLibrary(idList) {
    return request({
        url: '/safeEmergencySupply/deleteInOutRecord',
        method: 'delete',
        params: {
            idList: idList.toString()
        }
    })
}
/**
 * 应急物资储备库
 */
// 应急物资储备库列表
export function getSupplyLibraryList(data) {
    return request({
        url: '/safeEmergencySupplyLibrary/getPage',
        method: 'post',
        data
    })
}
// 新增
export function addSupplyLibrary(data) {
    return request({
        url: '/safeEmergencySupplyLibrary',
        method: 'post',
        data
    })
}
// 详情
export function getSupplyLibraryDetail(id) {
    return request({
        url: `/safeEmergencySupplyLibrary/${id}`,
        method: 'get',
    })
}
// 编辑
export function editSupplyLibrary(data) {
    return request({
        url: '/safeEmergencySupplyLibrary',
        method: 'put',
        data
    })
}
// 删除
export function deleteSupplyLibrary(idList) {
    return request({
        url: '/safeEmergencySupplyLibrary',
        method: 'delete',
        params: {
            idList: idList.toString()
        }
    })
}
// 应急法规
export function addRegula(data) {
    return request({
        url: '/emergencyRegula/addRegula',
        method: 'post',
        data
    })
}
// 修改
export function editRegula(data) {
    return request({
        url: '/emergencyRegula',
        method: 'put',
        data
    })
}
// 删除
export function delRegula(idList) {
    return request({
        url: '/emergencyRegula',
        method: 'DELETE',
        params: {
            idList: idList.toString()
        }
    })
}
// 删除
export function getRegula(id) {
    return request({
        url: `/emergencyRegula/${id}`,
        method: 'get',
        params: {}
    })
}

// 应急资源概况统计
export function topCount(data) {
    return request({
        url: '/emergencyResourceOverview/topCount',
        method: 'post',
        data
    })
}

// 应急资源曲线
export function trendData(data) {
    return request({
        url: '/emergencyResourceOverview/trendData',
        method: 'post',
        data
    })
}

// 应急资源明细
export function trendDetailData(data) {
    return request({
        url: '/emergencyResourceOverview/trendDetailData',
        method: 'post',
        data
    })
}

// 应急资源明细导出
export function trendDetailDataExport(data) {
    return request({
        url: '/emergencyResourceOverview/trendDetailDataExport',
        method: 'post',
        data
    })
}

// 应急信息发布详情
export function informationDetail(id) {
    return request({
        url: `/emergencyInformationPublish/informationDetail/${id}`,
        method: 'get'
    })
}

// 应急信息发布 - 新增
export function addInformation(data) {
    return request({
        url: '/emergencyInformationPublish/addInformation',
        method: 'post',
        data
    })
}

// 应急信息发布 - 修改
export function updateInformation(data) {
    return request({
        url: '/emergencyInformationPublish/updateInformation',
        method: 'put',
        data
    })
}

// 应急信息发布 - 删除
export function delInformation(data) {
    return request({
        url: '/emergencyInformationPublish?idList=' + data,
        method: 'DELETE',
    })
}

// 新增应急小组
export function addEmergencyGroup(data) {
    return request({
        url: '/collaborativeProcess/emergencyGroup',
        method: 'post',
        data
    })
}

// 编辑应急小组
export function updateEmergencyGroup(data) {
    return request({
        url: '/collaborativeProcess/emergencyGroup',
        method: 'put',
        data
    })
}

// 删除应急小组
export function delEmergencyGroup(data) {
    return request({
        url: '/collaborativeProcess/emergencyGroup',
        method: 'DELETE',
        data
    })
}
// 根据id查询应急小组详情
export function getEmergencyGroup(id) {
    return request({
        url: `/collaborativeProcess/emergencyGroup/${id}`,
        method: 'get'
    })
}

// 根据条件，分页(不分页)查询应急小组所属人员列表
export function listGroupRefPerson(data) {
    return request({
        url: '/collaborativeProcess/emergencyGroup/listGroupRefPerson',
        method: 'post',
        data
    })
}

// 发起通话-新增通话记录
export function addCallLog(data) {
    return request({
        url: '/collaborativeProcess/emergencyGroup/addCallLog',
        method: 'post',
        data
    })
}

// 结束通话
export function updateCallLog(data) {
    return request({
        url: '/collaborativeProcess/emergencyGroup/overCallLog',
        method: 'post',
        data
    })
}

// 加入通话
export function addCallLogPerson(data) {
    return request({
        url: '/collaborativeProcess/emergencyGroup/addOrOutCallLog',
        method: 'post',
        data
    })
}


export const emergencyScreen = {
  // 设备列表
  waterQualityDevice: (data) => {
    return request({
        url: '/waterQualityDevice/list',
        method: 'post',
        data,
        baseURL: '/api/livable'
    })
  },
  // 当前水质状态
  queryCurrentWaterQuality: (data) => {
    return request({
        url: '/waterQualityEnvAnalysis/queryCurrentWaterQuality',
        method: 'post',
        data,
        baseURL: '/api/livable'
    })
  },
  // 当前水质状态/查询核心指标
  queryCoreMetrics: (data) => {
    return request({
        url: '/waterQualityEnvAnalysis/queryCoreMetrics',
        method: 'post',
        data,
        baseURL: '/api/livable'
    })
  },
  // 当前管网状态
  getPipelineStatus: (data) => {
    return request({
        url: '/pipelineStatistical/getCurrentStatus',
        method: 'post',
        data,
    })
  },
  // 当前泵站状态
  pumpStationPage: (data) => {
    return request({
        url: '/draingePumpStationArchives/getPage',
        method: 'post',
        data,
    })
  },
  // 泵站详情
  draingePumpStationArchives: (data) => {
    return request({
        url: `/draingePumpStationArchives/${data}`,
        method: 'get',
    })
  },
  /**
   * 水质设备列表
   */
  waterQualityDeviceList(data) {
      return request({
          url: '/waterQualityDevice/list',
          method: 'post',
          data,
          baseURL: '/api/livable'
      });
  },
  /**
   * 查询折线图数据，假数据
   */
  queryHistoricDataPageFromEs(data) {
      return request({
          url: '/monitor/queryHistoricDataPageFromEs',
          method: 'post',
          data,
          baseURL: '/api/livable'
      });
  },
  /**
   * 水质等级
   */
  queryWaterQualityGradeLineChart(data) {
      return request({
          url: '/waterQualityEnvAnalysis/queryWaterQualityGradeLineChart',
          method: 'post',
          data,
          baseURL: '/api/livable'
      });
  },
}
