import { ManholeCoverInfo, devicePropertyStatus, ComponentInfo } from "@/api/manholeCoverService";
export interface facilitiesOverview {
    annualOutputValueSum: number;
    archivesTotalNum: number;
    areaSum: number;
    drainageAllowanceSum: number;
    drainageHouseholdLevelCountList: chartObject[];
    drainageHouseholdTypeCountList: chartObject[];
}
export interface chartObject {
    type: number;
    sum: number;
}
export interface facilitiesDetaillInfo extends ManholeCoverInfo {
    areaPath?: string;
    contactPerson?: string;
    contactPhone?: string;
    pipeLineName?: string;
    catchmentArea?: string;
    licenceNo?: string;
    drainageHouseholdArchivesLevel?: number;
    drainageHouseholdArchivesType?: number;
    drainageAllowance?: number;
    licenseIssuanceTime?: string;
    licenseStartTime?: string;
    lightingType?: number;
    licenseEndTime?: string;
    // 新增字段
    affiliatedSewagePlant?: string;
    enterpriseType?: string;
    // 列表
    licenseInfos?: [];
}
// 扩展信息
export interface extendInfoType {
    areaPath: string;
    bsm: string;
    deviceFirstTypeName: string;
    deviceId: string;
    deviceSecondTypeName: string;
    objId: string;
    sbmc: string;
    sbzt: string | number;
    sbxh: string;
    szdywg: string;
    szjd: string;
    szsq: string;
    collectionPointId?: number | string;
    partitionId?: number | string;
    subitemId?: number | string;
    collectionPointName?: number | string;
    partitionName?: number | string;
    subitemName?: number | string;
    type?: number | string;
}
// 能源设备详细信息
export interface energyEquipmentInfo {
    extendInfo: extendInfoType;
    devicePropertyStatusList?: devicePropertyStatus[];
    objInfo?: ComponentInfo;
    lastPushTime?: string;
    status?: number;
    id?: number;
    type?: number;
    collectionPointId?: number | string;
    partitionId?: number | string;
    subitemId?: number | string;
    collectionPointName?: number | string;
    partitionName?: number | string;
    subitemName?: number | string;
}
// 设备基础信息
export interface deviceBaseInfo {
    deviceId: string;
    id?: number;
    useStatus?: number;
}
// 公厕设施
export interface toilletComponentInfo {
    objInfo?: ComponentInfo;
    objId?: string;
    toiletName?: string;
    toiletCode?: string;
    alarmNum?: number;
    startDate?: string;
    initDate?: string;
    firstObjCategoryName?: string;
    ownerEnterpriseName?: string;
}
// 摄像机 - 待补充
export interface cameraInfo {
    id: number;
    cameraName?: string; // 相机名称
    deviceCode?: string; // 相机编号
    cameraGroupList: cameraGroupInfo[] | number[]; // 相机所在分组
    deviceExtendInfo?: extendInfoType; // 相机扩展信息
    objInfo?: ComponentInfo; // 相机部件信息
}
// 摄像机组
export interface cameraGroupInfo {
    id?: number | string;
    groupName?: string;
    remark?: string;
    deployCameraList?: number[];
    cameraList?: cameraInfo[];
}

// 无人机详情
export interface droneInfo {
    droneBrand?: string;
    droneCategory?: string;
    droneColor?: string;
    droneFactory?: string;
    droneNo?: string;
    droneStatus?: number;
    droneType?: string;
    id?: number;
    device?: environmentDevices;
    status?: number;
    alarmCount?: number;
    choose?: boolean;
    droneName?: string
    maintenancePerson?: string
}
// 设备信息
export interface environmentDevices {
    code?: string;
    name?: string;
    latitude?: number;
    longitude?: number;
    alarmState?: number;
    useStatus?: number;
    modelId?: string;
    lastPushTime?: string;
    status?: number | string;
}
// 巡检计划详情
export interface inspectionPlanInfo {
    id?:number;
    planCode?: string;
    planName?: string;
    deviceCode?: string;
    actionId?: number|string;
    creatTime?: string;
    maintenancePerson?: string;
    planStatus?: number;
    timeType?: number | string;
    timeValues?: string[] | string;
    startTime?: string;
    pointList?: electronicFenceRegion[];
    note?:string
    patrolPerson?: string;
    patrolTime?: string;
    operationPerson?: string;
    areaPath?:string
}
// 坐标
export interface coordinate {
    positionX: number;
    positionY: number;
}
// 电子围栏范围
export interface electronicFenceRegion {
    id?: number;
    coordinateList: coordinate[];
}
