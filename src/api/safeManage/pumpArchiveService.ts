import request from '@/utils/request';
// 公共部件对象
export interface ComponentInfo {
    type?: number;
    checked?: boolean;
    licenseNo?: string;
    id?: number;
    deviceId?: string;
    name?: string;
    sbmc?: string; // 设备名称
    bsm?: string; // 部件code
    status?: number; // 状态
    useStatus?: number; // 部件使用状态
    firstObjCategoryName?: string; // 部件类型名称
    objId?: string; // 部件ID
    sbxh?: string; // 设备型号
    contactPhone?: string; // 联系电话
    contactPerson?: string; // 联系人
    alarmState?: number; // 告警状态
    deviceSecondTypeName?: string; // 设备类型
    objState?: number; // 部件状态
    secondObjCategoryName?: string; // 部件类型
    initDate?: string; // 安装时间
    modifyDate?: string; // 修改时间
    remark?: string; // 备注
    sbzt?: string; // 设备状态
    deviceCode?: string; // 设备编号
    switchState?: string; // 开关状态
    light?: string; // 灯光状态
    objName?: string;
    szdywg?: string;
    szjd?: string;
    szsq?: string;
    ownerEnterpriseName?: string;
    opEnterpricseName?: string;
    deptName?: string;
    areaPath?: string;
    objX?: number;
    objY?: number;
}
export interface pumpArchivesPropertyStatus {
    name: string;
    value: string;
    propName?: string;
    prop?: string;
    specs?: string;
    unit?: string;
}

export interface searchObjType {
    queryTimeStart: string;
    queryTimeEnd: string;
    time?: any[];
}
export interface pumpArchivesDeviceInfo {
    objId?: string;
    pumpInfos?: any[];
    input?: string;
    objInfo?: ComponentInfo;
    extendInfo?: ComponentInfo;
    pumpPropertyStatusList?: pumpArchivesPropertyStatus[];
    status?: number; // 在线状态
    useStatus?: number; // 使用状态
    deviceCode?: string; // 设备编码
    id?: number;
    typeName?: string; // 设备类型
    name?: string; // 设备名称
    notPushAlarmNum?: number; // 未推送告警数量
    pumpStationName?: string;// 泵站名称
    pumpStationType?: string;// 泵站分类
    pumpStationTypeName?:string, // 泵站分类名称
    operationDate?: string;// 运营时间
    serviceArea?: number;// 服务面积
    serviceScope?: string;// 服务范围
    warningLine?: number;// 预警线
    intoWaterBody?: string;// 排水水体
    designInletFlowRate?: number | null;// 进水流量
    designOutletFlowRate?: number| null;// 出水流量
    waterPumpId?: string;// 水泵编号
    waterPumpModel?: string;// 水泵型号
    waterPumpType?: string;// 水泵类型
    nameplateInRate?: string;// 铭牌流量
    nameplateMeter?: string;// 铭牌杨程/
    rate?: string;// 转速
    spareBoolean?: boolean;// 是否备用
}
export const pumpArchiveService = {
    getPage(data: recordsRequest) {
        return request({
            url: '/draingePumpStationArchives/getPage',
            method: 'post',
            data,
        });
    },
    // 地图设备概览
    deviceStatus(data: any) {
        return request({
            url: '/draingePumpStationMaps/deviceStatus',
            method: 'post',
            data
        });
    },
    // 根据id查询设备信息
    getPumpArchivesById(id: number) {
        return request({
            url: `/draingePumpStationArchives/${id}`,
            method: 'get',
        });
    },
    // 根据id查询设备告警信息
    queryAlarmPage(data: recordsRequest) {
        return request({
            url: '/drainageDevice/queryAlarmPage',
            method: 'post',
            data,
        });
    },
    pumpArchivesAction(data: Object, method: string) {
        return request({
            url: `/draingePumpStationArchives`,
            method: method,
            data,
            params: method == 'delete' ? data : {},
        });
    },
    getRelatedOrNotDevice(data: Object) {
        return request({
            url: `/drainageDevice/getRelatedOrNotDevice`,
            method: 'post',
            data
        });
    },
    relateDevice(data: Object) {
        return request({
            url: `/drainageDevice/relateDevice`,
            method: 'post',
            data
        });
    }
};
