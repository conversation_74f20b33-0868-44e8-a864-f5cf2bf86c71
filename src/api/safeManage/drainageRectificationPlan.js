import request from '@/utils/request';
// 新增检查
export function checkAdd(data) {
    return request({
        url: '/drainageRectificationPlan',
        method: 'post',
        data
    })
}
// 编辑检查
export function submit(data) {
    return request({
        url: '/drainageRectificationPlan/submit',
        method: 'put',
        data
    })
}

// 复核检查
export function review(data) {
    return request({
        url: '/drainageRectificationPlan/review',
        method: 'put',
        data
    })
}

// 复核检查
export function update(data) {
    return request({
        url: '/drainageRectificationPlan/update',
        method: 'put',
        data
    })
}

// 实施
export function implement(data) {
    return request({
        url: '/drainageRectificationPlan/implement',
        method: 'put',
        data
    })
}

export function changePlanStatus(id) {
    return request({
        url: '/drainageRectificationPlan/changePlanStatus/'+id,
        method: 'put'
    })
}

// 获取信息
export function checkGet(id) {
    return request({
        url: `/drainageRectificationPlan/${id}`,
        method: 'get'
    })
}

// 删除检查
export function checkDelete(data) {
    return request({
        url: `/drainageRectificationPlan?idList=${data}`,
        method: 'delete',
    })
}

// 获取排水户
export function getArchives(data) {
    return request({
        url: `/draingeHouseholdArchives/getPage`,
        method: 'post',
        data
    })
}

// 获取排水概览
export function getInstallOver(data) {
    return request({
        url: '/drainageRectificationPlan/getInstallOver',
        method: 'post',
        data
    })
}
