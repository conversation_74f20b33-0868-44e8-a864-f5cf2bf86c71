import request from '@/utils/request';
// 地图降雨等级颜色 - 水位判断
export const mapRainfallLevelColor = [
    { level: 1, name: '小雨', min: 0, max: 0.42, color: '#0F7DFE' },
    { level: 2, name: '中雨', min: 0.42, max: 1.04, color: '#696BE8' },
    { level: 3, name: '大雨', min: 1.04, max: 2.08, color: '#A95DD7' },
    { level: 4, name: '暴雨', min: 2.08, max: 4.16, color: '#CF7D92' },
    { level: 5, name: '大暴雨', min: 4.16, max: 10.42, color: '#FEA43A' },
    { level: 6, name: '特大暴雨', min: 10.42, max: Number.MAX_VALUE, color: '#FFEC87' }
]
// 地图降雨等级颜色 - 降雨量判断
export const mapRainfallLevelColorByTotal = [
    { level: 1, name: '小雨', min: 0, max: 10, color: '#0F7DFE' },
    { level: 2, name: '中雨', min: 10, max: 25, color: '#696BE8' },
    { level: 3, name: '大雨', min: 25, max: 50, color: '#A95DD7' },
    { level: 4, name: '暴雨', min: 50, max: 100, color: '#CF7D92' },
    { level: 5, name: '大暴雨', min: 100, max: 250, color: '#FEA43A' },
    { level: 6, name: '特大暴雨', min: 250, max: Number.MAX_VALUE, color: '#FFEC87' }
]
export const lakeChannelMonitoringService = {
    // 根据区域查询湖渠设备实时统计数据
    getDeviceStatus(data) {
        return request({
            url: '/drainageLakeMonitoring/deviceStatus',
            method: 'post',
            data
        })
    },
    // 告警
    queryAlarmPage(data) {
        return request({
            url: '/drainageDevice/queryAlarmPage',
            method: 'post',
            data
        })
    }
}
export const waterRainMonitoringService = {
    // 根据区域查询湖渠设备实时统计数据
    getDeviceStatus(data) {
        return request({
            url: '/drainageRainMonitoring/deviceStatus',
            method: 'post',
            data
        })
    },
    // 告警
    queryAlarmPage(data) {
        return request({
            url: '/drainageDevice/queryAlarmPage',
            method: 'post',
            data
        })
    }
}
