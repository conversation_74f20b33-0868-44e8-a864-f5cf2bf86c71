import request from '@/utils/request';
// 新增档案
export const controlCameraService = {
    deployCameraGroup(params) {
        return request({
            url: '/deployCameraGroup',
            method: 'delete',
            params
        })
    },
    getCameraList(data) {
        return request({
            url: '/deployCamera/selectDtoPage',
            method: 'post',
            data
        })
    },
    addGroup(data) {
        return request({
            url: '/deployCameraGroup/addGroup',
            method: 'post',
            data
        })
    },
    updateDtoOne(data) {
        return request({
            url: '/deployCameraGroup/updateDtoOne',
            method: 'put',
            data
        })
    },
    selectDtoOne(data) {
        return request({
            url: '/deployCameraGroup/selectDtoOne',
            method: 'post',
            data
        })
    }
}

