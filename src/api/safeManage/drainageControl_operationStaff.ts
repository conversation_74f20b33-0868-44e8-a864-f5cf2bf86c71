import request from '@/utils/request';

const apiUrl = '/drainageEmergencyPersonDeviceRef'

export const operationStaffService = {
  add(data:any) {
      return request({
          url: `${apiUrl}/saveRelation`,
          method: 'post',
          data
      })
  },
  // 删 List<Long>
  delete(params:any) {
      return request({
          url: `${apiUrl}/deleteRelation`,
          method: 'delete',
          params
      })
  },

  update(data:any) {
      return request({
          url: `${apiUrl}/updateRelation`,
          method: 'put',
          data
      })
  },

  getById(id:any) {
      return request({
          url: `${apiUrl}/${id}`,
          method: 'get',
      })
  },
};