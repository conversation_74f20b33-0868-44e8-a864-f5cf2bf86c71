import request from '@/utils/request';
/**
 * 应急资源管理
 */
// 获取大数据人员列表
export function getPersonList(data) {
    return request({
        url: '/worker/getPage',
        method: 'post',
        data
    })
}


export function getNotRefPersonList(departId) {
    return request({
        url: '/safeDepartment/getNotRefWorkerList' + (departId ? ('/' + departId) : ''),
        method: 'get'
    })
}

export function addDepartment(data) {
    return request({
        url: '/safeDepartment',
        method: 'post',
        data
    })
}

export function updateDepartment(data) {
    return request({
        url: '/safeDepartment/update',
        method: 'put',
        data
    })
}

export function getDetail(id) {
    return request({
        url: '/safeDepartment/' + id,
        method: 'get'
    })
}

// 删除
export function delDepartment(idList) {
    return request({
        url: '/safeDepartment',
        method: 'DELETE',
        params: {
            idList: idList.toString()
        }
    })
}
// 获取所有部门资源
export const getAllDepartmentResource = (data = {}) => {
    return request({
        url: '/safeDepartmentResourcesRef/list',
        method: 'post',
        data: {
            page: {
                size: -1,
                current: 1
            },
            customQueryParams: data
        }
    })
}
// 获取所有资源
export const getAllSource = (data = {}) => {
    return request({
        url: '/safeEmergencySupply/getPage',
        method: 'post',
        data: {
            page: {
                size: -1,
                current: 1
            },
            customQueryParams: data
        }
    })
}
// 获取所有部门
export const getDepartmentListByData = (data = {}) => {
    return request({
        url: '/safeDepartment/getPage',
        method: 'post',
        data: {
            page: {
                size: -1,
                current: 1
            },
            customQueryParams: data
        }
    })
}