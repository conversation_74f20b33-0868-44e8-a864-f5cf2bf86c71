import request from '@/utils/request';

// // 设备汇总数据
// export interface creaCountInfo {
//     roadTotalNum?: number;
//     roadTotalLength?: number;
//     totalMain?: number;
//     totalDetect?: number;
// }
// // 设备概览
// export interface creaCountResInfo {
//     countKey: string;
//     countName: string;
//     countValue: number;
// }
// 公共部件对象
export interface ComponentInfo {
    type?: number;
    checked?: boolean;
    licenseNo?: string;
    id?: number;
    deviceId?: string;
    name?: string;
    sbmc?: string; // 设备名称
    bsm?: string; // 部件code
    status?: number; // 状态
    useStatus?: number; // 部件使用状态
    objId?: string; // 部件ID
    sbxh?: string; // 设备型号
    contactPhone?: string; // 联系电话
    contactPerson?: string; // 联系人
    alarmState?: number; // 告警状态
    deviceSecondTypeName?: string; // 设备类型
    objState?: number; // 部件状态
    secondObjCategoryName?: string; // 部件类型
    initDate?: string; // 安装时间
    modifyDate?: string; // 修改时间
    remark?: string; // 备注
    sbzt?: string; // 设备状态
    deviceCode?: string; // 设备编号
    switchState?: string; // 开关状态
    light?: string; // 灯光状态
    objName?: string;
    szdywg?: string;
    szjd?: string;
    szsq?: string;
    ownerEnterpriseName?: string;
    opEnterpricseName?: string;
    deptName?: string;
    areaPath?: string;
    objX?: number;
    objY?: number;
}
// // 设施信息
// export interface facilityInfo {
//     id: number;
//     maintenanceUnit?: string;
//     length?: number;
//     grade?: number;
//     designYear?: number;
//     totalArea?: number;
//     roadGrade?: string;
// }
// // 部件对象数组
// export interface installationInfo {
//     objInfo: ComponentInfo;
//     facilityInfo: facilityInfo;
//     lastDetectTime?: string;
//     lastMaintenanceTime?: string;
//     maintenanceNum?: number;
//     detectNum?: number;
//     id?: number;
// }

// /**
//  * 井盖数据
//  */
// // 井盖在线
// export interface ManholeCoverOnline {
//     sum: number;
//     online: number;
//     recordTime?: string;
// }
export interface MonitorDeviceCovertInfo {
    input?: string;
    objInfo?: ComponentInfo;
    extendInfo?: ComponentInfo;
    devicePropertyStatusList?: devicePropertyStatus[];
    status?: number;
    useStatus?: number;
    deviceCode?: string; // 设备编码
    id?: number;
    typeName?: string; // 设备类型
    name?: string; // 设备名称
}
// 井盖设备状态
export interface devicePropertyStatus {
    name: string;
    value: string;
    propName?: string;
    prop?: string;
    modifyTime?:string;
}
// // 警告告警对象
export interface MonitorDeviceCoverAlarm {
    alarmTime?: string;
    status?: number;
    sbmc?: string;
    content?: string;
    code?: string; // 告警编号
    level?: number; // 告警级别
    pushStatus?: number; // 推送状态
    areaPath?: string; // 区域路径
    alarmType?: number | string; // 告警类型
}
// // 井盖告警数据
export interface MonitorDeviceCoverAlarmList {
    alarm: number;
    alarmTotal: number;
    alarmList: MonitorDeviceCoverAlarm[];
}
// // 区域树
// export interface areaTree {
//     value?: string;
//     label?: string;
//     code?: string;
//     title?: string;
//     key?: string;
//     checked?: boolean;
//     expand?: boolean;
//     selected?: boolean;
//     name?:string;
//     id?: number;
//     fullId?: string;
//     children?: areaTree[];
// }
// export interface treeData {
//     id: string;
//     value: string;
//     title: string;
//     selected?: boolean;
//     checked?: boolean;
//     expand?: boolean;
//     children?: treeData[];
// }
// export interface emitType {
//     (xx: string, ...arg: any): void;
// }
export const monitorDeviceService = {
    // /**
    //  * 获取区域树
    //  */
    // manholeAreaTree(params: { type: string; table?: string; modelIds?: string }) {
    //     return request({
    //         url: "/deviceAreaTree/getTree",
    //         method: "get",
    //         params,
    //     });
    // },
    // /**
    //  * 条件，分页(不分页)查询井盖
    //  */
    // manholeList(data: recordsRequest) {
    //     return request({
    //         url: "/manhole/list",
    //         method: "post",
    //         data,
    //     });
    // },
    // /**
    //  * 根据区域查询井盖在线统计
    //  */
    // manholeMapOnline(data: Object) {
    //     return request({
    //         url: "/deviceStatistics/online",
    //         method: "post",
    //         data,
    //     });
    // },
    // /**
    //  * 根据区域查询井盖在线情况
    //  */
    // manholeMapDay(data: Object) {
    //     return request({
    //         url: "/deviceStatistics/day",
    //         method: "post",
    //         data,
    //     });
    // },
    // /**
    //  * 根据区域查询告警情况
    //  */
    // manholeMapAlarms(data: Object) {
    //     return request({
    //         url: "/manholeMap/alarms",
    //         method: "post",
    //         data,
    //     });
    // },
    monitorDeviceDetail(id: number) {
        return request({
            url: `/pipelineMonitorDevice/${id}`,
            method: "get",
        });
    },
    /**
     * 根据区域查询告警情况
     */
    alarmList(data: recordsRequest) {
        return request({
            url: '/pipelineMonitorDevice/alarmList',
            method: "post",
            data
        });
    },

    editMonitorDevice(data: MonitorDeviceCovertInfo) {
        return request({
            url: "/pipelineMonitorDevice",
            method: "put",
            data,
        });
    },

    getAlarmDetail(id: String) {
        return request({
            url: `/pipelineMonitorDevice/alarm/${id}`,
            method: "get",
        });
    },
};
