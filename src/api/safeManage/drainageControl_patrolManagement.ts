import request from '@/utils/request';

const apiUrl = '/drainagePatrolManagement'
export const patrolManagementService = {
    // 增 DrainagePatrolManagement
    add(data:any) {
        return request({
            url: `${apiUrl}`,
            method: 'post',
            data
        })
    },
    // 删 List<Long>
    delete(params:any) {
        return request({
            url: `${apiUrl}`,
            method: 'delete',
            params
        })
    },
    // 改 DrainagePatrolManagement
    update(data:any) {
        return request({
            url: `${apiUrl}`,
            method: 'put',
            data
        })
    },
    // 查单条 DrainagePatrolManagement
    getById(id:any) {
        return request({
            url: `${apiUrl}/${id}`,
            method: 'get',
        })
    },
    // 获取泵站列表信息
    getArchives(data:any) {
        return request({
            url: `/draingePumpStationArchives/getPage`,
            method: 'post',
            data
        })
    }
};


