import request from '@/utils/request';
// 公共部件对象
export interface ComponentInfo {
    type?: number;
    checked?: boolean;
    licenseNo?: string;
    id?: number;
    deviceId?: string;
    name?: string;
    sbmc?: string; // 设备名称
    bsm?: string; // 部件code
    status?: number; // 状态
    useStatus?: number; // 部件使用状态
    firstObjCategoryName?: string; // 部件类型名称
    objId?: string; // 部件ID
    sbxh?: string; // 设备型号
    contactPhone?: string; // 联系电话
    contactPerson?: string; // 联系人
    alarmState?: number; // 告警状态
    deviceSecondTypeName?: string; // 设备类型
    objState?: number; // 部件状态
    secondObjCategoryName?: string; // 部件类型
    initDate?: string; // 安装时间
    modifyDate?: string; // 修改时间
    remark?: string; // 备注
    sbzt?: string; // 设备状态
    deviceCode?: string; // 设备编号
    switchState?: string; // 开关状态
    light?: string; // 灯光状态
    objName?: string;
    szdywg?: string;
    szjd?: string;
    szsq?: string;
    ownerEnterpriseName?: string;
    opEnterpricseName?: string;
    deptName?: string;
    areaPath?: string;
    objX?: number;
    objY?: number;
}
// 设备状态
export interface devicePropertyStatus {
    name: string;
    value: string;
    propName?: string;
    prop?: string;
    specs?: string;
    unit?: string;
}
export interface drainageDeviceInfo {
    input?: string;
    objInfo?: ComponentInfo;
    extendInfo?: ComponentInfo;
    devicePropertyStatusList?: devicePropertyStatus[];
    status?: number;//在线状态
    useStatus?: number;//使用状态
    deviceCode?: string; // 设备编码
    id?: number;
    typeName?: string; // 设备类型
    name?: string; // 设备名称
    notPushAlarmNum?: number; // 未推送告警数量
}

export const drainageDeviceService = {
    // 设备批量新增
    addDrainageDevices(data: Object) {
        return request({
            url: `/drainageDevice/batch`,
            method: "post",
            data
        })
    },
    // 根据id查询设备信息
    getDrainageDeviceById(id: number) {
        return request({
            url: `/drainageDevice/${id}`,
            method: "get"
        })
    },

    // 设备新增，编辑，删除
    drainageDeviceAction(data: Object, method: string) {
        return request({
            url: `/drainageDevice`,
            method: method,
            data,
        })
    },
    // 获取设备型号
    getDeviceUnitList(data: Object) {
        return request({
            url: "/deviceExtendInfo/getConfigDeviceUnit",
            method: "post",
            data
        })
    },
    // 根据设备编码查询设备信息
    getDeviceByCode(deviceCode: string) {
        return request({
            url: `/drainageDevice/${deviceCode}`,
            method: "get"
        })
    },
    // 获取设备列表
    getDrainageDeviceList(data: Object) {
        return request({
            url: `/drainageDevice/list`,
            method: "post",
            data
        })
    }
}