//智慧管网统计分析和统计分析配置
import request from '@/utils/request';
/**
 * 查询管道分析配置标准详情
 * @returns 
 */
export function getStandardConfig() {
  return request({
    url: '/pipelineStandard',
    method: 'GET'
  })
}
export interface UpdateStandardConfig {
  num: number;
  weekValue: number;
  monthValue: number;
  yearValue: number;
}
/**
 * 
 * @param data 编辑管道分析配置请求体参数
 * @returns 
 */
export function updateStandardConfig(data: UpdateStandardConfig) {
  return request({
    url: '/pipelineStandard',
    method: 'put',
    data
  })
}
/**
 * 获取汇总统计
 * @returns 
 */
export function getSummary() {
  return request({
    url: '/pipelineStatistical/summary',
    method: 'GET'
  })
}
/**
 * 获取管道树
 * @param type 类型 1 管道 2降雨量
 * @returns 
 */
export function getPipeTree(type: string) {
  return request({
    url: `/pipelineStatistical/tree/${type}`,
    method: 'GET'
  })
}
interface SearchObj {
  valueType?: any;
  label: string;
  dateType: number;
  endTime: string;
  startTime: string;
  value: string;
}
/**
 * 获取k线图
 * @param data 请求体参数
 * @param type 请求路径参数  1 管道 2降雨量
 * @returns 
 */
export function getPipelineStatisticalKxDataAnalysis(data: SearchObj, type: string) {
  return request({
    url: `/pipelineStatistical/kxDataAnalysis/${type}`,
    method: 'post',
    data
  })
}
/**
 * 获取汇总列表
 * @param data 请求体参数
 * @param type 请求路径参数  1 管道 2降雨量
 * @returns 
 */
export function getList(data: SearchObj, type: string) {
  return request({
    url: `/pipelineStatistical/statDataSummaryAnalysis/${type}`,
    method: 'post',
    data
  })
}
/**
 * 获取明细列表
 * @param data 请求体参数
 * @param type 请求路径参数  1 管道 2降雨量
 * @returns 
 */

export function getDetailList(data: SearchObj, type: string) {
  return request({
    url: `/pipelineStatistical/statDataAnalysis/${type}`,
    method: 'post',
    data: {
      "page": {
        "current": 1,
        "size": 9999999999
      },
      "customQueryParams":data
    }
  })
}

/**
 * 获取管道监测设备列表
 * @param data 请求体参数
 * @returns 
 */

export function getPipelineMonitorDeviceList(data: any) {
  return request({
    url: '/pipelineMonitorDevice/getPage',
    method: 'post',
    data: {
      "page": {
        "current": 1,
        "size": 9999999999
      },
      "customQueryParams":data
    }
  })
}
/**
 * 获取管道监测设备详情
 * @param id 设备id
 * @returns 
 */
export function getPipelineMonitorDeviceDetail(id: string) {
  return request({
    url: `/pipelineMonitorDevice/${id}`,
    method: 'GET'
  })
}
