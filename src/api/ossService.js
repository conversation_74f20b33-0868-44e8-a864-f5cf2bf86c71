import request from '@/utils/request';
import store from '@/store';

export const ossService = {
    /**
     * sso 密钥
     */
    policy(data) {
        return new Promise((resolve, reject) => {
            request({
                url: '/oss/policy',
                method: 'get',
                data
            }).then(resp => {
                const data = resp.data;
                if (data.uploadHost.indexOf('http') === -1) {
                    let reg = 'api/'
                    if (data.uploadHost.startsWith(reg)) {
                        data.uploadHost = data.uploadHost.slice(0, reg.length) + store.getters.pageName + '/' + data.uploadHost.slice(reg.length)
                    }
                    data.uploadHost = location.origin + '/' + data.uploadHost;
                }
                if (data.accessHost.indexOf('http') === -1) {
                    data.accessHost = location.origin + '/' + data.accessHost;
                }
                if (data.path.lastIndexOf('/') != data.path.length - 1) {
                    data.path += '/'
                }
                resolve(data)
            })
        })
    },
    async uploadBase64Image(base64String, filename) {
        let file = base64toImage(base64String, filename)
        let oss = await this.policy({})
        return request({
            url: oss.uploadHost,
            method: 'post',
            data: {
                file,
                policy: oss.policy,
                OSSAccessKeyId: oss.accessId,
                Signature: oss.signature,
                path: oss.path,
                key: oss.path + filename
            },
            headers: {
                'Content-Type': 'multipart/form-data'
            }
        })
    },
    async uploadFiles(file, filename) {
        let oss = await this.policy({})
        return request({
            url: oss.uploadHost,
            method: 'post',
            data: {
                file,
                policy: oss.policy,
                OSSAccessKeyId: oss.accessId,
                Signature: oss.signature,
                path: oss.path,
                key: oss.path + filename
            },
            headers: {
                'Content-Type': 'multipart/form-data'
            }
        })
    }

}

function base64toImage(base64String, filename) {
    let arr = base64String.split(',');
    let mime = arr[0].match(/:(.*?);/)[1];
    let bstr = atob(arr[1]);
    let n = bstr.length;
    let u8arr = new Uint8Array(n)
    while (n--) {
        u8arr[n] = bstr.charCodeAt(n);
    }
    let file = new File([u8arr], filename, { type: mime });
    return file;
}
function base64toFile(base64String, filename, mimeType) {
    // 将Base64字符串转换为ArrayBuffer
    let byteString = atob(base64String);
    let arrayBuffer = new ArrayBuffer(byteString.length);
    let uint8Array = new Uint8Array(arrayBuffer);
    for (let i = 0; i < byteString.length; i++) {
        uint8Array[i] = byteString.charCodeAt(i)
    }

    // 创建Blob对象
    let blob = new Blob([arrayBuffer], { type: mimeType })
    // 创建文件对象
    let file = new File([blob], filename, { type: mimeType });
    return file;
}
