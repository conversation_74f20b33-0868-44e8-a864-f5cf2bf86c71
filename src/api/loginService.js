import request from '@/utils/request';
import store from '@/store';
const applicationIds = {
    traffic: 2,
    livable: 3,
    safe: 4,
    base: 1
}
export const loginService = {
    /**
     * 登录
     */
    login(data) {
        return request({
            url: '/login',
            method: 'post',
            data
        })
    },
    /**
     * 退出
     */
    logout() {
        return request({
            url: '/common/user/logout',
            method: 'get',
            data: {}
        })
    },
    /**
     * 获取用户信息
     */
    getLoginUser() {
        return request({
            url: '/common/user/getLoginUser',
            method: 'get',
            params: {}
        })
    },
    /**
     * 获取用户信息
     */
    getUserCode(param) {
        return request({
            url: `/privilege/selectPrivilegeByUser?type=0&applicationId=${applicationIds[store.getters.pageName]}` + (param || ''),
            method: 'get',
            params: {}
        })
    },
    /**
     * 获取应用权限
     */
    getApplicationAuth() {
        return request({
            url: '/privilege/selectApplicationIds',
            method: 'get',
            params: {}
        })
    },
    password(data) {
        return request({
            url: '/common/user/password',
            method: 'put',
            data
        })
    },
    phone(data) {
        return request({
            url: '/common/user/updatePhone',
            method: 'put',
            data
        })
    },
    nickname(data) {
        return request({
            url: '/common/user/updateNickname',
            method: 'put',
            data
        })
    },
    saveUserPeerId(peerId) {
        return request({
            url: `/common/user/saveUserPeerId?peerId=${peerId}`,
            method: 'post',
            data: {}
        })
    },
}
