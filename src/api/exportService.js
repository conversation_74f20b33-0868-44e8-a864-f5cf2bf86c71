import request from '@/utils/request';
import { Message } from 'view-ui-plus';
/**
 * @url:导出接口
 * @data参数
 * @name导出文件名
 * */
let pollingTimer = null
let taskId = ''
let fileName = ''
// 轮询的最长时间，单位秒
let maxTime = 10 * 60;

export const exportService = (url, data, method = 'post', name = '') => {
    request({
        url: url,
        method,
        data,
    }).then(res => {
        if (res.success) {
            Message.loading({
                content: '导出中...',
                duration: 0
            });
            fileName = name
            taskId = res.data
            startPolling()
        }
    })
}
let timer = null

const startPolling = async () => {
    if (timer) clearTimeout(timer)
    const res = await fetchProgress()
    if (res === '继续轮询') {
        timer = setTimeout(() => {
            startPolling()
        }, 600)
    }
}
const fetchProgress = () => {
    return new Promise(async (resolve) => {
        const result = fetchProgressMethod && await fetchProgressMethod();
        if (result.success) {
            if (!result.data) return;
            console.log('轮询结果:', result.data)
            const {
                totalCount,
                estimateCount,
                failedMessage,
                status,
                fileUrl,
            } = result.data;
            if (+status === 1) {
                // 导出中， 更新进度，继续轮询
                resolve('继续轮询')
                return;
            }
            // 导出完成
            if (+status === 2) {
                clearStop()
                let arr = fileUrl.split('/')
                Util.download(fileUrl, fileName || arr[arr.length - 1])
                return;
            }
            // 导出失败
            if (+status === 3) {
                clearStop()
                Message.error({
                    content: failedMessage,
                    duration: 3
                });
                return;
            }
        }
    })
    // const result = fetchProgressMethod && await fetchProgressMethod();
    // if (result.success) {
    //     if (!result.data) return;
    //     console.log('轮询结果:', result.data)
    //     const {
    //         totalCount,
    //         estimateCount,
    //         failedMessage,
    //         status,
    //         fileUrl,
    //     } = result.data;
    //     if (+status === 1) {
    //         // 导出中， 更新进度，继续轮询
    //         return;
    //     }
    //     // 导出完成
    //     if (+status === 2) {
    //         clearStop()
    //         let arr = fileUrl.split('/')
    //         Util.download(fileUrl, fileName || arr[arr.length - 1])
    //         return;
    //     }
    //     // 导出失败
    //     if (+status === 3) {
    //         clearStop()
    //         Message.error({
    //             content: failedMessage,
    //             duration: 3
    //         });
    //         return;
    //     }
    // }
}
function clearStop() {
    clearInterval(pollingTimer)
    Message.destroy()
}
const fetchProgressMethod = async () => {
    return request({
        url: '/excelTask/getTaskInfo',
        method: 'get',
        params: {
            taskId
        }
    })
}
