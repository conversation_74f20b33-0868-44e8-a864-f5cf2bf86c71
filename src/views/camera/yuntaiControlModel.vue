<template>
		
<div class="">
	<Row type="flex" style="" class="main-con">
		<Col class="flex-auto">
			<div class="video" id="video-box">
				<div >
					<JudgeVideo ref="vidc" :videoObj="ysy.obj" :index="ysy.index" :styles="ysy.styles" />
				</div>
				
			</div>
		</Col>
		<Col class="m-right">
			<div class="tit"><span>云台控制</span></div>
			<div class="control" v-if="y_c.show">
				<div class="direction">
					<div class="box" :class="item.class" v-for="item in y_c.c.directionArr" :style="'visibility: ' + (item.show ? 'visible':'hidden')" >
						<div class="btn" v-if="item.class === 'p'" @click="chooseControl(item)">
							<Icon type="md-locate" color="#000" size="18" />
						</div>
						<div class="btn" v-else @mousedown="chooseControl(item)" @mouseup="stopControl">
							<Icon type="md-navigate" color="#000" size="15" />
						</div>
					</div>
				</div>
				<div class="step" v-if="y_c.speed.show">
					<span>云台速度</span>
					<Select v-model="y_c.speed.value">
				        <Option v-for="(item, index) in y_c.speed.specs" :value="index" :key="index">{{ item }}</Option>
				    </Select>
				</div>
				<div class="zooms">
					<div class="box" v-for="item in y_c.c.zoomsArr" :key="item.id" :style="'visibility: ' + (item.show ? 'visible':'hidden')">
						<div class="sub btn" @mousedown="chooseControl(item, item.value[0])" @mouseup="stopControl"><Icon type="md-remove" color="#000" size="18" /></div>
						<p>{{item.name}}</p>
						<div class="add btn" @mousedown="chooseControl(item, item.value[1])" @mouseup="stopControl"><Icon type="md-add" color="#000" size="18" /></div>
					</div>
				</div>
			</div>
			<div class="tit"><span>图像抓拍</span></div>
			<div class="snap" v-if="y_c.snap.show">
				<span @click="pictureCapture"><Icon type="ios-camera-outline" color="#fff" size="30" /></span>
			</div>
		</Col>
	</Row>
</div>
	
</template>

<script>
import Util from '@/libs/util'
import YsyModel from "./ysyModel";
export default {
	components:{YsyModel},
	name: 'yuntaiControlModel',
    data () {
	    return {
	    	y_c: {
	    		show: false,
	    		c: {
	    			directionArr: [
				    	{name: "左上", class: 'lt', value: '4', show: false},
				    	{name: "上", class: 't', value: '0', show: false},
				    	{name: "右上", class: 'rt', value: '6', show: false},
				    	{name: "左", class: 'l', value: '2', show: false},
				    	{name: "定位", class: 'p', value: '1', show: false},
				    	{name: "右", class: 'r', value: '3', show: false},
				    	{name: "左下", class: 'lb', value: '5', show: false},
				    	{name: "下", class: 'b', value: '1', show: false},
				    	{name: "右下", class: 'rb', value: '7', show: false},
			    	],
			    	zoomsArr: [
				    	{name: "变倍", id: 1, value: ['9', '8']},
				    	{name: "变焦", id: 2, value: ['10', '11']},
			    	],
	    		},
	    		speed: {
	    			show: false,
		    		specs: {},
		    		value: '1',
		    		identifier: 'rotation_speed'
		    	},
	    		snap: {
	    			show: false,
	    		}
	    	},
	    	ysy: {
	    		obj: {},
	    		styles: {},
	    		index: -1
	    	}
		    
	    }
    },
    computed: {
    	
    },
    mounted(){
    	this.ysy.styles = this.getStyles()
    },
    methods: {
    	init(item, index){
    		let data = Util.objClone(item)
    		this.ysy.obj = data;
    		this.ysy.index = index;
    		this.clear()
    		data.mainId = 'ysyConId'
    		setTimeout(()=> {
    			this.$refs.vidc.open(data)
    		}, 100)
    		this.getData()
    	},
    	getData(){
    		Util.request('/deviceService/getDeviceServicesList', {deviceUnitId: this.ysy.obj.deviceUnitId}, 'post').then(resp => {
    			let data = resp.data.data;
    			let yun = this.$_.find(data, {identifier: 'ptz_control'})
    			if(yun){
    				yun.paramList.forEach(o => {
    					let specs = JSON.parse(o.specs) 
    					if(o.identifier == 'operation_command'){
    						this.y_c.c.directionArr.forEach((item) =>{
	    						if(specs[item.value]){
	    							item.show = true
	    							item.identifier = o.identifier
	    							item.service_id = yun.identifier
	    						}
	    					})
	    					this.y_c.c.zoomsArr.forEach((item) =>{
	    						if(specs[item.value[0]] || specs[item.value[1]]){
	    							item.show = true
	    							item.identifier = o.identifier
	    							item.service_id = yun.identifier
	    						}
	    					})
    					}else if(o.identifier == this.y_c.speed.identifier){
    						this.y_c.speed.specs = specs
    						this.y_c.speed.show = true
    						this.y_c.speed.service_id = yun.identifier
    					}
    				})
    				this.y_c.show = true
    			}
    			let stop = this.$_.find(data, {identifier: 'stop_turning'})
    			if(stop){
    				let s = this.y_c.c.directionArr[4]
    				s.show = true
    				s.identifier = stop.paramList[0].identifier
    				s.service_id = stop.identifier
    				this.y_c.c.directionArr.splice(4, 1, s)
    			}
    			let pic = this.$_.find(data, {identifier: 'picture_capture'})
    			if(pic){
    				this.y_c.snap.show = true
    				this.y_c.snap.service_id = pic.identifier
    				this.y_c.snap.identifier = pic.paramList[0].identifier
    			}
    			console.log(this.y_c)
			})
    	},
    	stopControl() {
    		this.chooseControl(this.y_c.c.directionArr[4], '', true)
    	},
    	chooseControl(item, value, noMsg){
    		let speed = this.y_c.speed
    		let parameter = {};
    		parameter[item.identifier] = value || item.value
    		if(item.service_id == speed.service_id){
    			parameter[speed.identifier] = speed.value
    		}
    		
    		let param = {
    			downParameter: {
    				device_id: this.ysy.obj.ezopenObj.deviceSerial,
    				service_id: item.service_id,
    				parameter: parameter, 
    			}
    		}
    		this.send(param, noMsg)
    	},
    	pictureCapture(){
    		let item = this.y_c.snap
    		let parameter = {};
    		parameter[item.identifier] = 1
    		let param = {
    			downParameter: {
    				device_id: this.ysy.obj.ezopenObj.deviceSerial,
    				service_id: item.service_id,
    				parameter: parameter, 
    			}
    		}
    		this.send(param)
    	},
    	send(param, noMsg = false){
    		console.log(param)
    		this.$Util.request({url:'/deviceManage/send', data:param, method:'post'}).then(resp => {
    			if(resp.data.data.code == '0') {
    				if (!noMsg) {
	                    this.$Message.success(resp.data.data.msg || '命令下发成功')
                    }
                }else{
                	if (!noMsg) {
                		this.$Message.error(resp.data.data.msg)
                	}
                }
    		})
    	},
    	clear(){
    		this.$refs.vidc.clear()
    	},
    	getStyles (){
    		let el = document.getElementById('video-box')
    		var w = window.innerWidth * 0.65 - 150 -30,
	    		h = 500;
    		if(el){
    			w = el.offsetWidth || w
    			h = el.offsetHeight || h
    		}
    		return {
    			width: parseInt(w) + 'px',
    			height: parseInt(h) + 'px',
    			position: 'relative'
    		}
    	}
    }
}
</script>

<style lang="less" scoped>

.main-con{
	background: #414141;
	min-height: 500px;
	align-items: stretch;

	.flex-auto{
		flex: 1;
		padding: 10px;
	}
	.m-right{
		width: 150px;
		background: #5C5C5C;

		.tit{
			color: #fff;
			line-height: 32px;
			font-size: 13px;
			border-bottom: 1px solid #fff;
			span{
				display: inline-block;
				padding: 0 10px;
			}
		}
	}

	.video{
		width: 100%;
		height: 100%;
		background: #000;
		position: relative;
	}

	.control{
		padding: 10px;
		color: #fff;
		margin-bottom: 15px;

		.btn{
			width: 30px;
			height: 30px;
			background: #BCBCBD;
			border-radius: 50%;
			text-align: center;
			line-height: 30px;
			cursor: pointer;
			.ivu-icon{
				vertical-align: middle;
			}
		}

		.direction{
			display: flex;
			flex-wrap: wrap;
			justify-content: space-between;
			margin-bottom: 15px;

			.box{
				margin: 6px;
			}
			.lt i{transform: rotate(-45deg);}
			.rt i{transform: rotate(45deg);}
			.l i{transform: rotate(-90deg);}
			.r i{transform: rotate(90deg);}
			.lb i{transform: rotate(-135deg);}
			.b i{transform: rotate(180deg);}
			.rb i{transform: rotate(135deg);}
		}

		.step{
			display: flex;
			align-items: center;
			justify-content: center;
			font-size: 13px;
			margin-bottom: 10px;

			.ivu-select{
				width: 60px;
				line-height: 20px;
				margin-left: 5px;

				/deep/ .ivu-select-selection{
					height: 20px;

					.ivu-select-placeholder,
					.ivu-select-selected-value{
						line-height: 20px;
						height: 20px;
						font-size: 13px;
					}
				} 
				.ivu-select-item{
					padding: 2px 10px;
					font-size: 13px;
				}
			}
		}

		.zooms{
			.box{
				padding: 6px;
				display: flex;
				align-items:center;
				justify-content: space-between;

				p{
					padding:0 5px;
					text-align:center;
					font-size: 13px;
				}
			}
		}
	}

	.snap{
		padding: 8px 15px;
		span{
			cursor: pointer;
		}
	}
}
</style>