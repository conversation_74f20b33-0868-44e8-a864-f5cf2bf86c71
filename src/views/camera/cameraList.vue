<template>
  <div class="main">
    <Card dis-hover>
      <Row>
        <Col span="1"><p style="margin-top:5px">名称</p></Col>
        <Col span="4">
          <Input v-model="customQueryParams.name" placeholder="请输入" />
        </Col>
        <Col offset="1">
		<Button @click="clearSearch" style="width: 70px">重置</Button>
		<Button type="primary" @click="getSearchObj" style="width: 70px; margin-left: 10px">搜索</Button>
		</Col>
      </Row>
    </Card>
    <YsyVideoPlayerModel :video-list="videoObj.afterList" style="margin-top:20px"/>
    <PageModule
      ref="pageInstance"
      style="padding-bottom: 10px;"
      :default-page-size="12"
      :page-transfer="true"
      :page-opts="[12, 24, 48, 96]"
      url="/dahc/getDeviceVideoList"
      @resultData="setVideoList"
    />
  </div>
</template>

<script>
import YsyVideoPlayerModel from './ysyVideoPlayerModel';
import YsyModel from './ysyModel';

export default {
  name: 'cameraList',
  components: {
    YsyVideoPlayerModel,
    YsyModel
  },
  props: {},
  data() {
    return {
      videoObj: {
        beforeList: [],
        afterList: []
      },
      customQueryParams: {
        onlineState: null,
        name: null
      }
    };
  },
  mounted() {
    this.getData();
  },
  methods: {
    getData() {
      this.$refs.pageInstance.setCustomQueryP(this.customQueryParams);
    },
    setVideoList(data) {
      this.videoObj.beforeList = data;
      this.resolverJoinObj();
    },
    getSearchObj() {
      this.getData();
    },
    clearSearch() {
      this.customQueryParams = {
        onlineState: null,
        name: null
      };
      this.getData();
    },
    resolverJoinObj() {
      let result = [];
      // eslint-disable-next-line no-undef
      _.map(this.videoObj.beforeList, (obj) => {
        let videoObj = {
          deviceId: obj.id,
          deviceUnitId: obj.deviceUnitId,
          deviceName: obj.deviceName,
          deviceCode: obj.deviceCode,
          videoUrl: '',
          onlineState: obj.onlineState,
          propName: '',
          ezopenObj: {
			  channelNoVal: '',
			  accessToken: '',
			  deviceSerial: obj.deviceCode
          }
        };
        let attributeList = obj.childAttributeList;
        if (attributeList && attributeList.length > 0) {
          for (let i = 0; i < attributeList.length; i++) {
            let item = attributeList[i];
            if (
              item.propCode === 'ezopen' ||
              item.propCode === 'flvUrl' ||
              // item.propCode === 'flvHttpsUrl' ||
              item.propCode === 'mu38' ||
              item.propCode === 'live_address'
            ) {
              videoObj.videoUrl = item.propValue;
              // 暂时修复方案，因为修改了物模型的propName 导致视频不能播放。全局改为propCode 会导致新的问题，后期解决，遗留问题，视频封面不能展示
				// videoObj.propName = item.propName;
				videoObj.propName = item.propCode;
            } else if (item.propName === 'channelNo') {
              videoObj.ezopenObj.channelNoVal = item.propValue;
            } else if (item.propName === 'accessToken') {
              videoObj.ezopenObj.accessToken = item.propValue;
            } else {
              videoObj.ezopenObj[item.propName] = item.propValue;
            }
            // if (videoObj.videoUrl) {
            // 	break;
            // }
          }
        }
        result.push(videoObj);
      });
      console.log(result);
      this.videoObj.afterList = result;
    }
  }
};
</script>

<style scoped="scoped" lang="less">
.main {
  .ysyVideo {
    display: flex;
    justify-content: space-around;
    flex-wrap: wrap;
  }
}
</style>
