<template>
  <div class="container">
    <div class="mask" v-show="isShowStartTime || isShowEndTime" @click="hideDatePop"></div>
    <div class="choose-time">
      <div class="name">时间</div>
      <div class="right">
        <DatePicker type="datetime" placeholder="开始时间"
                    :open="isShowStartTime"
                    :value="startTime"
                    @on-change="handleStartTimeChange"
                    @on-ok="handleStartTimeOk"
                    confirm>
          <div class="ivu-input-wrapper" @click="handleStartTimeClick">
            <input type="text" :value="startTime" name="" placeholder="开始时间" class="ivu-input">
          </div>
        </DatePicker>
        <span>-</span>
        <DatePicker type="datetime" placeholder="结束时间" placement="bottom-end"
                    :open="isShowEndTime"
                    :value="endTime"
                    @on-change="handleEndTimeChange"
                    @on-ok="handleEndTimeOk"
                    confirm>
          <div class="ivu-input-wrapper" @click="handleEndTimeClick">
            <input type="text" :value="endTime" name="" placeholder="结束时间" class="ivu-input">
          </div>
        </DatePicker>
      </div>
    </div>

    <div class="main">
      <div class="posr">
        <ysy-model main-id="ysyModealId" ref="ysmM" :styles="boxStyle"/>
        <div class="full" @click="videoFull"><Icon type="md-expand" size="25" color="#ffffff" /></div>
      </div>
        
      
      <div class="device-name">{{ videoObj.deviceName }}</div>
    </div>
  </div>
</template>

<script>

import YsyModel from './ysyModel'
import OpenFlv from "../../distributionRoom/openFlv";

export default {

  name: 'cameraDetail',
  components: {YsyModel, OpenFlv},
  data() {
    return {
      isShowStartTime: false,
      isShowEndTime: false,
      startTime: "",
      endTime: "",
      videoObj: {}
    }
  },
  watch:{
      '$route':{
          deep:true,
          handler(data){
            console.log(data)
            if(data.path === "/ysyCameraPhDetail"){
              this.initQuery(data.query)
            }
          }
      }
  },
  computed: {
    boxStyle: function () {
      var w = parseInt(window.innerWidth * 0.9);
      var h = 300;
      return {
        width: w + 'px',
        height: h + 'px'
      };
    }
  },
  mounted() {
    this.initQuery(this.$route.query)
  },
  methods: {
    initQuery(data){
      this.videoObj = data;
      this.videoObj.id = 'video-container';
      this.videoObj.copyUrl = data.url;
      this.videoObj.mainId = 'ysyModealId';
      this.$refs.ysmM.clear();
      this.$refs.ysmM.init(this.videoObj).then(re => {
        re.play();
      })
    },
    handleStartTimeClick() {
      this.isShowStartTime = true;
      this.isShowEndTime = false;
    },
    handleStartTimeChange(date) {
      this.startTime = date;
    },
    handleStartTimeOk() {
      this.handleEndTimeClick()
    },
    handleEndTimeClick() {
      if (this.startTime) {
        this.isShowEndTime = true;
        this.isShowStartTime = false;
      } else {
        this.handleStartTimeClick()
      }
    },
    handleEndTimeChange(date) {
      this.endTime = date;
    },
    handleEndTimeOk() {
      this.isShowEndTime = false;
      this.judgeTime()
    },
    hideDatePop() {
      this.isShowStartTime = false;
      this.isShowEndTime = false;
    },
    judgeTime() {
      if (this.startTime && this.endTime) {
        var s = new Date(this.startTime).getTime();
        var e = new Date(this.endTime).getTime();
        if (s > e) {
          var time = this.endTime;
          this.endTime = this.startTime;
          this.startTime = time
        }
        var obj = this.videoObj;
        let cn = this.videoObj.channelNoVal;

        obj.url = obj.copyUrl.substring(0, obj.copyUrl.lastIndexOf('/') + 1) + cn + '.rec?begin=' + this.formatDate(this.startTime) + '&end=' + this.formatDate(this.endTime);
        this.$refs['ysydetail'].init(obj).then(re => {
          re.play();
        })
      }
    },
    videoFull() {
      this.$refs.ysmM.full();
    },
    formatDate(d) {
      var date = new Date(d);
      var o = [
        {'y': date.getFullYear()},
        {'M': (date.getMonth() + 1).toString().padStart(2, '0')},
        {'d': date.getDate().toString().padStart(2, '0')},
        {'h': date.getHours().toString().padStart(2, '0')},
        {'m': date.getMinutes().toString().padStart(2, '0')},
        {'s': date.getSeconds().toString().padStart(2, '0')}
      ];
      let r = [];
      Object.keys(o).forEach(key => {
        r.push(Object.values(o[key]))
      })
      return r.join('');
    }
  }
}
</script>

<style lang="less" scoped>
.choose-time {
  display: flex;
  align-items: center;
  padding: 10px;
  position: relative;
  z-index: 10;

  .name {
    margin-right: 10px;
    font-size: 12px;
  }

  .right {
    flex: 1;
    display: flex;
    align-items: center;
    border: 1px solid #dcdee2;

    span {
      margin: 0 5px;
    }
  }
}

.ivu-input {
  text-align: center;
  border: none;
  font-size: 12px;
}

.ivu-input:focus {
  box-shadow: none;
}

.mask {
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
}

.main {
  width: 90%;
  margin: 0 auto;
  padding: 10px 0;

  .posr{
    position: relative;

    .full{
      position: absolute;
      width: 30px;
      height: 30px;
      border-radius: 4px;
      background: rgba(0,0,0,0.5);
      bottom: 10px;
      right: 10px;
      text-align: center;
      line-height: 30px;

      i{
        vertical-align: middle;
      }
    }
  }

  .device-name {
    width: 100%;
    text-align: center;
    padding: 5px 10px;
    background: #F2F2F2;
    margin-top: 2px;
    border-radius: 4px;
    font-size: 13px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;;
  }
}
</style>
