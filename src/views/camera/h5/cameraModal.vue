<template>
  <div>
    <Row>
      <Col span="20">
        <DatePicker type="datetimerange"
                    placement="bottom-end"
                    :options="dateRangeOpt"
                    v-model="dateRanges"
                    format="yyyy-MM-dd hh:mm:ss"
                    placeholder="请选择回放时间" style="width:100%">
        </DatePicker>
      </Col>
      <Col span="4" push="1">
        <Button>确定</Button>
      </Col>
    </Row>
    <YsyModel/>
  </div>
</template>
<script>
import YsyModel from "./ysyModel";

export default {
  name: "cameraModal",
  components: {
    YsyModel
  },
  data() {
    return {
      dateRanges: [],
      dateRangeOpt: {
        disabledDate(date) {
          return date && date.valueOf() > Date.now();
        }
      }
    }
  }
}
</script>

<style scoped>

</style>
