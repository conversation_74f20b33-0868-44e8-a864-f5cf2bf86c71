<template>
	<div class="main">
		<div v-for="(item,index) in videoList " :key="index" class="con">
			<div class="item" :style="boxStyle">
				<JudgeVideo :ref="'vid' + index" :video-obj="item" :index="index" :styles="boxStyle" v-show="perViewObj.currentIndex === index" @on-flv-fail="perViewObj.currentIndex = '---'" />
				<div class="cover" v-show="perViewObj.currentIndex != index" :style="{backgroundImage: 'url('+imagesUrl+')'}"></div>
				<div class="action" v-show="perViewObj.minPerView[index]">
					<div class="play" v-show="perViewObj.currentIndex!=index" @click="videoPlay(item,index)">
						<Icon type="logo-youtube" size="50" color="white" />
					</div>
					<div class="pause" v-show="perViewObj.currentIndex==index" @click="suspend(item,index)">关闭</div>
				</div>
			</div>
			<div class="device-name" @click="toCameraDetail(item)">{{ item.deviceName }}</div>
		</div>
	</div>
</template>
<script>

export default {
	name: 'ysyViodeoPlayerModel',
	components: {},
	props: {
		videoList: {
			type: [Array],
			default: () => []
		}
	},
	data() {
		return {
			perViewObj: {
				minPerView: [],
				currPerViewMinImg: '',
				currentIndex: '------'
			}
		}
	},
	computed: {
		boxStyle: function() {
			var w = parseInt(window.innerWidth * 0.9 * 0.49);
			return {
				width: w + 'px',
				height: w + 'px'
			};
		},
		imagesUrl() {
			return require('../images/blue-ysy.png')
		}
	},
	watch: {
		videoList: {
			immediate: true,
			deep: true,
			handler(nv) {
				let res = [];
				for (let i = 0; i < nv.length; i++) {
					res.push(true);
				}
				this.perViewObj.minPerView = res
			}
		}
	},
	mounted() {

	},
	methods: {
		videoPlay(data, index) {
			this.clear(this.perViewObj.currentIndex);
			let format = ['ezopen', 'flvUrl']
			if (format.indexOf(data.propName) < 0) {
				if (data.propName) {
					this.$Message.warning('不受支持的:' + data.propName + '  协议')
				} else {
					this.$Message.warning('请检查设备个性化配置')
				}
				this.perViewObj.currentIndex = '---'
				return
			}
			this.perViewObj.currentIndex = index
			this.$refs['vid' + index][0].open(data)
		},
		toCameraDetail(data) {
			if (!data || data.propName != 'ezopen') {
				this.$Message.warning('回放不受支持的监控设备')
				return;
			}
			this.clear(this.perViewObj.currentIndex);
			var obj = {
				url: data.videoUrl,
				accessToken: data.ezopenObj.accessToken,
				deviceName: data.deviceName,
				propName: data.propName,
				channelNoVal: data.ezopenObj.channelNoVal
			}
			this.perViewObj.currentIndex = '------';
			this.$router.push({
				path: '/ysyCameraPhDetail',
				query: obj
			})
		},
		suspend(item, index) {
			this.clear(index)
			this.perViewObj.currentIndex = '------'
		},
		clear(index) {
			try {
				return this.$refs['vid' + index][0].clear()
			} catch (e) {}
		}
	}
}
</script>

<style scoped="scoped" lang="less">
.main{
	display: flex;
	flex-wrap: wrap;
	justify-content: space-between;
	width: 100%;
	.con{
		position: relative;
		margin-bottom: 20px;
		z-index: 1;
		overflow: hidden;
		border-radius: 4px;
		.item{
			position: relative;
			background: #000;
		}
		.cover{
			width: 100%;
			height: 100%;
			background-repeat: no-repeat;
			background-size: 100% 100%;
		}
		.action{
			.play{
				position: absolute;
				width: 100%;
				height: 100%;
				top: 0;
				left: 0;
				background: rgba(0, 0, 0, 0.3);
				display: flex;
				z-index: 100;
				i{
					margin: auto;
				}
			}
			.pause{
				position: absolute;
				top: 5px;
				right: 10px;
				color: #fff;
				z-index: 1000;
				font-size: 12px;
			}
		}
	}
}
.video-details {
	background-color: white;
	width: 250px;
	height: 35px;
	margin-top: -35px;
	border-radius: 0px 0px 8px 8px;
	position: absolute;
	border-style: solid;
	border-color: #f6f6f6;
}

.minPerView {
	width: 100%;
	height: 100%;
	border-radius: 4px;
	background-size: 100% 100%;
	position: relative;
}
.posa{
	position: absolute;
	width: 100%;
	height: 100%;
	top: 0;
	left: 0;
	background: rgba(14, 21, 60, 0.5);
	z-index: 1;
	display: flex;
	border-radius: 4px;
}
.posa i{
	margin: auto;
}

.hello-ezuikit-js {
	display: flex;
	flex-wrap: wrap;
	justify-content: space-between;
	width: 100%;
}
.device-name{
	width: 100%;
	text-align: center;
	padding: 5px 10px;
	background: #F2F2F2;
	margin-top: 2px;
	border-radius: 4px;
	font-size: 13px;
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis;;
}
</style>
