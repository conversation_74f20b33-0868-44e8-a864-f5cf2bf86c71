<template>
  <div class="container">
    <div class="search-bar">
      <div class="weui-search-bar__form">
        <div class="weui-search-bar__box">
          <Icon type="ios-search-outline" size="18"/>
          <input type="search" v-model="searchValue" @search="getSearchObj" name="" placeholder="搜索"
                 class="weui-search-bar__input" id="searchBar">
        </div>
        <label class="weui-search-bar__label" v-show="!isShowSearchInput" @click="showSearchInput">
          <Icon type="ios-search-outline" size="18"/>
          <span class="weui-search-bar__text">搜索</span>
        </label>
      </div>
      <div class="weui-search-bar__cancel-btn" v-show="isShowSearchInput" @click="hideSearchInput">取消</div>
    </div>
    <div class="main">
      <YsyVideoPlayerModel :video-list="videoObj.afterList"/>
      <!-- <YsyVideoPlayerModel v-for="(item, index) in videoObj.afterList" :video-obj="item" :video-index="index" /> -->
    </div>
    <div class="weui-loadmore weui-loadmore_line" v-if="!loadingMore">
      <div class="weui-loadmore__tips weui-loadmore__tips_in-line" v-if="!isLast" @click="loadMore">点击加载更多</div>
      <div class="weui-loadmore__tips weui-loadmore__tips_in-line" v-if="isLast">没有更多数据</div>
    </div>
  </div>


</template>

<script>
import Util from '@/libs/util'
import YsyVideoPlayerModel from "./ysyVideoPlayerModel";

export default {

  name: 'cameraList',

  data() {
    return {
      videoObj: {
        beforeList: [],
        afterList: []
      },
      customQueryParams: {
        onlineState: null,
        name: null
      },
      loadingMore: false,
      isLast: false,
      page: 1,
      isShowSearchInput: false,
      searchValue: "",
    }
  },
  components: {
    YsyVideoPlayerModel
  },
  mounted() {
    this.getData();
  },
  methods: {
    loadMore: function () {
      this.page += 1;
      this.loadingMore = true;
      this.getData(true);
    },
    getSearchObj() {
      this.customQueryParams.name = this.searchValue;
      this.videoObj = {
        beforeList: [],
        afterList: []
      }
      this.getData()
    },
    hideSearchInput() {
      this.isShowSearchInput = false;
      this.searchValue = '';
      this.getSearchObj()
    },
    showSearchInput() {
      this.isShowSearchInput = true;
      document.getElementById('searchBar').focus();
    },
    getData(isLoadMore = false) {
      if (!isLoadMore) {
        this.page = 1
      }
      var params = {
        page: {current: this.page, size: 15},
        customQueryParams: this.customQueryParams
      }
      Util.request('/dahc/getDeviceVideoList', params, 'post', {}).then(res => {
        // console.log(res.data)
        if (res.data.success) {
          const respData = res.data.data;
          let records = respData.records, productList = [];
          if (isLoadMore) {
            productList = this.videoObj.beforeList.concat(records)
          }
          this.videoObj.beforeList = productList;
          this.isLast = respData.pages == respData.current || respData.pages == 0
          this.loadingMore = false;
          this.resolverJoinObj(records)
        }
      }).catch(function (error) {
        console.log('导出流水报表发生错误！', error);
      })
    },
    resolverJoinObj(records) {
      //支持3种播放模式 mu38 flv ezopen
      let result = []
      _.map(records, obj => {
        let videoObj = {
          deviceName: obj.deviceName,
          videoUrl: '',
          onlineState: obj.onlineState,
          deviceCode: obj.deviceCode,
          propName: '',
          ezopenObj: {
            channelNoVal: '',
            accessToken: ''
          }
        };
        let attributeList = obj.childAttributeList
        if (attributeList && attributeList.length > 0) {
          for (let i = 0; i < attributeList.length; i++) {
            let item = attributeList[i];
            if (item.propName === "ezopen" || item.propName === "flvUrl" || item.propName === "mu38") {
              videoObj.videoUrl = item.propValue
              videoObj.propName = item.propName
            } else if (item.propName === "channelNo") {
              videoObj.ezopenObj.channelNoVal = item.propValue
            } else if (item.propName === "accessToken") {
              videoObj.ezopenObj.accessToken = item.propValue
            }
            // if (videoObj.videoUrl) {
            //   break;
            // }
          }
        }
        result.push(videoObj)

      })
      console.log(result)
      // this.videoObj.afterList = result;
      this.videoObj.afterList = this.videoObj.afterList.concat(result);
    }
  }
}
</script>
<style lang="less" scoped="scoped">
.container{
    background: #fff;
}
.search-bar {
  position: relative;
  padding: 8px;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  box-sizing: border-box;
  background-color: #EDEDED;
  -webkit-text-size-adjust: 100%;
  -webkit-box-align: center;
  -webkit-align-items: center;
  align-items: center;
}

.weui-search-bar__form {
  position: relative;
  -webkit-box-flex: 1;
  -webkit-flex: auto;
  flex: auto;
  background-color: #fff;
  border-radius: 4px;
}

.weui-search-bar__box {
  position: relative;
  padding: 0 10px;
  height: 100%;
  width: 100%;
  box-sizing: border-box;
  z-index: 1;
  display: flex;
  align-items: center;
}

.weui-search-bar__input {
  height: inherit;
  line-height: inherit;
  padding: 8px 0;
  width: 100%;
  height: 1.14285714em;
  border: 0;
  font-size: 14px;
  line-height: 1.14285714em;
  box-sizing: content-box;
  background: transparent;
  caret-color: var(--weui-BRAND);
  color: var(--weui-FG-0);
}

.weui-search-bar__input:focus {
  box-shadow: none;
  border: none;
  outline: none;
}

.weui-search-bar__label {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 2;
  font-size: 0;
  border-radius: 4px;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
  align-items: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  background: #fff;
}

.weui-search-bar__label span {
  display: inline-block;
  font-size: 14px;
  vertical-align: middle;
}

.weui-search-bar__cancel-btn {
  margin-left: 8px;
  line-height: 28px;
  white-space: nowrap;
}

.main {
  width: 92%;
  margin: 0 auto;
  padding: 10px 0;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}

.weui-loadmore {
  width: 70%;
  margin: 0 auto 10px;
  line-height: 40px;
  font-size: 14px;
  text-align: center;
}

.weui-loadmore__tips {
  display: inline-block;
  vertical-align: middle;
  color: #777;
}

.weui-loadmore__tips::before,
.weui-loadmore__tips::after {
  content: "";
  width: 30px;
  height: 1px;
  background: #e0dede;
  display: inline-block;
  vertical-align: middle;
  margin: 0 5px;
}

</style>
