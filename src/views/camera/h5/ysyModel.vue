<script>
import {h} from 'vue'

export default {
  name: 'ysyModel',
  props: {
    styles: {
      type: [Object],
      default: () => {
        return {width: '200px', height: '200px'}
      }
    },
    ysyObj: {
      type: [Object],
      default: () => {
        return {
          id: 'video-container',
          url: '',
          accessToken: ''
        }
      }
    },
    mainId: {
      type: [String],
      default: ''
    }
  },
  mounted() {
  },
  methods: {
    init(data) {
      return new Promise((resolve, reject) => {
        // this.ysyObj = data ? data : this.ysyObj;
        if (document.getElementById(data.id)) {
    			document.getElementById(data.id).remove()
    		}
        let ysyvideo = document.createElement('div')
        ysyvideo.setAttribute('id', data.id)
        ysyvideo.setAttribute('style', `width:${this.styles.width};height:${this.styles.height}`)
        var main = document.getElementById(data.mainId);
        main.style = `width:${this.styles.width};height:${this.styles.height}`;
        // main.setAttribute('style', `width:${this.styles.width};height:${this.styles.height}`)
        main.appendChild(ysyvideo);
        let w = parseInt(this.styles.width),
            h = parseInt(this.styles.height);
        this.ezuiIns = new EZUIKit.EZUIPlayer({
          autoplay: false,
          id: data.id,
          accessToken: data.accessToken,
          url: data.url,
          decoderPath: '/dist/plugin',
          width: w,
          height: h,
          // audio: 1, // 是否默认开启声音 0 - 关闭 1 - 开启
          // openSoundCallBack: data => console.log("开启声音回调", data),
          // closeSoundCallBack: data => console.log("关闭声音回调", data),
          // startSaveCallBack: data => console.log("开始录像回调", data),
          // stopSaveCallBack: data => console.log("录像回调", data),
          // capturePictureCallBack: data => console.log("截图成功回调", data),
          // fullScreenCallBack: data => console.log("全屏回调", data),
          // getOSDTimeCallBack: data => console.log("获取OSDTime回调", data),
        });
        if (this.ezuiIns) {
          setTimeout(() => {
            resolve(this.ezuiIns)
          }, 600)
        }
        this.ezuiIns.on('log', this.log())
      })

    },
    clear() {
      console.log(444 + this.ezuiIns)
  		if (this.ezuiIns) {
        console.log(3333)
  			return this.ezuiIns.stop()
  		}
  	},
  	full() {
  		this.ezuiIns.fullScreen()
  	},
    close() {
      if (this.ezuiIns) {
        this.ezuiIns.stop()
      }
      this.ezuiIns = null;
    },
    play() {
      this.ezuiIns.play()
    },
    log(str, className) {
      console.log(str)
    }
  },
  render(r, context) {
    // var w = parseInt(window.innerWidth * 0.8),
    //     h = parseInt(window.innerHeight * 0.7);
    return h('div', {
      id: this.mainId,
      style: {width: 'auto', height: 'auto', position: 'relative'}
    })
  }
}
</script>

<style scoped>

</style>
