<template>
  <div class="main">
    <Row>
    <Col
      v-for="(item,index) in videoList "
      class="con-col"
      :class="item.onlineState == 0 ? 'off':'on'"
      :key="index"
      span="6" :xxl="6" :xl="6" :lg="12" :md="12" :sm="24" :xs="24"
    >
      <div class="con" :style="styles" @mouseenter="showPerViewCamera(item,index)" @mouseleave="exitCamera(item,index)">
      <JudgeVideo
        :ref="'vid' + index"
        :video-obj="item"
        :index="index"
        :styles="styles"
        v-show="perViewObj.currentIndex === index"
        @on-flv-fail="perViewObj.currentIndex = '---'"
      />

      <div class="cover ysy-bg-img" v-show="perViewObj.currentIndex != index">
        <img v-if="item.ezopenObj.originImageKey" class="cover-img" :src="item.ezopenObj.originImageKey" />
        <p class="videoMessage">{{ item.deviceName }}</p>
      </div>
      <div
        class="action"
        v-show="perViewObj.minPerView[index]"
        :style="(item.propName != 'ezopen' && perViewObj.currentIndex==index) ? 'height: calc(100% - 65px);background: none' : '' "
      >
        <div class="play" v-show="perViewObj.currentIndex!=index" @click="videoPlay(item,index)">
          <img src="./images/play.png" style="width:36px;height:36px" />
        </div>
        <div class="play" v-show="perViewObj.currentIndex==index" @click="suspend(item,index)">
          <!-- <Icon type="md-pause" size="50" color="white" /> -->
          <img src="./images/stop.png" style="width:36px;height:36px;margin-top:30px" />
        </div>
        <div class="btn-box" v-if="item.propName=='ezopen'">
          <div class="ic" @click.stop="showModals(item,index)">
            <Icon type="ios-repeat" size="35" />
          </div>
          <div class="ic" @click.stop="showControlModals(item,index)">
            <Icon type="md-expand" size="22" />
          </div>
        </div>
        <div
          class="btn-box"
          v-if="item.propName=='live_address'"
          v-show="perViewObj.currentIndex!=index"
        >
          <div class="ic" @click.stop="showImouControlModals(item,index)">
            <Icon style="position:relative; top:2px;" type="ios-desktop" size="22" />
            <span>
              云台控制
              <span />
              <!-- <Icon type="ios-desktop" size="22" /> -->
              <!-- <Icon type="md-expand" size="22" /> -->
            </span>
          </div>
        </div>
      </div>
      </div>
    </Col>
    </Row>
    <Modal v-model="modalObj.showModal" title="回放" width="600">
      <div slot="footer">
        <Button @click="closeModal">关闭</Button>
      </div>
      <CameraModal ref="cameraModal" />
    </Modal>

    <Modal
      v-model="y_c.showModal"
      :footer-hide="true"
      :mask-closable="false"
      :destroy-on-close="true"
      width="65%"
      @on-visible-change="visibleChangeYuntai"
      class-name="video-center-modal"
    >
      <YuntaiControlModel ref="yuntaiC" />
    </Modal>
    <Modal
      v-model="y_c.showImouModal"
      :footer-hide="true"
      :mask-closable="false"
      :destroy-on-close="true"
      width="65%"
      @on-visible-change="visibleChangeYuntaiImou"
      class-name="video-center-modal"
    >
      <imouYuntaiControl ref="yuntaiImou" />
    </Modal>
  </div>
</template>
<script>
import CameraModal from './cameraModal';
import YuntaiControlModel from './yuntaiControlModel';
import imouYuntaiControl from '@/components/video/YuntaiControl';

export default {
  name: 'ysyViodeoPlayerModel',
  components: { CameraModal, YuntaiControlModel, imouYuntaiControl },
  props: {
    videoList: {
      type: [Array],
      default: () => []
    }
  },
  data() {
    return {
      styles: { width: '273px', height: '168px' },
      ezuiIns: '',
      perViewObj: {
        minPerView: [],
        currPerViewMinImg: '',
        currentIndex: '------',
        showIcon: true
      },
      modalObj: {
        showModal: false,
        dateRanges: []
      },
      y_c: {
        showModal: false,
        showImouModal: false
      },
      videoObj: {},
      mapFlag: true
    };
  },
  computed: {
    // imagesUrl() {
    //   let theme = localStorage.getItem('themeColor');
    //   if (theme == 'dark') {
    //     return require('./images/99999999999.png');
    //   } else if (theme == 'light') {
    //     return require('./images/99999999998.png');
    //   }
    // }
  },
  watch: {
    videoList: {
      immediate: true,
      deep: true,
      handler(nv) {
        let res = [];
        for (let i = 0; i < nv.length; i++) {
          res.push(false);
        }
        this.perViewObj.minPerView = res;
      }
    }
  },
  mounted() {},
  methods: {
    videoPlay(data, index) {
      this.clear(this.perViewObj.currentIndex);

      let format = ['ezopen', 'flvUrl', 'live_address'];
      if (format.indexOf(data.propName) < 0) {
        if (data.propName) {
          this.$Message.warning('不受支持的:' + data.propName + '  协议');
        } else {
          this.$Message.warning('请检查设备个性化配置');
        }
        this.perViewObj.currentIndex = '---';
        return;
      }
      this.perViewObj.currentIndex = index;
      this.$refs['vid' + index][0].open(data);
    },
    showPerViewCamera(item, index) {
      this.perViewObj.minPerView.splice(index, 1, true);
    },
    exitCamera(item, index) {
      this.perViewObj.minPerView.splice(index, 1, false);
    },
    showControlModals(data, index) {
      if (!data || data.propName != 'ezopen') {
        this.$Message.warning('云台控制不受支持的监控设备');
        return;
      }
      this.$refs.yuntaiC.init(data, index);
      this.perViewObj.currentIndex = '------';
      this.y_c.showModal = true;
    },
    visibleChangeYuntai(flag) {
      if (!flag) {
        this.$refs.yuntaiC.clear();
      }
    },
    showImouControlModals(data, index) {
      if (!data || data.propName != 'live_address') {
        this.$Message.warning('云台控制不受支持的监控设备');
        return;
      }
      this.$refs.yuntaiImou.init(data, 'cam-index');
      this.perViewObj.currentIndex = '------';
      this.y_c.showImouModal = true;
    },
    visibleChangeYuntaiImou(flag) {
      if (!flag) {
        this.$refs.yuntaiImou.clear();
      }
    },
    showModals(data, index) {
      if (!data || data.propName != 'ezopen') {
        this.$Message.warning('回放不受支持的监控设备');
        return;
      }
      this.perViewObj.currentIndex = '------';
      this.$refs.cameraModal.init(
        {
          id: 'video' + index,
          url: data.videoUrl,
          channelNoVal: data.ezopenObj.channelNoVal,
          accessToken: data.ezopenObj.accessToken
        },
        index
      );
      this.modalObj.showModal = true;
    },
    suspend(item, index) {
      this.clear(index);
      this.perViewObj.currentIndex = '------';
    },
    closeModal() {
      this.$refs['cameraModal'].clear();
      this.modalObj.showModal = false;
    },
    clear(index) {
      try {
        return this.$refs['vid' + index][0].clear();
      } catch (e) {
        console.log(e);
      }
    }
  }
};
</script>

<style scoped="scoped" lang="less">
.main {
  .con-col{
    min-width: 283px;
  }
  .con {
    margin: 0 auto 20px;
    // margin-right: 30px;
    position: relative;
    z-index: 1;
    border-radius: 8px;
    overflow: hidden;
    .cover {
      width: 100%;
      height: 100%;
      background-repeat: no-repeat;
      background-size: 100% 100%;
      .cover-img {
        width: 100%;
        height: 139px;
      }
      .videoMessage {
        position: absolute;
        bottom: 3px;
        left: 30px;
        color: white;
        font-size: 14px;
      }
      //   .name {
      //     display: flex;
      //     align-items: center;
      //     padding: 5px 10px;
      //     .onlinstatus {
      //       width: 5px;
      //       height: 5px;
      //       border-radius: 50%;
      //       margin-right: 5px;
      //     }
      //     .videoMessage {
      //       color: white;
      //       font-weight: bold;
      //       font-size: 1px;
      //     }
      //   }
    }
    .action {
      position: absolute;
      width: 100%;
      height: 100%;
      top: 0;
      left: 0;
      background: rgba(0, 0, 0, 0.5);
      display: flex;
      flex-direction: column;
      z-index: 1000;

      .play {
        width: 100%;
        flex: 1;
        display: flex;
        color: #fff;
        cursor: pointer;
        align-items: center;
        justify-content: center;
      }
      .btn-box {
        width: 100%;
        height: 35px;
        background: #fff;
        display: flex;
        .ic {
          flex: 1;
          line-height: 35px;
          text-align: center;
          cursor: pointer;
        }
      }
    }

    &.on {
      box-shadow: 1px 5px 11px 2px #646262;

      .onlinstatus {
        background: #09fafa;
      }
    }
    &.off {
      //   box-shadow: 1px 2px 11px 2px #e85454;

      .onlinstatus {
        background: #878989;
      }
    }
  }
}
</style>
