<script>
// import EZUIKit from 'ezuikit-js';
import { h } from 'vue';
export default {
	name: 'YsyModel',
	props: {
		styles: {
			type: [Object],
			default: () => {
				return { width: '200px', height: '200px', position: 'relative' };
			}
		},
		mainId: {
			type: [String],
			default: ''
		},
		controls: { default: true },
		autoplay: { default: false },
		isShowAudioBtn: { default: true }
	},
	data() {
		return {
			videoObj: {},
			fullScreen: false
		};
	},
	watch: {
		styles(newV) {
			if (newV) {
				this.reSize();
			}
		},
		isShowAudioBtn() {
			this.toggleAudioBtn()
		}
	},
	mounted() {},
	deactivated() {},
	beforeDestroy() {
	    this.clear()
	},
	methods: {
		async init(data) {
			let autoplay = this.autoplay ? 1 : 0
			if (this.ezuiIns && data.url === this.videoObj.url) {
				if (data.accessToken === this.videoObj.accessToken) {
					return new Promise((resolve, reject) => {
						resolve()
					})
				} else {
					autoplay = 1
				}
			}

			this.videoObj = data;
			await this.clear();
			return new Promise((resolve, reject) => {
				console.log(data);
				let ysyvideo = document.createElement('div');
				ysyvideo.setAttribute('id', data.id);
				let w = this.styles.width ? this.styles.width.replace('px', '') : 250;
				let h = this.styles.height ? this.styles.height.replace('px', '') : 200;
				ysyvideo.setAttribute('style', `width:${w};height:${h};position: relative;`);
				document.getElementById(this.mainId).appendChild(ysyvideo);
				console.log(this.styles);
				this.toggleAudioBtn()
				this.ezuiIns = new EZUIKit.EZUIKitPlayer({
					id: data.id,
					accessToken: data.accessToken,
					url: data.url,
					width: w,
					height: h,
					autoplay: autoplay,
					plugin: [],
					// template: 'standard', // 播放器模板，可以通过选定模板，使用内置的播放器样式，组件 simple：极简版;standard：标准版;security：安防版(预览回放);vioce：语音版
					themeData: {
						poster: data.poster,
						header: {
							color: '#00E5FF',
							activeColor: '#FFFFFF',
							backgroundColor: '#000000',
							btnList: [
								// {
								//   iconId: 'deviceID',
								//   part: 'left',
								//   defaultActive: 1,
								//   memo: '顶部设备名称',
								//   isrender: 1
								// }
							]
						},
						footer: {
							color: '#FFFFFF',
							activeColor: '#00E5FF',
							backgroundColor: '#00000021',
							btnList: [
								{
									iconId: 'play',
									part: 'left',
									defaultActive: this.autoplay ? 1 : 0,
									memo: '播放',
									isrender: 1
								},
								{
									iconId: 'sound',
									part: 'left',
									defaultActive: 0,
									memo: '声音按钮',
									isrender: 1
								},
								{
								  iconId: 'expend',
								  part: 'right',
								  defaultActive: 0,
								  memo: '全局全屏按钮',
								  isrender: 1
								}
							]
						}
					},
					handleSuccess: (res) => {
						console.log('播放成功回调', res);
					},
					handleError: (res) => {
					},
					fullScreenCallBack: (res) => {
						console.log('全屏', Date.now());
						this.fullScreen = true;
					}
					// audio: 1, // 是否默认开启声音 0 - 关闭 1 - 开启
					// openSoundCallBack: data => console.log("开启声音回调", data),
					// closeSoundCallBack: data => console.log("关闭声音回调", data),
					// startSaveCallBack: data => console.log("开始录像回调", data),
					// stopSaveCallBack: data => console.log("录像回调", data),
					// capturePictureCallBack: data => console.log("截图成功回调", data),
					// fullScreenCallBack: data => console.log("全屏回调", data),
					// getOSDTimeCallBack: data => console.log("获取OSDTime回调", data),
				});

				if (this.ezuiIns) {
					setTimeout(() => {
						console.log(this.ezuiIns);
						resolve(this.ezuiIns);
					}, 600);
				}

				// this.ezuiIns.on('log', this.log())
			});
		},
		async clear() {
			console.log('clear ysy video');

			if (this.ezuiIns) {
				try {
					await this.ezuiIns.stop().then(() => {
						this.clearHtml();
					});
				} catch (e) {
					// statements
					this.clearHtml();
				}

			}
		},
		toggleAudioBtn() {
			if (!this.isShowAudioBtn) {
				document.getElementById(this.mainId).className = 'hide-audio-btn'
			} else {
				document.getElementById(this.mainId).className = ''
			}
		},
		clearHtml() {
			this.ezuiIns = null;
			console.log('clear');
			// document.getElementById(this.videoObj.id).remove()
			if (this.mainId && document.getElementById(this.mainId)) {
				document.getElementById(this.mainId).innerHTML = '';
			}
		},
		reSize() {
			if (this.ezuiIns) {
				let main = document.getElementById(this.mainId);
				let w = this.styles.width ? this.styles.width.replace('px', '') : 250;
				let h = this.styles.height ? this.styles.height.replace('px', '') : 200;
				if (this.ezuiIns.width != w || this.ezuiIns.height != h) {
					if (!document.fullscreen) {
						console.log('当前全屏', this.fullScreen);
						this.ezuiIns.reSize(w, h);
					}
				}
			}
		},
		play() {
			if (this.ezuiIns) {
				this.ezuiIns.play();
			}
		},
		capturePicture(name) {
			if (this.ezuiIns) {
				this.ezuiIns.capturePicture(name);
			}
		},
		setVolumeVideo(volume) {
			if (this.ezuiIns) {
				this.ezuiIns.volume = volume;
			}
		},
		log(str, className) {
			console.log(str);
		},
		full() {
			if (this.ezuiIns) {
				this.ezuiIns.fullScreen();
			}
		}
	},
	render(r, context) {
		return h(
			'div',
			{
				style: this.styles,
				class: 'ysy-video',
				id: this.mainId
			},
			[]
		);
	}
};
</script>

<style lang="less" scoped>
/deep/.loading-container {
	/* right: 0;
	  margin: auto;
	  white-space: nowrap; */
	width: 100% !important;
	height: 100% !important;
	.loading-item {
		width: 100% !important;
		height: 100% !important;
	}
}
.ysy-video{
	/deep/.ez-iframe-footer-container{
		display: none !important;
	}
}
.ysy-video:hover{
	/deep/.ez-iframe-footer-container{
		display: flex !important;
	}
}
.hide-audio-btn{
	/deep/.ez-iframe-footer-container{
		#video1-sound{
			display: none;
		}
	}
}
</style>
