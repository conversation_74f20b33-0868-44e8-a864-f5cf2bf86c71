<template>
  <div>
    <Card dis-hover>
      <div class="search-box">
        <Form :model="searchObj" :label-width="80" label-position="right">
          <Row :gutter="15">
            <Col span="7">
              <FormItem label="设备名称" :label-width="70">
                <Input v-model="searchObj.deviceName" placeholder="请输入" />
              </FormItem>
            </Col>
            <Col span="7">
              <FormItem label="抓拍时间">
                <DatePicker
                  type="datetimerange"
                  v-model="t_p.curRange"
                  @on-change="changeTime"
                  :options="t_p.timeRangeOption"
                  placeholder="选择时间"
                  style="width: 100%"
                ></DatePicker>
              </FormItem>
            </Col>
            <Col span="7" offset="1" >
              <Button @click="resetSearch">重置</Button>
              <Button type="primary" @click="getData">搜索</Button>
            </Col>
          </Row>
        </Form>
      </div>
    </Card>

    <Card dis-hover>
      <Row>
        <Col span="12">
          <TitleNmae title="抓拍图像列表" />
        </Col>
        <Col span="12" style="text-align: right;">
          <Button type="primary" @click="batchDownloadZip">批量下载</Button>
        </Col>
      </Row>
      <div>
        <Row class="list">
          <Col v-for="(item, index) in imgList" :key="index" span="6" class="col-con">
            <div class="box video-card-bg">
              <Checkbox v-model="item.checked" class="choose"></Checkbox>
              <img :src="item.fileHttpUrl" class="img" @click="setCurrentImgUri(item)" />
              <div class="item" @click="changeCheck(item, index)">
                <div class="name  @title-color">{{ item.deviceName }}</div>
                <div class="time  @text-color">
                  {{ item.captureTime }}
                  <a
                    href="javascript:void(0)"
                    @click.stop="oneImgDownload(item)"
                    class="down"
                  >下载</a>
                </div>
              </div>
            </div>
          </Col>
        </Row>
        <PageModule
          ref="pageInstance"
          style="padding-bottom: 10px;"
          :default-page-size="12"
          :page-transfer="true"
          :page-opts="[12, 24, 48, 96]"
          url="/imageCapture/getPage"
          @resultData="setImgList"
          @on-change-page="changeDataPage"
        />
      </div>
    </Card>

    <MaxImgPerView
      :styles="{top:'1%',margin:'10px auto'}"
      @imgFullPreviewFlag="setImgFullPreviewFlag"
      :current-img-uri="m_i.currentImgUri"
      :img-full-preview-flag="m_i.imgFullPreviewFlag"
    />
  </div>
</template>

<script>
import Util from '@/libs/util';
import MaxImgPerView from '../faceId/imgComponents/maxImgPerView';
const defaultSearchObj = {
  deviceName: '',
  queryTimeStart: '',
  queryTimeEnd: ''
};
export default {
  name: 'headSnapped',
  components: {
    MaxImgPerView
  },
  data() {
    let timeRangeOption = Util.timeRangeOption;
    timeRangeOption.disabledDate = (date) => {
      let start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 91);
      return (date && date.valueOf() > Date.now()) || date.valueOf() < start.valueOf();
    };
    return {
      searchObj: Util.objClone(defaultSearchObj),
      t_p: {
        timeRangeOption: timeRangeOption,
        curRange: []
      },
      columns: [
        { type: 'selection', width: 50, align: 'center' },
        { title: '设备名称', key: 'deviceName', tooltip: true },
        { title: '文件名称', key: 'fileName', tooltip: true },
        { title: '抓拍时间', key: 'captureTime', width: 135, align: 'center' },
        { title: '文件大小', key: 'fileSize', align: 'center', minWidth: 150, maxWidth: 200 },
        { title: '操作', width: 100, slot: 'action', align: 'center' }
      ],
      m_i: {
        currentImgUri: '',
        imgFullPreviewFlag: false
      },
      imgList: [],
      s_page: {}
    };
  },
  mounted() {
    this.setDefaultTime();
    this.getData();
  },
  methods: {
    setDefaultTime() {
      let arr = Util.getDateRangeS(new Date(), 'nd', 'yyyy-MM-dd hh:mm:ss');
      this.searchObj.queryTimeStart = arr[0];
      this.searchObj.queryTimeEnd = arr[1];
      this.t_p.curRange = arr;
    },
    setImgList(data) {
      this.imgList = data.map((item) => {
        item.checked = false;
        return item;
      });
    },
    getData() {
      this.$refs.pageInstance.search(this.searchObj);
      // this.$refs.imgTb.search(this.searchObj)
    },
    resetSearch() {
      this.searchObj = Util.objClone(defaultSearchObj);
      this.t_p.curRange = [];
      this.setDefaultTime();
      this.getData();
    },
    changeCheck(row, index) {
      row.checked = !row.checked;
      this.$set(this.imgList, index, row);
    },
    changeDataPage(curPage) {
      this.s_page = curPage;
    },
    batchDownloadZip() {
      var obj = {};
      var list = this.getSelectionImg();
      let fileUrlList = [];
      if (this.$_.isEmpty(list)) {
        obj = Util.objClone(this.searchObj);
        obj.page = this.s_page;
      } else {
        list.forEach((item) => {
          fileUrlList.push(item.fileHttpUrl);
        });
        obj.fileUrlList = fileUrlList;
      }
      Util.request('/imageCapture/batchDownloadZip', obj, 'post', {}, 'blob').then((resp) => {
        console.log(resp.data.text());
        if (resp.data.size < 65) {
          resp.data.text().then((r) => {
            this.$Message.info(r);
          });
          return;
        }
        let blob = new Blob([resp.data], { type: 'application/octet-stream' });
        let href = window.URL.createObjectURL(blob);
        let time = Util.formatDate(new Date(), 'yyyyMMddhhmmss');
        let name = `抓拍图片_${time}.zip`;
        Util.download(href, name);
      });
    },
    oneImgDownload(row) {
      let fileHttpUrl = row.fileHttpUrl;
      if (!fileHttpUrl) {
        this.$Modal.warning({
          title: '提示',
          content: '没有图片'
        });
        return;
      } else {
        //   console.log(1)
      }
      let fileName = row.fileName;
      if (!fileName) {
        let time = Util.formatDate(new Date(), 'yyyyMMddhhmmss');
        fileName = `抓拍图片_${time}.jpg`;
      }
      var image = new Image();
      image.setAttribute('crossOrigin', 'anonymous');
      image.onload = () => {
        var canvas = document.createElement('canvas');
        canvas.width = image.width;
        canvas.height = image.height;
        var context = canvas.getContext('2d');
        context.drawImage(image, 0, 0, image.width, image.height);
        var url = canvas.toDataURL('image/jpeg');
        Util.download(url, fileName);
      };
      image.src = fileHttpUrl;
    },
    getSelectionImg() {
      return this.imgList.filter((item) => {
        return item.checked;
      });
    },
    setImgFullPreviewFlag() {
      this.m_i.imgFullPreviewFlag = false;
      this.m_i.currentImgUri = '';
    },
    setCurrentImgUri(row) {
      this.m_i.imgFullPreviewFlag = true;
      this.m_i.currentImgUri = row.fileHttpUrl;
    },
    changeTime(val) {
      this.searchObj.queryTimeStart = val[0];
      if (val[1].indexOf('00:00:00') >= 0) {
        this.searchObj.queryTimeEnd = Util.formatDate(val[1], 'yyyy-MM-dd') + ' 23:59:59';
        this.$set(this.t_p.curRange, 1, this.searchObj.queryTimeEnd);
      } else {
        this.searchObj.queryTimeEnd = val[1];
      }
    }
  }
};
</script>

<style lang="less" scoped>
.ivu-card {
  margin-bottom: 15px;
  .search-box {
    margin-bottom: -16px;
    .ivu-select,
    .ivu-input-wrapper {
      width: 90%;
    }
  }

  .list {
    align-items: stretch;
    padding: 10px;
    .col-con {
      padding: 10px;
    }
    .box {
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      position: relative;
      .choose {
        position: absolute;
        top: 5px;
        left: 5px;
      }
      img {
        display: block;
        width: 100%;
        height: auto;
      }
      .item {
        padding: 5px 10px;
        line-height: 24px;
        width: 100%;
        .time,
        .name {
          position: relative;
          padding-right: 30px;
        }
        .down {
          position: absolute;
          right: 0;
          top: 0;
          font-size: 13px;
        }
      }
    }
  }
}
</style>
