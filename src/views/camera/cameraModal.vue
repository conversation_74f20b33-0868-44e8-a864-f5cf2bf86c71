<template>
	<div>
		<Row>
			<Col span="16">
				<DatePicker type="datetimerange"
							placement="bottom-end"
							:options="dateRangeOpt"
							v-model="dateRanges"
							format="yyyy-MM-dd HH:mm:ss"
							placeholder="请选择回放时间" style="width:100%">
				</DatePicker>
			</Col>
			<Col span="4" push="1">
				<Button @click="checkedDateRangesPlay">确定</Button>
			</Col>
			<Col span="4">
				<Button @click="videoFull">全屏</Button>
			</Col>
		</Row>
		<div style="padding: 10px">
			<YsyModel main-id="ysyModealId" ref="ysmM" :styles="{width:'550px',height:'400px',position: 'relative'}"/>
		</div>
	</div>
</template>
<script>
import YsyModel from "./ysyModel";

export default {
	name: "cameraModal",
	components: {
		YsyModel
	},
	props: {
		index: {
			type: [String],
			default: ''
		}
	},
	data() {
		return {
			dateRanges: [],
			dateRangeOpt: {
				disabledDate(date) {
					return date && date.valueOf() > Date.now();
				}
			},
			ysyObj: {}
		}
	},
	methods: {
		init(data, index) {
			data.mainId = 'ysyModealId'
			this.$refs.ysmM.clear();
			this.ysyObj = data;
			this.$refs.ysmM.init(data).then(re => {
				re.play();
			})
		},
		checkedDateRangesPlay() {
			if (!this.dateRanges || (!this.dateRanges[0] || !this.dateRanges[1])) {
				this.$Message.warning('请选择时间')
				return;
			}
			let cn = this.ysyObj.channelNoVal
			let dataRange = 'begin=' + this.formatDate(this.dateRanges[0]) + '&end=' +
				this.formatDate(this.dateRanges[1])
			this.ysyObj.url = this.ysyObj.url.substring(0, this.ysyObj.url.lastIndexOf('/') + 1) + cn + '.rec?' + dataRange;
			this.$refs.ysmM.clear();
			this.$refs.ysmM.init(this.ysyObj).then(re => {
				re.play();
			})

		},
		formatDate(d) {
			let date = new Date(d)
			let p = [
				{"y": date.getFullYear()},//年
				{"M": (date.getMonth() + 1).toString().padStart(2, '0')},//月
				{"d": date.getDate().toString().padStart(2, '0')},//日
				{"H": date.getHours().toString().padStart(2, '0')},//小时
				{"m": date.getMinutes().toString().padStart(2, '0')},//fen
				{"s": date.getSeconds().toString().padStart(2, '0')}//秒
			]
			let r = [];
			Object.keys(p).forEach(key => {
				r.push(Object.values(p[key]))
			})
			return r.join('');
		},
		clear() {
			this.dateRanges = []
			this.$refs.ysmM.clear();
		},
		videoFull() {
			this.$refs.ysmM.full();
		}
	}
}
</script>

<style scoped>

</style>
