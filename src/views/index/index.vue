<template>
    <div class="home-container">
        <titleCard :applicationId="applicationId" />
        <Row :gutter="8" style="margin-bottom: 8px;">
            <Col style="width: 54%">
                <onlineCard :applicationId="applicationId" />
            </Col>
            <Col style="width: 29%;">
                <deviceClassify :applicationId="applicationId" />
            </Col>
            <Col style="flex: 1">
                <ContentCard title="快捷功能">
                    <div class="menu-list" style="height: 304px;">
                        <div v-for="item in shortcutMenu" @click="jumpMenu(item)" :key="item.code" class="box">
                            <div class="icon-box">
                                <span class="icon iconfont" :class="[item.icon]"></span>
                            </div>
                            <div class="name">{{ item.title }}</div>
                        </div>
                    </div>
                </ContentCard>
            </Col>
        </Row>
        <Row :gutter="8" style="margin-bottom: 8px;">
            <Col style="width: 31%">
                <alarmWorkOrder :applicationId="applicationId" />
            </Col>
            <Col style="flex: 1;">
                <alarmLog :applicationId="applicationId" />
            </Col>
            <Col style="width: 30%">
                <alarmRank :applicationId="applicationId" />
            </Col>
        </Row>
    </div>
</template>

<script>
import titleCard from './components/titleCard'
import onlineCard from './components/onlineCard'
import deviceClassify from './components/deviceClassify'
import alarmWorkOrder from './components/alarmWorkOrder'
import alarmLog from './components/alarmLog'
import alarmRank from './components/alarmRank'
import { shortcutFunction } from './data'
import { mapGetters } from 'vuex';
export default {
    name: 'index',
    props: {
    },
    components: {
        titleCard,
        onlineCard,
        deviceClassify,
        alarmWorkOrder,
        alarmLog,
        alarmRank
    },
    data() {
        return {
        }
    },
    computed: {
        ...mapGetters(['permission_routes', 'pageName', 'applicationId']),
        shortcutMenu() {
            if (!this.pageName) {
                return []
            }
            return shortcutFunction[this.pageName]
        }
    },
    methods: {
        // 快捷功能
        jumpMenu(item) {
            let menu = this.permission_routes.find(k => k.code === item.code)
            if (menu) {
                let first = this.getFirstMenu(menu)
                console.log(first)
                if (first.url) {
                    this.$router.push(first.url)
                    return
                }
            }
            this.$Message.warning('暂无权限')
        },
        getFirstMenu(menu) {
            if (menu.children && menu.children.length > 0) {
                return this.getFirstMenu(menu.children[0])
            }
            return menu
        }
    }
}
</script>

<style lang="less" scoped>
.home-container{
    padding-top: 8px;
    .title-box{}
    .menu-list{
        display: flex;
        flex-wrap: wrap;
        padding-top: 8px;
        .box{
            width: 50%;
            text-align: center;
            padding: 12px 0;
            margin-bottom: 16px;
            cursor: pointer;
            .icon-box{
                width: 36px;
                height: 36px;
                border-radius: 6px;
                display: flex;
                background: @fill-1;
                color: @text-color;
                font-size: 18px;
                margin: 0 auto;
                .icon{
                    margin: auto;

                }
            }
            .name{
                font-size: 12px;
                line-height: 20px;
                color: @title-color;
            }
            &:hover{
                .icon-box{
                    background: #E8F3FF;
                }
            }
            &:active{
                .icon-box{
                    background: #E8F3FF;
                    color: @primary-color;
                }
            }
        }
    }
}
</style>
