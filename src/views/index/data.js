// 快捷功能
export const shortcutFunction = {
    livable: [
        { title: '自动灌溉', code: 'autoIrrigate', icon: 'icon-guangai' },
        { title: '垃圾运收管理', code: 'garbageCollectionManagement', icon: 'icon-shouyun' },
        { title: '电子围栏', code: 'electronicFence', icon: 'icon-dian<PERSON><PERSON>lan' },
        { title: '智慧公厕', code: 'smartPublicToilet', icon: 'icon-gongce' },
        { title: '环境监测', code: 'environmentMonitoring', icon: 'icon-huanjing' },
        { title: '统计分析', code: 'statisticalAnalysis', icon: 'icon-tongji' }
    ],
    traffic: [
        { title: '智慧畅行', code: 'wisdomSmoothness', icon: 'icon-cheliang' },
        { title: '智慧停车', code: 'intelligentParking', icon: 'icon-tingche2' },
        { title: '智慧公交站', code: 'smartBusStation', icon: 'icon-gongjiaozhan' },
        { title: '智慧出行', code: 'smartTravel', icon: 'icon-chuhang' },
        { title: '智慧路灯', code: 'smartStreetLamp', icon: 'icon-ludeng' },
        { title: '统计分析', code: 'statisticalAnalysis', icon: 'icon-tongji' }
    ],
    safe: [
        { title: '排水控制管理', code: 'drainageControl', icon: 'icon-bengzhan' },
        { title: '布控管理', code: 'controlManagement', icon: 'icon-bukong' },
        { title: '智慧管网', code: 'smartNetwork', icon: 'icon-guanwang' },
        { title: '应急处置管理', code: 'emergencyResponse', icon: 'icon-wuzi' },
        { title: '能源能耗', code: 'consumption', icon: 'icon-guangai' },
        { title: '统计分析', code: 'statisticalAnalysis', icon: 'icon-tongji' }
    ]
}

export const titleList = [
    {
        title: '设备总数',
        img: require('@/assets/images/icon_home1.png'),
        key: 'deviceOffonlineInfo',
        value: 'total',
        unit: '个',
        children: [
            { name: '在线', key: 'onlineNum', unit: '' },
            { name: '离线', key: 'offlineNum', unit: '' },
            { name: '在线率', key: 'onlineRate', unit: '%', noFmt: true }
        ]
    },
    {
        title: '告警总数',
        img: require('@/assets/images/icon_home2.png'),
        key: 'alarmCountInfo',
        value: 'alarmCount',
        unit: '个',
        children: [
            { name: '昨日新增', key: 'alarmCountYesterday', unit: '' },
            { name: '本月新增', key: 'alarmCountMonth', unit: '' }
        ]
    },
    {
        title: '告警规则总数',
        img: require('@/assets/images/icon_home3.png'),
        key: 'alarmRuleCountInfo',
        value: 'alarmRuleCount',
        unit: '个',
        children: [
            { name: '昨日新增', key: 'alarmRuleCountYesterday', unit: '' },
            { name: '本月新增', key: 'alarmRuleCountMonth', unit: '' }
        ]
    },
    {
        title: '用户总数',
        img: require('@/assets/images/icon_home4.png'),
        key: 'userCountInfo',
        value: 'userCount',
        unit: '个',
        children: [
            { name: '昨日新增', key: 'userCountYesterday', unit: '' },
            { name: '本月新增', key: 'userCountMonth', unit: '' }
        ]
    }
]
