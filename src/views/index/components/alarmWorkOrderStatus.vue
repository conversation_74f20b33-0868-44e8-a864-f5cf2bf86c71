<template>
    <s-tag v-if="item && item.color" :color="item.color" :background="hexToRgba(item.color, 0.1)">{{ item.name }}</s-tag>

</template>

<script>
import { hexToRgba } from '@/utils/tool'
import { enumeration } from '@/config/enumeration'
export default {
    name: 'alarmWorkOrderStatus',
    props: {
        value: { default: '' }
    },
    data() {
        return {}
    },
    computed: {
        item() {
            return enumeration.alarmWorkOrderStatus[this.value]
        }
    },
    methods: {
        hexToRgba
    }
}
</script>

<style lang="less" scoped>

</style>
