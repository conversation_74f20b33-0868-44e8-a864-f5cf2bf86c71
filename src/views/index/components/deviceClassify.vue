<template>
    <ContentCard>
        <EchartItem ref="chartPie" :option="options" @initEchart="loadChart" style="height: 336px" />
    </ContentCard>
</template>

<script>
import EchartItem from "@/components/common/EchartItem";
import { pieAutoAnimation } from '@/utils/mixins'
export default {
    name: 'deviceClassify',
    props: {
        applicationId: { default: '' }
    },
    components: {
        EchartItem
    },
    mixins: [pieAutoAnimation],
    data() {
        return {
            options: {
                title: {
                    text: '设备分类',
                    textStyle: {
                        fontSize: 16,
                        color: '#1E2A55'
                    }
                },
                tooltip: {
                    trigger: 'item',
                    formatter: (params) => {
                        let html = `<div>${params.name}</div>`
                        html += `<div>${params.marker}${params.value}个</div>`
                        html += `<div>${params.marker}${params.percent}%</div>`
                        return html
                    }
                },
                legend: {
                    type: 'scroll',
                    bottom: 0,
                    icon: 'circle',
                    itemWidth: 10,
                    itemHeight: 10,
                    textStyle: {
                        color: '#4E627E'
                    },
                    pageIcons: {
                        horizontal: ['M0,0L12,12L12,9L3,0,L12,-9L12,-12z', 'M0,0L-12,12L-12,9L-3,0,L-12,-9L-12,-12z']
                    },
                    pageIconSize: 12,
                    pageIconInactiveColor: '#C2C6CE',
                    pageIconColor: '#4E627E',
                    pageTextStyle: {
                        color: '#1E2A55'
                    },
                    pageFormatter: () => {
                        return `${this.currentHighlightIndex + 1} / ${this.dataLen}`
                    }
                },
                color: ['#249EFF', '#9391FF', '#FE7B32', '#21CCFF', '#86DF6C', '#0E42D2'],
                series: [
                    {
                        name: '设备分类',
                        type: 'pie',
                        center: ['50%', '48%'],
                        radius: ['45%', '65%'],
                        legendHoverLink: false,
                        avoidLabelOverlap: false,
                        itemStyle: {
                            borderColor: '#fff',
                            borderWidth: 2,
                            color: (params) => {
                                return this.options.color[params.dataIndex % this.options.color.length]
                            }
                        },
                        label: {
                            show: false,
                            position: 'center',
                            formatter: ['{per|{d}%}', '{def|{b}}', '{num|{c}个}'].join('\n'),
                            rich: {
                                per: {
                                    fontSize: 24,
                                    fontWeight: 'bold',
                                    lineHeight: 32,
                                    textBorderColor: 'none',
                                    color: '#1E2A55'
                                },
                                def: {
                                    fontSize: 12,
                                    lineHeight: 22,
                                    textBorderColor: 'none',
                                    color: '#4E627E'
                                },
                                num: {
                                    fontSize: 14,
                                    lineHeight: 24,
                                    textBorderColor: 'none',
                                    color: '#4E627E'
                                }
                            }
                        },
                        emphasis: {
                            label: {
                                show: true
                            }
                        },
                        data: []
                    }
                ]
            }
        }
    },
    watch: {
        applicationId(id) {
            if (id) {
                this.getData()
            }
        }
    },
    created() {
        if (this.applicationId) {
            this.getData()
        }
    },
    methods: {
        getData() {
            let param = {
                baseApplication: {
                    id: this.applicationId
                }
            }
            this.clearHighlightText()
            this.$request('/sysHomePage/typeDeviceCount', param, 'post').then(res => {
                let list = []
                res.data.forEach(item => {
                    list.push({
                        name: item.typeName,
                        value: item.num
                    })
                })
                this.options.series[0].data = list
                this.dataLen = list.length
                this.currentHighlightIndex = 0
                this.initChartAnim()
            })
        }
    }
}
</script>

<style lang="less" scoped>

</style>
