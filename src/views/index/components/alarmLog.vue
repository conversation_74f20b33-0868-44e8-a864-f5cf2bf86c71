<template>
    <ContentCard title="最新告警日志" class="alarm-card">
        <swiper
            direction="vertical"
            :modules="modules"
            :slides-per-view="4.5"
            :autoplay="autoplay"
            @swiper="onSwiper"
            @slideChange="onSlideChange"
            style="height: 432px"
            class="alarm-swiper"
            v-if="alarmList.length > 0"
            @mouseenter.native="swiperStop"
            @mouseleave.native="swiperStart"
        >
            <swiper-slide v-for="(item, index) in alarmList">
                <div class="alarm-item" :class="detailInfo.index === index ? 'on':''">
                    <div class="info" @click.stop="chooseAlarm(item, index)" v-if="item.alarm">
                        <div class="top-box">
                            <div class="name">
                                <TooltipAutoShow :content="item.deviceExtendInfo && item.deviceExtendInfo.sbmc" />
                            </div>
                            <s-tag color="rgba(22, 93, 255, 1)" background="rgba(64, 128, 255, 0.1)">{{ item.alarm.alarmTypeName }}</s-tag>
                        </div>
                        <div class="code">{{ item.deviceExtendInfo && item.deviceExtendInfo.bsm }}</div>
                        <div class="bot-box">
                            <div class="time">{{item.alarm.alarmTime}}</div>
                            <alarmWorkOrderStatus v-if="item.eventProcess" :value="item.eventProcess.processStatus" />
                        </div>
                    </div>
                </div>
            </swiper-slide>
        </swiper>
        <no-data v-else style="height: 432px" />
        <SideDetail :show="showSideDetail" :data="detailInfo.info" :modelId="detailInfo.modelId" @on-cancel="closeSide"></SideDetail>
    </ContentCard>
</template>

<script>
import { ref, watch, getCurrentInstance } from 'vue'
import { Autoplay } from 'swiper';
import { SwiperSlide } from 'swiper/vue/swiper-slide';
import { Swiper } from 'swiper/vue/swiper';

import SideDetail from '@/components/global/SideDetail'
import alarmWorkOrderStatus from './alarmWorkOrderStatus'
import 'swiper/swiper.css';
export default {
    name: 'alarmLog',
    props: {
        applicationId: { default: '' }
    },
    components: {
        Swiper,
        SwiperSlide,
        SideDetail,
        alarmWorkOrderStatus
    },
    setup(props) {
        const that = getCurrentInstance()?.appContext.config.globalProperties
        const autoplay = ref({
            delay: 5000
        })
        const swiperMy = ref(null)
        const onSwiper = (swiper) => {
            swiperMy.value = swiper
        };
        const alarmList = ref([])
        const onSlideChange = () => {
        };
        const swiperStop = () => {
            if (swiperMy.value) {
                swiperMy.value.autoplay.stop()
            }
        }
        const swiperStart = () => {
            if (swiperMy.value && !showSideDetail.value) {
                swiperMy.value.autoplay.start()
            }
        }
        const searchObj = ref({
            customQueryParams: {
                baseApplication: {
                    id: props.applicationId
                },
                startTime: '',
                endTime: ''
            },
            page: {
                current: 1,
                size: 20
            }
        })

        const getData = () => {
            if (!props.applicationId) {
                return
            }
            that.$request('/sysHomePage/alarmTopList', searchObj.value, 'post').then(res => {
                if (res.success) {
                    alarmList.value = res.data || []
                }
            })
        }
        getData()
        watch(props.applicationId, () => {
            getData()
        })
        const chooseAlarm = (item, index) => {
            showSideDetail.value = true
            let info = {
                code: item.alarm?.code,
                bsm: item.deviceExtendInfo?.bsm,
                deviceCode: item.alarm?.deviceCode,
                sbmc: item.deviceExtendInfo?.sbmc,
                alarmType: item.alarm?.alarmType,
                level: item.alarm?.level,
                content: item.alarm?.content,
                pushStatus: item.alarm?.pushStatus,
                areaPath: (item.deviceExtendInfo?.areaPath || '').replace('&', '/'),
                alarmTime: item.alarm?.alarmTime
            }
            detailInfo.value.info = info
            detailInfo.value.modelId = parseInt(item.alarm?.model)
            detailInfo.value.index = index
            swiperStop()
        }
        const showSideDetail = ref(false)
        const detailInfo = ref({
            info: {},
            modelId: null,
            index: null
        })
        const closeSide = () => {
            showSideDetail.value = false
            detailInfo.value.index = null
            swiperStart()
        }
        return {
            swiperMy,
            autoplay,
            onSwiper,
            onSlideChange,
            swiperStop,
            swiperStart,
            alarmList,
            modules: [Autoplay],
            showSideDetail,
            detailInfo,
            closeSide,
            chooseAlarm
        };
    },
}
</script>

<style lang="less" scoped>
.alarm-card{
    padding: 16px 0;
    /deep/.ivu-typography{
        padding-left: 24px;
    }
}
.no-data{
    padding-top: 100px;
}
.alarm-item{
    padding: 0 24px;
    cursor: pointer;
    .info{
        border-bottom: 1px solid @line-2;
        padding: 8px 0;
    }
    .top-box{
        display: flex;
        align-items: center;
        height: 32px;
        margin-bottom: 4px;
        .name{
            flex: 1;
            overflow: hidden;
        }
    }
    .code{
        line-height: 22px;
    }
    .bot-box{
        display: flex;
        align-items: center;
        justify-content: space-between;
        .time{
            color: rgba(121, 135, 153, 1);
            font-size: 12px;
            line-height: 20px;
        }
    }
    &.on,
    &:hover{
        background: @fill-2;
        /deep/.ivu-tooltip-rel,
        .code,
        .bot-box .time{
            color: @primary-color;
        }
    }
}
.alarm-swiper{

}
</style>
