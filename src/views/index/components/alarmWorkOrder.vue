<template>
    <ContentCard>
        <div class="top-right">
            <dateSelect @on-change="changeDate" :hideDate="true" placement="bottom-start" />
        </div>
        <EchartItem ref="chartPie" :option="options" @initEchart="loadChart" style="height: 464px" />
    </ContentCard>
</template>

<script>
import EchartItem from "@/components/common/EchartItem";
import { pieAutoAnimation } from '@/utils/mixins'
import dateSelect from '@/components/common/dateSelect/index'
import { enumeration } from '@/config/enumeration'
export default {
    name: 'alarmWorkOrder',
    props: {
        applicationId: { default: '' }
    },
    components: {
        EchartItem,
        dateSelect
    },
    mixins: [pieAutoAnimation],
    data() {
        return {
            options: {
                title: {
                    text: '告警工单状态',
                    textStyle: {
                        fontSize: 16,
                        color: '#1E2A55'
                    }
                },
                tooltip: {
                    trigger: 'item',
                    formatter: (params) => {
                        let html = `<div>${params.name}</div>`
                        html += `<div>${params.marker}${params.value}个</div>`
                        html += `<div>${params.marker}${params.percent}%</div>`
                        return html
                    }
                },
                legend: {
                    show: true,
                    bottom: 0,
                    icon: 'circle',
                    itemWidth: 10,
                    itemHeight: 10,
                    formatter: '{len|{name}}',
                    textStyle: {
                        color: '#4E627E',

                        rich: {
                            len: {
                                width: 200,
                            }
                        }
                    }
                },
                color: ['#249EFF', '#9391FF', '#FE7B32', '#21CCFF', '#86DF6C', '#0E42D2'],
                series: [
                    {
                        name: '告警工单状态',
                        type: 'pie',
                        center: ['50%', '46%'],
                        radius: ['40%', '60%'],
                        avoidLabelOverlap: false,
                        legendHoverLink: false,
                        itemStyle: {
                            borderColor: '#fff',
                            borderWidth: 2
                        },
                        label: {
                            show: false,
                            position: 'center',
                            formatter: ['{per|{d}%}', '{def|{b}}', '{num|{c}个}'].join('\n'),
                            rich: {
                                per: {
                                    fontSize: 24,
                                    fontWeight: 'bold',
                                    lineHeight: 32,
                                    textBorderColor: 'none',
                                    color: '#1E2A55'
                                },
                                def: {
                                    fontSize: 12,
                                    lineHeight: 22,
                                    textBorderColor: 'none',
                                    color: '#4E627E'
                                },
                                num: {
                                    fontSize: 14,
                                    lineHeight: 24,
                                    textBorderColor: 'none',
                                    color: '#4E627E'
                                }
                            }
                        },
                        emphasis: {
                            label: {
                                show: true
                            }
                        },
                        emptyCircleStyle: {
                            color: '#F3F7FB'
                        },
                        z: 5,
                        data: []
                    },
                    {
                        name: '告警工单状态',
                        type: 'pie',
                        center: ['50%', '46%'],
                        radius: ['40%', '60%'],
                        avoidLabelOverlap: false,
                        legendHoverLink: false,
                        silent: true,
                        itemStyle: {
                            borderColor: '#fff',
                            borderWidth: 2
                        },
                        label: {
                            show: true,
                            position: 'outside',
                            formatter: '{d}%'
                        },
                        labelLine: {
                            show: true,
                            length: 10,
                            length2: 10
                        },
                        data: []
                    }
                ]
            },
            searchObj: {
                baseApplication: {
                    id: ''
                },
                startTime: '',
                endTime: ''
            }
        }
    },
    watch: {
        applicationId(id) {
            if (id) {
                this.getData()
            }
        }
    },
    created() {
        if (this.applicationId) {
            this.getData()
        }
    },
    methods: {
        changeDate(arr) {
            this.searchObj.startTime = arr[0]
            this.searchObj.endTime = arr[1]
            this.getData()
        },
        getData() {
            if (!this.searchObj.startTime || !this.applicationId) {
                return
            }
            this.searchObj.baseApplication.id = this.applicationId
            this.clearHighlightText()
            this.$request('/sysHomePage/alarmWorkOrderPieChart', this.searchObj, 'post').then(res => {
                if (res.success && res.data && res.data.length > 0) {
                    let list = []
                    let total = 0
                    res.data.forEach(item => {
                        let key = parseInt(item.key)
                        let value = item.value || 0
                        list.push({
                            name: item.name,
                            value: value,
                            itemStyle: {
                                color: enumeration.alarmWorkOrderStatus[key].color
                            }
                        })
                        total += value
                    })
                    if (total === 0) {
                        this.emptyCircleStyle()
                    } else {
                        if (list.length > 15) {
                            this.options.legend.type = 'scroll'
                        } else {
                            this.options.legend.type = 'plain'
                        }
                        this.options.graphic = {
                            type: 'text',
                            style: { opacity: 0 }
                        }
                        this.options.series[0].data = list
                        this.options.series[1].data = list
                        this.dataLen = list.length
                        this.currentHighlightIndex = 0
                        this.initChartAnim()
                    }

                } else {
                    this.emptyCircleStyle()
                }
            })
        },
        emptyCircleStyle() {
            let list = []
            for (let i in enumeration.alarmWorkOrderStatus) {
                let obj = enumeration.alarmWorkOrderStatus[i]
                list.push({
                    name: obj.name,
                    itemStyle: {
                        color: obj.color
                    }
                })
            }
            this.options.legend.type = 'plain'
            this.options.legend.data = list
            this.options.series[0].data = []
            this.options.series[1].data = list
            this.options.graphic = {
                type: 'text',
                z: 10,
                left: 'center',
                top: '44%',
                style: {
                    text: '暂无工单',
                    font: '14px Microsoft YaHei',
                    fill: '#4E627E',
                    opacity: 1
                }
            }
        }
    }
}
</script>

<style lang="less" scoped>
.top-right{
    position: absolute;
    top: 15px;
    right: 11px;
    z-index: 9;
}
</style>
