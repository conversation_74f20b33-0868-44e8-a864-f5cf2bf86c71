<template>
    <ContentCard>
        <EchartItem :option="options" style="height: 336px" />
    </ContentCard>
</template>

<script>
import EchartItem from '@/components/common/EchartItem';
import { tooltipExtraCssText } from '@/utils/tool';
export default {
    name: 'OnlineCard',
    components: {
        EchartItem
    },
    props: {
        applicationId: { default: '' }
    },
    data() {
        return {
            options: {
                title: {
                    text: '在离线情况',
                    textStyle: {
                        fontSize: 16,
                        color: '#1E2A55'
                    }
                },
                tooltip: {
                    trigger: 'axis',
                    triggerOn: 'mousemove',
                    extraCssText: tooltipExtraCssText,
                    borderWidth: 0,
                    formatter: (arg) => {
                        return this.getEchartsTooltip(arg)
                    },
                    enterable: true,
                    position: function(point, params, dom, rect, size) {
                        // 固定在顶部
                        return [point[0], '1%'];
                        // return [0, point[1]];
                    }
                },
                legend: {
                    icon: 'circle',
                    itemHeight: 8,
                    itemWidth: 8,
                    right: 0
                },
                grid: {
                    left: 10,
                    right: 10,
                    bottom: 20,
                    top: 40,
                    containLabel: true,
                },
                xAxis: [
                    {
                        type: 'category',
                        data: [],
                        axisLabel: {
                            textStyle: {
                                color: '#798799', // 设置 Y 轴标签颜色为蓝色
                            },
                        },
                        axisLine: {
                            show: false,
                            lineStyle: {
                                color: '#E0E6F1',
                            }
                        },
                        boundaryGap: true,
                    },
                ],
                yAxis: [
                    {

                        axisLine: {
                            show: false,
                            lineStyle: {
                                color: '#E0E6F1',
                            }
                        },
                        axisLabel: {
                            textStyle: {
                                color: '#798799', // 设置 Y 轴标签颜色为蓝色
                            }
                        },
                        splitLine: {
                            lineStyle: {
                                type: 'dashed', // 虚线

                            },
                            show: true, // 隐藏
                        },
                    },
                ],
                color: ['#37E2E2', '#FF708B'],
                series: [
                    {
                        name: '在线',
                        type: 'line',
                        smooth: true,
                        showSymbol: false,
                        areaStyle: {
                            color: {
                                type: 'linear',
                                x: 0,
                                y: 0,
                                x2: 0,
                                y2: 1,
                                colorStops: [{
                                    offset: 0, color: 'rgba(100, 255, 236, 0.12)' // 0% 处的颜色
                                }, {
                                    offset: 1, color: 'rgba(52, 255, 243, 0)' // 100% 处的颜色
                                }],
                                global: false
                            }
                        },
                        data: [],
                    },
                    {
                        name: '离线',
                        type: 'line',
                        smooth: true,
                        showSymbol: false,
                        areaStyle: {
                            color: {
                                type: 'linear',
                                x: 0,
                                y: 0,
                                x2: 0,
                                y2: 1,
                                colorStops: [{
                                    offset: 0, color: 'rgba(255, 112, 139, 0.12)' // 0% 处的颜色
                                }, {
                                    offset: 1, color: 'rgba(255, 112, 139, 0)' // 100% 处的颜色
                                }],
                                global: false
                            }
                        },
                        data: [],
                    }
                ]
            },
            searchObj: {
                baseApplication: {
                    id: ''
                },
                startTime: '',
                endTime: ''
            },
            detailObj: {}
        }
    },
    watch: {
        applicationId(id) {
            if (id) {
                this.getData()
            }
        }
    },
    created() {
        let time = new Date()
        time.setDate(time.getDate() - 1)
        let arr = this.$Util.getDateRangeS(time, 'nd', 'YYYY-MM-DD HH:mm:ss')
        this.searchObj.startTime = arr[0]
        this.searchObj.endTime = arr[1]
        if (this.applicationId) {
            this.getData()
        }
    },
    methods: {
        getData() {
            this.searchObj.baseApplication.id = this.applicationId
            this.$request('/sysHomePage/offOnLineKx', this.searchObj, 'post').then(async(res) => {
                let obj = {
                    day: [],
                    d1: [],
                    d2: []
                }
                res.data.forEach(item => {
                    obj.day.push(this.$Util.formatDate(item.recordTime, 'YYYY-MM-DD'))
                    obj.d1.push(item.onlineNum || 0)
                    obj.d2.push(item.offlineNum || 0)
                })
                this.options.xAxis[0].data = obj.day
                this.options.series[0].data = obj.d1
                this.options.series[1].data = obj.d2
                for (let k in obj.day) {
                    await this.getDetail(obj.day[k])
                }
            })
        },
        getDetail(time) {
            let param = {
                baseApplication: {
                    id: this.applicationId
                },
                recordTime: time + ' 00:00:00'
            }
            return new Promise((resolve, reject) => {
                this.$request('/sysHomePage/offOnLineKxDetail', param, 'post').then(res => {
                    this.detailObj[time] = res.data || []
                    resolve()
                })
            })
        },
        getEchartsTooltip(arg) {
            let name = arg[0].name
            let list = this.detailObj[name]
            if (!list || list.length === 0) {
                return ''
            }
            let dom = '<div style="min-width: 300px">'
            // 标题
            dom += `<div style="display: flex;align-items: center;padding-right: 10px;"><div style="flex: 1;">${name}</div>
<div style="display: flex;align-items: center;width: 60px;justify-content: flex-end;margin-right: 8px;">
<span style="width: 10px;height: 10px;border-radius: 50%;background: #37E2E2;margin-right: 8px;"></span>
<div style="color: #1E2A55;font-size: 12px;font-weight: 600">在线</div></div>
<div style="display: flex;align-items: center;width: 60px;justify-content: flex-end;">
<span style="width: 10px;height: 10px;border-radius: 50%;background: #FF708B;margin-right: 8px;"></span>
<div style="color: #1E2A55;font-size: 12px;font-weight: 600">离线</div></div>`
            dom += '</div>'
            // 标题end

            dom += '<div style="max-height:60vh;overflow-y:auto">'
            if (list && list.length > 0) {
                list.forEach(item => {
                    dom += this.getItemHtml(item.deviceTypeName, item.onlineNum || 0, item.offlineNum || 0)
                })
            }
            dom += '</div></div>'
            return dom
        },
        getItemHtml(name, onlineNum, offlineNum) {
            return `<div style="
                  display:flex;
                  align-items: center;
                  min-height: 32px;
                  justify-content: space-between;
                  background: rgba(255, 255, 255, 0.9);
                  box-shadow: 6px 0px 20px rgba(34, 87, 188, 0.1);
                  border-radius: 4px;
                  padding-left: 8px;
                  margin-top: 4px;
                  padding-right: 10px;
                  ">
                  <div style="
                  color: #4E5969;
                  font-size: 12px;
                  flex: 1;
                  overflow:hidden;
                  margin-right: 8px;
                  font-family: 'PingFang SC';
                  ">${name}</div>
                  <div style="
                  color: #1D2129;
                  font-size: 13px;
                  font-family: 'PingFang SC';
                  font-weight: 700;
                  width: 60px;text-align:right;margin-right: 8px;
                  ">${onlineNum}</div>
                  <div style="
                  color: #1D2129;
                  font-size: 13px;
                  font-family: 'PingFang SC';
                  font-weight: 700;
                  width: 60px;text-align:right;
                  ">${offlineNum}</div>
                  </div>`
        }
    }
}
</script>

<style lang="less" scoped>

</style>
