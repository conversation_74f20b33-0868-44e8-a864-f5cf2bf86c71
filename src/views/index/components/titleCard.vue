<template>
        <div class="stat-list">
            <template v-for="(item, index) in m.list">
                <div class="stat-info">
                    <imgCard :label="item.title" :value="m[item.key][item.value]" :unit="item.unit" :img="item.img" />
                    <div class="bot-box">
                        <template v-for="cit in item.children">
                            <div v-if="cit.noFmt" class="item">
                                <TooltipAutoShow>
                                    <div class="ellipsis-1">
                                        {{ cit.name }}
                                        <span class="blue">{{ m[item.key][cit.key] }}{{ cit.unit }}</span>
                                    </div>
                                </TooltipAutoShow>
                            </div>
                            <div v-else class="item">
                                <TooltipAutoShow>
                                    <div class="ellipsis-1">
                                        {{ cit.name }}
                                        <span class="red">{{ $Util.formatPowerNum(m[item.key][cit.key]) }}{{ cit.unit }}</span>
                                    </div>
                                </TooltipAutoShow>
                            </div>
                        </template>
                    </div>
                </div>
            </template>
        </div>

</template>

<script>
import { titleList } from '../data'
export default {
    name: 'titleCard',
    props: {
        applicationId: { default: '' }
    },
    data() {
        return {
            m: {
                alarmCountInfo: {},
                alarmRuleCountInfo: {},
                deviceOffonlineInfo: {
                    total: 0,
                    onlineRate: 0
                },
                userCountInfo: {},
                list: titleList
            }
        }
    },
    watch: {
        applicationId(id) {
            if (id) {
                this.getData()
            }
        }
    },
    created() {
        if (this.applicationId) {
            this.getData()
        }
    },
    methods: {
        getData() {
            let param = {
                baseApplication: {
                    id: this.applicationId
                }
            }
            this.$request('/sysHomePage/dataOverview', param, 'post').then(res => {
                let data = res.data || {}
                for (let k in data) {
                    data[k].forEach(item => {
                        this.m[k][item.key] = item.value || 0
                    })
                }
                this.m.deviceOffonlineInfo.total = this.m.deviceOffonlineInfo.onlineNum + this.m.deviceOffonlineInfo.offlineNum
                if (this.m.deviceOffonlineInfo.total === 0) {
                    this.m.deviceOffonlineInfo.onlineRate = 0
                } else {
                    this.m.deviceOffonlineInfo.onlineRate = this.$Util.formatNum(100 * this.m.deviceOffonlineInfo.onlineNum / this.m.deviceOffonlineInfo.total)
                }
            })
        }
    }
}
</script>

<style lang="less" scoped>
.stat-list{
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: #fff;
    border-radius: 4px;
    margin-bottom: 8px;
    .stat-info{
        position: relative;
        padding: 16px 24px;
        flex: 1;
        overflow: hidden;
        .img-card{
            padding: 0;
            margin-bottom: 8px;
        }
    }
    .line{
        width: 1px;
        height: 60px;
        background: @line-1;
        margin: 0 8px;
    }
    .bot-box{
        display: flex;
        align-items: center;
        justify-content: space-between;
        border-top: 1px solid @line-1;
        .item{
            font-size: 12px;
            line-height: 20px;
            color: @text-4-1;
            padding-top: 8px;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            span{
                padding-left: 4px;
                font-size: 14px;
                font-weight: 600;
                //display: block;
            }
            &:last-child{
                margin-right: 0;
            }
        }
        .green{
            color: @success-color;
        }
        .red{
            color: @error-color;
        }
        .blue{
            color: @primary-color;
        }
    }
}
</style>
