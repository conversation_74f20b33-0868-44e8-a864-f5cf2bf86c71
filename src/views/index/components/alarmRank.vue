<template>
    <ContentCard title="告警排名">
        <div class="top-right">
            <dateSelect @on-change="changeDate" :hideDate="true" placement="bottom-end" />
        </div>
        <swiper
            direction="vertical"
            :modules="modules"
            :slides-per-view="8"
            :autoplay="autoplay"
            @swiper="onSwiper"
            @slideChange="onSlideChange"
            style="height: 432px"
            class="alarm-swiper"
            v-if="alarmList.length > 0"
        >
            <swiper-slide v-for="(item, index) in alarmList">
                <div class="alarm-item">
                    <div class="tit-box">
                        <div class="num" :class="'n' + (index + 1)">{{ index + 1 }}</div>
                        <div class="name">
                            <TooltipAutoShow :content="item.eventName" />
                        </div>
                        <div class="value">{{ item.alarmCount }}</div>
                    </div>
                    <Progress :percent="item.percent" stroke-color="#4086FF" :stroke-width="8" hide-info />
                </div>
            </swiper-slide>
        </swiper>
        <no-data v-else style="height: 432px" />
    </ContentCard>
</template>

<script>
import { ref, watch, getCurrentInstance } from 'vue'
import { Autoplay } from 'swiper';
import { SwiperSlide } from 'swiper/vue/swiper-slide';
import { Swiper } from 'swiper/vue/swiper';
import 'swiper/swiper.css';
import dateSelect from '@/components/common/dateSelect/index'
export default {
    name: 'alarmRank',
    props: {
        applicationId: { default: '' }
    },
    components: {
        Swiper,
        SwiperSlide,
        dateSelect
    },
    setup(props) {
        const that = getCurrentInstance()?.appContext.config.globalProperties
        const autoplay = ref({
            delay: 5000
        })
        const swiperMy = ref(null)
        const onSwiper = (swiper) => {
            swiperMy.value = swiper
        };
        const onSlideChange = () => {
        };
        const alarmList = ref([])
        const searchObj = ref({
            baseApplication: {
                id: props.applicationId
            },
            startTime: '',
            endTime: ''
        })

        const getData = () => {
            if (!props.applicationId || !searchObj.value.startTime) {
                return
            }
            that.$request('/sysHomePage/alarmTopCount', searchObj.value, 'post').then(res => {
                let total = 0
                if (res.data && res.data.length > 0) {
                    total = res.data[0].alarmCount
                }
                alarmList.value = (res.data || []).map(k => {
                    k.percent = that.$Util.formatNum(100 * k.alarmCount / total)
                    return k
                })
            })
        }
        getData()
        watch(props.applicationId, () => {
            getData()
        })
        const changeDate = (arr) => {
            searchObj.value.startTime = arr[0]
            searchObj.value.endTime = arr[1]
            getData()
        }
        return {
            swiperMy,
            autoplay,
            onSwiper,
            onSlideChange,
            modules: [Autoplay],
            alarmList,
            changeDate
        };
    }
}
</script>

<style lang="less" scoped>
.top-right{
    position: absolute;
    top: 15px;
    right: 11px;
    z-index: 9;
}
.no-data{
    padding-top: 100px;
}
.alarm-item{
    padding: 8px 0;
    .tit-box{
        display: flex;
        align-items: center;
        margin-bottom: 8px;
        font-size: 12px;
        .num{
            min-width: 16px;
            height: 16px;
            background: #EBEDF0;
            border-radius: 2px;
            color: @text-3-1;
            text-align: center;
            vertical-align: middle;
            line-height: 16px;
            margin-right: 8px;
            &.n1{
                background: #FDA979;
                box-shadow: inset 0px 2px 3px rgba(255, 255, 255, 0.5);
                color: #fff;
            }
            &.n2{
                background: #FFC876;
                box-shadow: inset 0px 2px 3px rgba(255, 255, 255, 0.5);
                color: #fff;
            }
            &.n3{
                background: #FDE079;
                box-shadow: inset 0px 2px 3px rgba(255, 255, 255, 0.5);
                color: #fff;
            }
        }
        .name{
            overflow: hidden;
            flex: 1;
            color: #4E5969;
            line-height: 20px;
            .ivu-tooltip{
                height: 20px;
                display: block;
                /deep/.ivu-tooltip-rel{
                    line-height: 20px;
                }
            }
        }
        .value{
            color: #4E5969;
            font-weight: 500;
            line-height: 20px;
        }
    }
    .ivu-progress, /deep/.ivu-progress-outer{
        display: block;
    }
    /deep/.ivu-progress-inner{
        display: block;
        background: rgba(241, 245, 255, 1);
    }

}
</style>
