<script lang="ts" setup>
import eModal from '@/components/common/modal/index.vue';
import { Button } from 'view-ui-plus';
import {
    defineProps,
    onMounted,
    ref,
    reactive,
    nextTick,
    watch
} from 'vue';
import { getFirstFpsPic, isNullOrEmpty } from '@/utils/tool';
const props = defineProps({
    title: {
        default: '',
    },
    data: {
        type: Object,
        default: () => {
            return {}
        },
    },
})

const data: any = reactive(props.data)
// 车辆详情
const carDetailArr = reactive([
    data.licensePlateNumber,
    data.vehicleType == '1' ? '渣土车' : '未知'
])
// 图片数组长度
const picVideoLength = ref<number>(0)
// 图片视频数组
const attachmentUrlArr = reactive<Array<string>>(data.alarmCaptureUrlList)
// 视频数组
const videoArr = ref<Array<string>>([]);
videoArr.value = attachmentUrlArr.filter((item: string) => /\.(mp4|mov|avi|wmv|flv|mkv|webm)$/.test(item))
// 图片数组
const picArr = ref<Array<string>>([])
picArr.value = attachmentUrlArr.filter((item: string) => !/\.(mp4|mov|avi|wmv|flv|mkv|webm)$/.test(item))
picVideoLength.value = attachmentUrlArr.length
const showModal = ref<boolean>(false);
// 视频第一帧图片
const picBase64 = ref<Array<any>>([]);
onMounted(() => {
    videoArr.value.forEach((item: string) => {
        getFirstFpsPic(item, (base64: string) => {
            picBase64.value.push(base64);
        });
    });
});
function openModal() {
    showModal.value = true;
    nextTick(() => {
        const scrollDom = document.querySelector('.scroll');
        scrollDom?.addEventListener(
            'wheel',
            (e) => {
                e.preventDefault();
            },
            { passive: false }
        );
    });
}
const isVideo = ref<boolean>(false);
watch(
    () => picArr.value,
    (newVal, oldVal) => {
        isVideo.value = isNullOrEmpty(newVal) && true || false
    }, { immediate: true }
);
const initPic = ref<string>(picArr.value[0]);
const activeIndex = ref<number>(0);
const text = ref<string>('图片');
function handleClickPic(picUrl: string, index: number) {
    isVideo.value = false;
    initPic.value = picUrl;
    activeIndex.value = index;
    text.value = '图片';
    if (index === 0) {
        nextTick(() => {
            const scrollDom: any = document.querySelector('.scroll');
            scrollDom.scrollTo({
                left: 0,
                behavior: 'smooth',
            });
        });
    }
    // console.log(index, picVideoLength.value, 'picVideoLength.value');
    if (index === picArr.value.length - 1) {
        nextTick(() => {
            const scrollDom: any = document.querySelector('.scroll');
            scrollDom.scrollTo({
                left: 760 * maxPage.value,
                behavior: 'smooth',
            });
        });
    }
}
function handleClickVideo() {
    text.value = '视频';
    isVideo.value = true;
}

const transform = [0, 190, 380, 570, 760];
let counts = ref<number>(1);
const manyPic = ref<number>(picVideoLength.value);
const remainder = manyPic.value % 4;
const maxPage = ref<number>(1);
const fillPage = Math.floor(manyPic.value / 4);
maxPage.value = Math.ceil(manyPic.value / 4);

function handlePage(count: number) {
    const scrollDom: any = document.querySelector('.scroll');
    counts.value += count;
    if (counts.value <= fillPage) {
        scrollDom.scrollTo({
            left: 760 * (counts.value - 1) - 9,
            behavior: 'smooth',
        });
    } else {
        scrollDom.scrollTo({
            left: 760 * (counts.value - 1) + transform[remainder - 1] - 9,
            behavior: 'smooth',
        });
    }
    // console.log(maxPage.value, counts.value, 'zuidazuixiao');
}
</script>
<template>
    <div :data-number="picVideoLength" class="img-view" @click="openModal">
        <img :src="picArr[0] || picBase64[0]" v-defaultImg alt="" />
        <div class="img-mask">
            <Icon type="ios-eye-outline"></Icon>
        </div>
    </div>
    <eModal class="modal" :option="{
        title: props.title,
        footerHide: true,
        width: 872,
        styles: {
            position: 'absolute',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
        },
    }" @on-cancel="showModal = false" :is-show="showModal">
        <div class="view-container">
            <img v-if="!isVideo" class="img" v-defaultImg :src="initPic" alt="" />
            <video v-else class="img" controls>
                <source :src="videoArr[0]" type="video/mp4" />
            </video>
            <!-- <div class="detail">
                <s-label label="车辆号码" :value="carDetailArr[0]"></s-label>
                <s-label label="车辆类型" :value="carDetailArr[1]"></s-label>
            </div> -->
        </div>
        <div class="title-date">
            <span>抓拍{{ text }}</span>
            <!-- <span>2023-02-24 09:09:58</span> -->
        </div>
        <div class="bottom">
            <div class="pic-list">
                <Button size="small" :style="{ visibility: (counts !== 1 && 'visible') || 'hidden' }" class="arrow"
                    @click="handlePage(-1)">
                    <Icon type="ios-arrow-back" />
                </Button>
                <div class="scroll">
                    <div class="view-list">
                        <!-- :data-count="index + 1" -->
                        <div @click="handleClickPic(i, index)" :class="['item', activeIndex === index ? 'active' : '']"
                            v-for="(i, index) in picArr" :key="i">
                            <img :src="i" alt="" v-defaultImg />
                        </div>
                    </div>
                </div>
                <Button class="arrow" :style="{ visibility: (counts < maxPage && 'visible') || 'hidden' }"
                    @click="handlePage(1)" size="small">
                    <Icon type="ios-arrow-forward" />
                </Button>
            </div>
            <div v-show="picBase64[0]" class="video" @click="handleClickVideo">
                <img class="suspend" src="./images/play-circle-fill.png" alt="" />
                <img :src="picBase64[0]" alt="" />
            </div>
        </div>
    </eModal>
</template>

<style lang="less" scoped>
.img-view {
    width: 36px;
    height: 36px;
    margin: 8px 0;
    border-radius: 4px;
    display: flex;
    align-items: center;
    position: relative;
    cursor: pointer;
    &:hover {
        .img-mask{
            display: flex;
        }
    }
    &::after {
        content: attr(data-number);
        display: block;
        position: absolute;
        right: -10px;
        top: -4px;
        min-width: 20px;
        background: #165dff;
        height: 20px;
        border: 2px solid #ffffff;
        border-radius: 20px;
        color: #ffffff;
        font-size: 12px;
        text-align: center;
        line-height: 18px;
        padding: 0 5px;
    }
    img {
        object-fit: cover !important;
        overflow: hidden;
        border-radius: 4px;
        width: 100%;
        height: 100%;
    }
}
.modal {
    .view-container {
        display: flex;
        column-gap: 16px;

        .img {
            width: 808px;
            height: 450px;
        }
    }
    .title-date {
        height: 30px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        span:first-child {
            color: #1e2a55;
            font-size: 14px;
        }
        span:last-child {
            color: #798799;
            font-size: 14px;
        }
    }
    .bottom {
        display: flex;
        flex-direction: row;
        column-gap: 16px;
        align-items: center;
        position: relative;
        .pic-list {
            max-width: 824px;
            display: flex;
            column-gap: 16px;
            align-items: center;
            
            .ivu-icon{
                margin-right: 0px;
            }

            .arrow {
                background: #f8fafb;
                color: #c2c6ce;
                padding: 0 1px;
                height: 40px;
                display: flex;
                justify-content: center;
                align-items: center;
                cursor: pointer;
                border-radius: 4px;
                width: 24px;

                .ivu-icon {
                    margin-top: 2px;
                    font-size: 20px;
                    color: #c2c6ce;
                }
                &:hover {
                    border-color: #c2c6ce;
                }
            }
            .scroll {
                margin-top: 10px;
                overflow: scroll;
                overflow-x: hidden;
                cursor: pointer;
                width: 808px;
                height: 130px;
                display: flex;
                align-items: center;
                content-visibility: auto;

                .view-list {
                    // width: 100%;
                    display: flex;
                    height: 98px;
                    padding: 0 10px;
                    .item {
                        margin-right: 16px;
                        position: relative;
                        &:first-child {
                            margin-left: 9px;
                        }
                    }
                    .active {
                        box-shadow: -2px 1px 13px rgba(83, 117, 167, 0.2);
                        scale: 1.1;
                        border: 2px solid @primary-color;
                    }
                    img {
                        width: 174px;
                        height: 100%;
                    }
                }
            }
        }
        &::after {
            content: "";
            display: block;
            position: absolute;
            bottom: 0;
            left: 0;
            height: 10px;
            width: 100%;
            z-index: 100;
            background-color: transparent;
        }

        .video {
            display: flex;
            justify-content: center;
            align-items: center;
            position: relative;
            width: 204px;
            .suspend {
                width: 28px;
                height: 28px;
                position: absolute;
                left: 0;
                right: 0;
                top: 0;
                bottom: 0;
                margin: auto;
            }
            img {
                width: 174px;
                height: 98px;
            }
        }
    }
}
.img-mask{
    display: none;
    position: absolute;
    border-radius: 4px;
    left: 0;
    top: 0;
    background: rgba(0,0,0,.6);
    width: 100%;
    height: 100%;
    align-items: center;
    justify-content: center;
}
.img-mask i{
    color: #fff;
    font-size: 20px;
    margin: 0 2px;
}
</style>
