<template>
    <div>
        <BreadcrumbCustom>
            <BreadcrumbItem>火情监测</BreadcrumbItem>
            <BreadcrumbItem>监测地图</BreadcrumbItem>
        </BreadcrumbCustom>
        <Card dis-hover :bordered="false" class="map-cont" id="fullscreen">
            <div class="face-plate-cont">
                <div class="left-plate">
                    <div class="area-tree plate-bg">
                        <map-area-tree-select type="1" @updateAreaPath="updateAreaPath"
                            table="livable_fire_monitoring_device" />
                    </div>
                    <div class="device-cont plate-bg">
                        <Input suffix="ios-search" placeholder="请输入设备信息" clearable v-model="searchText" />
                        <scorll-box :scorll-id="activedObjId" key="id">
                            <template #cont>
                                <map-device-list :deviceList="deviceList" :searchText="searchText" lableKey="sbmc"
                                    valueKey="deviceCode" :activedObjId="activedObjId" @actived-obj="handleActivedObj" />
                            </template>
                        </scorll-box>
                    </div>
                </div>
                <div class="right-plate">
                    <div class="full-op" @click="handleFullScreen">
                        <Icon :type="fullFlag ? 'md-contract' : 'md-expand'" />
                    </div>
                    <div class="device-info-cont">
                        <s-tab :tab-list="tabList" class="plate-bg" tit-class="right-tab-title"
                            :body-class="`${isToggleDropdown ? '' : 'hide-tab-box'} right-tab-body`"
                            @handleChange="handleChange" :default-active="defaultActive">
                            <template #expand>
                                <div class="toggle" @click="isToggleDropdown = !isToggleDropdown">
                                    <Icon :type="isToggleDropdown ? 'ios-arrow-down' : 'ios-arrow-up'" />
                                </div>
                            </template>
                            <template #overview>
                                <div class="scorll-map-cont manhole-cover-right">
                                    <device-oview :area-paths="areaPaths" :deviceList="deviceList" ref="overviewRef" />
                                </div>
                            </template>
                            <template #detail>
                                <device-detail :actived-obj-id="activedObjId" />
                            </template>
                        </s-tab>
                    </div>
                </div>
            </div>
            <div id="container" class="container-map" :class="fullFlag ? 'full-map' : ''"></div>
        </Card>
    </div>
</template>

<script lang="ts">
import AMapLoader from '@amap/amap-jsapi-loader';
import { defineComponent, nextTick, onMounted, ref, Ref, watch } from 'vue';
import { ComponentInfo } from '@/api/manholeCoverService';
import { garFireDeviceList } from '@/api/livableManage/fireMonitoringService';
import DeviceOview from './components/deviceOview.vue';
import DeviceDetail from './components/deviceDetail.vue';
import { launchIntoFullscreen, exitFullscreen } from '@/utils/tool'
import mapAreaTreeSelect from '@/components/common/mapAreaTreeSelect/index'
import scorllBox from '@/components/common/scorllBox/index'
import { getMarkerContent } from '@/components/common/mapAreaTreeSelect/markercontent'
import mapDeviceList from '@/components/common/mapDeviceList'
export default defineComponent({
    components: {
        DeviceOview,
        DeviceDetail,
        mapAreaTreeSelect,
        scorllBox,
        mapDeviceList
    },
    setup(props, ctx) {
        // *********************
        // 地图
        // *********************
        const map = ref<any>(null);
        const Amap = ref<any>(null);
        const ininMap = () => {
            AMapLoader.load({
                key: 'ea53a5291f8f7c1f215ba20930ea9866',
                version: '2.0'
            }).then((AMap) => {
                Amap.value = AMap
                map.value = new AMap.Map('container', {
                    resizeEnable: true,
                    // mapStyle: 'https://geohub.amap.com/mapstyle/clone?id=20d3f4f1e43c80b9c68c30fb8a1ab94b',
                    zoom: 16,
                    center: [114.61, 30.45],
                })
                nextTick(() => {
                    getList([], true)
                })
            })
        }
        // 更新地图上的图标
        const updateMapIcon = (data: ComponentInfo[]) => {
            if (!map.value) return
            // 清除地图上的图标
            map.value.clearMap()
            data.forEach((ele: ComponentInfo) => {
                if (!ele.objX || !ele.objY) return
                const content = getMarkerContent(ele, activedObjId.value, 'hide-tag', (d: ComponentInfo) => {
                    return require('./images/icon_camera.png')
                })
                const icon = new Amap.value.Marker({
                    position: [ele.objX, ele.objY],
                    content,
                    size: new Amap.value.Size(40, 40),
                    offset: new Amap.value.Pixel(-5, -35),
                    extData: ele.id,
                    label: {
                        content: `<div class="marker-label-box">${ele.sbmc}</div>`,
                        offset: new Amap.value.Pixel(0, 0),
                    }
                })
                icon.on('click', (e: any) => {
                    if (activedObjId.value && activedObjId.value == e.target.getExtData()) {
                        activedObjId.value = 0
                        defaultActive.value = 0
                        return
                    }
                    defaultActive.value = 1
                    activedObjId.value = e.target.getExtData()
                })
                map.value.add([icon])
            })
            nextTick(() => {
                map.value.setFitView();
            })

        }
        onMounted(() => {
            ininMap();
            window.addEventListener('fullscreenchange', e => {
                // 监听到屏幕变化，在回调中判断是否已退出全屏
                const isFull = document.fullscreenElement || document['mozFullScreenElement'] || document['webkitFullscreenElement'];
                fullFlag.value = isFull ? true : false
            })
        })
        // *********************
        // 左侧区域树
        // *********************
        // 搜索树
        const areaPaths = ref<string[]>([])
        // 更新当前勾选区域
        const updateAreaPath = (areaPath: string[]) => {
            console.log('updateAreaPath')

            areaPaths.value = areaPath
            // 查询当前区域的设施
            getList(areaPath)
            defaultActive.value = 0
        }

        // *********************
        // 左侧设施列表
        // *********************
        // 设施搜索关键字
        const searchText = ref<string>('')
        // 选中的设施
        const activedObjId = ref<number>(0)
        // 监听选中的设施
        watch(activedObjId, () => {
            const activeDevice = deviceList.value.find(k => k.id == activedObjId.value)
            if (activeDevice?.objX) {
                map.value?.setZoomAndCenter(14, [activeDevice.objX, activeDevice.objY])

            }
            const overlays: any[] = map.value?.getAllOverlays('marker')
            overlays?.forEach(e => {
                if (e.getExtData() == activedObjId.value) {
                    const content = getMarkerContent(activeDevice, activedObjId.value, 'hide-tag', (d: ComponentInfo) => {
                        return require('./images/icon_camera.png')
                    })
                    e.setContent(content)
                } else {
                    const markerInfo = deviceList.value.filter(k => k.id == e.getExtData())[0] || {}
                    const content = getMarkerContent(markerInfo, activedObjId.value, 'hide-tag', (d: ComponentInfo) => {
                        return require('./images/icon_camera.png')
                    })
                    e.setContent(content)
                }
            })
            if (activedObjId.value) {
                isToggleDropdown.value = true
            }
            nextTick(() => {
                map.value.setFitView();
            })
        })
        // 设备列表
        const deviceList = ref<ComponentInfo[]>([])
        // 手动设置当前选中设备
        const handleActivedObj = (item: ComponentInfo) => {

            activedObjId.value = (activedObjId.value == item.id) ? 0 : (item.id || 0)
            if (activedObjId.value) {
                defaultActive.value = 1
                return
            }
            defaultActive.value = 0
        }
        // 查询当前区域的设施
        const getList = async (areaPaths?: string[], first?: boolean) => {
            const params = {
                page: {
                    current: 1,
                    size: -1
                },
                customQueryParams: {
                    areaPaths: areaPaths
                }
            }
            const res = await garFireDeviceList(params)
            const { data, success }: { data: recordsResponse, success: boolean } = res as unknown as HttpResponse<recordsResponse>
            if (success) {
                deviceList.value = (data.records as ComponentInfo[]).map(k => ({ ...k, switchState: k.light }));
                const actived_Obj = deviceList.value.filter(k => k.id == activedObjId.value)
                if (!actived_Obj.length) { // 已经勾选的设备不在当前区域树中
                    activedObjId.value = deviceList.value[0]?.id || 0
                }

                // 更新地图上的图标
                updateMapIcon(deviceList.value)

            }
        }

        // 地图全屏
        const fullFlag: Ref<boolean | null> = ref(false)
        const handleFullScreen = () => {
            const element = document.getElementById('fullscreen');
            if (!element) return
            // false是进入全屏状态
            if (fullFlag.value) {
                // 退出全屏
                exitFullscreen(element);
            } else {
                launchIntoFullscreen(element);
            }
            fullFlag.value = !fullFlag.value
        }
        // 是否展开右侧
        const isToggleDropdown = ref<boolean>(true)
        watch(() => isToggleDropdown.value, (val) => {
            if (val) {
                overviewRef.value.handleResize()
            }
        })
        watch(() => isToggleDropdown.value, (val) => {
            if (val) {
                overviewRef.value.handleResize()
            }
        })
        const defaultActive = ref<number>(0)
        const tabList = ref([
            {
                name: '设备概览',
                key: 'overview',
                icon: 'dashboard'
            },
            {
                name: '设备详情',
                key: 'detail',
                icon: 'common'
            }
        ])
        // 概览ref
        const overviewRef = ref()
        // 切换回需要resize
        const handleChange = (i: number) => {
            if (i == 0) {
                overviewRef.value.handleResize()
            }
            defaultActive.value = i
            isToggleDropdown.value = true
        }
        return {
            deviceList,
            tabList,
            areaPaths,
            activedObjId,
            searchText,
            handleFullScreen,
            fullFlag,
            isToggleDropdown,
            updateAreaPath,
            handleChange,
            overviewRef,
            defaultActive,
            handleActivedObj
        };
    }
});
</script>

<style lang="less" scoped>
@import '../../../../styles/mapPage.less';
@import './index.less';
</style>
