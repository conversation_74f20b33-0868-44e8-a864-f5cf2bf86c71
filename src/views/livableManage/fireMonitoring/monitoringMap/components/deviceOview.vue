<template>
    <div class="bridge-gather">
        <div>
            <div class="icon-box"><img src="@/assets/images/icon_date_today.png" />今日告警</div>
            <div class="gather-num">
                {{ overviewData.todayAlarmNumbers }}
            </div>
        </div>
        <div>
            <div class="icon-box"><img src="@/assets/images/icon_date_week.png" />本周告警</div>
            <div class="gather-num">
                {{ overviewData.weekAlarmNumbers }}
            </div>
        </div>
        <div>
            <div class="icon-box"><img src="@/assets/images/icon_date_month.png" />本月告警</div>
            <div class="gather-num">
                {{ overviewData.mouthAlarmNumbers }}
            </div>
        </div>

    </div>
    <div class="plate-tit">
        <img src="../images/icon_manholecover_011.png" class="title-icon" />
        告警趋势
    </div>
    <div class="online-overview">
        <echart-item :option="lineOption" class="pie-echart" ref="lineRef" />
    </div>
    <!-- <div class="online-overview">
        <rankList :list="alarmCameraList" />
    </div> -->
    <div class="plate-tit">
        告警情况
    </div>
    <div class="alarm-overview">
        <div class="alarm-info">
            <div class="alarm-info">
                <img src="@/assets/images/icon-告警铃铛.png" class="title-icon" />
                <div class="ml-6">
                    告警设备数
                    <div class="num">
                        {{alarmCount.alarm}}
                    </div>
                </div>
            </div>
            <div class="alarm-info">
                <img src="@/assets/images/icon_manholecover_013.png" class="title-icon" />
                <div class="ml-6">
                    正常设备数
                    <div class="num">
                        {{alarmCount.normal}}
                    </div>
                </div>
            </div>
        </div>
        
        <div class="alarm-list">
            <div class="alarm-item" v-for="(item, index) in mapAlarmList || []" :key="index">
                <tooltip-auto-show>{{ item.sbmc }}</tooltip-auto-show>
                <tooltip-auto-show>{{ item.content }}</tooltip-auto-show>
                <span>{{ item.alarmTime }}</span>
            </div>
        </div>
    </div>
</template>
<script lang="ts">
import { defineComponent, ref, onMounted,watch } from 'vue'
import { ManholeCoverAlarm, ManholeCoverOnline } from '@/api/manholeCoverService';
import { deviceStatisticsAlarm,alarmStatisticsDay } from '@/api/livableManage/fireMonitoringService'
import EchartItem from '@/components/common/EchartItem/index'
import { ECOption } from '@/components/common/EchartItem/echart';
export default defineComponent({
    components: {
        EchartItem
    },
    props: {
        areaPaths: {
            type: Array,
            default: () => []
        },
        deviceList:{
            type:Array,
            default:()=>[]
        }
    },
    setup(props) {
        interface AlarmMapStatistics {
            top10: any[],
            latest5: any[],
            mouthAlarmNumbers: number,
            todayAlarmNumbers: number,
            weekAlarmNumbers: number,
        }
        // *********************
        // 在线统计
        // *********************
        // 在线统计
        const alarmCameraList = ref()
        const overviewData = ref({
            mouthAlarmNumbers: 0,
            todayAlarmNumbers: 0,
            weekAlarmNumbers: 0,
        })

        const alarmCount = ref({
            alarm: 0,
            normal: 0
        })

        const lineOption = ref<ECOption>({
            tooltip: {
                trigger: 'axis',
                confine: true,
                axisPointer: {}
            },
            grid: {
                top: 20,
                bottom: 30,
                left: 0,
                right: 0,
                containLabel: true
            },
            xAxis: [
                {
                    type: 'category',
                    axisLabel: {
                        color: '#4E568C'
                    },
                    axisLine: {
                        show: false
                    },
                    axisTick: {
                        show: false
                    },
                    data: []
                }
            ],
            yAxis: {
                type: 'value',
                minInterval : 1,
                axisLine: {
                    show: false
                },
                axisTick: {
                    show: false
                }
            },
            color: ['#4080FF'],
            series: [
                {
                    data: [],
                    name: '告警数',
                    type: 'line',
                    smooth: true,
                    showSymbol: false
                }
            ]
        })
        
        const lineRef = ref()
        const handleResize = () => {
            lineRef.value.handleResize()
        }
        // *********************
        // 在线情况柱状图
        // *********************

         // 在线情况
         const getMapDay = async () => {
            const params = {
                areaPaths: props.areaPaths as unknown as string[],
                model: 34
            }
            const res = await alarmStatisticsDay(params)
            const { data, success }: { data: ManholeCoverOnline[], success: boolean } = res as unknown as HttpResponse<ManholeCoverOnline[]>
            if (success) {
                lineOption.value.xAxis![0].data = data.map(k => k.recordTime?.slice(5, 10))
                lineOption.value.series![0].data = data.map(k => {
                    return {
                        name: k.recordTime,
                        value: k.online
                    }
                })
                console.log(lineOption.value.series![0].data)
            }
        }

        // 5条告警列表
        const mapAlarmList = ref<ManholeCoverAlarm[]>([])

        // 告警统计
        const getMapAlarms = async () => {
            //注入areapath
            const params = {
                areaPaths: props.areaPaths as unknown as string[],
                model: 34
            }
            const res = await deviceStatisticsAlarm(params)
            const { data, success }: { data: AlarmMapStatistics, success: boolean } = res as unknown as HttpResponse<AlarmMapStatistics>
            if (success) {
                const { top10 } = data;
                // const total = top10.reduce((total, item) => {
                //     return total + item.alarmNumbers
                // }, 0)
                alarmCameraList.value = top10.map(k => {
                    return {
                        name: k.sbmc,
                        value: k.alarmNumbers,
                        percent: top10[0].alarmNumbers ? (k.alarmNumbers / top10[0].alarmNumbers) * 100 : 0
                    }
                });
                mapAlarmList.value = data.latest5;
                overviewData.value = {
                    mouthAlarmNumbers: data.mouthAlarmNumbers,
                    todayAlarmNumbers: data.todayAlarmNumbers,
                    weekAlarmNumbers: data.weekAlarmNumbers,
                }
            }

            //处理告警数与正常数
            if (props.deviceList) {
                let alarmList =  props.deviceList.filter((m: any) => m.alarmState ==1 )
                alarmCount.value.alarm = alarmList.length
                alarmCount.value.normal = props.deviceList.length - alarmList.length
            }
        }
        onMounted(() => {
            getMapAlarms()
            getMapDay()
        })
        watch(props, () => {
            // getMapOnline()
            getMapDay()
            getMapAlarms()
            
        }, { immediate: true })
        return {
            mapAlarmList,
            overviewData,
            alarmCount,
            alarmCameraList,
            handleResize,
            lineRef,
            lineOption
        }
    }
})
</script>
<style lang="less" scoped>
/deep/.ivu-tooltip {
    height: 40px;

    .ivu-tooltip-rel {
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        line-height: 40px;
        width: 100%;
    }
}

.bridge-gather {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    column-gap: 8px;
    line-height: 22px;
    color: @text-color;
    padding-bottom: 8px;
    position: relative;
    white-space: nowrap;
    height: 90px;
    .icon-box {
        display: flex;
        column-gap: 2px;
        align-items: center;
        padding-top: 15px;
    }
    img  {
        display: block;
        height: 22px;
        margin-right: 4px;
    }
    .gather-num {
        display: flex;
        align-items: flex-end;
        font-weight: 500;
        font-size: 24px;
        line-height: 19px;
        color: @title-color;
        margin: 8px 0px;
        padding-left: 26px;
        span {
            font-weight: 400;
            font-size: 12px;
            line-height: 14px;
            color: @input-placeholder-color ;
            margin-left: 5px;
        }
    }
    .percent-box {
        align-items: center;
        height: 20px;
        font-size: 12px;
        .percent-num {
            margin-left: 8px;
        }
    }
}
</style>
