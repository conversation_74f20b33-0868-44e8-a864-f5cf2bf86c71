<template>
    <div class="details-container">
        <div class="head-cont" :class="deviceDetail?.status == 1 ? 'online' : ''">
            <div class="icon-img">
                <img src="../images/icon_camera.png" class="top-icon" />
            </div>
            <s-tag :color="deviceDetail?.status == 1 ? '#00B42A' : '#1E2A55'"
                :background="deviceDetail?.status == 1 ? '#E8FFEA' : '#F2F3F5'" class="status-tag">
                {{ deviceDetail?.status == 1 ? '在线' : '离线' }}
            </s-tag>
        </div>
        <div class="name">
            {{ deviceDetail?.extendInfo?.sbmc }}
        </div>
        <div class="code">
            {{ deviceDetail?.deviceCode }}
        </div>
        <no-data v-if="!activedObjId" value="请先选择设施" />
        <s-tab :tab-list="tabList" bodyClass="tab-info" v-else>
            <template #detail>
                <div class="scorll-map-cont">
                    <div class="device-info-list">
                        <s-label label="设备标识码" :value="deviceDetail?.extendInfo?.bsm" />
                        <s-label label="使用状态" :value="deviceDetail?.useStatus == 1 ? '启用' : '停用'" />
                        <s-label label="区域位置" :value="deviceDetail?.extendInfo?.areaPath?.replace(/@/g, '/')" />
                        <s-label label="权属单位" :value="deviceDetail?.objInfo?.ownerEnterpriseName" />
                        <s-label label="联 系 人" :value="deviceDetail?.objInfo?.contactPerson || '-'" />
                        <s-label label="联系电话" :value="deviceDetail?.objInfo?.contactPhone || '-'" />
                    </div>
                    <div class="alarm-info-list" v-show="deviceDetail?.devicePropertyStatusList?.length">
                        <s-label v-for="(item, index) in deviceDetail?.devicePropertyStatusList || []" :key="index"
                            :label="item.propName">
                            <template #value>
                                <span >{{ item.value }}</span>
                            </template>
                        </s-label>
                    </div>
                </div>
            </template>
            <template #alarm>
                <div class="scorll-map-cont">
                    <div class="video-box">
                        <video controls class="video-content" :autoplay="false">
                            <source src="https://www.w3school.com.cn/example/html5/mov_bbb.mp4" type="video/mp4">
                        </video>
                    </div>
                    <div class="trace-box" style="background:none;">
                        <DatePicker v-model="dateRange" format="yyyy/MM/dd" type="daterange" placement="bottom-end" transfer
                            placeholder="请选择日期范围" style="width:100%;padding:16px 0;" 
                            @on-change="getSelectPersonCarRecordDtoPage"
                            />
                    </div>
                    <div>
                        <div class="plate-tit" style="margin-bottom: 16px;">
                            <img src="../images/icon_contorl_004.png" class="title-icon" />
                            告警抓拍
                        </div>
                        <no-data value="当前时段暂无告警记录" v-if="!deviceAlarm?.length" />
                        <div v-for="(item, index) in deviceAlarm || []" :key="index" style="margin-bottom: 16px;">
                            <img style="width: 100%;" class="img" v-defaultImg :src="item.alarmCaptureUrlList[0]" alt="" />
                            <span>抓拍时间：{{item.alarmTime}}</span>
                        </div>
                        
                    </div>
                </div>
            </template>
        </s-tab>
    </div>
    
</template>
<script lang="ts">
import { defineComponent, ref, watch } from 'vue'
import { ManholeCoverInfo, ManholeCoverAlarm } from '@/api/manholeCoverService'
import { getDeviceDetail,deviceAlarmByCode } from '@/api/livableManage/fireMonitoringService'
import { streetlightDetaillInfo } from '@/api/streetlightMapType'
import Util from '@/utils'
export default defineComponent({
    props: {
        activedObjId: {
            type: Number,
            default: ''
        }
    },
    setup(props, ctx) {
        // 设备详情
        const deviceDetail = ref<streetlightDetaillInfo>()
        const deviceCode = ref('')
        const dateRange = ref([])
        const getDetail = async () => {
            if (!props.activedObjId) {
                deviceDetail.value = {
                    objInfo: {},
                    extendInfo: {},
                    lightingType: 1
                }
                return
            }
            const res = await getDeviceDetail(props.activedObjId)
            const { data, success }: { data: streetlightDetaillInfo, success: boolean } = res as unknown as HttpResponse<streetlightDetaillInfo>
            if (success) {
                deviceDetail.value = data
                if (data.deviceCode) {
                    deviceCode.value = data.deviceCode
                    getManholeMapAlarm(data.deviceCode)
                }
                // 更新地图上的图标

            }
        }
        const getSelectPersonCarRecordDtoPage = async () => {
            let param = {
                deviceCode: deviceCode.value,
                startTime: dateRange.value[0]?Util.formatDate(dateRange.value[0], 'yyyy-MM-DD') + ' 00:00:00':null,
                endTime: dateRange.value[0]?Util.formatDate(dateRange.value[1], 'yyyy-MM-DD') + ' 23:59:59':null,
            }
            const res = await deviceAlarmByCode(param)
            const { data, success }: { data: ManholeCoverAlarm[], success: boolean } = res as unknown as HttpResponse<ManholeCoverAlarm[]>
            if (success) {
                deviceAlarm.value = data
                // 更新地图上的图标

            }
        }
        //设备告警
        const deviceAlarm = ref<ManholeCoverAlarm[]>([])
        const getManholeMapAlarm = async (deviceCodestr: string) => {
            if (!props.activedObjId){
                deviceCode.value = ''
                return
            } 
            let param = {
                deviceCode: deviceCodestr
            }
            const res = await deviceAlarmByCode(param)
            const { data, success }: { data: ManholeCoverAlarm[], success: boolean } = res as unknown as HttpResponse<ManholeCoverAlarm[]>
            if (success) {
                deviceAlarm.value = data
                // 更新地图上的图标

            }
        }
        watch(() => props.activedObjId, () => {
            dateRange.value = []
            getDetail()

        }, { immediate: true })
        const tabList = ref([
            {
                name: '设备详情',
                key: 'detail',
                icon: '1'
            },
            {
                name: '告警日志',
                key: 'alarm',
                icon: '1'
            }
        ])
        return {
            tabList,
            deviceDetail,
            deviceAlarm,
            dateRange,
            deviceCode,
            getSelectPersonCarRecordDtoPage
        }
    }
})
</script>
<style lang="less" scoped>

    /deep/.tab-cont.actived{
        display:flex;
        flex-direction:column;
    }

</style>