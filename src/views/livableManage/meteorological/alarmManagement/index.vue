<script lang="ts" setup>
import SelectAlarmType from '@/components/common/selectAlarmType/index.vue';
import { getCurrentInstance, reactive, markRaw, toRefs, onBeforeMount, onMounted, ref, computed } from 'vue'
import { useStore } from 'vuex'
import TableContentCard from '@/components/global/TableContentCard'
import SideDetail from '@/components/global/SideDetail'
import { getAreaTree } from '@/api/livableManage/parkSoilService'
import { treeData } from '@/api/manholeCoverService'
const $store=useStore()
// console.log($store.getters.dictionary.meteorological_alarm_type)
const tableList = ref<any>([
  { title: '设备编号', key: 'deviceCode', tooltip: true },
  { title: '设备名称', key: 'sbmc', tooltip: true },
  { title: '设备标识码', key: 'bsm', minWidth: 100, tooltip: true },
  { title: '告警类型', key: 'alarmTypeName', maxWidth: 130, tooltip: true },
  { title: '告警等级', slot: 'level', tooltip: true, width: 80 },
  { title: '告警详情', key: 'content', tooltip: true },
  { title: '是否推送', slot: 'pushStatus', tooltip: true, width: 80 },
  { title: '区域位置', key: 'areaPath', tooltip: true },
  { title: '告警时间', key: 'alarmTime', tooltip: true, width: 160 },
  { title: '操作', slot: 'action', maxWidth: 80 }
])
const searchObj = ref<any>({})
const listCom = ref()
interface searchObjType {
  szjd?: string,
  szsq?: string,
  szdywg?: string,
  deviceCode?: string,
  sbmc?: string,
  bsm?: string,
  status?: number,
  useStatus?: number,
  areaLocation?: string,
  areaPaths?: Array<string>
}
function handleSubmit() {
  let params: searchObjType = {}
  params = JSON.parse(JSON.stringify(searchObj.value))
  if (searchObj.value.areaLocation) {
    const arr: Array<string> = searchObj.value.areaLocation.split('/')
    params.szjd = arr[0]
    params.szsq = arr[1]
    params.szdywg = arr[2]
    delete params.areaLocation
  }
  //内置模块id
  params.modelIds = ['13']
  listCom.value.search(params)
}
onMounted(() => {
  handleSubmit()
})
// const a = computed(() =>...mapGetters(['loginUser']))
const showSideDetail = ref<boolean>(false)
const detailInfo = ref<any>(false)
function checkDetail(row: any) {
  showSideDetail.value = true
  detailInfo.value = row
}
function closeSide() {
  showSideDetail.value = false

}

const that = getCurrentInstance()?.appContext.config.globalProperties

</script>
<template>
  <BreadcrumbCustom>
    <BreadcrumbItem>气象环境</BreadcrumbItem>
    <BreadcrumbItem>告警管理</BreadcrumbItem>
  </BreadcrumbCustom>
  <ContentCard title="告警管理">
    <BaseForm @handle-submit="handleSubmit" :model="searchObj" inline :label-width="90">
      <template #formitem>
        <FormItem label="设备编号" prop="deviceCodeKey">
          <Input v-model="searchObj.deviceCodeKey" :maxlength="20" placeholder="请输入" clearable></Input>
        </FormItem>
        <FormItem label="区域位置" prop="areaLocation">
            <AreaSelectTree v-model="searchObj.areaLocation" table-name="livable_meteorological_environment_device" />
        </FormItem>
        <FormItem label="设备标识码" prop="bsm">
          <Input v-model="searchObj.bsm" :maxlength="20" placeholder="请输入" clearable></Input>
        </FormItem>
        <FormItem label="告警类型" prop="alarmType">
          <!-- <Select :transfer="false" v-model="searchObj.alarmType" clearable>
            <Option v-for="(value, key) in $store.getters.dictionary.meteorological_alarm_type" :key="key"
              :value="key" clearable>
              {{ value }}
            </Option>
          </Select> -->
          <!-- <dict-drop-down-select code="meteorological_alarm_type" v-model="searchObj.alarmType" /> -->
          <select-alarm-type v-model="searchObj.alarmType" :param="{modelId: 13, type: 2}" />
        </FormItem>
        <FormItem label="告警等级" prop="level">
          <Select :transfer="false" v-model="searchObj.level" clearable>
            <Option v-for="(item, index) in $enumeration.alarmGrade.filter((i: string, index: number) => index > 0)"
              :key="index" :value="index + 1" clearable>
              {{ item }}
            </Option>
          </Select>
        </FormItem>
        <FormItem label="是否推送" prop="pushStatus">
          <Select :transfer="false" v-model="searchObj.pushStatus" clearable>
            <Option v-for="(item, index) in $enumeration.isPush" :key="index" :value="index" clearable>
              {{ item }}
            </Option>
          </Select>
        </FormItem>
      </template>
    </BaseForm>
    <TableContentCard :baseBtn="false">
      <template #btn>
        <!-- <Button type="primary">导出</Button>
          <Button type="primary">告警设置</Button> -->
      </template>
      <baseTable :model="searchObj" ref="listCom" url="/alarm/list" :columns="tableList">
        <!-- <template #alarmType="{ row }">
          <div>
            {{ $store.getters.dictionary.meteorological_alarm_type[row.alarmType] }}
            <dict-label code="meteorological_alarm_type" :value="row.alarmType" />
          </div>
        </template> -->
        <template #level="{ row }">
          <div>
            {{ $enumeration.alarmGrade[row.level] }}
          </div>
        </template>
        <template #pushStatus="{ row }">
          <div>
            <!-- {{ $enumeration.isPush[row.pushStatus] }} -->
            <pushStatus :value="row.pushStatus"/>
          </div>
        </template>
        <template #action="{ row }">
          <LinkBtn size="small" @click="checkDetail(row)">查看</LinkBtn>
        </template>
      </baseTable>
    </TableContentCard>
  </ContentCard>
  <SideDetail :show="showSideDetail" :data="detailInfo" :modelId="13" @on-cancel="closeSide"></SideDetail>
</template>

<style lang="less" scoped></style>
