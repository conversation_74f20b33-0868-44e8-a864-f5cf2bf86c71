<template>
    <SModal :is-show="isShowModal" @on-cancel="closeModal" :mask-closable="false" class-name="fill-page-modal" :footer-hide="true" width="840"
        title="监测站点管理">
        <Form ref="refAddForm" :model="addForm" class="form">
            <div style=" background: #E8F3FF;padding: 9px 16px 9px 16px;display: flex;align-items: center;">
                <span style="font-size: 14px;">
                    <Icon color="#165dff" type="md-alert" style="margin-right: 8px;" />一个监测站点用来关联多种气象传感器
                </span>
            </div>
            <div style="margin: 16px 0px 16px 0px;display: flex; align-items: center;">
                <img :src="require('@/assets/images/icon-meteorological.png')" alt="" style="width: 24px;height: 24px;margin-right: 8px;">
                <h4 style="margin-right: 17px;">监测站点列表</h4>
                <LinkBtn size="small" @click="newContent" style="display: flex; align-items: center;" :disabled="someoneIsEdit">
                    <Icon :color="someoneIsEdit ? '#94BFFF' : '#165dff'" type="md-add-circle" style="margin-right: 4px;" />添加
                </LinkBtn>
            </div>
            <div class="th-box">
                <div class="th">监测站点名称</div>
                <div class="th">备注</div>
                <div class="th">更新时间</div>
                <div class="th">操作</div>
            </div>
            <div class="form-box">
                <div v-for="(item, index) in addForm" :key="index" class="th-box">
                    <!-- 编辑 -->
                    <template v-if="item.isEdit">
                        <!-- 监测站点名称 -->
                        <FormItem :prop="`[${index}].name`" class="tr-item" :rules="{ required: true, message: '请完善必填项', trigger: 'change,blur' }">
                            <Input v-model="addForm[index].name" clearable maxlength="20" placeholder="请输入"> </Input>
                        </FormItem>
                        <!-- 备注 -->
                        <FormItem :prop="`[${index}].remark`" class="tr-item">
                            <Input v-model="addForm[index].remark" clearable maxlength="100" placeholder="请输入"> </Input>
                        </FormItem>
                        <!-- 更新时间 -->
                        <FormItem :prop="`[${index}].modifyTime`" class="tr-item">
                            <tooltipAutoShow :content="addForm[index].modifyTime" />
                        </FormItem>
                        <div class="operation">
                            <LinkBtn size="small" @click="addContent(index)">确定</LinkBtn>
                            <LinkBtn size="small" @click="cancel(index)">取消</LinkBtn>
                        </div>
                    </template>
                    <template v-else>
                        <!-- 监测站点名称 -->
                        <tooltipAutoShow :content="addForm[index].name" style="margin-left: 12px;" />
                        <!-- 备注 -->
                        <tooltipAutoShow :content="addForm[index].remark" />
                        <!-- 更新时间 -->
                        <tooltipAutoShow :content="addForm[index].modifyTime" />
                        <div class="operation">
                            <LinkBtn size="small" @click="editContent(index)" :disabled="someoneIsEdit">编辑</LinkBtn>
                            <LinkBtn size="small" @click="deleteContent(index)" :disabled="someoneIsEdit">删除</LinkBtn>
                        </div>
                    </template>
                </div>
            </div>
        </Form>
    </SModal>
</template>

<script setup>
import SModal from '@/components/common/modal/index.vue'
import { computed, ref, defineProps, getCurrentInstance, watch, defineExpose, defineEmits } from 'vue'
import { insertMonitoringSite, updateMonitoringSite, deleteMonitoringSite, api_getMonitoringSiteList } from '@/api/livableManage/meteorological'
import Util from '@/utils'

const props = defineProps({
    isShow: {
        type: Boolean
    }
})
const emits = defineEmits(['update:isShow'])
const that = getCurrentInstance()?.appContext.config.globalProperties

// 是否显示弹窗
const isShowModal = computed({
    get() {
        return props.isShow
    },
    set(newVal) {
        emits('update:isShow', newVal)
    }
})
watch(isShowModal, (newV) => {
    if (newV) {
        reset()
    }
})
// 完成操作，重新查表
const reset = async() => {
    let res = await api_getMonitoringSiteList()
    if (res.success) {
            addForm.value = res.data
    }
}
const openModal = () => {
    isShowModal.value = true;
}
const closeModal = () => {
    isShowModal.value = false
    someoneIsEdit.value = false
}
defineExpose({ openModal, closeModal })

// 表单
const someoneIsEdit = ref(false)
const refAddForm = ref()
const addForm = ref([])
// 添加一行
const newContent = () => {
    addForm.value.unshift({
        name: '',
        remark: '',
        isEdit: true
    })
    someoneIsEdit.value = true
}
// 增/改
const addContent = async(index) => {
    let validate = await refAddForm.value.validate()
    if (validate) {
        let res
        if (addForm.value[index].id) {
            res = await updateMonitoringSite(addForm.value[index])
            if (res.success) {
                that.$Message.success('更新成功')
                reset()
                someoneIsEdit.value = false
            }
        } else {
            res = await insertMonitoringSite(addForm.value[index])
            if (res.success) {
                that.$Message.success('添加成功')
                reset()
                someoneIsEdit.value = false
            }
        }
    }
}

// 删除
const deleteContent = async(index) => {
    that?.$Modal.confirm({
        title: '提示',
        content: '您确定要删除选中的数据吗？',
        onOk: async() => {
            let ids = [addForm.value[index].id]
            let res = await deleteMonitoringSite({ ids })
            if (res.success) {
                that.$Message.success('删除成功')
                reset()
                someoneIsEdit.value = false
            }
        }
    })
}
const backup = ref({})
// 编辑
const editContent = (index) => {
    addForm.value[index].isEdit = true
    backup.value = Util.deepCopy(addForm.value[index])
    someoneIsEdit.value = true
}
// 取消
const cancel = (index) => {
    if (addForm.value[index].id) {
        addForm.value[index] = Util.deepCopy(backup.value)
        addForm.value[index].isEdit = false
        backup.value = {}
    } else {
        addForm.value.splice(index, 1)
    }
    someoneIsEdit.value = false
}
</script>

<style lang="less" scoped>

.form {
    height: 480px;
}
.th-box {
    display: grid;
    grid-template-columns: 20% 40% 20% 15%;
    background: #f3f7fb;
    border-bottom: 1px solid #e5e6eb;
    height: 40px;
    align-items: center;
    font-size: 14px;
    color: #1e2a55;
    grid-column-gap: 12px;

    :deep(.ivu-form-item) {
        margin-bottom:0px;
    }

    .tr-item {
        // margin-top: 3px;
    }


    .operation {
        // margin-bottom: 16px;
    }

    .th {
        font-weight: 800;
    }
    .th:first-child {
        margin-left: 12px;
    }

    .ivu-form-item:first-child {
        margin-left: 12px;
    }

    .ivu-form-item {
        font-weight: 500;
    }
}

.form-box {
    .th-box {
        background-color: transparent;
        align-items: center;
    }
}
// .form-box div.th-box:last-child {
//     margin-bottom: 20px;
// }
</style>
