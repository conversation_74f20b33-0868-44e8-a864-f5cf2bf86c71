<template>
    <ContentCard title="监测数据">
        <BaseForm :model="formData" :label-width="90" @handle-submit="handleSubmit" ref="formRef" :rules="formRules"
            :initFormData="true" @handleReset="handleReset">
            <template #formitem>
                <FormItem :label="options.labelName" prop="deviceCode">
                    <device-drop-down-select :modelId="modelId" v-model="formData.deviceCode" firstSelect
                        @initSelect="(initFormData = JSON.stringify(formData)) && handleSubmit()" @onChange="deviceOnChange"
                        :initSelectData="initSelectData" :valueKey="options.valueKey" :labelKey="options.labelKey"
                        :showKey="options.showKey" />
                </FormItem>
                <FormItem label="选择时间" prop="timeRange">
                    <DatePicker v-model="formData.timeRange" type="daterange" format="yyyy-MM-dd" placeholder="请选择"
                        :editable="false" />
                </FormItem>
            </template>
        </BaseForm>
        <div class="switch" @click="handleSwitch">
            <icon :custom="`iconfont ${switchBtn ? 'icon-list' : 'icon-apps'}`" />
            {{ switchBtn ? '切换表格' : '切换图表' }}
        </div>
        <!-- 图表视图 START -->
        <div class="echart-box" v-show="switchBtn">
            <div class="echart-title">
                <s-label :label="options.labelName" :value="formData.currentName" value-style="font-size: 14px" />
                <s-label :label="options.valueName" :value="formData.currentCode" value-style="font-size: 14px"
                    v-if="options.valueName" />
            </div>
            <div class="echart-overview">
                <no-data v-if="!activedDevice" value="当前暂无数据" class="nodata-box" />
                <div class="device-list-box" v-else>
                    设备列表：
                    <RadioGroup v-model="activedDevice">
                        <Radio :label="item.deviceCode" v-for="(item, i) in deviceList">{{ item.name }}</Radio>
                    </RadioGroup>
                </div>
                <no-data v-if="!tableData?.length" value="当前设备暂无数据" class="nodata-box" />
                <echart-item :option="lineOption" @initEchart="initEchart" v-show="tableData?.length" ref="lineRef" />
            </div>
        </div>
        <!-- 表格视图 START -->
        <base-table :columns="columns" :data="tableData" v-show="!switchBtn"/>
    </ContentCard>
</template>
<script lang="ts">
import { defineComponent, ref, watch, nextTick } from 'vue'
import { monitorService, historicDataPageFromEs } from '@/api/livableManage/monitorService'
import deviceDropDownSelect from '@/components/common/deviceDropDownSelect/index'
import EchartItem from '@/components/common/EchartItem/index'
import { ECOption } from '@/components/common/EchartItem/echart';
import { validateform } from '@/utils/validateform'
import { ManholeCoverInfo } from "@/api/manholeCoverService";
// import { monitorService } from "@/api/livableManage/monitorService";
import Util from '@/utils';
export default defineComponent({
    components: {
        EchartItem,
        deviceDropDownSelect
    },
    props: {
        modelId: {
            type: Number,
            default: 9
        },
        attrList: {
            type: Array,
            default: () => [{
                attr: 'temperature',
                name: '土壤温度',
                unit: '℃'
            },
            {
                attr: 'humidity',
                name: '土壤湿度',
                unit: '%'
            }]
        },
        initSelectData: {
            type: Array,
            default: () => ([])
        },
        // 是否是从es查询
        isRealData: {
            type: Boolean,
            default: false
        },
        options: {
            type: Object,
            default: {
                valueKey: 'deviceCode',
                labelKey: 'sbmc',
                labelName: '设备名称',
                valueName: '设备编号',
                showKey: 'valueKey'
            }
        }
    },
    setup(props) {
        // 切换视图按钮
        const switchBtn = ref<boolean>(true);
        // *********************
        // 折线图
        // *********************
        interface attrInfo {
            attr: string;
            name: string;
            unit: string
        }
        interface deviceDetailInfo {
            name: string;
            deviceCode: string;
            attrList?: attrInfo[];
        }
        const attrList: attrInfo[] = props.attrList as attrInfo[]
        // interface
        // 折线颜色静态数据
        const colorList = ['#F77234', '#165DFF', '#33D1C9', '#fac858', '#ee6666', '#73c0de', '#3ba272', '#fc8452', '#DAFF44', '#FF8EF4']
        // 折线图数据
        const lineOption = ref<ECOption>({
            grid: {
                top: 65,
                left: 5,
                right: 5,
                bottom: 80,
                containLabel: true
            },
            color: colorList,
            legend: {
                show: true,
                bottom: 5,
                icon: 'circle',
                itemHeight: 14,
                itemWidth: 14,
                itemStyle: {
                    borderRadius: 14
                },
                top: 8,
                data: []
            },
            tooltip: {
                trigger: 'axis',
                confine: true,
                axisPointer: {
                }
            },
            dataZoom: [
                {
                    type: 'inside',
                    start: 0,
                    minValueSpan: 10,
                    end: 100
                },
                {
                    start: 0,
                    end: 10,
                    height: 14,
                    bottom: 55,
                    backgroundColor: '#F2F3F5',
                    dataBackground: {
                        lineStyle: {
                            width: 1,
                            color: 'rgba(4, 135, 255, 0.5)'
                        },
                        areaStyle: {
                            color: 'rgba(4, 135, 255, 0.3)'
                        }
                    },
                    handleStyle: {
                        opacity: 0
                    },
                    borderColor: 'rgba(4, 135, 255, 0)',
                    fillerColor: 'rgba(206, 224, 255, 0.5)'
                }
            ],
            xAxis: [
                {
                    type: 'category',
                    axisTick: {
                        show: false
                    },
                    axisLine: {
                        show: true
                    },
                    data: []
                }
            ],
            yAxis: [
                {
                    type: 'value',
                    axisTick: {
                        show: false
                    },
                    axisLine: {
                        show: false
                    }
                }
            ],
            series: [{
                smooth: true
            }]
        })
        // 定义表单数据
        const formData = ref(
            {
                deviceCode: '',
                currentName: '',
                currentCode: '',
                timeRange: [new Date(new Date().getTime() - 86400000), new Date()],
                fieldList: attrList.map((k) => k?.attr)
            }
        )
        // 定义表单验证
        const formRules = ref({
            deviceCode: [validateform.required],
            timeRange: [validateform.requiredArray, validateform.maxDayRang30]
        })
        // 定义表格列头
        const defaultColumns = ref<any[]>([])
        const columns = ref<any[]>([])
        const tableData = ref<historicDataPageFromEs[]>([])
        const formRef = ref<any>()
        // 自定义初始化表单数据
        const initFormData = ref<string>('');
        // 手动重置表单
        const handleReset = () => {
            formData.value = JSON.parse(initFormData.value)
            currentDevice.value = initCurrentDevice.value
            handleSubmit();
        }
        const deviceList = ref<deviceDetailInfo[]>([])
        const activedDevice = ref<string>('')

        // 提交表单
        const handleSubmit = async () => {
            const valid = await formRef.value?.validate?.();
            // 记录当前查询的设备，以便显示
            formData.value.currentName = currentDevice.value.label || currentDevice.value[props.options.labelKey] || ''
            formData.value.currentCode = currentDevice.value.value || currentDevice.value[props.options.valueKey] || ''
            const params = Util.objClone(formData.value)
            // 处理时间数据
            params.queryTimeStart = Util.formatDate(params.timeRange[0], 'yyyy-MM-DD') + ' 00:00:00'
            params.queryTimeEnd = Util.formatDate(params.timeRange[1], 'yyyy-MM-DD') + ' 23:59:59'
            params.order = "asc";
            // 查询设备
            const res = await monitorService.meteorologicalDeviceListAll(+formData.value.currentCode)
            const { data, success }: { success: boolean, data: ManholeCoverInfo[] } = res as unknown as HttpResponse<ManholeCoverInfo[]>
            if (success) {
                deviceList.value = data.map((k: ManholeCoverInfo) => {
                    return {
                        deviceCode: k.deviceCode,
                        name: k.device?.name,
                        attrList: k.physicModel?.filter(m => m.identifier != 'device_id').map(m => ({ attr: m.identifier, name: m.name, unit: m.dataType?.specs?.unit }))
                    }
                }) as deviceDetailInfo[];
                activedDevice.value = deviceList.value[0]?.deviceCode || ''
                getDeviceHisEsm()
                // setTimeout(() => console.log(baseTableRef.value), 2000)
            }


        }
        const getDeviceHisEsm = async () => {
            const params = Util.objClone(formData.value)
            // 处理时间数据
            params.queryTimeStart = Util.formatDate(params.timeRange[0], 'yyyy-MM-DD') + ' 00:00:00'
            params.queryTimeEnd = Util.formatDate(params.timeRange[1], 'yyyy-MM-DD') + ' 23:59:59'
            params.order = "asc"
            params.deviceCode = activedDevice.value
            const activedDeviceObj = deviceList.value.filter(k => k.deviceCode == activedDevice.value)[0]
            const attrList = activedDeviceObj?.attrList || []
            params.fieldList = attrList.map((k) => k.attr)
            const res = await monitorService[props.isRealData ? 'queryHistoricDataFromEs' : 'queryHistoricDataPageFromEs'](params)
            const { data, success }: { success: boolean, data: any } = res as unknown as HttpResponse<recordsResponse<historicDataPageFromEs[]>>
            if (success) { }
            // 设置表格数据
            const list = (data?.records || []).map((k: any) => ({ ...k, deviceCode: activedDevice.value, deviceName: activedDeviceObj.name || '--' }))
            // 深拷贝
            const listCopy = JSON.parse(JSON.stringify(list))
            // 列表以时间逆序
            tableData.value = listCopy.sort((a: historicDataPageFromEs, b: historicDataPageFromEs) => new Date(b.createTime as string).getTime() - new Date(a.createTime as string).getTime())
            // 默认表头
            const _defaultColumns: any = [
                { title: "设备编号", key: "deviceCode", tooltip: true, fixed: 'left' },
                { title: "设备名称", key: "deviceName", tooltip: true, fixed: 'left' },
                { title: "采集时间", key: "createTime", tooltip: true, fixed: 'right', minWidth: 160 }
            ]
            //
            // 设置折线图数据
            const series: any[] = []
            lineOption.value.xAxis![0].data = list.map((item: any) => item.createTime)
            lineOption.value.legend!['data'] = attrList.map((attr, i) => {
                return {
                    icon: 'circle',
                    name: `${attr.name}(${attr.unit})`,
                    itemStyle: {
                        color: '#fff',
                        borderColor: colorList[i],
                        borderWidth: 5
                    }
                }
            })
            attrList.forEach((attr, i) => {
                _defaultColumns.splice(i + 2, 0, { title: `${attr.name}(${attr.unit})`, key: attr.attr, tooltip: true })
                series.push({
                    // showSymbol: false,
                    name: attr.name + '(' + attr.unit + ')',
                    type: 'line',
                    // 图以时间顺序
                    data: tableData.value.map(item => item[attr.attr]).reverse(),
                    smooth: true
                })
            })
            defaultColumns.value = _defaultColumns // 存一份全量表头
            // columns.value = _defaultColumns
            lineOption.value.series = series
        }
        watch(() => activedDevice.value, () => {
            getDeviceHisEsm()
        })
        const selecte = ref<any>()
        // 获取初始化后的echart实例
        const initEchart = (echart: any) => {
            echart.off('legendselectchanged').on('legendselectchanged', ({ selected }: { selected: any }) => {
                selecte.value = selected

            })
        }
        const handleSwitch = () => {
            switchBtn.value = !switchBtn.value
            lineRef.value.handleResize();
            // console.log(baseTableRef.value.handleResize)

            nextTick(() => {
                columns.value = defaultColumns.value.filter(k => !selecte.value || selecte.value[k.title] == true || selecte.value[k.title] == undefined)
            })
        }
        interface deviceInfo {
            label?: string,
            value?: string,
            deviceCode?: string,
            sbmc?: string;
            name?: string;
        }
        const initCurrentDevice = ref<deviceInfo>({}) // 存一份初始选中的设备
        const currentDevice = ref<deviceInfo>({})
        const deviceOnChange = (data: deviceInfo) => {
            if (!initCurrentDevice.value.deviceCode) {
                initCurrentDevice.value = data
            }
            currentDevice.value = data
        }
        const modelId = ref(props.modelId)
        const lineRef = ref()
        const initSelectData = ref()

        watch(() => props.initSelectData, () => {
            initSelectData.value = props.initSelectData
        }, { immediate: true, deep: true })
        const options = ref({})
        watch(() => props.options, () => {
            options.value = props.options
        }, { immediate: true, deep: true })
        return {
            handleSubmit,
            columns,
            formData,
            lineOption,
            switchBtn,
            tableData,
            initEchart,
            deviceOnChange,
            formRef,
            formRules,
            initFormData,
            handleReset,
            modelId,
            lineRef,
            initSelectData,
            options,
            deviceList,
            activedDevice,
            getDeviceHisEsm,
            handleSwitch
        }
    }
})
</script>
<style lang="less" scoped>
@import './index.less';
</style>
