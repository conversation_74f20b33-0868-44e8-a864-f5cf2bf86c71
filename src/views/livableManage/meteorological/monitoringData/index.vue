<template>
    <BreadcrumbCustom>
        <BreadcrumbItem>气象环境</BreadcrumbItem>
        <BreadcrumbItem>监测数据</BreadcrumbItem>
    </BreadcrumbCustom>
    <monitoring-data :init-select-data="monitoringSiteList" :is-real-data="true" :model-id="0" :attr-list="[]" :options="{
    valueKey: 'id',
    labelKey: 'name',
    labelName: '监测站点',
    valueName: '',
    showKey: 'labelKey'
}" />
</template>
<script lang="ts">
import { defineComponent, ref } from 'vue'
import MonitoringData from './components/monitoringData.vue'
import { api_getMonitoringSiteHasDeviceList } from '@/api/livableManage/meteorological'
export default defineComponent({
    components: {
        MonitoringData
    },
    setup() {
        // 监测站点列表
        const monitoringSiteList = ref<any>([])
        function getMonitoringSiteList() {
            api_getMonitoringSiteHasDeviceList().then((res: any) => {
                if (res.success) {
                    monitoringSiteList.value = (res.data || []).map((k: any) => ({ ...k, id: `${k.id}` }))
                }
            })
        }
        getMonitoringSiteList()
        return {
            monitoringSiteList
        }
    }
})
</script>
<style lang="less" scoped></style>
