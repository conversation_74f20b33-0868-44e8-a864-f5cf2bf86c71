<template>
    <ContentCard title="监测数据">
        <BaseForm :model="formData" :label-width="90" @handle-submit="handleSubmit" ref="formRef" :rules="formRules"
            :initFormData="true" @handleReset="handleReset">
            <template #formitem>
                <FormItem label="设备编号" prop="deviceCode">
                    <device-drop-down-select :modelId="modelId" v-model="formData.deviceCode" firstSelect
                        @initSelect="(initFormData = JSON.stringify(formData)) && handleSubmit()"
                        @onChange="deviceOnChange" />
                </FormItem>
                <FormItem label="选择时间" prop="timeRange">
                    <DatePicker v-model="formData.timeRange" type="daterange" format="yyyy-MM-dd" placeholder="请选择"
                        :editable="false" />
                </FormItem>
            </template>
        </BaseForm>
        
        <!-- 使用 Flexbox 布局 -->
        <div class="table-operations" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">
            <Button type="primary" @click="handleAdd">新增记录</Button>
            <div class="switch" @click="(switchBtn = !switchBtn) && lineRef.handleResize()" style="display: flex; align-items: center; height: 40px;">
                <icon :custom="`iconfont ${switchBtn ? 'icon-list' : 'icon-apps'}`" />
                <span style="margin-left: 8px;">{{ switchBtn ? '切换表格' : '切换图表' }}</span>
            </div>
        </div>
        
        <!-- 图表视图 START -->
        <div class="echart-box" v-show="switchBtn">
            <div class="echart-title">
                <s-label label="设备名称" :value="formData.currentName" value-style="font-size: 14px" />
                <s-label label="设备编号" :value="formData.currentCode" value-style="font-size: 14px" />
            </div>
            <div class="echart-overview">
                <no-data v-if="!tableData?.length" value="当前设备暂无数据" class="nodata-box" />
                <echart-item :option="lineOption" @initEchart="initEchart" v-show="tableData?.length" ref="lineRef" />
            </div>
        </div>
        <!-- 表格视图 START -->
        <base-table :columns="columns" :data="tableData" v-show="!switchBtn" />
    </ContentCard>

    <!-- 新增弹窗 -->
    <Modal 
        v-model="showAddModal" 
        title="新增记录" 
        :loading="loading"
        @on-ok="handleAddSubmit" 
        @on-cancel="resetAddForm" 
        :width="600"
    >
        <Form :model="addForm" :rules="addFormRules" ref="addFormRef" label-width="90">
            <FormItem label="设备编号" prop="deviceCode">
                <device-drop-down-select 
                    :modelId="modelId" 
                    v-model="addForm.deviceCode"
                    placeholder="请选择设备"
                    style="width: 100%;"
                />
            </FormItem>
            <FormItem label="水压" prop="waterPressure">
                <Input v-model="addForm.waterPressure" placeholder="请输入水压">
                    <template #append>Pa</template>
                </Input>
            </FormItem>
            <FormItem label="水位" prop="waterLevel">
                <Input v-model="addForm.waterLevel" placeholder="请输入水位">
                    <template #append>mm</template>
                </Input>
            </FormItem>
            <FormItem label="应力" prop="stress">
                <Input v-model="addForm.stress" placeholder="请输入应力">
                    <template #append>MPa</template>
                </Input>
            </FormItem>
            <FormItem label="倾斜" prop="tilt">
                <Input v-model="addForm.tilt" placeholder="请输入倾斜度">
                    <template #append>°</template>
                </Input>
            </FormItem>
            <FormItem label="沉降" prop="settlement">
                <Input v-model="addForm.settlement" placeholder="请输入沉降值">
                    <template #append>mm</template>
                </Input>
            </FormItem>
        </Form>
    </Modal>
</template>
<script lang="ts">
import { defineComponent, ref, onMounted } from 'vue'
import { monitorService, monitorData } from '@/api/livableManage/monitorService'
import deviceDropDownSelect from '@/components/common/deviceDropDownSelect/index'
import EchartItem from '@/components/common/EchartItem/index'
import { ECOption } from '@/components/common/EchartItem/echart';
import { validateform } from '@/utils/validateform'
import { MessageError, MessageSuccess } from '@/hooks/message'
import Util from '@/utils';
export default defineComponent({
    components: {
        EchartItem,
        deviceDropDownSelect
    },
    props: {
        mockData: {
            type: Array,
            default: [] //mockData
        },
        dataIsMock: {
            type: Boolean,
            default: false
        },
        sliceIndex: {
            default: 0
        },
        modelId: {
            type: Number,
            default: 9
        },
        attrList: {
            type: Array,
            default: () => [
            // {
            //     attr: 'deviceCode',
            //     name: '设备编码',
            // },
            // {
            //     attr: 'deviceName',
            //     name: '设备名称',
            // },
            {
                attr: 'waterPressure',
                name: '水压',
                // unit: '单位' // 请根据实际单位修改
            },
            {
                attr: 'waterLevel',
                name: '水位',
                // unit: '单位' // 请根据实际单位修改
            },
            {
                attr: 'stress',
                name: '应力',
                // unit: '单位' // 请根据实际单位修改
            },
            {
                attr: 'tilt',
                name: '倾斜',
                // unit: '单位' // 请根据实际单位修改
            },
            {
                attr: 'settlement',
                name: '沉降',
                // unit: '单位' // 请根据实际单位修改
            },
            // {
            //     attr: 'createTime',
            //     name: '创建时间',
            // },
            ]
        }

    },
    setup(props: any, { emit }: any) {
        const loading = ref(false); // 控制 loading 状态
        // 切换视图按钮
        const switchBtn = ref<boolean>(true);
        // *********************
        // 折线图
        // *********************
        interface attrInfo {
            attr: string;
            name: string;
            unit: string
        }
        const attrList: attrInfo[] = props.attrList as attrInfo[]
        // 折线颜色静态数据
        const colorList = ['#F77234', '#165DFF', '#33D1C9', '#fac858', '#ee6666', '#73c0de', '#3ba272', '#fc8452', '#DAFF44', '#FF8EF4']
        // 折线图数据
        const lineOption = ref<ECOption>({
            grid: {
                top: 65,
                left: 5,
                right: 5,
                bottom: 80,
                containLabel: true
            },
            color: colorList,
            legend: {
                show: true,
                bottom: 5,
                icon: 'circle',
                itemHeight: 14,
                itemWidth: 14,
                itemStyle: {
                    borderRadius: 14
                },
                top: 8,
                data: [
                    // { icon: 'circle', name: '土壤温度(℃)', itemStyle: { color: '#fff', borderColor: '#F77234', borderWidth: 5 } },
                    // { icon: 'circle', name: '土壤湿度(%)', itemStyle: { color: '#fff', borderColor: '#165DFF', borderWidth: 5 } },
                ]
            },
            tooltip: {
                trigger: 'axis',
                confine: true,
                axisPointer: {
                }
            },
            dataZoom: [
                {
                    type: 'inside',
                    start: 0,
                    minValueSpan: 10,
                    end: 100
                },
                {
                    start: 0,
                    end: 10,
                    height: 14,
                    bottom: 55,
                    backgroundColor: '#F2F3F5',
                    dataBackground: {
                        lineStyle: {
                            width: 1,
                            color: 'rgba(4, 135, 255, 0.5)'
                        },
                        areaStyle: {
                            color: 'rgba(4, 135, 255, 0.3)'
                        }
                    },
                    handleStyle: {
                        opacity: 0
                    },
                    borderColor: 'rgba(4, 135, 255, 0)',
                    fillerColor: 'rgba(206, 224, 255, 0.5)'
                }
            ],
            xAxis: [
                {
                    type: 'category',
                    axisTick: {
                        show: false
                    },
                    axisLine: {
                        show: true
                    },
                    data: []
                }
            ],
            yAxis: [
                {
                    type: 'value',
                    axisTick: {
                        show: false
                    },
                    axisLine: {
                        show: false
                    }
                }
            ],
            series: [{
                smooth: true
            }]
        })
        // 定义表单数据
        const formData = ref(
            {
                deviceCode: '',
                timeRange: [new Date(new Date().getTime() - 86400000), new Date()],
                currentName: '',
                currentCode: ''
            }
        )
        // 定义表单验证
        const formRules = ref({
            deviceCode: [validateform.required],
            timeRange: [validateform.requiredArray, validateform.maxDayRang30]
        })
        // 定义表格列头
        const defaultColumns = ref<any[]>([])
        const columns = ref<any[]>([])
        const tableData = ref<monitorData[]>([])
        const formRef = ref<any>()
        // 自定义初始化表单数据
        const initFormData = ref<string>('');
        // 手动重置表单
        const handleReset = () => {
            if (props.dataIsMock) {

                emit('on-mock-search')
                return
            }
            formData.value = JSON.parse(initFormData.value)
            currentDevice.value = initCurrentDevice.value
            handleSubmit();
        }
        // 提交表单
        const handleSubmit = async () => {
            const valid = await formRef.value?.validate?.();
            // 记录当前查询的设备，以便显示
            formData.value.currentName = props.dataIsMock ? (currentDevice.value.deviceName || '') : currentDevice.value.label || currentDevice.value.sbmc || ''
            formData.value.currentCode = currentDevice.value.value || currentDevice.value.deviceCode || ''
            const customQueryParams = Util.objClone(formData.value)
            //构造分页参数
            const page = {
                current: 1,
                size: -1
            }
            // 处理时间数据
            customQueryParams.starTime = Util.formatDate(customQueryParams.timeRange[0], 'yyyy-MM-DD') + ' 00:00:00'
            customQueryParams.endTime = Util.formatDate(customQueryParams.timeRange[1], 'yyyy-MM-DD') + ' 23:59:59'
            customQueryParams.order = "asc"
            const params = {
                customQueryParams:customQueryParams,
                page:page
            }
            // 深基坑监测数据查询
            const res = await monitorService.monitoringDataList(params)
            const { data, success }: { success: boolean, data: any } = res as unknown as HttpResponse<recordsResponse<monitorData[]>>

            if (success) {
                // 设置表格数据
                const random = (val: number) => {
                    return Math.round(Math.random() * val);
                }
                const list = (data?.records || [])
                // 深拷贝
                const listCopy = JSON.parse(JSON.stringify(list))
                // 列表以时间逆序
                tableData.value =  listCopy.sort((a: monitorData, b: monitorData) => new Date(b.createTime as string).getTime() - new Date(a.createTime as string).getTime())
                // 默认表头
                const _defaultColumns: any = [
                    { title: "设备编号", key: "deviceCode", tooltip: true, fixed: 'left' },
                    { title: "设备名称", key: "deviceName", tooltip: true, fixed: 'left' },
                    { title: "采集时间", key: "createTime", tooltip: true, fixed: 'right', minWidth: 160 }
                ]
                //
                // 设置折线图数据
                const series: any[] = []
                lineOption.value.xAxis![0].data = list.map((item: any) => item.createTime)
                lineOption.value.legend!['data'] = attrList.map((attr, i) => {
                    return {
                        icon: 'circle',
                        name: `${attr.name}${attr.hasOwnProperty('unit') ? `(${attr.unit})` : ''}`,
                        itemStyle: {
                            color: '#fff',
                            borderColor: colorList[i],
                            borderWidth: 5
                        }
                    }
                })
                attrList.forEach((attr, i) => {
                    _defaultColumns.splice(i + 2, 0, { title: `${attr.name}${attr.hasOwnProperty('unit') ? `(${attr.unit})` : ''}`, key: attr.attr, tooltip: true })
                    series.push({
                        // showSymbol: false,
                        name: `${attr.name}${attr.hasOwnProperty('unit') ? `(${attr.unit})` : ''}`,
                        type: 'line',
                        // 图以时间顺序
                        data: tableData.value.map(item => item[attr.attr] || 0).reverse(),
                        smooth: true
                    })
                })
                defaultColumns.value = _defaultColumns // 存一份全量表头
                columns.value = _defaultColumns
                lineOption.value.series = series

            }
        }
        // 获取初始化后的echart实例
        const initEchart = (echart: any) => {
            echart.off('legendselectchanged').on('legendselectchanged', ({ selected }: { selected: any }) => {
                // 修改legend选中状态 重新渲染表头
                columns.value = defaultColumns.value.filter(k => selected[k.title] == true || selected[k.title] == undefined)
            })
        }
        interface deviceInfo {
            label?: string,
            value?: string,
            deviceCode?: string,
            sbmc?: string,
            deviceName?: string
        }
        const initCurrentDevice = ref<deviceInfo>({}) // 存一份初始选中的设备
        const currentDevice = ref<deviceInfo>({})
        const deviceOnChange = (data: deviceInfo) => {
            if (!initCurrentDevice.value.deviceCode) {
                initCurrentDevice.value = data
            }
            currentDevice.value = data
        }
        const modelId = ref(props.modelId)
        const lineRef = ref()

        // 新增记录相关
        const showAddModal = ref(false)
        const addFormRef = ref()
        const addForm = ref({
            deviceCode: '',
            waterPressure: null,
            waterLevel: null,
            stress: null,
            tilt: null,
            settlement: null
        })

        // 数字校验规则
        const validateNumber = (rule: any, value: any, callback: any) => {
            if (!value) {
                callback(new Error('请输入数值'));
            } else if (isNaN(Number(value))) {
                callback(new Error('请输入数字'));
            } else {
                callback();
            }
        };

        const addFormRules = {
            deviceCode: [{ required: true, message: '请选择设备', trigger: 'change' }],
            waterPressure: [
                { required: true, message: '请输入水压', trigger: 'blur' },
                { validator: validateNumber, trigger: 'blur' }
            ],
            waterLevel: [
                { required: true, message: '请输入水位', trigger: 'blur' },
                { validator: validateNumber, trigger: 'blur' }
            ],
            stress: [
                { required: true, message: '请输入应力', trigger: 'blur' },
                { validator: validateNumber, trigger: 'blur' }
            ],
            tilt: [
                { required: true, message: '请输入倾斜度', trigger: 'blur' },
                { validator: validateNumber, trigger: 'blur' }
            ],
            settlement: [
                { required: true, message: '请输入沉降值', trigger: 'blur' },
                { validator: validateNumber, trigger: 'blur' }
            ]
        }

        const handleAdd = () => {
            showAddModal.value = true
        }

        const resetAddForm = () => {
            addFormRef.value?.resetFields()
        }
        
        const handleAddSubmit = async () => {
            try {
                loading.value = true; // 开始请求时设置 loading
                const valid = await addFormRef.value?.validate()
                if (!valid) {
                    loading.value = false; // 验证失败时重置 loading
                    return
                }
                
                const { data, success } = await monitorService.addMonitoringData(addForm.value) as any
                if (success) {
                    MessageSuccess('添加成功')
                    showAddModal.value = false
                    resetAddForm()
                    handleSubmit()
                }
            } catch (error) {
                MessageError('添加失败')
                console.error('添加失败', error)
            } finally {
                loading.value = false; // 请求结束时重置 loading
            }
        }

        return {
            handleSubmit,
            columns,
            formData,
            lineOption,
            switchBtn,
            tableData,
            initEchart,
            deviceOnChange,
            formRef,
            formRules,
            initFormData,
            handleReset,
            modelId,
            lineRef,
            showAddModal,
            addForm,
            addFormRef,
            addFormRules,
            handleAdd,
            handleAddSubmit,
            resetAddForm,
            loading
        }
    }
})
</script>
<style lang="less" scoped>
@import './index.less';
.table-operations {
    margin-bottom: 16px;
    text-align: right;
}
</style>
