<template>
    <div>
        <BreadcrumbCustom>
            <BreadcrumbItem>火情监测</BreadcrumbItem>
            <BreadcrumbItem to="/fireMonitoring/alarmManage">告警管理</BreadcrumbItem>
            <BreadcrumbItem>详情</BreadcrumbItem>
        </BreadcrumbCustom>
        <detailCard ref="detail" title="基础信息" :src="require('@/assets/images/icon_detail.png')" :is-back-btn="true" @on-back="backPage">
            <div>
                <Row :gutter="80">
                    <Col span="8">
                    <s-label label="告警编号" :value="subObj.code" />
                    </Col>
                    <Col span="8">
                    <s-label label="告警类型" :value="subObj.alarmTypeName">
                    </s-label>
                    </Col>
                    <Col span="8">
                    <s-label label="告警时间" :value="subObj.alarmTime" />
                    </Col>
                </Row>
                <Row :gutter="80">
                    <Col span="8">
                    <s-label label="区域位置" :value="subObj.areaPath" />
                    </Col>
                    <Col span="8">
                    <s-label label="摄像头编号" :value="subObj.deviceCode" />
                    </Col>
                    <Col span="8">
                    <s-label label="摄像头名称" :value="subObj.sbmc" />
                    </Col>
                </Row>
            </div>
        </detailCard>
        <!-- <detailCard title="识别车辆" :src="require('@/assets/images/icon-车.png')">
            <div>
                <Row :gutter="80">
                    <Col span="8">
                    <s-label label="车辆号码" :value="data.carDetailArr[0]" />
                    </Col>
                    <Col span="8">
                    <s-label label="车辆类型" :value="data.carDetailArr[1]" />
                    </Col>
                </Row>
            </div>
        </detailCard> -->
        <detailTab  :video-arr="data.videoArr" :pic-arr="data.picArr"></detailTab>
    </div>
</template>

<script>
import detailTab from './components/detailTab.vue';
export default {
    name: 'AlarmRecordsDetail',
    components: {
        detailTab
    },
    data() {
        return {
            // 告警id
            id: undefined,
            subObj: {},
            data: {
                carDetailArr: [],
                videoArr: [],
                picArr: []
            }

        }
    },
    created() {
        if (this.$route.query.id) {
            this.id = this.$route.query.id
        }
        this.getDetailById(this.id)
    },
    methods: {
        // 得到详情
        getDetailById(id) {
            this.$request(`/fireMonitoring/alarmManagement/${id}`).then(res => {
                if (res.success) {
                    this.subObj = res.data
                    // this.data.carDetailArr = [
                    //     this.subObj.licensePlateNumber,
                    //     this.subObj.vehicleType == '1' ? '渣土车' : '未知'
                    // ]
                    this.data.videoArr = this.subObj.alarmCaptureUrlList.filter((item) => /\.(mp4|mov|avi|wmv|flv|mkv|webm)$/i.test(item))
                    this.data.picArr = this.subObj.alarmCaptureUrlList.filter((item) => !/\.(mp4|mov|avi|wmv|flv|mkv|webm)$/i.test(item))
                }

            })

        },
        backPage() {
            this.$router.push({
                path: '/fireMonitoring/alarmManage'
            })
        }
    }
}
</script>

<style lang="less" scoped>
    .btn {
    position: fixed;
    bottom: 0;
    width: 100%;
    text-align: left;
    background-color: #ffffff;
    text-align: right;
    padding: 8px 40px ;
    left: 0;
    box-shadow: 0px -3px 12px rgba(45, 75, 103, 0.1);
    z-index: 5;

    .ivu-btn {
        min-width: 68px;
        margin-left: 8px;
        margin-right: 8px;
    }
    }
/deep/ .tab-title {
    justify-content: flex-start;
    padding-bottom: 16px;
    height: 48px;
    column-gap: 20px;


}</style>
