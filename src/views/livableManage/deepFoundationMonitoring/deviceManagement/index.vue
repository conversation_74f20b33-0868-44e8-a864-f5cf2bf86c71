<script lang="ts" setup>
import { commonService } from '@/api/commonService'
import TableContentCard from '@/components/global/TableContentCard'
import { deleteDevice, newAddDevice, editDevice } from '@/api/livableManage/deepFoundationDeviceService.js'
import { getCurrentInstance, reactive, onMounted, ref } from 'vue'
import { useRouter } from 'vue-router';
const searchObj = reactive<any>({})
const listCom = ref<any>(null)
interface searchObjType {
    szjd?: string,
    szsq?: string,
    szdywg?: string,
    deviceCode?: string,
    sbmc?: string,
    bsm?: string,
    status?: number,
    useStatus?: number,
    areaLocation?: string
}
function handleSubmit() {
    let params: searchObjType = {}
    params = window.Util.objClone(searchObj)
    if (searchObj.areaLocation) {
        const arr: Array<string> = searchObj.areaLocation.split('/')
        params.szjd = arr[0]
        params.szsq = arr[1]
        params.szdywg = arr[2]
        delete params.areaLocation
    }
    //   console.log(params);
    listCom.value.search(params)
}
onMounted(() => {
    handleSubmit()
})
const tableList = ref<any>([
    { type: 'selection', maxWidth: 40 },
    { title: '设备编号', key: 'deviceCode', tooltip: true },
    { title: '设备名称', key: 'sbmc', tooltip: true },
    { title: '在线状态', slot: 'status', width: 80 },
    { title: '使用状态', slot: 'useStatus', width: 80 },
    { title: '设备标识码', key: 'bsm', tooltip: true, width: 200 },
    { title: '权属单位', key: 'ownerEnterpriseName', tooltip: true },
    { title: '区域位置', key: 'areaPath', tooltip: true, width: 200 },
    { title: '操作', slot: 'action', maxWidth: 100 }
])


const router = useRouter()
function goDetail(id: string) {
    router.push({
        name: 'deepFoundationMonitoring:deviceManagementDetail',
        query: {
            id
        }
    })
}
// 启用停用editDevice
interface DisableType {
    id: number,
    deviceCode: string,
    useStatus?: number
}
async function handleDisable(row: any, label: string) {
    const params: DisableType = {
        id: row.id,
        deviceCode: row.deviceCode
    }
    if (label === '启用') {
        params.useStatus = 1
    } else {
        params.useStatus = 0
    }
    //   let res:any = await editDevice(params)
    let res: any = await commonService.updateUseStatus(params.deviceCode, params.useStatus)
    if (res.success) {
        that?.$Message.success(label + '成功')
        handleSubmit()
    }

}
// 关联设备
let isShowDialog = ref<boolean>(false)
function bindDevice() {
    isShowDialog.value = true
    //   console.log(isShowDialog.value);
}
function handleOk() {
    isShowDialog.value = false
}

// 批量删除
let ids: Array<Number> = []
const that = getCurrentInstance()?.appContext.config.globalProperties
const { $enumeration } = that
function handleDelete() {
    if (ids.length === 0) {
        that?.$Message.warning('最少选择一条数据')
        return
    }
    that?.$Modal.confirm({
        title: '提示',
        content: '您确定要删除选中的数据吗？',
        onOk: async () => {
            let res: any = await deleteDevice(ids)
            if (res.success) {
                that.$Message.success('删除成功')
                handleSubmit()
                ids = []
            }
        }
    })
}
function selectionChange(selected: any) {
    //   console.log(selected);
    ids = selected.map((i: any) => i.id)
}
interface newAddType {
    deviceCode: string,
    useStatus: number
}
async function addSubmit(data: Array<any>) {
    //   console.log(data);
    let params: Array<newAddType> = data.map((i: any) => {
        return {
            deviceCode: i.deviceId,
            useStatus: 1
        }
    })
    let res: any = await newAddDevice(params)
    //   console.log(res);
    if (res.success) {
        that?.$Message.success('关联成功')
        handleSubmit()
    }
}

</script>
<template>
    <BreadcrumbCustom>
        <BreadcrumbItem>深基监测</BreadcrumbItem>
        <BreadcrumbItem>设备管理</BreadcrumbItem>
    </BreadcrumbCustom>
    <ContentCard title="设备管理">
        <BaseForm @handle-submit="handleSubmit" :model="searchObj" inline :label-width="90">
            <template #formitem>
                <FormItem label="设备编号" prop="deviceCode">
                    <Input v-model="searchObj.deviceCode" :maxlength="20" placeholder="请输入" clearable></Input>
                </FormItem>
                <FormItem label="设备名称" prop="sbmc">
                    <Input v-model="searchObj.sbmc" :maxlength="20" placeholder="请输入" clearable></Input>
                </FormItem>
                <FormItem label="区域位置" prop="areaLocation">
                    <AreaSelectTree v-model="searchObj.areaLocation"
                        table-name="livable_deep_foundation_monitoring_device" />
                </FormItem>
                <FormItem label="设备标识码" prop="bsm">
                    <Input v-model="searchObj.bsm" :maxlength="20" placeholder="请输入" clearable></Input>
                </FormItem>
                <FormItem label="在线状态" prop="status">
                    <Select :transfer="false" v-model="searchObj.status" clearable>
                        <Option v-for="(item, index) in $enumeration.onlineStatus" :key="index" :value="index" clearable>
                            {{ item.title }}
                        </Option>
                    </Select>
                </FormItem>
                <FormItem label="使用状态" prop="useStatus">
                    <Select :transfer="false" v-model="searchObj.useStatus" clearable>
                        <Option v-for="(item, index) in $enumeration.useStatus" :key="index" :value="index" clearable>
                            {{ item }}
                        </Option>
                    </Select>
                </FormItem>
            </template>
        </BaseForm>
        <TableContentCard :base-btn="false">
            <template #btn>
                <!-- <Button @click="bindDevice" type="primary">
            设备关联
          </Button> -->
                <deviceSelect v-auth="'environmentMonitoring:deepFoundationMonitoring:deviceManage:add'" :model-id="36"
                    :multiple="true" @on-change="addSubmit" />
                <Dropdown v-auth="['environmentMonitoring:deepFoundationMonitoring:deviceManage:batchDelete']"
                    placement="bottom-start">
                    <Button custom-icon="iconfont icon-caret-down">批量操作</Button>
                    <template #list>
                        <DropdownMenu style="width: 111px">
                            <!-- <DropdownItem v-auth="'environmentMonitoring:GardenSoil:deviceManage:batchUpdate'"批量修改</DropdownItem> -->
                            <DropdownItem v-auth="'environmentMonitoring:deepFoundationMonitoring:deviceManage:batchDelete'"
                                @click="handleDelete">
                                批量删除
                            </DropdownItem>
                        </DropdownMenu>
                    </template>
                </Dropdown>
            </template>
            <baseTable @on-selection-change="selectionChange" :model="searchObj" ref="listCom"
                url="/deepFoundationDevice/list" :columns="tableList">
                <!--    1在线0离线  -->
                <template #status="{ row }">
                    <s-tag v-if="row.status == 1">在线</s-tag>
                    <s-tag v-else background="#F2F3F5" color="#4E627E">离线</s-tag>
                </template>
                <!--    1启用0停用  -->
                <template #useStatus="{ row }">
                    <s-tag v-if="row.useStatus == 1" background="#E8F3FF" color="#165DFF">启用</s-tag>
                    <s-tag v-else background="#FFF7E8" color="#FF7D00">停用</s-tag>
                </template>
                <template #action="{ row }">
                    <LinkBtn size="small" @click="goDetail(row.id)">详情</LinkBtn>
                    <LinkBtn size="small" @click="handleDisable(row, $enumeration.useStatus[row.useStatus === 0 ? 1 : 0])">
                        {{
                            $enumeration.useStatus[row.useStatus === 0 ? 1 : 0] }}
                    </LinkBtn>
                </template>
            </baseTable>
        </TableContentCard>
    </ContentCard>
</template>

<style lang="less" scoped>
.header {
    display: flex;

    .label {
        width: 90px;

    }

    .frame {
        border: 1px solid #165DFF;
        flex: 1;
    }
}

.dialog-form {
    padding: 15px 0;

    .ivu-form-item {
        width: 100%;
    }
}

.operation-btn {
    display: flex;
    justify-content: space-between;
    padding-bottom: 15px;

    div {
        .ivu-btn {
            margin-left: 10px;
        }
    }
}
</style>
