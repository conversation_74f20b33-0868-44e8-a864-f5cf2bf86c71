import { getDate } from "wei-util"
const random = (val:number)=>{
  return Math.round(Math.random() * val);
}
const statusMap = ['异常','正常']
const listData = new Array(88).fill('').map((item,index)=>{
  return {
    deviceCode:'VIR' + ('169890997115' + (index % 9)),
    deviceName:'位移监测' + (index % 9),
    createTime:getDate({day:-index,timeType:'start'}),
    monitoringName:'基坑位移监测第一点',
    monitoringPoint:'位移监测',
    onceChange:random(25),
    sumChange:random(50),
    rateChange:random(10),
    status:statusMap[random(1)]
  }
})
export {listData}