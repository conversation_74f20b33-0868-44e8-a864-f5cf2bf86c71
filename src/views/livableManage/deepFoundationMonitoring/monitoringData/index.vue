<script lang="ts" setup>
import MonitoringData from '../components/monitoringData.vue'
import { ref } from 'vue'
import axios from 'axios'

const attrList = [
    {
        attr: 'waterPressure',
        name: '水压',
        unit: 'Pa' // 请根据实际单位修改
    },
    {
        attr: 'waterLevel',
        name: '水位',
        unit: 'mm' // 请根据实际单位修改
    },
    {
        attr: 'stress',
        name: '应力',
        unit: 'MPa‌' // 请根据实际单位修改
    },
    {
        attr: 'tilt',
        name: '倾斜',
        unit: '度' // 请根据实际单位修改
    },
    {
        attr: 'settlement',
        name: '沉降',
        unit: 'mm' // 请根据实际单位修改
    },
]

const filterListData = ref<any>([])

const clickSearch = async (obj: any) => {
    const params = {
        timeRange: obj.timeRange,
        deviceCode: obj.deviceCode,
        // 其他分页参数
    }
    
    try {
        const response = await axios.post('/monitoringData/list', params)
        filterListData.value = response.data // 根据实际返回数据格式调整
    } catch (error) {
        console.error('查询失败', error)
    }
}
</script>
<template>
    <BreadcrumbCustom>
        <BreadcrumbItem>深基监测</BreadcrumbItem>
        <BreadcrumbItem>数据监测</BreadcrumbItem>
    </BreadcrumbCustom>
    <MonitoringData @on-mock-search="clickSearch" :sliceIndex="2" :model-id="36"
         :isRealData="true" :attrList="attrList" />
</template>

<style lang="less" scoped></style>