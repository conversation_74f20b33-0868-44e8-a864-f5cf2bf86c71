<template>
    <div id="container-polygon">
        <Button type="primary" size="small" class="btn-edit" @click="clickPolygon" v-if="isEdit">
            点击绘制区域
        </Button>
    </div>
</template>
<script>
import Util from "@/utils";
import { isNullOrEmpty } from "@/utils/tool";
import AMapLoader from "@amap/amap-jsapi-loader";

export default {
    props: {
        //
        path: {
            default: () => [],
        },
        centerPoint: {
            type: Array,
            default: () => [],
        },
        isEdit: { default: true },
    },
    data() {
        return {
            AMap: null,
            polygon: null,
            map: null,
            polyEditor: null,
        };
    },
    mounted() {
        this.initMap();
    },
    methods: {
        initMap() {
            new AMapLoader.load({
                key: "ea53a5291f8f7c1f215ba20930ea9866",
                version: "2.0",
                plugins: ["AMap.PolygonEditor"],
            }).then((AMap) => {
                this.AMap = AMap;
                // 初始化地图对象
                this.map = new AMap.Map("container-polygon", {
                    resizeEnable: true,
                    zoom: 16,
                    center: this.centerPoint,
                });
                const icon = new AMap.Marker({
                    position: this.centerPoint,
                });
                this.map.add([icon])
                const baseCircuit = [
                    [this.centerPoint[0] - 0.0001, this.centerPoint[1] + 0.0001],
                    [this.centerPoint[0] + 0.0001, this.centerPoint[1] + 0.0001],
                    [this.centerPoint[0] + 0.0001, this.centerPoint[1] - 0.0001],
                    [this.centerPoint[0] - 0.0001, this.centerPoint[1] - 0.0001],
                ];
                const path = Util.objClone(this.path);
                if (path.length >= 2 && path[0].toString() === path[path.length - 1].toString()) {
                    path.pop(); // 删除最后一个点
                }
                this.polygon = new AMap.Polygon({
                    path: (!isNullOrEmpty(path) && path) || baseCircuit,
                    strokeColor: "#3366FF",
                    strokeOpacity: 1,
                    strokeWeight: 5,
                    draggable: false,
                });
                this.map.add(this.polygon);
                // 将折线添加到地图上
                this.polygon.setMap(this.map);
                this.map.setFitView();
            });
        },
        clickPolygon() {
            console.log(this.AMap.PolygonEditor);
            this.isEditMode = true;
            this.polyEditor = new this.AMap.PolygonEditor(this.map, this.polygon);
            this.polygon?.setOptions({
                draggable: true,
            });
            this.polyEditor.open();
        },
        handleConfirm() {
            return this.polyEditor?.getTarget() || false;
        },
    },
};
</script>

<style lang="less" scoped>
#container-polygon {
    width: 100%;
    height: 400px;
}

.btn-edit {
    position: absolute;
    right: 10px;
    top: 10px;
    z-index: 10;
}
</style>
