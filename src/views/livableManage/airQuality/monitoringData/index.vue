<template>
    <BreadcrumbCustom>
        <BreadcrumbItem>空气质量</BreadcrumbItem>
        <BreadcrumbItem>监测数据</BreadcrumbItem>
    </BreadcrumbCustom>
    <monitoring-data :model-id="11" :is-attr-list-from-es="true" :attr-list="[
            {
                attr: 'pm_25',
                name: 'PM2.5浓度',
                unit: 'ug/m3'
            },
            {
                attr: 'pm_10',
                name: 'PM10浓度',
                unit: 'ug/m3'
            },
            {
                attr: 'air_temperature',
                name: '空气温度',
                unit: '℃'
            },
            {
                attr: 'air_humidity',
                name: '空气湿度',
                unit: '%'
            },
            {
                attr: 'co',
                name: 'CO浓度',
                unit: 'ppb'
            },
            {
                attr: 'so2',
                name: 'SO2浓度',
                unit: 'ppb'
            },
            {
                attr: 'no2',
                name: 'NO2浓度',
                unit: 'ppb'
            },
            {
                attr: 'o3',
                name: 'O3浓度',
                unit: 'ppb'
            }
        ]"
/>
</template>
<script lang="ts">
import { defineComponent, ref } from 'vue'
import MonitoringData from '../../components/monitoringData.vue'
export default defineComponent({
    components: {
        MonitoringData
    }
})
</script>
<style lang="less" scoped>
</style>
