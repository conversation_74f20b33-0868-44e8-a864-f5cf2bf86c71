
.switch {
    height: 32px;
    background: #F3F7FB;
    border-radius: 2px;
    line-height: 32px;
    color: #4E627E;
    width: 110px;
    text-align: center;
    cursor: pointer;
    margin-bottom: 13px;
    float: right;

    &:hover {
        color: @primary-color;
    }
}

/deep/.base-table {
    clear: both;
}

.echart-box {
    height: ~'calc(100vh - 285px)';
    clear: both;

    .echart-title {
        column-gap: 70px;
        display: flex;
        align-items: center;
        line-height: 32px;
        height: 32px;
        background: @table-thead-bg ;
        border-bottom: 1px solid @border-color-split;
        color: @input-placeholder-color;
        padding: 0 24px;

        /deep/.s-label {
            line-height: 34px;

            .value {
                font-size: 16px;
                font-weight: bold;
            }
        }
    }

    .echart-overview {
        height: ~'calc(100% - 32px)';
        // padding: 24px;
    }
}