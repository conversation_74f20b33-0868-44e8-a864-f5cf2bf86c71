<script lang="ts" setup>
import SideDetail from '@/components/global/SideDetail'
import SelectAlarmType from '@/components/common/selectAlarmType/index.vue';
import { reactive, getCurrentInstance, onMounted, ref } from 'vue'
const tableList = reactive<Array<Object>>([
    { title: '车牌号', slot: 'carNo', tooltip: true, minWidth: 100 },
    { title: '设备编号', key: 'deviceCode', tooltip: true, minWidth: 160 },
    { title: '告警类型', key: 'alarmTypeName', minWidth: 80 },
    { title: '告警等级', slot: 'level', minWidth: 80 },
    { title: '是否推送', slot: 'pushStatus', minWidth: 80 },
    { title: '电子围栏', key: 'areaPath', tooltip: true, minWidth: 180 },
    { title: '告警时间', key: 'alarmTime', tooltip: true, width: 160 },
    { title: '操作', slot: 'action', width: 80 }
])
const searchObj = reactive<Object>({
    deviceCode: '',
    alarmType: '',
    level: '',
    pushStatus: '',
    carDTO: {
        carNo: ''
    },
    modelIds: ['15']
})
const list = ref()
const { $Util, $enumeration } = getCurrentInstance()?.appContext.config.globalProperties
function submit() {
    // console.log(searchObj);
    const params = $Util.objClone(searchObj)
    list.value.search(params)
}
onMounted(() => {
    submit()
})
let isShow = ref<Boolean>(false)
function handleLook(id: String) {
    getDetailInfo(id).then(res =>{
        isShow.value = true
    })
}

function confirm() {
    isShow.value = false
}
function cancel() {
    isShow.value = false
}

// 获取详情信息
const detailData = ref<Object>({})
async function getDetailInfo(id: String) {
    await $Util.request('/carAlarm/' + id).then((res: any) => {
        if (res.success) {
            detailData.value = res.data
        }
    })
}
const contentList = reactive<Array<Object>>([
    { label: '告警编号：', content: '', key: 'code' },
    { label: '车牌号：', content: '', key: 'carDTO.carNo' },
    { label: '设备编号：', content: '', key: 'deviceCode' },
    { label: '设备名称：', content: '', key: 'sbmc' },
    { label: '告警类型：', content: '', key: 'alarmType' },
    { label: '告警等级：', content: '', key: 'level' },
    { label: '告警详情：', content: '', key: 'content' },
    { label: '是否推送：', content: '', key: 'pushStatus' },
    { label: '告警时间：', content: '', key: 'alarmTime' }
])
</script>
<template>
    <BreadcrumbCustom>
        <BreadcrumbItem>电子围栏</BreadcrumbItem>
        <BreadcrumbItem>告警管理</BreadcrumbItem>
    </BreadcrumbCustom>
    <ContentCard title="告警管理">
        <BaseForm :model="searchObj" :label-width="100" @handle-submit="submit">
            <template #formitem>
                <FormItem label="设备编号" prop="deviceCode">
                    <Input v-model="searchObj.deviceCode" placeholder="请输入" clearable></Input>
                </FormItem>
                <FormItem label="车牌号" prop="carDTO.carNo">
                    <Input v-model="searchObj.carDTO.carNo" placeholder="请输入" clearable></Input>
                </FormItem>
                <FormItem label="告警类型" prop="alarmType">
                    <!-- <dict-drop-down-select code="ele_fence_alarm_type" v-model="searchObj.alarmType" /> -->
                    <select-alarm-type v-model="searchObj.alarmType" :param="{modelId: 15, type: 2}" />
                </FormItem>
                <FormItem label="告警等级" prop="level">
                    <Select v-model="searchObj.level" :transfer="false" placeholder="请选择" clearable>
                        <Option
                            v-for="(item, index) in $enumeration.alarmGrade.filter((i: String, index: Number) => index != 0)"
                            :key="index" :value="index + 1">
                            {{ item }}
                        </Option>
                    </Select>
                </FormItem>
                <FormItem label="是否推送" prop="pushStatus">
                    <Select :transfer="false" placeholder="请选择" v-model="searchObj.pushStatus" clearable>
                        <Option v-for="(item, index) in $enumeration.isPush" :key="index" :value="index">
                            {{ item }}
                        </Option>
                    </Select>
                </FormItem>
            </template>
        </BaseForm>
        <TableContentCard :base-btn="false">
            <template #btn>
            </template>
            <baseTable ref="list" url="/carAlarm/list" :columns="tableList">
                <template #carNo="{ row }">
                    <TooltipAutoShow :content="row.carDTO?.carNo" />
                </template>
                <!-- <template #alarmType="{ row }">
                    <dict-label code="ele_fence_alarm_type" :value="row.alarmType"/>
                </template> -->
                <template #level="{ row }">
                    {{ $enumeration.alarmGrade[row.level] }}
                </template>
                <template #pushStatus="{ row }">
                    <!-- {{ $enumeration.isPush[row.pushStatus] }} -->
                    <pushStatus :value="row.pushStatus"/>
                </template>
                <template #action="{ row }">
                    <LinkBtn size="small" @click="handleLook(row.id)">
                        查看
                    </LinkBtn>
                </template>
            </baseTable>
        </TableContentCard>
    </ContentCard>
    <SideDetail :data="detailData" @on-cancel="cancel" @on-confirm="confirm" :contentList="contentList" :show="isShow" :model-id="15" title="告警详情">
    </SideDetail>
</template>

<style lang="less" scoped></style>
