<template>
    <div>
        <BreadcrumbCustom>
            <BreadcrumbItem>电子围栏</BreadcrumbItem>
            <BreadcrumbItem to="/electronicFenceManagement/locationDeviceManagement">定位设备管理</BreadcrumbItem>
            <BreadcrumbItem>详情</BreadcrumbItem>
        </BreadcrumbCustom>
        <Form ref="addForm" :model="subObj" :rules="rules" label-position="top">
            <detailCard
                ref="detail"
                title="基础信息"
                :src="require('@/assets/images/icon_detail.png')"
                :isBackBtn="true"
                :isEditBtn="false"
                :loading="m.loading"
                @on-submit="submitFun"
                @on-edit="editChange"
                @on-back="backPage"
                :class="[isEdit ? 'edit-card' : '' ]"
            >
                <infoShow v-if="!isEdit" :subObj="m.device" />
                <editBox v-if="isEdit" :detailObj="m.device" ref="edit" />
            </detailCard>
        </Form>
        <detailCard
            title="设备流水"
            :src="require('@/assets/images/icon_detail.png')"
            v-show="!isEdit"
        >
            <deviceFlow :deviceCode="m.device.deviceCode" />
        </detailCard>
    </div>
</template>

<script>
import editBox from "./components/editBox";
import infoShow from "./components/infoShow";
import deviceFlow from './components/deviceFlow'
import { DetailMix } from "./components/edit-mixin";
export default {
    name: 'detail',
    components: {
        editBox,
        infoShow,
        deviceFlow
    },
    mixins: [DetailMix],
    data() {
        return {
            isEdit: false, // 是否编辑
            detailId: ''
        }
    },
    created() {
        if (this.$route.query.id) {
            this.detailId = this.$route.query.id
            this.getDetailById(this.$route.query.id)
        }
    },
    methods: {
        submitFun() {
            this.submitAjax('put')
        },
        editChange(flag) {
            this.isEdit = flag
            if (this.isEdit) {
                this.init()
            }
        }
    }
}
</script>

<style lang="less" scoped>
/deep/ .tab-title {
    justify-content: flex-start;
    padding-bottom: 16px;
    height: 48px;
    column-gap: 20px;


}
</style>
