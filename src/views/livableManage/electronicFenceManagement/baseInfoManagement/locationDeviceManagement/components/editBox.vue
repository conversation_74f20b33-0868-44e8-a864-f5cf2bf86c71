<template>
    <Row :gutter="80">
        <Col span="8">
            <FormItem label="设备编号">
                <Input v-model="detailObj.deviceCode" placeholder="" disabled></Input>
            </FormItem>
        </Col>
        <Col span="8">
            <FormItem label="车牌号">
                <Select v-model="subObj.carNo" clearable filterable placeholder="请选择">
                    <Option v-for="(item, index) in m.carList" :value="item.carNo" :key="item.id">
                        {{ item.carNo }}
                    </Option>
                </Select>
            </FormItem>
        </Col>
        <Col span="8">
            <FormItem label="设备名称">
                <Input :model-value="detailObj.deviceDTO?.name" placeholder="" disabled></Input>
            </FormItem>
        </Col>
        <Col span="8">
            <FormItem label="设备型号">
                <Input :model-value="detailObj.deviceDTO?.extendInfo?.sbxh" placeholder="" disabled></Input>
            </FormItem>
        </Col>
        <Col span="8">
            <FormItem label="设备标识码">
                <Input :model-value="detailObj.deviceDTO?.extendInfo?.bsm" placeholder="" disabled></Input>
            </FormItem>
        </Col>
        <Col span="8">
            <FormItem label="在线状态">
                <Input :model-value="$enumeration.deviceStateList[detailObj.deviceDTO?.status]" placeholder="" disabled></Input>
            </FormItem>
        </Col>
        <Col span="8">
            <FormItem label="使用状态">
                <Input :model-value="$enumeration.deviceUesState[detailObj.useStatus]" placeholder="" disabled></Input>
            </FormItem>
        </Col>
    </Row>
</template>

<script>
import { EditMix } from "./edit-mixin";
export default {
    name: 'editBox',
    mixins: [EditMix],
    data() {
        return {}
    },
    methods: {}
}
</script>

<style lang="less" scoped>

</style>
