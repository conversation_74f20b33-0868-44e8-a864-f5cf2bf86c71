<template>
    <div>
        <Row :gutter="80">
            <Col span="8">
                <s-label label="设备编号" >
                    <template #value><span style="font-size: 16px;font-weight: 600;">{{subObj.deviceCode}}</span></template>
                </s-label>
            </Col>
            <Col span="16">
                <s-label label="车牌号">
                    <template #value><span style="font-weight: 600;font-size: 16px;">{{subObj.carDTO?.carNo}}</span></template>
                </s-label>
            </Col>
        </Row>
        <Row :gutter="80">
            <Col span="8">
                <s-label label="设备名称" :value="subObj.deviceDTO?.name" />
            </Col>
            <Col span="8">
                <s-label label="设备型号" :value="subObj.deviceDTO?.extendInfo?.sbxh" />
            </Col>
            <Col span="8">
                <s-label label="在线状态">
                    <template #value>
                        <onlineStatus :value="subObj.deviceDTO?.status" />
                    </template>
                </s-label>
            </Col>
            <Col span="8">
                <s-label label="设备标识码" :value="subObj.deviceDTO?.extendInfo?.bsm" />
            </Col>
            <Col span="8">
                <s-label label="使用状态">
                    <template #value>
                        <useStatus :value="subObj.useStatus" />
                    </template>
                </s-label>
            </Col>
        </Row>
    </div>
</template>

<script>
export default {
    name: 'infoShow',
    props: {
        subObj: { default() { return {} } }
    },
    data() {
        return {}
    },
    methods: {
        getLightingType() {
            let obj = this.$enumeration.lightingType.find(item => item.value == this.subObj.lightingType)
            return obj ? obj.name : ''
        },
    }
}
</script>

<style lang="less" scoped>

</style>
