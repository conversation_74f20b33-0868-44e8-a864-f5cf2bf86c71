<template>
    <div>
        <Form class="search-box">
            <Row>
            <Col span="12">
                <FormItem label="时间范围" prop="time" :label-width="80">
                <DatePicker v-model="searchObj.time" @on-change="changeTime" @on-clear="clearTime" @on-ok="search" transfer type="datetimerange" style="width: 320px" placeholder="请选择" :editable="false" />
                </FormItem>
            </Col>
            </Row>
        </Form>
        <base-table ref="flowTb" :columns="columns" url="/monitor/queryHistoricDataFromEsPage">
</base-table>
    </div>
</template>

<script>
export default {
    name: 'DeviceFlow',
    props: {
        deviceCode: { default: '' }
    },
    data() {
        return {
            searchObj: {
                time: [],
                deviceCode: this.deviceCode,
                queryTimeStart: '',
                queryTimeEnd: ''
            },
            columns: [],
        }
    },
    watch: {
        deviceCode() {
            this.searchObj.deviceCode = this.deviceCode
            this.getAttrData()
        }
    },
    created() {
        this.initTime()
        this.getAttrData()
    },
    methods: {
        initTime() {
            let arr = this.$Util.getDateRangeS(new Date(), 'cd', 'YYYY-MM-DD HH:mm:ss', false, 2)
            this.changeTime(arr)
        },
        getAttrData() {
            if (!this.searchObj.deviceCode) {
                return
            }
            this.$request('/monitor/getPhysicModel', { deviceCode: this.searchObj.deviceCode }, 'post').then(res => {
                if (res.success) {
                    let columns = [
                        { title: '上报日期', key: 'createTime', tooltip: true },
                        { title: '经度', key: 'longitude', tooltip: true },
                        { title: '纬度', key: 'latitude', tooltip: true }
                    ]
                    let properties = []
                    if (res.data && res.data.properties) {
                        properties = res.data.properties
                    }
                    columns.forEach(item => {
                        if (!item.key) {
                            let obj = properties.find(cit => item.title === cit.name)
                            if (obj) {
                                item.key = obj.identifier
                                item.dataType = obj.dataType
                            }
                        }
                    })
                    this.columns = columns
                    this.search()
                }
            })
        },
        clearTime() {
            this.initTime()
            this.search()
        },
        changeTime(val) {
            if (val && val[0]) {
                let sArr = val[0].split(' ')
                if (!sArr[1]) { // 如果为空，加上时分秒
                    val[0] = sArr[0] + ' 00:00:00'
                }
                let eArr = val[1].split(' ')
                if (!eArr[1] || (eArr[1] === '00:00:00')) {
                    // 如果为空或者通过日期修改时，时分秒改为' 23:59:59'
                    val[1] = eArr[0] + ' 23:59:59'
                }
            }
            this.searchObj.time = val
            this.searchObj.queryTimeStart = val[0]
            this.searchObj.queryTimeEnd = val[1]
        },
        search() {
            if (!this.searchObj.deviceCode || !this.searchObj.time[0]) {
                return
            }
            let param = this.$Util.objClone(this.searchObj)
            delete param.time
            this.$refs.flowTb.search(param)
        }
    }
}
</script>

<style lang="less" scoped>

</style>
