import { validateform } from '@/utils/validateform'
const defaultSubObj = {
    carNo: '',
}

export const EditMix = {
    props: {
        detailObj: { default() { return {} } }
    },
    data() {
        return {
            m: {
                currentFacility: {}, // 当前选择的设施
                carList: []
            },
            subObj: this.$Util.objClone(defaultSubObj)
        }
    },
    computed: {
    },
    watch: {
        subObj: {
            handler(obj) { },
            deep: true
        }
    },
    created() {

    },
    methods: {
        init(data = {}) {
            this.subObj = data
            this.getCarList()
        },
        getCarList() {
            let param = {
                "page": {
                    "current": 1,
                    "size": -1
                },
                "customQueryParams": {
                    "bind":false
                }
            }
            this.$request('/livableCar/getPage', param, 'post').then(res => {
                if (res.success) {
                    this.m.carList = res.data.records
                    if (this.subObj.carNo) {
                        this.m.carList.unshift({
                            carNo: this.subObj.carNo,
                            id: -1
                        })
                    }
                }
            })
        }
    }
}

export const DetailMix = {
    data() {
        return {
            m: {
                loading: false,
                device: {},
                devicePropertyStatusList: []
            },
            subObj: this.$Util.objClone(defaultSubObj),
            rules: {
            }
        }
    },
    created() {
    },
    methods: {
        formatDate(param, key) {
            if (param[key]) {
                param[key] = this.$Util.formatDate(param[key])
            }
        },
        submitAjax(met) {
            this.$refs['addForm'].validate((valid) => {
                if (valid) {
                    this.m.loading = true
                    let param = this.$Util.objClone(this.subObj)
                    console.log(param)
                    this.$request('/livablePositionDevice', param, met).then(res => {
                        if (res.success) {
                            this.$Message.success('提交成功')
                            if (met === 'put') {
                                this.$refs.detail.handleEdit(false)
                                this.getDetailById(param.id)
                            } else {
                                this.backPage()
                            }
                        }
                    }).finally(() => {
                        this.m.loading = false
                    })
                }
            })
        },
        // 得到详情
        getDetailById(id) {
            this.$request(`/livablePositionDevice/${id}`).then(res => {
                let data = res.data
                let subObj = this.$Util.objClone(defaultSubObj)
                this.m.device = this.$Util.objClone(data)
                for (let k in subObj) {
                    if (data[k] || data[k] === 0) {
                        subObj[k] = data[k]
                    }
                }
                subObj.id = data.id
                this.subObj = subObj
            })
        },
        init() {
            this.$nextTick(() => {
                try {
                    this.$refs.edit.init(this.subObj)
                } catch { }
            })
        },
        backPage() {
            if(this.$route.query.type == 2){
                this.$store.dispatch('goBack',this.$route.query.type)
             }else if(this.$route.query.type == 3){
                this.$store.dispatch('goBack',this.$route.query.type)
             }
            this.$router.back()
        }
    }
}
