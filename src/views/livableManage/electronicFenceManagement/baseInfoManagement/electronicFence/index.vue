<template>
    <BreadcrumbCustom>
        <BreadcrumbItem>电子围栏</BreadcrumbItem>
        <BreadcrumbItem>车辆轨迹查看</BreadcrumbItem>
    </BreadcrumbCustom>
    <Card dis-hover :bordered="false" class="map-cont" id="fullscreen">
        <div class="face-plate-cont electronic-fence">
            <div class="left-plate plate-bg">
                <div class="name">
                    车辆筛选
                </div>
                <Input suffix="ios-search" placeholder="请输入车牌" clearable v-model="searchTreeText" />
                <Checkbox class="check-all" :indeterminate="indeterminate" :model-value="checkAll"
                    @click.prevent="handleCheckAll">
                    全选
                </Checkbox>
                <div class="scorll-map-cont">
                    <CheckboxGroup v-model="checkAllGroup" @on-change="checkAllGroupChange">
                        <div class="vehicle-box" v-for="(item, index) in deviceList" :key="index">
                            <Checkbox :label="item.id" class="vehicle-checkbox">
                                <div class="vehicle-item">
                                    <div class="vehicle-name">
                                        <div class="carNo">
                                            <Icon custom="iconfont icon-chepai" class="chepai" />{{ item.carNo }}
                                        </div>
                                        <s-tag :background="item.device?.status == 1 ? '#E8FFEA' : '#F2F3F5'"
                                            :color="item.device?.status == 1 ? '#00B42A' : '#4E627E'">
                                            {{ item.device?.status == 1 ? '在线' : '离线' }}
                                        </s-tag>
                                    </div>
                                    <div class="vehicle-time">
                                        <!-- <div class="car-type">{{ item.name }}&nbsp;-</div> -->
                                        <div class="car-category"> {{ store.getters.dictionary.base_obj_vehicle_type[item.carCategory ||
                                            0] }}
                                        </div>
                                    </div>
                                </div>
                            </Checkbox>
                            <div class="view-locus" :class="activeTrajectoryNo == item.carNo ? 'actived' : ''"
                                @click="activeTrajectoryNo = (activeTrajectoryNo == item.carNo ? '' : item.carNo) || ''">
                                查看历史轨迹
                            </div>
                        </div>
                    </CheckboxGroup>
                </div>

                <Page :total="totalSize" size="small" page-size="7" class="min-page" simple
                    @on-change="getLivableCarGetPage" show-total />
            </div>
            <div class="right-plate plate-bg" v-show="activedObj.id ? true : false">
                <device-detail :actived-obj="activedObj" />
            </div>
            <div class="history-select plate-bg" v-show="activeTrajectoryNo ? true : false">
                <div class="name">
                    <div class="carNo">{{ activeTrajectoryNo }}</div>&nbsp;- 统计日期
                </div>
                <DatePicker v-model="timeRange[0]" type="datetime" format="yyyy-MM-dd HH:mm:ss" placeholder="请选择起始日期" :editable="false" />
                <DatePicker v-model="timeRange[1]" type="datetime" format="yyyy-MM-dd HH:mm:ss" placeholder="请选择结束日期" :editable="false" />
                <div class="view-locus-detail" :class="!timeRange[0] || !timeRange[1] ? 'disabled' : ''"
                    @click="getCarTrackList">
                    查看轨迹
                </div>
            </div>
        </div>
        <div id="container-electornic" class="container-map" :class="fullFlag ? 'full-map' : ''"></div>
    </Card>
</template>
<script lang="ts">
import AMapLoader from '@amap/amap-jsapi-loader';
import { defineComponent, nextTick, onMounted, Ref, ref, watch } from 'vue'
import { launchIntoFullscreen, exitFullscreen } from '@/utils/tool'
import { electronicFenceService } from '@/api/livableManage/electronicFenceService'
import { livableCarInfo } from '@/api/livableManage/type'
import deviceDetail from './components/deviceDetail.vue'
import { enumeration } from '@/config/enumeration'
import Util from '@/utils/index';
import { Message } from 'view-ui-plus'
import useDebounce from "@/hooks/useDebounce";
import { getMarkerContent } from '@/components/common/mapAreaTreeSelect/markercontent'
import { useStore } from 'vuex';
export default defineComponent({
    components: {
        deviceDetail
    },
    setup() {
        const store = useStore();
        // *********************
        // 地图
        // *********************
        // 初始化图标
        const map = ref<any>(null);
        const Amap = ref<any>(null);
        const carMarker = ref()
        const positionMarker = ref()
        const ininMap = () => {
            AMapLoader.load({
                key: 'ea53a5291f8f7c1f215ba20930ea9866',
                version: '2.0'
            }).then((AMap) => {
                Amap.value = AMap
                map.value = new AMap.Map('container-electornic', {
                    resizeEnable: true,
                    // mapStyle: 'https://geohub.amap.com/mapstyle/clone?id=20d3f4f1e43c80b9c68c30fb8a1ab94b',
                    zoom: 16,
                    center: [114.61, 30.45]
                })
                // 创建车辆图标
                const carIcon = new AMap.Icon({
                    size: new AMap.Size(34, 64),
                    image: require('./images/icon_electronicFence_008.png'),
                    imageSize: new AMap.Size(34, 64),
                    imageOffset: new AMap.Pixel(0, 0)
                });
                carMarker.value = new Amap.value.Marker({
                    icon: carIcon,
                    offset: new Amap.value.Pixel(-34, 0),
                    autoRotation: true,
                    angle: -90,
                });
                // 创建起终图标
                positionMarker.value = [
                    new Amap.value.Marker({
                        icon: new AMap.Icon({
                            size: new AMap.Size(30, 46),
                            image: require('./images/icon_electronicFence_006.png'),
                            imageSize: new AMap.Size(30, 46),
                        }),
                        offset: new Amap.value.Pixel(-15, -40),
                    }),
                    new Amap.value.Marker({
                        icon: new AMap.Icon({
                            size: new AMap.Size(30, 46),
                            image: require('./images/icon_electronicFence_007.png'),
                            imageSize: new AMap.Size(30, 46)
                        }),
                        offset: new Amap.value.Pixel(-15, -40),
                    })
                ]

                nextTick(() => {
                    getLivableCarGetPage(1)
                })
            })
        }
        // 更新地图上的图标
        const updateMapIcon = (data: livableCarInfo[]) => {
            // 清除地图上的图标
            if (!map.value) return;
            map.value.clearMap();
            data.forEach((ele: livableCarInfo) => {
                if (!ele.device?.gdx || !ele.device?.gdy) return;
                const content = getMarkerContent(ele, activedObj.value.id, ele.alarmCount ? '' : 'hide-tag', (d: livableCarInfo) => {
                    return require('./images/icon_electronicFence_010.png')
                }, 'id', ele.alarmCount)
                const icon = new Amap.value.Marker({
                    position: [ele.device.gdx, ele.device.gdy],
                    content,
                    offset: new Amap.value.Pixel(-26, -26),
                    extData: ele.id
                });
                icon.on('click', (e: any) => {
                    if (activedObj.value.id && activedObj.value.id == e.target.getExtData()) {
                        activedObj.value = {}
                    } else {
                        activedObj.value = ele
                    }
                })
                icon.setMap(map.value)
            })

            nextTick(() => {
                map.value?.setFitView();
            })

        }

        // 地图全屏
        const fullFlag: Ref<boolean | null> = ref(false)
        const handleFullScreen = () => {
            const element = document.getElementById('fullscreen');
            if (!element) return
            // false是进入全屏状态
            if (fullFlag.value) {
                // 退出全屏
                exitFullscreen(element);
            } else {
                launchIntoFullscreen(element);
            }
            fullFlag.value = !fullFlag.value
        }
        // *********************
        // 地图轨迹
        // *********************
        // 查看历史轨迹
        const activeTrajectoryNo = ref<string>('')
        // 监听查询轨迹的车辆
        watch(activeTrajectoryNo, () => {
            if (!activeTrajectoryNo.value && !checkAllGroup.value.length) {
                indeterminate.value = false
                handleCheckAll()
            }

        })
        const timeRange = ref<Date[]>([
            new Date(new Date().setHours(0, 0, 0, 0)),
            new Date()
        ])
        const getCarTrackList = async () => {
            if (!timeRange.value[0] || !timeRange.value[1]) return
            if (timeRange.value[0].getTime() > timeRange.value[1].getTime()) {
                Message.error('开始时间不能大于结束时间')
                return
            }
            const paramData = {
                queryTimeStart: Util.formatDate(timeRange.value[0]),
                queryTimeEnd: Util.formatDate(timeRange.value[1]),
            }
            // 重置数据
            checkAllGroup.value = []
            checkAllGroupChange([])
            const res = await electronicFenceService.carTrackList(activeTrajectoryNo.value, paramData);
            // eslint-disable-next-line
            const { data, success }: { success: boolean; data: number[][] } = res as unknown as HttpResponse<number[][]>;
            if (success) {
                handleViewPath(data)
            }
        }
        const handleViewPath = (lineArr: number[][]) => {
            // 重置数据
            // checkAllGroup.value = []
            // map.value.clearMap();
            // 获取轨迹数据
            carMarker.value.setPosition(lineArr[0])
            positionMarker.value[0].setPosition(lineArr[0])
            positionMarker.value[1].setPosition(lineArr[lineArr.length - 1])
            map.value.add([carMarker.value, ...positionMarker.value])
            // 绘制轨迹
            new Amap.value.Polyline({
                map: map.value,
                path: lineArr,
                showDir: true,
                strokeColor: "#00A120",  //线颜色
                strokeOpacity: 1,     //线透明度
                strokeWeight: 8,      //线宽
                // strokeStyle: "solid"  //线样式
            });
            var passedPolyline = new Amap.value.Polyline({
                map: map.value,
                // path: lineArr,
                strokeColor: "#74CB85",  //线颜色
                strokeOpacity: 1,     //线透明度
                strokeWeight: 9,      //线宽
                // strokeStyle: "solid"  //线样式
            });
            carMarker.value.on('moving', (e: any) => {
                passedPolyline.setPath(e.passedPath);
            });
            map.value.setFitView();
            const path = lineArr.map(k => ({ position: new Amap.value.LngLat(k[0], k[1]), duration: 200 }))
            nextTick(() => {
                Amap.value.plugin('AMap.MoveAnimation', function () {
                    carMarker.value.moveAlong(path);
                });
            })
        }
        // 左侧车辆列表
        const searchTreeText = ref(''); // 查询车牌
        watch(searchTreeText, () => {
            useDebounce(() => {
                getLivableCarGetPage(1);
            });
        })
        // 是否全选
        const checkAll = ref(true)
        const indeterminate = ref(false)
        const checkAllGroup = ref<number[]>([])
        const totalSize = ref<number>(0)
        // *********************
        // 左侧车辆列表
        // *********************
        // 选中的车辆id
        const activedObj = ref<livableCarInfo>({})
        watch(() => activedObj.value, () => {
            if (!deviceList.value) return
            const activeDevice = deviceList.value.find(k => k.id == activedObj.value.id)
            if (activeDevice?.device?.gdx) { // 存在坐标 处理地图
                map.value?.setZoomAndCenter(14, [activeDevice?.device?.gdx, activeDevice?.device?.gdy])

            }
            const overlays: any[] = map.value?.getAllOverlays('marker')
            overlays?.forEach(e => {
                if (e.getExtData() == activedObj.value.id) {
                    const content = getMarkerContent(activeDevice, activedObj.value.id, activeDevice!.alarmCount ? '' : 'hide-tag', (d: livableCarInfo) => {
                        return require('./images/icon_electronicFence_010.png')
                    }, 'id', activeDevice!.alarmCount)
                    e.setContent(content)
                } else {
                    const markerInfo = deviceList.value.filter(k => k.id == e.getExtData())[0] || {}
                    const content = getMarkerContent(markerInfo, activedObj.value.id, markerInfo.alarmCount ? '' : 'hide-tag', (d: livableCarInfo) => {
                        return require('./images/icon_electronicFence_010.png')
                    }, 'id', markerInfo.alarmCount)
                    e.setContent(content)
                }
            })
        })
        // 监听选中的设施
        watch(checkAllGroup, () => {
            const selectCar = deviceList.value.filter(item => checkAllGroup.value.includes(item.id || 0))
            if (!checkAllGroup.value.includes(activedObj.value.id || 0)) {
                activedObj.value = {}
            }
            updateMapIcon(selectCar)
        })

        const checkAllGroupChange = (data: any[]) => {
            if (data.length === deviceList.value.length) {
                indeterminate.value = false;
                checkAll.value = true;
            } else if (data.length > 0) {
                indeterminate.value = true;
                checkAll.value = false;
                activeTrajectoryNo.value = ''
            } else {
                indeterminate.value = false;
                checkAll.value = false;
            }
        }
        // 全选方法
        const handleCheckAll = () => {
            if (indeterminate.value) {
                checkAll.value = false;
            } else {
                checkAll.value = !checkAll.value;
            }
            indeterminate.value = false;
            if (checkAll.value) {
                checkAllGroup.value = deviceList.value.map(item => item.id) as number[];
            } else {
                checkAllGroup.value = [];
            }
        }
        // 获取车辆列表
        const deviceList = ref<livableCarInfo[]>([])
        const getLivableCarGetPage = async (current: number) => {
            const params = {
                page: {
                    current,
                    size: 7
                },
                customQueryParams: {
                    carNo: searchTreeText.value || null,
                    bind: true
                }

            }
            const res = await electronicFenceService.livableCarGetPage(params);
            const { data, success }: { success: boolean; data: recordsResponse<livableCarInfo[]> } =
                res as unknown as HttpResponse<recordsResponse<livableCarInfo[]>>;
            if (success) {
                deviceList.value = data.records||[]
                checkAllGroup.value = data.records.map(item => item.id) as number[]
                indeterminate.value = false;
                checkAll.value = true;
                totalSize.value = data.total || 0
                updateMapIcon(data.records)
            }
        }

        onMounted(() => {
            ininMap();
            window.addEventListener('fullscreenchange', e => {
                // 监听到屏幕变化，在回调中判断是否已退出全屏
                const isFull = document.fullscreenElement || document['mozFullScreenElement'] || document['webkitFullscreenElement'];
                fullFlag.value = isFull || false
            })
        })
        return {
            fullFlag,
            handleFullScreen,
            searchTreeText,
            handleCheckAll,
            checkAll,
            checkAllGroup,
            checkAllGroupChange,
            deviceList,
            enumeration,
            activeTrajectoryNo,
            indeterminate,
            getLivableCarGetPage,
            totalSize,
            activedObj,
            handleViewPath,
            getCarTrackList,
            timeRange,
            store
        }
    }
})
</script>
<style lang="less" scoped>
@import '../../../../../styles/mapPage.less';
@import './index.less';
</style>

