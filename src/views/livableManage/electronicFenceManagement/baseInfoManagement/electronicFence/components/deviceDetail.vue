<template>
    <div class="details-container">
        <div class="head-cont" :class="deviceDetail?.device?.status == 1 ? 'online' : ''">
            <div class="icon-img">
                <img src="../images/icon_electronicFence_009.png" class="top-icon" />
            </div>
            <s-tag :color="deviceDetail?.device?.status == 1 ? '#00B42A' : '#1E2A55'"
                :background="deviceDetail?.device?.status == 1 ? '#E8FFEA' : '#F2F3F5'" class="status-tag">
                {{ deviceDetail?.device?.status == 1 ? '在线' : '离线' }}
            </s-tag>
        </div>
        <div class="name">
            {{ deviceDetail?.carNo }}
        </div>
        <div class="code">
            {{ store.getters.dictionary.base_obj_vehicle_type[deviceDetail?.carCategory || 0] }}
        </div>
        <div class="scorll-map-cont">
            <div class="alarm-info-list">
                <s-label label="品牌" :value="deviceDetail?.carBrand" valueStyle="margin-bottom:0px"/>
                <s-label label="名称" :value="deviceDetail?.name" valueStyle="margin-bottom:0px"/>
                <s-label label="参数" :value="deviceDetail?.description" valueStyle="margin-bottom:0px"/>
            </div>
            <div class="device-info-list">
                <s-label label="定位设备" :value="deviceDetail?.device?.code" valueStyle="margin-bottom:0px"/>
                <s-label label="经纬度"
                    :value="`${deviceDetail?.device?.longitude || '-'},${deviceDetail?.device?.latitude || '-'}`" valueStyle="margin-bottom:0px"/>
                <s-label label="末次定位时间" :value="deviceDetail?.device?.lastPushTime" valueStyle="margin-bottom:0px"/>
            </div>
            <div class="plate-tit">
                <img src="../images/icon_manholecover_010.png" class="title-icon" />
                最近报警信息
            </div>
            <Steps :current="2" direction="vertical" size="small">
                <Step v-for="(item, index) in deviceAlarm || []" :key="index" :title="alarmType[item.alarmType]"
                    :content="`${item.content || ''}\n${item.alarmTime || ''}`"></Step>
            </Steps>
            <no-data v-if="!deviceAlarm.length" value="暂无报警信息" />
        </div>
    </div>
</template>
<script lang="ts">
import { defineComponent, ref, watch } from 'vue'
import { ManholeCoverAlarm } from '@/api/manholeCoverService'
import { livableCarInfo } from '@/api/livableManage/type'
import { electronicFenceService } from '@/api/livableManage/electronicFenceService'
import { enumeration } from '@/config/enumeration'
import { useStore } from 'vuex'
export default defineComponent({
    props: {
        activedObj: {
            type: Object,
            default: () => { }
        }
    },
    setup(props, ctx) {
        const store = useStore()
        const alarmType = store.getters.dictionary.ele_fence_alarm_type
        // console.log($store.getters.dictionary.ele_fence_alarm_type)
        // 设备详情
        const deviceDetail = ref<livableCarInfo>()
        //设备告警
        const deviceAlarm = ref<ManholeCoverAlarm[]>([])
        const getManholeMapAlarm = async () => {
            if (!props.activedObj.device?.code) return
            const params = {
                page: {
                    current: 1,
                    size: 3
                },
                customQueryParams: {
                    deviceCode: props.activedObj.device?.code
                }
            }
            const res = await electronicFenceService.carAlarmList(params)
            const { data, success }: { data: recordsResponse<ManholeCoverAlarm[]>, success: boolean } = res as unknown as HttpResponse<recordsResponse<ManholeCoverAlarm[]>>
            if (success) {
                deviceAlarm.value = data.records

            }
        }
        watch(() => props.activedObj, () => {
            deviceDetail.value = props.activedObj
            getManholeMapAlarm()

        })
        return {
            deviceDetail,
            deviceAlarm,
            enumeration,
            alarmType,
            store
        }
    }
})
</script>
