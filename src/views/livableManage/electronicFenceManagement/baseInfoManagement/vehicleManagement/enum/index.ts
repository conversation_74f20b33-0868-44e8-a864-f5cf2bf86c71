export enum VehicleTypeEnum{
    SPRINKLER = 1,
    SUCTION_SEWAGE_TRUCK,
    FECAL_SUCTION_TRUCK ,
    GARBAGE_TRUCK ,
    HIGH_PRESSURE_CLEANING_VEHICLE,
    ROAD_SWEEPER,
    OTHER
}

export const VehicleTypeMap = {
    [VehicleTypeEnum.SPRINKLER]: '洒水车',
    [VehicleTypeEnum.SUCTION_SEWAGE_TRUCK]: '吸污车',
    [VehicleTypeEnum.FECAL_SUCTION_TRUCK]: '吸粪车',
    [VehicleTypeEnum.GARBAGE_TRUCK]: '垃圾车',
    [VehicleTypeEnum.HIGH_PRESSURE_CLEANING_VEHICLE]: '高压清洗车',
    [VehicleTypeEnum.ROAD_SWEEPER]: '扫路车',
    [VehicleTypeEnum.OTHER]: '其他'
}

export enum StatusTypeEnum {
    DISABLE,
    ENABLED
}
export const StatusTypeMap = {
    [StatusTypeEnum.DISABLE]: '禁用',
    [StatusTypeEnum.ENABLED]: '启用'
}
