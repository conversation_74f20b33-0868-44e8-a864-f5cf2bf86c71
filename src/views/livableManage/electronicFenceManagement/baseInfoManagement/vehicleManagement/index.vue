<template>
    <BreadcrumbCustom>
        <BreadcrumbItem>电子围栏</BreadcrumbItem>
        <BreadcrumbItem>车辆管理</BreadcrumbItem>
    </BreadcrumbCustom>
    <ContentCard title="车辆管理" :base-btn="false">
        <BaseForm @handle-submit="onSubmit(listRef)" :model="condition" inline :label-width="90">
            <template #formitem>
                <FormItem label="车牌号" prop="carNo">
                    <Input v-model="condition.carNo" :maxlength="20" placeholder="请输入" clearable></Input>
                </FormItem>
                <FormItem label="车辆类型" prop="carCategory">
                    <dict-drop-down-select code="base_obj_vehicle_type"
                        v-model="condition.carCategory"></dict-drop-down-select>
                </FormItem>
                <FormItem label="运行状态" prop="carStatus">
                    <dict-drop-down-select code="base_obj_vehicle_status"
                        v-model="condition.carStatus"></dict-drop-down-select>
                </FormItem>
            </template>
        </BaseForm>
        <btnCard>
            <!-- <Button type="primary" @click="onClickAddBtn" v-auth="'electronicFence:car:add'" icon="md-add">
                新建
            </Button> -->
            <VehicleSelect v-auth="'electronicFence:car:add'" @on-change="onSubmit(listRef)" />
            <Button icon="ios-trash" @click="onDelete" v-auth="'electronicFence:car:del'">
                删除
            </Button>
        </btnCard>

        <baseTable @on-selection-change="onSelectionChange" :model="condition" ref="listRef" :columns="tableColumn"
            url="/livableCar/getPage">
            <template #carCategory="{ row }">
                {{ store.getters.dictionary.base_obj_vehicle_type[row.carCategory] }}
            </template>
            <template #carStatus="{ row }">
                {{ store.getters.dictionary.base_obj_vehicle_status[row.carStatus] }}
            </template>
            <template #action="{ row }">
                <LinkBtn size="small" @click="goDetail(row)">
                    详情
                </LinkBtn>
            </template>
        </baseTable>
    </ContentCard>
</template>

<script lang="ts" setup>
import TableContentCard from '@/components/global/TableContentCard'
import { getCurrentInstance, onMounted, ref, toRaw } from 'vue';
import {
    StatusTypeEnum, StatusTypeMap,
    VehicleTypeEnum,
    VehicleTypeMap
} from './enum';
import { useRouter } from 'vue-router';
import { useStore } from 'vuex';
import { deleteCar } from '@/api/livableManage/vehicleManagementService';
import { ICarInfo } from './type';
import VehicleSelect from './components/vehicleSelect'

const _ = require('lodash')
const router = useRouter();
const store = useStore();

const that = getCurrentInstance()?.appContext.config.globalProperties;

type BaseTable = InstanceType<typeof baseTable>
const listRef = ref<BaseTable>(null);

// 获取车辆类型
const vehicleOption = Object.keys(VehicleTypeEnum).map(item => {
    return {
        label: VehicleTypeMap[item],
        value: item
    }
})

// 获取状态
const statusOption = Object.keys(StatusTypeEnum).map(item => {
    return {
        label: StatusTypeMap[item],
        value: item
    }
})
// 筛选表单
const condition = ref({
    carNo: '',
    carCategory: '',
    carStatus: ''
})

const onSubmit = async (listRef: any) => {
    const { carNo, carStatus, carCategory } = condition.value
    const params = {
        carNo: _.isEmpty(carNo) ? undefined : carNo,
        carCategory: _.isEmpty(carCategory) ? undefined : +carCategory,
        carStatus: _.isEmpty(carStatus) ? undefined : +carStatus
    }
    listRef.search(params)
}
onMounted(() => {
    onSubmit(listRef.value);
})

// 删除
const selectedIds = ref<(number | undefined)[]>([])

const onSelectionChange = (selectedRow: ICarInfo[] = []) => {
    console.log(selectedRow)
    selectedIds.value = selectedRow.map(item => item.id)
}
const onDelete = () => {
    if (selectedIds.value.length === 0) {
        that && that.$Message.warning('最少选择一条数据')
        return
    }
    that && that.$Modal.confirm({
        title: '提示',
        content: '您确定要删除选中的数据吗',
        onOk: () => {
            const deleteIds = selectedIds.value;
            delAjax(deleteIds)
        }
    })
}

const delAjax = async (ids: (number | undefined)[]) => {
    console.log(ids);
    const params = {
        idList: toRaw(ids).toString()
    }
    const res = await deleteCar(params);
    if (res.success) {
        that?.$Message.success('删除成功')
        selectedIds.value = []
    }
    await onSubmit(listRef.value);
}


const goDetail = (row: ICarInfo) => {
    console.log('去详情')
    const query = {
        carNo: row.carNo
    }
    router.push({ name: 'vehicleManagementDetail', query })
}

// const onClickAddBtn = () => {
//     console.log('add')
//     router.push({ name: 'addVehicle' })
// }


const tableColumn = ref([
    { type: 'selection', width: 40 },
    { title: '车牌号', key: 'carNo', tooltip: true },
    { title: '品牌', key: 'carBrand', tooltip: true },
    { title: '类型', slot: 'carCategory', tooltip: true },
    { title: '车辆参数', key: 'description', tooltip: true },
    { title: '车辆名称', key: 'name', tooltip: true },
    { title: '运行状态', slot: 'carStatus', width: 80 },
    { title: '定位设备', key: 'deviceCode', tooltip: true },
    { title: '操作', slot: 'action', width: 80, lock: true, fixed: true }
])

</script>

<style lang="less" scoped>
.header {
    background-color: #fff;
    margin: 10px;

    .header-top {
        border-bottom: 1px solid;
    }
}
</style>
