<template>
    <BreadcrumbCustom>
        <BreadcrumbItem>电子围栏</BreadcrumbItem>
        <BreadcrumbItem to="/electronicFenceManagement/vehicleManagement">
            车辆管理
        </BreadcrumbItem>
        <BreadcrumbItem>{{isEdit ? '编辑' : '详情'}}</BreadcrumbItem>
    </BreadcrumbCustom>
    <Form :rules="formRules" :model="detailInfo" ref="formRef">
        <detailCard ref="cardRef"
                    title="基础信息"
                    @on-edit="onEdit"
                    @on-submit="onSubmitEdit(formRef)"
                    :src="require('@/assets/images/icon-基础信息.png')"
                    :is-edit-btn="false"
                    :is-back-btn="true"
                    @on-back="router.back()"
        >
            <template v-if="isEdit">
                <Row>
                    <Col span="8">
                        <FormItem label="车牌号" prop="carNo" required>
                            <Input v-model="detailInfo.carNo"
                                   :maxlength="50"
                                   placeholder="请输入"
                                   :disabled="!isEdit"
                                   clearable
                            ></Input>
                        </FormItem>
                    </Col>
                </Row>
                <Row>
                    <Col span="8">
                        <FormItem label="厂家" prop="carFactory">
                            <Input v-model="detailInfo.carFactory" :maxlength="100"
                                   placeholder="请输入"
                                   :disabled="!isEdit"
                                   clearable
                            ></Input>
                        </FormItem>
                    </Col>
                    <Col span="8">
                        <FormItem label="型号" prop="carType">
                            <Input v-model="detailInfo.carType" :maxlength="100" placeholder="请输入"
                                   :disabled="!isEdit"
                                   clearable
                            ></Input>
                        </FormItem>
                    </Col>
                    <Col span="8">
                        <FormItem label="状态" prop="carStatus" required>
                            <Select v-model="detailInfo.carStatus" clearable>
                                <Option v-for="item in statusOption" :value="item.value"
                                        :key="item.value">
                                    {{ item.label }}
                                </Option>
                            </Select>
                        </FormItem>
                    </Col>
                </Row>
                <Row>
                    <Col span="8">
                        <FormItem label="品牌" prop="carBrand">
                            <Input v-model="detailInfo.carBrand" :maxlength="100" placeholder="请输入"
                                   :disabled="!isEdit"
                                   clearable
                            ></Input>
                        </FormItem>
                    </Col>
                    <Col span="8">
                        <FormItem label="类型" prop="carCategory" required>
                            <Select v-model="detailInfo.carCategory" :disabled="!isEdit" clearable>
                                <Option v-for="item in vehicleOption" :value="item.value"
                                        :key="item.value"
                                >
                                    {{ item.label }}
                                </Option>
                            </Select>
                        </FormItem>
                    </Col>
                    <Col span="8">
                        <FormItem label="颜色" prop="carColor">
                            <Input v-model="detailInfo.carColor" :maxlength="100" placeholder="请输入"
                                   :disabled="!isEdit"
                                   clearable
                            ></Input>
                        </FormItem>
                    </Col>
                </Row>
                <Row>
                    <Col span="24">
                        <FormItem label="备注" style="width: 100%" prop="remark">
                            <sTextarea v-model="detailInfo.remark" :disabled="!isEdit"></sTextarea>
                        </FormItem>
                    </Col>
                    <Col span="8"></Col>
                </Row>
            </template>
            <template v-else>
                <Row>
                    <Col span="8">
                        <s-label :bold="true" label="车牌号" :value="detailInfo.carNo" />
                    </Col>
                </Row>
                <Row>
                    <Col span="8">
                        <s-label label="品牌" :value="detailInfo.carBrand"></s-label>
                    </Col>
                    <Col span="8">
                        <s-label label="车辆参数" :value="detailInfo.description"></s-label>
                    </Col>
                    <Col span="8">
                        <s-label label="车辆名称" :value="detailInfo.name"></s-label>
                    </Col>
                </Row>
                <Row>
                    <Col span="8">
                        <s-label label="类型" :value="typeFormatter"></s-label>
                    </Col>
                    <Col span="8">
                        <s-label label="运行状态" :value="statusFormatter"></s-label>
                    </Col>
                </Row>
                <Row>
                    <Col span="24">
                        <s-label label="备注" :value="detailInfo.remark"></s-label>
                    </Col>
                </Row>
            </template>
        </detailCard>
        <detailCard title="设备信息" :src="require('@/assets/images/icon-设备列表.png')">
            <div class="device_information">
                <template v-if="isEdit">
                    <Row>
                        <Col span="8">
                            <FormItem label="设备编号">
                                <Select v-model="detailInfo.deviceCode" clearable>
                                    <Option v-for="item in deviceInformation" :value="item.value"
                                            :key="item.value">
                                        {{item.value}} （{{ item.label }}）
                                    </Option>
                                </Select>
                            </FormItem>
                        </Col>
                    </Row>
                </template>
                <template v-else>
                    <Row>
                        <Col span="8">
                            <s-label label="设备状态" >
                                <template #value>
                                    <template v-if="detailInfo.deviceCode">
                                        <auto-tag type="1" :value="deviceOnlineStatus"> </auto-tag>
                                    </template>
                                    <template v-else>
                                        <span>--</span>
                                    </template>
                                </template>
                            </s-label>
                        </Col>
                        <Col span="8">
                            <s-label label="设备编号" :value="detailInfo.deviceCode"></s-label>
                        </Col>
                        <Col span="8">
                            <s-label label="经纬度">
                                <template #value>
                                <span class="show-map" @click="openMap">
                                    <Icon type="ios-pin-outline" v-if="!isEdit && PositionInfo.objX" />
                                </span>
                                    <span v-if="detailInfo?.device?.longitude">
                                   {{`${detailInfo?.device?.longitude || ''}，${detailInfo?.device?.latitude || ''}`}}
                                </span>
                                    <span v-else>
                                    --
                                </span>
                                </template>
                            </s-label>
                        </Col>
                    </Row>
                    <Row>
                        <Col span="8">
                            <s-label label="更新时间"
                                     :value="deviceUpdateTime"></s-label>
                        </Col>
                    </Row>
                </template>
            </div>
        </detailCard>
    </Form>
    <component :is="componentName" @close-modal="emitCloseModal()" :makers="[PositionInfo]" />
</template>

<script lang="ts" setup>

import { useRoute, useRouter } from 'vue-router';
import { useStore } from 'vuex';
import { computed, getCurrentInstance, markRaw, reactive, ref, toRaw } from 'vue';
import {
    StatusTypeEnum, StatusTypeMap,
    VehicleTypeEnum,
    VehicleTypeMap
} from './enum';
import {
    modifyCar, queryDeviceInformation,
    querySingleCarInfo
} from '@/api/livableManage/vehicleManagementService';
import { ICarDetailInfo, IDeviceOption } from "./type";
import { Form } from "view-ui-plus";
import deviceMarkerMap from '@/components/common/deviceMarkerMap'
import newAdd from "@/views/trafficManage/wisdomRoadBridge/bridgeManagement/newAdd.vue";
import edit from "@/views/trafficManage/wisdomRoadBridge/bridgeManagement/edit.vue";
import detail from "@/views/trafficManage/wisdomRoadBridge/bridgeManagement/detail.vue";

const moment = require('moment');

const route = useRoute();
const router = useRouter();
const store = useStore();
const that = getCurrentInstance()?.appContext.config.globalProperties
type FormRef = InstanceType<typeof Form>;
const formRef = ref<FormRef | null>(null)
type CardRef = InstanceType<typeof detailCard>
const cardRef = ref<CardRef | null>(null);

const PositionInfo = computed(() => {
    return {
        objY: detailInfo.value?.device?.latitude,
        objX: detailInfo.value?.device?.longitude,
    }
})

const detailInfo = ref<ICarDetailInfo>({
    deviceCode: '',
    carNo: '', // 车牌号
    carBrand: '', // 品牌
    carCategory: '', // 类型
    description: '',
    name: '',
    remark: '',
    carStatus: '', // 状态
})
const deviceOnlineStatus = computed(() => {
    return detailInfo.value?.device?.status;
})
// enum => name
const typeFormatter = computed(() => {
    return store.getters.dictionary.base_obj_vehicle_type[detailInfo.value.carCategory]
})
// enum => name
const statusFormatter = computed(() => {
    return store.getters.dictionary.base_obj_vehicle_status[detailInfo.value.carStatus]
})

const deviceUpdateTime = computed(() => {
    return detailInfo.value?.device?.modifyTime
        ? moment(detailInfo.value.device.modifyTime).format('YYYY-MM-DD HH:mm:ss')
        : '';
})


const onInit = async() => {
    const query = route.query
    const params = {
        carNo: query.carNo,
    }
    console.log('edit', params)

    const res = await querySingleCarInfo(params);
    if (res.success && res.data) {
        const {carStatus, carCategory} = <ICarDetailInfo>res.data;
        detailInfo.value = Object.assign({}, res.data, { carStatus: String(carStatus), carCategory: String(carCategory) })
    }
    // 已删除编辑
    // await queryDeviceOption();
}
onInit();

const isEdit = ref<boolean>(false)
const onEdit = () => {
    isEdit.value = !isEdit.value;
}
// 提交编辑
const onSubmitEdit = (formRef:any) => {
    formRef && formRef.validate((valid: boolean) => {
        console.log('表单校验结果', valid)
        if (valid) {
            const params = {
                id: detailInfo.value.id,
                deviceCode: detailInfo.value.deviceCode,
                carNo: detailInfo.value.carNo,
                carType: detailInfo.value.carType,
                carStatus: +detailInfo.value.carStatus,
                carFactory: detailInfo.value.carFactory,
                carCategory: +detailInfo.value.carCategory,
                carBrand: detailInfo.value.carBrand,
                carColor: detailInfo.value.carColor,
                remark: detailInfo.value.remark
            }
            modifyCar(params)
                .then(res => {
                    console.log(res)
                    if (res.success) {
                        console.log('上传成功')
                        that?.$Message.success('修改车辆信息成功')
                        isEdit.value = false;
                        cardRef.value.isEditFlag = false
                    }
                })
                .then(() => {
                    onInit();
                })
        }
    })
}

// 编辑表单校验
const fields = {
    carNo: '车牌号',
    carCategory: '车辆类型',
    carStatus: '状态'
}
const validateRequire = (rule: any, value: number | string, callback: any) => {
    if (!value) {
        callback(new Error(fields[rule.field] + '必须填写'))
    } else {
        callback()
    }
}
const formRules = ref({
    carNo: [{ validator: validateRequire }],
    carCategory: [{ validator: validateRequire }],
    carStatus: [{ validator: validateRequire }]
})


// 车辆类型
const vehicleOption = Object.keys(VehicleTypeEnum).map(item => {
    return {
        label: VehicleTypeMap[item],
        value: item
    }
})
// 车辆状态
const statusOption = Object.keys(StatusTypeEnum).map(item => {
    return {
        label: StatusTypeMap[item],
        value: item
    }
})

// 设备option
const deviceInformation = ref<IDeviceOption[]>([])
/**
 * @Description: 查询设备option
 */
const queryDeviceOption = async () => {
    const params = {
        page: {
            current: 1,
            size: -1 // 传-1 查所有
        },
        customQueryParams: {
            bind: false
        }
    }
    const res = await queryDeviceInformation(params)
    if (res.success) {
        deviceInformation.value = res.data?.records?.map(item => {
            return {
                value: item?.deviceDTO?.code,
                label: item?.deviceDTO?.name
            }
        })
        if(detailInfo.value.device?.code){
            deviceInformation.value.push({
                value: detailInfo.value.device?.code,
                label: detailInfo.value.device?.name
            })
        }
    }
}

const componentName = ref(null)
const componentArr = reactive<any>(markRaw(deviceMarkerMap))
const openMap = () => {
    console.log(componentName.value)
    componentName.value = componentArr;
}
const emitCloseModal = () => {
    componentName.value = null;
}
const test:IPoint[] = [
    {
        objX: 122,
        objY: 122
    }
]

interface IPoint{
    objX: number,
    objY: number,
}
</script>

<style lang="less" scoped>
.status-box {
    color: #4CD263;
}

.lon-and-lat-wrapper {
    display: flex;
}

.lon-and-lat-box {
    min-width: 80px;
    height: 20px;
    line-height: 18px;
    background-color: #F2F2F2;
    border: 1px solid black;
    margin: 0 4px;
    text-align: center;
}

.position-box {
    width: 26px;
    height: 20px;
    margin: 0 4px;
    background-color: #3069EE;
}
/deep/ .ivu-form-item{
    width: 80%;
}
/deep/ .ivu-form-item-label{
    padding-top: 0;
    padding-bottom: 8px;
}
.show-map {
    cursor: pointer;
    font-size: 16px;
    color: #165DFF;
    .ivu-icon {
        font-weight: 700;
    }
}
</style>
