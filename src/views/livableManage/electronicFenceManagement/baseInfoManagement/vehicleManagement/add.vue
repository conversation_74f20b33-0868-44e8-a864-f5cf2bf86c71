<template>
    <BreadcrumbCustom>
        <BreadcrumbItem>电子围栏</BreadcrumbItem>
        <BreadcrumbItem to="/electronicFenceManagement/vehicleManagement">
            车辆管理
        </BreadcrumbItem>
        <BreadcrumbItem>新建</BreadcrumbItem>
    </BreadcrumbCustom>
    <Form :rules="formRules"
          :model="formData"
          ref="formRef"
    >
<!--        flag-->
    <detailCard title="新建车辆" :is-back-btn="true" @on-back="backPage">
            <Title class="car-info" level="5">车辆信息</Title>
            <div class="form-wrapper">
                <Row>
                    <Col span="8">
                        <FormItem label="车牌号" prop="carNo" required>
                            <Input v-model="formData.carNo" :maxlength="50"
                                   placeholder="请输入" clearable />
                        </FormItem>
                    </Col>
                    <Col span="8">
                        <FormItem label="车辆型号" prop="carType">
                            <Input v-model="formData.carType" :maxlength="100" placeholder="请输入"
                                   clearable />
                        </FormItem>
                    </Col>
                    <Col span="8">
                        <FormItem label="厂家" prop="carFactory">
                            <Input v-model="formData.carFactory" :maxlength="100" placeholder="请输入"
                                   clearable />
                        </FormItem>
                    </Col>
                </Row>
                <Row>
                    <Col span="8">
                        <FormItem label="车辆类型" prop="carCategory" required>
                            <Select v-model="formData.carCategory" clearable>
                                <Option v-for="item in vehicleOption" :value="item.value"
                                        :key="item.value">
                                    {{ item.label }}
                                </Option>
                            </Select>
                        </FormItem>
                    </Col>
                    <Col span="8">
                        <FormItem label="品牌" prop="carBrand">
                            <Input v-model="formData.carBrand" :maxlength="100" placeholder="请输入"
                                   clearable />
                        </FormItem>
                    </Col>
                    <Col span="8">
                        <FormItem label="状态" prop="carStatus" required>
                            <Select v-model="formData.carStatus" clearable>
                                <Option v-for="item in statusOption" :value="item.value"
                                        :key="item.value">
                                    {{ item.label }}
                                </Option>
                            </Select>
                        </FormItem>
                    </Col>
                </Row>
                <Row>
                    <Col span="8">
                        <FormItem label="颜色" prop="carColor">
                            <Input v-model="formData.carColor" :maxlength="100" placeholder="请输入"
                                   clearable />
                        </FormItem>
                    </Col>
                    <Col span="8"></Col>
                </Row>
                <Row>
                    <Col span="24">
                        <FormItem label="备注" style="width: 100%" prop="remark">
                            <Input v-model="formData.remark" :maxlength="200" placeholder="请输入"
                                   clearable />
                        </FormItem>
                    </Col>
                </Row>
            </div>
        <Title level="5" class="car-info">设备信息</Title>
        <div class="form-wrapper">
            <Row>
                <Col span="8">
                    <FormItem label="定位设备">
                        <Select v-model="formData.deviceCode" clearable>
                            <Option v-for="item in deviceInformation" :value="item.value"
                                    :key="item.value">
                                {{ item.value }} {{ item.label }}
                            </Option>
                        </Select>
                    </FormItem>
                </Col>
                <Col span="8" />
                <Col span="8" />
            </Row>
            <Row>
                <div class="btn-box">
                    <Button type="primary" @click="onCondirm(formRef)">提交</Button>
                    <Button @click="backPage">取消</Button>
                </div>
            </Row>
        </div>
    </detailCard>
    </Form>
</template>

<script lang="ts" setup>

import { useRouter } from 'vue-router';
import { getCurrentInstance, ref, toRaw } from 'vue';
import {
    ICarAddInfo, IDeviceOption
} from './type';
import { StatusTypeEnum, StatusTypeMap, VehicleTypeEnum, VehicleTypeMap } from './enum';
import { queryDeviceInformation } from '@/api/livableManage/vehicleManagementService';
import { Form } from 'view-ui-plus';

const router = useRouter()

const that = getCurrentInstance()?.appContext.config.globalProperties

type FormRef = InstanceType<typeof Form>;
const formRef = ref<FormRef | null>(null);

const formData = ref<ICarAddInfo>({
    carNo: '',
    carColor: '',
    carStatus: 0,
    carBrand: '',
    carType: '',
    carCategory: 0,
    carFactory: '',
    deviceCode: '',
    remark: ''
})

const onCondirm = (formRef: any) => {
    // formRef && formRef.validate((valid: any) => {
    //     console.log('表单校验结果', valid)
    //     if (valid) {
    //         const params = toRaw(formData.value)
    //         console.log(params)
    //         addCar(params)
    //             .then(res => {
    //                 console.log(res)
    //                 if (res.success) {
    //                     console.log('上传成功')
    //                     that?.$Message.success('新增车辆成功')
    //                     router.back();
    //                 }
    //             })
    //     }
    // })
}



const deviceInformation = ref<IDeviceOption[]>([])
const queryDeviceOption = async() => {
    const params = {
        page: {
            current: 1,
            size: -1 // 传-1 查所有
        },
        customQueryParams: {
            bind: false
        }
    }
    const res = await queryDeviceInformation(params)
    if (res.success) {
        deviceInformation.value = res.data?.records?.map(item => {
            return {
                value: item?.deviceDTO?.code,
                label: item?.deviceDTO?.name
            }
        })
    }
}
queryDeviceOption()


// 表单校验规则
const fields = {
    carNo: '车牌号',
    carCategory: '车辆类型',
    carStatus: '状态'
}
const validateRequire = (rule: any, value: number | string, callback: any) => {
    if (!value) {
        callback(new Error(fields[rule.field] + '必须填写'))
    } else {
        callback()
    }
}
const formRules = ref({
    carNo: [{ validator: validateRequire }],
    carCategory: [{ validator: validateRequire }],
    carStatus: [{ validator: validateRequire }]
})

const vehicleOption = Object.keys(VehicleTypeEnum).map(item => {
    return {
        label: VehicleTypeMap[item],
        value: item
    }
})
const statusOption = Object.keys(StatusTypeEnum).map(item => {
    return {
        label: StatusTypeMap[item],
        value: item
    }
})


const backPage = () => {
    router.back()
}
</script>

<style lang="less" scoped>
/deep/ .ivu-form-item{
    width: 80%;
}
/deep/ .ivu-form-item-label{
    padding-top: 0;
    padding-bottom: 8px;
}
.btn-box{
    .ivu-btn+.ivu-btn{
        margin-left: 8px;
    }
}
.car-info{
    font-style: normal;
    width: 63px;
    height: 24px;
    font-size: 14px;
    line-height: 24px;
    font-weight: 600;
    display: flex;
    align-items: center;
    color: #1E2A55;
}
.form-wrapper{
    margin-left: 8px;
}
</style>
