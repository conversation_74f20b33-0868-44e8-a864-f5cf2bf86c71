// 车辆信息
export interface ICarInfo {
    id?: number;
    carNo: string; // 车牌号
    carBrand: string; // 品牌
    carCategory: string; // 类型
    description: string; // 车辆参数
    name: string; // 车辆名称
    deviceCode: string; // 定位设备
    carStatus: string; // 运行状态
}

// 定位设备信息
export interface IpositioningDeviceInfo {
    code: string;
    name: string;
    longitude: number;
    latitude: number;
    modifyTime: string;
}
export interface ICarAddInfo extends ICarInfo {
    remark: string; // 备注
}
export interface ICarDetailInfo extends ICarInfo{
    device?: IpositioningDeviceInfo; // 定位设备详情
    remark: string; // 备注
}
// 定位设备选项
export interface IDeviceOption {
    value: string;
    label: string;
}
