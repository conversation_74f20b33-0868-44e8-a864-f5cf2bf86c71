<template>
    <Modal v-model="showFlag" title="关联车辆" :width="80" class-name="fill-page-modal" transfer :mask-closable="false">
        <chooseDeviceBox v-if="multiple" title="已选车辆">
            <div class="placeholder" v-show="selDevice.length === 0">请在下方选择数据</div>
            <Tooltip v-for="(item, index) in selDevice" :max-width="300" :key="index">
                <Tag color="blue" closable @on-close="delChoose(item, index)">{{ item.plate }}</Tag>
                <template #content>
                    <p>车辆名称: {{ item.name }}</p>
                    <p>车牌号: {{ item.plate }}</p>
                </template>
            </Tooltip>
        </chooseDeviceBox>
        <Form :model="searchObj" :label-width="90" @submit.native.prevent>
            <Row>
                <Col span="8">
                <FormItem label="车牌号" prop="plate">
                    <Input v-model="searchObj.plate" @on-change="() => searchInput()" @on-enter="searchInput(100)"
                        @on-clear="() => searchInput(100)" suffix="ios-search" clearable placeholder="请输入车牌号"></Input>
                </FormItem>
                </Col>
            </Row>
        </Form>
        <base-table ref="deviceTb" :columns="columns" url="/livableCar/getUnrelatedPage"
            @on-selection-change="selectionChange" :load-done="loadDone" size="small" :page-size="10">
            <template #type="{ row }">
                {{ store.getters.dictionary.base_obj_vehicle_type[row.type] }}
            </template>
            <template #status="{ row }">
                {{ store.getters.dictionary.base_obj_vehicle_status[row.status] }}
            </template>
            <template #action="{ row }">
                <link-btn size="small" @click="chooseOne(row)" :disabled="row._disabled">选择</link-btn>
            </template>
        </base-table>
        <template #footer>
            <div class="btn-box">
                <Button @click="handleCancel">取消</Button>
                <Button type="primary" @click="confirm" :loading="loading">确定</Button>
            </div>
        </template>
    </Modal>
</template>
<script lang="ts" setup>
import useDebounce from '@/hooks/useDebounce';
import chooseDeviceBox from '@/components/common/deviceSelect/chooseDeviceBox'
import { computed, onMounted, ref, watch, defineEmits } from 'vue';
import { linkVehicle } from '@/api/livableManage/vehicleManagementService'
import { Message } from 'view-ui-plus';
import { useStore } from 'vuex';
const store = useStore();
const showFlag = ref(true)
const multiple = ref(true)
const loading = ref(false)
const selIdList = computed(() => {
    return selDevice.value.map(item => item[keyName])
})
interface vehicleInfo {
    id: number,
    plate: string,
    name: string,

}
const keyName = 'id'
const selDevice = ref<vehicleInfo[]>([])
const delChoose = (row, index) => {
    selDevice.value.splice(index, 1)
    changeData([row[keyName]], false)
}
const chooseOne = (row) => {
    selDevice.value.push(row)
    changeData([row[keyName]])
}
// 初始化表格数据
const loadDone = (data, list) => {
    list.forEach(item => {
        if (selIdList.value.indexOf(item[keyName]) >= 0) {
            item._disabled = true
            item._checked = true
        }
    })
    return list
}
// 改变表格数据
const changeData = (ids, dis = true) => {
    deviceTb.value.changeData(list => {
        list.forEach(item => {
            if (ids.indexOf(item[keyName]) >= 0) {
                item._disabled = dis
                item._checked = dis
            }
        })
        return list
    })
}
const searchInput = (delay = 1000) => {
    useDebounce(() => {
        search()
    }, delay)
}
const selectionChange = (list: vehicleInfo[]) => {
    let ids: number[] = []
    list.forEach(item => {
        let id = item[keyName]
        if (selIdList.value.indexOf(id) < 0) {
            selDevice.value.push(item)
            ids.push(id)
        }
    })
    if (ids.length > 0) {
        changeData(ids)
    }
}
const searchObj = ref({
    plate: ''
})
const columns = [
    { type: 'selection', width: 40 },
    { title: '车牌号', key: 'plate', tooltip: true },
    { title: '名称', key: 'name', tooltip: true },
    { title: '品牌', key: 'brand', tooltip: true },
    { title: '车辆参数', key: 'description', tooltip: true, minWidth: 100 },
    { title: '类型', slot: 'type', tooltip: true },
    { title: '运行状态', slot: 'status', tooltip: true },
    { title: '操作', slot: 'action', width: 80 }
]
const handleCancel = () => {
    showFlag.value = false
}
const confirm = async() => {
    if (selDevice.value.length === 0) {
        Message.warning('最少选择一条数据')
        return
    }
    const plates = selDevice.value.map(k => k.plate)
    const res = await linkVehicle({ plates })
    const { success } = res
    if (success) {
        Message.success('关联成功')
        emits('onChange')
        handleCancel()
    }
}
const deviceTb = ref()
const search = () => {
    deviceTb.value.search(searchObj.value)
}
onMounted(() => {
    search()
})
const emits = defineEmits(['handleClose', 'onChange'])
watch(() => showFlag.value, (val: boolean) => {
    if (!val) {
        emits('handleClose')
    }
})
</script>
