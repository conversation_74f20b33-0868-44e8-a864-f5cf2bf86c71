<template>
    <BreadcrumbCustom>
        <BreadcrumbItem>电子围栏</BreadcrumbItem>
        <BreadcrumbItem to="/electronicFenceManagement/peopleManagement">
            人员管理
        </BreadcrumbItem>
        <BreadcrumbItem>详情</BreadcrumbItem>
    </BreadcrumbCustom>
    <Form :rules="formRules" :model="detailInfo">
        <detailCard title="基础信息" :src="require('@/assets/images/icon-基础信息.png')" :is-back-btn="true"
            @on-back="router.back()">
            <Row>
                <Col span="8">
                <s-label :bold="true" label="人员姓名" :value="detailInfo.name"></s-label>
                </Col>
            </Row>
            <Row>
                <Col span="8">
                <s-label label="身份证号" :value="detailInfo.idCard"></s-label>
                </Col>
                <Col span="8">
                <s-label label="岗位" :value="detailInfo.job"></s-label>
                </Col>
                <Col span="8">
                <s-label label="性别" :value="detailInfo.sex == 1 ? '男' : '女'"></s-label>
                </Col>
            </Row>
            <Row>
                <Col span="8">
                <s-label label="单位信息" :value="detailInfo.enterpriseName"></s-label>
                </Col>
                <Col span="8">
                <s-label label="手机" :value="detailInfo.mobile"></s-label>
                </Col>
                <Col span="8" />
                <Col span="8" />
            </Row>
            <Row>
                <Col span="24">
                <s-label label="备注" :value="detailInfo.remark"></s-label>
                </Col>
            </Row>
        </detailCard>
        <detailCard title="定位设备" :src="require('@/assets/images/icon-设备列表.png')">
            <div class="device_information">
                <Row>
                    <Col span="8">
                    <s-label label="设备状态">
                        <template #value>
                            <template v-if="detailInfo?.device?.code">
                                <auto-tag type="1" :value="deviceOnlineStatus"> </auto-tag>
                            </template>
                            <template v-else>
                                <span>--</span>
                            </template>
                        </template>
                    </s-label>
                    </Col>
                    <Col span="8">
                    <s-label label="设备编号" :value="detailInfo?.device?.code"></s-label>
                    </Col>
                    <Col span="8">
                    <s-label label="经纬度">
                        <template #value>
                            <span class="show-map" @click="openMap">
                                <Icon type="ios-pin-outline" v-if="PositionInfo.objX" />
                            </span>
                            <span v-if="PositionInfo?.objX">
                                {{ `${PositionInfo?.objX || ''}，${PositionInfo?.objY || ''}` }}
                            </span>
                            <span v-else>
                                --
                            </span>
                        </template>
                    </s-label>
                    </Col>
                </Row>
                <Row>
                    <Col span="8">
                    <s-label label="更新时间" :value="deviceUpdateTime"></s-label>
                    </Col>
                </Row>
            </div>
        </detailCard>
    </Form>
    <component :is="componentName" @close-modal="emitCloseModal()" :makers="[PositionInfo]" />
</template>

<script lang="ts" setup>

import { useRoute, useRouter } from 'vue-router';
import { computed, markRaw, reactive, ref } from 'vue';
import { querySinglePeopleInfo } from '@/api/livableManage/vehicleManagementService';
import { ICarDetailInfo } from './type';
import { Form } from 'view-ui-plus';
import deviceMarkerMap from '@/components/common/deviceMarkerMap'

const moment = require('moment');

const route = useRoute();
const router = useRouter();

const PositionInfo = computed(() => {
    const { devicePropertyStatusList = [] } = detailInfo.value
    const objY = devicePropertyStatusList.find((k: any) => k.prop == 'latitude')?.value
    const objX = devicePropertyStatusList.find((k: any) => k.prop == 'longitude')?.value
    return {
        objY, objX
    }
})

const detailInfo = ref({
    name: '',
    job: '', // 车牌号
    mobile: '', // 厂家
    idCard: '', // 品牌
    sex: '', // 型号
    enterpriseName: 0, // 类型
    remark: '',
    devicePropertyStatusList: [],
    device: {
        code: '',
        modifyTime: ''
    }
})
const deviceOnlineStatus = computed(() => {
    return detailInfo.value?.device?.status;
})

const deviceUpdateTime = computed(() => {
    return detailInfo.value?.device?.modifyTime
        ? moment(detailInfo.value.device.modifyTime).format('YYYY-MM-DD HH:mm:ss')
        : '';
})

const onInit = async() => {
    const query = route.query
    const res = await querySinglePeopleInfo(query.mobile);
    if (res.success && res.data) {
        detailInfo.value = res.data
    }
}
onInit();

const componentName = ref(null)
const componentArr = reactive<any>(markRaw(deviceMarkerMap))
const openMap = () => {
    componentName.value = componentArr;
}
const emitCloseModal = () => {
    componentName.value = null;
}
</script>

<style lang="less" scoped>
.status-box {
    color: #4CD263;
}

.lon-and-lat-wrapper {
    display: flex;
}

.lon-and-lat-box {
    min-width: 80px;
    height: 20px;
    line-height: 18px;
    background-color: #F2F2F2;
    border: 1px solid black;
    margin: 0 4px;
    text-align: center;
}

.position-box {
    width: 26px;
    height: 20px;
    margin: 0 4px;
    background-color: #3069EE;
}

/deep/ .ivu-form-item {
    width: 80%;
}

/deep/ .ivu-form-item-label {
    padding-top: 0;
    padding-bottom: 8px;
}

.show-map {
    cursor: pointer;
    font-size: 16px;
    color: #165DFF;

    .ivu-icon {
        font-weight: 700;
    }
}
</style>
