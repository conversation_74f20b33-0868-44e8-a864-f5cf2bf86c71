<template>
    <div>
        <Button type="primary" @click="handleOpenModal">
            <i class="iconfont">&#xe6b1;</i>
            关联人员
        </Button>
        <component :is="componentName" ref="device" :title="title" :search-title="searchTitle" :multiple="multiple"
            @handleClose="componentName = ''" @on-change="changeDevice" />
    </div>
</template>
<script type="ts" setup>
import { ref, defineEmits } from 'vue';
import peopleSelectComponent from './peopleSelectComponent'

const searchTitle = '请在下方选择数据'
const multiple = true
const title = '关联人员'
const componentName = ref('')
const handleOpenModal = () => {
    componentName.value = peopleSelectComponent
}
const emits = defineEmits(['onChange'])
const changeDevice = () => {
    console.log('changeDevice')
    emits('onChange')
}
</script>
