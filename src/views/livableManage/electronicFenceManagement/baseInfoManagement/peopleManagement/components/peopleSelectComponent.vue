<template>
    <Modal v-model="showFlag" title="关联人员" :width="80" class-name="fill-page-modal" transfer :mask-closable="false">
        <chooseDeviceBox v-if="multiple" title="已选人员">
            <div class="placeholder" v-show="selDevice.length === 0">请在下方选择数据</div>
            <Tooltip v-for="(item, index) in selDevice" :max-width="300" :key="index">
                <Tag color="blue" closable @on-close="delChoose(item, index)">{{ item.name }}</Tag>
                <template #content>
                    <p>人员姓名: {{ item.name }}</p>
                    <p>手机号: {{ item.mobile }}</p>
                </template>
            </Tooltip>
        </chooseDeviceBox>
        <Form :model="searchObj" :label-width="90" @submit.native.prevent>
            <Row>
                <Col span="8">
                <FormItem label="人员名称" prop="name">
                    <Input v-model="searchObj.name" @on-change="() => searchInput()" @on-enter="searchInput(100)"
                        @on-clear="() => searchInput(100)" suffix="ios-search" clearable placeholder="请输入名称或设备编号"></Input>
                </FormItem>
                </Col>
            </Row>
        </Form>
        <base-table ref="deviceTb" :columns="columns" url="/electronicfence/people/getUnrelatedPage"
            @on-selection-change="selectionChange" :load-done="loadDone" size="small" :page-size="10">
            <template #sex="{ row }">
                {{ row.sex == 1 ? '男' : '女' }}
            </template>
            <template #action="{ row }">
                <link-btn size="small" @click="chooseOne(row)" :disabled="row._disabled">选择</link-btn>
            </template>
        </base-table>
        <template #footer>
            <div class="btn-box">
                <Button @click="handleCancel">取消</Button>
                <Button type="primary" @click="confirm" :loading="loading">确定</Button>
            </div>
        </template>
    </Modal>
</template>
<script lang="ts" setup>
import useDebounce from '@/hooks/useDebounce';
import chooseDeviceBox from '@/components/common/deviceSelect/chooseDeviceBox'
import { computed, onMounted, ref, watch, defineEmits } from 'vue';
import { linkPeople } from '@/api/livableManage/vehicleManagementService'
import { Message } from 'view-ui-plus';
const showFlag = ref(true)
const multiple = ref(true)
const loading = ref(false)
const selIdList = computed(() => {
    return selDevice.value.map(item => item[keyName])
})
interface peopleInfo {
    id: number,
    mobile: string,
    name: string,

}
const keyName = 'id'
const selDevice = ref<peopleInfo[]>([])
const delChoose = (row, index) => {
    selDevice.value.splice(index, 1)
    changeData([row[keyName]], false)
}
const chooseOne = (row) => {
    selDevice.value.push(row)
    changeData([row[keyName]])
}
// 初始化表格数据
const loadDone = (data, list) => {
    list.forEach(item => {
        if (selIdList.value.indexOf(item[keyName]) >= 0) {
            item._disabled = true
            item._checked = true
        }
    })
    return list
}
// 改变表格数据
const changeData = (ids, dis = true) => {
    deviceTb.value.changeData(list => {
        list.forEach(item => {
            if (ids.indexOf(item[keyName]) >= 0) {
                item._disabled = dis
                item._checked = dis
            }
        })
        return list
    })
}
const searchInput = (delay = 1000) => {
    useDebounce(() => {
        search()
    }, delay)
}
const selectionChange = (list: peopleInfo[]) => {
    let ids: number[] = []
    list.forEach(item => {
        let id = item[keyName]
        if (selIdList.value.indexOf(id) < 0) {
            selDevice.value.push(item)
            ids.push(id)
        }
    })
    if (ids.length > 0) {
        changeData(ids)
    }
}
const searchObj = ref({
    name: ''
})
const columns = [
    { type: 'selection', width: 40 },
    { title: '人员名称', key: 'name', tooltip: true },
    { title: '企业名称', key: 'enterpriseName', tooltip: true },
    { title: '手机号', key: 'mobile', tooltip: true, minWidth: 100 },
    { title: '性别', slot: 'sex', tooltip: true },
    { title: '操作', slot: 'action', width: 80 }
]
const handleCancel = () => {
    showFlag.value = false
}
const confirm = async() => {
    if (selDevice.value.length === 0) {
        Message.warning('最少选择一条数据')
        return
    }
    const mobiles = selDevice.value.map(k => k.mobile)
    const res = await linkPeople({ mobiles })
    const { success } = res
    if (success) {
        Message.success('关联成功')
        emits('onChange')
        handleCancel()
    }
}
const deviceTb = ref()
const search = () => {
    deviceTb.value.search(searchObj.value)
}
onMounted(() => {
    search()
})
const emits = defineEmits(['handleClose', 'onChange'])
watch(() => showFlag.value, (val: boolean) => {
    if (!val) {
        emits('handleClose')
    }
})
</script>
