<template>
    <BreadcrumbCustom>
        <BreadcrumbItem>电子围栏</BreadcrumbItem>
        <BreadcrumbItem>人员管理</BreadcrumbItem>
    </BreadcrumbCustom>
    <ContentCard title="人员管理" :base-btn="false">
        <BaseForm @handle-submit="onSubmit(listRef)" :model="condition" inline :label-width="90">
            <template #formitem>
                <FormItem label="人员名称" prop="name">
                    <Input v-model="condition.name" :maxlength="20" placeholder="请输入" clearable></Input>
                </FormItem>
            </template>
        </BaseForm>
        <btnCard>
            <peopleSelect v-auth="'electronicFence:peopleManagement:relation'" @on-change="onSubmit(listRef)" />
            <Button icon="ios-trash" @click="onDelete" v-auth="'electronicFence:peopleManagement:delete'">
                删除
            </Button>
        </btnCard>

        <baseTable @on-selection-change="onSelectionChange" :model="condition" ref="listRef" :columns="tableColumn"
            url="/electronicfence/people/getPage">
            <template #sex="{ row }">
                {{ row.sex == 1 ? '男' : '女' }}
            </template>
            <template #action="{ row }">
                <LinkBtn size="small" @click="goDetail(row)">
                    详情
                </LinkBtn>
            </template>
        </baseTable>
    </ContentCard>
</template>

<script lang="ts" setup>
import { getCurrentInstance, onMounted, ref, toRaw } from 'vue';
import { useRouter } from 'vue-router';
import { deletePeople } from '@/api/livableManage/vehicleManagementService';
import { ICarInfo } from './type';
import peopleSelect from './components/peopleSelect'
const router = useRouter();

const that = getCurrentInstance()?.appContext.config.globalProperties;

type BaseTable = InstanceType<typeof baseTable>
const listRef = ref<BaseTable>(null);

// 筛选表单
const condition = ref({
    name: ''
})

const onSubmit = async (listRef: any) => {
    listRef.search(condition.value)
}
onMounted(() => {
    onSubmit(listRef.value);
})

// 删除
const selectedIds = ref<(number | undefined)[]>([])

const onSelectionChange = (selectedRow: ICarInfo[] = []) => {
    selectedIds.value = selectedRow.map(item => item.mobile)
}
const onDelete = () => {
    if (selectedIds.value.length === 0) {
        that && that.$Message.warning('最少选择一条数据')
        return
    }
    that && that.$Modal.confirm({
        title: '提示',
        content: '您确定要删除选中的数据吗',
        onOk: () => {
            const deleteIds = selectedIds.value;
            delAjax(deleteIds)
        }
    })
}

const delAjax = async (ids: (number | undefined)[]) => {
    const params = {
        mobiles: ids
    }
    const res = await deletePeople(params);
    if (res.success) {
        that?.$Message.success('删除成功')
        selectedIds.value = []
    }
    await onSubmit(listRef.value);
}


const goDetail = (row: ICarInfo) => {
    const query = {
        mobile: row.mobile
    }
    router.push({ name: 'electronicFence:car:detail', query })
}

const tableColumn = ref([
    { type: 'selection', width: 40 },
    { title: '人员名称', key: 'name', tooltip: true },
    { title: '身份证号', key: 'idCard', tooltip: true },
    { title: '企业名称', key: 'enterpriseName', tooltip: true },
    { title: '手机', key: 'mobile', tooltip: true },
    { title: '岗位', key: 'job', tooltip: true },
    { title: '性别', slot: 'sex', tooltip: true },
    { title: '工牌编号', key: 'identifier', tooltip: true },
    { title: '操作', slot: 'action', width: 80, lock: true, fixed: true }
])

</script>

<style lang="less" scoped>
.header {
    background-color: #fff;
    margin: 10px;

    .header-top {
        border-bottom: 1px solid;
    }
}
</style>
