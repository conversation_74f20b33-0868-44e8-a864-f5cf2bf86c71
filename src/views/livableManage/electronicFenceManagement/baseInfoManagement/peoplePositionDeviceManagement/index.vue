<template>
    <div>
        <BreadcrumbCustom>
            <BreadcrumbItem>电子围栏</BreadcrumbItem>
            <BreadcrumbItem>人员工牌设备管理</BreadcrumbItem>
        </BreadcrumbCustom>
        <ContentCard title="人员工牌设备管理">
            <BaseForm :model="searchObj" :label-width="90" @handle-submit="search">
                <template #formitem>
                    <FormItem label="员工名称" prop="workName">
                        <Input v-model="searchObj.workName" clearable placeholder="请输入"></Input>
                    </FormItem>
                    <FormItem label="设备编号" prop="deviceCode">
                        <Input v-model="searchObj.deviceCode" clearable placeholder="请输入"></Input>
                    </FormItem>
                    <FormItem label="工牌编号" prop="identifier">
                        <Input v-model="searchObj.identifier" clearable placeholder="请输入"></Input>
                    </FormItem>
                    <FormItem label="在线状态" prop="status">
                        <Select v-model="searchObj.status" clearable placeholder="请选择">
                            <Option :value="1">在线</Option>
                            <Option :value="0">离线</Option>
                        </Select>
                    </FormItem>
                    <FormItem label="使用状态" prop="useStatus">
                        <Select v-model="searchObj.useStatus" clearable placeholder="请选择">
                            <Option v-for="(item, index) in $enumeration.deviceUesState" :value="index" :key="index">
                                {{ item }}
                            </Option>
                        </Select>
                    </FormItem>
                </template>
            </BaseForm>

            <div class="action-box">
                <!-- 关联设备 -->
                <deviceSelect :model-id="37" :multiple="true"
                    v-auth="'electronicFence:peoplePositionDeviceManagement:relation'" @on-change="addSubmit" />
                <Button icon="ios-trash" @click="delMore" v-auth="'electronicFence:peoplePositionDeviceManagement:delete'">
                    删除
                </Button>
            </div>

            <base-table ref="mainTb" :columns="columns" url="/electronicfence/peoplePositionDevice/getPage"
                @on-selection-change="selectionChange">
                <template #status="{ row }">
                    <onlineStatus :value="row?.status" />
                </template>
                <template #useStatus="{ row }">
                    <useStatus :value="row.useStatus" />
                </template>
                <template #action="{ row }">
                    <link-btn size="small" @click="toDetail(row)">
                        查看
                    </link-btn>
                    <LinkBtn size="small" @click="editUseState(row)"
                        v-auth="'electronicFence:peoplePositionDeviceManagement:enable'">
                        {{ row.useStatus == 1 ? '停用' : '启用' }}
                    </LinkBtn>
                </template>
            </base-table>
        </ContentCard>
    </div>
</template>

<script>
import { commonService } from '@/api/commonService'
export default {
    data() {
        return {
            columns: [
                { type: 'selection', width: 40, align: 'center' },
                { title: '设备编号', key: 'deviceCode', tooltip: true },
                { title: '工牌编号', key: 'identifier', tooltip: true },
                { title: '设备标识码', key: 'bsm', minWidth: 100, tooltip: true },
                { title: '设备型号', key: 'deviceUnitCode' },
                { title: '在线状态', slot: 'status', width: 80 },
                { title: '使用状态', slot: 'useStatus', width: 80 },
                { title: '员工名称', key: 'workName', tooltip: true },
                { title: '最后更新时间', key: 'modifyTime', width: 160, tooltip: true },
                { title: '操作', slot: 'action', width: 100 }
            ],
            searchObj: {
                deviceCode: '',
                useStatus: '',
            },
            m: {
                selectList: [] // 表格已选数据
            }
        }
    },
    mounted() {
        this.search()
    },
    methods: {
        search() {
            let param = Util.objClone(this.searchObj)
            if (this.searchObj.areaPath) {
                let areaLocationTreeArr = this.searchObj.areaPath.split('@')
                param.szjd = areaLocationTreeArr[0]
                param.szsq = areaLocationTreeArr[1]
                param.szdywg = areaLocationTreeArr[2]
                delete param.areaPath
            }
            this.$refs.mainTb.search(param)
        },
        editUseState(row) {
            let state = row.useStatus
            let str = ''
            if (state == 1) {
                state = 0
                str = '停用'
            } else {
                state = 1
                str = '启用'
            }
            this.$Modal.confirm({
                title: '提示',
                content: `您确定要${str}吗`,
                onOk: () => {
                    // this.$request('/electronicfence/peoplePositionDevice', { id: row.id, useStatus: state }, 'put')
                    commonService.updateUseStatus(row.deviceCode, state)
                    .then(res => {
                        console.log(res)
                        if (res.success) {
                            this.$Message.success('操作成功')
                            this.search()
                        }
                    })
                }
            })
        },
        addSubmit(list) {
            let param = []
            list.forEach(item => {
                param.push({
                    deviceCode: item.deviceId
                })
            })
            this.$request('/electronicfence/peoplePositionDevice', param, 'post').then(res => {
                if (res.success) {
                    this.$Message.success('操作成功')
                    this.search()
                }
            })
        },
        toDetail(row) {
            this.$router.push({
                path: '/electronicFenceManagement/peoplePositionDeviceManagementDetail',
                query: {
                    id: row.id
                }
            })
        },
        delMore() {
            if (this.m.selectList.length === 0) {
                this.$Message.warning('最少选择一条数据')
                return
            }
            this.$Modal.confirm({
                title: '提示',
                content: '您确定要删除选中的数据吗',
                onOk: () => {
                    let arr = []
                    this.m.selectList.forEach(item => {
                        arr.push(item.id)
                    })
                    this.delAjax(arr)
                }
            })
        },
        delAjax(arr) {
            this.$request('/electronicfence/peoplePositionDevice?idList=' + arr.join(','), {}, 'delete').then(res => {
                if (res.success) {
                    this.$Message.success('删除成功')
                    this.m.selectList.length = []
                    this.search()
                }
            })
        },
        selectionChange(list) {
            this.m.selectList = list || []
        },
        handleSubmit() {
            console.log(this.searchObj)
        }
    }
}
</script>

<style lang="less" scoped>
.action-box {

    display: flex;

    .ivu-btn {
        margin-right: 8px;
        margin-bottom: 16px;
    }

    .device-select {
        margin-bottom: 16px;
    }
}</style>
