<template>
    <div>
        <BreadcrumbCustom>
            <BreadcrumbItem>电子围栏</BreadcrumbItem>
            <BreadcrumbItem to="/electronicFenceManagement/peoplePositionDeviceManagement">人员工牌设备管理</BreadcrumbItem>
            <BreadcrumbItem>详情</BreadcrumbItem>
        </BreadcrumbCustom>
        <Form ref="addForm" :model="subObj" :rules="rules" label-position="top">
            <detailCard ref="detail" title="基础信息" :src="require('@/assets/images/icon_detail.png')" :is-back-btn="true"
                @on-back="backPage">
                <infoShow :sub-obj="m.device" />
            </detailCard>
        </Form>
        <detailCard title="设备流水" :src="require('@/assets/images/icon_detail.png')">
            <deviceFlow :device-code="m.device.deviceCode" />
        </detailCard>
    </div>
</template>

<script>
import infoShow from './components/infoShow';
import deviceFlow from './components/deviceFlow'
export default {
    name: 'Detail',
    components: {
        infoShow,
        deviceFlow
    },
    data() {
        return {
            detailId: '',
            m: {
                device: {}
            }
        }
    },
    created() {
        if (this.$route.query.id) {
            this.detailId = this.$route.query.id
            this.getDetailById(this.$route.query.id)
        }
    },
    methods: {
        getDetailById(id) {
            this.$request(`/electronicfence/peoplePositionDevice/${id}`).then(res => {
                this.m.device = res.data
            })
        },
        backPage() {
            this.$router.back()
        }
    }
}
</script>

<style lang="less" scoped>
/deep/ .tab-title {
    justify-content: flex-start;
    padding-bottom: 16px;
    height: 48px;
    column-gap: 20px;


}
</style>
