/deep/.ivu-card-body {
    padding: 0;
    .face-plate-cont.electronic-fence {
        position: absolute;
        width: 100%;
        height: 100%;
        display: flex;
        justify-content: space-between;
        z-index: 1;
        pointer-events: none;
        .name {
            color: #1E2A55;
            font-size: 16px;
            line-height: 24px;
            margin-bottom: 12px;
            font-weight: bold;
            padding-left: 8px;
            display: flex;
            width: 100%;
            align-items: center;
            justify-content: space-between;
            .add-fence {
                font-size: 12px;
                color:#fff;
                height: 20px;
                width: 92px;
                background:#165DFF;
                display: flex;
                justify-content: center;
                align-items: center;
                column-gap: 8px;
                cursor: pointer;
            }
            .carNo {
                max-width: 110px;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;

            }
        }
        .left-plate {
            width: 224px;
            margin: 16px;
            pointer-events: auto;
            padding: 16px 8px;
            .check-all {
                margin-top: 13px;
                padding-left: 5px;
            }
            .operation-box {
                display: flex;
                align-items: center;
                padding-left: 28px;
                margin-top: 8px;
            }
            .vehicle-box {
                height: 78px;
                margin-top: 8px;
                color: #1E2A55;
                font-size: 12px;
                line-height: 22px;
                padding: 8px 8px 0 5px;
                overflow: hidden;
                .vehicle-checkbox {
                    width: 100%;
                    display: flex;
                    .ivu-checkbox {
                        padding-top: 2px;
                        padding-right: 8px;
                    }
                    .ivu-checkbox-label-text {
                        flex: 1;
                    }
                }
                .vehicle-item {
                    .vehicle-name {
                        display: flex;
                        color: #0E42D2;
                        justify-content: space-between;
                        width: 155px;
                        overflow: hidden;
                        .carNo {
                            width: 115px;
                            white-space: nowrap;
                            overflow: hidden;
                            text-overflow: ellipsis;
                            .chepai {
                                vertical-align: middle;
                                margin-right: 4px;
                                margin-top: -3px;
                            }
                        }
                    }
                    .vehicle-time {
                        display: flex;
                        width: 160px;
                        .car-category {
                            flex:1;
                            // max-width: 60px;
                            white-space: nowrap;
                            overflow: hidden;
                            text-overflow: ellipsis;
                        }
                        .car-type {
                            max-width: 155px;
                            white-space: nowrap;
                            overflow: hidden;
                            text-overflow: ellipsis;
                        }
                    }
                }
                .view-locus {
                    margin-right: 8px;
                    width: 158px;
                    text-align: center;
                    line-height: 22px;
                    height: 24px;
                    border: 1px solid #165DFF;
                    border-radius: 2px;
                    // margin-top: 8px;
                    font-size: 12px;
                    color:#165DFF;
                    cursor: pointer;
                    &.actived {
                        background: #0E42D2;
                        color: #FFFFFF;
                    }

                }
            }
            .min-page {
                margin-top: 20px;
                display: flex;
                justify-content: flex-end;
                /deep/&.ivu-page.mini .ivu-page-item{
                    height: 24px;
                    width: 24px;
                    line-height: 24px;
                    min-width: 24px;
                }
            }
        }
        .right-plate {
            width: 328px;
            margin: 16px;
            pointer-events: auto;
            padding: 0;
            .name {
                display: block;
            }
            .scorll-map-cont {
                padding: 0 16px;
            }
            .device-info-list {
                padding-top: 16px;
            }
            .s-label {
                align-items: center;
                .title {
                    // width: 100px;
                }
            }
        }
        .history-select {
            position: absolute;
            left: 248px;
            width: 224px;
            height: 184px;
            top: 16px;
            pointer-events: auto;
            padding: 16px 8px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            .ivu-date-picker+.ivu-date-picker {
                margin-top: 8px;
            }
            .view-locus-detail {
                width: 124px;
                height: 28px;
                background: #165DFF;
                border-radius: 2px;
                color: #fff;
                line-height: 28px;
                text-align: center;
                margin-top: 16px;
                cursor: pointer;
                &.disabled {
                    background: #D9D9D9;
                    cursor: not-allowed;
                }
            }
        }
    }
    .container-map {
        width: 100%;
        height: ~'calc(100vh - 110px)';
        &.full-map {
            height: 100vh;
        }
    }
}
.electronic-fence-dropdown {
    width: 57px;
    .ivu-dropdown-menu {
        min-width: 57px;
    }
}
