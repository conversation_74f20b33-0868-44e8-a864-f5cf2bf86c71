<template>
    <BreadcrumbCustom>
        <BreadcrumbItem>电子围栏</BreadcrumbItem>
        <BreadcrumbItem>电子围栏管理</BreadcrumbItem>
    </BreadcrumbCustom>
    <Card dis-hover :bordered="false" class="map-cont" id="fullscreen">
        <div class="face-plate-cont electronic-fence">
            <div class="left-plate plate-bg">
                <div class="name">
                    电子围栏
                    <div class="add-fence" @click="handleFenceModal()" v-auth="'electronicFence:ef_management:add'">
                        <Icon type="md-add" />电子围栏
                    </div>
                </div>
                <Input suffix="ios-search" placeholder="请输入围栏名称" clearable v-model="searchTreeText" />
                <Checkbox class="check-all" :indeterminate="indeterminate" :model-value="checkAll"
                    @click.prevent="handleCheckAll">
                    全选
                </Checkbox>
                <div class="scorll-map-cont">
                    <CheckboxGroup v-model="checkAllGroup" @on-change="checkAllGroupChange">
                        <div class="vehicle-box" v-for="(item, index) in deviceList" :key="index">
                            <Checkbox :label="item.id" class="vehicle-checkbox">
                                <div class="vehicle-item">
                                    <div class="vehicle-time">
                                        <div class="car-type">{{ item.name }}</div>
                                    </div>
                                </div>
                            </Checkbox>
                            <div class="operation-box">
                                <div class="view-locus" @click="handleSetRegion(`${item.id}`)"
                                    v-auth="'electronicFence:ef_management:regionSet'">范围设置</div>
                                <Dropdown transfer transfer-class-name="electronic-fence-dropdown" placement="bottom-start"
                                    @on-click="handleOperation">
                                    <a href="javascript:void(0)">
                                        <span class="icon iconfont icon-more-vertical"></span>
                                    </a>
                                    <template #list>
                                        <DropdownMenu>
                                            <DropdownItem :name="`${item.id}-set`"
                                                v-auth="'electronicFence:ef_management:edit'">设置</DropdownItem>
                                            <DropdownItem :name="`${item.id}-del`"
                                                v-auth="'electronicFence:ef_management:del'">删除</DropdownItem>
                                        </DropdownMenu>
                                    </template>
                                </Dropdown>
                            </div>

                        </div>
                    </CheckboxGroup>
                </div>
            </div>
            <div class="right-plate plate-bg" v-show="activedObj.id ? true : false">
                <device-detail :actived-obj="activedObj" />
            </div>
        </div>
        <div id="container-electornic" class="container-map" :class="fullFlag ? 'full-map' : ''"></div>
        <s-modal :width="componentName == 'ElectronicFenceModal' ? 600 : 999"
            :title="componentName == 'ElectronicFenceModal' ? '电子围栏' : '范围设置'" :component-name="componentName"
            @emitClose="componentName = ''" :ref-box="modalRef" :transfer="true" @handleSubmit="getElectronicFenceList">
            <component :is="componentName" ref="modalRef" :id="id" />
        </s-modal>
    </Card>
</template>
<script lang="ts">
import AMapLoader from '@amap/amap-jsapi-loader';
import { defineComponent, nextTick, onMounted, Ref, ref, watch } from 'vue'
import { launchIntoFullscreen, exitFullscreen } from '@/utils/tool'
import { electronicFenceService } from '@/api/livableManage/electronicFenceService'
import { livableElectronicFenceInfo, electronicFenceRegion, coordinate } from '@/api/livableManage/type'
import { enumeration } from '@/config/enumeration'
// import Util from '@/utils/index';
import { Modal, Message } from 'view-ui-plus';
import useDebounce from "@/hooks/useDebounce";
import ElectronicFenceModal from './components/ElectronicFenceModal.vue'
import ElectronicFenceRegionModal from './components/ElectronicFenceRegionModal.vue'
export default defineComponent({
    components: {
        ElectronicFenceModal,
        ElectronicFenceRegionModal
    },
    setup() {
        // *********************
        // 地图
        // *********************
        // 初始化图标
        const map = ref<any>(null);
        const Amap = ref<any>(null);

        const ininMap = () => {
            AMapLoader.load({
                key: 'ea53a5291f8f7c1f215ba20930ea9866',
                version: '2.0'
            }).then((AMap) => {
                Amap.value = AMap
                map.value = new AMap.Map('container-electornic', {
                    resizeEnable: true,
                    zoom: 16,
                    center: [114.61, 30.45]
                })

                nextTick(() => {
                    getElectronicFenceList()
                })
            })
        }
        // 更新围栏框
        const UpdatePolygon = async (data: electronicFenceRegion[]) => {
            await map.value?.clearMap()

            data.forEach(ele => {
                if (!ele.coordinateList) return;
                var path = ele.coordinateList.map((k: coordinate) => new Amap.value.LngLat(k.positionX, k.positionY))
                var polygon = new Amap.value.Polygon({
                    path: path,
                    fillColor: 'rgba(190, 218, 255, 0.6)',
                    borderWeight: 2,
                    strokeColor: 'rgba(22, 93, 255, 0.6)',
                });
                map.value.add(polygon);
            })
            nextTick(() => {
                map.value?.setFitView();
            })
        }

        // 地图全屏
        const fullFlag: Ref<boolean | null> = ref(false)
        const handleFullScreen = () => {
            const element = document.getElementById('fullscreen');
            if (!element) return
            // false是进入全屏状态
            if (fullFlag.value) {
                // 退出全屏
                exitFullscreen(element);
            } else {
                launchIntoFullscreen(element);
            }
            fullFlag.value = !fullFlag.value
        }
        // 列表点击更多
        const handleOperation = (name: string) => {
            const [_id, _type] = name.split('-')
            if (_type == 'del') {
                handleDel(_id)
            }
            else {
                handleFenceModal(_id)
            }
        }
        // 删除操作
        const handleDel = (_id: string) => {
            Modal.confirm({
                title: '操作确认',
                content: '您确定要删除选中的数据吗？',
                loading: true,
                onOk: () => {
                    electronicFenceService.delElectronicFence([_id]).then((res: any) => {
                        if (res.success) {
                            Message.success('删除成功')
                            getElectronicFenceList()

                        }
                    }).finally(() => {
                        Modal.remove()
                    })
                }
            });
        }
        // 左侧列表
        const searchTreeText = ref(''); // 查询车牌
        watch(searchTreeText, () => {
            useDebounce(() => {
                getElectronicFenceList();
            });
        })
        // 是否全选
        const checkAll = ref(false)
        const indeterminate = ref(true)
        const checkAllGroup = ref<number[]>([])
        const totalSize = ref<number>(0)
        // *********************
        // 左侧列表
        // *********************
        // 选中的id
        const activedObj = ref<livableElectronicFenceInfo>({})
        watch(() => activedObj.value, () => { })
        // 监听选中的设施
        watch(checkAllGroup, () => {
            if (!checkAllGroup.value?.length) {
                map.value?.clearMap()
                return;
            }
            getRegion()
        })

        const checkAllGroupChange = (data: any[]) => {
            if (data.length === deviceList.value.length) {
                indeterminate.value = false;
                checkAll.value = true;
            } else if (data.length > 0) {
                indeterminate.value = true;
                checkAll.value = false;
            } else {
                indeterminate.value = false;
                checkAll.value = false;
            }
        }
        // 全选方法
        const handleCheckAll = () => {
            if (indeterminate.value) {
                checkAll.value = false;
            } else {
                checkAll.value = !checkAll.value;
            }
            indeterminate.value = false;
            if (checkAll.value) {
                checkAllGroup.value = deviceList.value.map(item => item.id) as number[];
            } else {
                checkAllGroup.value = [];
            }
        }
        // 获取围栏范围
        const getRegion = async () => {
            const res = await electronicFenceService.electronicFenceRegion(checkAllGroup.value.join(','));
            const { data, success }: { success: boolean; data: electronicFenceRegion[] } =
                res as unknown as HttpResponse<electronicFenceRegion[]>;
            if (success) {
                UpdatePolygon(data)
            }

        }
        // 获取围栏列表
        const deviceList = ref<livableElectronicFenceInfo[]>([])
        const getElectronicFenceList = async () => {
            const params = {
                page: {
                    current: 1,
                    size: -1
                },
                customQueryParams: {
                    name: searchTreeText.value || null,
                }
            }
            const res = await electronicFenceService.electronicFenceList(params);
            const { data, success }: { success: boolean; data: recordsResponse<livableElectronicFenceInfo[]> } =
                res as unknown as HttpResponse<recordsResponse<livableElectronicFenceInfo[]>>;
            if (success) {
                deviceList.value = data.records
                checkAllGroup.value = [data.records[0]?.id || 0]
                getRegion()
            }
        }

        onMounted(() => {
            ininMap();
            window.addEventListener('fullscreenchange', e => {
                // 监听到屏幕变化，在回调中判断是否已退出全屏
                const isFull = document.fullscreenElement || document['mozFullScreenElement'] || document['webkitFullscreenElement'];
                fullFlag.value = isFull || false
            })
        })
        // 弹框数据
        const componentName = ref('')
        const modalRef = ref()
        const id = ref('')
        const handleFenceModal = (_id = '') => {
            componentName.value = 'ElectronicFenceModal'
            id.value = _id
        }
        const handleSetRegion = (_id = '') => {
            componentName.value = 'ElectronicFenceRegionModal'
            id.value = _id
        }
        return {
            fullFlag,
            handleFullScreen,
            searchTreeText,
            handleCheckAll,
            checkAll,
            checkAllGroup,
            checkAllGroupChange,
            deviceList,
            enumeration,
            indeterminate,
            totalSize,
            activedObj,
            handleOperation,
            componentName,
            modalRef,
            handleFenceModal,
            id,
            getElectronicFenceList,
            handleSetRegion
        }
    }
})
</script>
<style lang="less" scoped>
@import '../../../../styles/mapPage.less';
@import './index.less';
</style>

