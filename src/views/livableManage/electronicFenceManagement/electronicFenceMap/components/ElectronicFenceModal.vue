<template>
    <Form ref="addForm" label-position="top" :rules="ruleValidate" :model="addFormValue" class="electronic-fence-modal">
        <Row :gutter="80">
            <Col span="24">
                <FormItem label="电子围栏名称" prop="name">
                    <Input v-model="addFormValue.name" maxlength="20"></Input>
                </FormItem>
            </Col>
            <Col span="24">
                <FormItem label="围栏类型" prop="type">
                <dictDropDownSelect code="electronic_fence_type" v-model="addFormValue.type" :disabled="isApproval"></dictDropDownSelect>
            </FormItem>
            </Col>
        </Row>
        <span>围栏出入告警</span>
        <Row :gutter="80">
            <Col span="24">
            <div class="alarm-set-box">
                <FormItem label="" prop="fenceIn">
                    <Checkbox v-model="addFormValue.fenceIn" />
                </FormItem>
                <span class="" style="margin: 0 8px 20px 0">进入围栏</span>
                <FormItem label="" prop="inTime">
                    <Input placeholder="" style="width: 131px" v-model="addFormValue.inTime">
                    <template #suffix>
                        分钟
                    </template>
                    </Input>
                </FormItem>
                <div class="" style="margin: 0 8px 20px">后</div>
                <div class="alarm-tips">
                    <Icon type="md-information-circle" />
                    <div style="white-space: nowrap;">提示:你已进入</div><tooltip-auto-show>{{ addFormValue.name }}</tooltip-auto-show>
                </div>
            </div>
            <div class="alarm-set-box">
                <FormItem label="" prop="fenceIn">
                    <Checkbox v-model="addFormValue.fenceOut" />
                </FormItem>
                <span class="" style="margin: 0 8px 20px 0">离开围栏</span>
                <FormItem label="" prop="outTime">
                    <Input placeholder="" style="width: 131px" v-model="addFormValue.outTime">
                    <template #suffix>
                        分钟
                    </template>
                    </Input>
                </FormItem>
                <div class="" style="margin: 0 8px 20px">后</div>
                <div class="alarm-tips">
                    <Icon type="md-information-circle" /><div style="white-space: nowrap;">提示:你已离开</div><tooltip-auto-show>{{ addFormValue.name }}</tooltip-auto-show>
                </div>
            </div>
            </Col>
        </Row>
        <Row :gutter="80">
            <Col span="24">
            <FormItem label="选择监控车辆" prop="type">
                <s-transfer :data="carList" :defaultTargetKeys="defaultTargetKeys" :titles="['全部监控车辆', '已选监控车辆']" filterable
                    @on-change="handleGroupChange">
                </s-transfer>
            </FormItem>
            </Col>
        </Row>
        <Row :gutter="80">
            <Col span="12">
            <template v-for="(item, index) in addFormValue.timeValueArray" :key="index">
                <FormItem :prop="'timeValueArray.' + index" class="duty-time">
                    <template #label v-if="index == 0">
                        {{ index == 0 ? '控制时间' : '' }}
                    </template>
                    <TimePicker v-model="addFormValue.timeValueArray![index]" type="timerange" placement="bottom-end"
                        placeholder="开始时间 - 结束时间" style="width: 100%" format="HH:mm" />
                    <div class="operation" @click="handAddTimer" v-if="index == 0">
                        <Icon type="md-add-circle" /> 添加
                    </div>
                    <div class="operation" @click="handDelTimer(index)" v-if="index != 0">
                        <Icon type="md-close-circle" /> 删除
                    </div>
                </FormItem>
            </template>
            </Col>
        </Row>
        <Row :gutter="80">
            <Col span="24">
            <FormItem label="备注" prop="note">
                <Input v-model="addFormValue.note" type="textarea" :rows="4" placeholder="请输入" maxlength="200"
                    show-word-limit></Input>
            </FormItem>
            </Col>
        </Row>
    </Form>
</template>
<script lang="ts">
import { defineComponent, ref, watch } from 'vue';
import { electronicFenceService } from '@/api/livableManage/electronicFenceService'
import { livableElectronicFenceInfo, livableCarInfo } from '@/api/livableManage/type'
import { Message } from 'view-ui-plus';
import { validateform } from "@/utils/validateform";
export default defineComponent({
    name: 'ElectronicFenceModal',
    props: {
        id: {
            default: '',
            type: String
        }
    },
    setup(props, ctx) {
        interface transferType {
            key?: number;
            label?: string;
        }
        const addFormValue = ref<livableElectronicFenceInfo>({ timeValueArray: [[]] })
        const ruleValidate = ref({
            name: [validateform.required],
            inTime: [validateform.nonnegativePositiveInteger, {
                validator: (rule: any, value: any, callback: any) => {

                    if (addFormValue.value.fenceIn && !(`${value}`?.trim())) {

                        callback(new Error('请填写时间'))
                    } else {
                        callback()
                    }

                }
            }],
            outTime: [validateform.nonnegativePositiveInteger, {
                validator: (rule: any, value: any, callback: any) => {

                    if (addFormValue.value.fenceOut && !(`${value}`?.trim())) {
                        callback(new Error('请填写时间'))
                        // return
                    } else {
                        callback()
                    }

                }
            }]
        })
        const targetKeys = ref<number[]>([])
        const defaultTargetKeys = ref<number[]>([])
        // 获取车辆信息
        const carList = ref<transferType[]>([])
        const getElectronicFenceCar = async () => {
            const res = await electronicFenceService.electronicFenceCar(props.id);
            const { data, success }: { success: boolean; data: livableCarInfo[] } =
                res as unknown as HttpResponse<livableCarInfo[]>;
            if (success) {
                carList.value = data.map(k => ({ key: k.id, label: k.carNo }))
                defaultTargetKeys.value = data.filter(k => k.choose).map(k => k.id || 0) || []

            }
        }
        const getElectronicFence = async () => {
            const res = await electronicFenceService.getElectronicFence(props.id);
            const { data, success }: { success: boolean; data: livableElectronicFenceInfo } =
                res as unknown as HttpResponse<livableElectronicFenceInfo>;
            if (success) {
                addFormValue.value = {
                    ...data,
                    fenceIn: data.fenceIn ? true : false,
                    fenceOut: data.fenceOut ? true : false,
                    timeValueArray: data.timeValues?.split(',').map(k => k.split('-')) || [[]]
                }
            }
        }
        getElectronicFenceCar() // 查询车辆
        if (props.id) {
            getElectronicFence() // 查询详情
        }
        const handleGroupChange = (d: number[]) => {
            targetKeys.value = d
        }

        // 增加时间段
        const handAddTimer = () => {
            addFormValue.value.timeValueArray!.push([])
        }
        const handDelTimer = (i: number) => {
            addFormValue.value.timeValueArray!.splice(i, 1)
        }
        // 提交表单
        const addForm = ref()
        // 提交表单
        const handleConfirm = async () => {
            const validated = await addForm.value.validate();
            if (validated) {
                const timeValues = addFormValue.value.timeValueArray?.filter(k => k[0]).map(k => k.join('-')).join(',') || null
                const fenceIn = addFormValue.value.fenceIn ? 1 : 0;
                const fenceOut = addFormValue.value.fenceOut ? 1 : 0;
                const params = {
                    ...addFormValue.value,
                    timeValues,
                    fenceIn,
                    fenceOut,
                    carIds: targetKeys.value
                }
                const res = await electronicFenceService[props.id ? 'editElectronicFence' : 'addElectronicFence'](params);
                const { success }: { success: boolean } = res as unknown as HttpResponse;
                if (success) {
                    Message.success('操作成功')
                }
                return success;
            }
            return validated
        }
        return {
            addFormValue,
            ruleValidate,
            handAddTimer,
            handDelTimer,
            carList,
            handleGroupChange,
            defaultTargetKeys,
            handleConfirm,
            addForm
        }
    },
});

</script>
<style lang="less" scoped>
.electronic-fence-modal {
    /deep/.ivu-transfer-list {
        width: 226px;
        height: 327px;
    }

    .alarm-set-box {
        display: flex;
        align-items: center;

        /deep/.ivu-input-suffix {
            background: #F2F3F5;
            width: 52px;
            display: inline-block;
            line-height: 32px;
            text-align: center;
        }

        .alarm-tips {
            margin-bottom: 20px;
            background: #E8F3FF;
            height: 32px;
            line-height: 32px;
            flex: 1;
            padding-left: 16px;
            display: flex;
            overflow: hidden;
            align-items: center;
            /deep/.ivu-tooltip {
                overflow: hidden;
            }

            /deep/.ivu-icon {
                color: #165DFF;
                margin-right: 8px;
            }
        }
    }
}

.duty-time {
    /deep/.ivu-form-item-content {
        position: relative;

        .operation {
            color: #165DFF;
            width: 80px;
            position: absolute;
            right: -90px;
            top: 0;
            cursor: pointer;
        }
    }
}
</style>
