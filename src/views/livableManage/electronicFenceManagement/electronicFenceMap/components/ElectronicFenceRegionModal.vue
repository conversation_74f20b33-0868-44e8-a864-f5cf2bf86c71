<template>
    <div class="map-container-box">
        <div class="list-box">
            <Tooltip content="仅允许添加10个范围" placement="bottom-start" :disabled="pathRanges?.length != 10" transfer>
                <div class="add-range" @click="handleAdd" :class="pathRanges?.length == 10 ? 'disabled' : ''">
                    <Icon type="md-add" />范围
                </div>
            </Tooltip>
            <div class="range-item" :class="actived == index ? 'actived' : ''" v-for="(item, index) in pathRanges"
                @click="handleChooseRange(index)" :key="index">范围{{ index + 1
                }}
                <Icon type="md-close" v-if="actived == index" @click.stop="handleDel(index)" />
            </div>
        </div>
        <div class="map-box">
            <div class="search-box">
                <div class="op-btn start" :class="editStatus ? '' : 'edit'" @click="handleReset">重置</div>
                <div class="" style="pointer-events: auto;">
                    <Input v-model="searchKey" placeholder="请输入关键字" style="width: 240px" />
                    <div class="plate-bg poi-box">
                        <div class="poi-list" v-for="(item, index) in poiList" :key="index" @click="handleChoose(item)">
                            <div class="name"><tooltip-auto-show :content="item.name" /></div>
                            <div class="address"><tooltip-auto-show :content="item.address.length ? item.address : ''" />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div id="range-container" class="range-container"></div>
        </div>
    </div>
</template>
<script lang="ts">
import { defineComponent, onMounted, ref, watch } from 'vue'
import AMapLoader from '@amap/amap-jsapi-loader';
import useDebounce from '@/hooks/useDebounce';
import { SearchResult, SearchTip } from '@/types/map'
import { Message } from 'view-ui-plus';
import { electronicFenceService } from '@/api/livableManage/electronicFenceService'
import { electronicFenceRegion, coordinate } from '@/api/livableManage/type'
import { nextTick } from 'process';
export default defineComponent({
    props: {
        id: {
            type: String,
            default: ''
        }
    },
    setup(props) {
        const map = ref<any>(null);
        const Amap = ref<any>(null);
        const autoComplete = ref<any>(null);
        const polyEditor = ref();
        const ininMap = () => {
            AMapLoader.load({
                key: 'ea53a5291f8f7c1f215ba20930ea9866',
                version: '2.0',
                plugins: ['AMap.AutoComplete', 'AMap.PolygonEditor']
            }).then((AMap) => {
                Amap.value = AMap
                autoComplete.value = new AMap.AutoComplete({ city: '全国' });
                map.value = new AMap.Map('range-container', {
                    resizeEnable: true,
                    zoom: 16,
                    center: [114.61, 30.45],
                })
                getRangPath()

            })
        }
        onMounted(() => {
            ininMap()
        })
        const searchKey = ref('')
        const poiList = ref()
        watch(() => searchKey.value, (val) => {
            useDebounce(() => {
                autoComplete.value.search(val, (status: string, result: SearchResult) => {
                    poiList.value = result.tips
                })
            });
        })
        // 点击重置
        const handleReset = () => {
            if (!editStatus.value) return
            editStatus.value = false;
            map.value.clearMap();
            polyEditor.value.close();
            polyEditor.value.setTarget();
            polyEditor.value.open();
        }
        // poi选中
        const handleChoose = (data: SearchTip) => {
            const { location } = data
            map.value.setCenter([location.lng, location.lat])
            poiList.value = []
        }
        // 提交保存
        const handleConfirm = async () => {
            if (actived.value !== -1) {
                await handleChooseRange(actived.value)
            }
            const params = {
                id: props.id,
                regionList: pathRanges.value
            }
            const res = await electronicFenceService.addElectronicFenceRegion(params);
            const { success }: { success: boolean } = res as unknown as HttpResponse;
            if (success) {
                Message.success('操作成功')
            }
            return success;
        }
        const pathRanges = ref<electronicFenceRegion[]>([{ coordinateList: [] }])
        // 查询范围
        const getRangPath = async () => {
            const res = await electronicFenceService.electronicFenceRegion(props.id);
            const { success, data }: { success: boolean, data: electronicFenceRegion[] } = res as unknown as HttpResponse<electronicFenceRegion[]>;
            if (success) {
                pathRanges.value = data.map(k => ({ coordinateList: k.coordinateList || [] }))
                if (data.length) {
                    if (data[0].coordinateList?.length) {
                        initPath()
                    } else {
                        initPolyEditor()
                    }

                }
                nextTick(() => {
                    startListen()
                })
            }
        }
        const startListen = () => {
            console.log('startListen')
            polyEditor.value?.on('add', (data: any) => {
                console.log('add')
                editStatus.value = true;
                var polygon = data.target;
                polyEditor.value.addAdsorbPolygons(polygon);
                polygon.on('dblclick', () => {
                    polyEditor.value.setTarget(polygon);
                    polyEditor.value.open();
                })
            })
        }
        // 初始化路径编辑
        const initPath = async () => {
            polyEditor.value?.close();
            map.value.clearMap();
            console.log(actived.value)
            if (actived.value == -1) {
                return
            }
            const _path = pathRanges.value[actived.value]?.coordinateList || []
            if (_path.length) {
                const polygon = new Amap.value.Polygon({
                    path: _path.map((k: coordinate) => new Amap.value.LngLat(k.positionX, k.positionY)),     //设置折线的节点数组
                    strokeColor: "#FF33FF",
                    strokeWeight: 6,
                    strokeOpacity: 0.2,
                    fillOpacity: 0.4,
                    fillColor: '#1791fc',
                    zIndex: 50,
                    bubble: true,
                });
                map.value.add([polygon])
                polyEditor.value = new Amap.value.PolygonEditor(map.value, polygon);
            }
            if (!_path.length) {
                editStatus.value = false;
                polyEditor.value.setTarget();
            }
            else editStatus.value = true;
            polyEditor.value.open();
            nextTick(() => {
                map.value.setFitView()
            })
        }
        const editStatus = ref<boolean>(false)
        // 初始化画线区域
        const initPolyEditor = () => {
            polyEditor.value = new Amap.value.PolygonEditor(map.value);
            polyEditor.value.close();
            polyEditor.value.setTarget();
            polyEditor.value.open();
        }
        // 删除范围
        const handleDel = (index: number) => {
            pathRanges.value.splice(index, 1)
            if (actived.value > pathRanges.value.length - 1) {
                actived.value = pathRanges.value.length - 1
            } else {
                // actived.value = index
            }

            initPath()
        }
        // 切换当前选中的范围
        const handleChooseRange = async (index: number) => {
            // 获取当前围栏范围 存下来
            await getActivedPath()
            // 切换到新的标签和范围
            actived.value = index
            initPath()
        }
        // 新增范围
        const handleAdd = () => {
            if (pathRanges.value.length && pathRanges.value.length > 9) return
            // 在后面插入一条新数据
            pathRanges.value.push({ coordinateList: [] })
            // 切换到最新数据
            handleChooseRange(pathRanges.value.length - 1)
            if (pathRanges.value.length == 1) { // 当前新增的为第一个
                initPolyEditor()
                nextTick(() => {
                    startListen()
                })
            }
        }
        // 当前查看的范围
        const actived = ref(0)
        // 存一下数据路径
        const getActivedPath = () => {
            const path = polyEditor.value?.getTarget()
            console.log('getActivedPath', actived.value)
            pathRanges.value[actived.value].coordinateList = path?.getPath()?.map((k: any) => ({ positionX: k.lng, positionY: k.lat })) || []
        }

        return {
            handleConfirm,
            searchKey,
            poiList,
            handleChoose,
            handleReset,
            editStatus,
            handleAdd,
            handleChooseRange,
            actived,
            pathRanges,
            handleDel
        }
    }
})
</script>
<style lang="less" scoped>
.map-container-box {
    display: flex;

    .list-box {
        // position: absolute;
        height: 60vh;
        width: 91px;
        background: #FFF;
        filter: drop-shadow(-2px 1px 13px rgba(83, 117, 167, 0.20));
        display: flex;
        flex-direction: column;
        row-gap: 12px;
        align-items: center;
        padding: 14px 0;
        overflow-y: auto;

        .add-range {
            display: flex;
            width: 75px;
            color: #fff;
            background: #165DFF;
            align-items: center;
            justify-content: center;
            cursor: pointer;

            &.disabled {
                background: #ccc;
                cursor: not-allowed;
            }

        }

        .range-item {
            height: 30px;
            color: #165DFF;
            line-height: 30px;
            cursor: pointer;
            width: 77px;
            padding: 0 8px;

            &.actived {
                background-color: #E8F3FF;
                border: 1px solid #165DFF;
                justify-content: space-between;
                display: flex;
                align-items: center;
            }
        }
    }
}

.map-box {
    position: relative;
    flex: 1;
    height: 60vh;


    .search-box {
        position: absolute;
        top: 16px;
        right: 16px;
        left: 16px;
        z-index: 9;
        width: ~'calc(100% - 32px)';
        justify-content: space-between;
        display: flex;
        pointer-events: none;

        .op-btn {
            padding: 0 16px;
            height: 32px;
            color: #fff;
            background: #165DFF;
            line-height: 32px;
            cursor: pointer;
            pointer-events: auto;

            &.edit {
                background: #E8F3FF;
                color: #4E627E;
                cursor: not-allowed;
            }
        }
    }

    .poi-box {
        // padding: 8px 0;
        max-width: 240px;
    }

    .poi-list {
        display: flex;
        height: 24px;
        align-items: center;
        column-gap: 8px;
        padding: 0 8px;
        cursor: pointer;

        &:first-child {
            padding-top: 8px;
        }

        &:last-child {
            padding-bottom: 8px;
        }

        &:hover {
            background-color: #EEF3FE;
        }

        &>div {
            white-space: nowrap;
            display: block;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .address {
            font-size: 12px;
            color: @text-3-1;
            max-width: 50%;
        }
    }
}

.range-container {
    height: 60vh;
}
</style>
