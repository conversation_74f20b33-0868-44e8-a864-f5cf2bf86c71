<template>
    <div class="details-container">
        <div class="head-cont" :class="deviceDetail?.device.status == 1 ? 'online' : ''">
            <div class="icon-img">
                <!-- 土壤 -->
                <img src="../images/icon_environmental_001.png" class="top-icon" v-if="deviceDetail?.modelIds == '9'" />
                 <!-- 空气 -->
                 <img src="../images/icon_environmental_002.png" class="top-icon" v-else-if="deviceDetail?.modelIds == '11'" />
                 <!-- 噪声 -->
                <img src="../images/icon_environmental_004.png" class="top-icon" v-else-if="deviceDetail?.modelIds == '12'" />
                <!-- 气象 -->
                <img src="../images/icon_environmental_005.png" class="top-icon" v-else-if="deviceDetail?.modelIds == '13'" />
                <!-- 水质 -->
                <img src="../images/icon_environmental_003.png" class="top-icon" v-else-if="deviceDetail?.modelIds == '14'" />
                <!-- 积水 -->
                <img src="../images/icon_environmental_007.png" class="top-icon" v-else-if="deviceDetail?.modelIds == '16'" />
                <!-- 雨情 -->
                <img src="../images/icon_environmental_008.png" class="top-icon" v-else-if="deviceDetail?.modelIds == '17'" />
                <!-- 湖渠 -->
                <img src="../images/icon_environmental_009.png" class="top-icon" v-else-if="deviceDetail?.modelIds == '18'" />
                <!-- 通用 -->
                <img src="../images/icon_environmental_006.png" class="top-icon" v-else />
            </div>
            <s-tag :color="deviceDetail?.device.status == 1 ? '#00B42A' : '#1E2A55'"
                :background="deviceDetail?.device.status == 1 ? '#E8FFEA' : '#F2F3F5'" class="status-tag">
                {{ deviceDetail?.device.status == 1 ? '在线' : '离线' }}
            </s-tag>
        </div>
        <div class="name">
            {{ deviceDetail?.device?.name }}
        </div>
        <div class="code">
            {{ deviceDetail?.device?.code }}
        </div>
        <no-data v-if="!activedObjId" value="请先选择设施" />
        <s-tab :tab-list="tabList" class="tab-info" v-else>
            <template #detail>
                <div class="scorll-map-cont">
                    <div class="device-info-list">
                        <s-label label="设备标识码" :value="deviceDetail?.extendInfo?.bsm" />
                        <s-label label="使用状态" :value="deviceDetail?.device?.useStatus == 1 ? '启用' : '停用'" />
                        <s-label label="区域位置" :value="deviceDetail?.extendInfo?.areaPath?.replace(/@/g, '/')" />
                        <s-label label="权属单位" :value="deviceDetail?.objInfo?.ownerEnterpriseName" />
                        <s-label label="联 系 人" :value="deviceDetail?.objInfo?.contactPerson || '-'" />
                        <s-label label="联系电话" :value="deviceDetail?.objInfo?.contactPhone || '-'" />
                    </div>
                    <div class="alarm-info-list" v-show="deviceDetail?.devicePropertyStatusList?.length">
                        <s-label v-for="(item, index) in deviceDetail?.devicePropertyStatusList || []" :key="index"
                            :label="item.propName" :value="item.value">
                        </s-label>
                    </div>
                </div>
            </template>
            <template #alarm>
                <div class="scorll-map-cont">
                    <no-data v-if="!deviceAlarm.length" value="暂无告警" />
                    <Steps :current="2" direction="vertical" size="small">
                        <Step v-for="(item, index) in deviceAlarm || []" :key="index" :title="item.content"
                            :content="item.alarmTime"></Step>
                    </Steps>
                </div>
            </template>
            <!-- 运维情况 -->
            <template #operation>
                <div class="scorll-map-cont">
                    <div class="device-info-list">
                        <s-label label="养护时间" :value="'2023-04-13 10:12:37'" />
                        <s-label label="养护内容" :value="'养护'" />
                        <s-label label="养护人" :value="'管理员'" />
                        <s-label label="养护类型" :value="'定期'" />
                        <s-label label="养护状态" :value="'已完成'" />
                    </div>
                </div>
            </template>
        </s-tab>
    </div>
</template>
<script lang="ts">
import { defineComponent, ref, watch } from 'vue'
import { ManholeCoverAlarm } from '@/api/manholeCoverService'
import { getDeviceObjInfoDevicePropertyStatusListInfo, alarmList } from '@/api/livableManage/parkSoilService'
import { environmentDevicesDetail } from '@/api/livableManage/type'
export default defineComponent({
    props: {
        activedObjId: {
            type: String,
            default: ''
        },
        activedType: {
            type: String,
            default: ''
        }
    },
    setup(props, ctx) {
        // 设备详情
        const deviceDetail = ref<environmentDevicesDetail>()
        const getDetail = async () => {
            if (!props.activedObjId) {
                deviceDetail.value = {
                    objInfo: {},
                    extendInfo: {},
                    device: {},
                    modelIds: props.activedType
                }
                return
            }
            const res = await getDeviceObjInfoDevicePropertyStatusListInfo({ deviceCode: props.activedObjId,modelId: props.activedType })
            const { data, success }: { data: environmentDevicesDetail, success: boolean } = res as unknown as HttpResponse<environmentDevicesDetail>
            if (success) {
                deviceDetail.value = data
                if (data.device.code) {
                    getManholeMapAlarm(data.device.code)
                }
                // 更新地图上的图标

            }
        }
        //设备告警
        const deviceAlarm = ref<ManholeCoverAlarm[]>([])
        const getManholeMapAlarm = async (deviceCode: string) => {
            if (!props.activedObjId) return
            const res = await alarmList({ customQueryParams: { deviceCodeKey: deviceCode }, page: { size: 5, currnt: 1 } })
            const { data, success }: { data: recordsResponse, success: boolean } = res as unknown as HttpResponse<recordsResponse>
            if (success) {
                deviceAlarm.value = data.records as ManholeCoverAlarm[]
                // 更新地图上的图标

            }
        }
        // 运维情况
        // const operationData = ref<any>()
        // const getOperation = async () => {
        //     if (!props.activedObjId) return
        //     const res = await getOperationList({ customQueryParams: { deviceCodeKey: props.activedObjId }, page: { size: 5, currnt: 1 } })
        //     const { data, success }: { data: recordsResponse, success: boolean } = res as unknown as HttpResponse<recordsResponse>
        //     if (success) {
        //         operationData.value = data.records
        //         // 更新地图上的图标

        //     }
        // }
        watch(() => props.activedObjId, () => {
            getDetail()

        })
        const tabList = ref([
            {
                name: '设备详情',
                key: 'detail',
                icon: '1'
            },
            {
                name: '告警日志',
                key: 'alarm',
                icon: '1'
            },
            {
                name: '运维情况',
                key: 'operation',
                icon: '1'
            }
        ])
        return {
            tabList,
            deviceDetail,
            deviceAlarm,
            // operationData,
        }
    }
})
</script>
<style lang="less" scoped>
/deep/.tab-title .tab-item {
    padding: 0 24px;
    height: 32px;
    color: #4E5969;
    cursor: pointer;
    align-items: center;
    border-radius: 100px;
    display: block;
}
</style>