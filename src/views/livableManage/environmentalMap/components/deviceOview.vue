<template>
    <div class="plate-tit">
        设备总量
    </div>
    <div class="device-overview">
        <div class="device-num">
            {{ overviewData.deviceCount }}
            <div class="online-num">
                在线率<span>{{ overviewData.onlineRate ? (overviewData.onlineRate * 100).toFixed(2) : 0 }}%</span>
            </div>
        </div>
        <div class="device-echart">
            <echart-item :option="pieOption" class="pie-echart" ref="pieRef"/>
        </div>
    </div>
    <div class="plate-tit">
        <img src="../images/icon_manholecover_011.png" class="title-icon" />
        在线情况
    </div>
    <div class="online-overview">
        <echart-item :option="lineOption" class="pie-echart" ref="lineRef"/>
    </div>
    <div class="plate-tit">
        <img src="../images/icon_manholecover_010.png" class="title-icon" />
        告警情况
    </div>
    <div class="alarm-overview">
        <div class="alarm-info">
            <div class="alarm-info">
                <img src="../images/icon_manholecover_012.png" class="title-icon" />
                <div class="ml-6">
                    告警设备数
                    <div class="num">
                        {{ overviewData.alarmDeviceCount }}
                    </div>
                </div>
            </div>
            <div class="alarm-info">
                <img src="../images/icon_manholecover_013.png" class="title-icon" />
                <div class="ml-6">
                    正常设备数
                    <div class="num">
                        {{ overviewData.normalDeviceCount }}
                    </div>
                </div>
            </div>
        </div>
        <div class="alarm-list">
            <div class="alarm-item" v-for="(item, index) in mapAlarmList || []" :key="index">
                <Tooltip :content="item.sbmc" :max-width="200" placement="top" :disabled="(item.sbmc?.length || 0) < 5">
                    {{ item.sbmc }}
                </Tooltip>
                <Tooltip :content="item.content" :max-width="200" placement="top"
                    :disabled="(item.content?.length || 0) < 5">
                    {{ item.content }}
                </Tooltip>
                <span>{{ item.alarmTime }}</span>
            </div>
        </div>
    </div>
</template>
<script lang="ts">
import { defineComponent, ref, watch } from 'vue'
import { ManholeCoverAlarm } from '@/api/manholeCoverService';
import { queryDeviceOnlineAlarmCount, queryDeviceOnlineAlarmCountGroupByModel, alarmList } from '@/api/livableManage/parkSoilService'
import { environmentDevicesOverview } from '@/api/livableManage/type'
import EchartItem from '@/components/common/EchartItem/index'
import { ECOption } from '@/components/common/EchartItem/echart';
import { enumeration } from '@/config/enumeration'
export default defineComponent({
    components: {
        EchartItem
    },
    props: {
        areaPaths: {
            type: Array,
            default: () => []
        },
        activedType: {
            type: String,
            default: ''
        }
    },
    setup(props) {
        // *********************
        // 在线统计
        // *********************
        // 在线统计
        const overviewData = ref<environmentDevicesOverview>({
            onlineCount: 0,
            offlineCount: 0
        })

        // 在线饼状图
        const pieOption = ref<ECOption>({
            tooltip: {
                trigger: 'item',
                confine: true,
            },
            legend: {
                top: 'middle',
                left: 120,
                icon: 'circle',
                itemWidth: 6,
                itemHeight: 6,
                orient: 'vertical',
                textStyle: {
                    color: '#4E627E'
                },
                formatter: (name: String) => {
                    return `${name}  ${name == '在线' ? overviewData.value.onlineCount : overviewData.value.offlineCount}`
                }
            },
            color: ['#7BE188', '#FFCF8B'],
            series: [
                {
                    name: '设备在离线',
                    type: 'pie',
                    radius: ['48%', '68%'],
                    center: ['32%', '50%'],
                    avoidLabelOverlap: false,
                    label: {
                        show: false,
                        position: 'center'
                    },
                    emphasis: {
                        label: {
                            show: true,
                            fontSize: 14,
                            fontWeight: 'bold'
                        }
                    },
                    labelLine: {
                        show: false
                    },
                    data: []
                }
            ]
        })
        const getManholeMapOnline = async () => {
            const params = {
                areaPaths: props.areaPaths,
                queryModelIds: props.activedType ? [props.activedType] : [9, 11, 12, 13, 14, 16, 17, 18]
            }
            const res = await queryDeviceOnlineAlarmCount(params)
            const { data, success }: { data: environmentDevicesOverview, success: boolean } = res as unknown as HttpResponse<environmentDevicesOverview>
            if (success) {
                overviewData.value = data
                pieOption.value.series![0].data = [{ name: '在线', value: data.onlineCount }, { name: '离线', value: data.offlineCount }]

            }
        }

        // *********************
        // 在线情况柱状图
        // *********************
        // 在线柱状图
        const lineOption = ref<ECOption>({
            tooltip: {
                trigger: 'axis',
                axisPointer: {},
                confine: true,
                formatter: '{b}：{c}%'
            },
            grid: {
                top: 30,
                bottom: 25,
                left: 0,
                right: 0,
                containLabel: true
            },
            xAxis: [
                {
                    type: 'category',
                    axisLabel: {
                        color: '#4E568C',
                        interval: 0,
                    },
                    axisLine: {
                        show: false
                    },
                    axisTick: {
                        show: false
                    },
                    data: []
                }
            ],
            yAxis: {
                type: 'value',
                axisLine: {
                    show: false
                },
                axisTick: {
                    show: false
                },
                splitLine: {
                    lineStyle: {
                        color: '#F2F3F5'
                    }
                },
            },
            color: ['#4080FF'],
            series: [
                {
                    data: [],
                    name: '在线率',
                    type: 'bar',
                    itemStyle: {
                        color: '#6CDFDF'
                    },
                    barWidth: '10%',
                    barGap: '10%'
                }
            ]
        })

        // 5条告警列表
        const mapAlarmList = ref<ManholeCoverAlarm[]>([])
        const getMapAlarmsList = async () => {
            const params = {
                page: {
                    current: 1,
                    size: 5
                },
                customQueryParams: {
                    areaPaths: props.areaPaths,
                    modelIds: props.activedType ? [props.activedType] : [9, 11, 12, 13, 14, 16, 17, 18]
                }
            }
            const res = await alarmList(params)
            const { data, success }: { data: recordsResponse, success: boolean } = res as unknown as HttpResponse<recordsResponse>
            if (success) {
                mapAlarmList.value = data.records as ManholeCoverAlarm[]
            }
        }
        // 查询设备在离线告警
        const getMapAlarms = async () => {
            const params = {
                areaPaths: props.areaPaths,
                queryModelIds: props.activedType ? [props.activedType] : [9, 11, 12, 13, 14, 16, 17, 18]
            }
            const res = await queryDeviceOnlineAlarmCountGroupByModel(params)
            const { data, success }: { data: environmentDevicesOverview[], success: boolean } = res as unknown as HttpResponse<environmentDevicesOverview[]>
            if (success) {
                lineOption.value.xAxis![0].data = data.map(k => enumeration.modelTypeList[k.modelId!].slice(0, 2))
                lineOption.value.series![0].data = data.map(k => {
                    return {
                        name: enumeration.modelTypeList[k.modelId!].slice(0, 2),
                        value: ((k.deviceCount ? k.onlineCount! / k.deviceCount : 0) * 100).toFixed(2)
                    }
                })
            }
        }
        const pieRef = ref()
        const lineRef = ref()
        const handleResize = () => {
            pieRef.value.handleResize()
            lineRef.value.handleResize()
        }
        watch(props, () => {
            getManholeMapOnline()
            getMapAlarmsList()
            getMapAlarms()
        }, { immediate: true })
        return {
            pieOption,
            lineOption,
            mapAlarmList,
            overviewData,
            handleResize,
            pieRef,
            lineRef
        }
    }
})
</script>
<style lang="less" scoped>
/deep/.ivu-tooltip {
    height: 40px;

    .ivu-tooltip-rel {
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        line-height: 40px;
        width: 100%;
    }
}
</style>
