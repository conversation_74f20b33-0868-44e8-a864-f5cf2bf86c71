<template>
    <div>
        <BreadcrumbCustom>
            <BreadcrumbItem>环境监测</BreadcrumbItem>
            <BreadcrumbItem>环境概览</BreadcrumbItem>
        </BreadcrumbCustom>
        <Card dis-hover :bordered="false" class="map-cont" id="fullscreen">
            <div class="face-plate-cont">
                <div class="left-plate">
                    <!-- 区域树 -->
                    <div class="area-tree plate-bg">
                        <map-area-tree-select type="1" @updateAreaPath="updateAreaPath" table="livable_garden_soil_device"
                            modelIds="9,11,12,13,14,16,17,18" />
                    </div>
                    <div class="device-box">
                        <!-- 设备类型 -->
                        <div class="device-type-list plate-bg">
                            <div class="scorll-map-cont">
                                <div class="device-type-item" :class="activedType == '' ? 'actived' : ''"
                                    @click="activedType = ''" @mouseenter="overType = 0" @mouseleave="overType = ''">
                                    全部<span>{{ deviceList.length }}</span></div>
                                <div class="device-type-item" :class="activedType == item.modelId ? 'actived' : ''"
                                    v-for="(item, index) in modelTypeList" :key="index" @click="activedType = item.modelId"
                                    @mouseenter="overType = item.modelId" @mouseleave="overType = ''">
                                    {{ item.name }}<span>{{ item.num }}</span></div>
                            </div>
                        </div>
                        <!-- 设备列表 -->
                        <div class="device-cont plate-bg">
                            <!-- <div class="close-device">
                                <Icon type="md-close" @click="showDeviceList = false" />
                            </div> -->
                            <Input suffix="ios-search" placeholder="请输入设备信息" clearable v-model="searchText" />
                            <scorll-box :scorll-id="activedObjId" key="code">
                                <template #cont>
                                    <map-device-list
                                        :deviceList="deviceList.filter(k => overType === '' ? (k.modelId == activedType || activedType === '') : (overType === 0 || k.modelId == overType))"
                                        :searchText="searchText" lableKey="name" valueKey="code"
                                        :activedObjId="activedObjId" @actived-obj="handleActivedObj" primaryKey="code" />
                                </template>
                            </scorll-box>
                        </div>
                    </div>
                </div>
                <div class="right-plate">
                    <div class="full-op" @click="handleFullScreen">
                        <Icon :type="fullFlag ? 'md-contract' : 'md-expand'" />
                    </div>
                    <div class="device-info-cont">
                        <s-tab :tab-list="tabList" class="plate-bg" tit-class="right-tab-title"
                            :body-class="`${isToggleDropdown ? '' : 'hide-tab-box'} right-tab-body`"
                            @handleChange="handleChange" :default-active="defaultActive">
                            <template #expand>
                                <div class="toggle" @click="isToggleDropdown = !isToggleDropdown">
                                    <Icon :type="isToggleDropdown ? 'ios-arrow-down' : 'ios-arrow-up'" />
                                </div>
                            </template>
                            <template #overview>
                                <div class="scorll-map-cont manhole-cover-right">
                                    <device-oview :area-paths="areaPaths" :actived-type="activedType" ref="overviewRef" />
                                </div>
                            </template>
                            <template #detail>
                                <device-detail :actived-obj-id="activedObjId" :actived-type="activedType" />
                            </template>
                        </s-tab>
                    </div>
                </div>
            </div>
            <div id="container" class="container-map" :class="fullFlag ? 'full-map' : ''"></div>
        </Card>
    </div>
</template>

<script lang="ts">
import AMapLoader from '@amap/amap-jsapi-loader';
import { defineComponent, nextTick, onMounted, ref, Ref, watch } from 'vue';
import { queryDevices } from '@/api/livableManage/parkSoilService'
import { environmentDevices } from '@/api/livableManage/type'
import DeviceOview from './components/deviceOview.vue';
import DeviceDetail from './components/deviceDetail.vue';
import { launchIntoFullscreen, exitFullscreen,filterArr} from '@/utils/tool'
import mapAreaTreeSelect from '@/components/common/mapAreaTreeSelect/index'
import { enumeration } from '@/config/enumeration'
import scorllBox from '@/components/common/scorllBox/index'
import { getMarkerContent, renderClusterMarker, renderMarker, showInfoWindow } from '@/components/common/mapAreaTreeSelect/markercontent'
import mapDeviceList from '@/components/common/mapDeviceList'
export default defineComponent({
    components: {
        DeviceOview,
        DeviceDetail,
        mapAreaTreeSelect,
        scorllBox,
        mapDeviceList
    },
    setup(props, ctx) {
        // *********************
        // 地图
        // *********************

        const map = ref<any>(null);
        const Amap = ref<any>(null);
        const iconImgs = {
            '9': './images/icon_environmental_001.png',// '园林土壤设备',
            '11': './images/icon_environmental_002.png',//空气质量
            '12': './images/icon_environmental_004.png',//噪声环境
            '13': './images/icon_environmental_005.png',//气象环境
            '14': './images/icon_environmental_003.png',// 水质监测
            '16': './images/icon_environmental_007.png',// 积水监测
            '17': './images/icon_environmental_008.png',// 雨情监测
            '18': './images/icon_environmental_009.png',// 湖渠监测
        }
        const cluster = ref<any>(null);
        const ininMap = () => {
            AMapLoader.load({
                key: 'ea53a5291f8f7c1f215ba20930ea9866',
                plugins: ['AMap.MarkerCluster'],
                version: '2.0'
            }).then((AMap) => {
                Amap.value = AMap
                map.value = new AMap.Map('container', {
                    resizeEnable: true,
                    // mapStyle: 'https://geohub.amap.com/mapstyle/clone?id=20d3f4f1e43c80b9c68c30fb8a1ab94b',
                    zoom: 16,
                    center: [114.61, 30.45],
                })
                map.value.on('movestart', () => {
                    map.value?.clearInfoWindow()
                })

                map.value.on('zoomstart', () => {
                    map.value?.clearInfoWindow()
                })
                nextTick(() => {
                    getQueryDevices([], true)
                })
            })
        }
        const _renderClusterMarker = async (context: any) => {
            // console.log('_renderClusterMarker')
            renderClusterMarker(context, activedObjId.value, (_data: string[][], imgUrl: any) => {
                const activeDevice = _data.find(k => k[0] == activedObjId.value)
                const _name = activeDevice?.[2]
                const _id = activeDevice?.[3] || _data[0][3]
                return getMarkerContent({ code: _name ? activedObjId.value : null }, activedObjId.value, 'hide-tag', () => {
                    return _id ? require(`${iconImgs[_id]}`) : imgUrl
                }, 'code')


            })
        }
        const _renderMarker = async (context: any) => {
            // console.log('_renderMarker')
            renderMarker(context, (id: number) => {
                const activeDevice = deviceList.value.find(k => k.code == `${id}` && (activedType.value == k.modelId||!activedType.value))
                return getMarkerContent(activeDevice, activedObjId.value, 'hide-tag', (d: any) => {
                    return require(`${iconImgs[d.modelId || '9']}`)
                }, 'code');
            })
        }
        // 更新地图上的图标
        const updateMapIcon = (data: environmentDevices[]) => {
            // 清除地图上的图标
            if (!map.value) return;
            // map.value.clearMap()
            if (cluster.value) {
                cluster.value.setMap(null);
            }
            const p = filterArr(data,'code'); // 根据code去重复
            const points = p?.filter((k: environmentDevices) => k?.gdx && k?.gdy).map(k => ({ lnglat: [k?.gdx, k.gdy], exData: `${k.code}@${k.code || 0}@${k.name}@${k.modelId}` })) || [];
            cluster.value = new Amap.value.MarkerCluster(map.value, points, {
                maxZoom: 24,
                gridSize: 30, // 设置网格像素大小
                renderClusterMarker: _renderClusterMarker, // 自定义聚合点样式
                renderMarker: _renderMarker, // 自定义非聚合点样式
            });
            cluster.value.on('click', (e: any) => {
                const { clusterData } = e
                map.value.clearInfoWindow()
                if (clusterData.length == 1) {
                    const _d = clusterData[0].exData.split('@')[0]
                    setActivedObjId(_d)
                } else {
                    showInfoWindow(clusterData, e, map.value, activedObjId.value, (_d: string) => {
                        map.value?.clearInfoWindow()
                        setActivedObjId(_d)
                    })
                }
            })
            // 自适应显示
            new Amap.value.Polygon({
                path: points.map(k => k.lnglat),  //以5个点的坐标创建一个隐藏的多边形
                map: map.value,
                strokeOpacity: 0,//透明
                fillOpacity: 0,//透明
                bubble: true//事件穿透到地图
            });
            var overlaysList = map.value.getAllOverlays('polygon');//获取多边形图层

            //2.使用setFitView方法
            map.value.setFitView(overlaysList);//自适应显示

        }
        const setActivedObjId = (_d: string) => {
            if (activedObjId.value == _d) {
                activedObjId.value = ''
                defaultActive.value = 0
                return
            }
            defaultActive.value = 1
            activedObjId.value = _d
        }
        onMounted(() => {
            ininMap();
            window.addEventListener('fullscreenchange', e => {
                // 监听到屏幕变化，在回调中判断是否已退出全屏
                const isFull = document.fullscreenElement || document['mozFullScreenElement'] || document['webkitFullscreenElement'];
                fullFlag.value = isFull ? true : false
            })
        })
        // *********************
        // 左侧区域树
        // *********************
        // 搜索树
        const areaPaths = ref<string[]>([])
        // 更新当前勾选区域
        const updateAreaPath = (areaPath: string[]) => {
            areaPaths.value = areaPath
            // 查询当前区域的设施
            getQueryDevices(areaPath)
            defaultActive.value = 0
            activedType.value = ''
        }
        // *********************
        // 左侧设备类型列表
        // *********************
        const modelTypeList = ref<any[]>([])
        const activedType = ref<string>('')
        const overType = ref<string | number>('')
        watch(activedType, () => {
            updateMapIcon(deviceList.value.filter(k => !activedType.value || k.modelId == activedType.value))
        })
        // *********************
        // 左侧设施列表
        // *********************
        // 设施搜索关键字
        const searchText = ref<string>('')
        // 选中的设施
        const activedObjId = ref<string>('')
        // 监听选中的设施
        watch(activedObjId, () => {
            const activeDevice = deviceList.value.find(k => k.code == activedObjId.value)
            if (activeDevice?.gdx) { // 存在坐标 处理地图
                map.value?.setZoomAndCenter(17, [activeDevice.gdx, activeDevice.gdy])
            }
            const overlays: any[] = map.value?.getAllOverlays('marker')
            overlays?.forEach(e => {

                const markerInfo = deviceList.value.find(k => k.code == e.getExtData())
                if (!markerInfo) {// 根据选中的id修改图标和name
                    const extData = e.getExtData()
                    const _data = extData.map((k: any) => k.split('@'))
                    const _name = _data.find((k: any) => k[0] == activedObjId.value)?.[2]
                    const _modelId = _data.find((k: any) => k[0] == activedObjId.value)?.[3]|| _data[0][3]
                    // console.log(_data)
                    const content = getMarkerContent({ code: _name ? activedObjId.value : null }, activedObjId.value, 'hide-tag', () => {
                        return require(`${iconImgs[_modelId || '9']}`)
                    }, 'code')
                    const lable = `${content}<div class="marker-label-box label-box"><span class="name">${_name || _data[0][2]}</span><span class="num">${_data.length}</span></div>`
                    e.setContent(lable)
                    return
                }
                const content = getMarkerContent(markerInfo, activedObjId.value, 'hide-tag', (d: environmentDevices) => {
                    return require(`${iconImgs[d.modelId || '9']}`)
                }, 'code')
                e.setContent(`${content}<div class="marker-label-box label-box">${markerInfo.name}</div>`)
            })
            if (activedObjId.value) {
                isToggleDropdown.value = true
            }
        })
        // 设备列表
        const deviceList = ref<environmentDevices[]>([])
        // 手动设置当前选中设备
        const handleActivedObj = (item: environmentDevices) => {
            activedObjId.value = (activedObjId.value == item.code) ? '' : (item.code || '')
            if (activedObjId.value) {
                defaultActive.value = 1
                return
            }
            defaultActive.value = 0
        }
        // 查询当前区域的设施
        const getQueryDevices = async (areaPaths?: string[], initialization?: boolean) => {
            const params = {
                // modelIds: [9,11,12,13,14,16,17,18], // modelIds初始值写在后端
                queryModelIds: [],
                areaPaths
            }
            console.log('params', params)
            const res = await queryDevices(params)
            const { data, success }: { data: environmentDevices[], success: boolean } = res as unknown as HttpResponse<environmentDevices[]>
            if (success) {
                deviceList.value = data;
                const actived_Obj = deviceList.value.filter(k => k.code == activedObjId.value)
                if (!actived_Obj.length) { // 已经勾选的设备不在当前区域树中
                    activedObjId.value = deviceList.value[0]?.code || ''
                }
                const modelIds = Array.from(new Set(data.map(k => k.modelId))) as string[]
                modelTypeList.value = modelIds.map(k => {
                    return {
                        name: enumeration.modelTypeList[+k],
                        modelId: k,
                        num: data.filter(m => m.modelId == k).length
                    }
                })
                // 更新地图上的图标
                updateMapIcon(deviceList.value)

            }
        }

        // 地图全屏
        const fullFlag: Ref<boolean | null> = ref(false)
        const handleFullScreen = () => {
            const element = document.getElementById('fullscreen');
            if (!element) return
            // false是进入全屏状态
            if (fullFlag.value) {
                // 退出全屏
                exitFullscreen(element);
            } else {
                launchIntoFullscreen(element);
            }
            fullFlag.value = !fullFlag.value
        }
        // 是否展示设备列表
        const showDeviceList = ref<boolean>(true)
        // 是否展开右侧
        const isToggleDropdown = ref<boolean>(true);
        watch(() => isToggleDropdown.value, (val) => {
            if (val) {
                overviewRef.value.handleResize()
            }
        })
        const tabList = ref([
            {
                name: '设备概览',
                key: 'overview',
                icon: 'dashboard'
            },
            {
                name: '设备详情',
                key: 'detail',
                icon: 'common'
            }
        ])
        // 概览ref
        const overviewRef = ref()
        const defaultActive = ref<number>(0)
        // 切换回需要resize
        const handleChange = (i: number) => {
            if (i == 0) {
                overviewRef.value.handleResize()
            }
            defaultActive.value = i
            isToggleDropdown.value = true
        }
        return {
            deviceList,
            tabList,
            areaPaths,
            activedObjId,
            searchText,
            handleFullScreen,
            fullFlag,
            isToggleDropdown,
            updateAreaPath,
            activedType,
            modelTypeList,
            showDeviceList,
            handleChange,
            overviewRef,
            defaultActive,
            handleActivedObj,
            overType
        };
    }
});
</script>

<style lang="less" scoped>
@import '../../../styles/mapPage.less';
@import './index.less';
</style>
