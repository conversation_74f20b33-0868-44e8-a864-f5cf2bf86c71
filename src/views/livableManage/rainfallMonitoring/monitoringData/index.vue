<template>
    <BreadcrumbCustom>
        <BreadcrumbItem>雨情监测</BreadcrumbItem>
        <BreadcrumbItem>监测数据</BreadcrumbItem>
    </BreadcrumbCustom>
    <monitoring-data :model-id="17" :attr-list="[
            {
                attr: 'rainfall',
                name: '降雨量',
                unit: 'mm'
            }
        ]"
/>
</template>
<script lang="ts">
import { defineComponent } from 'vue'
import MonitoringData from './monitoringData.vue'
export default defineComponent({
    name: 'RainfallMonitoringData',
    components: {
        MonitoringData
    }
})
</script>
<style lang="less" scoped>
@import './index.less';
</style>
