<template>
    <ContentCard title="监测数据"  style="margin-bottom: 17px; padding-bottom: 0px;" class="rainfall-form">
        <BaseForm :model="formData" :label-width="90" @handle-submit="handleSubmit" ref="formRef" :rules="formRules"
            :initFormData="true" @handleReset="handleReset" >
            <template #formitem>
                <FormItem label="设备编号" prop="deviceCode">
                    <device-drop-down-select :modelId="modelId" v-model="formData.deviceCode" firstSelect
                        @initSelect="(initFormData = JSON.stringify(formData)) && handleSubmit()"
                        @onChange="deviceOnChange" />
                </FormItem>
                <FormItem label="选择时间" prop="timeRange">
                    <DatePicker v-model="formData.timeRange" type="daterange" format="yyyy-MM-dd" placeholder="请选择" :editable="false" />
                </FormItem>
            </template>
        </BaseForm>
    </ContentCard>
    <div class="frame" style="margin-bottom: 15px">
        <Card class="card" shadow padding="0">
            <imgCard :img="require('./image/icon_rain1.png')" label="当前雨量" :value="currentRecord.rainfallIntensity"
                     unit="mm" />
        </Card>
        <Card class="card" shadow padding="0">
            <imgCard :img="require('./image/icon_rain2.png')" label="今日降雨量" :value="currentRecord.todayRainfall"
                     unit="mm" />
        </Card>
        <Card class="card" shadow padding="0">
            <imgCard :img="require('./image/icon_rain3.png')" label="本周降雨量" :value="currentRecord.weekRainfall"
                     unit="mm" />
        </Card>
        <Card class="card" shadow padding="0">
            <imgCard :img="require('./image/icon_rain4.png')" label="本月降雨量" :value="currentRecord.mouthRainfall"
                     unit="mm" />
        </Card>
    </div>
    <ContentCard>
        <div class="switch" @click="(switchBtn = !switchBtn) && lineRef.handleResize()">
            <icon :custom="`iconfont ${switchBtn ? 'icon-list' : 'icon-apps'}`" />
            {{ switchBtn ? '切换表格' : '切换图表' }}
        </div>
        <!-- 图表视图 START -->
        <div class="echart-box" v-show="switchBtn">
            <div class="echart-title">
                <s-label label="设备名称" :value="formData.currentName" />
                <s-label label="设备编号" :value="formData.currentCode" />
            </div>
            <div class="echart-overview">
                <no-data v-if="!tableData?.length" value="当前设备暂无数据" class="nodata-box" />
                <echart-item :option="lineOption" @initEchart="initEchart" v-show="tableData?.length" ref="lineRef" />
            </div>
        </div>
        <!-- 表格视图 START -->
        <baseTable :columns="columns" :data="tableData" v-show="!switchBtn" />
    </ContentCard>
</template>
<script lang="ts">
import { defineComponent, onMounted, ref } from 'vue'
import { historicDataPageFromEs } from '@/api/livableManage/monitorService'
import { queryHistoricDataPageFromEs, newDataFromEs } from '@/api/livableManage/rainfallMonitoringService.js'
import deviceDropDownSelect from '@/components/common/deviceDropDownSelect/index'
import EchartItem from '@/components/common/EchartItem/index'
import { ECOption } from '@/components/common/EchartItem/echart';
import { validateform } from '@/utils/validateform'
import Util from '@/utils';
export default defineComponent({
    components: {
        EchartItem,
        deviceDropDownSelect
    },
    props: {
        modelId: {
            type: Number,
            default: 9
        },
        attrList: {
            type: Array,
            default: () => [{
                attr: 'temperature',
                name: '土壤温度',
                unit: '℃'
            },
            {
                attr: 'humidity',
                name: '土壤湿度',
                unit: '%'
            }]
        }
    },
    setup(props) {
        // 切换视图按钮
        const switchBtn = ref<boolean>(true);
        // *********************
        // 折线图
        // *********************
        interface attrInfo {
            attr: string;
            name: string;
            unit: string
        }
        const attrList: attrInfo[] = props.attrList as attrInfo[]
        // 折线颜色静态数据
        const colorList = ['#81E2FF']
        // 折线图数据
        const lineOption = ref<ECOption>({
            grid: {
                top: 65,
                left: 5,
                right: 5,
                bottom: 80,
                containLabel: true
            },
            color: colorList,
            legend: {
                show: true,
                bottom: 5,
                icon: 'circle',
                itemHeight: 14,
                itemWidth: 14,
                itemStyle: {
                    borderRadius: 14
                },
                data: [
                ]
            },
            tooltip: {
                trigger: 'axis',
                confine: true,
                axisPointer: {
                }
            },
            dataZoom: [
                {
                    type: 'inside',
                    start: 0,
                    minValueSpan: 10,
                    end: 100
                },
                {
                    start: 0,
                    end: 10,
                    height: 14,
                    bottom: 55,
                    backgroundColor: '#F2F3F5',
                    dataBackground: {
                        lineStyle: {
                            width: 1,
                            color: 'rgba(4, 135, 255, 0.5)'
                        },
                        areaStyle: {
                            color: 'rgba(4, 135, 255, 0.3)'
                        }
                    },
                    handleStyle: {
                        opacity: 0
                    },
                    borderColor: 'rgba(4, 135, 255, 0)',
                    fillerColor: 'rgba(206, 224, 255, 0.5)'
                }
            ],
            xAxis: [
                {
                    type: 'category',
                    axisTick: {
                        show: false
                    },
                    axisLine: {
                        show: true
                    },
                    data: []
                }
            ],
            yAxis: [
                {
                    type: 'value',
                    axisTick: {
                        show: false
                    },
                    axisLine: {
                        show: false
                    }
                }
            ],
            series: [{
                smooth: true
            }]
        })
        // 定义表单数据
        const formData = ref(
            {
                deviceCode: '',
                currentName: '',
                currentCode: '',
                timeRange: [new Date(new Date().getTime() - 86400000), new Date()],
                fieldList: attrList.map((k) => k?.attr)
            }
        )
        // 定义表单验证
        const formRules = ref({
            deviceCode: [validateform.required],
            timeRange: [validateform.requiredArray, validateform.maxDayRang365]
        })
        // 定义表格列头
        const defaultColumns = ref<any[]>([])
        const columns = ref<any[]>([])
        const tableData = ref<historicDataPageFromEs[]>([])
        const formRef = ref<any>()
        // 自定义初始化表单数据
        const initFormData = ref<string>('');
        // 手动重置表单
        const handleReset = () => {
            formData.value = JSON.parse(initFormData.value)
            currentDevice.value = initCurrentDevice.value
            handleSubmit();
        }
        const currentRecord=ref({
            rainfallIntensity:'--',
            todayRainfall:'--',
            weekRainfall:'--',
            mouthRainfall:'--',
        })
        // 提交表单
        const handleSubmit = async () => {
            const valid = await formRef.value?.validate?.();
            // 记录当前查询的设备，以便显示
            formData.value.currentName = currentDevice.value.label || currentDevice.value.sbmc || ''
            formData.value.currentCode = currentDevice.value.value || currentDevice.value.deviceCode || ''
            const params = Util.objClone(formData.value)
            // 处理时间数据
            params.queryTimeStart = Util.formatDate(params.timeRange[0], 'yyyy-MM-DD') + ' 00:00:00'
            params.queryTimeEnd = Util.formatDate(params.timeRange[1], 'yyyy-MM-DD') + ' 23:59:59'
            // 请求
            const res1:any= await newDataFromEs(formData.value.currentCode)
            if (res1.success) {
                currentRecord.value=res1.data
            } else {
                currentRecord.value = {
                    rainfallIntensity:'--',
                    todayRainfall:'--',
                    weekRainfall:'--',
                    mouthRainfall:'--',
                }
            }

            params.order = "asc"
            // 请求
            const res = await queryHistoricDataPageFromEs(params)
            const { data, success }: { success: boolean, data: recordsResponse<historicDataPageFromEs[]> } = res as unknown as HttpResponse<recordsResponse<historicDataPageFromEs[]>>
            // 成功
            if (success) {
                // 设置表格数据
                const list = (data?.records || []).map(k => ({ ...k, deviceCode: formData.value.currentCode, deviceName: formData.value.currentName || '--' }))
                const listCopy = JSON.parse(JSON.stringify(list))
                // 列表以时间逆序
                tableData.value = listCopy.sort((a: historicDataPageFromEs, b: historicDataPageFromEs) => new Date(b.createTime as string).getTime() - new Date(a.createTime as string).getTime())
                // 默认表头
                const _defaultColumns: any = [
                    { title: "设备编号", key: "deviceCode", tooltip: true, fixed: 'left' },
                    { title: "设备名称", key: "deviceName", tooltip: true, fixed: 'left' },
                    { title: "时间", key: "createTime", tooltip: true, fixed: 'right', minWidth: 160 }
                ]
                // 设置折线图数据
                const series: any[] = []
                lineOption.value.xAxis![0].data = list.map(item => item.createTime)
                lineOption.value.legend!['data'] = attrList.map((attr, i) => {
                    return {
                        icon: 'circle',
                        name: `${attr.name}(${attr.unit})`,
                        itemStyle: {
                            color: '#fff',
                            borderColor: colorList[i],
                            borderWidth: 5
                        }
                    }
                })
                attrList.forEach((attr, i) => {
                    _defaultColumns.splice(i + 2, 0, { title: `${attr.name}(${attr.unit})`, key: attr.attr, tooltip: true })
                    series.push({
                        // showSymbol: false,
                        name: attr.name + '(' + attr.unit + ')',
                        type: 'bar',
                        barMaxWidth: '40',
                        data: tableData.value.map(item => item[attr.attr] || 0),
                        smooth: true
                    })
                })
                defaultColumns.value = _defaultColumns // 存一份全量表头
                columns.value = _defaultColumns
                lineOption.value.series = series

            }
        }
        // 获取初始化后的echart实例
        const initEchart = (echart: any) => {
            echart.off('legendselectchanged').on('legendselectchanged', ({ selected }: { selected: any }) => {
                // 修改legend选中状态 重新渲染表头
                columns.value = defaultColumns.value.filter(k => selected[k.title] == true || selected[k.title] == undefined)
            })
        }
        interface deviceInfo {
            label?: string,
            value?: string,
            deviceCode?: string,
            sbmc?: string
        }
        const initCurrentDevice = ref<deviceInfo>({}) // 存一份初始选中的设备
        const currentDevice = ref<deviceInfo>({})
        const deviceOnChange = (data: deviceInfo) => {
            if (!initCurrentDevice.value.deviceCode) {
                initCurrentDevice.value = data
            }
            currentDevice.value = data
        }
        const modelId = ref(props.modelId)
        const lineRef = ref()
        return {
            currentRecord,
            handleSubmit,
            columns,
            formData,
            lineOption,
            switchBtn,
            tableData,
            initEchart,
            deviceOnChange,
            formRef,
            formRules,
            initFormData,
            handleReset,
            modelId,
            lineRef
        }
    }
})
</script>
<style lang="less" scoped>
@import './index.less';
</style>
