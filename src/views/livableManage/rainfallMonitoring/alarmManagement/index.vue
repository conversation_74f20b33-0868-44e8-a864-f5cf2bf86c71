<script lang="ts" setup>
import SelectAlarmType from '@/components/common/selectAlarmType/index.vue';
import { getCurrentInstance, onMounted, ref } from 'vue'
import TableContentCard from '@/components/global/TableContentCard'
import SideDetail from '@/components/global/SideDetail'


const tableList = ref<any>([
  { title: '设备编号', key: 'deviceCode', tooltip: true },
  { title: '设备名称', key: 'sbmc', tooltip: true },
  { title: '设备标识码', key: 'bsm', tooltip: true, minWidth: 100 },
  { title: '告警类型', key: 'alarmTypeName', tooltip: true, maxWidth: 130 },
  { title: '告警等级', slot: 'level', tooltip: true, width: 80 },
  { title: '告警详情', key: 'content', tooltip: true },
  { title: '是否推送', slot: 'pushStatus', tooltip: true, width: 80 },
  { title: '区域位置', key: 'areaPath', tooltip: true },
  { title: '告警时间', key: 'alarmTime', width: 160, tooltip: true },
  { title: '操作', slot: 'action', maxWidth: 100 }
])
const searchObj = ref<any>({})
const listCom = ref()
interface searchObjType {
  szjd?: string,
  szsq?: string,
  szdywg?: string,
  deviceCode?: string,
  sbmc?: string,
  bsm?: string,
  status?: number,
  useStatus?: number,
  areaLocation?: string,
  areaPaths?: Array<string>,
  modelIds?: Array<string>
}
function handleSubmit() {
  let params: searchObjType = {}
  params = JSON.parse(JSON.stringify(searchObj.value))
  if (searchObj.value.areaLocation) {
    const arr: Array<string> = searchObj.value.areaLocation.split('/')
    params.szjd = arr[0]
    params.szsq = arr[1]
    params.szdywg = arr[2]
    delete params.areaLocation
  }
   // 内置模块id
  params.modelIds = ['17']
  listCom.value.search(params)
}
onMounted(() => {
  handleSubmit()
})

const showSideDetail = ref<boolean>(false)
const detailInfo = ref<any>(false)
function checkDetail(row:any) {
  showSideDetail.value = true
  detailInfo.value = row
}
function closeSide() {
  showSideDetail.value = false
}

const that = getCurrentInstance()?.appContext.config.globalProperties
const { $enumeration } = that

</script>
<template>
  <BreadcrumbCustom>
    <BreadcrumbItem>雨情监测</BreadcrumbItem>
    <BreadcrumbItem>告警管理</BreadcrumbItem>
  </BreadcrumbCustom>
  <ContentCard title="告警管理">
    <BaseForm @handle-submit="handleSubmit" :model="searchObj" inline :label-width="90">
      <template #formitem>
        <FormItem label="设备编号" prop="deviceCodeKey">
          <Input v-model="searchObj.deviceCodeKey" :maxlength="20" placeholder="请输入" clearable></Input>
        </FormItem>
        <FormItem label="区域位置" prop="areaLocation">
            <AreaSelectTree v-model="searchObj.areaLocation" table-name="livable_rainfall_monitoring_device" :is-alarm="true" :model-ids="[17]" />
        </FormItem>
        <FormItem label="设备标识码" prop="bsm">
          <Input v-model="searchObj.bsm" :maxlength="20" placeholder="请输入" clearable></Input>
        </FormItem>
        <FormItem label="告警类型" prop="alarmType">
          <!-- <Select :transfer="false" v-model="searchObj.alarmType" clearable>
            <Option v-for="(value, key) in $store.getters.dictionary.rainfall_alarm_type" :key="key"
              :value="key" clearable>
              {{ value }}
            </Option>
          </Select> -->
          <!-- <dict-drop-down-select code="rainfall_alarm_type" v-model="searchObj.alarmType" /> -->
          <select-alarm-type v-model="searchObj.alarmType" :param="{modelId: 17, type: 2}" />
        </FormItem>
        <FormItem label="告警等级" prop="level">
          <Select :transfer="false" v-model="searchObj.level" clearable>
            <Option v-for="(item, index) in $enumeration.alarmGrade.filter((i: string, index: number) => index > 0)" :key="index"
              :value="index+1" clearable >
              {{ item }}
            </Option>
          </Select>
        </FormItem>
        <FormItem label="是否推送" prop="pushStatus">
          <Select :transfer="false" v-model="searchObj.pushStatus" clearable>
            <Option v-for="(item, index) in $enumeration.isPush" :key="index" :value="index" clearable>
              {{ item }}
            </Option>
          </Select>
        </FormItem>
      </template>
    </BaseForm>
    <TableContentCard :base-btn="false">
      <template #btn>
        <!-- <Button type="primary">导出</Button>
        <Button type="primary">告警设置</Button> -->
      </template>
      <baseTable :model="searchObj" ref="listCom" url="/alarm/list" :columns="tableList">
        <!-- <template #alarmType="{ row }">
          <div>
            {{ $store.getters.dictionary.rainfall_alarm_type[row.alarmType] }}
            <dict-label code="rainfall_alarm_type" :value="row.alarmType" />
          </div>
        </template> -->
        <template #level="{ row }">
          <div>
            {{ $enumeration.alarmGrade[row.level] }}
          </div>
        </template>
        <template #pushStatus="{ row }">
          <div>
            <!-- {{ $enumeration.isPush[row.pushStatus] }} -->
            <pushStatus :value="row.pushStatus" />
          </div>
        </template>
        <template #action="{row}">
          <LinkBtn size="small" @click="checkDetail(row)">查看</LinkBtn>
        </template>
      </baseTable>
    </TableContentCard>
  </ContentCard>
  <SideDetail :show="showSideDetail" :data="detailInfo" :model-id="17" @on-cancel="closeSide"></SideDetail>
</template>

<style lang="less" scoped></style>
