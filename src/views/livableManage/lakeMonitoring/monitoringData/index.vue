<template>
    <BreadcrumbCustom>
        <BreadcrumbItem>湖渠监测</BreadcrumbItem>
        <BreadcrumbItem>监测数据</BreadcrumbItem>
    </BreadcrumbCustom>
    <monitoring-data :model-id="18" :attr-list="[
            {
                attr: 'water_speed',
                name: '流速',
                unit: 'm/s'
            },
            {
                attr: 'liquid_level_value',
                name: '水位',
                unit: 'm'
            },
            {
                attr: 'instantaneous_flow',
                name: '瞬时流量',
                unit: 'm³/s'
            },
            {
                attr:'water_flux',
                name:'累计流量',
                unit:'t'

            }
        ]"
/>
</template>
<script lang="ts">
import { defineComponent } from 'vue'
import MonitoringData from '../../components/monitoringData.vue'
export default defineComponent({
    components: {
        MonitoringData
    }
})
</script>
<style lang="less" scoped>
</style>
