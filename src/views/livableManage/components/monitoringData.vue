<template>
    <ContentCard title="监测数据">
        <BaseForm :model="formData" :label-width="90" @handle-submit="handleSubmit" ref="formRef" :rules="formRules"
            :initFormData="true" @handleReset="handleReset">
            <template #formitem>
                <FormItem label="设备编号" prop="deviceCode">
                    <device-drop-down-select :modelId="modelId" v-model="formData.deviceCode" firstSelect
                        @initSelect="(initFormData = JSON.stringify(formData)) && handleSubmit()"
                        @onChange="deviceOnChange" />
                </FormItem>
                <FormItem label="选择时间" prop="timeRange">
                    <DatePicker v-model="formData.timeRange" type="daterange" format="yyyy-MM-dd" placeholder="请选择"
                        :editable="false" />
                </FormItem>
            </template>
        </BaseForm>
        <div class="switch" @click="(switchBtn = !switchBtn) && lineRef.handleResize()">
            <icon :custom="`iconfont ${switchBtn ? 'icon-list' : 'icon-apps'}`" />
            {{ switchBtn ? '切换表格' : '切换图表' }}
        </div>
        <!-- 图表视图 START -->
        <div class="echart-box" v-show="switchBtn">
            <div class="echart-title">
                <s-label label="设备名称" :value="formData.currentName" value-style="font-size: 14px" />
                <s-label label="设备编号" :value="formData.currentCode" value-style="font-size: 14px" />
            </div>
            <div class="echart-overview">
                <no-data v-if="!tableData?.length" value="当前设备暂无数据" class="nodata-box" />
                <echart-item :option="lineOption" @initEchart="initEchart" v-show="tableData?.length" ref="lineRef" />
            </div>
        </div>
        <!-- 表格视图 START -->
        <base-table :columns="columns" :data="tableData" v-show="!switchBtn" />
    </ContentCard>
</template>
<script lang="ts">
import { defineComponent, ref, onMounted } from 'vue'
import { monitorService, historicDataPageFromEs } from '@/api/livableManage/monitorService'
import deviceDropDownSelect from '@/components/common/deviceDropDownSelect/index'
import EchartItem from '@/components/common/EchartItem/index'
import { ECOption } from '@/components/common/EchartItem/echart';
import { validateform } from '@/utils/validateform'
import Util from '@/utils';
export default defineComponent({
    components: {
        EchartItem,
        deviceDropDownSelect
    },
    props: {
        mockData: {
            type: Array,
            default: [] //mockData
        },
        dataIsMock: {
            type: Boolean,
            default: false
        },
        sliceIndex: {
            default: 0
        },
        modelId: {
            type: Number,
            default: 9
        },
        attrList: {
            type: Array,
            default: () => [{
                attr: 'temperature',
                name: '土壤温度',
                unit: '℃'
            },
            {
                attr: 'humidity',
                name: '土壤湿度',
                unit: '%'
            }]
        },
        // 是否是从es查询，已废弃，都走es
        isRealData: {
            type: Boolean,
            default: false
        },
        // 属性列表是否来自es，默认false，来自attrList
        isAttrListFromEs: {
            type: Boolean,
            default: false
        },

    },
    setup(props: any, { emit }: any) {
        // 切换视图按钮
        const switchBtn = ref<boolean>(true);
        // *********************
        // 折线图
        // *********************
        interface attrInfo {
            attr: string;
            name: string;
            unit: string
        }
        const attrList: attrInfo[] = props.attrList as attrInfo[]
        // 折线颜色静态数据
        const colorList = ['#F77234', '#165DFF', '#33D1C9', '#fac858', '#ee6666', '#73c0de', '#3ba272', '#fc8452', '#DAFF44', '#FF8EF4']
        // 折线图数据
        const lineOption = ref<ECOption>({
            grid: {
                top: 65,
                left: 5,
                right: 5,
                bottom: 80,
                containLabel: true
            },
            color: colorList,
            legend: {
                show: true,
                bottom: 5,
                icon: 'circle',
                itemHeight: 14,
                itemWidth: 14,
                itemStyle: {
                    borderRadius: 14
                },
                top: 8,
                data: [
                    // { icon: 'circle', name: '土壤温度(℃)', itemStyle: { color: '#fff', borderColor: '#F77234', borderWidth: 5 } },
                    // { icon: 'circle', name: '土壤湿度(%)', itemStyle: { color: '#fff', borderColor: '#165DFF', borderWidth: 5 } },
                ]
            },
            tooltip: {
                trigger: 'axis',
                confine: true,
                axisPointer: {
                }
            },
            dataZoom: [
                {
                    type: 'inside',
                    start: 0,
                    minValueSpan: 10,
                    end: 100
                },
                {
                    start: 0,
                    end: 10,
                    height: 14,
                    bottom: 55,
                    backgroundColor: '#F2F3F5',
                    dataBackground: {
                        lineStyle: {
                            width: 1,
                            color: 'rgba(4, 135, 255, 0.5)'
                        },
                        areaStyle: {
                            color: 'rgba(4, 135, 255, 0.3)'
                        }
                    },
                    handleStyle: {
                        opacity: 0
                    },
                    borderColor: 'rgba(4, 135, 255, 0)',
                    fillerColor: 'rgba(206, 224, 255, 0.5)'
                }
            ],
            xAxis: [
                {
                    type: 'category',
                    axisTick: {
                        show: false
                    },
                    axisLine: {
                        show: true
                    },
                    data: []
                }
            ],
            yAxis: [
                {
                    type: 'value',
                    axisTick: {
                        show: false
                    },
                    axisLine: {
                        show: false
                    }
                }
            ],
            series: [{
                smooth: true
            }]
        })
        // 定义表单数据
        const formData = ref(
            {
                deviceCode: '',
                currentName: '',
                currentCode: '',
                timeRange: [new Date(new Date().getTime() - 86400000), new Date()],
                fieldList: attrList.map((k) => k?.attr)
            }
        )
        // 定义表单验证
        const formRules = ref({
            deviceCode: [validateform.required],
            timeRange: [validateform.requiredArray, validateform.maxDayRang30]
        })
        // 定义表格列头
        const defaultColumns = ref<any[]>([])
        const columns = ref<any[]>([])
        const tableData = ref<historicDataPageFromEs[]>([])
        const formRef = ref<any>()
        // 自定义初始化表单数据
        const initFormData = ref<string>('');
        // 手动重置表单
        const handleReset = () => {
            if (props.dataIsMock) {

                emit('on-mock-search')
                return
            }
            formData.value = JSON.parse(initFormData.value)
            currentDevice.value = initCurrentDevice.value
            handleSubmit();
        }
        // 提交表单
        const handleSubmit = async () => {
            const valid = await formRef.value?.validate?.();
            // 记录当前查询的设备，以便显示
            formData.value.currentName = props.dataIsMock ? (currentDevice.value.deviceName || '') : currentDevice.value.label || currentDevice.value.sbmc || ''
            formData.value.currentCode = currentDevice.value.value || currentDevice.value.deviceCode || ''
            const params = Util.objClone(formData.value)
            // 处理时间数据
            params.queryTimeStart = Util.formatDate(params.timeRange[0], 'yyyy-MM-DD') + ' 00:00:00'
            params.queryTimeEnd = Util.formatDate(params.timeRange[1], 'yyyy-MM-DD') + ' 23:59:59'
            params.order = "asc"
            // 请求
            let res
            if (props.isRealData) {
                res = await monitorService.queryHistoricDataFromEs(params)
            } else {
                res = await monitorService.queryHistoricDataPageFromEs(params)

            }
            // const { data, success }: { success: boolean, data: recordsResponse<historicDataPageFromEs[]> } = res as unknown as HttpResponse<recordsResponse<historicDataPageFromEs[]>>
            const { data, success }: { success: boolean, data: any } = res as unknown as HttpResponse<recordsResponse<historicDataPageFromEs[]>>
            // 成功
            emit('on-mock-search', formData.value)

            if (success) {

                // 如果有物模型，设置attrList
                //  if (props.isRealData && data?.physicModel?.properties) {
                //     attrList.splice(0)

                //     let physicModel = {}
                //     data.physicModel.properties.forEach((item: { identifier: string }) => {
                //         if (formData.value.fieldList.includes(item.identifier)) {
                //             physicModel[item.identifier] = item
                //         }
                //     })
                //     if (data?.records?.[0]) {
                //         let attrListSub = Object.keys(data.records[0])
                //         attrListSub.filter((item: any) => item != 'createTime').forEach((item: any) => {
                //             let attr = physicModel[item]
                //             if (attr) {
                //                 attrList.push({
                //                     attr: item,
                //                     name: attr?.name,
                //                     unit: attr.dataType.specs.unit
                //                 })
                //             }
                //         })
                //     }
                // }
                // 如果属性列表从es取且有物模型，设置attrList
                if (props.isAttrListFromEs && data?.physicModel?.properties) {
                    attrList.splice(0)
                    data.physicModel.properties.filter((item: any) => item.identifier != 'current_time' && item.identifier != 'device_id').forEach((item: any) => {
                        attrList.push({
                            attr: item?.identifier,
                            name: item?.name,
                            unit: item?.dataType.specs.unit || '-'
                        })
                    })
                }
                // 设置表格数据
                const random = (val: number) => {
                    return Math.round(Math.random() * val);
                }
                const mockDataObj = props.sliceIndex ? () => ({
                    monitoringName: '基坑位移监测第一点', status: ['异常', '正常'][random(1)],
                    onceChange: random(25),
                    sumChange: random(50),
                    rateChange: random(10),
                }) : () => ({})
                const list = (data?.records || []).map((k: any) => ({ ...k, deviceCode: formData.value.currentCode, deviceName: formData.value.currentName || '--', ...mockDataObj() }))
                // 深拷贝
                const listCopy = JSON.parse(JSON.stringify(list))
                // 列表以时间逆序
                tableData.value =  listCopy.sort((a: historicDataPageFromEs, b: historicDataPageFromEs) => new Date(b.createTime as string).getTime() - new Date(a.createTime as string).getTime())
                // 默认表头
                const _defaultColumns: any = [
                    { title: "设备编号", key: "deviceCode", tooltip: true, fixed: 'left' },
                    { title: "设备名称", key: "deviceName", tooltip: true, fixed: 'left' },
                    { title: "采集时间", key: "createTime", tooltip: true, fixed: 'right', minWidth: 160 }
                ]
                //
                // 设置折线图数据
                const series: any[] = []
                lineOption.value.xAxis![0].data = list.map((item: any) => item.createTime)
                lineOption.value.legend!['data'] = attrList.slice(props.sliceIndex, attrList.length).map((attr, i) => {
                    return {
                        icon: 'circle',
                        name: `${attr.name}${attr.hasOwnProperty('unit') ? `(${attr.unit})` : ''}`,
                        itemStyle: {
                            color: '#fff',
                            borderColor: colorList[i],
                            borderWidth: 5
                        }
                    }
                })
                attrList.forEach((attr, i) => {
                    _defaultColumns.splice(i + 2, 0, { title: `${attr.name}${attr.hasOwnProperty('unit') ? `(${attr.unit})` : ''}`, key: attr.attr, tooltip: true })
                    series.push({
                        // showSymbol: false,
                        name: `${attr.name}${attr.hasOwnProperty('unit') ? `(${attr.unit})` : ''}`,
                        type: 'line',
                        // 图以时间顺序
                        data: tableData.value.map(item => item[attr.attr] || 0).reverse(),
                        smooth: true
                    })
                })
                defaultColumns.value = _defaultColumns // 存一份全量表头
                columns.value = _defaultColumns
                lineOption.value.series = series.slice(props.sliceIndex, series.length - props.sliceIndex + 1)

            }
        }
        // 获取初始化后的echart实例
        const initEchart = (echart: any) => {
            echart.off('legendselectchanged').on('legendselectchanged', ({ selected }: { selected: any }) => {
                // 修改legend选中状态 重新渲染表头
                columns.value = defaultColumns.value.filter(k => selected[k.title] == true || selected[k.title] == undefined)
            })
        }
        interface deviceInfo {
            label?: string,
            value?: string,
            deviceCode?: string,
            sbmc?: string,
            deviceName?: string
        }
        const initCurrentDevice = ref<deviceInfo>({}) // 存一份初始选中的设备
        const currentDevice = ref<deviceInfo>({})
        const deviceOnChange = (data: deviceInfo) => {
            if (!initCurrentDevice.value.deviceCode) {
                initCurrentDevice.value = data
            }
            currentDevice.value = data
        }
        const modelId = ref(props.modelId)
        const lineRef = ref()
        return {
            handleSubmit,
            columns,
            formData,
            lineOption,
            switchBtn,
            tableData,
            initEchart,
            deviceOnChange,
            formRef,
            formRules,
            initFormData,
            handleReset,
            modelId,
            lineRef,
        }
    }
})
</script>
<style lang="less" scoped>
@import './index.less';
</style>
