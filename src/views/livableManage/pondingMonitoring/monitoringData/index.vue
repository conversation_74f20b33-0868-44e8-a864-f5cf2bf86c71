<template>
    <BreadcrumbCustom>
        <BreadcrumbItem>湖渠监测</BreadcrumbItem>
        <BreadcrumbItem>监测数据</BreadcrumbItem>
    </BreadcrumbCustom>
    <monitoring-data :model-id="18" :attr-list="[
            {
                attr: 'water_level',
                name: '水位',
                unit: 'm'
            }
        ]"
/>
</template>
<script lang="ts">
import { defineComponent } from 'vue'
import MonitoringData from '../../components/monitoringData.vue'
export default defineComponent({
    components: {
        MonitoringData
    }
})
</script>
<style lang="less" scoped>
</style>
