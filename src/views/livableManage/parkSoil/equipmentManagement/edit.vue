<script lang="ts" setup>
import { getDetailInfo, editSoilDevice } from '@/api/livableManage/parkSoilService'
import { ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
const detailInfo = ref<any>({ objInfo: {}, extendInfo: {}, devicePropertyStatusList: [] })
const route = useRoute()
async function getData() {
  let res: any = await getDetailInfo(route.query.id)
  if (res.success) {
    detailInfo.value = res.data
  }
  console.log(detailInfo.value);
}
getData()
const router = useRouter()
async function confirm() {
  let res: any = await editSoilDevice(detailInfo.value)
  console.log(res);
  router.back()
}
function handlejwd(objInfo: any) {
  console.info("===============")
  if (!objInfo.gdx) return ''
  return objInfo.gdx + ' , ' + objInfo.gdy
}
</script>
<template>
  <BreadcrumbCustom>
    <BreadcrumbItem>园林土壤</BreadcrumbItem>
    <BreadcrumbItem :to="`/parkSoil/equipmentManagement?id=${$route.query.id}`">设备管理</BreadcrumbItem>
    <BreadcrumbItem :to="`/parkSoil/equipmentManagementDetail?id=${$route.query.id}`">设备详情</BreadcrumbItem>
    <BreadcrumbItem>设备编辑</BreadcrumbItem>
  </BreadcrumbCustom>
  <Form :model="detailInfo" label-position="top">
    <detailCard :is-back-btn="true" :src="require('@/assets/images/icon-基础信息.png')" @on-back="$router.back()" title="基本信息">
      <Row>
        <Col span="8">
        <FormItem label="设备编号">
          <Input v-model="detailInfo.extendInfo.bsm" disabled></Input>
        </FormItem>
        </Col>
      </Row>
      <Row>
        <Col span="8">
        <FormItem label="设备名称">
          <Input v-model="detailInfo.extendInfo.sbmc" disabled></Input>
        </FormItem>
        </Col>
        <Col span="8">
        <FormItem label="设备型号">
          <Input v-model="detailInfo.extendInfo.sbxh" disabled></Input>
        </FormItem>
        </Col>
        <Col span="8">
        <FormItem label="设备类型">
          <Input v-model="detailInfo.extendInfo.deviceSecondTypeName" disabled></Input>
        </FormItem>
        </Col>
        <Col span="8">
        <FormItem label="设备标识码">
          <Input v-model="detailInfo.extendInfo.bsm" disabled></Input>
        </FormItem>
        </Col>
        <Col span="8">
        <FormItem label="设备状态">
          <Input v-model="$enumeration.deviceStateList[detailInfo.extendInfo.sbzt]" disabled></Input>
        </FormItem>
        </Col>
        <Col span="8">
        <FormItem label="区域位置">
          <Input v-model="detailInfo.extendInfo.areaPath" disabled></Input>
        </FormItem>
        </Col>
      </Row>
    </detailCard>
    <!-- <detailCard :src="require('@/assets/images/icon-soil.png')" title="设备属性">
      <Row>
        <Col span="8">
        <FormItem label="在线状态">
          <Input v-model="$enumeration.onlineStatus[detailInfo.status || 0].title" disabled></Input>
        </FormItem>
        </Col>
        <Col span="8" v-for="(item, index) in detailInfo.devicePropertyStatusList" :key="index">
        <FormItem :label="item.propName">
          <Input v-model="item.value" disabled></Input>
        </FormItem>
        </Col>
        <Col span="8">
        <FormItem label="更新时间">
          <Input v-model="detailInfo.modifyTime" disabled></Input>
        </FormItem>
        </Col>
      </Row>
    </detailCard> -->
    <detailCard style="margin-bottom: 46px;">
      <Row>
        <Col :span="24">
        <h3 style="padding: 15px 0;">部件信息</h3>
        </Col>
      </Row>
      <Row>
        <Col span="8">
        <FormItem label="部件标识码">
          <Input v-model="detailInfo.objInfo.objId" disabled></Input>
        </FormItem>
        </Col>
        <Col span="8">
        <FormItem label="部件名称">
          <Input v-model="detailInfo.objInfo.objName" disabled></Input>
        </FormItem>
        </Col>
        <Col span="8">
        <FormItem label="部件类型">
          <Input v-model="detailInfo.objInfo.secondObjCategoryName" disabled></Input>
        </FormItem>
        </Col>
        <Col span="8">
        <FormItem label="主管部门">
          <Input v-model="detailInfo.objInfo.deptName" disabled></Input>
        </FormItem>
        </Col>
        <Col span="8">
        <FormItem label="权属单位">
          <Input v-model="detailInfo.objInfo.ownerEnterpriseName" disabled></Input>
        </FormItem>
        </Col>
        <Col span="8">
        <FormItem label="养护单位">
          <Input v-model="detailInfo.objInfo.opEnterpricseName" disabled></Input>
        </FormItem>
        </Col>
        <Col span="8">
        <FormItem label="部件状态">
          <Input v-model="$enumeration.objState[detailInfo.objInfo.objState]" disabled></Input>
        </FormItem>
        </Col>
        <Col span="8">
        <FormItem label="联系人">
          <Input v-model="detailInfo.objInfo.contactPerson" disabled></Input>
        </FormItem>
        </Col>

        <Col span="8">
        <FormItem label="联系电话">
          <Input v-model="detailInfo.objInfo.contactPhone" disabled></Input>
        </FormItem>
        </Col>
        <Col span="8">
        <FormItem label="初始时间">
          <Input v-model="detailInfo.objInfo.initDate" disabled></Input>
        </FormItem>
        </Col>
        <Col span="8">
        <FormItem label="变更时间">
          <Input v-model="detailInfo.objInfo.modifyDate" disabled></Input>
        </FormItem>
        </Col>
        <Col span="8">
        <FormItem label="经纬度">
          <Input :model-value="handlejwd(detailInfo.objInfo)" disabled></Input>
        </FormItem>
        </Col>
        <Col span="8">
        <FormItem label="备注">
          <Input v-model="detailInfo.objInfo.remark" :rows="4" type="textarea" disabled></Input>
        </FormItem>
        </Col>
      </Row>
    </detailCard>
  </Form>
  <div class="btn">
    <Button @click="$router.back()">取消</Button>
    <Button type="primary" @click="confirm">提交</Button>
  </div>
</template>

<style lang="less" scoped>
.ivu-form-item {
  width: 90%;
}

.btn {
  position: fixed;
  bottom: 0;
  width: 100%;
  text-align: left;
  background-color: #ffffff;
  text-align: right;
  padding: 8px 40px ;
  left: 0;
  box-shadow: 0px -3px 12px rgba(45, 75, 103, 0.1);
  z-index: 5;

  .ivu-btn {
    min-width: 68px;
    // margin-left: 8px;
    // margin-right: 8px;
  }
  .ivu-btn+.ivu-btn{
        margin-left: 8px;
    }
}
</style>
