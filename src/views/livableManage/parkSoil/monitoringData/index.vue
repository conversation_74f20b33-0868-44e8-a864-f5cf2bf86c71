<template>
    <BreadcrumbCustom>
        <BreadcrumbItem>园林土壤</BreadcrumbItem>
        <BreadcrumbItem>监测数据</BreadcrumbItem>
    </BreadcrumbCustom>
    <monitoring-data :model-id="9" :attr-list="[
        {
            attr: 'temperature',
            name: '土壤温度',
            unit: '℃'
        },
        {
            attr: 'humidity',
            name: '土壤湿度',
            unit: '%'
        }
    ]"
/>
</template>
<script lang="ts">
import { defineComponent } from 'vue'
import MonitoringData from '../../components/monitoringData.vue'
export default defineComponent({
    components: {
        MonitoringData
    },
    setup() {
        return {

        }
    }
})
</script>
<style lang="less" scoped></style>
