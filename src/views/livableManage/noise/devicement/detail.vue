<script lang="ts" setup>
import deviceStatusList from '@/components/common/deviceStatusList'
import { getDetailInfo } from '@/api/livableManage/noiseService'
import SideDetail from '@/components/global/SideDetail'
import { handleCoordinate } from '@/utils/tool'
import { reactive, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router';
import { useStore } from 'vuex'
const $store = useStore()

const tabList = ref([
  {
    name: '部件信息',
    key: 'unitInfo',
    icon: '1'
  },
  {
    name: '告警信息',
    key: 'alarmInfo',
    icon: '1'
  }
])
const tableList = ref<any>([
  { title: '设备名称', key: 'sbmc', maxWidth: 100, tooltip: true },
  { title: '告警类型', key: 'alarmTypeName', maxWidth: 150, tooltip: true },
  { title: '告警等级', slot: 'level', maxWidth: 100, tooltip: true },
  { title: '告警详情', key: 'content', maxWidth: 130, tooltip: true },
  { title: '是否推送', slot: 'pushStatus' },
  { title: '区域位置', key: 'areaPath', tooltip: true, maxWidth: 170 },
  { title: '告警时间', key: 'alarmTime', tooltip: true, width: 160 },
  { title: '操作', slot: 'operation', maxWidth: 80, align: 'center' }
])
const route = useRoute()
let detailInfo = ref<any>({
  objInfo: {}, extendInfo: {}, devicePropertyStatusList: [], monitorPoint: {}
})
async function getData() {
  let res: any = await getDetailInfo(route.query.id)
  if (res.success) {
    detailInfo.value = res.data
    let attrList = res.data.devicePropertyStatusList || []
    let physicModel = {}
    if (res.data.physicModel) {
      res.data.physicModel.forEach((item: { identifier: string | number }) => {
        physicModel[item.identifier] = item
      })
    }
    attrList.forEach((item: any) => {
      let attr = physicModel[item.prop]
      if (attr) {
        item.dataType = attr.dataType
        if (!item.propName) {
          item.propName = attr.name
        }
      }
    })
    detailInfo.value.devicePropertyStatusList = attrList
  }
}
getData()
let alarmData = reactive<Object>({})
let isShowSide = ref<boolean>(false)
function goDetail(row: string) {
  alarmData = JSON.parse(row)
  isShowSide.value = true

}
const listCom = ref()
function getList(index: number) {
  index && listCom.value.search({ deviceCode: detailInfo.value.deviceCode })
}
const router = useRouter()
function handleEdit() {
  router.push({
    name: 'noiseEnvironment:devicementEdit',
    query: {
      id: route.query.id
    }
  })
}
function getAlarmValue(type: any) {
  const { noise_alarm_type } = $store.getters.dictionary
  console.info(noise_alarm_type)
  let selectOne = noise_alarm_type.filter((k: any) => k.value == type)[0] || {};
  if (selectOne) {
    return selectOne.name;
  }
  return ''
}
// openMap()
</script>
<template>
  <BreadcrumbCustom>
    <BreadcrumbItem>噪声环境</BreadcrumbItem>
    <BreadcrumbItem to="/meteorological/equipmentManagement">设备管理</BreadcrumbItem>
    <BreadcrumbItem>设备详情</BreadcrumbItem>
  </BreadcrumbCustom>

  <detailCard @on-edit="handleEdit" :src="require('@/assets/images/icon-基础信息.png')"
    :is-edit-btn="$Util.checkAuth('environmentMonitoring:waterQualityTest:deviceManage:edit')" :is-back-btn="true"
    @on-back="$router.back()" title="基础信息">
    <Row>
      <Col span="8">
      <s-label class="code" label="设备编号" style="font-size: 16px;font-weight: 600;" :value="detailInfo.deviceCode" />
      </Col>
      <Col span="8">
      <s-label label="区域位置" style="font-weight: 600;" :value="detailInfo.extendInfo.areaPath" />
      </Col>
    </Row>
    <Row>
      <Col span="8">
      <s-label label="设备名称" :value="detailInfo.extendInfo.sbmc" />
      </Col>
      <Col span="8">
      <s-label label="设备型号" :value="detailInfo.extendInfo.sbxh" />
      </Col>
      <Col span="8">
      <s-label label="设备类型" :value="detailInfo.extendInfo.deviceSecondTypeName" />
      </Col>
    </Row>
    <Row>
      <Col span="8">
      <s-label label="设备标识码" :value="detailInfo.extendInfo.bsm" />
      </Col>
      <Col span="8">
      <s-label label="设备状态" :value="$enumeration.deviceStateList[detailInfo.extendInfo.sbzt] || ''" />
      </Col>
      <Col span="8">
      <s-label label="监测范围" :value="detailInfo.scopeMonitoring" />
      </Col>
      <Col span="8">
      <s-label label="精度" :value="detailInfo.precision" />
      </Col>
    </Row>
  </detailCard>
  <detailCard title="设备属性" :src="require('@/assets/images/icon-noise.png')">
    <!-- <Row>
        <Col span="8">
        <s-label label="在线状态">
            <template #value>
                <s-tag v-if="detailInfo.status == 1">在线</s-tag>
                <s-tag v-else-if="detailInfo.status == 0" background="#F2F3F5" color="#4E627E">离线</s-tag>
                <span v-else>--</span>
            </template>
        </s-label>
      </Col>
      <Col span="8" v-for="(item,index) in detailInfo.devicePropertyStatusList" :key="index">
      <s-label :label="item.propName" :value="item.value" />
      </Col>
      <Col span="8">
      <s-label label="更新时间" :value="detailInfo.modifyTime" />
      </Col>
    </Row> -->
    <deviceStatusList :list="detailInfo.devicePropertyStatusList" :status="detailInfo.status"
      :attr-time="detailInfo.lastPushTime" :gutter="0" />
  </detailCard>
  <detailCard>
    <s-tab :tab-list="tabList" @handleChange="getList" justify="start">
      <template #unitInfo>
        <Row>
          <Col span="8">
          <s-label label="点位标识码" :value="detailInfo.monitorPoint?.bsm || ''" />
          </Col>
          <Col span="8">
          <s-label label="点位名称" :value="detailInfo.monitorPoint?.dwmc || ''" />
          </Col>
          <Col span="8">
          <!-- <s-label label="点位类型" :value="$store.getters.dictionary.obj_category[detailInfo.monitorPoint?.dwlx || '']" /> -->
          <s-label label="点位类型" :value="detailInfo.monitorPoint?.dwlx || ''" />
          </Col>
        </Row>
        <Row>
          <Col span="8">
          <s-label label="主管部门" :value="detailInfo.objInfo?.deptName" />
          </Col>
          <Col span="8">
          <s-label label="权属单位" :value="detailInfo.objInfo?.ownerEnterpriseName" />
          </Col>
          <Col span="8">
          <s-label label="养护单位" :value="detailInfo.objInfo?.opEnterpricseName" />
          </Col>
        </Row>
        <Row>
          <Col span="8">
          <s-label label="部件状态" :value="$enumeration.objState[detailInfo.objInfo?.objState]" />
          </Col>
          <Col span="8">
          <s-label label="联系人" :value="detailInfo.objInfo?.contactPerson" />
          </Col>
          <Col span="8">
          <s-label label="联系电话" :value="detailInfo.objInfo?.contactPhone" />
          </Col>
        </Row>
        <Row>
          <Col span="8">
          <s-label label="初始时间" :value="detailInfo.objInfo?.initDate" />
          </Col>
          <Col span="8">
          <s-label label="变更时间" :value="detailInfo.objInfo?.modifyDate" />
          </Col>
          <Col span="8">
          <s-label label="经纬度">
            <template #value>
              <jwd-map
                :obj-info="{ objX: detailInfo.monitorPoint?.gdx || '', objY: detailInfo.monitorPoint?.gdy || '', gdx: detailInfo.monitorPoint?.gdx || '', gdy: detailInfo.monitorPoint?.gdy || '' }"></jwd-map>
              <!-- <Icon type="ios-pin" v-if="detailInfo.objInfo"
                @click="goMap(detailInfo.objInfo?.objX || '', detailInfo.objInfo?.objY || '')" color="#165DFF"
                size="16" />
              {{ handleCoordinate(detailInfo.objInfo && detailInfo.objInfo || '--') }} -->
            </template>
          </s-label>
          </Col>
        </Row>
        <Row>
          <Col span="8">
          <s-label label="备注" :value="detailInfo.objInfo?.remark" />
          </Col>
        </Row>
      </template>
      <template #alarmInfo>
        <baseTable :columns="tableList" ref="listCom" url="/noiseDeviceAlarm/list">
          <!-- <template #alarmType="{row}">
            <div>
              {{ $store.getters.dictionary.noise_alarm_type[row.alarmType]}}
              <dict-label code="noise_alarm_type" :value="row.alarmType"></dict-label>
            </div>
          </template> -->
          <template #level="{ row }">
            <div>
              {{ $enumeration.alarmGrade[row.level] }}
            </div>
          </template>
          <template #pushStatus="{ row }">
            <div>
              {{ $enumeration.isPush[row.pushStatus] }}
            </div>
          </template>
          <template #operation="{ row }">
            <LinkBtn size="small" @click="goDetail(JSON.stringify(row))">查看</LinkBtn>
          </template>
        </baseTable>
      </template>
    </s-tab>
  </detailCard>
  <component :is="componentId" :makers="jwdList" @close-modal="closeModal" />
  <SideDetail @on-cancel="isShowSide = false" :data="alarmData" :model-id="12" :show="isShowSide" title="告警详情"></SideDetail>
</template>

<style lang="less" scoped></style>
