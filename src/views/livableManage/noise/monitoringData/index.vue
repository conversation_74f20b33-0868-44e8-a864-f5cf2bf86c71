<template>
    <BreadcrumbCustom>
        <BreadcrumbItem>噪声环境</BreadcrumbItem>
        <BreadcrumbItem>监测数据</BreadcrumbItem>
    </BreadcrumbCustom>
    <monitoring-data :model-id="12" :attr-list="[
        {
            attr: 'noise',
            name: '噪声',
            unit: 'db'
        }
    ]"
/>
</template>
<script lang="ts">
import { defineComponent } from 'vue'
import MonitoringData from '../../components/monitoringData.vue'
export default defineComponent({
    components: {
        MonitoringData
    }
})
</script>
<style lang="less" scoped>
</style>
