<template>
    <div class="state-list">
        <imgCard :img="require('../images/manhole_total.png')" label="车辆总数" :value="dataRef.carNum" unit="辆"
            :horizontal="true" img-width="48" font-size="22" style="margin-bottom: 8px;" />
        <imgCard :img="require('../images/manhole_normal.png')" label="今日告警车辆数" :value="dataRef.todayCarNum" unit="个"
            :horizontal="true" img-width="48" font-size="22" style="margin-bottom: 8px;" />
        <imgCard :img="require('../images/manhole_alarm.png')" label="告警总数" :value="dataRef.sum" unit="个"
            :horizontal="true" img-width="48" font-size="22" />
    </div>
</template>

<script>
import { toRef, watch, getCurrentInstance } from 'vue'
export default {
    name: 'StatisticsCard',
    components: {
    },
    props: {
        data: {
            default: {}
        }
    },
    setup(props) {
        const dataRef = toRef(props, 'data')
        return {
            dataRef
        }
    },
}
</script>

<style lang="less" scoped>
.state-list {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;
    height: 100%;

    .img-card {
        width: 100%;
        background: #fff;
        flex: inherit;
        flex-basis: 64px;
    }
}
</style>
