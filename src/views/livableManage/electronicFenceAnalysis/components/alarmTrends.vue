<template>
    <EchartItem :option="option" style="height: 336px;margin-top: 8px;"
        v-if="dataList && Object.keys(dataList).length > 0" />
    <no-data v-else style="height: 336px" />
</template>

<script>
import moment from 'moment'
import { tooltipExtraCssText, EchartsTooltip, hexToRgb } from '@/utils/tool'
import EchartItem from '@/components/common/EchartItem'
import { ref, watch, getCurrentInstance, computed } from 'vue'
export default {
    name: 'AlarmTrends',
    components: {
        EchartItem
    },
    props: {
        searchObj: {
            default: {
                queryStartTime: '',
                queryEndTime: ''
            }
        }
    },
    setup(props) {
        const that = getCurrentInstance()?.appContext.config.globalProperties
        const searchObjRef = ref(props.searchObj)
        const option = computed(
            () => {
                const xList = dataList.value.map(k => k.time.slice(5, 10))
                // 告警类型
                let uniqueLegend = dataList.value[0]?.alarms?.map(k => k.alarmName) || []
                // 二维数组
                const resultList = uniqueLegend.map(k => {
                    return dataList.value.map(item => item.alarms.find(m => m.alarmName == k)?.num || 0)
                })
                console.log(resultList)
                const unitOfMeasurement = ['次', '次', '次', '次', '次', '次', '次']
                const colorList = ['#FF708B', '#FAAC7B', '#FFCF8B', '#77EAA5', '#33D1C9', '#78C4FF']
                const option = {
                    tooltip: {
                        trigger: 'axis',
                        axisPointer: {
                            type: 'shadow'
                        },
                        // borderWidth: 0,
                        // textStyle: {
                        //     color: '#4E5969',
                        //     fontSize: 12,
                        // },
                        extraCssText: tooltipExtraCssText,
                        formatter: (oldArg) => {
                            const width = 240;
                            // 没有遮罩数组了
                            const arg = oldArg
                            // const arg = oldArg?.slice(0, -1)
                            const domArr = arg.map((item, index) => {
                                if (!index) {
                                    return `
                                    <div style="background-color: rgb(255,255,255,0.6);">
                                    <div style="padding:8px 0 8px 14px;">${arg[index].name}</div>
                                    <div style="
                                    display:flex;
                                    width: ${typeof width === 'number' ? width + 'px' : width};
                                    flex-direction: column;
                                    row-gap: 4px;
                                    ">
                                    <div style="
                                    display:flex;
                                    align-items: center;
                                    height: 32px;
                                    justify-content: space-between;
                                    background: rgba(255, 255, 255, 0.9);
                                    box-shadow: 6px 0px 20px rgba(34, 87, 188, 0.1);
                                    border-radius: 4px;
                                    ">
                                    <span style="margin:0 8px;height:11px;width:11px;border-radius:50%;background:${hexToRgb(item.color)};"></span>
                                    <span style="
                                    color: #4E5969;
                                    font-size: 12px;
                                    font-family: 'PingFang SC';
                                    ">${arg[index].seriesName}</span>
                                    <span style="
                                    color: #1D2129;
                                    font-size: 13px;
                                    font-family: 'PingFang SC';
                                    font-weight: 720;
                                    margin-left: auto;
                                    margin-right: 8px;
                                    ">${arg[index].value}${unitOfMeasurement[index]}</span>
                                    </div></div></div>`
                                } else {
                                    return ` <div style="
                                    background-color: rgb(255,255,255,0.6);
                                ">
                                    <div style="
                                    display:flex;
                                    width: ${typeof width === 'number' ? width + 'px' : width};
                                    flex-direction: column;
                                    row-gap: 4px;
                                    ">
                                    <div style="
                                    display:flex;
                                    align-items: center;
                                    height: 32px;
                                    justify-content: space-between;
                                    background: rgba(255, 255, 255, 0.9);
                                    box-shadow: 6px 0px 20px rgba(34, 87, 188, 0.1);
                                    border-radius: 4px;
                                    ">
                                    <span style="margin:0 8px;height:11px;width:11px;border-radius:50%;background:${hexToRgb(item.color)};"></span>
                                    <span style="
                                    color: #4E5969;
                                    font-size: 12px;
                                    font-family: 'PingFang SC';
                                    ">${arg[index].seriesName}</span>
                                    <span style="
                                    color: #1D2129;
                                    font-size: 13px;
                                    font-family: 'PingFang SC';
                                    font-weight: 720;
                                    margin-left: auto;
                                    margin-right: 8px;
                                    ">${arg[index].value}${unitOfMeasurement[index]}</span>
                                    </div></div></div>`
                                }
                            })
                            let dom = ''
                            domArr.forEach((item) => {
                                dom += item
                            })
                            return dom
                        }
                    },
                    grid: {
                        left: 10,
                        right: 10,
                        top: 30,
                        bottom: 0,
                        containLabel: true,
                    },
                    legend: {
                        data: uniqueLegend,
                        itemWidth: 10,
                        itemHeight: 10
                    },
                    xAxis: [
                        {
                            type: 'category',
                            data: xList,
                            axisLine: {
                                show: true, // 决定是否显示刻度线
                                lineStyle: {
                                    color: '#C2CAD8'
                                }
                            },
                            splitLine: {
                                show: false, // 去掉网格辅助线
                            },
                            axisTick: {
                                show: true, // 决定刻度的显示
                                alignWithLabel: true, // 将类目轴刻度与图像对其
                                lineStyle: {
                                    color: '#C2CAD8'
                                }
                            },
                            axisLabel: {
                                // interval: 0, // 代表显示所有x轴标签显示
                                color: '#798799',
                            },
                            nameTextStyle: {
                                color: '#798799'
                            },
                        },
                        // {
                        //     show: false,
                        //     type: 'category',
                        //     data: xList,
                        // }
                    ],
                    yAxis: [
                        {
                            type: 'value',
                            name: '单位(次)',
                            axisLabel: {
                                formatter: '{value}'
                            },
                            splitLine: {
                                show: true,
                                color: '#E5E6EB',
                                lineStyle: {
                                    type: 'dashed',
                                }
                            }
                        }
                    ],
                    series: [],
                }
                // 填充数据
                uniqueLegend.forEach((item, index) => {
                    option.series.unshift(
                        {
                            coordinateSystem: 'cartesian2d',
                            name: uniqueLegend[index],
                            type: 'bar',
                            stack: 'same',
                            smooth: true,
                            symbol: 'none',
                            barWidth: 12,
                            itemStyle: {
                                color: colorList[index], // 折线颜色
                            },
                            data: resultList[index],
                            xAxisIndex: 0
                        })
                })
                return option
            })
        const dataList = ref({})
        const getData = () => {
            let param = {}
            Object.assign(param, searchObjRef.value)
            that.$request('/electronicFenceOverview/trends', param, 'post').then(res => {
                if (res.success) {
                    dataList.value = res.data
                }
            })
        }
        watch(searchObjRef.value, () => {
            getData()
        })
        // 初始化调用
        getData()
        return {
            dataList,
            searchObjRef,
            getData,
            option
        }
    },
}
</script>

<style lang="less" scoped>
.no-data {
    padding-top: 100px;
}
</style>
