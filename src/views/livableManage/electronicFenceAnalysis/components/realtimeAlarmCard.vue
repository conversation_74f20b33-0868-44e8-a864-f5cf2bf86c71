<template>
    <ContentCard title="告警总数" style="height: 208px;">
        <div class="state-list" style="padding: 8px;">
            <template v-for="(item, index) in dataRef" :key="index">
                <imgCard :img="require('@/assets/images/icon-告警铃铛.png')" :label="item.alarmName" :value="item.num" unit="个"
                    :horizontal="true" img-width="34" font-size="22" label-font-size="14" @click="changeRoute(item.name)" />
            </template>
        </div>
    </ContentCard>
</template>

<script>
import { toRef } from 'vue'

export default {
    name: 'RealtimeAlarmCard',
    components: {
    },
    props: {

        data: {
            default: []
        }
    },
    emits: ['changeRoute'],
    setup(props, context) {
        const dataRef = toRef(props, 'data')
        const changeRoute = (status) => {
            // 匹配转换

        }
        return {
            dataRef,
            changeRoute
        }
    },
}
</script>

<style lang="less" scoped>
.state-list {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    grid-template-rows: repeat(2, 1fr);
    grid-gap: 16px 8px;

    .img-card {
        height: 56px;
        background-color: #F6FAFF;

        &:hover {
            background: #E8F3FF;
        }

        &:active {
            background: #E8F3FF;
            color: @primary-color;
        }
    }
}
</style>
