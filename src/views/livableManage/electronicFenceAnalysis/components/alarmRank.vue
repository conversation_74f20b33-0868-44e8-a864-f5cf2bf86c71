<template>
    <swiper direction="vertical" :modules="modules" :slides-per-view="6" :autoplay="autoplay" @swiper="onSwiper"
        @slideChange="onSlideChange" style="height: 332px; margin-top: 9px;" v-if="alarmList.length > 0"
        @mouseenter.native="swiperStop" @mouseleave.native="swiperStart">
        <swiper-slide v-for="(item, index) in alarmList" :key="index">
            <div class="alarm-item">
                <div class="tit-box">
                    <div class="num" :class="'n' + (index + 1)">{{ index + 1 }}</div>
                    <div class="name">
                        <TooltipAutoShow :content="item.eventName" />
                    </div>
                    <div class="value">{{ item.alarmCount }}</div>
                </div>
                <Progress :percent="item.percent" stroke-color="#4086FF" :stroke-width="4" hide-info />
            </div>
        </swiper-slide>
    </swiper>
    <no-data v-else style="height: 332px" />
</template>

<script>
import { Swiper } from 'swiper/vue/swiper';
import { SwiperSlide } from 'swiper/vue/swiper-slide';
import { Autoplay } from 'swiper';
import 'swiper/swiper.css';
import { ref, watch, getCurrentInstance } from 'vue'
export default {
    name: 'AlarmRank',
    components: {
        Swiper,
        SwiperSlide,
    },
    props: {
        searchObj: {
            default: {
                szjd: '',
                szsq: '',
                szdywg: '',
                startTime: '',
                endTime: ''
            }
        }
    },
    setup(props) {
        const that = getCurrentInstance()?.appContext.config.globalProperties
        const searchObjRef = ref(props.searchObj)
        const autoplay = ref({
            delay: 5000
        })
        const swiperMy = ref(null)
        const onSwiper = (swiper) => {
            swiperMy.value = swiper
        }
        const onSlideChange = () => {
        }
        const swiperStop = () => {
            if (swiperMy.value) {
                swiperMy.value.autoplay.stop()
            }
        }
        const swiperStart = () => {
            if (swiperMy.value) {
                swiperMy.value.autoplay.start()
            }
        }
        const alarmList = ref([])
        const getData = () => {
            that.$request('/electronicFenceOverview/rank', searchObjRef.value, 'post').then(res => {
                if (res.success) {
                    const countMax = eval(res.data.map((item) => item.num || 0).join('+'))

                    alarmList.value = res.data?.map(item => {
                        return {
                            eventName: item.carNo,
                            alarmCount: item.num,
                            percent: (item.num / countMax * 100).toFixed()

                        }
                    }) || []

                }
            })
        }
        watch(searchObjRef.value, (newV, oldV) => {
            getData()
        })
        // 初始化调用
        getData()
        return {
            searchObjRef,
            getData,
            swiperMy,
            autoplay,
            onSwiper,
            swiperStop,
            swiperStart,
            onSlideChange,
            modules: [Autoplay],
            alarmList,
        }
    },
}
</script>

<style lang="less" scoped>
.alarm-item {
    padding: 8px 0;

    .tit-box {
        display: flex;
        align-items: center;
        margin-bottom: 8px;
        font-size: 12px;

        .num {
            min-width: 16px;
            height: 16px;
            background: #EBEDF0;
            border-radius: 2px;
            color: @text-3-1;
            text-align: center;
            vertical-align: middle;
            line-height: 16px;
            margin-right: 8px;

            &.n1 {
                background: #FDA979;
                box-shadow: inset 0px 2px 3px rgba(255, 255, 255, 0.5);
                color: #fff;
            }

            &.n2 {
                background: #FFC876;
                box-shadow: inset 0px 2px 3px rgba(255, 255, 255, 0.5);
                color: #fff;
            }

            &.n3 {
                background: #FDE079;
                box-shadow: inset 0px 2px 3px rgba(255, 255, 255, 0.5);
                color: #fff;
            }
        }

        .name {
            overflow: hidden;
            flex: 1;
            color: #4E5969;
            line-height: 20px;

            .ivu-tooltip {
                height: 20px;
                display: block;

                /deep/.ivu-tooltip-rel {
                    line-height: 20px;
                }
            }
        }

        .value {
            color: #4E5969;
            font-weight: 500;
            line-height: 20px;
        }
    }

    .ivu-progress,
    /deep/.ivu-progress-outer {
        display: block;
    }

    /deep/.ivu-progress-inner {
        display: block;
        background: rgba(241, 245, 255, 1);
    }
}

.no-data {
    padding-top: 100px;
}
</style>
