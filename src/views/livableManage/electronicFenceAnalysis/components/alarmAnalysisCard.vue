<template>
    <ContentCard title="告警次数趋势">
        <div class="top-right">
            <dateSelect @on-change="changeDate" :hide-date="true" placement="bottom-end" :curIndex="2"/>
        </div>
        <Row style="margin-bottom: 24px;" :gutter="8">
            <Col style="width: 67%; margin-top: 8px;">
            <div class="label">
                <div class="block"></div>
                <div class="font">告警趋势</div>
            </div>
            <AlarmTrends :search-obj="searchObj" />
            </Col>
            <Col style="width: 33%;margin-top: 8px;">
            <div class="label">
                <div class="block"></div>
                <div class="font">累计告警排行</div>
            </div>
            <AlarmRank :search-obj="searchObj" />
            </Col>
        </Row>
        <Row>
            <Col class="buttom" style="width: 100%;">
            <s-tab :tab-list="[{ name: '明细表', key: 'report' }]">
                <template #report>
                    <AlarmTable :table-search-obj="tableSearchObj" @on-export="handleExport" />
                </template>
            </s-tab>
            </Col>
        </Row>
    </ContentCard>
    <EModal :option="{ title: '批量导出', footerHide: true, width: 408 }" :is-show="showProgressModal"
        @on-cancel="handleCancel">
        <div class="download">
            <Circle :percent="progress" :stroke-color="(progress >= 100 && '#5cb85c') || '#2db7f5'" :size="80">
                <Icon v-if="progress == 101" type="ios-checkmark" size="60" style="color: #5cb85c"></Icon>
                <span v-if="progress <= 100" class="progress-num">{{ progress }}%</span>
            </Circle>
            <div class="tips">数据导出中，请勿关闭窗口</div>
        </div>
    </EModal>
</template>

<script>
import { ref, toRef, watch, getCurrentInstance } from 'vue'
import { queryBatchExportPatrolReportUrl } from '@/api/safeManage/parkPatrolService'
import { exportService } from '@/api/exportService'
import dateSelect from '@/components/common/dateSelect/index'
import AlarmTrends from './alarmTrends.vue'
import AlarmRank from './alarmRank.vue'
import AlarmTable from './alarmTable.vue'
import EModal from '@/components/common/modal/index.vue'

export default {
    name: 'AlarmAnalysisCard',
    components: {
        dateSelect,
        AlarmTrends,
        AlarmRank,
        AlarmTable,
        EModal
    },
    props: {
    },
    setup(props) {
        const that = getCurrentInstance()?.appContext.config.globalProperties
        const searchObj = ref({
            queryStartTime: '',
            queryEndTime: ''
        })
        const tableSearchObj = ref({
            queryStartTime: '',
            queryEndTime: ''
        })
        const changeDate = (arr) => {
            searchObj.value.queryStartTime = arr[0]
            searchObj.value.queryEndTime = arr[1]

            tableSearchObj.value.queryStartTime = arr[0]
            tableSearchObj.value.queryEndTime = arr[1]
        }

        // 导出
        const progress = ref(0)
        const showProgressModal = ref(false)
        let timer // 递归计时器,用户清除递归查询
        function exportPatrolReport(key) {
            timer = setTimeout(() => {
                queryBatchExportPatrolReportUrl(key).then((res) => {
                    if (res.data.success) {
                        progress.value = 100;
                        const timer1 = setTimeout(() => {
                            progress.value = 101;
                            clearTimeout(timer1);
                        }, 800);
                        const timer2 = setTimeout(() => {
                            showProgressModal.value = false;
                            window.open(res.data.data);
                            progress.value = 0;
                            clearTimeout(timer2);
                        }, 1500);
                    } else {
                        showProgressModal.value = true;
                        progress.value = Math.ceil((res.data.done / res.data.total) * 100);
                        clearTimeout(timer)
                        exportPatrolReport(key);
                    }
                });
            }, 2000);
        }

        // 批量导出获取key
        function handleExport(param) {
            // 导出（简单版）
            exportService('/electronicFenceOverview/export', param)
            // 导出（复杂版）
            // that.$request('/manholeAlarm/export', param, 'post').then((res) => {
            //     if (res.success) {
            //         showProgressModal.value = true;
            //         exportPatrolReport(res.data);
            //     }
            // })
        }


        const handleCancel = () => {
            clearTimeout(timer);
        }

        return {
            changeDate,
            showProgressModal,
            handleCancel,
            handleExport,
            searchObj,
            tableSearchObj
        }
    }
}
</script>

<style lang="less" scoped>
.top-right {
    position: absolute;
    top: 15px;
    right: 11px;
    z-index: 9;
}

.label {
    display: flex;
    align-items: center;

    .block {
        background-color: #1890FF;
        width: 2px;
        height: 12px;
        margin-right: 8px;
        border-radius: 5px;
    }

    .font {
        font-weight: 500;
    }
}

.buttom {
    position: relative;

    /deep/ .tab-title {
        justify-content: flex-start;
        column-gap: 12px;
    }
}

.download {
    text-align: center;
    padding: 48px 0;

    .tips {
        color: #798799;
        font-size: 14px;
    }
}
</style>
