<template>
    <div class="container">
        <div class="top">
            <Button type="primary" @click="handleExport">
                <i style="font-size: 11px" class="iconfont icon-Vector"></i>
                导出
            </Button>
        </div>
        <div class="middle" style="width: 300px;">
            <!-- <Select v-model="bsm" style="width:300px;">
                <Option v-for="(item, index) in deviceList" :value="item.bsm" :key="item.bsm">
                    {{ item.bsm + (item.sbmc ? `(${item.sbmc})` : '') }}
                </Option>
            </Select> -->
            <select-alarm-type v-model="bsm" :param="{ modelId: 15, type: 2 }" />
        </div>
        <base-table ref="tableRef" url="/electronicFenceOverview/detail" :columns="columns">
        </base-table>
    </div>
</template>

<script>
import { ref, toRef, watch, getCurrentInstance, onMounted } from 'vue'
import SelectAlarmType from '@/components/common/selectAlarmType/index.vue';
export default {
    name: 'AlarmTable',
    components: {
        SelectAlarmType
    },
    props: {
        tableSearchObj: {
            default: {
                startTime: '',
                endTime: ''
            }
        },
    },
    emits: ['on-export'],
    setup(props, context) {
        const that = getCurrentInstance()?.appContext.config.globalProperties
        const tableRef = ref()
        const tableSearchObjRef = ref(props.tableSearchObj)
        const bsm = ref('')

        const columns = [
            { title: '告警时间', key: 'alarmTime', tooltip: true },
            { title: '车牌号', key: 'carNo', tooltip: true },
            { title: '告警设备', key: 'sbmc', tooltip: true,minWidth: 80 },
            { title: '告警类型', key: 'alarmTypeName', tooltip: true },
            { title: '告警详情', key: 'content', tooltip: true }
        ]

        watch(bsm, (newV) => {
            getData()
        })
        watch(tableSearchObjRef.value, (newV, oldV) => {
            getData()
        })
        const getData = () => {
            let param = getParam()
            tableRef.value.search(param)
        }

        const handleExport = () => {
            let param = getParam()
            context.emit('on-export', param)
        }

        const getParam = () => {
            let param = {
                queryStartTime: tableSearchObjRef.value.queryStartTime,
                queryEndTime: tableSearchObjRef.value.queryEndTime,
                modelIds: ['15'],
                alarmType: bsm.value
            }
            return param
        }

        // 初始化调用
        onMounted(() => {
            getData()
        })
        return {
            tableRef,
            tableSearchObjRef,
            getData,
            handleExport,
            columns,
            bsm,
            props
        }
    },
}
</script>

<style lang="less" scoped>
.container {
    width: 100%;

    .top {
        position: absolute;
        right: 0px;
        top: 0px;
        height: 40px;
        font-size: 12px;
    }

    .middle {
        margin-top: 16px;
        margin-bottom: 8px;
    }
}
</style>
