<template>
    <div class="home-container">
        <BreadcrumbCustom>
            <BreadcrumbItem>统计分析</BreadcrumbItem>
            <BreadcrumbItem>电子围栏概览</BreadcrumbItem>
        </BreadcrumbCustom>
        <Row>
            <Col style="width: 100%">
            <ContentCard class="content-card-title" title="电子围栏概览"></ContentCard>
            </Col>
        </Row>
        <Row :gutter="8" style="margin: 8px 0;">
            <Col style="width: 24%">
            <StatisticsCard :data="statisticsData" />
            </Col>
            <Col style="width: 76%">
            <RealtimeAlarmCard :data="realtimeAlarmData" />
            </Col>
        </Row>
        <Row>
            <Col style="width: 100%">
            <AlarmAnalysisCard />
            </Col>
        </Row>
    </div>
</template>

<script>
import { ref, getCurrentInstance } from 'vue'
import StatisticsCard from './components/statisticsCard'
import RealtimeAlarmCard from './components/realtimeAlarmCard'
import AlarmAnalysisCard from './components/alarmAnalysisCard'
export default {
    name: 'ElectronicFenceAnalysis',
    components: {
        StatisticsCard,
        RealtimeAlarmCard,
        AlarmAnalysisCard,
    },
    props: {
    },
    setup() {
        const that = getCurrentInstance()?.appContext.config.globalProperties
        const areaLocation = ref('')

        const statisticsData = ref([])
        const realtimeAlarmData = ref([])

        const getData = async() => {
            const countGroupRes = await that.$request('/electronicFenceOverview/countGroup', {}, 'get')
            if (countGroupRes.success) {
                realtimeAlarmData.value = countGroupRes.data
            }
           const res = await that.$request('/electronicFenceOverview/overview', {}, 'get')
            if (res.success) {
                statisticsData.value = res.data
            }
        }

        // 初始化
        getData()
        return {
            areaLocation,
            realtimeAlarmData,
            statisticsData
        }
    }
}
</script>

<style lang="less" scoped>
.stat-list {
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: #fff;
    border-radius: 4px;
    margin-bottom: 8px;

    .stat-info {
        position: relative;
        padding: 16px 24px;
    }

}
/deep/.ivu-typography {
    margin-bottom: 0;
}
</style>
