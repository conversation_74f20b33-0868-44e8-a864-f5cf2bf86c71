<script lang="ts" setup>

import { getCurrentInstance, onMounted, ref, computed } from 'vue'
import TableContentCard from '@/components/global/TableContentCard'
import SideDetail from '@/components/global/SideDetail'
import { commonService } from '@/api/commonService.js'
const that = getCurrentInstance()?.appContext.config.globalProperties
const tableList = ref<any>([
  { title: '设备编号', key: 'deviceCode', tooltip: true },
  { title: '设备名称', key: 'sbmc', tooltip: true },
  { title: '设备标识码', key: 'bsm', tooltip: true, minWidth: 100 },
    { title: '业务模块', slot: 'modelName', tooltip: true, width: 90 },
    { title: '告警类型', key: 'alarmTypeName', tooltip: true, maxWidth: 130 },
  { title: '告警等级', slot: 'level', tooltip: true, width: 80 },
  { title: '告警详情', key: 'content', tooltip: true },
  { title: '是否推送', slot: 'pushStatus', tooltip: true, width: 80 },
  { title: '区域位置', key: 'areaPath', tooltip: true },
  { title: '告警时间', key: 'alarmTime', width: 160, tooltip: true },
  { title: '操作', slot: 'action', maxWidth: 80 }
])
const searchObj = ref<any>({})
const listCom = ref()
interface searchObjType {
  szjd?: string,
  szsq?: string,
  szdywg?: string,
  deviceCode?: string,
  sbmc?: string,
  bsm?: string,
  status?: number,
  useStatus?: number,
  areaLocation?: string,
  areaPaths?: Array<string>,
    alarmTime?: Array<string>,
    modelId?: any,
    modelIds?: Array<string>,
    queryStartTime?: string,
    queryEndTime?: string
}
function handleSubmit() {
  let params: searchObjType = {}
  params = JSON.parse(JSON.stringify(searchObj.value))
  if (searchObj.value.areaLocation) {
    const arr: Array<string> = searchObj.value.areaLocation.split('/')
    params.szjd = arr[0]
    params.szsq = arr[1]
    params.szdywg = arr[2]
    delete params.areaLocation
  }
  if (searchObj.value.alarmTime && searchObj.value.alarmTime.length > 0 && searchObj.value.alarmTime[0]) {
      params.queryStartTime = searchObj.value.alarmTime[0]
      params.queryEndTime = searchObj.value.alarmTime[1]
      delete params.alarmTime
  }
  // 内置模块id
    if (params.modelId) {
        params.modelIds = [params.modelId]
        delete params.modelId
    } else {
        params.modelIds = modelIds.value
    }

  listCom.value.search(params)
}
onMounted(() => {
  handleSubmit()
})

const showSideDetail = ref<boolean>(false)
const detailInfo = ref<any>(false)
function checkDetail(row:any) {
  showSideDetail.value = true
    row.modelName = that.$Util.findNameByList(serviceModuleList.value, row.model)
  detailInfo.value = row
}
function closeSide() {
  showSideDetail.value = false

}



const contentList = ref<any>([
    { label: '告警编号：', content: '', key: 'code' },
    { label: '设备标识码：', content: '', key: 'bsm' },
    { label: '设备编号：', content: '', key: 'deviceCode' },
    { label: '设备名称：', content: '', key: 'sbmc' },
    { label: '业务模块：', content: '', key: 'modelName' },
    { label: '告警类型：', content: '', key: 'alarmType' },
    { label: '告警等级：', content: '', key: 'level' },
    { label: '告警详情：', content: '', key: 'content' },
    { label: '是否推送：', content: '', key: 'pushStatus' },
    { label: '区域位置：', content: '', key: 'areaPath' },
    { label: '告警时间：', content: '', key: 'alarmTime' },
])

const serviceModuleList = ref<any>([
    { name: '园林土壤', value: 9 },
    { name: '空气质量', value: 11 },
    { name: '水质监测', value: 14 },
    { name: '气象环境', value: 13 },
    { name: '噪声环境', value: 12 },
    { name: '积水监测', value: 16 },
    { name: '雨情监测', value: 17 },
    { name: '湖渠监测', value: 18 }
])
const modelIds = ref<any>([])
const alarmEventListAll = ref<any>([])
const alarmEventList = computed(() => {
    if (searchObj.value.modelId) {
        return alarmEventListAll.value.filter((k: any) => k.modelId == searchObj.value.modelId)
    }
    return alarmEventListAll.value
})
// 得到告警事件
getAlarmEventList()
function getAlarmEventList() {
    let ids = serviceModuleList.value.map((k: any) => k.value)
    modelIds.value = ids
    commonService.getAlarmEventList({ 'type': 2, 'modelIds': ids }).then(res => {
        alarmEventListAll.value = res.data
    })
}

// 改变业务模块
const changeModelId = (val: any) => {
    searchObj.value.alarmType = null
}

</script>
<template>
  <BreadcrumbCustom>
    <BreadcrumbItem>环境监测</BreadcrumbItem>
    <BreadcrumbItem>环境综合告警</BreadcrumbItem>
  </BreadcrumbCustom>
  <ContentCard title="环境综合告警">
    <BaseForm @handle-submit="handleSubmit" :model="searchObj" inline :label-width="90">
      <template #formitem>
        <FormItem label="设备编号" prop="deviceCodeKey">
          <Input v-model="searchObj.deviceCodeKey" :maxlength="20" placeholder="请输入" clearable></Input>
        </FormItem>
        <FormItem label="区域位置" prop="areaLocation">
            <AreaSelectTree v-model="searchObj.areaLocation" table-name="livable_air_quality_device" />
        </FormItem>
        <FormItem label="设备标识码" prop="bsm">
          <Input v-model="searchObj.bsm" :maxlength="20" placeholder="请输入" clearable></Input>
        </FormItem>
          <FormItem label="业务模块" prop="modelId">
              <Select v-model="searchObj.modelId" @on-change="changeModelId" clearable>
                  <Option v-for="item in serviceModuleList" :key="item.value" :value="item.value" clearable>
                      {{ item.name }}
                  </Option>
              </Select>
          </FormItem>
        <FormItem label="告警类型" prop="alarmType">
            <Select v-model="searchObj.alarmType" clearable>
                <Option v-for="item in alarmEventList" :key="item.code" :value="item.code" clearable>
                    {{ item.name }}
                </Option>
            </Select>
        </FormItem>
        <FormItem label="告警等级" prop="level">
          <Select :transfer="false" v-model="searchObj.level" clearable>
            <Option v-for="(item, index) in $enumeration.alarmGrade.filter((i: string, index: number) => index > 0)" :key="index"
              :value="index+1" clearable>
              {{ item }}
            </Option>
          </Select>
        </FormItem>
        <FormItem label="是否推送" prop="pushStatus">
          <Select :transfer="false" v-model="searchObj.pushStatus" clearable>
            <Option v-for="(item, index) in $enumeration.isPush" :key="index" :value="index" clearable>
              {{ item }}
            </Option>
          </Select>
        </FormItem>
          <FormItem label="告警时间" prop="alarmTime">
              <sDatePicker v-model="searchObj.alarmTime" />
          </FormItem>
      </template>
    </BaseForm>
    <TableContentCard :baseBtn="false">
      <template #btn>
        <!-- <Button type="primary">导出</Button>
        <Button type="primary">告警设置</Button> -->
      </template>
      <baseTable :model="searchObj" ref="listCom" url="/alarm/list" :columns="tableList">
          <template #modelName="{ row }">{{ $Util.findNameByList(serviceModuleList, row.model) }}</template>
        <template #level="{ row }">
          <div>
            {{ $enumeration.alarmGrade[row.level] }}
          </div>
        </template>
        <template #pushStatus="{ row }">
          <div>
            <pushStatus :value="row.pushStatus"/>
          </div>
        </template>
        <template #action="{row}">
          <LinkBtn size="small" @click="checkDetail(row)">查看</LinkBtn>
        </template>
      </baseTable>
    </TableContentCard>
  </ContentCard>
  <SideDetail :show="showSideDetail" :data="detailInfo" :contentList="contentList" :modelId="11" @on-cancel="closeSide"></SideDetail>
</template>

<style lang="less" scoped></style>
