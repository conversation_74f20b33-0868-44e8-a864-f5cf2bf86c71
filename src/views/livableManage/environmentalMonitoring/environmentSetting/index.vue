<script lang="ts" setup>
import waterSet from './components/waterSet.vue'
import airSet from './components/airSet.vue'
import {
    ref,
} from "vue";
import { tabList, tableData, tableList } from "./data";


const tabIndex = ref<number>(0);

const handleTabChange = (index: number) => {
    tabIndex.value = index;
};
const handleSpan = ({ row, column, rowIndex, columnIndex }: any) => {
    if (rowIndex === 0 && columnIndex === 0) {
        return [2, 1];
    } else if (rowIndex === 1 && columnIndex === 0) {
        return [0, 0];
    }
    if (rowIndex === 2 && columnIndex === 0) {
        return [2, 1];
    } else if (rowIndex === 3 && columnIndex === 0) {
        return [0, 0];
    }
    if (rowIndex === 4 && columnIndex === 0) {
        return [2, 1];
    } else if (rowIndex === 5 && columnIndex === 0) {
        return [0, 0];
    }
    if (rowIndex === 6 && columnIndex === 0) {
        return [2, 1];
    } else if (rowIndex === 7 && columnIndex === 0) {
        return [0, 0];
    }

    if (rowIndex === 0 && columnIndex === 1) {
        return [2, 1];
    } else if (rowIndex === 1 && columnIndex === 1) {
        return [0, 0];
    }
    if (rowIndex === 2 && columnIndex === 1) {
        return [2, 1];
    } else if (rowIndex === 3 && columnIndex === 1) {
        return [0, 0];
    }
    if (rowIndex === 4 && columnIndex === 1) {
        return [2, 1];
    } else if (rowIndex === 5 && columnIndex === 1) {
        return [0, 0];
    }
    if (rowIndex === 6 && columnIndex === 1) {
        return [2, 1];
    } else if (rowIndex === 7 && columnIndex === 1) {
        return [0, 0];
    }

    if (rowIndex === 0 && columnIndex === 5) {
        return [4, 1];
    } else if (rowIndex > 0 && rowIndex < 4 && columnIndex === 5) {
        return [0, 0];
    }
    if (rowIndex === 4 && columnIndex === 5) {
        return [2, 1];
    } else if (rowIndex > 3 && rowIndex < 6 && columnIndex === 5) {
        return [0, 0];
    }
    if (rowIndex === 6 && columnIndex === 5) {
        return [4, 1];
    } else if (rowIndex > 5 && rowIndex < 9 && columnIndex === 5) {
        return [0, 0];
    }
};
</script>

<template>
    <BreadcrumbCustom>
        <BreadcrumbItem>环境检测</BreadcrumbItem>
        <BreadcrumbItem>环境设置</BreadcrumbItem>
    </BreadcrumbCustom>
    <ContentCard class="container">
        <s-tab :tab-list="tabList" @handleChange="handleTabChange" justify="start">
            <template #air>
                <airSet v-if="tabIndex === 0" v-auth="'environmentMonitoring:envSet:airJudge'" />
            </template>
            <template #water>
                <waterSet v-if="tabIndex === 1" v-auth="'environmentMonitoring:envSet:waterJudge'" />
            </template>
        </s-tab>
        <Poptip class="first-icon" placement="right-start" v-auth="'environmentMonitoring:envSet:airJudge'">
            <Icon class="iconfont icon-question-circle"></Icon>
            <template #title>
                <div>GB-标准参考值</div>
            </template>
            <template #content>
                <base-table :show-page="false" :data="tableData" :span-method="handleSpan" :columns="tableList">
                </base-table>
            </template>
        </Poptip>
    </ContentCard>
</template>

<style lang="less" scoped>

.container {
    .iconfont {
        color: #c9cdd4;
        font-size: 10px;
    }

    position: relative;

    .first-icon {
        position: absolute;
        top: 20px;
        left: 162px;
    }

    .footer {
        display: flex;
        flex-direction: column;
        row-gap: 8px;
    }
}


</style>
