<template>
    <div class="water-set">
        <Form ref="waterSetForm" :model="searchObj" :rules="rules" :label-width="0">
        <div class="info">
            <div class="title-box">
                <h5 class="title">周达标判断</h5>
            </div>
            <div class="box">
                <FormItem prop="weekValue" style="width: 200px">
                    <input-num v-model="searchObj.weekValue" append="%"></input-num>
                </FormItem>
                <div class="tip">注释：项目或设备每周达标判断标准，实际达标率大于等于达标率标准，为达标</div>
            </div>
        </div>
        <div class="info">
            <div class="title-box">
                <h5 class="title">月达标判断</h5>
            </div>
            <div class="box">
                <FormItem prop="monthValue" style="width: 200px">
                    <input-num v-model="searchObj.monthValue" append="%"></input-num>
                </FormItem>
                <div class="tip">注释：项目或设备每月达标判断标准，实际达标率大于等于达标率标准，为达标</div>
            </div>
        </div>
        <div class="info">
            <div class="title-box">
                <h5 class="title">年达标判断</h5>
            </div>
            <div class="box">
                <FormItem prop="yearValue" style="width: 200px">
                    <input-num v-model="searchObj.yearValue" append="%"></input-num>
                </FormItem>
                <div class="tip">注释：项目或设备每年达标判断标准，实际达标率大于等于达标率标准，为达标</div>
            </div>
        </div>
            <div class="info">
                <div class="title-box">
                    <h5 class="title">自定义范围判断</h5>
                </div>
                <div class="box">
                    <FormItem prop="cusValue" style="width: 200px">
                        <input-num v-model="searchObj.cusValue" append="%"></input-num>
                    </FormItem>
                    <div class="tip">注释：项目或设备自定义达标判断标准，实际达标率大于等于达标率标准，为达标</div>
                </div>
            </div>
        </Form>
        <div class="btn" v-auth="'environmentMonitoring:envSet:saveWater'">
            <Button type="primary" @click="save" :loading="loading">提交</Button>
            <Button @click="cancel">取消</Button>
        </div>
    </div>
</template>

<script>
import { validateform } from '@/utils/validateform'
const defaultSearchObj = {
    weekValue: 0.00,
    monthValue: 0.00,
    yearValue: 0.00,
    cusValue: 0.00
}
export default {
    name: 'WaterSet',
    data() {
        const validateNum = [validateform.powerNum(3, 2), { max: 100, min: 0, message: '最大值为100', type: 'number' }]
        return {
            searchObj: this.$Util.objClone(defaultSearchObj),
            rules: {
                weekValue: validateNum,
                monthValue: validateNum,
                yearValue: validateNum,
                cusValue: validateNum
            },
            info: {},
            loading: false
        }
    },
    mounted() {
        this.getDetail()
    },
    methods: {
        save() {
            this.$refs['waterSetForm'].validate((valid) => {
                if (valid) {
                    this.loading = true
                    this.$request('/waterStandard', this.searchObj, 'put').then(res => {
                        if (res.success) {
                            this.info = this.$Util.objClone(this.searchObj)
                            this.$Message.success('保存成功')
                        }
                    }).finally(() => {
                        this.loading = false
                    })
                }
            })
        },
        getDetail() {
            this.$request('/waterStandard').then(res => {
                let data = res.data || {}
                let searchObj = this.$Util.objClone(defaultSearchObj)
                for (let i in searchObj) {
                    searchObj[i] = data[i] || 0
                }
                this.searchObj = searchObj
                this.info = this.$Util.objClone(searchObj)
            })
        },
        cancel() {
            this.searchObj = this.$Util.objClone(this.info)
        }
    }
}
</script>

<style lang="less" scoped>

.water-set{
    padding: 10px 40px;
    .title-box {
        position: relative;
        height: 25px;
        margin-bottom: 6px;
        .title {
            color: #1e2a55;
            font-size: 14px;
            font-weight: 600;
        }

    }
    .info{
        margin-bottom: 16px;
        .box{
            padding: 0 24px;
        }
        .tip{
            font-size: 12px;
            color: @text-2-1;
            padding-top: 4px;
        }
    }
    .ivu-form-item{
        margin-bottom: 0;
    }
    /deep/.ivu-form-item-error-tip{
        left: 100%;
        top: 6px;
        white-space: nowrap;
        padding-left: 8px;
    }
}
</style>
