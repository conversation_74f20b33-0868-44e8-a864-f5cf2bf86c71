<script lang="ts" setup>
import FormBox from './FormBox.vue';
import {
    getCurrentInstance,
    ref,
} from 'vue';
import { airGrain } from '../data';
import {
    getNewConfig,
    saveOrUpdateConfig,
} from '@/api/livableManage/airQualityService.js';
const that = getCurrentInstance()?.appContext.config.globalProperties;
const form = ref<any>({
    criteria: '所有污染物达标检测点达标',
});

let firstPointStandard: string[] = [];
const getConfig = async() => {
    let res: any = await getNewConfig();
    if (res.success) {
        form.value = res.data;
        if (res.data.pointStandard === 1) {
            firstPointStandard = res.data.selectProperties.split(',')
        } else {
            firstPointStandard = []
        }
        form.value.airGrain = firstPointStandard
        if (res.data.pointStandard !== 1) isDisabled.value = true;
        form.value.airGrain =
            (form.value.selectProperties && form.value.selectProperties.split(',')) || [];
    }
};
getConfig();
const isDisabled = ref<boolean>(false);
const handlePointStandardChange = (label: number) => {
    if (label !== 1) {
        isDisabled.value = true;
        form.value.airGrain = airGrain;
        form.value.selectProperties = airGrain.join(',');
    } else {
        isDisabled.value = false;
        form.value.airGrain = firstPointStandard;
        form.value.selectProperties = form.value.airGrain.join(',');
    }
};
// 选择复选框
const checkbox = (checkedArr: any[]) => {
    form.value.selectProperties = checkedArr.join(',');
};
const formCom1 = ref();
const formCom2 = ref();
const save = async() => {
    let flag: boolean = await formCom1.value.formCom.validate();
    if (flag) {
        formCom2.value.formCom.validate(async(validate: boolean) => {
            if (validate) {
                let res: any = await saveOrUpdateConfig(form.value);
                if (res.success) {
                    that?.$Message.success('保存成功');
                    getConfig();
                }
            }
        });
    }
};
const cancel = () => {
    getConfig();
};


</script>

<template>
    <div class="container">
        <div class="content">
            <div class="title-box">
                <h5 class="title">
                    污染物达标标准
                    <Poptip class="poptip-icon" content="浓度限值表示，低于等于浓度限值为达标，超过浓度限值为不达标" placement="right-start">
                        <Icon class="iconfont icon-question-circle"></Icon>
                    </Poptip>
                </h5>
            </div>
            <TitleCustom class="title-card" title="小时平均"></TitleCustom>
            <FormBox ref="formCom1" origin-key="hour" v-model="form.detailList" />
            <TitleCustom class="title-card" title="日均点"></TitleCustom>
            <FormBox ref="formCom2" origin-key="day" v-model="form.detailList" />
            <div class="footer">
                <div class="title-box">
                    <h5 class="title" style="margin-top: 6px;">监测点环境标准</h5>
                </div>
                <div>判断标准</div>
                <RadioGroup @on-change="handlePointStandardChange" v-model="form.pointStandard">
                    <Radio :label="3">所有污染物达标检测点达标</Radio>
                    <Radio :label="1">选中污染物达标检测点达标</Radio>
                    <Radio :label="2">任意污染物达标监测点达标</Radio>
                </RadioGroup>
                <CheckboxGroup v-model="form.airGrain" @on-change="checkbox">
                    <Checkbox :disabled="isDisabled" :label="item" v-for="(item, index) in airGrain" :key="index">
                    </Checkbox>
                </CheckboxGroup>
            </div>
            <div class="btn" v-auth="'environmentMonitoring:envSet:saveAir'">
                <Button type="primary" @click="save">提交</Button>
                <Button @click="cancel">取消</Button>
            </div>
        </div>
</div>
</template>

<style lang="less" scoped>
.title-box {
    position: relative;
    height: 25px;
    .title {
        position: absolute;
        left: -8px;
        top: 0;
        color: #1e2a55;
        font-size: 14px;
        font-weight: 600;

        display: flex;
        align-items: center;

        .poptip-icon {
            /deep/ .ivu-poptip-rel {
                display: flex;
                align-items: center;
            }
        }

        .iconfont {
            margin: 2px 0 0 8px;
        }
    }

}

.container {
    .iconfont {
        color: #c9cdd4;
        font-size: 10px;
    }

    position: relative;

    .footer {
        display: flex;
        flex-direction: column;
        row-gap: 8px;
    }
}



.title-card {
    margin-bottom: 8px;
}

.content {
    padding: 10px 48px;

    .btn {
        margin-top: 20px;
        display: flex;
    }
}
</style>
