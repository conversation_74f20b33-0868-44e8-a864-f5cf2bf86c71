<script lang="ts" setup>
import {
    defineExpose,
    defineProps,
    markRaw,
    toRefs,
    defineEmits,
    onMounted,
    ref,
    watch,
} from "vue";
import { validateform } from "@/utils/validateform";
import { defaultDetailList } from "../data";
import { isNullOrEmpty } from "@/utils/tool";
const emit: any = defineEmits();
const props = defineProps({
    modelValue: {
        default: () => [],
    },
    originKey: {
        default: "hour",
    },
});
const formDynamic = ref<any>({ form: [] });

watch(
    () => props.modelValue,
    (newVal, oldVal) => {
        if (isNullOrEmpty(newVal)) {
            formDynamic.value.form = defaultDetailList;
        } else {
            formDynamic.value.form = newVal;
        }
    },
    { deep: true, immediate: true }
);
const enumKey = {
    hour: "hourAvePeak",
    day: "dayAvePeak",
};
const handleChange = () => {
    emit("update:modelValue", formDynamic.value.form);
};
const formCom = ref();
defineExpose({ formCom });
</script>
<template>
    <Form label-position="top" ref="formCom" :model="formDynamic">
        <Row gutter="20">
            <Col span="4" v-for="(item, index) in formDynamic.form" :key="index">
                <FormItem
                    v-show="item.propertiesName !== 'CO浓度'"
                    :label="item.propertiesName + '限值'"
                    :prop="'form.' + index + '.' + enumKey[originKey]"
                    :rules="[validateform.requiredNum, validateform.nonnegativeNumber]"
                >
                    <InputNum
                        @on-change="handleChange"
                        v-model="item[enumKey[originKey]]"
                        append="μg/m³"
                    ></InputNum>
                </FormItem>
                <FormItem
                    v-show="item.propertiesName === 'CO浓度'"
                    :label="item.propertiesName + '限值'"
                    :prop="'form.' + index + '.' + enumKey[originKey]"
                    :rules="[validateform.requiredNum, validateform.nonnegativeNumber]"
                >
                    <InputNum
                        @on-change="handleChange"
                        v-model="item[enumKey[originKey]]"
                        append="mg/m³"
                    ></InputNum>
                </FormItem>
            </Col>
        </Row>
    </Form>
</template>

<style lang="less" scoped></style>
