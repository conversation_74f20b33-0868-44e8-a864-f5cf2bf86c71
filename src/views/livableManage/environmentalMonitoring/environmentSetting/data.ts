const airGrain = ['PM10', 'PM2.5', 'SO2', 'NO2', 'CO', 'O3']
const tabList = [
    {
        name: "空气质量达标判断",
        key: "air",
        auth: 'environmentMonitoring:envSet:airJudge'
    },
    {
        name: "水质环境判断",
        key: "water",
        auth: 'environmentMonitoring:envSet:waterJudge'
    }
];
const tableList = [
  { title: '序号', type: 'index', width: 60 },
  { title: '污染物项目', key: 'pollutantItem', width: 150, tooltip: true },
  { title: '平均时间', key: 'averageTime', width: 150 },
  {
    title: '浓度限度值', key: 'Concentration', width: 150, children: [
      {
        title: '一级',
        key: 'stair',
        width: 75
      },
      {
        title: '二级',
        key: 'second',
        width: 75
      },
    ]
  },
  { title: '单位', key: 'unit', width: 80 },
]
const tableData = [
  {
    pollutantItem: '二氧化硫 （SO2）',
    averageTime: '24小时平均',
    stair: 50,
    second: 150,
    unit: 'μg/m³'
  },
  {
    pollutantItem: '二氧化硫 （SO2）',
    averageTime: '1小时平均',
    stair: 150,
    second: 500,
    unit: 'μg/m³'
  },
  {
    pollutantItem: '二氧化氮 （NO2）',
    averageTime: '24小时平均',
    stair: 80,
    second: 80,
    unit: 'μg/m³'
  },
  {
    pollutantItem: '二氧化氮 （NO2）',
    averageTime: '1小时平均',
    stair: 200,
    second: 200,
    unit: 'μg/m³'
  },
  {
    pollutantItem: '一氧化碳 （CO）',
    averageTime: '24小时平均',
    stair: 4,
    second: 4,
    unit: 'mg/m³'
  },
  {
    pollutantItem: '一氧化碳 （CO）',
    averageTime: '1小时平均',
    stair: 10,
    second: 10,
    unit: 'mg/m³'
  },
  {
    pollutantItem: '臭氧 （O3）',
    averageTime: '日最大8小时平均',
    stair: 100,
    second: 160,
    unit: 'μg/m³'
  },
  {
    pollutantItem: '臭氧 （O3）',
    averageTime: '1小时平均',
    stair: 160,
    second: 200,
    unit: 'μg/m³'
  },
  {
    pollutantItem: '颗粒物 （粒径小于等于10μm）',
    averageTime: '24小时平均',
    stair: 50,
    second: 150,
    unit: 'μg/m³'
  },
  {
    pollutantItem: '颗粒物 （粒径小于等于2.5μm）',
    averageTime: '24小时平均',
    stair: 37,
    second: 75,
    unit: 'μg/m³'
  }
]
const defaultDetailList = [
  {
    "id": 103,
    "standardId": 18,
    "propertiesKey": "o3",
    "propertiesName": "O3浓度",
    "dayAvePeak": 0,
    "hourAvePeak": 0
  },
  {
    "id": 104,
    "standardId": 18,
    "propertiesKey": "no2",
    "propertiesName": "NO2浓度",
    "dayAvePeak": 0,
    "hourAvePeak": 0
  },
  {
    "id": 105,
    "standardId": 18,
    "propertiesKey": "so2",
    "propertiesName": "SO2浓度",
    "dayAvePeak": 0,
    "hourAvePeak": 0
  },
  {
    "id": 106,
    "standardId": 18,
    "propertiesKey": "co",
    "propertiesName": "CO浓度",
    "dayAvePeak": 0,
    "hourAvePeak": 0
  },
  {
    "id": 107,
    "standardId": 18,
    "propertiesKey": "pm_10",
    "propertiesName": "PM10浓度",
    "dayAvePeak": 0,
    "hourAvePeak": 0
  },
  {
    "id": 108,
    "standardId": 18,
    "propertiesKey": "pm_25",
    "propertiesName": "PM2.5浓度",
    "dayAvePeak": 0,
    "hourAvePeak": 0
  }
]
export { airGrain, tableList, tableData,defaultDetailList, tabList }
