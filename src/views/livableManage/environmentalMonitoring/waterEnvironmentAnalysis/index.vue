<template>
    <div>
        <BreadcrumbCustom>
            <BreadcrumbItem>环境监测</BreadcrumbItem>
            <BreadcrumbItem>水质环境概览</BreadcrumbItem>
        </BreadcrumbCustom>
        <ContentCard style="margin-bottom: 8px;">
            <Dropdown class="device-sel" @on-click="chooseDevice" trigger="click" placement="bottom-start">
                <Title level="5" align="center">{{ m.curDevice.sbmc }}（{{ m.curDevice.deviceCode }}） <Icon type="ios-arrow-down" /></Title>
                <template #list>
                    <DropdownMenu>
                        <DropdownItem
                            v-for="item in m.deviceList"
                            :class="item.deviceCode == m.curDevice.deviceCode ? 'on':''"
                            :name="item.deviceCode" :key="item.id"
                        >{{ item.sbmc }}（{{ item.deviceCode }}）</DropdownItem>
                    </DropdownMenu>
                </template>
            </Dropdown>
        </ContentCard>
        <Row :gutter="8" style="margin-bottom: 8px;flex-wrap: nowrap;">
            <Col style="width: 288px">
                <div class="state-list">
                    <imgCard
                        :img="require('./images/icon_core1.png')" label="当前水质等级"
                        :value="$enumeration.waterQualityClassify[m.coreObj.currentWaterQualityGrade]"
                        unit="" :horizontal="true" imgWidth="48" fontSize="20" />
                    <imgCard
                        :img="require('./images/icon_core2.png')" label="本月水质达标天数"
                        :value="m.coreObj.thisMonthStandardDays" unit="天" :horizontal="true" imgWidth="48" />
                    <imgCard
                        :img="require('./images/icon_core3.png')" label="本月水质达标率"
                        :value="formatPercent(m.coreObj.thisMonthStandardRate)" unit="%" :horizontal="true" imgWidth="48" />
                    <imgCard
                        :img="require('./images/icon_core4.png')" label="本年水质达标天数"
                        :value="m.coreObj.thisYearStandardDays" unit="天" :horizontal="true" imgWidth="48" />
                    <imgCard
                        :img="require('./images/icon_core5.png')" label="本年水质达标率"
                        :value="formatPercent(m.coreObj.thisYearStandardRate)" unit="%" :horizontal="true" imgWidth="48" />
                </div>
            </Col>
            <Col flex="1">
                <ContentCard title="当前水质">
                    <div class="attr-list">
                        <div v-for="item in m.curAttrList" :key="item.identifier" class="box">
                            <div class="img">
                                <img :src="require('./images/icon_' + item.identifier + '.png')" alt="">
                            </div>
                            <div class="name">{{ item.attrName }}</div>
                            <div class="val">{{ item.value }}<span>{{ item.unit }}</span></div>
                        </div>
                    </div>
                    <echart-item :option="wqOption" class="wq-echart" />
                </ContentCard>
            </Col>
            <Col style="width: 420px">
                <ContentCard>
                    <div class="rank-tit">
                        <Title level="5">超标污染物项目排名</Title>
                        <dateSelect @on-change="changeRankTime" :hideDate="true" :hideToday="true" />
                    </div>
                    <rankList :list="m.rankList" />
                </ContentCard>
            </Col>
        </Row>

        <ContentCard style="margin-bottom: 8px;">
            <div class="rank-tit">
                <Title level="5">水质等级</Title>
                <dateSelect @on-change="changeWaterLevelTime" />
            </div>
            <echart-item :option="lineOption" class="line-echart" />
        </ContentCard>
        <ContentCard>
            <div class="rank-tit">
                <Title level="5">水质等级分布</Title>
                <DatePicker
                    v-model="m.waterDisYear" @on-change="waterDisYearChange"
                    :editable="false" type="year" placeholder="选择年" format="yyyy"
                    style="width: 200px" />
            </div>
            <echart-item :option="barOption" class="line-echart" />
        </ContentCard>

    </div>
</template>

<script>
import EchartItem from '@/components/common/EchartItem/index'
import dateSelect from '@/components/common/dateSelect/index'
import rankList from '@/components/common/rankList/rankDashed'
import { EchartsTooltip, tooltipExtraCssText, hexToRgba } from '@/utils/tool.ts'

const colorLine = ['#21CCFF', '#05D0C4', '#18D865', '#F7BA1E', '#FAAC7B', '#FF708B']
export default {
    components: {
        EchartItem,
        dateSelect,
        rankList
    },
    data() {
        return {
            wqOption: {
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow'
                    },
                    formatter: (params) => {
                        let param = params[0]
                        let html = `<div>${param.axisValue}</div>`
                        html += `<div>${param.marker}${param.seriesName} ${this.$enumeration.waterQualityClassify[param.value]}</div>`
                        html += `<div>${param.marker}${param.data.num}${param.data.unit}</div>`
                        return html
                    }
                },
                grid: {
                    top: 30,
                    left: 0,
                    right: 0,
                    bottom: 5,
                    containLabel: true
                },
                color: ['#165DFF'],
                xAxis: [
                    {
                        type: 'category',
                        data: [],
                        axisTick: {
                            alignWithLabel: true,
                            lineStyle: {
                                color: '#C2CAD8'
                            }
                        },
                        axisLine: {
                            lineStyle: {
                                color: '#C2CAD8'
                            }
                        },
                        axisLabel: {
                            color: '#798799'
                        }
                    }
                ],
                yAxis: [
                    {
                        type: 'category',
                        data: [0, 1, 2, 3, 4, 5, 6],
                        boundaryGap: false,
                        axisLabel: {
                            formatter: (value, index) => {
                                if (value > 0) {
                                    return this.$enumeration.waterQualityClassify[value];
                                }
                            }
                        },
                        axisLine: { show: false },
                        axisTick: { show: false },
                        splitLine: {
                            show: true,
                            lineStyle: {
                                color: '#E0E6F1',
                                type: 'dashed'
                            }
                        },
                        splitArea: {
                            show: true,
                            areaStyle: {
                                color: ['#ffffff', '#F8FAFB']
                            }
                        }
                    }
                ],
                series: [
                    {
                        name: '当前水质',
                        type: 'bar',
                        barMaxWidth: '40%',
                        itemStyle: {
                            borderRadius: 2
                        },
                        label: {
                            show: true,
                            position: 'top',
                            formatter: (param) => {
                                return `${param.data.num}${param.data.unit}`
                            }
                        },
                        data: []
                    }
                ]
            },
            lineOption: {
                grid: {
                    left: 10,
                    right: 150,
                    top: 40,
                    bottom: 5,
                    containLabel: true
                },
                tooltip: {
                    extraCssText: tooltipExtraCssText,
                    borderWidth: 0,
                    formatter: (param) => {
                        let value = param.value
                        let html = '<div style="background: linear-gradient(304.17deg, rgba(253, 254, 255, 0.6) -6.04%, rgba(244, 247, 252, 0.6) 85.2%);backdrop-filter: blur(5px);padding: 8px;border-radius: 4px;">'
                        html += `<div style="width: 180px;line-height: 20px;margin-bottom: 4px;">${value[0]}</div>`
                        html += `<div style="background: rgba(255, 255, 255, 0.9);margin-bottom: 4px;border-radius: 4px;">`
                        html += `<div style="display: flex;align-items: center;height: 32px;padding: 0 8px;">${param.marker}
                                 <span style="flex: 1">${param.seriesName}</span>
                                 ${this.$enumeration.waterQualityClassify[value[1]]}</div>`
                        html += '</div>'
                        html += `<div style="background: rgba(255, 255, 255, 0.9);border-radius: 4px;padding: 4px 8px;">`
                        value.slice(2).forEach(cit => {
                            html += `<div style="line-height: 20px;">${cit.name}: ${cit.value}${cit.unit || ''}</div>`
                        })
                        html += '</div>'
                        html += '</div>'
                        return html
                    }
                },
                xAxis: {
                    type: 'category',
                    axisTick: {
                        alignWithLabel: true,
                        lineStyle: {
                            color: '#C2CAD8'
                        }
                    },
                    axisLine: {
                        lineStyle: {
                            color: '#C2CAD8'
                        }
                    },
                    nameTextStyle: {
                        color: '#798799'
                    },
                    axisLabel: {
                        color: '#798799',
                        formatter: (value) => {
                            if (this.m.waterLevelTimeType === 0) {
                                return this.$Util.formatDate(value, 'HH')
                            } else {
                                return value
                            }
                        }
                    }
                },
                yAxis: {
                    type: 'value',
                    max: 6,
                    min: 0,
                    interval: 1,
                    splitLine: {
                        show: true,
                        lineStyle: {
                            color: '#E0E6F1',
                            type: 'dashed',
                            width: 0.5
                        }
                    },
                    axisLabel: {
                        formatter: (value, index) => {
                            if (value > 0) {
                                return this.$enumeration.waterQualityClassify[value];
                            }
                        }
                    },
                    axisLine: { show: false },
                    axisTick: { show: false }
                },
                visualMap: [
                    {
                        left: 'right',
                        top: 15,
                        dimension: 1,
                        min: 1,
                        max: 6,
                        itemWidth: 30,
                        itemHeight: 100,
                        calculable: true,
                        precision: 0.1,
                        text: ['圆形大小：水质等级'],
                        textGap: 15,
                        inRange: {
                            symbolSize: [10, 32]
                        },
                        outOfRange: {
                            symbolSize: [10, 32],
                            opacity: 0.5
                        },
                        controller: {
                            inRange: {
                                color: ['#6AA1FF', '#165DFF']
                            },
                            outOfRange: {
                                color: ['#999']
                            }
                        }
                    },
                    {
                        left: 'right',
                        bottom: 10,
                        dimension: 1,
                        min: 1,
                        max: 6,
                        itemHeight: 100,
                        text: ['颜色：水质等级'],
                        textGap: 15,
                        inRange: {
                            color: colorLine,
                        },
                        controller: {
                            inRange: {
                                color: colorLine
                            },
                            outOfRange: {
                                color: ['#999']
                            }
                        }
                    }
                ],
                series: [
                    {
                        name: '水质等级',
                        type: 'scatter',
                        data: []
                    }
                ]
            },
            barOption: {
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow'
                    },
                    extraCssText: tooltipExtraCssText,
                    formatter: (params) => {
                        // console.log(params)
                        let cur = ''
                        let last = ''
                        let curArr = []
                        let lastArr = []
                        params.forEach(item => {
                            let m = item.name < 10 ? '0' + item.name : item.name
                            if (item.seriesIndex < 6) {
                                item.name = this.m.waterDisYear + '-' + m
                                curArr.push(item)
                            } else {
                                item.name = (this.m.waterDisYear - 1) + '-' + m
                                lastArr.push(item)
                            }
                        })
                        if (curArr.length > 0) {
                            cur = EchartsTooltip(curArr)
                        }
                        if (lastArr.length > 0) {
                            last = EchartsTooltip(lastArr)
                        }
                        let html = `<div style="display: flex;" >`
                        if (cur) {
                            html += `<div style="margin-right: 4px;">${cur}</div>`
                        }
                        if (last) {
                            html += `<div class="last">${last}</div>`
                        }
                        html += '</div>'
                        return html
                    }
                },
                grid: {
                    top: 30,
                    bottom: 30,
                    left: 10,
                    right: 0,
                    containLabel: true
                },
                color: ['#246EFF', '#21CCFF', '#9A81FF'],
                legend: {
                    show: true,
                    itemHeight: 10,
                    itemWidth: 10,
                    icon: 'circle',
                    bottom: 0
                },
                xAxis: [
                    {
                        type: 'category',
                        data: [],
                        axisPointer: {
                            type: 'shadow'
                        },
                        axisLabel: {
                            color: '#86909C',
                            formatter: '{value} 月'
                        },
                        axisLine: {
                            show: true,
                            lineStyle: {
                                color: '#E5E6EB'
                            }
                        },
                        axisTick: {
                            show: true,
                            alignWithLabel: true,
                            lineStyle: {
                                color: '#86909C'
                            }
                        }
                    }
                ],
                yAxis: [
                    {
                        type: 'value',
                        name: '天',
                        splitLine: {
                            show: true,
                            lineStyle: {
                                color: '#F2F3F5'
                            }
                        },
                        axisLabel: {
                            color: '#4E5969'
                        }
                    }
                ],
                series: []
            },
            m: {
                curDevice: {},
                deviceList: [],
                coreObj: {}, // 核心指标
                curAttrList: [],
                rankList: [],
                rankTime: [],
                waterLevelTime: [],
                waterLevelTimeType: '', // 时间类型
                waterDisYear: ''
            }
        }
    },
    created() {
        this.m.waterDisYear = this.$Util.formatDate(new Date(), 'YYYY')
        this.getDeviceList()
    },
    methods: {
        initData() {
            let barData = []
            for (let i = 0; i < 16; i++) {
                barData.push({
                    value: Math.floor(Math.random() * 100),
                    name: i + '号控制器号控制器号控制器'
                })
            }
            this.pieOption.series[0].data = barData
            let line = {
                data: [],
                day: []
            }
            let t = new Date()
            for (let i = 0; i < 10; i++) {
                line.data.push(Math.floor(Math.random() * 100))
                line.day.push(this.$Util.formatDate(t))
            }
        },
        formatPercent(num) {
            return this.$Util.formatNum(num * 100)
        },
        // 选择设备
        chooseDevice(code) {
            let obj = this.m.deviceList.find(it => it.deviceCode == code)
            this.m.curDevice = obj || {}
            this.getData()
        },
        getDeviceList() {
            let param = {
                customQueryParams: {},
                page: { current: 1, size: -1 }
            }
            this.$request('/waterQualityDevice/list', param, 'post').then(res => {
                if (res.success) {
                    this.m.deviceList = res.data.records
                    this.m.curDevice = this.m.deviceList[0] || {}
                    this.getData()
                }
            })
        },
        getData() {
            if (!this.m.curDevice.deviceCode) {
                return
            }
            this.getNumData()
            this.queryCurrentWaterQuality()
            this.getRankList()
            this.getWaterLevelData()
            this.queryWaterQualityGradeDistribution()
        },
        // 核心指标
        getNumData() {
            let param = {
                deviceCode: this.m.curDevice.deviceCode
            }
            this.$request('/waterQualityEnvAnalysis/queryCoreMetrics', param, 'post').then(res => {
                if (res.success) {
                    this.m.coreObj = res.data
                }
            })
        },
        // 查询当前水质
        queryCurrentWaterQuality() {
            let param = {
                deviceCode: this.m.curDevice.deviceCode
            }
            this.$request('/waterQualityEnvAnalysis/queryCurrentWaterQuality', param, 'post').then(res => {
                if (res.success) {
                    const curAttr = ['temperature', 'ph', 'conductivity', 'turbidity']
                    let curAttrList = []
                    let obj = {
                        xData: [],
                        data: []
                    }
                    res.data && res.data.forEach(item => {
                        if (curAttr.indexOf(item.identifier) >= 0) {
                            curAttrList.push(this.$Util.objClone(item))
                        } else {
                            obj.xData.push(item.attrName)
                            obj.data.push({
                                value: item.grade,
                                num: item.value,
                                unit: item.unit
                            })
                        }
                    })
                    this.m.curAttrList = curAttrList
                    this.wqOption.xAxis[0].data = obj.xData
                    this.wqOption.series[0].data = obj.data
                }
            })
        },
        // 超标污染物项目排名时间选择
        changeRankTime(val) {
            this.m.rankTime = val
            this.getRankList()
        },
        // 超标污染物项目排名
        getRankList() {
            if (!this.m.curDevice.deviceCode || !this.m.rankTime[0]) {
                return
            }
            let param = {
                deviceCode: this.m.curDevice.deviceCode,
                queryStartTime: this.m.rankTime[0],
                queryEndTime: this.m.rankTime[1]
            }
            this.$request('/waterQualityEnvAnalysis/queryRankItemsOverPollutantStandard', param, 'post').then(res => {
                if (res.success) {
                    let total = res.data[0]?.overTimes
                    let list = []
                    res.data.forEach(item => {
                        let obj = { name: item.attrName, value: item.overTimes }
                        obj.percent = this.$Util.formatNum(100 * item.overTimes / total)
                        list.push(obj)
                    })
                    this.m.rankList = list
                }
            })
        },
        // 水质等级时间选择
        changeWaterLevelTime(val, type) {
            this.m.waterLevelTime = val
            this.m.waterLevelTimeType = type
            this.getWaterLevelData()
        },
        // 水质等级
        getWaterLevelData() {
            if (!this.m.curDevice.deviceCode || !this.m.waterLevelTime[0]) {
                return
            }
            let param = {
                deviceCode: this.m.curDevice.deviceCode,
                queryStartTime: this.m.waterLevelTime[0],
                queryEndTime: this.m.waterLevelTime[1],
                granularityType: 2 // 粗细度类型 1:小时 2:天
            }
            if (this.m.waterLevelTimeType === 0) {
                param.granularityType = 1
            }
            this.$request('/waterQualityEnvAnalysis/queryWaterQualityGradeLineChart', param, 'post').then(res => {
                if (res.success) {
                    let list = []
                    res.data.forEach(item => {
                        let arr = []
                        if (this.m.waterLevelTimeType === 0) {
                            arr.push(item.time)
                        } else {
                            arr.push(this.$Util.formatDate(item.time, 'YYYY-MM-DD'))
                        }
                        arr.push(item.grade)
                        item.details.forEach(cit => {
                            arr.push({
                                value: cit.value,
                                name: cit.attrName,
                                unit: cit.unit
                            })
                        })
                        list.push(arr)
                    })
                    this.lineOption.series[0].data = list
                }
            })
        },
        // 水质等级分布选择年
        waterDisYearChange(val) {
            this.m.waterDisYear = val
            this.queryWaterQualityGradeDistribution()
        },
        // 水质等级分布
        queryWaterQualityGradeDistribution() {
            if (!this.m.curDevice.deviceCode) {
                return
            }
            let param = {
                deviceCode: this.m.curDevice.deviceCode,
                queryEndTime: this.m.waterDisYear
            }
            this.$request('/waterQualityEnvAnalysis/queryWaterQualityGradeDistribution', param, 'post').then(res => {
                if (res.success) {
                    let statObj = {
                        cur: {
                            day: [],
                            data: {
                                1: [],
                                2: [],
                                3: [],
                                4: [],
                                5: [],
                                6: []
                            }
                        },
                        last: {
                            day: [],
                            data: {
                                1: [],
                                2: [],
                                3: [],
                                4: [],
                                5: [],
                                6: []
                            }
                        }
                    }
                    let curYear = new Date().getFullYear()
                    let curMonth = new Date().getMonth()
                    res.data.forEach((child, index) => {
                        let key = 'last'
                        let list = child
                        if (index === 1) {
                            key = 'cur'
                            if (this.m.waterDisYear == curYear) {
                                list = child.slice(0, curMonth + 1)
                            }
                        }
                        list.forEach(item => {
                            statObj[key].day.push(item.datetime)
                            if (item.distribution && item.distribution.length > 0) {
                                let grade = []
                                item.distribution.forEach(cit => {
                                    statObj[key].data[cit.grade].push(cit.days)
                                    grade.push(cit.grade)
                                })
                                for (let i = 1; i <= 6; i++) {
                                    if (grade.indexOf(i) < 0) {
                                        statObj[key].data[i].push(0)
                                    }
                                }
                            } else {
                                for (let i = 1; i <= 6; i++) {
                                    statObj[key].data[i].push(0)
                                }
                            }

                        })
                    })
                    let series = []
                    for (let k in statObj) {
                        for (let i in statObj[k].data) {
                            let obj = {
                                name: this.$enumeration.waterQualityClassify[i],
                                type: 'bar',
                                stack: k,
                                barWidth: 15,
                                barGap: '100%',
                                data: statObj[k].data[i],
                                itemStyle: {
                                }
                            }
                            if (k === 'last') {
                                obj.itemStyle.color = hexToRgba(colorLine[i - 1], 0.3)
                                // obj.xAxisIndex = 0
                            } else {
                                obj.itemStyle.color = colorLine[i - 1]
                                // obj.xAxisIndex = 1
                            }
                            series.push(obj)
                        }
                    }

                    this.barOption.xAxis[0].data = statObj.last.day.map(m => parseInt(m.split('-')[1]))
                    // this.barOption.xAxis[1].data = statObj.cur.day
                    this.barOption.series = series
                    console.log(statObj)
                    console.log(series)
                }
            })
        }
    }
}
</script>

<style lang="less" scoped>

.device-sel{
    position: relative;
    .ivu-typography{
        margin-bottom: 0;
        cursor: pointer;
        padding: 4px 8px;
        &:hover{
            background: @fill-1;
        }
    }
    /deep/.ivu-select-dropdown{
        width: 100%;
        min-width: 300px;
        max-height: 240px;
        overflow: auto;
    }
    .ivu-dropdown-item.on{
        color: @primary-color;
    }
}
.state-list{
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items:center;
    height: 100%;
    .img-card{
        width: 100%;
        background: #fff;
        flex: inherit;
    }
}
.attr-list{
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 -8px;
    width: 100%;
    overflow: hidden;
    .box{
        display: flex;
        align-items: center;
        margin: 0 8px;
        .img{
            width: 16px;
            margin-right: 8px;
            img{
                display: block;
                width: 100%;
            }
        }
        .name{
            margin-right: 8px;
            color: @text-color;
            font-size: 12px;
        }
    }
}
.wq-echart{
    height: 264px;
}
.ivu-col{
    & > .content-card{
        height: 100%;
        width: 100%;
    }
}
.rank-tit{
    display: flex;
    align-items: center;
    justify-content: space-between;
    h5{
        margin-bottom: 0;
    }
}
.rank-list{
    padding: 8px 0;
    /deep/.box{
        margin-bottom: 6px;
        &:last-child{
            margin-bottom: 0;
        }
    }
}
.line-echart{
    height: 360px;
}
.total{
    position: absolute;
    width: 24%;
    top: 30%;
    height: 39%;
    left: 18%;
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}
</style>
