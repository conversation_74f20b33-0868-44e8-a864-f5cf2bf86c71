export interface SearchObj {
  startTime?: string;
  endTime?: string;
  deviceCode?: any;
  queryType?: string;
  unitCode?: string;
  statisticsType?: number;
}
export interface DayStatisticsList {
  id: number;
  recordTime: string;
  o3: number;
  no2: number;
  so2: number;
  co: number;
  pm10: number;
  pm25: number;
  o3Enight: number;
  aqi: number;
  flowCount: number;
  creatorId?: any;
  createTime: string;
  modifyId?: any;
  modifyTime: string;
  remark?: any;
  alonePm10?: any;
  alonePm25?: any;
  alonePmFlowCount?: any;
  endReach: number;
  deviceCode?: any;
  statisticsType: number;
  airEnvironmentQualityStatisticsLs?: any;
}
export interface Statistics {
  recordTime: string;
  bqStatistics: BqStatistics;
  tqStatistics: TqStatistics;
}

interface TqStatistics {
  id?: any;
  recordTime: string;
  o3?: any;
  no2?: any;
  so2?: any;
  co?: any;
  pm10?: any;
  pm25?: any;
  o3Enight?: any;
  aqi?: any;
  flowCount?: any;
  creatorId?: any;
  createTime?: any;
  modifyId?: any;
  modifyTime?: any;
  remark?: any;
  alonePm10?: any;
  alonePm25?: any;
  alonePmFlowCount?: any;
  endReach?: any;
  deviceCode?: any;
  statisticsType?: any;
  airEnvironmentQualityStatisticsLs?: any;
}

interface BqStatistics {
  id: number;
  recordTime: string;
  o3: number;
  no2: number;
  so2: number;
  co: number;
  pm10: number;
  pm25: number;
  o3Enight: number;
  aqi: number;
  flowCount: number;
  creatorId?: any;
  createTime: string;
  modifyId?: any;
  modifyTime: string;
  remark?: any;
  alonePm10?: any;
  alonePm25?: any;
  alonePmFlowCount?: any;
  endReach: number;
  deviceCode?: any;
  statisticsType: number;
  airEnvironmentQualityStatisticsLs?: any;
}
export interface deviceCodeList {
  disabled?: boolean;
  id: number;
  parentId: number;
  nodeName: string;
  nodeCode: string;
  nodeType: number;
  index: number;
}
export interface DeviceInfo {
  expand: boolean;
  selected: boolean;
  checked: boolean;
  title: string;
  label: string;
  value: string;
  id: number;
  parentId: number;
  nodeName: string;
  nodeCode: string;
  nodeType: number;
  index: number;
  statisticsType?: number;
  children?: Child[];
  nodeKey: number;
}

interface Child {
  expand: boolean;
  selected: boolean;
  checked: boolean;
  title: string;
  label: string;
  value: string;
  statisticsType?: number;
  id: number;
  parentId: number;
  nodeName: string;
  nodeCode: string;
  nodeType: number;
  index: number;
  nodeKey: number;
}
export interface InitParams {
  statisticsType?: number,
  nodeCode?: string,
}
export interface Column {
  title: string;
  key?: string;
  fixed?: string;
  width: number;
  slot?: string;
}
export interface TreeData {
  expand: boolean;
  selected: boolean;
  title: string;
  label: string;
  value: string;
  statisticsType: number;
  id: number | null;
  parentId: number;
  nodeName: string;
  nodeCode: string;
  nodeType: number;
  index: number;
  children?: TreeData[];
  nodeKey: number;
}
export interface DateType {
  val:Date,
  origin: string
}