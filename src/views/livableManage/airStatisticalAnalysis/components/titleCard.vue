<script lang="ts" setup>
import {
    defineComponent,
    reactive,
    markRaw,
    toRefs,
    defineProps,
    onMounted,
    ref,
    computed,
} from "vue";
const props = defineProps({
    data: {
        type: Object,
        default: () => ({}),
    },
    originIndex: {
        default: 0,
    },
});

const src = computed(() => (trend: string | undefined) => {
    if (!trend) return "";
    const numberTrend: number = +trend.replace("%", "");
    if (numberTrend > 0) {
        return require("@/assets/images/上涨.png");
    } else {
        return require("@/assets/images/下降.png");
    }
});
const enumNameKey = ["lateHour", "tdQuality", "twQuality", "tmQuality", "tyQuality"];
const yEnumNameKey = ["ydLateHour", "ydQuality", "ywQuality", "ymQuality", "yyQuality"];
const title = computed(() => {
    return props.data[enumNameKey[props.originIndex]].name || "";
});
const comparisonResultColor = computed(() => (comparison: string) => {
    if (comparison === "达标") {
        return "#00B42A";
    } else if (comparison === "不达标") {
        return "#FF9A2E";
    } else {
        return "#1E2A55";
    }
});
const comparisonResult = computed(() => (label: string) => {
    const enumLabel = {
        now: enumNameKey,
        y: yEnumNameKey,
    };
    let comparison = props.data[enumLabel[label][props.originIndex]].reach;
    //需要转变成汉字的key
    let keys = "lateHour,tdQuality,ydQuality,ydLateHour";
    let key = props.data[enumLabel[label][props.originIndex]].key;
    if (comparison === 0 && keys.indexOf(key) > -1) {
        return "不达标";
    } else if (comparison === 1 && keys.indexOf(key) > -1) {
        return "达标";
    } else if (comparison === 0 || comparison) {
        return comparison + "天";
    } else {
        return "--";
    }
});
const comparisonName = computed(() => {
    if (props.originIndex === 0) return '昨日同期对比'
    return props.data[yEnumNameKey[props.originIndex]].name || "";
});
const trend = computed(() => {
    const data = props.data[yEnumNameKey[props.originIndex]].trend;
    if (data === 0 || data) {
        return data + "%";
    } else {
        return;
    }
});
const upDownColor = computed(() => {
    if (Number(trend.value?.replace("%", "")) > 0) {
        return '#F53F3F'
    } else {
        return '#00B42A'
    }
})
</script>
<template>
    <div class="container">
        <div class="card">
            <div class="title">
                {{ title }}
            </div>
            <div class="standard">
                {{ comparisonResult("now") }}
            </div>
            <div class="comparison">
                <!--      -->
                <span class="span"><tooltip-auto-show :content="comparisonName" /></span>
                <span class="span"><tooltip-auto-show :content="comparisonResult('y')" /></span>
                <span class="span" :style="{ color: upDownColor }"><tooltip-auto-show
                        :content="trend?.replace('-', '')" /></span>
                <img v-show="trend" :src="src(trend)" />
            </div>
        </div>
    </div>
</template>

<style lang="less" scoped>
.container {
    height: 100px;
    width: 100%;

    .card {
        padding: 8px 16px;
        width: 100%;
        height: 100px;
        display: flex;
        flex-direction: column;
        row-gap: 5px;
        background-color: #fff;
        border-radius: 4px;

        .title {

            white-space: nowrap;
            height: 22px;
            font-size: 14px;
            color: #1e2a55;
            line-height: 22px;
            font-weight: 600;
        }

        .standard {
            height: 30px;
            color: v-bind(comparisonResultColor(comparisonResult("now")));
            font-size: 20px;
            font-weight: 600;
            line-height: 30px;
        }

        .comparison {
            display: flex;
            width: 100%;
            height: 18px;
            line-height: 26px;
            column-gap: 8px;
            align-items: center;
            .span {
                font-size: 14px;
            }

            .span:nth-child(1) {
                color: #4e627e;
                overflow: hidden;
            }

            .span:nth-child(2) {
                font-weight: 600;
                max-width: 33px;
                text-overflow: ellipsis;
                white-space: nowrap;
                color: #4e627e;
            }

            .span:nth-child(3) {
                max-width: 50px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }

            img {
                width: 16px;
                height: 16px;
            }
        }
    }
}
</style>
