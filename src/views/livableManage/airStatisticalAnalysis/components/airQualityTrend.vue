<script lang="ts" setup>
import EchartItem from "@/components/common/EchartItem";
import { EchartsTooltip } from "@/utils/tool";
import { watch, defineProps, nextTick, toRefs, onBeforeMount, onMounted, ref, computed } from "vue";
import { Statistics } from "../type";
import { isNullOrEmpty } from "@/utils/tool";
const props = defineProps({
    option: {
        default: () => ({}),
    },
    isHour: {
        default: ''
    }
});
const options = ref<any>({});
const aqiCategory = (aqi: any) => {
    if (isNullOrEmpty(aqi)) return "--";
    if (aqi <= 50 && aqi >= 0) return "优";
    if (aqi <= 100 && aqi >= 51) return "良";
    if (aqi <= 150 && aqi >= 101) return "轻度污染";
    if (aqi <= 200 && aqi >= 151) return "中度污染";
    if (aqi <= 300 && aqi >= 201) return "重度污染";
    if (aqi > 300) return "严重污染";
};
const aqiGrade = (aqi: any) => {
    if (isNullOrEmpty(aqi)) return "--";
    if (aqi <= 50 && aqi >= 0) return "一级";
    if (aqi <= 100 && aqi >= 51) return "二级";
    if (aqi <= 150 && aqi >= 101) return "三级";
    if (aqi <= 200 && aqi >= 151) return "四级";
    if (aqi <= 300 && aqi >= 201) return "五级";
    if (aqi > 300) return "六级";
};
watch(
    () => props.option,
    (newVal: any, oldVal: any) => {
        let XData: string[] = [],
            bqYData: number[] = [],
            tqYData: number[] = [];
        newVal.forEach((item: Statistics) => {
            if (props.isHour === 'hour') {
                XData.push(item.recordTime);
            } else {
                XData.push(item.recordTime.split(' ')[0]);
            }
            bqYData.push(item.bqStatistics?.aqi);
            tqYData.push(item.tqStatistics?.aqi);
        });
        options.value = {
            title: {
                text: "空气质量趋势",
                textStyle: {
                    fontSize: 16,
                },
                subtext: "单位：m",
                subtextStyle: {
                    color: "#798799",
                },
            },
            tooltip: {
                trigger: "axis",
                backgroundColor: "rgba(255,255,255,0.7)",
                borderWidth: 0,
                formatter: (arg: any) => {
                    arg[0].value = `<div class="air-tooltip">
                            <div><span>空气质量指数</span><span>${arg[0].value || '--'}</span></div>
                            <div><span>空气质量指数等级</span><span>${aqiGrade(arg[0].value)}</span></div>
                            <div><span>空气质量指数类别</span><span>${aqiCategory(arg[0].value)}</span></div>
                            </div>`;
                    arg[1] ? arg[1].value = `<div class="air-tooltip">
                            <div><span>空气质量指数</span><span>${arg[1].value || '--'}</span></div>
                            <div><span>空气质量指数等级</span><span>${aqiGrade(arg[1].value)}</span></div>
                            <div><span>空气质量指数类别</span><span>${aqiCategory(arg[1].value)}</span></div>
                            </div>` : ''
                    return EchartsTooltip(arg, "", 300);
                },
            },
            legend: {
                data: ["本期", "同期"],
                icon: "circle",
                itemHeight: 8,
                itemWidth: 8,
            },
            grid: {
                left: "3%",
                right: "4%",
                bottom: "3%",
                containLabel: true,
            },
            xAxis: [
                {
                    type: "category",
                    boundaryGap: false,
                    data: XData,
                    axisLabel: {
                        textStyle: {
                            color: "#798799", // 设置 Y 轴标签颜色为蓝色
                        },
                    },
                    axisLine: {
                        show: true,
                        lineStyle: {
                            color: "#E0E6F1",
                        }
                    },
                },
            ],
            yAxis: [
                {
                    type: "value",
                    axisLine: {
                        show: true,
                        lineStyle: {
                            color: "#E0E6F1",
                        }
                    },
                    axisLabel: {
                        textStyle: {
                            color: "#798799", // 设置 Y 轴标签颜色为蓝色
                        },
                    },
                    splitLine: {
                        lineStyle: {
                            type: "dashed", //虚线

                        },
                        show: true, //隐藏
                    },
                },
            ],
            color: ["#21CCFF", "#313CA9"],
            series: [
                {
                    name: "本期",
                    type: "line",
                    smooth: true,
                    data: bqYData,
                },
                {
                    name: "同期",
                    type: "line",
                    smooth: true,
                    data: tqYData,
                },
            ],
        };
    },
    { deep: true }
);
</script>
<template>
    <div class="container" ref="container">
        <EchartItem :option="options"></EchartItem>
    </div>
</template>

<style lang="less" scoped>
.container {
    width: 100%;
    height: 296px;
    padding: 12px 12px 20px;
}
</style>
