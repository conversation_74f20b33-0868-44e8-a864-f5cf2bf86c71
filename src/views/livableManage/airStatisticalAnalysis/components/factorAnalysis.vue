<script lang="ts" setup>
import EchartItem from "@/components/common/EchartItem";
import { EchartsTooltip } from "@/utils/tool";
import { watch, defineProps, nextTick, toRefs, defineEmits, onMounted, ref, computed } from "vue";
import { DayStatisticsList } from "../type";
const emit: any = defineEmits();
const options = ref<any>({});
const props = defineProps({
    option: {
        default: () => ({}),
    },
    isHour: {
        default: "",
    },
    selectedType: {
        default: ''
    }
});
watch(
    () => props.option,
    (newVal: any, oldVal: any) => {
        let XData: string[] = [],
            YDataPM25: number[] = [],
            YDataPM10: number[] = [],
            YDataCO: number[] = [],
            YDataSO2: number[] = [],
            YDataNO2: number[] = [],
            YDataO3: number[] = [];
        (newVal["dayStatisticsList"] || newVal["hourStatisticsList"]).forEach(
            (item: DayStatisticsList) => {
                if (props.isHour === 'day') {
                    XData.push(item.recordTime.split(' ')[0]);
                } else {
                    XData.push(item.recordTime);
                }
                YDataPM25.push(item.pm25);
                YDataPM10.push(item.pm10);
                YDataCO.push(item.co);
                YDataSO2.push(item.so2);
                YDataNO2.push(item.no2);
                YDataO3.push(item.o3);
            }
        );
        options.value = {
            title: {
                text: "因子浓度分析",
                textStyle: {
                    fontSize: 16,
                },
            },
            tooltip: {
                trigger: "axis",
                backgroundColor: "rgba(255,255,255,0.7)",
                borderWidth: 0,
                formatter: (arg: any) => {
                    return EchartsTooltip(arg, [
                        "μg/m³",
                        "μg/m³",
                        "mg/m³",
                        "μg/m³",
                        "μg/m³",
                        "μg/m³",
                    ]);
                },
            },
            legend: {
                data: ["CO", "SO2", "NO2", "O3", "PM2.5", "PM10"],
                icon: "rect",
                itemHeight: 2,
                itemWidth: 12,
            },

            grid: {
                left: "3%",
                right: "4%",
                bottom: "3%",
                containLabel: true,
            },
            xAxis: [
                {
                    type: "category",
                    boundaryGap: false,
                    data: XData,
                    axisLabel: {
                        textStyle: {
                            color: "#798799", // 设置 Y 轴标签颜色为蓝色
                        },
                    },
                    axisLine: {
                        show: true,
                        lineStyle: {
                            color: "#E0E6F1",
                        }
                    },
                },
            ],
            yAxis: [
                {
                    type: "value",
                    axisLine: {
                        show: true,
                        lineStyle: {
                            color: "#E0E6F1",
                        }
                    },
                    axisLabel: {
                        textStyle: {
                            color: "#798799", // 设置 Y 轴标签颜色为蓝色
                        },
                    },
                    splitLine: {
                        lineStyle: {
                            type: "dashed", //虚线

                        },
                        show: true, //隐藏
                    },
                },
            ],
            color: ["#21CCFF", "#05D0C4", "#18D865", "#F7BA1E", "#FAAC7B", "#FF708B"],
            series: [
                {
                    name: "CO",
                    type: "line",
                    smooth: true,
                    data: YDataCO,
                },
                {
                    name: "SO2",
                    type: "line",
                    smooth: true,
                    data: YDataSO2,
                },
                {
                    name: "NO2",
                    type: "line",
                    smooth: true,
                    data: YDataNO2,
                },
                {
                    name: "O3",
                    type: "line",
                    smooth: true,
                    data: YDataO3,
                },
                {
                    name: "PM2.5",
                    type: "line",
                    smooth: true,
                    data: YDataPM25,
                },
                {
                    name: "PM10",
                    type: "line",
                    smooth: true,
                    data: YDataPM10,
                },
            ],
        };
        if (props.selectedType === 'PM2.5/PM10传感器' || props.selectedType.includes('PM2.5') || props.selectedType.includes('PM10')) {
            options.value.series.splice(0, 4);
        }
        // else {
        //     if (props.isHour === "hour") {
        //         options.value.series.splice(-2);
        //     }
        // }
    },
    { deep: true }
);
const listenersEvent = (myChart: any) => {
    myChart.on("legendselectchanged", function (params: any) {
        const selected: string[] = [];
        for (let key in params.selected) {
            if (params.selected[key]) {
                selected.push(key);
            }
        }
        emit("on-legendselectchange", selected);
    });
};
</script>
<template>
    <div class="container" ref="container">
        <EchartItem @initEchart="listenersEvent" :option="options"></EchartItem>
    </div>
</template>

<style lang="less" scoped>
.container {
    padding: 12px 12px 20px;
    width: 100%;
    height: 296px;

}
</style>
