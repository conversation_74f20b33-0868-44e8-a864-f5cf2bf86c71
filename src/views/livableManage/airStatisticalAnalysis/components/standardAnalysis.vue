<script lang="ts" setup>
import EchartItem from "@/components/common/EchartItem";
import { EchartsTooltip,bus } from "@/utils/tool";
import { defineEmits, reactive, markRaw, toRefs, defineProps, watch, ref, computed } from "vue";
const props = defineProps({
    option: {
        default: () => ({}),
    },
});
const emit: any = defineEmits(['on-change']);
const options = ref<any>();
watch(
    () => props.option,
    (newVal: any, oldVal) => {
        const XData = newVal.reachVoList.map((item: any) => item.recordTime);
        const yData = newVal.reachVoList.map((item: any) => item.bqReachNum);
        options.value = {
            tooltip: {
                trigger: "axis",
                backgroundColor: "rgba(255,255,255,0.7)",
                borderWidth: 0,
                formatter: (arg: any) => {
                    return EchartsTooltip(arg, "天");
                },
            },
            xAxis: {
                type: "category",
                data: XData,
                axisTick: {
                    show: true, //决定刻度的显示
                    alignWithLabel: true, //将类目轴刻度与图像对其
                },
                axisLabel: {
                    textStyle: {
                        color: "#798799", // 设置 Y 轴标签颜色为蓝色
                    },
                },
                axisLine: {
                    show: true,
                    lineStyle: {
                        color: "#E0E6F1",
                    },
                },
            },
            grid: {
                left: "2%",
                right: "4%",
                top: "6%",
                bottom: "3%",
                containLabel: true,
            },
            yAxis: {
                type: "value",
                axisLine: {
                    show: true,
                    lineStyle: {
                        color: "#E0E6F1",
                    },
                },
                axisLabel: {
                    textStyle: {
                        color: "#798799", // 设置 Y 轴标签颜色为蓝色
                    },
                },
                splitLine: {
                    lineStyle: {
                        type: "dashed", //虚线
                    },
                    show: true, //隐藏
                },
            },
            series: [
                {
                    data: yData,
                    type: "line",
                    name: "达标数",
                    smooth: true,
                    areaStyle: {
                        color: {
                            type: "linear",
                            x: 0,
                            y: 0,
                            x2: 0,
                            y2: 1,
                            colorStops: [
                                {
                                    offset: 0,
                                    color: "rgba(17, 126, 255, 0.16)",
                                },
                                {
                                    offset: 1,
                                    color: "rgba(17, 128, 255, 0)",
                                },
                            ],
                            global: false,
                        },
                    },
                },
            ],
        };
    }
);
const activeTitle = ref<string>("日");
const enumKey = {
    年: "year",
    月: "month",
    日: "day",
};
const handleClick = (item: string) => {
    activeTitle.value = item;
    bus.emit('on-date-type-change', enumKey[item])
    emit("on-change", enumKey[item]);
};
</script>
<template>
    <div class="container">
        <div class="title-card">
            <div class="title">达标分析</div>
            <div class="year-month-day">
                <div
                    :class="(activeTitle === item && 'active') || ''"
                    @click="handleClick(item)"
                    v-for="item in ['年', '月', '日']"
                    :key="item"
                >
                    {{ item }}
                </div>
            </div>
        </div>
        <div class="echarts">
            <EchartItem :option="options"></EchartItem>
        </div>
    </div>
</template>

<style lang="less" scoped>
.container {
    padding: 12px 12px 20px;
    .title-card {
        height: 30px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        .title {
            font-weight: 600;
            color: #1e2a55;
            font-size: 16px;
        }
        .year-month-day {
            display: flex;
            background-color: #f3f7fb;
            align-items: center;
            width: 120px;
            height: 28px;
            cursor: pointer;
            div {
                flex: 1;
                margin: 0 2px;
                height: 22px;
                line-height: 22px;
                text-align: center;
                font-size: 12px;
                color: #4e627e;
            }
            .active {
                color: @primary-color;
                background-color: #fff;
            }
        }
    }
    .echarts {
        width: 100%;
        height: 257px;
    }
}
</style>
