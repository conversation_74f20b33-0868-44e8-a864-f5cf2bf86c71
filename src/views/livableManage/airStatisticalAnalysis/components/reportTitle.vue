<script lang="ts" setup>
import {
    defineProps,
    nextTick,
    markRaw,
    watch,
    defineEmits,
    defineExpose,
    ref,
    computed,
} from "vue";
import { SearchObj } from "../type";
import { enumSlot, airQualityTableList } from "../data";
import { isNullOrEmpty } from "@/utils/tool";
const emit: any = defineEmits(['on-export']);
const props = defineProps({
    tableList: {
        default: () => ({}),
    },
    url: {
        default: "",
    },
    origin: {
        default: "",
    },
    date: {
        type: Object,
        default: () => ({}),
    },
    showPage: {
        default: true,
    },
    isHour: {
        default: "",
    },
    titleString: {
        default: "",
    },
    selectedType: {
        default: ''
    }
});
const listCom = ref();
const getList = (searchObj: SearchObj) => {
    listCom.value.search(searchObj);
};
const listData = ref<any>([]);
const computedEnumSlot = computed(()=>{
    if (props.selectedType.includes('PM')) {
       return enumSlot.slice(0, 16)
    } else {
        return enumSlot
    }
})
const handleTableData = (data: any) => {
    listData.value = data.records || data;
};

defineExpose({ getList, listData });
const src = computed(() => (trend: number) => {
    if (trend > 0) {
        return require("@/assets/images/上涨.png");
    } else {
        return require("@/assets/images/下降.png");
    }
});
const rangeVoListIndex = computed(() => (item: string) => {
    const isPM25PM10 = props.selectedType.includes('PM')
    if (item.indexOf("CO") !== -1) return 0;
    if (item.indexOf("NO2") !== -1) return 1;
    if (item.indexOf("SO2") !== -1) return 2;
    if (item.indexOf("O3") !== -1) return 3;
    if (item.indexOf("PM25") !== -1) return isPM25PM10 ? 0 : 4; //判断选择PM2.5PM10时动态返回下标
    if (item.indexOf("PM10") !== -1) return isPM25PM10 ? 1 : 5;
}); //判断选择PM2.5PM10时动态返回下标
const replaceItemMim = computed(() => (item: string) => {
    if (item.indexOf("CO") !== -1) return item.replace("CO", "min");
    if (item.indexOf("NO2") !== -1) return item.replace("NO2", "min");
    if (item.indexOf("SO2") !== -1) return item.replace("SO2", "min");
    if (item.indexOf("O3") !== -1) return item.replace("O3", "min");
    if (item.indexOf("PM25") !== -1) return item.replace("PM25", "min");
    if (item.indexOf("PM10") !== -1) return item.replace("PM10", "min");
});
const replaceItemMax = computed(() => (item: string) => {
    if (item.indexOf("CO") !== -1) return item.replace("CO", "max");
    if (item.indexOf("NO2") !== -1) return item.replace("NO2", "max");
    if (item.indexOf("SO2") !== -1) return item.replace("SO2", "max");
    if (item.indexOf("O3") !== -1) return item.replace("O3", "max");
    if (item.indexOf("PM25") !== -1) return item.replace("PM25", "max");
    if (item.indexOf("PM10") !== -1) return item.replace("PM10", "max");
});
const replaceItemProp = computed(() => (item: string) => {
    const toLowerCaseFirst = (str: string) => {
        return str.charAt(0).toLowerCase() + str.slice(1);
    };
    if (item.indexOf("CO") !== -1) return toLowerCaseFirst(item.replace("CO", ""));
    if (item.indexOf("NO2") !== -1) return toLowerCaseFirst(item.replace("NO2", ""));
    if (item.indexOf("SO2") !== -1) return toLowerCaseFirst(item.replace("SO2", ""));
    if (item.indexOf("O3") !== -1) return toLowerCaseFirst(item.replace("O3", ""));
    if (item.indexOf("PM25") !== -1) return toLowerCaseFirst(item.replace("PM25", ""));
    if (item.indexOf("PM10") !== -1) return toLowerCaseFirst(item.replace("PM10", ""));
});
const handleData = (row: any, item: any) => {
    const index = rangeVoListIndex.value(item) as number;
    const replaceItemPropKey = replaceItemProp.value(item) as string;
    const replaceItemMimKey = replaceItemMim.value(item) as string;
    const replaceItemMaxKey = replaceItemMax.value(item) as string;

    if (!isNullOrEmpty(row.propDetailVoList)) {
        const value = row.propDetailVoList[index][replaceItemPropKey];
        return value && value > 0 ? value : "--";
    }
    const hasPropDetailVoListKey = Object.keys(row).includes('propDetailVoList')
    if (hasPropDetailVoListKey && isNullOrEmpty(row.propDetailVoList)) return '--'
    const maxPropExceed = row.rangeVoList[index][replaceItemMaxKey];
    const minPropExceed = row.rangeVoList[index][replaceItemMimKey];
    if (/PropExceed$/.test(item)) {
        if (isNullOrEmpty(maxPropExceed) || maxPropExceed < 0) return "--";
        if (!isNullOrEmpty(minPropExceed) && minPropExceed < 0) return maxPropExceed;
        return `【${minPropExceed} ,  ${maxPropExceed}】`;
    }
    if (/Prop$/.test(item)) {
        if (!isNullOrEmpty(minPropExceed)) {
            return `【${minPropExceed} ,  ${maxPropExceed}】`;
        } else {
            return "--";
        }
    }
};
const isShowTag = (row: any, item: string, type: string) => {
    const index = rangeVoListIndex.value(item) as number;
    const replaceItemMimKey = replaceItemMim.value(item) as string;
    const replaceItemMaxKey = replaceItemMax.value(item) as string;
    if (!isNullOrEmpty(row.propDetailVoList)) {
        const replaceItemPropKey = replaceItemProp.value(item) as string;
        const value = row.propDetailVoList[index][replaceItemPropKey];
        if (!isNullOrEmpty(value) && value > 0) {
            return true;
        } else {
            return false;
        }
    }
    if (!isNullOrEmpty(row.rangeVoList)) {
        const maxPropExceed = row.rangeVoList[index][replaceItemMaxKey];
        const minPropExceed = row.rangeVoList[index][replaceItemMimKey];
        if (/PropExceed$/.test(item)) {
            if (isNullOrEmpty(maxPropExceed) || maxPropExceed < 0) return false;
            if (!isNullOrEmpty(minPropExceed) && minPropExceed < 0) return true;
            return true;
        }
        if (/Prop$/.test(item)) {
            if (!isNullOrEmpty(minPropExceed)) {
                return true;
            } else {
                return false;
            }
        }
    }
};
const isReachColor = computed(() => (row: any, item: string, type: string) => {
    const colorObj = {
        background: ["#E8FFEA", "#FFF7E8"], //index 0为达标，1为不达标
        color: ["#00b42a", "#ff7d00"], //index 0为达标，1为不达标
    };
    const index = rangeVoListIndex.value(item) as number;
    const replaceItemPropKey = replaceItemProp.value(item);
    const replaceItemMimKey = replaceItemMim.value(item) as string;
    const replaceItemMaxKey = replaceItemMax.value(item) as string;
    // 单个设备浓度表
    if (!isNullOrEmpty(row.propDetailVoList)) {
        if (/BqProp/.test(item)) {
            const isReach =
                !isNullOrEmpty(row.propDetailVoList[index].bqPropExceed) &&
                row.propDetailVoList[index].bqPropExceed > 0;
            return isReach ? colorObj[type][1] : colorObj[type][0];
        }
        if (/TqProp/.test(item)) {
            const isReach =
                !isNullOrEmpty(row.propDetailVoList[index].tqPropExceed) &&
                row.propDetailVoList[index].tqPropExceed > 0;
            return isReach ? colorObj[type][1] : colorObj[type][0];
        }
        if (/SqProp/.test(item)) {
            const isReach =
                !isNullOrEmpty(row.propDetailVoList[index].sqPropExceed) &&
                row.propDetailVoList[index].sqPropExceed >= 0;
            return isReach ? colorObj[type][1] : colorObj[type][0];
        }
        return;
    }
    // 区间表
    if (/(B|S|T)qProp$/.test(item)) {
        if (!isNullOrEmpty(row.rangeVoList[index][replaceItemMimKey])) {
            const isReach =
                row.rangeVoList[index][replaceItemMimKey] > 0 &&
                !isNullOrEmpty(row.rangeVoList[index][replaceItemMimKey]);
            return (isReach && colorObj[type][1]) || colorObj[type][0];
        }
        return colorObj[type][1];
    }

    if (/(B|S|T)qPropExceed$/.test(item)) {
        if (!isNullOrEmpty(row.rangeVoList[index][replaceItemMaxKey])) {
            const isReach =
                row.rangeVoList[index][replaceItemMaxKey] > 0 &&
                !isNullOrEmpty(row.rangeVoList[index][replaceItemMaxKey]);
            return (isReach && colorObj[type][1]) || colorObj[type][0];
        }
        return colorObj[type][1];
    }
});
const showImg = (row: any, key: string, item: any) => {
    const index = rangeVoListIndex.value(item) as number;
    const voList = row["propDetailVoList"] || row["rangeVoList"];
    if (isNullOrEmpty(voList)) {
        return false;
    }
    if (isNullOrEmpty(voList[index][key])) {
        return false;
    } else {
        return true;
    }
};
const src2 = (row: any, item: any, key: string) => {
    const index = rangeVoListIndex.value(item) as number;
    if ((row["propDetailVoList"] || row["rangeVoList"])[index][key] > 0) {
        return require("@/assets/images/上涨.png");
    } else {
        return require("@/assets/images/下降.png");
    }
};
// 所属区间
const interval = (row: any, label: string) => {
    const enumKey = {
        bq: "bqStatisticsDto",
        tq: "tqStatisticsDto",
        sq: "sqStatisticsDto",
    };
    if (isNullOrEmpty(row[enumKey[label]])) return "--";
    if (row[enumKey[label]].aqi <= 50 && row[enumKey[label]].aqi >= 0) return "【0,50】";
    if (row[enumKey[label]].aqi <= 100 && row[enumKey[label]].aqi >= 51) return "【51,100】";
    if (row[enumKey[label]].aqi <= 150 && row[enumKey[label]].aqi >= 101) return "【101,150】";
    if (row[enumKey[label]].aqi <= 200 && row[enumKey[label]].aqi >= 151) return "【151,200】";
    if (row[enumKey[label]].aqi <= 300 && row[enumKey[label]].aqi >= 201) return "【201,300】";
    if (row[enumKey[label]].aqi > 300) return ">300";
};
const aqiGrade = (row: any, label: string) => {
    const enumKey = {
        bq: "bqStatisticsDto",
        tq: "tqStatisticsDto",
        sq: "sqStatisticsDto",
    };
    if (isNullOrEmpty(row[enumKey[label]])) return "--";
    if (row[enumKey[label]].aqi <= 50 && row[enumKey[label]].aqi >= 0) return "一级";
    if (row[enumKey[label]].aqi <= 100 && row[enumKey[label]].aqi >= 51) return "二级";
    if (row[enumKey[label]].aqi <= 150 && row[enumKey[label]].aqi >= 101) return "三级";
    if (row[enumKey[label]].aqi <= 200 && row[enumKey[label]].aqi >= 151) return "四级";
    if (row[enumKey[label]].aqi <= 300 && row[enumKey[label]].aqi >= 201) return "五级";
    if (row[enumKey[label]].aqi > 300) return "六级";
};
// let categoryFlag = false;
// const colorEnum = (row: any, label: string) => {
//     const enumKey = {
//         bq: "bqStatisticsDto",
//         tq: "tqStatisticsDto",
//         sq: "sqStatisticsDto",
//     };
//     nextTick(() => {
//         if (!categoryFlag) {
//             categoryFlag = true;
//             const domList = document.querySelectorAll("." + label);
//             domList.forEach((item: any) => {
//                 let attr = item.getAttribute("data-category");
//                 if (!attr) return;
//                 if (attr <= 50 && attr >= 0) {
//                     item.parentElement.parentElement.parentElement.style.backgroundColor =
//                         "#00FF00";
//                     item.parentElement.parentElement.parentElement.style.color = "#fff";
//                 }
//                 if (attr <= 100 && attr >= 51) {
//                     item.parentElement.parentElement.parentElement.style.backgroundColor =
//                         "#FFFF00";
//                     item.parentElement.parentElement.parentElement.style.color = "#fff";
//                 }
//                 if (attr <= 150 && attr >= 101) {
//                     item.parentElement.parentElement.parentElement.style.backgroundColor =
//                         "#FFA500";
//                     item.parentElement.parentElement.parentElement.style.color = "#fff";
//                 }
//                 if (attr <= 200 && attr >= 151) {
//                     item.parentElement.parentElement.parentElement.style.backgroundColor =
//                         "#FF0000";
//                     item.parentElement.parentElement.parentElement.style.color = "#fff";
//                 }
//                 if (attr <= 300 && attr >= 201) {
//                     item.parentElement.parentElement.parentElement.style.backgroundColor =
//                         "#800080";
//                     item.parentElement.parentElement.parentElement.style.color = "#fff";
//                 }
//                 if (attr > 300) {
//                     item.parentElement.parentElement.parentElement.style.backgroundColor =
//                         "#8B0000";
//                     item.parentElement.parentElement.parentElement.style.color = "#fff";
//                 }
//             });
//             categoryFlag = false;
//         }
//     });
//     return row[enumKey[label]]?.aqi || "";
//     // if (isNullOrEmpty(row[enumKey[label]])) return "--";

//     // if (row[enumKey[label]].aqi <= 50 && row[enumKey[label]].aqi >= 0) return "#00FF00";
//     // if (row[enumKey[label]].aqi <= 100 && row[enumKey[label]].aqi >= 51) return "#FFFF00";
//     // if (row[enumKey[label]].aqi <= 150 && row[enumKey[label]].aqi >= 101) return "#FFA500";
//     // if (row[enumKey[label]].aqi <= 200 && row[enumKey[label]].aqi >= 151) return "#FF0000";
//     // if (row[enumKey[label]].aqi <= 300 && row[enumKey[label]].aqi >= 201) return "#800080";
//     // if (row[enumKey[label]].aqi > 300) return "#8B0000";
// };
const aqiCategory = (row: any, label: string) => {
    const enumKey = {
        bq: "bqStatisticsDto",
        tq: "tqStatisticsDto",
        sq: "sqStatisticsDto",
    };
    if (isNullOrEmpty(row[enumKey[label]])) return "--";
    if (row[enumKey[label]].aqi <= 50 && row[enumKey[label]].aqi >= 0) return "优";
    if (row[enumKey[label]].aqi <= 100 && row[enumKey[label]].aqi >= 51) return "良";
    if (row[enumKey[label]].aqi <= 150 && row[enumKey[label]].aqi >= 101) return "轻度污染";
    if (row[enumKey[label]].aqi <= 200 && row[enumKey[label]].aqi >= 151) return "中度污染";
    if (row[enumKey[label]].aqi <= 300 && row[enumKey[label]].aqi >= 201) return "重度污染";
    if (row[enumKey[label]].aqi > 300) return "严重污染";
};
interface PollutantItem {
    pollutantName: string;
    pollutantNumber: number;
}

const aqiPrimaryPollutant = (row: any, label: string) => {
    const enumKey = {
        bq: "bqTopPollutePropKey",
        tq: "tqTopPollutePropKey",
        sq: "sqTopPollutePropKey",
    };
    if (isNullOrEmpty(row[enumKey[label]])) return "--";
    const enumPollutantName = {
        co: "CO",
        no2: "NO2",
        so2: "SO2",
        pm_25: "PM2.5",
        pm_10: "PM10",
        o3: "O3",
    };
    return row[enumKey[label]]
        .split(",")
        .map((item: string) => {
            return enumPollutantName[item];
        })
        .join();
    // if (row[enumKey[label]].aqi <= 50) return "--";
    // const pollutantKeyArr = ["co", "no2", "so2", "pm10", "pm25", "o3"];

    // let pollutantArr: PollutantItem[] = [];
    // pollutantKeyArr.forEach((pollutant: string) => {
    //     if (pollutant === "no2") {
    //         pollutantArr.push({
    //             pollutantName: enumPollutantName[pollutant],
    //             pollutantNumber: row[enumKey[label]][pollutant],
    //         });
    //     } else {
    //         pollutantArr.push({
    //             pollutantName: enumPollutantName[pollutant],
    //             pollutantNumber: row[enumKey[label]][pollutant],
    //         });
    //     }
    // });
    // pollutantArr = pollutantArr.sort(
    //     (a: PollutantItem, b: PollutantItem) => b.pollutantNumber - a.pollutantNumber
    // );
    // const samePollutantArr: PollutantItem[] = pollutantArr.filter(
    //     (item: PollutantItem) =>
    //         item.pollutantNumber && item.pollutantNumber === pollutantArr[0].pollutantNumber
    // ); //相同的污染物
    // let pollutantNameStr: string = "";
    // samePollutantArr.forEach((item: any, index: number) => {
    //     if (index === 0) {
    //         pollutantNameStr += item.pollutantName;
    //     } else {
    //         pollutantNameStr += "," + item.pollutantName;
    //     }
    // });
    // return pollutantNameStr;
};
const aqiExceedPollutant = (row: any, label: string) => {
    const enumKey = {
        bq: "bqExceedsPollutePropKeys",
        tq: "tqExceedsPollutePropKeys",
        sq: "sqExceedsPollutePropKeys",
    };
    if (isNullOrEmpty(row[enumKey[label]])) return "--";
    const enumPollutantName = {
        co: "CO",
        no2: "NO2",
        so2: "SO2",
        pm_25: "PM2.5",
        pm_10: "PM10",
        o3: "O3",
    };
    return row[enumKey[label]]
        .split(",")
        .map((item: string) => {
            return enumPollutantName[item];
        })
        .join();
    // let pollutantArr: PollutantItem[] = [];
    // pollutantKeyArr.forEach((pollutant: string) => {
    //     if (pollutant === "no2") {
    //         pollutantArr.push({
    //             pollutantName: enumPollutantName[pollutant],
    //             pollutantNumber: row[enumKey[label]][pollutant],
    //         });
    //     } else {
    //         pollutantArr.push({
    //             pollutantName: enumPollutantName[pollutant],
    //             pollutantNumber: row[enumKey[label]][pollutant],
    //         });
    //     }
    // });
    // pollutantArr = pollutantArr.sort(
    //     (a: PollutantItem, b: PollutantItem) => b.pollutantNumber - a.pollutantNumber
    // );
    // let pollutantNameStr: string = "--";
    // if (!isNullOrEmpty(pollutantArr)) {
    //     pollutantNameStr =
    //         pollutantArr
    //             .filter((item: PollutantItem) => item.pollutantNumber && item.pollutantNumber > 100)
    //             .map((i: PollutantItem) => i.pollutantName)
    //             .join() || "--";
    // } else {
    //     return "--";
    // }
    // return pollutantNameStr;
};
// let flag = false;
// const handleProp = computed(() => (row: any, slotName: string) => {
//     const isTrue: boolean = /(T|B|S)qProp$/.test(slotName);
//     if (isTrue) {
//         nextTick(() => {
//             if (!flag) {
//                 flag = true;
//                 const domList = document.querySelectorAll(".prop-value");
//                 domList.forEach((item: any) => {
//                     let attr = item.getAttribute("data-prop");
//                     if (!attr) return;
//                     if (attr > 0 && attr < 1) {
//                         item.parentElement.parentElement.parentElement.style.backgroundColor =
//                             "#ff7d00";
//                         item.parentElement.parentElement.parentElement.style.color = "#fff";
//                     } else if (attr > 1) {
//                         item.parentElement.parentElement.parentElement.style.backgroundColor =
//                             "#00B42A";
//                         item.parentElement.parentElement.parentElement.style.color = "#fff";
//                     }
//                 });
//                 flag = false;
//             }
//         });
//         const index = rangeVoListIndex.value(slotName);
//         const replaceItemPropKey = replaceItemProp.value(slotName);
//         if (!isNullOrEmpty(row.propDetailVoList))
//             return row.propDetailVoList[index][replaceItemPropKey];
//         if (!isNullOrEmpty(row.rangeVoList)) return row.rangeVoList[index][replaceItemPropKey];
//     }
// });
const nullOrEmpty = (item: any) => {
    if (!isNullOrEmpty(item)) {
        if (item % 1 === 0) return Math.abs(item) + "%";
        return Math.abs(item).toFixed(2) + "%";
    } else {
        return "--";
    }
};

const statisticsDtoAqi = (row: any, origin: string) => {
    if (origin === "bq") {
        if (!isNullOrEmpty(row.bqStatisticsDto)) {
            return row.bqStatisticsDto.aqi;
        } else {
            return "--";
        }
    }
    if (origin === "tq") {
        if (!isNullOrEmpty(row.tqStatisticsDto)) {
            return row.tqStatisticsDto.aqi;
        } else {
            return "--";
        }
    }
    if (origin === "sq") {
        if (!isNullOrEmpty(row.sqStatisticsDto)) {
            return row.sqStatisticsDto.aqi;
        } else {
            return "--";
        }
    }
};
const aqiColor = (row: any, origin: string, type: string) => {
    const enumKey = {
        bq: "bqStatisticsDto",
        tq: "tqStatisticsDto",
        sq: "sqStatisticsDto",
    };
    const colorObj = {
        background: ["#E8FFEA", "#FFF7E8"], //index 0为达标，1为不达标
        color: ["#00b42a", "#ff7d00"], //index 0为达标，1为不达标
    };
    if (!isNullOrEmpty(row[enumKey[origin]])) {
        const aqi = row[enumKey[origin]].aqi;
        if (aqi < 100) return colorObj[type][0];
        if (aqi >= 100) return colorObj[type][1];
    }
    return;
};
const reachNum = (item: number) => {
    if (item || item === 0) {
        return item + "天";
    } else {
        return "--";
    }
};
const handleDate = (date: any, label: string) => {
    const enumKey = {
        bqStart: "bqStartTime",
        bqEnd: "bqEndTime",
        sqStart: "sqStartTime",
        sqEnd: "sqEndTime",
        tqStart: "tqStartTime",
        tqEnd: "tqEndTime",
    };
    if (!isNullOrEmpty(date[enumKey[label]])) {
        return date[enumKey[label]].split(" ")[0];
    } else {
        return "";
    }
};
const handleTime = computed(() => (row: any) => {
    if (props.isHour === "hour") {
        return row.recordTime || "--";
    } else {
        return row.recordTime?.split(" ")[0] || "--";
    }
});
const handleExport = () => {
    emit("on-export", props.origin);
};
const headText = computed(() => {
    if (isNullOrEmpty(listData.value)) return "";
    const upDown = (reachRate: number | string) => {
        const reachRateType = typeof reachRate === "string";
        if (!reachRateType) {
            if (reachRate >= 0) {
                return "上升";
            } else if (reachRate < 0) {
                return "下降";
            }
        } else {
            return "上升";
        }
    };
    const toFixed2 = (rateComp: number | string) => {
        const reachRateType = typeof rateComp === "string";
        if (reachRateType) return "--";
        if (rateComp || rateComp === 0) {
            if (!isNaN(rateComp) && rateComp % 1 !== 0) return Math.abs(rateComp).toFixed(2) + "%";
            return Math.abs(rateComp) + "%";
        } else {
            return "--";
        }
    };
    if (props.origin === "达标统计表" || props.origin === "明细表" || props.origin === "汇总表") {
        if (!isNullOrEmpty(props.date)) {
            const objData: any = {};
            for (let key in props.date) {
                if (isNullOrEmpty(props.date[key])) {
                    objData[key] = "--";
                } else {
                    objData[key] = props.date[key];
                }
            }
            return `本期达标天数${objData.bqEndReach}天，同期达标天数${objData.tqEndReach
                }天，达标天数对比${upDown(objData.tqRateComp)}${toFixed2(
                    objData.tqRateComp
                )}，上期达标天数${objData.sqEndReach}天，达标天数对比${upDown(
                    objData.sqRateComp
                )}${toFixed2(objData.sqRateComp)}`;
        }
    }
    // if (props.origin === "汇总表") {
    //     if (listData.value[6]?.propKey === "综合结果") {
    //         const objData = listData.value[6];
    //         return `本期达标天数${objData.bqReachNum || "--"}天，同期达标天数${
    //             objData.tqReachNum || "--"
    //         }天，同期达标天数对比${upDown(objData.tqReachRateComp)}${toFixed2(
    //             objData.tqExceedRateComp
    //         )}，上期达标天数${objData.sqReachNum || "--"}天，上期达标天数对比${upDown(
    //             objData.sqReachRateComp
    //         )}${toFixed2(objData.sqReachRateComp)}`;
    //     }
    // }
    if (props.origin === "空气质量统计表") {
        return props.titleString;
    }
});
</script>
<template>
    <div class="top">
        <div class="date">
            <span>本期：{{ handleDate(date, "bqStart") + "~" + handleDate(date, "bqEnd") }}</span>
            <span>同期：{{ handleDate(date, "tqStart") + "~" + handleDate(date, "tqEnd") }}</span>
            <span>上期：{{ handleDate(date, "sqStart") + "~" + handleDate(date, "sqEnd") }}</span>
        </div>
        <div class="right">
            <Button type="primary" @click="handleExport">
                <i style="font-size: 11px" class="iconfont icon-Vector"></i>
                导出</Button>
            <div class="span">
                <span v-show="origin !== '达标统计表'">不达标</span>
                <span v-show="origin !== '达标统计表'">达标</span>
                <span>上涨</span>
                <span>下降</span>
            </div>
        </div>
    </div>
    <div class="middle">
        <Icon color="#165DFF" size="16" type="md-information-circle" />
        <span>{{ headText }}</span>
    </div>
    <base-table :show-page="showPage" @tableData="handleTableData" ref="listCom" :url="url" emptyBlockStr="--"
        :columns="tableList">
        <!-- 达标分析表 -->
        <template #bqReachNum="{ row }">
            {{ reachNum(row.bqReachNum) }}
        </template>
        <template #tqReachNum="{ row }">
            {{ reachNum(row.tqReachNum) }}
        </template>
        <template #sqReachNum="{ row }">
            {{ reachNum(row.sqReachNum) }}
        </template>
        <template #tqReachRate="{ row }">
            {{ nullOrEmpty(row.tqReachRate) }}
        </template>
        <template #tqRateComp="{ row }">
            <img v-if="row.tqRateComp" :src="src(row.tqRateComp)" alt="" />
            {{ nullOrEmpty(row.tqRateComp) }}
        </template>
        <template #sqRateComp="{ row }">
            <img v-if="row.sqRateComp" :src="src(row.sqRateComp)" alt="" />
            {{ nullOrEmpty(row.sqRateComp) }}
        </template>
        <!-- 空气质量分析 -->
        <template #recordTime="{ row }">
            {{ handleTime(row) }}
        </template>
        <template #bqAqi="{ row }">
            <s-tag :background="aqiColor(row, 'bq', 'background')" :color="aqiColor(row, 'bq', 'color')"
                v-show="statisticsDtoAqi(row, 'bq') !== '--'">{{ statisticsDtoAqi(row, "bq") }}</s-tag>
            <span v-show="statisticsDtoAqi(row, 'bq') === '--'">{{
                statisticsDtoAqi(row, "bq")
            }}</span>
        </template>
        <template #bqInterval="{ row }">
            {{ interval(row, "bq") }}
        </template>
        <template #bqGrade="{ row }">
            {{ aqiGrade(row, "bq") }}
        </template>
        <template #bqCategory="{ row, index }">
            <!-- :style="{ backgroundColor: colorEnum(row, 'bq'), color: '#fff' }" -->
            <!-- <div class="bq" :data-category="colorEnum(row, 'bq')"> -->
            {{ aqiCategory(row, "bq") }}
            <!-- </div> -->
        </template>
        <template #bqPrimaryPollutant="{ row }">
            <TooltipAutoShow>
                {{ aqiPrimaryPollutant(row, "bq") }}
            </TooltipAutoShow>
        </template>
        <template #bqExceedPollutant="{ row }">
            <TooltipAutoShow>
                {{ aqiExceedPollutant(row, "bq") }}
            </TooltipAutoShow>
        </template>

        <template #tqAqi="{ row }">
            <s-tag :background="aqiColor(row, 'tq', 'background')" :color="aqiColor(row, 'tq', 'color')"
                v-show="statisticsDtoAqi(row, 'tq') !== '--'">{{ statisticsDtoAqi(row, "tq") }}</s-tag>
            <span v-show="statisticsDtoAqi(row, 'tq') === '--'">{{
                statisticsDtoAqi(row, "tq")
            }}</span>
        </template>
        <template #tqInterval="{ row }">
            {{ interval(row, "tq") }}
        </template>
        <template #tqGrade="{ row }">
            {{ aqiGrade(row, "tq") }}
        </template>
        <template #tqCategory="{ row }">
            <!-- :style="{ backgroundColor: colorEnum(row, 'tq'), color: '#fff' }" -->
            <!-- <div class="tq" :data-category="colorEnum(row, 'tq')"> -->
            {{ aqiCategory(row, "tq") }}
            <!-- </div> -->
        </template>
        <template #tqPrimaryPollutant="{ row }">
            <TooltipAutoShow>
                {{ aqiPrimaryPollutant(row, "tq") }}
            </TooltipAutoShow>
        </template>
        <template #tqExceedPollutant="{ row }">
            <TooltipAutoShow>
                {{ aqiExceedPollutant(row, "tq") }}
            </TooltipAutoShow>
        </template>

        <template #sqAqi="{ row }">
            <s-tag :background="aqiColor(row, 'sq', 'background')" :color="aqiColor(row, 'sq', 'color')"
                v-show="statisticsDtoAqi(row, 'sq') !== '--'">{{ statisticsDtoAqi(row, "sq") }}</s-tag>
            <span v-show="statisticsDtoAqi(row, 'sq') === '--'">{{
                statisticsDtoAqi(row, "sq")
            }}</span>
        </template>
        <template #sqInterval="{ row }">
            {{ interval(row, "sq") }}
        </template>
        <template #sqGrade="{ row }">
            {{ aqiGrade(row, "sq") }}
        </template>
        <template #sqCategory="{ row, index }">
            <!-- :style="{ backgroundColor: colorEnum(row, 'tq'), color: '#fff' }" -->
            <!-- <div class="sq" :data-category="colorEnum(row, 'sq')"> -->
            {{ aqiCategory(row, "sq") }}
            <!-- </div> -->
        </template>
        <template #sqPrimaryPollutant="{ row }">
            <TooltipAutoShow>
                {{ aqiPrimaryPollutant(row, "sq") }}
            </TooltipAutoShow>
        </template>
        <template #sqExceedPollutant="{ row }">
            <TooltipAutoShow>
                {{ aqiExceedPollutant(row, "sq") }}
            </TooltipAutoShow>
        </template>

        <!-- 汇总表 -->
        <template #bqReachRate="{ row }">
            {{ nullOrEmpty(row.bqReachRate) }}
        </template>
        <template #tqReachRateComp="{ row }">
            <img v-if="row.tqReachRateComp" :src="src(row.tqReachRateComp)" alt="" />
            {{ nullOrEmpty(row.tqReachRateComp) }}
        </template>
        <template #sqReachRate="{ row }">
            {{ nullOrEmpty(row.sqReachRate) }}
        </template>
        <template #sqReachRateComp="{ row }">
            <img v-if="row.sqReachRateComp" :src="src(row.sqReachRateComp)" alt="" />
            {{ nullOrEmpty(row.sqReachRateComp) }}
        </template>
        <template #bqExceedRate="{ row }">
            {{ nullOrEmpty(row.bqExceedRate) }}
        </template>
        <template #tqExceedRateComp="{ row }">
            <img v-if="row.tqExceedRateComp" :src="src(row.tqExceedRateComp)" alt="" />
            {{ nullOrEmpty(row.tqExceedRateComp) }}
        </template>
        <template #sqExceedRateComp="{ row }">
            <img v-if="row.sqExceedRateComp" :src="src(row.sqExceedRateComp)" alt="" />
            {{ nullOrEmpty(row.sqExceedRateComp) }}
        </template>
        <!-- 明细表 -->
        <template v-for="(item, index) in computedEnumSlot" :key="index" #[item]="{ row }">
            <div v-if="/SqPropComp/.test(item)">
                <img v-show="showImg(row, 'sqPropComp', item)" :src="src2(row, item, 'sqPropComp')" alt="" />
                {{
                    nullOrEmpty(
                        (row["propDetailVoList"] || row["rangeVoList"])[rangeVoListIndex(item) as number][
                        "sqPropComp"
                        ]
                    )
                }}
            </div>
            <div v-else-if="/TqPropComp/.test(item)">
                <img v-show="showImg(row, 'tqPropComp', item)" :src="src2(row, item, 'tqPropComp')" alt="" />
                {{
                    nullOrEmpty(
                        (row["propDetailVoList"] || row["rangeVoList"])[rangeVoListIndex(item) as number][
                        "tqPropComp"
                        ]
                    )
                }}
            </div>
            <!-- class="prop-value" :data-prop="handleProp(row, item)" -->
            <div v-else>
                <s-tag v-show="isShowTag(row, item, 'color')" :background="isReachColor(row, item, 'background')"
                    :color="isReachColor(row, item, 'color')">{{ handleData(row, item) }}</s-tag>
                <span v-show="!isShowTag(row, item, 'color')">{{ handleData(row, item) }}</span>
            </div>
        </template>
    </base-table>
</template>

<style lang="less" scoped>
.top {
    display: flex;
    justify-content: space-between;
    height: 40px;
    align-items: flex-end;
    font-size: 12px;

    .date {
        display: flex;
        column-gap: 12px;
        color: #798799;
    }

    .right {
        position: relative;

        .ivu-btn {
            position: absolute;
            right: 0;
            top: -48px;
        }

        .span {
            display: flex;
            column-gap: 20px;
            color: #4e5969;

            span:nth-child(1) {
                position: relative;

                &::after {
                    content: "";
                    display: block;
                    position: absolute;
                    left: -12px;
                    top: 4px;
                    width: 9px;
                    height: 9px;
                    background-color: #ff7d00;
                }
            }

            span:nth-child(2) {
                position: relative;

                &::after {
                    content: "";
                    display: block;
                    position: absolute;
                    left: -12px;
                    top: 4px;
                    width: 9px;
                    height: 9px;
                    background-color: #00b42a;
                }
            }

            span:nth-child(3) {
                position: relative;

                &::after {
                    content: "";
                    display: block;
                    position: absolute;
                    left: -12px;
                    top: 3px;
                    width: 10px;
                    height: 11px;
                    background-image: url("../../../../assets/images/上涨.png");
                    background-size: cover;
                }
            }

            span:nth-child(4) {
                position: relative;

                &::after {
                    content: "";
                    display: block;
                    position: absolute;
                    left: -12px;
                    top: 3px;
                    width: 10px;
                    height: 11px;
                    background-image: url("../../../../assets/images/下降.png");
                    background-size: cover;
                }
            }
        }
    }
}

.middle {
    margin: 16px 0;
    height: 40px;
    border-radius: 2px;
    background-color: #e8f3ff;
    display: flex;
    align-items: center;
    padding: 12px;
    column-gap: 8px;
    color: #1d2129;
}

img {
    width: 16px;
    height: 16px;
}
</style>
