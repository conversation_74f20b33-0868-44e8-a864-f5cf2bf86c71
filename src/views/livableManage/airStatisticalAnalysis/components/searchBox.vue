<script lang="ts" setup>
import { watch, defineProps, defineEmits, ref, onUnmounted } from "vue";
import { InitParams, deviceCodeList, TreeData, DateType } from '../type'
import {
    airQualityDevice,
    airQualityDevice3
} from "@/api/livableManage/airQualityService.js";
import moment from "moment";
import { isNullOrEmpty, trace, bus } from "@/utils/tool";
import { getDate } from "wei-util";
const props = defineProps({
    showTab: {
        default: true,
    },
    tabIndex: {
        default: 0,
    },
});


const emit: any = defineEmits(['on-hour-day-change', 'on-date-change', 'on-device-change']);
const enumType = {
    0: "day",
    1: "day",
    2: "hour",
};
const datePickerTypeMap = {
    day: 'date',
    month: 'month',
    year: 'year'
}
const dateTimeMap = {
    day: [getDate({ day: -30, timeType: 'start' }, 'yyyy-MM-dd HH:mm:ss'), getDate({ timeType: 'end' }, 'yyyy-MM-dd HH:mm:ss')],
    month: [getDate({ month: -3, timeType: 'start' }, 'yyyy-MM-dd HH:mm:ss'), getDate({ timeType: 'end' }, 'yyyy-MM-dd HH:mm:ss')],
    year: [getDate({ year: -1, timeType: 'start' }, 'yyyy-MM-dd HH:mm:ss'), getDate({ timeType: 'end' }, 'yyyy-MM-dd HH:mm:ss')],
}
const activeTitle = ref<string>(enumType[props.tabIndex]);
const time = ref<string[]>([]);
time.value = dateTimeMap.day
const handleClick = (origin: string) => {
    activeTitle.value = origin;
    emit("on-hour-day-change", origin);
};

const handleDateChange = (date: DateType | string[], init?: InitParams | string) => {
    // if (isNullOrEmpty(date)) {
    //     time.value = dateTimeMap.day
    //     date = time.value
    // }
    if (!Array.isArray(date)) {
        const { val, origin } = date
        const timestamp = moment(val).unix()
        if (origin === 'start') {
            const endTimestamp = moment(time.value[1]).unix()
            if (timestamp > endTimestamp) {
                const time1 = time.value[1]
                time.value[1] = moment(val).endOf('day').format('YYYY-MM-DD HH:mm:ss')
                time.value[0] = time1
            } else {
                time.value[0] = moment(val).startOf('day').format('YYYY-MM-DD HH:mm:ss')
            }
        } else {
            const startTimestamp = moment(time.value[0]).unix()
            if (timestamp < startTimestamp) {
                const time0 = time.value[0]
                time.value[0] = moment(val).startOf('day').format('YYYY-MM-DD HH:mm:ss')
                time.value[1] = time0
            } else {
                time.value[1] = moment(val).endOf('day').format('YYYY-MM-DD HH:mm:ss')
            }
        }
        date = time.value
    }
    emit("on-date-change", date, init);
};
// 获取设备列表
const deviceList = ref<any[]>([]);
const getDeviceList = () => {
    // if (props.tabIndex === 2) {
    //     airQualityDevice3({}).then((res: any) => {
    //         if (res.success) {
    //             const data = res.data.records
    //             deviceList.value = data
    //         }
    //     });
    // } else
    //  {
    airQualityDevice({}).then((res: any) => {
        if (res.success) {
            const data = res.data
            deviceList.value = handleDeviceTree(data)
        }
    });
    // }
};
const handleDeviceTree = (data: deviceCodeList[]): TreeData[] => {
    let result: any[] = [];
    const enumType = {
        1: '类型',
        2: '设备'
    }
    data.forEach((item: deviceCodeList, index: number, arr: deviceCodeList[]) => {
        if (item.id) {
            const showText = `${enumType[item.nodeType]}(${item.nodeName})`
            const newItem = {
                expand: true,
                selected: item.nodeName === '多合一空气监测器' ? true : false,//默认已选多合一空气检测器
                title: showText,
                label: showText,
                value: showText,
                statisticsType: item.nodeType === 1 ? 3 : 2,
                ...item,
                children: arr.map(k => {
                    const showText = `${enumType[k.nodeType]}(${k.nodeName})`
                    if (k.parentId === item.id) {
                        return {
                            expand: true,
                            selected: false,
                            title: showText,
                            label: showText,
                            value: showText,
                            statisticsType: k.nodeType === 1 ? 3 : 2,
                            ...k,
                        }
                    }
                }).filter(Boolean)
            };
            result.push(newItem)
        }
    })
    return result;
}
getDeviceList();
watch(() => deviceList.value, (newVal, oldVal) => {
    if (isNullOrEmpty(oldVal) && !isNullOrEmpty(newVal)) {
        const { statisticsType, nodeCode }: { statisticsType: number, nodeCode: string } = newVal.find(item => item.nodeName === "多合一空气监测器") || {}
        handleDateChange(time.value, { statisticsType, nodeCode })
    }
}, { deep: true, immediate: true })
const handleChange = (val: string, data: any) => {
    trace(deviceList.value, (item: deviceCodeList) => { //深度优先添加动态添加树形禁止选择，防止重新点击导致取消选择
        item.disabled = false;
        if (item.nodeCode === data.nodeCode) {
            item.disabled = true
        }
    })
    emit("on-device-change", data);
}
const defaultText = ref<string>('类型(多合一空气监测器)')
const datePickerType = ref<string>('date')
bus.on('on-date-type-change', (dateType: string) => {
    datePickerType.value = datePickerTypeMap[dateType]
    time.value = dateTimeMap[dateType]
    console.log(dateType, 'dateType');
    handleDateChange(time.value)
})
onUnmounted(() => {
    bus.off('on-date-type-change')
})
</script>
<template>
    <div class="container">
        <AreaSelectTree @on-change="handleChange" :clearable="false" v-model="defaultText"
            style="width: 280px;position: relative;" :isRequestUrl="false"
            :data="tabIndex === 2 ? deviceList.slice(0, 1) : deviceList">
        </AreaSelectTree>
        <!-- <Select v-else @on-change="handleChange" clearable placeholder="请选择">
            <Option v-for="item in deviceList" :value="item.deviceCode" :key="item.id">{{ item.sbmc }} ({{ item.deviceCode
            }})</Option>
        </Select> -->
        <div class="right">
            <div class="time-range-box">
                <DatePicker :model-value="time[0]" :editable="false"
                    @on-change="(val: Date) => handleDateChange({ val, origin: 'start' })" :type="datePickerType"
                    placement="bottom-start" placeholder="开始日期" style="width: 120px" :clearable="false" />
                <div class="heng">-</div>
                <DatePicker :model-value="time[1]" :editable="false"
                    @on-change="(val: Date) => handleDateChange({ val, origin: 'end' })" :type="datePickerType"
                    placement="bottom-start" placeholder="结束日期" style="width: 120px" :clearable="false" />
            </div>
            <div class="tab" v-show="showTab">
                <span :class="['hour', (activeTitle === 'hour' && 'active') || '']" @click="handleClick('hour')">小时平均</span>
                <span :class="['day', (activeTitle === 'day' && 'active') || '']" @click="handleClick('day')">日平均</span>
            </div>
        </div>
    </div>
</template>

<style lang="less" scoped>
.container {
    padding: 12px 0;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .ivu-select {
        width: 265px;
    }

    .right {
        display: flex;
        align-items: center;
        column-gap: 12px;

        .time-range-box {
            display: flex;
            align-items: center;
            margin-left: 16px;

            .heng {
                margin: 0 8px;
            }
        }

        .tab {
            width: 132px;
            height: 24px;
            position: relative;
            cursor: pointer;

            span {
                display: inline-block;
                font-size: 14px;
                border-radius: 4px;
            }

            .hour {
                width: 100%;
                background-color: #bacfff;
                color: #fff;
                clip-path: polygon(0 0, 80px 0, 60px 24px, 0 24px);
                padding-left: 7px;

                &.active {
                    background-color: @primary-color;
                }
            }

            .day {
                width: 100%;
                position: absolute;
                left: 0;
                top: 0;
                background-color: #bacfff;
                clip-path: polygon(80px 0, 132px 0, 132px 24px, 60px 24px);
                color: #fff;
                text-align: right;
                padding-right: 10px;

                &.active {
                    background-color: @primary-color;
                }
            }
        }
    }
}
</style>
