const tabList = [
  {
    name: "达标分析",
    key: "standardAnalysis",
  },
  {
    name: "因子达标分析",
    key: "factorAnalysis",
  },
  {
    name: "空气质量趋势",
    key: "airQualityTrend",
  },
];

const tableList = [
  { title: "时间", key: "recordTime", width: 160 },
  { title: "本期达标数", slot: "bqReachNum" },
  { title: "本期达标率", slot: "bqReachRate" },
  { title: "同期达标数", slot: "tqReachNum" },
  { title: "同期达标率", slot: "tqReachRate" },
  { title: "同期达标数对比", slot: "tqRateComp" },
  { title: "上期达标数", slot: "sqReachNum" },
  { title: "上期达标率", slot: "sqReachRate" },
  { title: "上期达标数对比", slot: "sqRateComp" },
];
const enumSlot = [
  'PM25BqProp',//PM2.5本期浓度
  'PM25BqPropExceed',//PM2.5本期超标倍数
  'PM25TqProp',//PM2.5同期浓度值
  'PM25TqPropExceed',//PM2.5同期超标倍数
  'PM25TqPropComp',//PM2.5同期浓度对比
  'PM25SqProp',//PM2.5上期浓度值
  'PM25SqPropExceed',//PM2.5上期超标倍数
  'PM25SqPropComp',//PM2.5上期浓度值对比

  'PM10BqProp',//PM2.5本期浓度
  'PM10BqPropExceed',//PM2.5本期超标倍数
  'PM10TqProp',//PM2.5同期浓度值
  'PM10TqPropExceed',//PM2.5同期超标倍数
  'PM10TqPropComp',//PM2.5同期浓度对比
  'PM10SqProp',//PM2.5上期浓度值
  'PM10SqPropExceed',//PM2.5上期超标倍数
  'PM10SqPropComp',//PM2.5上期浓度值对比

  'COBqProp',//PM2.5本期浓度
  'COBqPropExceed',//PM2.5本期超标倍数
  'COTqProp',//PM2.5同期浓度值
  'COTqPropExceed',//PM2.5同期超标倍数
  'COTqPropComp',//PM2.5同期浓度对比
  'COSqProp',//PM2.5上期浓度值
  'COSqPropExceed',//PM2.5上期超标倍数
  'COSqPropComp',//PM2.5上期浓度值对比

  'SO2BqProp',//PM2.5本期浓度
  'SO2BqPropExceed',//PM2.5本期超标倍数
  'SO2TqProp',//PM2.5同期浓度值
  'SO2TqPropExceed',//PM2.5同期超标倍数
  'SO2TqPropComp',//PM2.5同期浓度对比
  'SO2SqProp',//PM2.5上期浓度值
  'SO2SqPropExceed',//PM2.5上期超标倍数
  'SO2SqPropComp',//PM2.5上期浓度值对比

  'NO2BqProp',//PM2.5本期浓度
  'NO2BqPropExceed',//PM2.5本期超标倍数
  'NO2TqProp',//PM2.5同期浓度值
  'NO2TqPropExceed',//PM2.5同期超标倍数
  'NO2TqPropComp',//PM2.5同期浓度对比
  'NO2SqProp',//PM2.5上期浓度值
  'NO2SqPropExceed',//PM2.5上期超标倍数
  'NO2SqPropComp',//PM2.5上期浓度值对比

  'O3BqProp',//PM2.5本期浓度
  'O3BqPropExceed',//PM2.5本期超标倍数
  'O3TqProp',//PM2.5同期浓度值
  'O3TqPropExceed',//PM2.5同期超标倍数
  'O3TqPropComp',//PM2.5同期浓度对比
  'O3SqProp',//PM2.5上期浓度值
  'O3SqPropExceed',//PM2.5上期超标倍数
  'O3SqPropComp',//PM2.5上期浓度值对比

]
const listTableList = [
  { title: "时间", key: "recordTime", fixed: "left", width: 160 },
  { title: "PM2.5本期浓度值", slot: "PM25BqProp", width: 160 },
  { title: "PM2.5本期超标倍数", slot: "PM25BqPropExceed", width: 160 },
  { title: "PM2.5同期浓度值", slot: "PM25TqProp", width: 160 },
  { title: "PM2.5同期超标倍数", slot: "PM25TqPropExceed", width: 160 },
  { title: "PM2.5同期浓度值对比", slot: "PM25TqPropComp", width: 160 },
  { title: "PM2.5上期浓度值", slot: "PM25SqProp", width: 160 },
  { title: "PM2.5上期超标倍数", slot: "PM25SqPropExceed", width: 160 },
  { title: "PM2.5上期浓度值对比", slot: "PM25SqPropComp", width: 160 },
  { title: "PM10本期浓度值", slot: "PM10BqProp", width: 160 },
  { title: "PM10本期超标倍数", slot: "PM10BqPropExceed", width: 160 },
  { title: "PM10本期浓度值", slot: "PM10TqProp", width: 160 },
  { title: "PM10周期超标倍数", slot: "PM10TqPropExceed", width: 160 },
  { title: "PM10同期浓度值对比", slot: "PM10TqPropComp", width: 160 },
  { title: "PM10上期浓度值", slot: "PM10SqProp", width: 160 },
  { title: "PM10上期超标倍数", slot: "PM10SqPropExceed", width: 160 },
  { title: "PM10浓度值对比", slot: "PM10SqPropComp", width: 160 },
  { title: "CO本期浓度值", slot: "COBqProp", width: 160 },
  { title: "CO超标倍数", slot: "COBqPropExceed", width: 160 },
  { title: "CO同期浓度值", slot: "COTqProp", width: 160 },
  { title: "CO同期超标倍数", slot: "COTqPropExceed", width: 160 },
  { title: "CO同期浓度值对比", slot: "COTqPropComp", width: 160 },
  { title: "CO上期浓度值", slot: "COSqProp", width: 160 },
  { title: "CO上期超标倍数", slot: "COSqPropExceed", width: 160 },
  { title: "CO浓度值对比", slot: "COSqPropComp", width: 160 },
  { title: "SO2本期浓度值", slot: "SO2BqProp", width: 160 },
  { title: "SO2超标倍数", slot: "SO2BqPropExceed", width: 160 },
  { title: "SO2同期浓度值", slot: "SO2TqProp", width: 160 },
  { title: "SO2同期超标倍数", slot: "SO2TqPropExceed", width: 160 },
  { title: "SO2同期浓度值对比", slot: "SO2TqPropComp", width: 160 },
  { title: "SO2上期浓度值", slot: "SO2SqProp", width: 160 },
  { title: "SO2上期超标倍数", slot: "SO2SqPropExceed", width: 160 },
  { title: "SO2浓度值对比", slot: "SO2SqPropComp", width: 160 },
  { title: "NO2本期浓度值", slot: "NO2BqProp", width: 160 },
  { title: "NO2超标倍数", slot: "NO2BqPropExceed", width: 160 },
  { title: "NO2同期浓度值", slot: "NO2TqProp", width: 160 },
  { title: "NO2同期超标倍数", slot: "NO2TqPropExceed", width: 160 },
  { title: "NO2同期浓度值对比", slot: "NO2TqPropComp", width: 160 },
  { title: "NO2上期浓度值", slot: "NO2SqProp", width: 160 },
  { title: "NO2上期超标倍数", slot: "NO2SqPropExceed", width: 160 },
  { title: "NO2浓度值对比", slot: "NO2SqPropComp", width: 160 },
  { title: "O3本期浓度值", slot: "O3BqProp", width: 160 },
  { title: "O3超标倍数", slot: "O3BqPropExceed", width: 160 },
  { title: "O3同期浓度值", slot: "O3TqProp", width: 160 },
  { title: "O3同期超标倍数", slot: "O3TqPropExceed", width: 160 },
  { title: "O3同期浓度值对比", slot: "O3TqPropComp", width: 160 },
  { title: "O3上期浓度值", slot: "O3SqProp", width: 160 },
  { title: "O3上期超标倍数", slot: "O3SqPropExceed", width: 160 },
  { title: "O3浓度值对比", slot: "O3SqPropComp", width: 160 },
];
const sumTableList = [
  { title: "属性", key: "propKey", fixed: "left", width: 120 },
  { title: "本期达标数", key: "bqReachNum", width: 120 },
  { title: "本期达标率", slot: "bqReachRate", width: 120 },
  { title: "同期达标数", key: "tqReachNum", width: 120 },
  { title: "同期达标数对比", slot: "tqReachRateComp", width: 120 },
  { title: "上期达标数", key: "sqReachNum", width: 120 },
  { title: "上期达标对比", slot: "sqReachRateComp", width: 120 },
  { title: "本期超标数", key: "bqExceedNum", width: 120 },
  { title: "本期超标率", slot: "bqExceedRate", width: 120 },
  { title: "同期超标数", key: "tqExceedNum", width: 120 },
  { title: "同期超标数对比", slot: "tqExceedRateComp", width: 120 },
  { title: "上期超标数", key: "sqExceedNum", width: 120 },
  { title: "上期超标数对比", slot: "sqExceedRateComp", width: 120 },
];
const airQualityTableList = [
  { title: "时间", slot: "recordTime", fixed: "left", width: 160 },
  { title: "本期空气质量指数", slot: "bqAqi", width: 140 },
  { title: "本期所属区间", slot: "bqInterval", width: 120 },
  { title: "本期空气质量指数等级", slot: "bqGrade", width: 160 },
  { title: "本期空气质量指数类别", slot: "bqCategory", width: 160 },
  { title: "本期首要污染物", slot: "bqPrimaryPollutant", width: 160 },
  { title: "本期超标污染物", slot: "bqExceedPollutant", width: 160 },

  { title: "同期空气质量指数", slot: "tqAqi", width: 140 },
  { title: "同期所属区间", slot: "tqInterval", width: 120 },
  { title: "同期空气质量指数等级", slot: "tqGrade", width: 160 },
  { title: "同期空气质量指数类别", slot: "tqCategory", width: 160 },
  { title: "同期首要污染物", slot: "tqPrimaryPollutant", width: 160 },
  { title: "同期超标污染物", slot: "tqExceedPollutant", width: 160 },

  { title: "上期空气质量指数", slot: "sqAqi", width: 140 },
  { title: "上期所属区间", slot: "sqInterval", width: 120 },
  { title: "上期空气质量指数等级", slot: "sqGrade", width: 160 },
  { title: "上期空气质量指数类别", slot: "sqCategory", width: 160 },
  { title: "上期首要污染物", slot: "sqPrimaryPollutant", width: 160 },
  { title: "上期超标污染物", slot: "sqExceedPollutant", width: 160 },
];

const handleData = (key: string, arr: any[], res: any) => {
  const dataMap = {
    tdQuality: "ydQuality",
    tyQuality: "yyQuality",
    lateHour: "ydLateHour",
    tmQuality: "ymQuality",
    twQuality: "ywQuality",
    ydQuality: "tdQuality",
    yyQuality: "tyQuality",
    ydLateHour: "lateHour",
    ymQuality: "tmQuality",
    ywQuality: "twQuality",
  };
  const findItem = arr.find((item: any) => Object.keys(item).includes(dataMap[key]));
  if (findItem) {
    findItem[key] = res.data[key];
  } else {
    arr.push({
      [key]: res.data[key],
    });
  }
};
export {
  tabList,
  tableList,
  listTableList,
  sumTableList,
  airQualityTableList,
  handleData,
  enumSlot,
}