<script lang="ts" setup>
import titleCard from "./components/titleCard.vue";
import searchBox from "./components/searchBox.vue";
import standardAnalysis from "./components/standardAnalysis.vue";
import factorAnalysis from "./components/factorAnalysis.vue";
import reportTitle from "./components/reportTitle.vue";
import airQualityTrend from "./components/airQualityTrend.vue";
import eModal from "@/components/common/modal/index.vue";
import { ref, nextTick, watch, getCurrentInstance, computed } from "vue";
import {
    tabList,
    tableList,
    listTableList,
    sumTableList,
    airQualityTableList,
    handleData,
} from "./data";
import { SearchObj, deviceCodeList, DeviceInfo, InitParams, Column } from "./type";
import {
    reachKxDataAnalysis,
    propDepthKxData,
    reachDataAnalysis,
    qualityTrendKxData,
    exportAirStatistics,
    propDetailDataExport,
    propSummaryDataExport,
    qualityStatisticsDataExport,
    qualityCountData,
} from "@/api/livableManage/airQualityService.js";
import { queryBatchExportPatrolReportUrl } from "@/api/safeManage/parkPatrolService";
import { isNullOrEmpty } from "@/utils/tool";
const that = getCurrentInstance()?.appContext.config.globalProperties;
const listCom = ref(); //组件实例
// 获取列表数据
const getTableList = () => {
    listCom.value.getList(searchObj.value);
};

const searchObj = ref<SearchObj>({});
// 判断过滤表头
const filterColumn = () => {
    const selectedTypeFlag = selectedType.value === 'PM2.5/PM10传感器' || selectedType.value.includes('PM10') || selectedType.value.includes('PM2.5')
    // if (searchObj.value.queryType === "hour") {
    //     if (selectedTypeFlag) {
    //         filterListTableList.value = listTableList.slice(0, 17)
    //     } else {
    //         filterListTableList.value = [listTableList[0], ...listTableList.slice(17, 49)]
    //     }
    // } else {
    if (selectedTypeFlag) {
        filterListTableList.value = listTableList.slice(0, 17)
    } else {
        filterListTableList.value = listTableList;
    }
    // }
}
// 获取达标卡片数据分析
const complianceData = ref<any>({});
const getReachDataAnalysis = () => {
    reachDataAnalysis({}).then((res: any) => {
        if (res.success) {
            const arr: any[] = [];
            for (let key in res.data) {
                handleData(key, arr, res);
            }
            complianceData.value = [arr[2], arr[0], arr[4], arr[3], arr[1]];
        }
    });
};
getReachDataAnalysis();
// 获取k线数据分析
const option = ref<any>({});
const getKData = (origin?: string) => {
    // if (tabIndex.value === 1) getPropDetailData();
    if (origin === "standardAnalysis") {
        reachKxDataAnalysis(searchObj.value).then((res: any) => {
            if (res.success) {
                option.value = res.data;
            }
        });
    }
    if (origin === "factorAnalysis") {
        propDepthKxData(searchObj.value).then((res: any) => {
            if (res.success) {
                option.value = res.data;
            }
        });
    }
    if (origin === "airQualityTrend") {
        qualityTrendKxData(searchObj.value).then((res: any) => {
            if (res.success) {
                option.value = res.data;
            }
        });
    }
    getTableList();
};
// 切换tab栏时
const tabIndex = ref<number>(0);
const enumType = {
    0: "day",
    1: "day",
    2: "hour",
};
const handleTabChange = (index: number) => {
    selectedType.value = ''
    tabIndex.value = index;
    searchObj.value.queryType = enumType[index];
    filterColumn()
    getTitle();
};
// 小时平均日数平均改变时
const handleHourDayChange = (val: string, origin: string) => {
    searchObj.value.queryType = val;
    if (origin === "factorAnalysis") {
        filterColumn()
        getKData("factorAnalysis");
    }
    if (origin === "airQualityTrend") {
        getKData("airQualityTrend");
    }
};
// 当时间改变时
const handleDateChange = (val: string, origin: string, init?: InitParams) => {
    searchObj.value.startTime = val[0];
    searchObj.value.endTime = val[1];
    if (init) { //初始化时带搜索参数
        searchObj.value.unitCode = ''
        searchObj.value.deviceCode = ''
        searchObj.value.queryType = enumType[tabIndex.value]
        searchObj.value.unitCode = init.nodeCode
        searchObj.value.statisticsType = init.statisticsType
    }
    // if (!init && tabIndex.value === 2) {
    //     delete searchObj.value.unitCode
    //     delete searchObj.value.statisticsType
    // }
    nextTick(() => {
        if (origin == "standardAnalysis") {
            getKData("standardAnalysis");
        }
        if (origin === "factorAnalysis") {
            getKData("factorAnalysis");
        }
        if (origin === "airQualityTrend") {
            getKData("airQualityTrend");
        }
    });
};
// 年月日改变
const handleYearMonthDayChange = (val: string, origin?: string) => {
    searchObj.value.queryType = val;
    if (origin) {
        // getKData("standardAnalysis");
    }
};
const factorAnalysisTabIndex = ref<number>(0);
const handleFactorTabChange = (index: number) => {
    factorAnalysisTabIndex.value = index;
    nextTick(() => {
        getTableList();
    })
};
// // 获取设备列表
// const deviceList = ref<any[]>([]);
// const getDeviceList = () => {
//     airQualityDevice(searchObj.value).then((res: any) => {
//         if (res.success) {
//             const data = res.data
//             deviceList.value = handleDeviceTree(data)
//         }
//     });
// };
// const handleDeviceTree = (data: deviceCodeList[]) => {
//     let result: any[] = [];
//     const enumType = {
//         1: '类型',
//         2: '设备'
//     }
//     data.forEach((item: deviceCodeList, index: number, arr: deviceCodeList[]) => {
//         if (item.id) {
//             const showText = `${enumType[item.nodeType]}(${item.nodeName})`
//             const newItem = {
//                 expand: true,
//                 selected: item.nodeName === '多合一空气监测器' ? true : false,//默认已选多合一空气检测器
//                 title: showText,
//                 label: showText,
//                 value: showText,
//                 statisticsType: item.nodeType === 1 ? 3 : 2,
//                 ...item,
//                 children: arr.map(k => {
//                     const showText = `${enumType[k.nodeType]}(${k.nodeName})`
//                     if (k.parentId === item.id) {
//                         return {
//                             expand: true,
//                             selected: false,
//                             title: showText,
//                             label: showText,
//                             value: showText,
//                             statisticsType: k.nodeType === 1 ? 3 : 2,
//                             ...k,
//                         }
//                     }
//                 }).filter(Boolean)
//             };
//             result.push(newItem)
//         }
//     })
//     return result;
// }
// getDeviceList();
//设备改变
const selectedType = ref<string>('')
const handleDeviceChange = (deviceInfo: DeviceInfo, origin?: string) => {
    if (typeof deviceInfo === "string") {
        delete searchObj.value.statisticsType
        delete searchObj.value.unitCode
        searchObj.value.deviceCode = deviceInfo
    } else {
        selectedType.value = deviceInfo.nodeName
        searchObj.value.statisticsType = deviceInfo.statisticsType
        if (deviceInfo.statisticsType === 2) {
            delete searchObj.value.unitCode
            searchObj.value.deviceCode = deviceInfo.nodeCode
        } else {
            delete searchObj.value.deviceCode
            searchObj.value.unitCode = deviceInfo.nodeCode;
        }
    }
    if (origin == "standardAnalysis") {
        getKData("standardAnalysis");
    }
    if (origin === "factorAnalysis") {
        filterColumn()
        getKData("factorAnalysis");
    }
    if (origin === "airQualityTrend") {
        getKData("airQualityTrend");
    }
};
// 获取明细数据
// const propData = ref<any[]>([]);
// const getPropDetailData = () => {
//     propDetailData(searchObj.value).then((res: any) => {
//         if (res.success) {
//             res.data.records.map((item: any) => {
//                 return {};
//             });
//             propData.value = res.data.records;
//         }
//     });
// };
// 导出
const progress = ref<number>(0);
const showProgressModal = ref<boolean>(false);
let timer: any; //递归计时器,用户清除递归查询
function exportPatrolReport(key: string) {
    timer = setTimeout(() => {
        queryBatchExportPatrolReportUrl(key).then((res: any) => {
            if (res.data.success) {
                progress.value = 100;
                const timer1 = setTimeout(() => {
                    progress.value = 101;
                    clearTimeout(timer1);
                }, 800);
                const timer2 = setTimeout(() => {
                    showProgressModal.value = false;
                    window.open(res.data.data);
                    progress.value = 0;
                    clearTimeout(timer2);
                }, 1500);
            } else {
                showProgressModal.value = true;
                progress.value = Math.ceil((res.data.done / res.data.total) * 100);
                clearTimeout(timer)
                exportPatrolReport(key);
            }
        });
    }, 2000);
}
// 导出接口枚取值
const enumExportApi = {
    达标统计表: exportAirStatistics,
    明细表: propDetailDataExport,
    汇总表: propSummaryDataExport,
    空气质量统计表: qualityStatisticsDataExport,
};
// 批量导出获取key
function handleExport(origin: string) {
    const batchExport = {
        startTime: searchObj.value.startTime, //计划名称
        endTime: searchObj.value.endTime, //计划编码
        queryType: searchObj.value.queryType,
        deviceCode: searchObj.value.deviceCode,
        unitCode: searchObj.value.unitCode,
        statisticsType: searchObj.value.statisticsType,
    };
    enumExportApi[origin](batchExport).then((res: any) => {
        if (res.success) {
            showProgressModal.value = true;
            exportPatrolReport(res.data);
        }
    });
}
const handleCancel = () => {
    clearTimeout(timer);
};

// 过滤表头
const filterListTableList = ref<Column[]>([]);
filterListTableList.value = listTableList;
const legend = ref<string[]>(['CO', 'SO2', 'NO', 'O3', 'PM2.5', 'PM10']); //初始的legend，用户过滤表格列数据
const handelFilterColumns = (selected: string[]) => {
    legend.value = selected
    filterListTableList.value = [];
    listTableList.forEach((item: any) => {
        selected.forEach((str: string) => {
            if (item.title.indexOf(str) !== -1) {
                filterListTableList.value.push(item);
            }
        });
    });
    filterListTableList.value.unshift(listTableList[0]);
};
// const  filterAirTableList = ref<Column[]>(airQualityTableList)
// const airColumnFilter = (selected:string[])=>{
//     if(selected.includes('本期')){
//         filterAirTableList.value = airQualityTableList.filter()
//     }
// }
// const computedFilterListTableList = computed(() => {
//     let columns: Column[]
//     if (selectedType.value === 'PM2.5/PM10传感器') {
//         const arr = <string[]>['PM2.5', 'PM10']
//         columns = filterListTableList.value.filter((column: Column) => {
//             return arr.find((str: string) => column.title.includes(str))
//         })
//         columns.unshift(listTableList[0])
//         return columns
//     }
//     if (selectedType.value.includes('PM2.5')) {
//         columns = filterListTableList.value.filter((column: Column) => {
//             return column.title.includes('PM2.5')
//         })
//         columns.unshift(listTableList[0])
//         return columns
//     }
//     if (selectedType.value.includes('PM10')) {
//         columns = filterListTableList.value.filter((column: Column) => {
//             return column.title.includes('PM10')
//         })
//         columns.unshift(listTableList[0])
//         return columns
//     }
//     columns = filterListTableList.value.filter((column: Column) => {
//         return legend.value.find((str: string) => {
//             return column.title.includes(str)
//         })
//     })
//     columns.unshift(listTableList[0])
//     return columns
// })
let titleString = ref<string>("");
const titleDate = ref<any>({})
const getTitle = () => {
    if (tabIndex.value === 2) {
        qualityCountData(searchObj.value).then((res: any) => {
            if (res.success) {
                const data: any = {};
                for (let key in res.data.c) {
                    if (isNullOrEmpty(res.data.c[key])) {
                        data[key] = '--';
                    } else {
                        data[key] = res.data.c[key]
                    }
                }
                titleDate.value = res.data.p
                titleString.value = `空气质量类别优${data.excellent}天，占比${data.excellentPart}，类别良${data.good}天，占比${data.goodPart}，
                轻度污染${data.mild}天，占比${data.mildPart}，中度污染${data.moderate}天，占比${data.moderatePart}，重度污染${data.severe}天，
                占比${data.severePart}，严重污染${data.serious}天，占比${data.seriousPart}`;
            }
        });
    }
};
watch(
    () => searchObj.value,
    () => {
        getTitle();
    },
    { deep: true }
);
</script>
<template>
    <BreadcrumbCustom>
        <BreadcrumbItem>统计分析</BreadcrumbItem>
        <BreadcrumbItem>空气达标分析</BreadcrumbItem>
    </BreadcrumbCustom>
    <ContentCard class="content-card-title" title="空气达标分析"></ContentCard>
    <div class="middle-cards">
        <titleCard class="title-card" v-for="(item, index) in complianceData" :data="item" :originIndex="index"
            :key="index"></titleCard>
    </div>
    <ContentCard class="content-card">
        <s-tab :tab-list="tabList" @handleChange="handleTabChange">
            <template #standardAnalysis v-if="tabIndex === 0">
                <searchBox :dateType="searchObj.queryType" :tabIndex="tabIndex" @on-device-change="(val) => handleDeviceChange(val, 'standardAnalysis')"
                    @on-date-change="(val, init) => handleDateChange(val, 'standardAnalysis', init)"
                    @on-hour-day-change="(val) => handleHourDayChange(val, 'standardAnalysis')" :showTab="false" />
                <standardAnalysis :option="option" @on-change="(val) => handleYearMonthDayChange(val, 'self')" />
                <s-tab :tab-list="[{ name: '达标统计表', key: 'report' }]">
                    <template #report>
                        <reportTitle :selectedType="selectedType" @on-export="handleExport" :date="option" origin="达标统计表"
                            ref="listCom" url="/airEnvironmentQualityStatistics/reachStatDataAnalysis"
                            :tableList="tableList" />
                    </template>
                </s-tab>
            </template>
            <template #factorAnalysis v-else-if="tabIndex === 1">
                <searchBox :dateType="searchObj.queryType" :tabIndex="tabIndex" @on-device-change="(val) => handleDeviceChange(val, 'factorAnalysis')"
                    @on-date-change="(val, init) => handleDateChange(val, 'factorAnalysis', init)"
                    @on-hour-day-change="(val) => handleHourDayChange(val, 'factorAnalysis')" />
                <factorAnalysis :selectedType="selectedType" :isHour="searchObj.queryType"
                    @on-legendselectchange="handelFilterColumns" :option="option" />
                <s-tab :tab-list="[
                        { name: '明细表', key: 'list' },
                        { name: '汇总表', key: 'sum' },
                    ]" @handleChange="handleFactorTabChange">
                    <template #list v-if="factorAnalysisTabIndex === 0">
                        <reportTitle :selectedType="selectedType" :isHour="searchObj.queryType" origin="明细表" :date="option"
                            @on-export="handleExport" ref="listCom" url="/airEnvironmentQualityStatistics/propDetailData"
                            :tableList="filterListTableList" />
                    </template>
                    <template #sum v-else>
                        <reportTitle :selectedType="selectedType" origin="汇总表" :showPage="false" @on-export="handleExport"
                            :date="option" ref="listCom" url="/airEnvironmentQualityStatistics/propSummaryData"
                            :tableList="sumTableList" />
                    </template>
                </s-tab>
            </template>
            <template #airQualityTrend v-else>
                <searchBox :dateType="searchObj.queryType" :tabIndex="tabIndex" @on-device-change="(val) => handleDeviceChange(val, 'airQualityTrend')"
                    @on-date-change="(val, init) => handleDateChange(val, 'airQualityTrend', init)"
                    @on-hour-day-change="(val) => handleHourDayChange(val, 'airQualityTrend')" />
                <airQualityTrend :option="option" :isHour="searchObj.queryType" />
                <s-tab :tab-list="[{ name: '空气质量统计表', key: 'airQuality' }]">
                    <template #airQuality>
                        <reportTitle :selectedType="selectedType" :titleString="titleString" :isHour="searchObj.queryType"
                            origin="空气质量统计表" @on-export="handleExport" :date="titleDate" ref="listCom"
                            url="/airEnvironmentQualityStatistics/qualityStatisticsData" :tableList="airQualityTableList" />
                    </template>
                </s-tab>
            </template>
        </s-tab>
    </ContentCard>
    <eModal :option="{
        title: '批量导出',
        footerHide: true,
        width: 408,
    }" :is-show="showProgressModal" @on-cancel="handleCancel">
        <div class="download">
            <Circle :percent="progress" :stroke-color="(progress >= 100 && '#5cb85c') || '#2db7f5'" :size="80">
                <Icon v-if="progress == 101" type="ios-checkmark" size="60" style="color: #5cb85c"></Icon>
                <span v-if="progress <= 100" class="progress-num">{{ progress }}%</span>
            </Circle>
            <div class="tips">数据导出中，请勿关闭窗口</div>
        </div>
    </eModal>
</template>

<style lang="less" scoped>
.content-card-title {
    /deep/ .ivu-typography {
        margin: 0;
    }
}

.download {
    text-align: center;
    padding: 48px 0;

    .tips {
        color: #798799;
        font-size: 14px;
    }
}

.middle-cards {
    margin: 8px 0 8px 0;
    width: 100%;
    display: flex;
    column-gap: 16px;
    .title-card{
        flex:1;
        overflow: hidden;
    }
}

.content-card {
    /deep/ .tab-title {
        justify-content: flex-start;
        column-gap: 12px;
    }

    .top {
        display: flex;
        justify-content: space-between;
        height: 40px;
        align-items: flex-end;
        font-size: 12px;

        .date {
            display: flex;
            column-gap: 12px;
            color: #798799;
        }

        .right {
            display: flex;
            column-gap: 16px;
            color: #4e5969;

            span:nth-child(1),
            span:nth-child(2) {
                position: relative;

                &::after {
                    content: "";
                    display: block;
                    position: absolute;
                    left: -12px;
                    top: 4px;
                    width: 9px;
                    height: 9px;
                    background-color: #ff7d00;
                }
            }

            span:nth-child(3) {
                position: relative;

                &::after {
                    content: "";
                    display: block;
                    position: absolute;
                    left: -12px;
                    top: 4px;
                    width: 10px;
                    height: 11px;
                    background-image: url("../../../assets/images/上涨.png");
                    background-size: cover;
                }
            }

            span:nth-child(4) {
                position: relative;

                &::after {
                    content: "";
                    display: block;
                    position: absolute;
                    left: -12px;
                    top: 4px;
                    width: 10px;
                    height: 11px;
                    background-image: url("../../../assets/images/下降.png");
                    background-size: cover;
                }
            }
        }
    }

    .middle {
        margin: 16px 0;
        height: 40px;
        border-radius: 2px;
        background-color: #e8f3ff;
        display: flex;
        align-items: center;
        padding: 12px;
        column-gap: 16px;
        color: #1d2129;
    }
}
</style>
