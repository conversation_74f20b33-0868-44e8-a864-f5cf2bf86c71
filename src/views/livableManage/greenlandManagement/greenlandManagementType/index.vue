<script lang="ts" setup>
import TableContentCard from '@/components/global/TableContentCard'
import { getGreenLandTypeList, addGreenLandType, editGreenLandType, deleteGreenLandType } from '@/api/livableManage/greenlandManagementService'
import AddAndEdit from './addAndEdit.vue'
import { getCurrentInstance, reactive, onMounted, ref } from 'vue'
const tableList = reactive([
  { title: '绿地类型', key: 'name' },
  { title: '备注', slot: 'remark' },
  { title: '创建人', key: 'creatorName' },
  { title: '创建时间', key: 'createTime', width: 160 },
  { title: '操作', slot: 'action', maxWidth: 100 }
])
const listCom = ref<any>()
function getList() {
  listCom.value.search()
}
onMounted(() => {
  getList()
})


const showDialog = ref<boolean>(false)
const dialogTitle = ref<string>('')
// 点击新建或编辑
const itemId = ref<number>(-1)
const edit = ref()
function handleDialog(origin: string,row:any) {
  dialogTitle.value = origin
  if (origin === '新建') {
    showDialog.value = true
  } else {
    itemId.value = row.id
    showDialog.value = true
    edit.value.handleForm(row)
  }
}
// 确定编辑或新建
interface Form {
  remark?: string,
  name?: string
}
async function handleConfirm(form: Form, origin: string) {
  if (origin === '新建') {
    let res: any = await addGreenLandType(form)
    if (res.success) {
      that?.$Message.success('新增成功')
      showDialog.value = false
      edit.value.formRef.resetFields()
      edit.value.handleForm({})
      getList()
    }
  } else {
    let res: any = await editGreenLandType(form)
    if (res.success) {
      that?.$Message.success('编辑成功')
      showDialog.value = false
      getList()
    }
  }
}
// 取消编辑或新建
function handleCancel() {
  showDialog.value = false
}

const that = getCurrentInstance()?.appContext.config.globalProperties
// 删除
function handleDelete(id:number) {
  that?.$Modal.confirm({
    title: '提示',
    content: '你确定要删除选中的记录吗？',
    onOk:async () => {
      let res:any = await deleteGreenLandType(id)
      if (res.success) {
        that.$Message.success('删除成功')
        getList()
      }
    }
  })
}

const searchObj = reactive<any>({
    name: ''
})
// 搜索查询
function handleSubmit() {
  const param = _.cloneDeep(searchObj)
  listCom.value.search(param)
}
</script>
<template>
  <BreadcrumbCustom>
    <BreadcrumbItem>绿地管理</BreadcrumbItem>
    <BreadcrumbItem>绿地类型管理</BreadcrumbItem>
  </BreadcrumbCustom>
  <ContentCard title="绿地类型管理">
    <BaseForm @handle-submit="handleSubmit" :model="searchObj" inline :label-width="90">
      <template #formitem>
        <FormItem label="绿地类型" prop="name">
          <Input v-model="searchObj.name" :maxlength="20" placeholder="请输入" clearable></Input>
        </FormItem>
      </template>
    </BaseForm>
    <TableContentCard add :baseBtn="false">
      <template #btn>
        <Button type="primary" v-auth="'greenLand:greenLandTypeManagement:add'" custom-icon="iconfont icon-plus" @click="handleDialog('新建',null)">新建</Button>
      </template>
      <baseTable :columns="tableList" ref="listCom" url="/greenLand/greenLandType/getPage">
        <template #remark="{row}">
            <tooltip-auto-show :content="row.remark"></tooltip-auto-show>
        </template>
        <template #action="{row}">
          <LinkBtn size="small" v-auth="'greenLand:greenLandTypeManagement:edit'" @click="handleDialog('编辑',row)">编辑</LinkBtn>
          <LinkBtn size="small" v-auth="'greenLand:greenLandTypeManagement:del'" @click="handleDelete(row.id)">删除</LinkBtn>
        </template>
      </baseTable>
    </TableContentCard>
  </ContentCard>
  <AddAndEdit :id="itemId" :show="showDialog" ref="edit" :title="dialogTitle" @on-confirm="handleConfirm" @on-cancel="handleCancel"></AddAndEdit>
</template>

<style lang="less" scoped>
/deep/ .ivu-typography{
    line-height: 16px;
    margin-bottom: 16px;
}
</style>
