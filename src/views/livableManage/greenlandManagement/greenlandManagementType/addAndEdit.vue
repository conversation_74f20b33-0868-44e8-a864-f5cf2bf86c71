<script lang="ts" setup>
import { emitType } from '@/api/manholeCoverService'
import Util  from '@/utils';
import { defineProps, defineEmits, defineExpose ,ref } from 'vue'
const props = defineProps({
  show: { default: false },
  title: { default: '' },
  id:{default:''}
})
const form = ref<any>({})
const formRef = ref(null)
const emit: emitType = defineEmits()
function handleOk(formRef:any) {
    formRef && formRef.validate((valid: any) => {
        if (valid) {
            if(props.id) form.value.id = props.id
            emit('on-confirm',form.value,props.title)
        }
    })
}
function handleCancel() {
  form.value = {}
  formRef?.value?.resetFields()
  emit('on-cancel')
}

function handleForm(row:any){
  form.value = Util.objClone(row)
}
defineExpose({ handleForm, formRef })

// 表单校验规则
const formRules = ref({
    name: [{ required: true, message: '绿地类型必须填写' }],
})

</script>
<template>
  <Modal v-model="props.show" :title="props.title" @on-cancel="handleCancel" :mask-closable="false" class-name="modal-wrapper" width="408">
    <div class="form-wrapper">
        <Form :model="form"  label-position="top" :rules="formRules" ref="formRef">
            <Row>
                <Col :span="24">
                    <FormItem label="绿地类型" prop="name">
                        <Input v-model="form.name"  placeholder="请输入" maxlength="20" clearable></Input>
                    </FormItem>
                </Col>
                <Col :span="24">
                    <FormItem label="备注">
                        <Input v-model="form.remark"   placeholder="请输入" maxlength="200" type="textarea" show-word-limit :rows="3" clearable></Input>
                    </FormItem>
                </Col>
            </Row>
        </Form>
    </div>
      <template #footer>
          <Button @click="handleCancel"> 取消 </Button>
          <Button type="primary" @click="handleOk(formRef)"> 确定 </Button>
      </template>
  </Modal>
</template>

<style lang="less" scoped>
.modal-wrapper{
    display: flex;
    justify-content: center;
    align-items: center;
    padding-bottom: 0;
}
:deep(.ivu-col:last-child .ivu-form-item){
    margin-bottom: 0;
}

.form-wrapper{
    margin-left: 16px;
    margin-right: 16px;
}

</style>
