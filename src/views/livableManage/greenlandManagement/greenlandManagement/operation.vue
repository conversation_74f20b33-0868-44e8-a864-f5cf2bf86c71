<script lang="ts" setup>
import { addGreenLandOperationRecords, } from '@/api/livableManage/greenlandManagementService'
import { ref, defineExpose, getCurrentInstance } from 'vue'
import moment from 'moment'
const show = ref<boolean>(false)
interface Operation {
  name?: string
  startTime?: string,
  endTime?: string,
  greenLandChangeArea?: number,
  changeContent?: string,
  greenLandId?: string,
  greeningChangeArea?: number
}
const form = ref<Operation>({
  greenLandChangeArea: 0,
  greeningChangeArea: 0
})
const that = getCurrentInstance()?.appContext.config.globalProperties
const formRef = ref();

async function ok() {
  console.log('运营', form.value)
  const valid = await formRef.value.validate()
  if (valid) {
    // 补全日期 YYYY-MM-DD 00:00:00
    form.value.startTime = createDate(form.value.startTime)
    form.value.endTime = createDate(form.value.endTime)
    let res: any = await addGreenLandOperationRecords(form.value)
    if (res.success) {
      that?.$Message.success('新增运营记录成功')
      formRef.value.resetFields();
      show.value = false
      form.value = defaultDataCreater();
      form.value = {}

    }
  }
}
function cancel() {
  console.log('cancel')
  form.value = {}
  formRef.value.resetFields();
  show.value = false
}
function handleShow(id: string) {
  show.value = true
  form.value.greenLandId = id
}
defineExpose({ handleShow });

// YYYY-MM-DD => YYYY-MM-DD HH:mm:ss
const createDate = (dateStr: string) => {
  return moment(dateStr).format('YYYY-MM-DD HH:mm:ss')
}
// 表单校验规则
const regex = /^-?(0|(\d{1,9}))(\.[\d]{1,2})?$/;

const greenLandChangeArea = (rule: any, value: any, callback: any) => {
  if (!regex.test(value)) {
    callback('最多输入9位整数两位小数')
  } else {
    callback()
  }
}
const greeningChangeArea = (rule: any, value: any, callback: any) => {
  if (!regex.test(value)) {
    callback('最多输入9位整数两位小数')
  } else {
    callback()
  }
}
const formRules = ref({
  name: [{ required: true, message: '记录名称必须填写' }],
  greenLandChangeArea: [
    { validator: greenLandChangeArea, trigger: 'blur' }
  ],
  greeningChangeArea: [
    { validator: greeningChangeArea, trigger: 'blur' }
  ],
})


// 创建初始数据
const defaultDataCreater = () => {
  return {
    name: '',
    endTime: '',
    greenLandChangeArea: 0,
    changeContent: '',
    greenLandId: '',
    greeningChangeArea: 0
  }
}
function inputBlur() {
  if (!form.value.greenLandChangeArea) {
    form.value.greenLandChangeArea = 0
  }
  if (!form.value.greeningChangeArea) {
    form.value.greeningChangeArea = 0
  }
}

</script>
<template>
  <Modal v-model="show" title="新建运营记录" :styles="{ top: '18px' }" class-name="vertical-center-modal" width="408" :mask-closable="false" @on-cancel="cancel" >
    <div class="form-wrapper">
        <Form :model="form" :rules="formRules" ref="formRef" label-position="top">
            <FormItem label="记录名称" required prop="name">
                <Input v-model="form.name" style="width: 344px" maxlength="20" clearable></Input>
            </FormItem>
            <FormItem label="绿地变化" prop="greenLandChangeArea">
                <Input clearable v-model="form.greenLandChangeArea" @on-blur="inputBlur" >
                    <template #append>
                        ㎡
                    </template>
                </Input>

            </FormItem>
            <FormItem label="绿化变化" prop="greeningChangeArea">
                <Input clearable v-model="form.greeningChangeArea" @on-blur="inputBlur">
                    <template #append>
                        ㎡
                    </template>
                </Input>

            </FormItem>
            <FormItem label="开始日期">
                <DatePicker v-model="form.startTime" @on-change="(a: any) => form.startTime = createDate(a)" type="date"
                            format="yyyy-MM-dd" placeholder="请选择日期" style="width: 344px" clearable :editable="false" />
            </FormItem>
            <FormItem label="结束日期">
                <DatePicker v-model="form.endTime" @on-change="(a: any) => form.endTime = createDate(a)" type="date"
                            format="yyyy-MM-dd" placeholder="请选择日期" style="width: 344px" clearable :editable="false" />
            </FormItem>
            <FormItem label="变化内容">
                <Input v-model="form.changeContent" :maxlength="200" type="textarea" show-word-limit
                       :rows="2" clearable></Input>
            </FormItem>
        </Form>
    </div>
    <template #footer>
      <Button @click="cancel">取消</Button>
      <Button type="primary" @click="ok">确定</Button>
    </template>
  </Modal>
</template>

<style lang="less" scoped>
/deep/ .ivu-input-number-input {
  position: relative;

  ::after {
    content: '㎡';
    display: inline-block;
    background: #F2F3F5;
    position: absolute;
    width: 32px;
    height: 32px;
  }
}

/deep/ .ivu-form-item-content {
  display: flex;

  .msg-box {
    height: 32px;
    width: 32px;
    line-height: 32px;
    display: inline-block;
    text-align: center;
    background: #f8f8f9;
    border-radius: 0 4px 4px 0;

    .msg {
      color: @normal-color;
      font-size: 12px;
    }
  }
}
.vertical-center-modal{
    display: flex;
    align-items: center;
    justify-content: center;
}

:deep(.ivu-date-picker .ivu-input-suffix .ivu-icon-ios-close-circle){
    font-family: "iconfont" !important;
    color: #4E627E;
    font-size: 16px;
    &:before{
        content: "\e705";
    }
}
:deep(.ivu-form-item:last-child){
    margin-bottom: 4px;
}
:deep(.ivu-form-item-label){
    padding-top: 0;
}
.form-wrapper{
    padding: 0 16px;
}


</style>
