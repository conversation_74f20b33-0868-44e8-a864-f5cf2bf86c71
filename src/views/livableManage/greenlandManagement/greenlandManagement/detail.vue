<script lang="ts" setup>
import { getGreenManagementDetail, getAllGreenLandType, greenManagementEdit, deleteGreenLandOperationRecords } from '@/api/livableManage/greenlandManagementService.js'
import { handleArea } from '@/utils/tool'
import { Form } from 'view-ui-plus';
import { getCurrentInstance, reactive, onMounted, ref, computed } from 'vue'
import { useRoute } from 'vue-router';
import moment from "moment";
import DrawArea
    from "@/views/livableManage/greenlandManagement/greenlandManagement/components/drawArea.vue";
import AreaSelectTree from "@/components/global/tree/areaSelectTree.vue";
import DictDropDownSelect from "@/components/global/dictDropDownSelect";
import AreaSelectTreeNoDevice from "@/components/global/tree/areaSelectTreeNoDevice.vue";
import { validateform } from "@/utils/validateform";
import { json } from 'stream/consumers';

const route = useRoute()
const rule = reactive([{ pattern: /^(?!0\d)\d{1,5}(\.\d{1,2})?$/, message: '只能输入数字，最多五位整数，两位小数', trigger: 'blur' }])
const editRule = reactive({
    greenLandNo: { required: true, message: '请完善必填项', trigger: 'change,blur' },
    greenLandName: { required: true, message: '请完善必填项', trigger: 'change,blur' },
    areaLocation: { required: true, message: '请完善必填项', trigger: 'change,blur' },
    greenLandArea: rule,
    greeningArea: rule,
    contactPhone: validateform.checkPhone
})
const tableList = ref<any>([
  { title: '记录名称', key: 'name', maxWidth: 100, tooltip: true },
  { title: '绿地变化', slot: 'greenLandChangeArea' },
  { title: '绿化变化', slot: 'greeningChangeArea' },
  { title: '开始日期', slot: 'startTime', minWidth: 90, tooltip: true },
  { title: '结束日期', slot: 'endTime', minWidth: 90, tooltip: true },
  { title: '变化内容', key: 'changeContent', tooltip: true },
  { title: '责任人', key: 'creatorName', tooltip: true },
  { title: '创建时间', key: 'createTime', tooltip: true },
  { title: '操作', slot: 'operation', width: 100, align: 'center' }
])
// 查询所有类型
const TypeList = ref<Array<any>>([])
async function getAllType() {
  let res: any = await getAllGreenLandType()
  if (res.success) {
    TypeList.value = res.data
  }
}
getAllType()

const detailInfo = reactive<any>({})
async function getDetailInfo() {
  let res: any = await getGreenManagementDetail(route.query.id)
  if (res.success) {
    for (let key in res.data) {
      if (key === 'objInfo') {
        for (let key2 in res.data[key]) {
          detailInfo[key2] = res.data[key][key2]
        }
      } else {
        detailInfo[key] = res.data[key]
      }
    }
    detailInfo.areaLocation = detailInfo.areaPath?.replace(/@/g, '/');
      // detailInfo.areaLocation
  }
}
getDetailInfo()
// 获取运营记录列表
interface searchObjType {
  startTime?: string,
  endTime?: string,
  greenLandId: string
}
const searchObj = reactive<any>({})
const listCom = ref()
function getList(form: Form | null) {
  let param: searchObjType = JSON.parse(JSON.stringify(searchObj))
  param.greenLandId = Number(route.query.id)
  if (form) {
    param = {
      ...form,
      ...param
    }
  }
  listCom.value.search(param)
}
onMounted(() => {
  getList(null)
})
// 点击详情
function goDetail(row: string) {
}
function handleEdit(flag: boolean) {
  if (flag === false) {
    isEdit.value = false
  } else {
    isEdit.value = true
  }
}
const idList = ref<Array<string>>([])
const that = getCurrentInstance()?.appContext.config.globalProperties
function handleDelete(id: number) {
  idList.value = [id]
  that?.$Modal.confirm({
    title: '提示',
    content: '您确定要删除该记录吗？',
    onOk: async () => {
      let res: any = await deleteGreenLandOperationRecords(idList.value)
      if (res.success) {
        that.$Message.success('删除成功')
        idList.value = []
        getList(null)
      }
    }
  })

}

const isEdit = ref<boolean>(false)
// 编辑提交
// const that = getCurrentInstance()?.appContext.config.globalProperties
const editCom = ref()
const detailEdit = ref()
function handleEditSubmit() {
  console.log(detailInfo);
  editCom.value.validate(async (valid: Boolean) => {
    if (valid) {
        if (detailInfo.areaLocation) {
            const arr: Array<string> = detailInfo.areaLocation.split('/')
            detailInfo.szjd = arr[0]
            detailInfo.szsq = arr[1]
            detailInfo.szdywg = arr[2]
        }
      let res: any = await greenManagementEdit(detailInfo)
      if (res.success) {
        that?.$Message.success('修改成功')
        isEdit.value = false
        getDetailInfo()
        detailEdit.value.isEditFlag = false
      }
    }
  })
}
const handleType = computed(() => (id: any) => {
  console.log(TypeList);
  return TypeList.value.find((i: any) => i.id == id)?.name
})
// 运营查询
interface Form {
  startTime?: string,
  endTime?: string
}
const form = ref<Array<string> | []>([])
const searchParams = ref<Form>({})
function handelSearch(times: Array<string>) {
  searchParams.value.startTime = times[0]
  searchParams.value.endTime = times[1].replace('00:00:00','23:59:59')
  getList(searchParams.value)
  form.value = [times[0],times[1].replace('00:00:00','23:59:59')]
}
function formaterAreaPath(areaPath: string | undefined) {
  return areaPath?.replace(/@/g, '/')
}
function dateFormatter(dateStr:string):string {
    return dateStr ? moment(dateStr).format('YYYY-MM-DD') : '--';
}


// 绘制区域弹窗相关

const isShowDialog = ref(false)
const drawAreaRef = ref()
const onCloseModal = () => {
    isShowDialog.value = false;
}
const onConfirmModal = () => {
    const mapData = drawAreaRef.value?.handleConfirm();
    const path = mapData?._opts?.path;
    if (path) {
        painAreaPath.value = path
    }
    isShowDialog.value = false
}
const circuit = ref();
const polyArea = () => {
    isShowDialog.value = true;
}
const painAreaPath = computed({
    get() {
        const points = detailInfo.points;
        // return points?.split(';')?.map((item: string) => item?.split(',')) || []
        return points?JSON.parse(points) : []
    },
    set(newVal) {
        // detailInfo.points = newVal ? newVal?.map((item:any[]) => item?.join(','))?.join(';') : '';
        detailInfo.points = JSON.stringify(newVal);
    }
})
</script>
<template>
  <BreadcrumbCustom>
    <BreadcrumbItem>绿地管理</BreadcrumbItem>
    <BreadcrumbItem to="/greenlandManagement/greenlandManagement">绿地管理</BreadcrumbItem>
    <BreadcrumbItem v-if="!isEdit">绿地详情</BreadcrumbItem>
    <BreadcrumbItem v-else>绿地编辑</BreadcrumbItem>
  </BreadcrumbCustom>
<Form :rules="editRule" ref="editCom" :model="detailInfo" label-position="top">
  <detailCard @on-submit="handleEditSubmit" ref="detailEdit" @on-edit="handleEdit"
    :src="require('@/assets/images/icon-绿地.png')" :isEditBtn="$Util.checkAuth('greenLand:greenLandManagement:edit')" :isBackBtn="true" @on-back="$router.back()"
    title="详情">
    <template v-if="!isEdit">
      <Row>
        <Col span="8">
        <s-label class="code" label="绿地编号" valueStyle="font-weight:600" :value="detailInfo.greenLandNo" />
        </Col>
        <Col span="8">
        <s-label label="区域位置" valueStyle="font-weight:600" :value="formaterAreaPath(detailInfo.areaPath)" />
        </Col>
      </Row>
      <Row>
        <Col span="8">
        <s-label label="绿地名称" :value="detailInfo.greenLandName" />
        </Col>
<!--        <Col span="8">-->
<!--        <s-label label="权属单位" :value="detailInfo.ownerEnterpriseName" />-->
<!--        </Col>-->
<!--        <Col span="8">-->
<!--        <s-label label="坐标" :value="detailInfo.objX + ' , ' + detailInfo.objY" />-->
<!--        </Col>-->
        <Col span="8">
            <s-label label="管理部门" :value="detailInfo.deptName" />
        </Col>
        <Col span="8">
            <s-label label="养护单位" :value="detailInfo.opEnterpriseName" />
        </Col>
          <Col span="8">
              <s-label label="联系人" :value="detailInfo.contactPerson" />
          </Col>
          <Col span="8">
              <s-label label="联系电话" :value="detailInfo.contactPhone" />
          </Col>
        <Col span="8">
            <s-label label="绘制区域" :value="detailInfo.points">
                <template #label>
                    <div style="display: flex">
                        <span>绘制区域:</span>
                        <div class="show-map" @click="polyArea" v-if="detailInfo.points">
                            <Icon type="ios-pin-outline" />
                        </div>
                    </div>
                </template>
            </s-label>
        </Col>
      </Row>
    </template>
    <template v-else>
        <Row>
          <Col span="8">
          <FormItem label="绿地编号" prop="greenLandNo">
            <Input maxlength="20" placeholder="请输入" v-model="detailInfo.greenLandNo" disabled clearable></Input>
          </FormItem>
          </Col>
        </Row>
        <Row>
          <Col span="8">
              <FormItem label="绿地名称" prop="greenLandName" required>
                <Input maxlength="20" placeholder="请输入" v-model="detailInfo.greenLandName" clearable></Input>
              </FormItem>
          </Col>


            <Col span="8">
                <FormItem label="区域位置" prop="areaLocation" required>
                    <AreaSelectTreeNoDevice v-model="detailInfo.areaLocation" />
<!--                    <AreaSelectTree v-model="detailInfo.areaLocation" table-name="livable_green_land" type="0" />-->
                </FormItem>
            </Col>


            <Col span="8">
                <FormItem label="管理部门" prop="deptName">
                    <Input maxlength="20" placeholder="请输入" v-model="detailInfo.deptName" clearable></Input>
                </FormItem>
            </Col>

            <Col span="8">
                <FormItem label="养护单位">
                    <Input maxlength="20" placeholder="请输入" v-model="detailInfo.opEnterpriseName" clearable></Input>
                </FormItem>
            </Col>
            <Col span="8">
                <FormItem label="联系人">
                    <Input maxlength="20" placeholder="请输入"  v-model="detailInfo.contactPerson" clearable></Input>
                </FormItem>
            </Col>
            <Col span="8">
                <FormItem label="联系电话" prop="contactPhone">
                    <Input maxlength="20" placeholder="请输入"  v-model="detailInfo.contactPhone" clearable></Input>
                </FormItem>
            </Col>
            <Col span="8">
                <!--                    点击弹出地图-->
                <FormItem label="绘制区域" prop="points">
                    <Input maxlength="20"
                        @click="polyArea"
                        :model-value="painAreaPath"
                        placeholder="前往地图进行区域绘制"
                    >
                        <template #suffix>
                            <!--                                <img class="point-map-icon" src="../images/Vector.png" alt="" />-->
                        </template>
                    </Input>
                </FormItem>
            </Col>
<!--          <Col span="8">-->
<!--          <FormItem label="权属单位">-->
<!--            <Input maxlength="20" placeholder="请输入" v-model="detailInfo.ownerEnterpriseName" ></Input>-->
<!--          </FormItem>-->
<!--          </Col>-->
<!--          <Col span="8">-->
<!--          <FormItem label="坐标">-->
<!--            <Input maxlength="20" placeholder="请输入" :model-value="detailInfo.objX + ' , ' + detailInfo.objY" ></Input>-->
<!--          </FormItem>-->
<!--          </Col>-->
<!--          <Col span="8">-->
<!--          <FormItem label="部件状态">-->
<!--            <Input maxlength="20" placeholder="请输入" :model-value="$enumeration.objState[detailInfo.objState]" ></Input>-->
<!--          </FormItem>-->
<!--          </Col>-->
        </Row>
    </template>
  </detailCard>
  <detailCard :style="isEdit ? 'margin-bottom:46px' : ''"  @on-edit="handleEdit" :isEditBtn="false"  :src="require('@/assets/images/icon-基础信息.png')" title="绿地信息">
    <template v-if="!isEdit">
      <Row>
        <Col span="8">
        <s-label label="绿地类型" :value="handleType(detailInfo.typeId)" />
        </Col>
        <Col span="8">
            <s-label label="绿化项目状态" :value="detailInfo.projectStatusName" />
        </Col>
          <Col span="8">
        <s-label label="初始绿地面积" :value="handleArea(detailInfo.greenLandArea)" />
        </Col>
        <Col span="8">
        <s-label label="初始绿化面积" :value="handleArea(detailInfo.greeningArea)" />
        </Col>
        <Col span="8">
            <s-label label="最新绿地面积" :value="handleArea(detailInfo.newGreenLandArea)" />
        </Col>
        <Col span="8">
            <s-label label="最新绿化面积" :value="handleArea(detailInfo.newGreeningLandArea)" />
        </Col>
        <Col span="8">
        <s-label label="备注" :value="detailInfo.remark" />
        </Col>
      </Row>
    </template>
    <template v-else>

        <Row>
          <Col span="8">
          <FormItem label="绿地类型">
            <Select :transfer="false" placeholder="请输入" v-model="detailInfo.typeId" clearable>
              <Option v-for="(item, index) in TypeList" :key="index" :value="item.id">{{ item.name }}</Option>
            </Select>
          </FormItem>
          </Col>
            <Col span="8">
                <FormItem label="绿化项目状态" prop="projectStatus">
                    <dict-drop-down-select code="greenLand_project_status" v-model="detailInfo.projectStatus" />
                </FormItem>
            </Col>
          <Col span="8">
          <FormItem label="初始绿地面积" prop="greenLandArea">
            <Input maxlength="20" placeholder="请输入" v-model="detailInfo.greenLandArea" clearable>
            <template #append>
              ㎡
            </template>
            </Input>
          </FormItem>
          </Col>
          <Col span="8">
          <FormItem label="初始绿化面积" prop="greeningArea">
            <Input maxlength="20" placeholder="请输入" v-model="detailInfo.greeningArea" clearable>
            <template #append>
              ㎡
            </template>
            </Input>
          </FormItem>
          </Col>
          <Col span="8">
          <FormItem label="最新绿地面积" prop="newGreenLandArea">
            <Input maxlength="20" placeholder="请输入"  v-model="detailInfo.newGreenLandArea" clearable disabled>
            <template #append>
              ㎡
            </template>
            </Input>
          </FormItem>
          </Col>
          <Col span="8">
          <FormItem label="最新绿化面积" prop="newGreeningLandArea">
            <Input maxlength="20" placeholder="请输入"  v-model="detailInfo.newGreeningLandArea" clearable disabled>
            <template #append>
              ㎡
            </template>
            </Input>
          </FormItem>
          </Col>
            <Col span="24">
                <FormItem label="备注" prop="remark">
                    <sTextarea v-model="detailInfo.remark"></sTextarea>
                </FormItem>
            </Col>
        </Row>
    </template>
  </detailCard>
  <detailCard v-show="!isEdit" :class="{'bottom-card':isEdit}" title="运营记录">
    <div class="form-date">
      <DatePicker :model-value="form"  type="daterange" format="yyyy-MM-dd HH:mm:ss" placeholder="请选择日期" @on-change="handelSearch"
        placement="bottom-end" style="width: 320px" :transfer="true" clearable :editable="false" />
    </div>
    <baseTable :columns="tableList" ref="listCom" url="/greenLand/greenLandOperationRecords/getPage">
      <template #greenLandChangeArea="{ row }">
        <tooltipAutoShow :content="handleArea(row.greenLandChangeArea)"></tooltipAutoShow>
      </template>
      <template #greeningChangeArea="{ row }">
        <tooltipAutoShow :content="handleArea(row.greeningChangeArea)"></tooltipAutoShow>
      </template>
        <template #startTime="{ row }">
            {{dateFormatter(row.startTime)}}
        </template>
        <template #endTime="{ row }">
            {{dateFormatter(row.endTime)}}
        </template>
      <template #operation="{ row }">
        <!-- <LinkBtn size="small" @click="goDetail(JSON.stringify(row))">查看</LinkBtn> -->
        <LinkBtn size="small" @click="handleDelete(row.id)">删除</LinkBtn>
      </template>
    </baseTable>
  </detailCard>
</Form>
    <Modal v-if="isShowDialog" v-model="isShowDialog" :width="800" title="绘制区域">
        <DrawArea :path="painAreaPath" :is-edit="isEdit" ref="drawAreaRef"></DrawArea>
        <template #footer>
            <Button @click="onCloseModal">取消</Button>
            <Button type="primary" @click="onConfirmModal">确定</Button>
        </template>
    </Modal>
</template>

<style lang="less" scoped>
.form-date {
  height: 40px;
  margin: 10px 0;
  width: 100%;
  text-align: left;
  line-height: 40px;
}

.ivu-form-item {
  width: 80%;
}
.bottom-card{
  margin-bottom: 46px;
}
.show-map {
    font-size: 16px;
    color: #165DFF;
    cursor: pointer;
    .ivu-icon {
        font-weight: 700;
    }
}
</style>
