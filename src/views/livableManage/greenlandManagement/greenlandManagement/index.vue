<script lang="ts" setup>
import TableContentCard from '@/components/global/TableContentCard'
import Operation from './operation.vue'
import setModal from './components/setModal.vue'
import { greenlandManagementDelete, getAllGreenLandType } from '@/api/livableManage/greenlandManagementService'
import { commonService } from '@/api/commonService'
import { reactive, markRaw, getCurrentInstance, onBeforeMount, onMounted, ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import _ from 'lodash'
import DictDropDownSelect from "@/components/global/dictDropDownSelect";
import AreaSelectTreeNoDevice from "@/components/global/tree/areaSelectTreeNoDevice.vue";
const tableList = reactive<any>([
  { type: 'selection', maxWidth: 40 },
  { title: '绿地编号', slot: 'objId', maxWidth: 100 },
  { title: '绿地名称', slot: 'greenLandName', width: 100 },
  { title: '绿地类型', slot: 'typeId' },
  { title: '绿化项目状态', slot: 'projectStatus', width: 120 },
  { title: '养护单位', slot: 'opEnterpriseName', maxWidth: 130 },
  { title: '主管部门', slot: 'deptName', tooltip: true },
  { title: '区域位置', slot: 'areaPath', tooltip: true, width: 200 },
  { title: '创建日期', key: 'createTime', tooltip: true, width: 160 },
  { title: '操作', slot: 'action', width: 100 }
])
const searchObj = reactive<any>({
    greenLandNo: '',
    projectStatus: '',
    objInfo: {}
})
const listCom = ref()
// 搜索查询
function handleSubmit() {
  const param = _.cloneDeep(searchObj)
  if (searchObj.areaLocation) {
    const arr: Array<string> = param.areaLocation.split('/')
    param.szjd = arr[0]
    param.szsq = arr[1]
    param.szdywg = arr[2]
    delete param.areaLocation
  }
  listCom.value.search(param)
}
onMounted(() => {
  handleSubmit()
})
const router = useRouter()
// 新增
function handleAdd() {
  router.push({
    name: 'greenLand:greenlandManagementAdd'
  })
}
// 删除
const that = getCurrentInstance()?.appContext.config.globalProperties

function handleDelete() {
  if(!idList.value.length) return that.$Message.warning('至少要选择一条数据')
  that?.$Modal.confirm({
    title: '提示',
    content: '您确定要删除选中的记录吗？',
    onOk: async () => {
      let res: any = await greenlandManagementDelete(idList.value)
      if (res.success) {
        that.$Message.success('删除成功')
        idList.value = []
        handleSubmit()
      }
    }
  })

}
const idList = ref<Array<string>>([])
function selectionChange(list: any) {
  idList.value = list.map((i: any) => i.id)
}
// 详情
function handleDetail(id: number, typeId: number, objId: string) {
  router.push({
    name: 'greenLand:greenlandManagementDetail',
    query: {
      id,
      typeId,
      objId
    }
  })
}
// 运营
const operationRef = ref()
function handleOperation(id: number) {
  operationRef.value.handleShow(id)
}
// document.querySelector('body')?.addEventListener('click',()=>{
//   handleDetail()
// })


// 查询所有类型
const TypeList = ref<Array<any>>([])
async function getAllType() {
  let res: any = await getAllGreenLandType()
  if (res.success) {
    TypeList.value = res.data
  }
}
getAllType()
const handleType = computed(() => (id: any) => {
  return TypeList.value.find((i: any) => i.id == id)?.name
})

function formaterAreaPath(areaPath: string | undefined) {
    return areaPath?.replace(/@/g, '/')
}

// 设置
const setR = ref()
const showSetModal = () => {
    setR.value.init()
}

</script>
<template>
  <BreadcrumbCustom>
    <BreadcrumbItem>绿地管理</BreadcrumbItem>
    <BreadcrumbItem>绿地管理</BreadcrumbItem>
  </BreadcrumbCustom>
  <ContentCard title="绿地管理">
    <BaseForm @handle-submit="handleSubmit" :model="searchObj" inline :label-width="90">
      <template #formitem>
        <FormItem label="绿地编号" prop="greenLandNo">
          <Input v-model="searchObj.greenLandNo" :maxlength="20" placeholder="请输入" clearable></Input>
        </FormItem>
        <FormItem label="绿地类型" prop="typeId">
          <Select :transfer="false" v-model="searchObj.typeId" clearable>
            <Option v-for="(item, index) in TypeList" :key="index" :value="item.id" clearable>
              {{ item.name }}
            </Option>
          </Select>
        </FormItem>
        <FormItem label="绿化项目状态" prop="projectStatus" label-width="100">
            <dict-drop-down-select code="greenLand_project_status" v-model="searchObj.projectStatus">
            </dict-drop-down-select>
<!--          <Select :transfer="false" v-model="searchObj.projectStatus" clearable>-->
<!--            <Option v-for="(item, index) in greeningProjectStatusOption" :key="index" :value="index+1" clearable>-->
<!--              {{ item }}-->
<!--            </Option>-->
<!--          </Select>-->
        </FormItem>
        <FormItem label="区域位置" prop="areaLocation">
            <AreaSelectTreeNoDevice v-model="searchObj.areaLocation" table-name="livable_green_land" />
        </FormItem>
      </template>
    </BaseForm>
    <TableContentCard  addAuth="greenLand:greenLandManagement:add" deleteAuth="greenLand:greenLandManagement:del" @on-add="handleAdd" @on-delete="handleDelete">
      <template #btn>
          <Button @click="showSetModal" v-auth="'greenLand:greenLandManagement:standardSave'">
              <Icon custom="iconfont icon-settings" />标椎设置</Button>
<!--        <Dropdown>
          <Button>
            批量操作
            <Icon type="ios-arrow-down"></Icon>
          </Button>
          <template #list>
            <DropdownMenu>
              <DropdownItem v-auth="'greenLand:greenLandManagement:import">批量导入</DropdownItem>
              <DropdownItem v-auth="'greenLand:greenLandManagement:export">批量导出</DropdownItem>
              <DropdownItem v-auth="'greenLand:greenLandManagement:bEdit">批量修改</DropdownItem>
            </DropdownMenu>
          </template>
        </Dropdown>-->
      </template>
      <baseTable @on-selection-change="selectionChange" :columns="tableList" ref="listCom"
        url="/greenLand/greenLand/getPage">
        <template #objId="{ row }">
          <tooltipAutoShow :content="row.greenLandNo"></tooltipAutoShow>
        </template>
        <template #greenLandName="{ row }">
          <tooltipAutoShow :content="row.greenLandName"></tooltipAutoShow>
        </template>
        <template #typeId="{ row }">
          <tooltipAutoShow :content="handleType(row.typeId)"></tooltipAutoShow>
        </template>
        <template #projectStatus="{ row }">
          <tooltipAutoShow :content="row.projectStatusName"></tooltipAutoShow>
        </template>
        <template #opEnterpriseName="{ row }">
          <tooltipAutoShow :content="row.opEnterpriseName"></tooltipAutoShow>
        </template>
        <template #deptName="{ row }">
          <tooltipAutoShow :content="row.deptName"></tooltipAutoShow>
        </template>
        <template #areaPath="{ row }">
          <tooltipAutoShow :content="formaterAreaPath(row.areaPath)"></tooltipAutoShow>
        </template>
        <template #action="{ row }">
          <LinkBtn size="small" @click="handleDetail(row.id, row.typeId, row.objId)">详情</LinkBtn>
          <LinkBtn size="small" @click="handleOperation(row.id)">运营</LinkBtn>
        </template>
      </baseTable>
    </TableContentCard>
  </ContentCard>
  <Operation ref="operationRef"></Operation>
    <setModal ref="setR" />
</template>

<style lang="less" scoped></style>
