<template>
    <Modal
        v-model="showFlag"
        title="标椎设置"
        :width="400"
        :mask-closable="true"
        @on-visible-change="visibleChange"
    >
        <div>
            <Form :model="form" :rules="rules" ref="setFormR" label-position="top">
                <FormItem label="本项目园区总面积" prop="landArea">
                    <InputNum v-model="form.landArea" append="㎡"></InputNum>
                </FormItem>
                <FormItem label="本项目绿地率标准" prop="landRate">
                    <InputNum v-model="form.landRate" append="%"></InputNum>
                </FormItem>
                <FormItem label="本项目绿化率标准" prop="greenRate">
                    <InputNum v-model="form.greenRate" append="%"></InputNum>
                </FormItem>
            </Form>
        </div>
        <template #footer>
            <div class="btn-box">
                <Button @click="closeModal">取消</Button>
                <Button type="primary" @click="confirm" :loading="loading">确定</Button>
            </div>
        </template>
    </Modal>
</template>

<script>
import { validateform } from '@/utils/validateform'
const defaultFormObj = {
    id: '',
    landArea: '',
    landRate: '',
    greenRate: ''
}
export default {
    name: 'setModal',
    data() {
        const bfbValidate = [
            validateform.requiredNum,
            validateform.powerNum(9, 2),
            { max: 100, message: '最大值为100', trigger: 'change,blur', type: 'number' }
        ]
        return {
            showFlag: false,
            form: this.$Util.objClone(defaultFormObj),
            rules: {
                landArea: [validateform.requiredNum, validateform.powerNum(9, 0)],
                landRate: bfbValidate,
                greenRate: bfbValidate,
            },
            loading: false,
            lastStand: this.$Util.objClone(defaultFormObj)
        }
    },
    created() {

    },
    methods: {
        confirm() {
            this.$refs['setFormR'].validate((valid) => {
                if (valid) {
                    this.loading = true
                    this.$request('/greenLandStandard/saveStandard', this.form, 'post').then(res => {
                        if (res.success) {
                            this.$Message.success('操作成功')
                            this.closeModal()
                        }
                    }).finally(() => {
                        this.loading = false
                    })
                }
            })
        },
        getData() {
            this.$request('/greenLandStandard/getLastStandard').then(res => {
                if (res.success) {
                    let obj = this.$Util.objClone(defaultFormObj)
                    for (let k in obj) {
                        if (res.data[k] || res.data[k] === 0) {
                            obj[k] = res.data[k]
                        }
                    }
                    this.lastStand = this.$Util.objClone(obj)
                    this.form = obj
                }
            })
        },
        init() {
            this.showFlag = true
            this.getData()
        },
        closeModal() {
            this.showFlag = false
        },
        visibleChange() {}
    }
}
</script>

<style lang="less" scoped>

</style>
