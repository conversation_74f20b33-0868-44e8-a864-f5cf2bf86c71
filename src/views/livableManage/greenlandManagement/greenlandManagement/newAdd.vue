<script lang="ts" setup>
import {
    getAllGreenLandType,
    greenManagementNewAdd,
} from '@/api/livableManage/greenlandManagementService';
import { ComponentInfo } from '@/api/fireHydrantService';
import {
    computed,
    getCurrentInstance,
    reactive,
    ref,
} from 'vue';
import { useRouter } from 'vue-router';
import DrawArea
    from './components/drawArea.vue';
import AreaSelectTree from '@/components/global/tree/areaSelectTree.vue';
import { validateform } from '@/utils/validateform';
import DictDropDownSelect from '@/components/global/dictDropDownSelect';
import AreaSelectTreeNoDevice from '@/components/global/tree/areaSelectTreeNoDevice.vue';

const rule = reactive([
    {
        pattern: /^([1-9]\d{0,4})(\.\d{1,2})?$/,
        message: '只能输入数字，最多五位整数，两位小数',
        trigger: 'blur',
    },
]);
const addRule = reactive({
    greenLandNo: validateform.required,
    greenLandName: validateform.required,
    areaLocation: validateform.required,
    greenLandArea: rule,
    greeningArea: rule,
    contactPhone: validateform.checkPhone
});
// 获取部件信息
const deviceSelect = ref<any>();

function bindObjInfo() {
    deviceSelect.value.showModal();
}

const readonly = ref<boolean>(false);

function clearObjId() {
    objInfo.value = {};
}

// 获取部件信息
const formParams = ref<any>({
    greenLandName: '',
    painArea: '',
    deviceExtendInfo: {
        szjd: '',
        szsq: '',
        szdywg: ''
    }
});
const objInfo = ref<ComponentInfo | {}>({});

function addSubmit(info: ComponentInfo) {
    objInfo.value = info;
    readonly.value = true;
}

// 确定添加
let selectObj = ref();
const that = getCurrentInstance()?.appContext.config.globalProperties;
const addCom = ref();
const router = useRouter();

function handleConfirm() {
    if (selectObj.value.validate()) {
        selectObj.value.validate(async(valid: Boolean) => {
            if (valid) {
                formParams.value = {
                    ...formParams.value,
                    ...objInfo.value,
                };
                if (formParams.value.areaLocation) {
                    const arr: Array<string> = formParams.value.areaLocation.split('/')
                    formParams.value.szjd = arr[0]
                    formParams.value.szsq = arr[1]
                    formParams.value.szdywg = arr[2]
                }
                let res: any = await greenManagementNewAdd(formParams.value);
                console.log(res);
                if (res.success) {
                    that?.$Message.success('新增成功');
                    router.back();
                }
            }
        });
    }
}

// 查询所有类型
const TypeList = ref<Array<any>>([]);

async function getAllType() {
    let res: any = await getAllGreenLandType();
    if (res.success) {
        TypeList.value = res.data;
    }
}

getAllType();
const onBack = () => {
    router.back();
};

function handleChange(form: ComponentInfo | {}) {
    console.log(form);
    objInfo.value = form;
}
// 绘制区域弹窗相关

const isShowDialog = ref(false)
const drawAreaRef = ref()
const onCloseModal = () => {
    isShowDialog.value = false;
}
const onConfirmModal = () => {
    const mapData = drawAreaRef.value?.handleConfirm();
    const path = mapData?._opts?.path;
    if (mapData) {
        painAreaPath.value = path
    }
    isShowDialog.value = false
}
const polyArea = (circuit:any) => {
    isShowDialog.value = true;
}
const painAreaPath = computed({
    get() {
        const points = formParams.value.points;;
        // return points?.split(';')?.map((item: string) => item?.split(',')) || []
        return points?JSON.parse(points) : []
    },
    set(newVal) {
        // detailInfo.points = newVal ? newVal?.map((item:any[]) => item?.join(','))?.join(';') : '';
        formParams.value.points= JSON.stringify(newVal);
    }
})

const handlerAreaPath = computed({
    get() {
        const { szjd, szsq, szdywg } = formParams.value.deviceExtendInfo
        if (!szjd) return '';
        const arr = [szjd, szsq, szdywg]
        return arr.join('/')
    },
    set(newValue) {
        const arr: Array<string> = newValue?.split('/')
        formParams.value.deviceExtendInfo.szjd = arr[0]
        formParams.value.deviceExtendInfo.szsq = arr[1]
        formParams.value.deviceExtendInfo.szdywg = arr[2]
    }
})
const greeningProjectStatusOption = [
    '已移交绿化项目',
    '未移交绿化项目',
    '在建绿化项目',
    '拟建绿化项目'
]
</script>
<template>
    <BreadcrumbCustom>
        <BreadcrumbItem>绿地管理</BreadcrumbItem>
        <BreadcrumbItem>新建</BreadcrumbItem>
    </BreadcrumbCustom>
    <Form ref="selectObj" label-position="top" :rules="addRule" :model="formParams">
        <detailCard title="新建" :is-back-btn="true" @on-back="onBack">
            <Row gutter="80">
<!--                唯一-->
                <Col span="8">
                    <FormItem label="绿地编号" prop="greenLandNo" required>
                        <Input clearable maxlength="20" placeholder="请输入内容" v-model="formParams.greenLandNo"></Input>
                    </FormItem>
                </Col>
            </Row>
            <Row gutter="80">
                <Col span="8">
<!--                    唯一-->
                    <FormItem label="绿地名称" prop="greenLandName" required>
                        <Input clearable maxlength="20" placeholder="请输入内容" v-model="formParams.greenLandName"></Input>
                    </FormItem>
                </Col>
                <Col span="8">
<!--                    下拉选择，树形结构-->
                    <FormItem label="区域位置" prop="areaLocation" required>
<!--                        <AreaSelectTree v-model="formParams.areaLocation" table-name="livable_green_land" type="0" />-->
                        <AreaSelectTreeNoDevice v-model="formParams.areaLocation" />
<!--                        <Input clearable maxlength="20" placeholder="请输入内容" :model-value="formParams.areaPath"></Input>-->
                    </FormItem>
                </Col>
                <Col span="8">
                    <FormItem label="管理部门" prop="deptName">
                        <Input clearable maxlength="20" placeholder="请输入内容" v-model="formParams.deptName"></Input>
                    </FormItem>
                </Col>
                <Col span="8">
                    <FormItem label="养护单位" prop="opEnterpriseName">
                        <Input clearable maxlength="20" placeholder="请输入内容" v-model="formParams.opEnterpriseName"></Input>
                    </FormItem>
                </Col>
                <Col span="8">
                    <FormItem label="联系人" prop="contactPerson">
                        <Input clearable maxlength="20" placeholder="请输入内容" v-model="formParams.contactPerson"></Input>
                    </FormItem>
                </Col>
                <Col span="8">
<!--                    电话长度校验-->
                    <FormItem label="联系电话" prop="contactPhone">
                        <Input clearable placeholder="请输入内容" v-model="formParams.contactPhone"></Input>
                    </FormItem>
                </Col>
                <Col span="8">
<!--                    点击弹出地图-->
                    <FormItem label="绘制区域" prop="points">
                        <Input
                            @click="polyArea"
                            :model-value="painAreaPath"
                            placeholder="前往地图进行区域绘制"
                        >
                            <template #suffix>
<!--                                <img class="point-map-icon" src="../images/Vector.png" alt="" />-->
                            </template>
                        </Input>
                    </FormItem>
                </Col>
            </Row>
            <Title level="5" class="title">绿地信息</Title>
            <Row gutter="80">
                <Col span="8">
                    <FormItem label="绿地类型" prop="typeId">
                        <Select
                            :transfer="false"
                            v-model="formParams.typeId"
                            placeholder="请选择"
                            clearable
                        >
                            <Option
                                v-for="(item, index) in TypeList"
                                :key="index"
                                :value="item.id"
                            >
{{ item.name }}
                            </Option
                            >
                        </Select>
                    </FormItem>
                </Col>
                <Col span="8">
                    <FormItem label="绿化项目状态" prop="projectStatus">
                        <dict-drop-down-select code="greenLand_project_status" v-model="formParams.projectStatus" />
                    </FormItem>
                </Col>
                <Col span="8">
                    <FormItem label="绿地面积" prop="greenLandArea">
                        <Input
                            v-model="formParams.greenLandArea"
                            placeholder="请输入"
                            clearable
                        >
                            <template #append> ㎡</template>
                        </Input>
                    </FormItem>
                </Col>
                <Col span="8">
                    <FormItem label="绿化面积" prop="greeningArea">
                        <Input v-model="formParams.greeningArea" placeholder="请输入" clearable>
                            <template #append> ㎡</template>
                        </Input
                        >
                    </FormItem>
                </Col>
                <Col span="8">
                    <FormItem label="备注">
                        <Input clearable
                            v-model="objInfo.remark"
                            type="textarea"
                            placeholder="请输入"
                        ></Input>
                    </FormItem>
                </Col>
            </Row>
            <div class="btn">
                <Button type="primary" @click="handleConfirm">提交</Button>
                <Button @click="onBack">取消</Button>
</div>
        </detailCard>
<!--        <detailCard title="基本信息" :is-back-btn="false">-->
<!--            <div class="btn">-->
<!--                <Button @click="onBack">取消</Button>-->
<!--                <Button type="primary" @click="handleConfirm">提交</Button>-->
<!--            </div>-->
<!--        </detailCard>-->
    </Form>
    <Modal v-if="isShowDialog" v-model="isShowDialog" :width="800" title="绘制区域">
        <DrawArea :path="painAreaPath" ref="drawAreaRef"></DrawArea>
        <template #footer>
            <Button @click="onCloseModal">取消</Button>
            <Button type="primary" @click="onConfirmModal">确定</Button>
        </template>
    </Modal>
</template>

<style lang="less" scoped>
.btn {
    .ivu-btn+.ivu-btn{
        margin-left: 8px;
    }
}

#baseInfo {
    position: relative;
    height: 40px;

    span {
        position: absolute;
        left: -14px;
        font-weight: 600;
        font-size: 14px;
        line-height: 24px;
        color: #1e2a55;
    }
}
.title{
    font-style: normal;
    width: 63px;
    height: 24px;
    font-size: 14px;
    line-height: 24px;
    font-weight: 600;
    display: flex;
    align-items: center;
    color: #1E2A55;
}
</style>
