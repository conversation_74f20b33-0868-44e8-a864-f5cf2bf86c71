<template>
    <div>
        <BreadcrumbCustom>
            <BreadcrumbItem>绿地管理</BreadcrumbItem>
            <BreadcrumbItem>绿地概览</BreadcrumbItem>
        </BreadcrumbCustom>
        <Card dis-hover :bordered="false" class="map-cont" id="fullscreen">
            <div class="face-plate-cont">
                <div class="left-plate">
                    <div class="area-tree plate-bg">
                        <map-area-tree-select type="2" methodUrl="getTreeNoDevice" :is-new-inter-face="true" @updateAreaPath="updateAreaPath" table="livable_green_land" />
                    </div>
                    <div class="device-cont plate-bg">
                        <Input suffix="ios-search" placeholder="请输入绿地信息" clearable v-model="searchText" />
                        <scorll-box :scorll-id="activedObjId" key="id">
                            <template #cont>
                                <map-device-list :deviceList="deviceList" :searchText="searchText" lableKey="objName"
                                    valueKey="greenLandNo" :activedObjId="activedObjId" @actived-obj="handleActivedObj"
                                    :showTag="[]" />
                            </template>
                        </scorll-box>
                    </div>
                </div>
                <div class="right-plate">
                    <div class="full-op" @click="handleFullScreen">
                        <Icon :type="fullFlag ? 'md-contract' : 'md-expand'" />
                    </div>
                    <div class="device-info-cont">
                        <s-tab :tab-list="tabList" class="plate-bg" tit-class="right-tab-title"
                            :body-class="`${isToggleDropdown ? '' : 'hide-tab-box'} right-tab-body`"
                            @handleChange="handleChange" :default-active="defaultActive">
                            <template #expand>
                                <div class="toggle" @click="isToggleDropdown = !isToggleDropdown">
                                    <Icon :type="isToggleDropdown ? 'ios-arrow-down' : 'ios-arrow-up'" />
                                </div>
                            </template>
                            <template #overview>
                                <div class="scorll-map-cont">
                                    <device-oview :area-paths="areaPaths" ref="overviewRef" />
                                </div>
                            </template>
                            <template #detail>
                                <device-detail :actived-obj-id="activedObjId" />
                            </template>
                        </s-tab>
                    </div>
                </div>
            </div>
            <div id="container" class="container-map" :class="fullFlag ? 'full-map' : ''"></div>
        </Card>
    </div>
</template>

<script lang="ts">
import AMapLoader from '@amap/amap-jsapi-loader';
import { defineComponent, nextTick, onMounted, ref, Ref, watch } from 'vue';
import { installationInfo } from '@/api/manholeCoverService';
import { getGreenManagementList } from '@/api/livableManage/greenlandManagementService'
import DeviceOview from './components/deviceOview.vue';
import DeviceDetail from './components/deviceDetail.vue';
import { launchIntoFullscreen, exitFullscreen } from '@/utils/tool'
import scorllBox from '@/components/common/scorllBox/index'
import mapAreaTreeSelect from '@/components/common/mapAreaTreeSelect/index'
import mapDeviceList from '@/components/common/mapDeviceList'
export default defineComponent({
    components: {
        DeviceOview,
        DeviceDetail,
        mapAreaTreeSelect,
        scorllBox,
        mapDeviceList
    },
    setup(props, ctx) {
        // *********************
        // 地图
        // *********************

        // 初始化图标
        const objIconImgs = ['icon_greenland_005.png', 'icon_greenland_006.png', 'icon_greenland_007.png']
        const map = ref<any>(null);
        const Amap = ref<any>(null);
        const iconList = ref<any>([])
        const areaList = ref<any[]>([])
        const ininMap = () => {
            AMapLoader.load({
                key: 'ea53a5291f8f7c1f215ba20930ea9866',
                version: '2.0'
            }).then((AMap) => {
                Amap.value = AMap
                map.value = new AMap.Map('container', {
                    resizeEnable: true,
                    // mapStyle: 'https://geohub.amap.com/mapstyle/clone?id=20d3f4f1e43c80b9c68c30fb8a1ab94b',
                    zoom: 16,
                    center: [114.61, 30.45],
                })

                // 创建普图标
                // objIconImgs.forEach(e => {
                //     const image = require('./images/' + e);
                //     iconList.value.push(
                //         new AMap.Icon({
                //             size: new AMap.Size(40, 40),
                //             image,
                //             imageSize: new AMap.Size(40, 40),
                //             imageOffset: new AMap.Pixel(0, 0)
                //         })
                //     )
                // })
                nextTick(() => {
                    getManholeList([])
                })
            })
        }
        // 更新地图上的区域
        const updateMapIcon = (data: installationInfo[]) => {
            console.log('updateMapIcon', activedObjId.value)
            // 清除地图上的区域
            if (!map.value) return
            map.value.clearMap()
            data.forEach((ele: any) => {
                if (!ele.points) return
                // const path = ele.points?.split(';')?.map((item: string) => item?.split(',').map(i => Number(i))) || []
                const path = ele.points?JSON.parse(ele.points) : []
                const area = new Amap.value.Polygon({
                    ...defualtStyles[0],
                    path: path,
                    // offset: new Amap.value.Pixel(-5, -35),
                    extData: ele.id,
                })
                areaList.value.push(area);

                area.on('click', (e: any) => {
                    if (activedObjId.value && activedObjId.value == e.target.getExtData()) {
                        // e.target.setIcon(iconList.value[0])
                        activedObjId.value = 0
                        defaultActive.value = 0
                        return
                    }
                    // e.target.setIcon(iconList.value[2])
                    defaultActive.value = 1
                    activedObjId.value = e.target.getExtData()
                })
                area.on('mouseout', (e: any) => {
                    if (activedObjId.value == e.target.getExtData()) return
                    e.target.setOptions({ ...defualtStyles[0] })
                })
                area.on('mouseover', (e: any) => {
                    if (activedObjId.value == e.target.getExtData()) return
                    e.target.setOptions({ ...defualtStyles[1] })
                })
                map.value.add(area)
            })
            nextTick(() => {
                map.value.setFitView();
            })

        }
        onMounted(() => {
            ininMap();
            window.addEventListener('fullscreenchange', e => {
                // 监听到屏幕变化，在回调中判断是否已退出全屏
                const isFull = document.fullscreenElement || document['mozFullScreenElement'] || document['webkitFullscreenElement'];
                fullFlag.value = isFull ? true : false
            })
        })
        // *********************
        // 左侧区域树
        // *********************
        // 搜索树
        const areaPaths = ref<string[]>([])
        // 更新当前勾选区域
        const updateAreaPath = (areaPath: string[]) => {
            areaPaths.value = areaPath
            // 查询当前区域的绿地
            getManholeList(areaPath)
            defaultActive.value = 0
        }
        const getPolygonCenter = (polygon:number[][]) => {
            let sumX = 0, sumY = 0;
            for (let i = 0; i < polygon.length; i++) {
                sumX += Number(polygon[i][0]);
                sumY += Number(polygon[i][1]);
            }
            const centerX = Number((sumX / polygon.length).toFixed(6));
            const centerY = Number((sumY / polygon.length).toFixed(6));
            return [centerX, centerY];
        }
        // *********************
        // 左侧绿地列表
        // *********************
        // 绿地搜索关键字
        const searchText = ref<string>('')
        // 选中的绿地
        const activedObjId = ref<number>(0)
        // 监听选中的绿地
        watch(activedObjId, () => {
            const activeDevice = deviceList.value.find(k => k.id == activedObjId.value)

            areaList.value?.forEach(e => {
                if (e.getExtData() == activedObjId.value) {
                    e.setOptions({ ...defualtStyles[2] })
                    map.value?.setFitView([e])
                } else {
                    e.setOptions({ ...defualtStyles[0] })
                }
            })
            if (activeDevice?.points) {
                // const objPoints = activeDevice.points.split(';')?.map((item: string) => item?.split(',')) || []'
                const objPoints = JSON.parse(activeDevice.points) || [];
                // const [centerX, centerY] = getPolygonCenter(objPoints);
                // map.value?.setZoomAndCenter(14, [centerX, centerY])
                nextTick(() => {
                    map.value.setFitView(objPoints);
                })
            }

            const overlays: any[] = map.value?.getAllOverlays('marker')
            overlays?.forEach(e => {
                if (e.getExtData() == activedObjId.value) {
                    e.setIcon(iconList.value[2])
                } else {
                    e.setIcon(iconList.value[0])
                }
            })
            if (activedObjId.value) {
                isToggleDropdown.value = true
            }
        })
        // 设备列表
        const deviceList = ref<any[]>([])
        // 手动设置当前选中设备
        const handleActivedObj = (item: any) => {
            activedObjId.value = (activedObjId.value == item.id) ? 0 : (item.id || 0)
            if (activedObjId.value) {
                defaultActive.value = 1
                return
            }
            defaultActive.value = 0
        }
        // 查询当前区域的绿地
        const getManholeList = async (areaPaths?: string[]) => {
            const params = {
                page: {
                    current: 1,
                    size: -1
                },
                customQueryParams: {
                    areaPaths: areaPaths
                }
            }
            const res = await getGreenManagementList(params)
            const { data, success }: { data: recordsResponse, success: boolean } = res as unknown as HttpResponse<recordsResponse>
            if (success) {
                deviceList.value = (data.records as any[]).map(k => ({ ...k, objName: k.greenLandName }));
                // 更新地图上的图标
                updateMapIcon(deviceList.value)
                //默认勾选的设备
                nextTick(() => {
                    activedObjId.value = deviceList.value[0].id || 0
                    if (activedObjId.value) {
                        const actived_Obj = deviceList.value.filter(k => k.id == activedObjId.value)
                        if (!actived_Obj.length) activedObjId.value = 0
                    }
                })

            }
        }

        // 地图全屏
        const fullFlag: Ref<boolean | null> = ref(false)
        const handleFullScreen = () => {
            const element = document.getElementById('fullscreen');
            if (!element) return
            // false是进入全屏状态
            if (fullFlag.value) {
                // 退出全屏
                exitFullscreen(element);
            } else {
                launchIntoFullscreen(element);
            }
            fullFlag.value = !fullFlag.value
        }
        // 是否展开右侧
        const isToggleDropdown = ref<boolean>(true)
        watch(() => isToggleDropdown.value, (val) => {
            if (val) {
                overviewRef.value.handleResize()
            }
        })
        const tabList = ref([
            {
                name: '绿地概览',
                key: 'overview',
                icon: 'dashboard'
            },
            {
                name: '绿地详情',
                key: 'detail',
                icon: 'common'
            }
        ])
        // 概览ref
        const overviewRef = ref()
        const defaultActive = ref<number>(0)
        // 切换回需要resize
        const handleChange = (i: number) => {
            if (i == 0) {
                overviewRef.value.handleResize()
            }
            defaultActive.value = i
            isToggleDropdown.value = true
        }

        // 区域范围3个样式
        const defualtStyles = [
            {
                strokeColor: "#165DFF",
                strokeWeight: 1.5,
                fillColor: 'rgba(190, 218, 255, 0.6)',
                zIndex: 50,
                cursor: 'pointer'
            },
            {
                strokeColor: "#4080FF",
                strokeWeight: 1.5,
                fillColor: '#94BFFF',
                zIndex: 50,
                cursor: 'pointer'
            },
            {
                strokeColor: "#0E42D2",
                strokeWeight: 1.5,
                fillColor: 'rgba(64, 128, 255, 0.6)',
                zIndex: 50,
                cursor: 'pointer'
            }
        ];
        return {
            deviceList,
            tabList,
            areaPaths,
            activedObjId,
            searchText,
            handleFullScreen,
            fullFlag,
            isToggleDropdown,
            updateAreaPath,
            handleChange,
            overviewRef,
            defaultActive,
            handleActivedObj

        };
    }
});
</script>

<style lang="less" scoped>
@import '../../../../styles/mapPage.less';
@import './index.less';
</style>
