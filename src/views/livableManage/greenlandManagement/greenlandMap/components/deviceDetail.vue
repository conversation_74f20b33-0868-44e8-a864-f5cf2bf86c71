<template>
    <div class="details-container">
        <div class="head-cont normal">
            <div class="icon-img"><img src="../images/icon_greenland_004.png" class="top-icon"/></div>
        </div>
        <div class="name">
            {{ deviceDetail?.greenLandName }}
        </div>
        <div class="code">
            {{ deviceDetail?.greenLandNo }}
        </div>
        <no-data v-if="!activedObjId" value="请先选择绿地" />
        <div class="scorll-map-cont">
            <div class="device-info-list">
<!--                <s-label label="绿地状态" :value="enumeration.objState[deviceDetail?.objInfo?.objState || 0]" />-->
                <s-label label="绿地类型" :value="deviceDetail?.typeName" />
                <s-label label="养护单位" :value="deviceDetail?.opEnterpriseName" />
                <s-label label="区域位置" :value="deviceDetail?.areaPath?.replace(/@/g, '/')" />
<!--                <s-label label="权属单位" :value="deviceDetail?.objInfo?.ownerEnterpriseName" />-->
                <s-label label="责 任 人" :value="deviceDetail?.contactPerson || '-'" />
                <s-label label="联系电话" :value="deviceDetail?.contactPhone || '-'" />
            </div>
            <div class="alarm-info-list">
                <div class="bridge-gather">
                    <div>
                        <div class="icon-name"><img src="../images/icon_greenland_002.png" />绿地面积</div>
                        <div class="percent-box">
                            <div class="gather-num">
                                {{ creaCount['greenLandTotalArea'] || '--' }}
                                <span>m²</span>
                            </div>
                            <div>环比<span class="percent-num primary-text">{{ `${creaCount['greenLandMOM'] != null ? creaCount['greenLandMOM'] : '--'}%`
                            }}</span></div>
                            <div>同比<span class="percent-num green-text">{{ `${creaCount['greenLandYOY'] != null ? creaCount['greenLandYOY'] : '--'}%` }}</span>
                            </div>
                        </div>

                    </div>
                    <div>
                        <div class="icon-name"><img src="../images/icon_greenland_001.png" />绿化面积</div>
                        <div class="percent-box">
                            <div class="gather-num">
                                {{ creaCount['greeningLandTotalArea'] || '--' }}
                                <span>m²</span>
                            </div>
                            <div>环比<span class="percent-num primary-text">{{ `${creaCount['greeningMOM'] != null ? creaCount['greeningMOM'] : '--'}%` }}</span>
                            </div>
                            <div>同比<span class="percent-num green-text">{{ `${creaCount['greeningYOY'] != null ? creaCount['greeningYOY'] : '--'}%` }}</span>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
<script lang="ts">
import { defineComponent, ref, watch } from 'vue'
import { creaCountResInfo, ManholeCoverInfo } from '@/api/manholeCoverService'
import { getGreenManagementDetail } from '@/api/livableManage/greenlandManagementService'
import { arrayToObject } from '@/utils/tool'
import { enumeration } from '@/config/enumeration'
export default defineComponent({
    props: {
        activedObjId: {
            type: Number,
            default: ''
        }
    },
    setup(props, ctx) {
        // 路灯详细
        interface deviceExtendInfo extends ManholeCoverInfo {
            countDTOList?: creaCountResInfo[];
        }
        // 设备详情
        const deviceDetail = ref<any>()
        const creaCount = ref<any>({
            greenLandTotalNum: 0,
            greeningYOY: null,
            greeningLandTotalArea: 0,
            greenLandMOM: null,
            greeningMOM: null,
            greenLandYOY: 0,
            greenLandTotalArea: 0
        })
        const getManholeDetail = async () => {
            if (!props.activedObjId) {
                deviceDetail.value = {
                    objInfo: {},
                    extendInfo: {},
                    countDTOList: []
                }
                return
            }
            const res = await getGreenManagementDetail(props.activedObjId)
            const { data, success }: { data: deviceExtendInfo, success: boolean } = res as unknown as HttpResponse<deviceExtendInfo>
            if (success) {
                deviceDetail.value = data
                creaCount.value = arrayToObject(data?.countDTOList || [], 'countKey', 'countValue')

            }
        }
        watch(() => props.activedObjId, () => {
            getManholeDetail()

        })
        return {
            deviceDetail,
            creaCount,
            enumeration
        }
    }
})
</script>
