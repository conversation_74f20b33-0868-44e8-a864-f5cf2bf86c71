<template>
    <div class="bridge-total-num">
        <img src="../images/icon_greenland_003.png" />
        <div class="bridge-num">
            绿地总数
            <div class="num">
                {{ creaCount['greenLandTotalNum'] }}
            </div>
        </div>
    </div>
    <div class="bridge-gather">
        <div>
            <div class="icon-box"><img src="../images/icon_greenland_002.png" /> 绿地面积</div>
            <div class="gather-num">
                {{ creaCount['greenLandTotalArea'] || '--' }}
                <span>m²</span>
            </div>
            <div class="percent-box">
                <div>环比<span class="percent-num primary-text">{{ `${creaCount['greenLandMOM'] != null ?
                    creaCount['greenLandMOM'] : '--'}%` }}</span></div>
                <div>同比<span class="percent-num green-text">{{ `${creaCount['greenLandYOY'] != null ?
                    creaCount['greenLandYOY'] : '--'}%` }}</span></div>
            </div>

        </div>
        <div>
            <div class="icon-box"><img src="../images/icon_greenland_001.png" />绿化面积</div>
            <div class="gather-num">

                {{ creaCount['greeningLandTotalArea'] || '--' }}
                <span>m²</span>
            </div>
            <div class="percent-box">
                <div>环比<span class="percent-num primary-text">{{ `${creaCount['greeningMOM'] != null ?
                    creaCount['greeningMOM'] : '--'}%` }}</span></div>
                <div>同比<span class="percent-num green-text">{{ `${creaCount['greeningYOY'] != null ?
                    creaCount['greeningYOY'] : '--'}%` }}</span></div>
            </div>

        </div>
    </div>
    <div class="plate-tit">
        <img src="../images/icon_bridge_015.png" class="title-icon" />
        绿地类型
    </div>
    <div class="device-echart">
        <echart-item :option="pieOption" class="pie-echart" ref="pieRef" />
    </div>
    <div class="alarm-overview">
        <div class="alarm-list">
            <div class="alarm-item" v-for="(item, index) in pieOption.series![0].data" :key="index">
                <div>
                    <span class="point" :style="{ 'background': item.color }"></span>
                    <Tooltip :content="item.name" :max-width="200" placement="top" :disabled="(item.name?.length || 0) < 6">
                        {{ item.name }}
                    </Tooltip>
                </div>
                {{ item.percent }}
                <span>{{ item.value }}</span>
            </div>
        </div>
    </div>
    <div class="plate-tit">
        <img src="../images/icon_bridge_014.png" class="title-icon" />
        运营统计
    </div>
    <div class="online-overview">
        <no-data v-if="!lineOption.xAxis![0].data.length" />
        <echart-item :option="lineOption" class="pie-echart" v-else ref="lineRef" />
    </div>
</template>
<script lang="ts">
import { defineComponent, ref, watch } from 'vue'
import { creaCountResInfo } from '@/api/manholeCoverService';
import { greenLandMapAreaCount, countGroupByType, operatortionRecordsCount } from '@/api/livableManage/greenlandManagementService'
import EchartItem from '@/components/common/EchartItem/index'
import { ECOption } from '@/components/common/EchartItem/echart';
import { arrayToObject } from '@/utils/tool'
import { enumeration } from '@/config/enumeration'
export default defineComponent({
    components: {
        EchartItem
    },
    props: {
        areaPaths: {
            type: Array,
            default: () => []
        }
    },
    setup(props, ctx) {
        // *********************
        // 右侧区域基础数据概览
        // *********************

        // 该区域的顶部数据概览
        const creaCount = ref<any>({
            greenLandTotalNum: 0,
            greeningYOY: 0,
            greeningLandTotalArea: 0,
            greenLandMOM: 0,
            greeningMOM: 0,
            greenLandYOY: 0,
            greenLandTotalArea: 0
        })
        // 查询该区域的概览
        const getGreenLandMapAreaCount = async () => {
            const params = {
                areaPaths: props.areaPaths
            }
            const res = await greenLandMapAreaCount(params)
            const { data, success }: { data: creaCountResInfo[], success: boolean } = res as unknown as HttpResponse<creaCountResInfo[]>
            if (success) {
                creaCount.value = arrayToObject(data, 'countKey', 'countValue')
            }
        }

        // *********************
        // 右侧区域道路等级概览
        // *********************
        // 在线饼状图
        const pieOption = ref<ECOption>({
            tooltip: {
                trigger: 'item',
                confine: true
            },
            legend: {
                show: false
            },
            color: ['#249EFF', '#846BCE', '#21CCFF', '#86DF6C', '#0E42D2'],
            series: [
                {
                    name: '绿地类型',
                    type: 'pie',
                    radius: ['48%', '68%'],
                    center: ['50%', '50%'],
                    avoidLabelOverlap: false,
                    emphasis: {
                        label: {
                            show: true,
                            fontSize: 14,
                            fontWeight: 'bold'
                        }
                    },
                    labelLine: {
                        show: true
                    },
                    label: {
                        show: true,
                        position: 'outside',
                        formatter: '{d}%'
                    },
                    data: []
                }
            ]
        })

        // 查询等级
        const getCountGroupByType = async () => {
            const params = {
                areaPaths: props.areaPaths
            }
            const res = await countGroupByType(params)
            const { data, success }: { data: creaCountResInfo[], success: boolean } = res as unknown as HttpResponse<creaCountResInfo[]>
            if (success) {
                const total = data.map(k => k.countValue)
                pieOption.value.series![0].data = data.filter(k => k.countName).map((k, i) => {
                    return {
                        color: ['#249EFF', '#846BCE', '#21CCFF', '#86DF6C', '#0E42D2'][i],
                        name: k.countName,
                        value: k.countValue,
                        percent: total ? `${(k.countValue * 100 / eval(total.join("+"))).toFixed(2)}%` : '0%'
                    }
                })
            }
        }

        // *********************
        // 右侧区域养护趋势概览
        // *********************
        // 在线折线图
        const lineOption = ref<ECOption>({
            tooltip: {
                trigger: 'axis',
                confine: true,
                axisPointer: {}
            },
            grid: {
                top: 20,
                bottom: 20,
                left: 0,
                right: 0,
                containLabel: true
            },
            xAxis: [
                {
                    type: 'category',
                    axisLabel: {
                        color: '#4E568C'
                    },
                    axisLine: {
                        show: false
                    },
                    axisTick: {
                        show: false
                    },
                    data: []
                }
            ],
            yAxis: {
                type: 'value',
                min: 0,
                minInterval: 1,
                axisLine: {
                    show: false
                },
                axisTick: {
                    show: false
                }
            },
            color: ['#4080FF'],
            series: [
                {
                    data: [],
                    name: '运营记录',
                    type: 'line',
                    smooth: true,
                    showSymbol: false
                }
            ]
        })

        // 查询养护趋势
        const getOperatortionRecordsCount = async () => {
            const params = {
                areaPaths: props.areaPaths
            }
            const res = await operatortionRecordsCount(params)
            const { data, success }: { data: creaCountResInfo[], success: boolean } = res as unknown as HttpResponse<creaCountResInfo[]>
            if (success) {
                lineOption.value.xAxis![0].data = data.map(k => k.countKey)
                lineOption.value.series![0].data = data.map(k => {
                    return {
                        name: k.countKey,
                        value: k.countValue,
                    }
                })
            }
        }
        //监听区域变化
        watch([props], () => {
            getGreenLandMapAreaCount() // 顶部汇总数字
            getCountGroupByType() // 等级
            getOperatortionRecordsCount() //运营统计
        }, { immediate: true })
        const pieRef = ref()
        const lineRef = ref()
        const handleResize = () => {
            pieRef.value.handleResize()
            lineRef.value.handleResize()
        }
        return {
            pieOption,
            lineOption,
            creaCount,
            enumeration,
            handleResize,
            pieRef,
            lineRef
        }
    }
})
</script>
<style lang="less" scoped>
/deep/.ivu-tooltip {
    height: 40px;
    flex: 1;
    overflow: hidden;

    .ivu-tooltip-rel {
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        line-height: 40px;
        width: 100%;
    }
}
</style>
