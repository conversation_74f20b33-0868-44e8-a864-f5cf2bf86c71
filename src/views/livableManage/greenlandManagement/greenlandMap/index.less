.device-info-cont {
    /deep/.right-tab-body {
        padding-bottom: 12px;
        .scorll-map-cont {
            margin-right: 4px;
            padding-top: 8px;
            padding-left: 16px;
        }
        .device-echart {
            height: 214px;
        }
        .online-overview {
            height: 182px;
        }
        .alarm-info {
            height: 100px;
        }
        .alarm-list {
            margin-bottom: 16px;
            .alarm-item {
                grid-template-columns: 50% 30% 20%!important;
                width: 100%;
                padding: 0 8px 0 0;
                &>div {
                    display:flex;
                    align-items: center;
                    overflow: hidden;
                }
                .point {
                    width: 10px;
                    height: 10px;
                    border-radius: 100%;
                    display: inline-block;
                    margin-right: 10px;
                    display: block;
                }
                span {
                    &:last-child {
                        // text-align: right;
                    }
                }
            }
        }
        .bridge-total-num {
            display: flex;
            padding: 0px 8px 8px 0px;
            align-items: center;
            img {
                display: block;
                width: 42px;
                height: 42px;
                margin-right: 20px;

            }
            .bridge-num {
                color: @text-color;
                font-weight: 400;
                font-size: 16px;
                line-height: 24px;

                .num {
                    color: @title-color;
                    font-weight: 700;
                    font-size: 24px;
                    line-height: 32px;
                }
            }
        }
        .bridge-gather {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            column-gap: 26px;
            line-height: 22px;
            color: @text-color;
            padding-bottom: 24px;
            position: relative;
            white-space: nowrap;
            height: 134px;
            .icon-box {
                display: flex;
                column-gap: 2px;
                align-items: center;
            }
            &:after {
                position: absolute;
                content: '';
                height: 54px;
                width: 1px;
                background: #E5E6EB;
                left: ~'calc(50% - 13px)';
                top: 20px;
            }
            img  {
                display: block;
                height: 18px;
                margin-right: 4px;
            }
            .gather-num {
                display: flex;
                align-items: flex-end;
                font-weight: 500;
                font-size: 16px;
                line-height: 19px;
                color: @title-color;
                margin: 8px 0px;
                span {
                    font-weight: 400;
                    font-size: 12px;
                    line-height: 14px;
                    color: @input-placeholder-color ;
                    margin-left: 5px;
                }
            }
            .percent-box {
                align-items: center;
                height: 20px;
                font-size: 12px;
                .percent-num {
                    margin-left: 8px;
                }
            }
        }
        .details-container {
            .head-cont {
                background: @primary-color!important;
                height: 56px;
                width: ~'calc(100% + 32px)';
                .name {
                    padding-top: 25px!important;
                }
            }
        }
        .code {
            margin-bottom: 8px;
        }
        .device-info-list {
            padding: 0px 8px 20px;
        }
        .alarm-info-list {
            .bridge-gather {
                display: block;
                &:after {
                    display: none;
                }
                .percent-box {
                    height: 22px;
                    display: grid;
                    grid-template-columns:repeat(3, 1fr);
                    align-content: center;
                    justify-content: space-around;
                    margin-top: 8px;
                    margin-bottom: 22px;
                }
                .icon-name {
                    display: flex;
                }
            }
        }
    }
}