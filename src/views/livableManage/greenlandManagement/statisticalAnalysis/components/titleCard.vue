<template>
    <div class="title-card-container">
        <!-- 左侧绿地数量方块 -->
        <div class="left-block">
            <div class="greenland-count-box">
                <div class="count-title">绿地数量</div>
                <div class="count-value">{{ greenlandCount }}</div>
                <div class="count-unit">个</div>
            </div>
        </div>
        
        <!-- 右侧统计卡片区域 -->
        <div class="right-stats">
            <!-- 第一行 -->
            <div class="stats-row">
                <div class="stat-item" v-for="item in statList.slice(0,4)" :key="item.title">
                    <div class="box">
                        <div class="tit">{{item.title}}</div>
                        <div class="stand-res">
                            <div class="val"><span>{{ $Util.formatNum(item.curValue) }}</span>{{ item.unit }}</div>
                            <compareBox v-if="item.curPerKey" :value="item.curRadio" />
                            <Tooltip v-if="item.tooltip" placement="right" max-width="300">
                                <Icon type="ios-help-circle-outline" />
                                <template #content>
                                    <div>{{ item.tooltip }}</div>
                                </template>
                            </Tooltip>
                        </div>
                        <div class="bot-box">
                            <div class="td">
                                <div class="name">去年同期</div>
                                <div class="val"><span>{{ $Util.formatNum(item.beforeValue) }}</span>{{ item.unit }}</div>
                            </div>
                            <div class="td">
                                <compareBox :value="item.beforeRadio" />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 第二行 -->
            <div class="stats-row">
                <div class="stat-item" v-for="item in statList.slice(4,8)" :key="item.title">
                    <div class="box">
                        <div class="tit">{{item.title}}</div>
                        <div class="stand-res">
                            <div class="val"><span>{{ $Util.formatNum(item.curValue) }}</span>{{ item.unit }}</div>
                            <compareBox v-if="item.curPerKey" :value="item.curRadio" />
                            <Tooltip v-if="item.tooltip" placement="right" max-width="300">
                                <Icon type="ios-help-circle-outline" />
                                <template #content>
                                    <div>{{ item.tooltip }}</div>
                                </template>
                            </Tooltip>
                        </div>
                        <div class="bot-box">
                            <div class="td">
                                <div class="name">去年同期</div>
                                <div class="val"><span>{{ $Util.formatNum(item.beforeValue) }}</span>{{ item.unit }}</div>
                            </div>
                            <div class="td">
                                <compareBox :value="item.beforeRadio" />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import compareBox from '@/components/common/compareBox/index'
const defaultStatList = [
    { title: '当前绿地率', unit: '%', key: 'landRateCountVoList', curKey: 'curLandRate', curPerKey: 'curLandChange', yyKey: 'yyLandRate', yyPerKey: 'yyLandChange', standKey: 'curLandStandard', tooltip: '' },
    { title: '当前绿地面积', unit: '㎡', key: 'landAreaCountVoList', curKey: 'curLandArea', yyKey: 'yyLandArea', yyPerKey: 'yyLandAreaChange' },
    { title: '本月绿地变化值', unit: '㎡', key: 'monthGreenAreaCountVoList', curKey: 'monthLandAreaChange', yyKey: 'yyMonthLandAreaChange', yyPerKey: 'yyMonthLandAreaChangeRate' },
    { title: '本年绿地变化值', unit: '㎡', key: 'yearGreenAreaCountVoList', curKey: 'yearLandAreaChange', yyKey: 'yyYearLandAreaChange', yyPerKey: 'yyYearLandAreaChangeRate' },
    { title: '当前绿化率', unit: '%', key: 'greenRateCountVoList', curKey: 'curGreenRate', curPerKey: 'curGreenChange', yyKey: 'yyGreenRate', yyPerKey: 'yyGreenChange', standKey: 'curGreenStandard', tooltip: '' },
    { title: '当前绿化面积', unit: '㎡', key: 'landAreaCountVoList', curKey: 'curGreenArea', yyKey: 'yyGreenArea', yyPerKey: 'yyGreenAreaChange' },
    { title: '本月绿化变化值', unit: '㎡', key: 'monthGreenAreaCountVoList', curKey: 'monthGreenChange', yyKey: 'yyMonthGreenAreaChange', yyPerKey: 'yyMonthGreenAreaChangeRate' },
    { title: '本年绿化变化值', unit: '㎡', key: 'yearGreenAreaCountVoList', curKey: 'yearGreenChange', yyKey: 'yyYearGreenAreaChange', yyPerKey: 'yyYearGreenAreaChangeRate' }
]
export default {
    name: 'titleCard',
    components: { compareBox },
    data() {
        return {
            statList: this.$Util.objClone(defaultStatList),
            greenlandCount: 0
        }
    },
    created() {
        this.getData()
    },
    methods: {
        getData() {
            this.$request('/greenLandAnalysis/centralIndexCount', {}, 'post').then(res => {
                let list = this.$Util.objClone(defaultStatList)
                let data = {}
                for (let i in res.data) {
                    data[i] = {}
                    //如果res.data[i]是个数组，则进行遍历
                    if (Array.isArray(res.data[i])) {
                        res.data[i].forEach(k => {
                            data[i][k.indexKey] = k.indexValue
                        })
                    }
                }
                list.forEach(item => {
                    let obj = data[item.key] || {}
                    item.curValue = obj[item.curKey]
                    item.beforeValue = obj[item.yyKey]
                    item.beforeRadio = obj[item.yyPerKey]
                    if (item.curPerKey) {
                        item.curRadio = obj[item.curPerKey]
                    }
                    if (item.standKey) {
                        item.tooltip = `${item.title}标准：${obj[item.standKey]}%`
                    }
                })
                this.statList = list
                this.greenlandCount = res.data.greenLandCount || 0
            })
        }
    }
}
</script>

<style lang="less" scoped>
.title-card-container {
    display: flex !important;
    gap: 16px !important;
    align-items: flex-start !important;
    margin-bottom: 16px;
    
    .left-block {
        flex: none !important;
        width: 180px !important;
        
        .greenland-count-box {
            background: #4CAF50 !important;
            color: #fff !important;
            height: 200px !important;
            border-radius: 8px !important;
            display: flex !important;
            flex-direction: column !important;
            align-items: center !important;
            justify-content: center !important;
            
            .count-title {
                font-size: 16px !important;
                font-weight: 600 !important;
                margin-bottom: 8px !important;
            }
            
            .count-value {
                font-size: 40px !important;
                font-weight: 700 !important;
                margin-bottom: 6px !important;
            }
            
            .count-unit {
                font-size: 14px !important;
            }
        }
    }
    
    .right-stats {
        flex: 1 !important;
        display: flex !important;
        flex-direction: column !important;
        gap: 8px !important;
        
        .stats-row {
            display: flex !important;
            gap: 16px !important;
            
            .stat-item {
                flex: 1 !important;
                
                .box {
                    background: #fff !important;
                    padding: 8px 16px !important;
                    border-radius: 8px !important;
                    height: 96px !important;
                    display: flex !important;
                    flex-direction: column !important;
                    justify-content: space-between !important;
                    margin-bottom: 0 !important;
                    
                    .tit {
                        font-weight: 600;
                        line-height: 24px;
                        margin-bottom: 4px;
                    }
                    
                    .stand-res {
                        margin-bottom: 12px;
                        display: flex;
                        align-items: center;
                        
                        .val {
                            color: @text-color;
                            font-size: 12px;
                            line-height: 20px;
                            
                            span {
                                color: @title-color;
                                font-size: 22px;
                                line-height: 28px;
                                font-weight: 700;
                            }
                        }
                        
                        .per {
                            font-size: 12px;
                        }
                        
                        .ivu-tooltip {
                            display: block;
                            margin-left: 8px;
                            
                            /deep/.ivu-tooltip-rel {
                                display: block;
                            }
                        }
                    }
                    
                    .bot-box {
                        display: flex;
                        align-items: center;
                        justify-content: space-between;
                        
                        .td {
                            display: flex;
                            align-items: center;
                            font-size: 12px;
                            line-height: 20px;
                            
                            .name {
                                color: rgba(78, 98, 126, 1);
                                margin-right: 8px;
                            }
                            
                            .val {
                                color: rgba(78, 98, 126, 1);
                                font-size: 14px;
                            }
                        }
                    }
                }
            }
        }
    }
}
</style>
