<template>
    <div class="search-box">
        <AreaSelectTree v-model="m.areaPath" @on-change="changeDevice" tableName="livable_green_land" :clearable="false" :isInitSelect="true" :hasDevice="true" class="device-sel"></AreaSelectTree>
        <dateSelectT @on-change="changeDateType" />
    </div>
</template>

<script>
import dateSelectT from '@/components/common/dateSelect/dateSelectT'
export default {
    name: 'searchBox',
    components: {
        dateSelectT
    },
    data() {
        return {
            m: {
                selectAreaLand: {},
                areaPath: '',
                date: {}
            }
        }
    },
    watch: {
    },
    created() {
    },
    methods: {
        changeDateType(obj) {
            this.m.date = obj
            this.emitChange()
        },
        changeDevice(val, item) {
            this.m.selectAreaLand = item
            this.emitChange()
        },
        emitChange() {
            if (!this.m.areaPath || !this.m.date.startTime) {
                return
            }
            let granularityType = ['day', 'month', 'year']
            let obj = {
                startTime: this.m.date.startTime,
                endTime: this.m.date.endTime,
                countType: granularityType[this.m.date.dateType],
                selectAreaLand: [
                    {
                        id: this.m.selectAreaLand.id,
                        dataType: this.m.selectAreaLand.dataType,
                        areaPath: this.m.selectAreaLand.value,
                        parentCode: this.m.selectAreaLand._parentCode
                    }
                ]
            }
            console.log(obj)
            this.$emit('on-change', obj)
        }
    }
}
</script>

<style lang="less" scoped>
.search-box{
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 8px;

    .device-sel{
        position: relative;
        width: 300px;
        margin-right: 48px;
    }

}
</style>
