<template>
    <!--绿地分布统计分析-->
    <div class="head-box">
            <searchBoxTow :device-list="[]" @on-change="search" />
        </div>
        <Title level="6" style="font-size: 14px; color:rgb(103, 105, 113);">绿地分布统计分析</Title>
    <EchartItem :option="options" style="height: 400px" />
</template>

<script>
import searchBoxTow from './searchBoxTow.vue'
import EchartItem from '@/components/common/EchartItem/index.tsx'
import { EchartsTooltip, tooltipExtraCssText } from '@/utils/tool';
export default {
    name: 'typeOfGreenSpace',
    components: {
        EchartItem,
        searchBoxTow
    },
    data() {
        return {
            options: {
                // title: {
                //     text: "绿地分布统计分析",
                //     textStyle: {
                //         fontSize: 18,
                //     }
                // },
                tooltip: {
                    trigger: "axis", // 触发类型
                    borderWidth: 0,
                    extraCssText: tooltipExtraCssText,
                    formatter: (arg) => {
                        return EchartsTooltip(arg, '㎡')
                    }
                },
                // legend: {
                //     icon: "circle",
                //     itemHeight: 8,
                //     itemWidth: 8,
                // },
                grid: {
                    left: 10,
                    right: 10,
                    bottom: 5,
                    top: 10,
                    containLabel: true,
                },
                xAxis: [
                    {
                        type: "category", // 坐标轴类型
                        data: [],
                        axisLabel: {
                            textStyle: {
                                color: "#798799" // 坐标轴文字颜色
                            }
                        },
                        axisLine: {
                            show: true,
                            lineStyle: {
                                color: "#E0E6F1" // 坐标轴线颜色
                            }
                        },
                        axisTick: {
                            show: false
                        }
                    }
                ],
                yAxis: [
                    {
                        type: "value",
                        boundaryGap: false,
                        axisLabel: {
                            textStyle: {
                                color: "#798799" // 坐标轴文字颜色
                            }
                        },
                        axisLine: {
                            show: false,
                            lineStyle: {
                                color: "#E0E6F1",  // 坐标轴线颜色
                            }
                        },
                        splitLine: {
                            lineStyle: {
                                type: "dashed" // 网格线类型

                            },
                            show: true
                        }
                    },
                ],
                color: ["#21CCFF", "#313CA9"],
                series: [
                    {
                        name: "增加",
                        type: "bar",
                        // stack: "x", // 堆叠
                        // showSymbol: false, // 取消显示圆点
                        smooth: true,
                        data: []
                    },
                    {
                        name: "减少",
                        type: "bar",
                        // stack: "x", // 堆叠
                        // showSymbol: false, // 取消显示圆点
                        smooth: true,
                        data: []
                    }
                ]
            },
            searchObj: {
                startTime: '',
                endTime: ''
            },
        }
    },
    created() {
    },
    methods: {
        search(obj) {
            this.searchObj.startTime = obj.startTime
            this.searchObj.endTime = obj.endTime
            this.getData()
        },
        // 告警事件趋势
        getData() {
            const requestData = {
            ...this.searchObj
        };
            this.$request('/greenLandAnalysis/landGreenStatusCount', requestData, 'post').then(res => {
                let data = res.data
                let obj = {
                    day: [],
                    d1: [],
                    d2: []
                }
                // 定义时间格式
                // let fmt = 'yyyy-MM'
                data.add.forEach(item => {
                    // 以月份为单位，格式化时间
                    // obj.day.push(this.$Utils.formatDate(item.timeStr, fmt))
                    obj.day.push(item.recordTime)
                    obj.d1.push(item.landArea)
                })
                data.subtract.forEach(item => {
                    obj.d2.push(item.landArea)
                })
                this.options.xAxis[0].data = obj.day
                this.options.series[0].data = obj.d1
                this.options.series[1].data = obj.d2
            })
        }
    }
}
</script>

<style lang="less" scoped>

</style>
