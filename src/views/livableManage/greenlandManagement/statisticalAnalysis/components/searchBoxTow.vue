<template>
    <div class="search-box">
        <div></div>
        <!-- <AreaSelectTree v-model="m.areaPath" @on-change="changeDevice" tableName="livable_green_land" :clearable="false" :isInitSelect="true" :hasDevice="true" class="device-sel"></AreaSelectTree> -->
        <dateSelectM @on-change="changeDateType" />
    </div>
</template>

<script>
import dateSelectM from '@/components/common/dateSelect/dateSelectM'
export default {
    name: 'searchBoxTow',
    components: {
        dateSelectM,
    },
    data() {
        return {
            m: {
                selectAreaLand: {},
                areaPath: '',
                date: {}
            }
        }
    },
    watch: {
    },
    created() {
    },
    methods: {
        changeDateType(obj) {
            // console.log(11, obj)
            this.m.date = obj
            this.emitChange()
        },
        changeDevice(val, item) {
            this.m.selectAreaLand = item
            this.emitChange()
        },
        emitChange() {
            if (!this.m.date.startTime) {
                return
            }
            let granularityType = ['month']
            let obj = {
                startTime: this.m.date.startTime,
                endTime: this.m.date.endTime,
                countType: granularityType[this.m.date.dateType],
                selectAreaLand: [
                    {
                        id: this.m.selectAreaLand.id,
                        dataType: this.m.selectAreaLand.dataType,
                        areaPath: this.m.selectAreaLand.value,
                        parentCode: this.m.selectAreaLand._parentCode
                    }
                ]
            }
            // console.log(22, obj)
            this.$emit('on-change', obj)
        }
    }
}
</script>

<style lang="less" scoped>
.search-box{
    display: flex;
    align-items: right;
    justify-content: space-between;
    margin-bottom: 8px;

    .device-sel{
        position: relative;
        width: 300px;
        margin-right: 48px;
    }

}
</style>
