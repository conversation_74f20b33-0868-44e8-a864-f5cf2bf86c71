<template>
    <!--绿地类型统计分析-->
    <div class="head-box">
            <searchBoxTow :device-list="[]" @on-change="search" />
        </div>
        <Title level="6" style="font-size: 14px; color:rgb(103, 105, 113);">绿地类型统计分析</Title>
    <EchartItem :option="options" style="height: 400px" />
</template> 

<script>
import searchBoxTow from './searchBoxTow.vue'
import EchartItem from '@/components/common/EchartItem/index.tsx'
import { EchartsTooltip, tooltipExtraCssText } from '@/utils/tool';
export default {
    name: 'typeOfGreenSpace',
    components: {
        EchartItem,
        searchBoxTow
    },
    data() {
        return {
            options: {
                // title: {
                //     text: '绿地类型统计分析',
                //     textStyle: {
                //         fontSize: 16,
                //     },
                // },
                tooltip: {
                    trigger: 'axis', // 触发类型
                    borderWidth: 0,
                    extraCssText: tooltipExtraCssText,
                    formatter: (arg) => {
                        return EchartsTooltip(arg, '%')
                    }
                },
                // legend: {
                //     data: [],
                //     itemHeight: 8,
                //     itemWidth: 8,
                // },
                grid: {
                    left: 10,
                    right: 10,
                    bottom: 5,
                    top: 10,
                    containLabel: true,
                },
                xAxis: [
                    {
                        type: 'category', // 坐标轴类型
                        data: [],
                        axisLabel: {
                            textStyle: {
                                color: '#798799' // 坐标轴文字颜色
                            },
                        },
                        axisLine: {
                            show: true,
                            lineStyle: {
                                color: '#E0E6F1' // 坐标轴线颜色
                            }
                        },
                        axisTick: {
                            show: false
                        }
                    }
                ],
                yAxis: [
                    {
                        // type: "value",
                        boundaryGap: false,
                        axisLabel: {
                            textStyle: {
                                color: '#798799' // 坐标轴文字颜色
                            },
                            formatter: '{value}%' // 坐标轴格式化
                        },
                        axisLine: {
                            // show: false,
                            lineStyle: {
                                color: '#E0E6F1', // 坐标轴线颜色
                            }
                        },
                        splitLine: {
                            lineStyle: {
                                type: 'dashed' // 网格线类型

                            },
                            show: true
                        },
                        max: 100
                    },
                ],
                color: ['#21CCFF', '#313CA9', '#FFA000', '#FF5722', '#900020', '#666666'],
                series: []
            },
            searchObj: {
                startTime: '',
                endTime: '',
            }
        }
    },
    created() {
    },
    methods: {
        search(obj) {
            this.searchObj.startTime = obj.startTime
            this.searchObj.endTime = obj.endTime
            this.getData()
        },
        // 告警事件趋势
        getData() {
            const requestData = {
                ...this.searchObj,
            }
            this.$request('/greenLandAnalysis/landGreenTypeCount', requestData, 'post').then(res => {
                let xAxisData = []
                let series = []
                let cur = {} // 当前时间段数据，是一个对象，key为时间，value为各个类型绿地面积
                // let all = 0
                for (let i in res.data) {
                    res.data[i].forEach(item => {
                        if (!cur[item.recordTime]) {
                            cur[item.recordTime] = {}
                        }
                        if (!cur[item.recordTime][i]) {
                            cur[item.recordTime][i] = item.landArea
                        }
                        if (!cur[item.recordTime]['total'] && cur[item.recordTime]['total'] !== 0) {
                            cur[item.recordTime]['total'] = 0
                        }
                        cur[item.recordTime]['total'] += item.landArea
                        // cur[item.recordTime][i].push(item.landArea)
                        // console.log('->', cur[i])
                    })
                }
                console.log('cur->', cur)
                let dObj = {}
                for (let i in cur) {
                    xAxisData.push(i)
                    for (let k in cur[i]) {
                        if (k != 'total') {
                            if (!dObj[k]) {
                                dObj[k] = []
                            }
                            dObj[k].push(parseFloat(((cur[i][k] / cur[i]['total']) * 100).toFixed(4)))
                        }
                    }
                    // console.log('i---->', i, cur[i])
                }

                console.log('dObj', dObj)
                for (let i in dObj) {
                //    console.log(i, res.data[i])
                    let ser = {
                        data: dObj[i],
                        name: i,
                        type: 'bar',
                        stack: 'x', // 堆叠
                        smooth: true,
                    }
                    series.push(ser)
                }
                this.options.xAxis[0].data = xAxisData
                this.options.series = series
                })
        }
    }
}
</script>

<style lang="less" scoped>

</style>
