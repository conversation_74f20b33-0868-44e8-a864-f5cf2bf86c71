<template>
    <div>
        <div class="head-box">
            <searchBox :deviceList="[]" @on-change="search" />
        </div>
        <EchartItem :option="options" style="height: 300px" />
        <div class="tab-main">
            <s-tab :tab-list="m.tabList" @handleChange="handleTabChange" justify="start">
                <template #collect>
                    <Table ref="collectTb" :data="m.collectList" :columns="m.collectColumns" row-key="id" class="coll-tb">
                        <template #landChangeRate="{ row }">
                            <compareBox v-if="row.greenLandSummaryVo" :value="row.greenLandSummaryVo.landChangeRate" />
                        </template>
                        <template #greenChangeRate="{ row }">
                            <compareBox v-if="row.greenLandSummaryVo" :value="row.greenLandSummaryVo.greenChangeRate" />
                        </template>
                        <template #operateNum="{ row }">
                            {{ row.greenLandSummaryVo ? formatNum(row.greenLandSummaryVo.operateNum) : '--' }}
                        </template>
                    </Table>
                    <Button @click="exportFun" type="primary" class="export-btn">
                        <i style="font-size: 11px" class="iconfont icon-Vector"></i>导出
                    </Button>
                </template>
                <template #detail>
                    <base-table ref="detailTb" emptyBlockStr="--" :columns="m.detailColumns" url="/greenLandAnalysis/landGreenSummaryDetailOneSelect"
                    >
                    </base-table>
                    <Button @click="exportFun" type="primary" class="export-btn">
                        <i style="font-size: 11px" class="iconfont icon-Vector"></i>导出
                    </Button>
                </template>

            </s-tab>
        </div>
    </div>
</template>

<script>
import searchBox from './searchBox'
import EchartItem from "@/components/common/EchartItem";
import { tooltipExtraCssText, EchartsTooltip } from "@/utils/tool";
import compareBox from '@/components/common/compareBox'
import { exportService } from '@/api/exportService'
export default {
    name: 'greenSpaceChangeTrend',
    components: {
        searchBox,
        EchartItem,
        compareBox
    },
    data() {
        const renderUnit = (h, params) => {
            return h('span', params.row[params.column.key] + '㎡')
        }
        const renderUnitCh = (h, params) => {
            return h('span', (params.row.greenLandSummaryVo ? this.formatNum(params.row.greenLandSummaryVo[params.column.key]) : '--') + '㎡')
        }
        return {
            options: {
                title: {
                    text: "绿地变化趋势",
                    textStyle: {
                        fontSize: 14,
                    }
                },
                tooltip: {
                    trigger: "axis",
                    extraCssText: tooltipExtraCssText,
                    borderWidth: 0,
                    formatter: (arg) => {
                        return EchartsTooltip(arg, '㎡')
                    }
                },
                legend: {
                    icon: "circle",
                    itemHeight: 8,
                    itemWidth: 8,
                },
                grid: {
                    left: 10,
                    right: 10,
                    bottom: 20,
                    top: 40,
                    containLabel: true,
                },
                xAxis: [
                    {
                        type: "category",
                        data: [],
                        axisLabel: {
                            textStyle: {
                                color: "#798799", // 设置 Y 轴标签颜色为蓝色
                            },
                        },
                        axisLine: {
                            show: false,
                            lineStyle: {
                                color: "#E0E6F1",
                            }
                        },
                    },
                ],
                yAxis: [
                    {
                        boundaryGap: false,
                        axisLine: {
                            show: false,
                            lineStyle: {
                                color: "#E0E6F1",
                            }
                        },
                        axisLabel: {
                            textStyle: {
                                color: "#798799", // 设置 Y 轴标签颜色为蓝色
                            }
                        },
                        splitLine: {
                            lineStyle: {
                                type: "dashed", //虚线

                            },
                            show: true, //隐藏
                        },
                    },
                ],
                color: ["#21CCFF", "#313CA9"],
                series: [
                    {
                        name: "绿地",
                        type: "line",
                        showSymbol: false,
                        smooth: true,
                        data: []
                    },
                    {
                        name: "绿化",
                        type: "line",
                        showSymbol: false,
                        smooth: true,
                        data: []
                    }
                ]
            },
            searchObj: {},
            m: {
                tabList: [
                    { name: "汇总", key: "collect" },
                    { name: "明细", key: "detail" }
                ],
                tabIndex: 0,
                timeList: ['', '', '天', '月', '年'],
                collectColumns: [
                    { title: '名称', key: 'name', tree: true, minWidth: 200, tooltip: true, className: 'tree-tb' },
                    { title: '期初绿地面积', key: 'startLandArea', minWidth: 110, render: renderUnitCh },
                    { title: '期末绿地面积', key: 'endLandArea', minWidth: 110, render: renderUnitCh },
                    { title: '绿地面积变化值', key: 'landChangeArea', minWidth: 120, render: renderUnitCh },
                    { title: '绿地面积变化率', slot: 'landChangeRate', minWidth: 120 },
                    { title: '期初绿化面积', key: 'startGreenArea', minWidth: 110, render: renderUnitCh },
                    { title: '期末绿化面积', key: 'endGreenArea', minWidth: 110, render: renderUnitCh },
                    { title: '绿化面积变化值', key: 'greenChangeArea', minWidth: 120, render: renderUnitCh },
                    { title: '绿化面积变化率', slot: 'greenChangeRate', minWidth: 120 },
                    { title: '运营次数', slot: 'operateNum', minWidth: 100 }
                ],
                collectList: [],
                detailColumns: [
                    { title: '日期', key: 'recordTimeStr', fixed: 'left', width: 100 },
                    { title: '期初绿地面积', key: 'startLandArea', minWidth: 110, render: renderUnit },
                    { title: '期末绿地面积', key: 'endLandArea', minWidth: 110, render: renderUnit },
                    { title: '绿地面积变化值', key: 'landChangeArea', minWidth: 120, render: renderUnit },
                    { title: '期初绿化面积', key: 'startGreenArea', minWidth: 110, render: renderUnit },
                    { title: '期末绿化面积', key: 'endGreenArea', minWidth: 110, render: renderUnit },
                    { title: '绿化面积变化值', key: 'greenChangeArea', minWidth: 120, render: renderUnit },
                    { title: '运营次数', key: 'operateNum', minWidth: 100 }
                ],
                stand: {
                    d1: [],
                    d2: []
                },
                countData: {},
                energyDate: {}
            }
        }
    },
    methods: {
        handleTabChange(index) {
            this.m.tabIndex = index
            this.getTableData()
        },
        search(obj) {
            this.searchObj = obj
            this.getStandStatData()
            this.getTableData()
        },
        getTableData() {
            if (this.m.tabIndex === 1) {
                this.$refs.detailTb?.search(this.searchObj)
            } else {
                this.getCollectList()
            }
        },
        exportFun() {
            let url = ''
            if (this.m.tabIndex === 1) {
                url += '/greenLandAnalysis/summaryCountDetailOneSelectExport'
            } else {
                url += '/greenLandAnalysis/summaryCountOneSelectExport'
            }
            exportService(url, this.searchObj, 'post')
        },
        getCollectList() {
            this.$request('/greenLandAnalysis/landGreenSummaryCountOneSelect', this.searchObj, 'post').then(res => {
                if (res.success) {
                    this.m.collectList = this.formatColl(res.data)
                    // console.log(this.m.collectList)
                }
            })
        },
        formatColl(data) {
            data.forEach(item => {
                if (item.dataType == 4) {
                    item.name = item.greenLand.greenLandName
                } else {
                    item.name = item.area.name
                }
                item._showChildren = true
                if (item.children && item.children.length > 0) {
                    item.children = this.formatColl(item.children)
                }
            })
            return data
        },
        // 达标统计数据
        getStandStatData() {
            this.$request('/greenLandAnalysis/landGreenBarCountOneSelect', this.searchObj, 'post').then(res => {
                let obj = {
                    day: [],
                    d1: [],
                    d2: []
                }
                let fmt = 'YYYY-MM-DD'
                if (this.searchObj.countType === 'month') {
                    fmt = 'YYYY-MM'
                } else if (this.searchObj.countType === 'year') {
                    fmt = 'YYYY'
                }
                res.data.forEach(item => {
                    obj.day.push(this.$Util.formatDate(item.recordTime, fmt))
                    obj.d1.push(item.landArea)
                    obj.d2.push(item.greenArea)
                })
                this.options.xAxis[0].data = obj.day
                this.options.series[0].data = obj.d1
                this.options.series[1].data = obj.d2
            })
        },
        formatNum(num) {
            if (num || num === 0) {
                let n = this.$Util.formatNum(num)
                return n
            }
            return '--'
        }
    }
}
</script>

<style lang="less" scoped>
.tab-main{
    position: relative;
}
.export-btn{
    position: absolute;
    right: 0;
    top: 0;
}
/deep/.tree-tb{
    .ivu-table-cell{
        display: flex;
        align-items: center;
        .ivu-tooltip{
            flex: 1;
            overflow: hidden;
            margin-left: 4px;
        }
    }
}
</style>
