<template>
    <div>
        <BreadcrumbCustom>
            <BreadcrumbItem>绿地管理</BreadcrumbItem>
            <BreadcrumbItem>统计分析</BreadcrumbItem>
        </BreadcrumbCustom>
        <ContentCard style="margin-bottom: 8px;">
            <Title :level="5">绿地统计分析</Title>
        </ContentCard>
        <titleCard />
        <ContentCard>
            <s-tab :tab-list="m.tabList" @handleChange="handleTabChange" justify="start">
            </s-tab>
            <greenSpaceChangeTrend v-if="m.tabIndex === 0" :device-list="m.deviceList" />
            <typeOfGreenSpace v-if="m.tabIndex === 1" :device-list="m.deviceList" style="height:600px" />
            <greenSpaceStatus v-if="m.tabIndex === 2" :device-list="m.deviceList" />
        </ContentCard>
    </div>
</template>

<script>
import greenSpaceChangeTrend from './components/greenSpaceChangeTrend'
import typeOfGreenSpace from './components/typeOfGreenSpace.vue'
import greenSpaceStatus from './components/greenSpaceStatus.vue'
import titleCard from './components/titleCard'
export default {
    name: 'index',
    components: {
        greenSpaceChangeTrend,
        typeOfGreenSpace,
        greenSpaceStatus,
        titleCard
    },
    data() {
        return {
            m: {

                tabList: [
                    { name: '绿地变化趋势', key: 'stand' },
                    { name: '绿地类型统计分析', key: 'distribution' },
                    { name: '绿地分布统计分析', key: 'distristand' }
                ],
                tabIndex: 0,
                showJsmFlag: false,
                deviceList: [],
                tabName: 'stand'
            }
        }
    },
    methods: {
        handleTabChange(i, item) {
            this.m.tabIndex = i
        }
    }
}
</script>

<style lang="less" scoped>
.ivu-typography{
    margin-bottom: 0;
}
</style>
