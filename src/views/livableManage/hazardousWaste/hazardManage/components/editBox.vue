<template>
    <!-- 1. 填写危险源点位 -->
    <div class="section-title">1. 填写危险源点位</div>
    <Row :gutter="80">
        <Col span="8">
        <FormItem label="危险源代码" prop="bsm" required>
            <!-- 编辑模式：显示为只读输入框 -->
            <Input
                v-if="isEditMode"
                v-model="form.bsm"
                disabled
                placeholder="危险源代码"
            ></Input>
            <!-- 新增模式：显示为下拉选择 -->
            <Select
                v-else
                v-model="form.bsm"
                @on-change="onHazardousPointChange"
                clearable
                filterable
                placeholder="请选择危险源"
            >
                <Option
                    v-for="item in hazardousPointList"
                    :value="item.bsm"
                    :key="item.id"
                    :disabled="item.isLinked"
                >
                    {{ item.wxymc }}（{{ item.bsm }}）
                </Option>
            </Select>
        </FormItem>
        </Col>
    </Row>

    <!-- 2. 确定并填写信息 -->
    <div class="section-title">2. 确定并填写信息</div>
    <Row :gutter="80">
        <Col span="8">
        <FormItem label="危险源名称" prop="wxymc">
            <Input
                v-model="form.wxymc"
                :disabled="true"
                placeholder="通过危险源点位生成"
                maxlength="50"
            ></Input>
        </FormItem>
        </Col>
        <Col span="8">
        <FormItem label="危险源类型" prop="wxyType">
            <Input
                v-model="form.wxyType"
                :disabled="true"
                placeholder="通过危险源点位生成"
                maxlength="50"
            ></Input>
        </FormItem>
        </Col>
        <Col span="8">
        <FormItem label="危险等级" prop="wxdj">
            <Input
                v-model="form.wxdj"
                disabled
                placeholder="通过危险源点位生成"
                maxlength="50"
            ></Input>
        </FormItem>
        </Col>
    </Row>
    <Row :gutter="80">
        <Col span="8">
        <FormItem label="区域位置" prop="areaPath">
            <Input
                v-model="form.applyAreaName"
                disabled
                placeholder="通过危险源点位生成"
                maxlength="100"
            ></Input>
        </FormItem>
        </Col>
        <Col span="8">
        <FormItem label="详细地址" prop="wxydz">
            <Input
                v-model="form.wxydz"
                disabled
                placeholder="通过危险源点位生成"
                maxlength="100"
            ></Input>
        </FormItem>
        </Col>
        <Col span="8">
        <FormItem label="经纬度" prop="coordinates">
            <Input
                v-model="form.coordinates"
                disabled
                placeholder="通过危险源点位生成"
                maxlength="50"
            ></Input>
        </FormItem>
        </Col>
    </Row>
    <Row :gutter="80">
        <Col span="8">
        <FormItem label="危险源影响面积" prop="wxyyxmj">
            <Input
                v-model="form.wxyyxmj"
                disabled
                placeholder="通过危险源点位生成"
                maxlength="50"
            >
                <template #suffix>km²</template>
            </Input>
        </FormItem>
        </Col>
        <Col span="8">
        <FormItem label="主管单位" prop="zgdwmc">
            <Input
                v-model="form.zgdwmc"
                disabled
                placeholder="通过危险源点位生成"
                maxlength="100"
            ></Input>
        </FormItem>
        </Col>
        <Col span="8">
        <FormItem label="主管单位负责人" prop="zgdwfzr">
            <Input
                v-model="form.zgdwfzr"
                disabled
                placeholder="通过危险源点位生成"
                maxlength="50"
            ></Input>
        </FormItem>
        </Col>
    </Row>
    <Row :gutter="80">
        <Col span="8">
        <FormItem label="数据收集人" prop="sjscr">
            <Input
                v-model="form.sjscr"
                disabled
                placeholder="通过危险源点位生成"
                maxlength="50"
            ></Input>
        </FormItem>
        </Col>
        <Col span="8">
        <FormItem label="数据收集时间" prop="sjscsj">
            <Input
                v-model="form.sjscsj"
                disabled
                placeholder="通过危险源点位生成"
                maxlength="50"
            ></Input>
        </FormItem>
        </Col>
        <Col span="8">
        <FormItem label="收集人联系方式" prop="sjscrlxfs">
            <Input
                v-model="form.sjscrlxfs"
                disabled
                placeholder="通过危险源点位生成"
                maxlength="50"
            ></Input>
        </FormItem>
        </Col>
    </Row>
    <Row>
        <Col span="24">
        <FormItem label="危险源描述" prop="wxyms">
            <Input
                v-model="form.wxyms"
                type="textarea"
                disabled
                placeholder="通过危险源点位生成"
                :rows="4"
                maxlength="500"
                show-word-limit
            ></Input>
        </FormItem>
        </Col>
    </Row>
</template>

<script setup>
import { defineProps, computed, ref, onMounted, inject, nextTick, watch } from 'vue'
import { hazardManageService } from '@/api/livableManage/hazardousWasteService.js'

const props = defineProps({
    modelValue: {
        type: Object,
    },
    isEditMode: {
        type: Boolean,
        default: false
    },
    editData: {
        type: Object,
        default: () => ({})
    }
})

const emit = defineEmits(['update:modelValue'])

const form = computed({
    get() {
        return props.modelValue
    },
    set(value) {
        emit('update:modelValue', value)
    }
})

// 获取父组件的表单引用
const formRef = inject('formRef', null)

// 危险源点位列表
const hazardousPointList = ref([])

// 获取危险源点位列表
const getHazardousPointList = async () => {
    try {
        const response = await hazardManageService.getHazardousPointList()
        if (response.success) {
            hazardousPointList.value = response.data.records || []
        }
    } catch (error) {
        console.error('获取危险源点位列表失败:', error)
    }
}

// 危险源选择变化处理
const onHazardousPointChange = async (selectedBsm) => {
    if (!selectedBsm) {
        resetAutoFilledFields()
        return
    }

    const selectedPoint = hazardousPointList.value.find(item => item.bsm === selectedBsm)
    if (selectedPoint) {
        fillFormFromSelectedPoint(selectedPoint)

        await nextTick()
        if (formRef && formRef.value) {
            formRef.value.clearValidate(['bsm'])
        }
    }
}

// 根据选择的危险源填充表单
const fillFormFromSelectedPoint = (point) => {
    // 危险源编码：保存选择的bsm值
    form.value.bsm = point.bsm || ''

    // 危险源名称：取接口中的 风险点危险源名称
    form.value.wxymc = point.wxymc || ''

    // 危险源类型：取接口中的 危险源类型名称
    form.value.wxyType = point.wxylxName || ''

    // 危险源等级：取接口中的 危险源等级名称
    form.value.wxdj = point.wxydjName || ''

    // 区域位置：取接口中的 所属区域名称
    form.value.areaPath = point.applyAreaName || ''

    // 详细地址：取接口中的 详细地址
    form.value.wxydz = point.wxydz || ''

    // 经纬度：取接口中的 经度+纬度的集合，以（）组合
    if (point.zbX && point.zbY) {
        form.value.coordinates = `(${point.zbX}, ${point.zbY})`
    } else {
        form.value.coordinates = ''
    }

    // 危险源影响面积：取接口中的 危险源影响面积，单位km2
    form.value.wxyyxmj = point.wxyyxmj || ''

    // 主管单位：取接口中的 主管单位名称
    form.value.zgdwmc = point.zgdwmc || ''

    // 主管单位负责人：取接口中的 主管单位负责人
    form.value.zgdwfzr = point.zgdwfzr || ''

    // 数据收集人：取接口中的 数据收集人
    form.value.sjscr = point.sjsjr || ''

    // 数据收集时间：取接口中的 数据收集时间
    form.value.sjscsj = point.sjsjsj || ''

    // 收集人联系方式：取接口中的 数据收集人联系方式
    form.value.sjscrlxfs = point.sjrlxfs || ''

    // 危险源描述：取接口中的 危险源描述，大文本
    form.value.wxyms = point.wxyms || ''
}

// 处理编辑模式下的数据初始化（从API返回的数据结构中提取）
const fillFormFromEditData = (data) => {
    if (!data || !data.hazardousPoint) return

    const hazardousPoint = data.hazardousPoint

    // 使用外层的id，其他属性全部使用hazardousPoint里面的属性
    form.value.id = data.id

    // 危险源编码：使用hazardousPoint中的bsm或wxydm
    form.value.bsm = hazardousPoint.bsm || hazardousPoint.wxydm || ''

    // 危险源名称：使用hazardousPoint中的wxymc
    form.value.wxymc = hazardousPoint.wxymc || ''

    // 危险源类型：使用hazardousPoint中的wxydl对应的名称
    form.value.wxyType = getWxyTypeName(hazardousPoint.wxydl) || ''

    // 危险源等级：使用hazardousPoint中的wxydj对应的名称
    form.value.wxdj = getWxydjName(hazardousPoint.wxydj) || ''

    // 区域位置：使用hazardousPoint中的applyAreaName
    form.value.areaPath = hazardousPoint.applyAreaName || ''

    // 详细地址：使用hazardousPoint中的wxydz
    form.value.wxydz = hazardousPoint.wxydz || ''

    // 经纬度：使用hazardousPoint中的zbX和zbY
    if (hazardousPoint.zbX && hazardousPoint.zbY) {
        form.value.coordinates = `(${hazardousPoint.zbX}, ${hazardousPoint.zbY})`
    } else {
        form.value.coordinates = ''
    }

    // 危险源影响面积：使用hazardousPoint中的wxyyxmj
    form.value.wxyyxmj = hazardousPoint.wxyyxmj || ''

    // 主管单位：使用hazardousPoint中的zgdwmc
    form.value.zgdwmc = hazardousPoint.zgdwmc || ''

    // 主管单位负责人：使用hazardousPoint中的zgdwfzr
    form.value.zgdwfzr = hazardousPoint.zgdwfzr || ''

    // 数据收集人：使用hazardousPoint中的sjsjr
    form.value.sjscr = hazardousPoint.sjsjr || ''

    // 数据收集时间：使用hazardousPoint中的sjsjsj
    form.value.sjscsj = hazardousPoint.sjsjsj || ''

    // 收集人联系方式：使用hazardousPoint中的sjrlxfs
    form.value.sjscrlxfs = hazardousPoint.sjrlxfs || ''

    // 危险源描述：使用hazardousPoint中的wxyms
    form.value.wxyms = hazardousPoint.wxyms || ''
}

// 获取危险源类型名称（根据代码转换为名称）
const getWxyTypeName = (wxydl) => {
    // 这里可以根据实际的枚举值进行转换
    const typeMap = {
        '1': '化学品',
        '2': '易燃易爆',
        // 可以根据实际情况添加更多映射
    }
    return typeMap[wxydl] || wxydl
}

// 获取危险源等级名称（根据代码转换为名称）
const getWxydjName = (wxydj) => {
    // 这里可以根据实际的枚举值进行转换
    const gradeMap = {
        '1': '一级',
        '2': '二级',
        '3': '三级',
        '4': '四级',
        '5': '五级'
    }
    return gradeMap[wxydj] || wxydj
}

// 重置自动填充的字段
const resetAutoFilledFields = () => {
    form.value.bsm = ''
    form.value.wxymc = ''
    form.value.wxyType = ''
    form.value.wxdj = ''
    form.value.areaPath = ''
    form.value.wxydz = ''
    form.value.coordinates = ''
    form.value.wxyyxmj = ''
    form.value.zgdwmc = ''
    form.value.zgdwfzr = ''
    form.value.sjscr = ''
    form.value.sjscsj = ''
    form.value.sjscrlxfs = ''
    form.value.wxyms = ''
}

// 监听编辑数据变化
watch(() => props.editData, (newData) => {
    if (props.isEditMode && newData && Object.keys(newData).length > 0) {
        fillFormFromEditData(newData)
    }
}, { immediate: true, deep: true })

// 组件挂载时获取数据
onMounted(() => {
    // 只有在新增模式下才需要获取危险源列表
    if (!props.isEditMode) {
        getHazardousPointList()
    } else if (props.editData && Object.keys(props.editData).length > 0) {
        // 编辑模式下，如果有编辑数据则填充表单
        fillFormFromEditData(props.editData)
    }
})
</script>

<style lang="less" scoped>
.section-title {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin: 24px 0 16px 0;
    padding-left: 8px;
    border-left: 4px solid #165DFF;
}

.show-map {
    position: absolute;
    top: 0px;
    left: 100px;
    transform: translate(0, -25%);
    font-size: 16px;
    color: #165DFF;
    cursor: pointer;
    z-index: 1000;

    .ivu-icon {
        font-weight: 700;
    }
}

:deep(.ivu-form-item-label) {
    padding-top: 0;
}

:deep(.ivu-cascader-rel .ivu-icon-ios-close-circle) {
    font-family: "iconfont" !important;
    color: #4E627E;
    font-size: 16px;

    &:before {
        content: "\e705";
    }
}
</style>
