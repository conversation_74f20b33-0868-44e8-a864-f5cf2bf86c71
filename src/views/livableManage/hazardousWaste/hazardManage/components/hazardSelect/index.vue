<template>
    <div class="device-select" :class="[multiple ? 'button' : '']">
        <Button type="primary" v-if="multiple" @click="showModal">
            <i class="iconfont">&#xe6b1;</i>
            关联危险源点
        </Button>
        <div class="select-box" @mouseenter="mouseenter" @mouseleave="mouseleave" v-else @click="showModal"
            :style="{ display: allNotShow ? 'none' : 'block' }">
            <div class="name" v-if="device.deviceId">
                {{ device.deviceId }}（{{ device.sbmc }}）
            </div>
            <div class="name" v-if="device.objId">{{ device.objId }}</div>
            <div class="ivu-select-placeholder" v-else>请选择</div>
            <Icon type="ios-arrow-down" class="ivu-select-arrow" />
            <Icon v-show="device.objId && showDeleteIcon" @click.prevent.stop="deleteObjId" type="ios-close-circle"
                class="ivu-select-arrow" />
        </div>
        <component :is="componentName" ref="device" :title="title" :search-title="searchTitle" :multiple="multiple"
            @on-change="changeDevice" />
    </div>
</template>

<script>
import deviceModal from './deviceModal';
export default {
    name: 'DeviceSelect',
    components: {
        deviceModal,
    },
    props: {
        type: { default: 'device' }, // device：设备  component：部件
        multiple: { default: false },
        allNotShow: { default: false },
    },
    data() {
        return {
            componentName: '',
            device: {},
            showDeleteIcon: false,
            title: '请选择危险源点进行关联',
            searchTitle: '请输入危险源名称或代码',
        };
    },
    created() {
        this.initCom();
    },
    methods: {
        initCom() {
            if (this.type == 'device') {
                this.componentName = 'deviceModal'
            }
        },
        showModal() {
            this.$refs.device.init()
        },
        changeDevice(data) {
            this.$emit('on-change', data);
            if (this.device.objId) this.$emit('update:modelValue', this.device.objId);
        },
        mouseenter() {
            this.showDeleteIcon = true;
        },
        mouseleave() {
            this.showDeleteIcon = false;
        },
        deleteObjId() {
            this.device = {};
            this.$emit('on-change', this.device);
        },
    },
};
</script>

<style lang="less" scoped>
.device-select {
    &.button {
        display: inline-block;
        margin-right: 8px;
        margin-bottom: 16px;

        .ivu-btn {
            margin-bottom: 0;
            margin-right: 0;
        }
    }
}

.select-box {
    border: 1px solid #dcdee2;
    height: 32px;
    position: relative;
    cursor: pointer;

    .name,
    .ivu-select-placeholder {
        height: 30px;
        line-height: 30px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        padding-left: 8px;
        padding-right: 24px;
    }

    .ivu-select-placeholder {
        color: @input-placeholder-color;
    }
}
</style>
