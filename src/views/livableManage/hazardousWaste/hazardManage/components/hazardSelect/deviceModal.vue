<template>
    <Modal v-model="showFlag" :title="title" :width="80" class-name="fill-page-modal" transfer
        @on-visible-change="resetStatus" :mask-closable="false" :footer-hide="!multiple">
        <chooseDeviceBox v-if="multiple">
            <div class="placeholder" v-show="m.selDevice.length === 0">请在下方选择设备</div>
            <Tooltip v-for="(item, index) in m.selDevice" :key="index" :max-width="300">
                <Tag color="blue" closable @on-close="delChoose(item, index)">{{ item.wxydm }}</Tag>
                <template #content>
                    <p>危险源代码: {{ item.wxydm }}</p>
                    <p>危险源名称: {{ item.wxymc }}</p>
                    <p>危险源大类: {{ item.wxydlName }}</p>
                    <p>危险等级: {{ wxdjNameList[item.wxdj] }}</p>
                </template>
            </Tooltip>
        </chooseDeviceBox>

        <Form :model="searchObj" :label-width="90" @submit.native.prevent>
            <Row>
                <Col span="8">
                <FormItem label="名称/代码" prop="wxymc">
                    <Input v-model="searchObj.wxymc" @on-change="() => searchInput()" @on-enter="searchInput(100)"
                        @on-clear="() => searchInput(100)" suffix="ios-search" clearable placeholder="请输入名称或代码"></Input>
                </FormItem>
                </Col>
                <Col span="8">
                <FormItem label="危险等级" prop="wxdj">
                    <Select v-model="searchObj.wxdj" @on-change="search" clearable placeholder="请选择">
                        <Option v-for="(item, index) in wxdjNameList" :value="index" :key="index">
                            {{ item }}
                        </Option>
                    </Select>
                </FormItem>
                </Col>
                <Col span="8">
                <FormItem label="危险源大类" prop="wxydl">
                    <Select v-model="searchObj.wxydl" @on-change="search" clearable placeholder="请选择">
                        <Option v-for="(value, index) in Object.keys($store.getters.dictionary.hazard_major).reverse()"
                            :key="index" :value="value">
                            {{ $store.getters.dictionary.hazard_major[value] }}
                        </Option>
                    </Select>
                </FormItem>
                </Col>
            </Row>
        </Form>
        <base-table ref="deviceTb" :columns="columns" url="/hazardous/hazardousPoint" @on-selection-change="selectionChange"
            :load-done="loadDone" size="small" :page-size="15">
            <template #wxdj="{ row }">
                <span>{{ wxdjNameList[row.wxdj] }}</span>
            </template>
            <template #action="{ row }">
                <link-btn size="small" @click="chooseOne(row)" :disabled="row._disabled">选择</link-btn>
            </template>
        </base-table>
        <template #footer>
            <div class="btn-box">
                <Button @click="handleCancel">取消</Button>
                <Button type="primary" @click="confirm" :loading="m.loading">确定</Button>
            </div>
        </template>
    </Modal>
</template>

<script>
import { wxdjNameList } from '../../../data'
import { commonService } from '@/api/commonService'
import modalMix from './modal-mix'
export default {
    name: 'DeviceModal',
    mixins: [modalMix],
    props: {
        title: { default: '请选择设备进行关联' },
    },
    emits: ['on-change', 'on-cancel'],
    data() {
        let columns = []
        if (this.multiple) {
            columns.push({ type: 'selection', width: 40, align: 'center' })
        }
        columns = columns.concat([
            { title: '危险源代码', key: 'wxydm', tooltip: true },
            { title: '危险源名称', key: 'wxymc', tooltip: true },
            { title: '危险源大类', key: 'wxydlName', tooltip: true},
            { title: '主管单位', key: 'zgdw', tooltip: true },
            { title: '危险等级', slot: 'wxdj', tooltip: true },
            // { title: '设备型号', slot: 'sbxh', tooltip: true },
            { title: '操作', slot: 'action', width: 80 }
        ])
        return {
            wxdjNameList,
            columns,
            searchObj: {
                deviceName: '',
                sbzt: '',
                deviceUnitCode: '',
                modelId: this.modelId
            },
            keyName: 'id'
        }
    },

    mounted() {
    },
    methods: {
        resetStatus() {
            this.searchObj = {
                deviceName: '',
                sbzt: '',
                deviceUnitCode: '',
                modelId: this.modelId
            }
        },
        getData() {
            this.getDeviceUnitList()
            this.search()
        },
        // 设备型号
        getDeviceUnitList() {
            // commonService.getConfigDeviceUnit({ modelId: this.modelId }).then(res => {
            //     if (res.success) {
            //         this.m.deviceUnitList = res.data || []
            //     }
            // })
            this.m.deviceUnitList = []
        },
        handleCancel() {
            this.showFlag = false
            this.$emit('on-cancel')
        }
    }
}
</script>

<style lang="less" scoped></style>
../data
