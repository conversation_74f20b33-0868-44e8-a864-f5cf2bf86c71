<template>
    <Row>
        <Col span="8">
        <s-label label="危险源名称" :value="detailInfo.hazardousPoint.wxymc" style="font-size: 16px;font-weight: 600;" />
        </Col>
        <Col span="8">
        <s-label label="区域位置" :value="detailInfo.hazardousPoint.applyAreaName" style="font-size: 16px;font-weight: 600;" />
        </Col>
    </Row>
    <Row>
        <Col span="8">
        <s-label label="危险源代码" :value="detailInfo.hazardousPoint.wxydm" />
        </Col>
        <Col span="8">
        <s-label label="危险源类型" :value="detailInfo.hazardousPoint.wxylxName" />
        </Col>
        <Col span="8">
        <s-label label="危险等级" :value="detailInfo.hazardousPoint.wxydjName" />
        </Col>
    </Row>
    <Row>
        <Col span="8">
        <s-label label="详细地址" :value="detailInfo.hazardousPoint.wxydz" />
        </Col>
        <Col span="8">
        <s-label label="经纬度" :value="detailInfo.hazardousPoint.coordinates" />
        </Col>
        <Col span="8">
        <s-label label="危险源影响面积" :value="detailInfo.hazardousPoint.wxyyxmj ? detailInfo.hazardousPoint.wxyyxmj + 'km²' : ''" />
        </Col>
    </Row>
    <Row>
        <Col span="8">
        <s-label label="主管单位" :value="detailInfo.hazardousPoint.zgdwmc" />
        </Col>
        <Col span="8">
        <s-label label="主管单位负责人" :value="detailInfo.hazardousPoint.zgdwfzr" />
        </Col>
        <Col span="8">
        <s-label label="数据收集人" :value="detailInfo.hazardousPoint.sjsjr" />
        </Col>
    </Row>
    <Row>
        <Col span="8">
        <s-label label="数据收集时间" :value="detailInfo.hazardousPoint.sjsjsj" />
        </Col>
        <Col span="8">
        <s-label label="收集人联系方式" :value="detailInfo.hazardousPoint.sjrlxfs" />
        </Col>
        <Col span="8">
        <!-- 预留位置 -->
        </Col>
    </Row>
    <Row>
        <Col span="24">
        <s-label label="危险源描述" :value="detailInfo.hazardousPoint.wxyms" />
        </Col>
    </Row>
</template>
<script setup>
import { defineProps } from 'vue';

const props = defineProps({
    detailInfo: {
        type: Object,
        default: () => { }
    }
});
</script>
<style lang="less" scoped>
.section-title {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin: 24px 0 16px 0;
    padding-left: 8px;
    border-left: 4px solid #165DFF;
}
</style>
