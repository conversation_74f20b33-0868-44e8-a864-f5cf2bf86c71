<template>
    <BreadcrumbCustom>
        <BreadcrumbItem>危废监测</BreadcrumbItem>
        <BreadcrumbItem to="/hazardousWaste/hazardManage">危险源管理</BreadcrumbItem>
        <BreadcrumbItem>详情</BreadcrumbItem>
    </BreadcrumbCustom>

    <detailCard ref="detailRef" @on-edit="handleEdit" @on-submit="onSubmit"
        :src="require('@/assets/images/icon-基础信息.png')"
        :is-edit-btn="$Util.checkAuth('environmentMonitoring:hazardousWaste:hazardManage')" :is-back-btn="true"
        @on-back="$router.back()" :class="[isEdit ? 'edit-card' : '']" title="基础信息">
        <Form label-position="top" ref="formRef" :model="hazardousPoint" :rules="formRules">
            <InfoShow v-if="!isEdit" :detail-info="detailInfo" />
            <EditBox v-else v-model="hazardousPoint" :is-edit-mode="true" />
        </Form>
    </detailCard>
    <detailCard v-if="!isEdit" title="关联设备" :src="require('@/assets/images/icon_detail_group.png')">
        <div v-for="(item, index) in detailInfo.deviceExtendInfoList" :key="index"
            style="margin-bottom:16px ;padding-bottom:16px;border-bottom:1px solid#E0E6F1;">
            <Row>
                <Col span="8">
                <s-label label="设备名称" :value="item.sbmc" />
                </Col>
                <Col span="8">
                <s-label label="设备编号" :value="item.deviceId" />
                </Col>
                <Col span="8">
                <s-label label="设备型号" :value="item.sbxh" />
                </Col>
                <Col span="8">
                <s-label label="设备区域位置" :value="item.areaPath" />
                </Col>
                <Col span="8">
                <s-label :label="item.deviceSecondTypeName === '安防视频监控' ? '视频查看' : '设备详情'" :value="item.deviceId" value-style="margin-bottom: 0px" class="s-label-center">
                    <template #value>
                        <LinkBtn v-if="item.deviceSecondTypeName === '安防视频监控'" size="small" @click="handleVideoView(item)">点击查看</LinkBtn>
                        <LinkBtn v-else size="small" @click="handleGoDiviceDetial(item.deviceId)">进入详情</LinkBtn>
                    </template>
                </s-label>
                </Col>
                <Col span="8">
                </Col>
            </Row>
        </div>
    </detailCard>
    <VideoModal ref="videoModalCom" v-model="showVideoModal" />
</template>
<script setup>
import { hazardManageService } from '@/api/livableManage/hazardousWasteService.js'
import EditBox from './components/editBox.vue';
import InfoShow from './components/InfoShow.vue';
import VideoModal from '@/views/safeManage/controlManagement/cameraManagement/components/videoModal.vue';
import { ref, getCurrentInstance, nextTick } from 'vue'
import { useRoute, useRouter } from 'vue-router';

const that = getCurrentInstance()?.appContext.config.globalProperties
const { $Util } = that

const isEdit = ref(false)
const route = useRoute()
// 详情
let detailInfo = ref({
    hazardousPoint: {}
})
// 编辑表单数据
const hazardousPoint = ref({})
async function getData() {
    let res = await hazardManageService.getDetailInfo(route.query.id)
    if (res.success) {
        const _hazardousPoint = res.data.hazardousPoint
        // _hazardousPoint.wxdj = _hazardousPoint.wxdj ? `${_hazardousPoint.wxdj}` : ''
        // _hazardousPoint['qualityGrade'] = res.data.qualityGrade ? `${res.data.qualityGrade}` : ''

        // 为编辑模式准备数据，确保字段名称一致
        const editData = {
            bsm: _hazardousPoint.bsm || _hazardousPoint.wxydm || '',
            wxymc: _hazardousPoint.wxymc || '',
            wxyType: _hazardousPoint.wxylxName || _hazardousPoint.wxyType || '',
            wxdj: _hazardousPoint.wxydjName || _hazardousPoint.wxdj || '',
            areaPath: _hazardousPoint.applyAreaName || _hazardousPoint.areaPath || '',
            wxydz: _hazardousPoint.wxydz || '',
            coordinates: _hazardousPoint.zbX && _hazardousPoint.zbY ? `(${_hazardousPoint.zbX}, ${_hazardousPoint.zbY})` : '',
            wxyyxmj: _hazardousPoint.wxyyxmj || '',
            zgdwmc: _hazardousPoint.zgdwmc || '',
            zgdwfzr: _hazardousPoint.zgdwfzr || '',
            sjscr: _hazardousPoint.sjsjr || _hazardousPoint.sjscr || '',
            sjscsj: _hazardousPoint.sjsjsj || _hazardousPoint.sjscsj || '',
            sjscrlxfs: _hazardousPoint.sjrlxfs || _hazardousPoint.sjscrlxfs || '',
            wxyms: _hazardousPoint.wxyms || ''
        }

        hazardousPoint.value = editData
        detailInfo.value = { ...res.data }

        // console.log('API返回的原始数据:', _hazardousPoint)
        // console.log('处理后的编辑数据:', editData)
        // console.log('hazardousPoint.value:', hazardousPoint.value)
    }
}
getData()

const router = useRouter()

// 视频相关
const showVideoModal = ref(false)
const videoModalCom = ref(null)

function handleEdit(val) {
    if (!val) {
        getData()
    }
    isEdit.value = val;
}

function handleGoDiviceDetial(deviceCode) {
    router.push({
        name: 'hazardousWaste:deviceManageDetail',
        query: {
            deviceCode
        }
    })
}

// 视频查看函数
function handleVideoView(item) {
    showVideoModal.value = true;
    const data = {
        deviceCode: item.deviceId,
    };

    // 根据接入渠道设置不同的视频流参数
    if (item.accessChannel === "2") {
        data.propName = "ezopen";
        data.url = item.propMap && item.propMap[item.propMap.video_stream];
        data.ezopenObj = {
            accessToken: item.propMap && item.propMap.accessToken,
        };
    }

    nextTick(() => {
        videoModalCom.value && videoModalCom.value.init(data);
    });
}
const formRules = {
    bsm: [{ required: true, message: '请选择危险源代码', trigger: 'change' }],
    wxymc: [{ required: true, message: '请输入危险源名称' }],
};
const isLoading = ref(false)
const formRef = ref()
const detailRef = ref()
const onSubmit = async() => {
    const validRes = await formRef.value?.validate()
    if (validRes) {
        isLoading.value = true;
        const params = Object.assign({}, hazardousPoint.value, { szjd: '', szsq: '', szdywg: '', id: route.query.id })
        const areaLocationTreeArr = params.areaPath?.split('@') || [];
        params.szjd = areaLocationTreeArr[0]
        params.szsq = areaLocationTreeArr[1]
        params.szdywg = areaLocationTreeArr[2]
        const res = await hazardManageService.edit(params)
        if (res.success) {
            that?.$Message.success('编辑成功')
            handleEdit(false)
            detailRef.value.isEditFlag = false;
        }
        isLoading.value = false;
    }
}

</script>


<style lang="less" scoped>
/deep/ .tab-title {
    margin: 0 0 16px 0;
}


/deep/ .s-label.s-label-center {
    padding-bottom: 12px;
    align-items: center;
}
</style>
