<template>
    <BreadcrumbCustom>
        <BreadcrumbItem>危废监测</BreadcrumbItem>
        <BreadcrumbItem>危险源管理</BreadcrumbItem>
    </BreadcrumbCustom>
    <ContentCard title="危险源管理">
        <BaseForm @handle-submit="handleSubmit" :model="searchObj" inline :label-width="90">
            <template #formitem>
                <FormItem label="危险源名称" prop="wxymc">
                    <Input v-model="searchObj.wxymc" :maxlength="20" placeholder="请输入" clearable></Input>
                </FormItem>
                <FormItem label="主管单位" prop="zgdw">
                    <Input v-model="searchObj.zgdw" :maxlength="20" placeholder="请输入" clearable></Input>
                </FormItem>
                <FormItem label="危险源类型" prop="wxydl">
                    <TreeSelect
                        v-model="searchObj.wxydl"
                        :data="wxydlTree"
                        placeholder="请选择"
                        clearable
                        :show-checkbox="false"
                        :multiple="false"
                        :check-strictly="true"
                        :default-expand-all="true"
                        label-key="label"
                        value-key="value"
                        children-key="children"
                    />
                </FormItem>
                <FormItem label="危险源代码" prop="wxydm">
                    <Input v-model="searchObj.wxydm" :maxlength="20" placeholder="请输入" clearable></Input>
                </FormItem>
                <FormItem label="危险等级" prop="wxydj">
                    <Select :transfer="false" v-model="searchObj.wxydj" clearable>
                        <Option v-for="item in wxdjList" :key="item.srcId" :value="item.srcId">
                            {{ item.riskDangerGradeName }}
                        </Option>
                    </Select>
                </FormItem>
                <FormItem label="是否监测" prop="monitor">
                    <Select :transfer="false" v-model="searchObj.monitor" clearable>
                        <Option v-for="(item, index) in isMonitoredList" :key="index" :value="index">
                            {{ item }}
                        </Option>
                    </Select>
                </FormItem>
                <!-- <FormItem label="区域位置" prop="areaLocation">
              <AreaSelectTree v-model="searchObj.areaLocation" table-name="livable_lake_monitoring_device" />
          </FormItem> -->
            </template>
        </BaseForm>
        <TableContentCard :base-btn="false">
            <template #btn>
                <!-- <hazardSelect v-auth="'environmentMonitoring:hazardousWaste:hazardManage:add'" :multiple="true"
                    @on-change="addSubmit" /> -->
                <Button icon="md-add" type="primary" @click="addSubmit">
                    新建
                </Button>
                <Dropdown v-auth="['environmentMonitoring:hazardousWaste:hazardManage:batchDelete']"
                    placement="bottom-start">
                    <Button custom-icon="iconfont icon-caret-down">批量操作</Button>
                    <template #list>
                        <DropdownMenu style="width: 111px">
                            <DropdownItem v-auth="'environmentMonitoring:hazardousWaste:hazardManage:batchDelete'"
                                @click="handleDelete">
                                批量删除
                            </DropdownItem>
                        </DropdownMenu>
                    </template>
                </Dropdown>
            </template>
            <baseTable @on-selection-change="selectionChange" :model="searchObj" ref="listCom" url="/hazardous/list"
                :columns="tableList">
                <template #monitor="{ row }">
                    <s-tag v-if="row.monitor == 1">{{ isMonitoredList[row.monitor] }}</s-tag>
                    <s-tag v-else background="#F2F3F5" color="#4E627E">{{ isMonitoredList[row.monitor] }}</s-tag>
                </template>
                <template #wxdj="{ row }">
                    <tooltipAutoShow :content="wxdjNameList[row.wxdj]" />
                </template>
                <!--   1在线0离线  -->
                <!-- <template #status="{ row }">
                    <s-tag v-if="row.status == 1">在线</s-tag>
                    <s-tag v-else background="#F2F3F5" color="#4E627E">离线</s-tag>
                </template> -->
                <!-- <template #liquid_level_value="{ row }">
                    {{ toFixed(findValue(row.devicePropertyStatusList, 'liquid_level_value', 'prop', 'value')) + ' ' +
                        findValue(row.devicePropertyStatusList, 'liquid_level_value', 'prop', 'specsMap.unit') }}
                </template> -->
                <template #action="{ row }">
                    <LinkBtn size="small" @click="goDetail(row.id)">详情</LinkBtn>
                    <LinkBtn size="small" @click="showModal(row)">关联</LinkBtn>
                </template>
            </baseTable>
        </TableContentCard>
        <deviceModal ref="deviceRef" :title="title" search-title="请输入名称或设备编号" :multiple="true" :model-id="''"
            @on-change="changeDevice" :url="`/hazardous/deviceList/${current.bsm}`" v-if="current.bsm" />
    </ContentCard>
</template>
<script setup>
import TableContentCard from '@/components/global/TableContentCard'
import { hazardManageService } from '@/api/livableManage/hazardousWasteService.js'
import deviceModal from '@/components/common/deviceSelect/deviceModal'
import { wxdjNameList, isMonitoredList } from '../data'
import { getCurrentInstance, reactive, onMounted, ref, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { useStore } from 'vuex'
const $store = useStore()
const that = getCurrentInstance()?.appContext.config.globalProperties
const searchObj = reactive({})
const listCom = ref(null)
// 新增危险等级下拉数据
const wxdjList = ref([])

// 树结构数据适配
function adaptTreeData(list, parentPath = '') {
    return (list || []).map(item => {
        const currentPath = parentPath ? `${parentPath}/${item.riskDangerClassifyName}` : item.riskDangerClassifyName
        return {
            value: item.id,
            label: currentPath, // 完整路径，用于选中后显示
            title: item.riskDangerClassifyName, // 只显示当前节点名称，用于树形展开时显示
            children: Array.isArray(item.children) ? adaptTreeData(item.children, currentPath) : []
        }
    })
}

const wxydlTree = ref([])

// 获取危险等级下拉
async function fetchGradeList() {
    try {
        const res = await that?.$request?.('/hazardous/gradeList', {}, 'get')
        if (res && res.success && Array.isArray(res.data)) {
            wxdjList.value = res.data
        } else {
            wxdjList.value = []
        }
    } catch (e) {
        wxdjList.value = []
    }
}

async function fetchWxydlTree() {
    try {
        const res = await that?.$request?.('/hazardous/classifyTree', {}, 'get')
        if (res && res.success && Array.isArray(res.data)) {
            wxydlTree.value = adaptTreeData(res.data)
        } else {
            wxydlTree.value = []
        }
    } catch (e) {
        wxydlTree.value = []
    }
}

function handleSubmit() {
    let params = {}
    params = window.Util.objClone(searchObj)
    // 查询参数危险等级字段名调整为wxydj
    // if (params.wxydj !== undefined) {
    //     params.wxydj = params.wxdj
    //     delete params.wxdj
    // }
    // if (searchObj.areaLocation) {
    //     const arr = searchObj.areaLocation.split('/')
    //     params.szjd = arr[0]
    //     params.szsq = arr[1]
    //     params.szdywg = arr[2]
    //     delete params.areaLocation
    // }
    listCom.value.search(params)
}
onMounted(() => {
    fetchGradeList()
    fetchWxydlTree()
    handleSubmit()
})
const tableList = ref([
    { type: 'selection', maxWidth: 40 },
    { title: '危险源代码', key: 'wxydm', tooltip: true, minWidth: 160 },
    { title: '危险源点名称', key: 'wxymc', tooltip: true, minWidth: 200 },
    { title: '是否监测', slot: 'monitor', minWidth: 80 },
    { title: '主管单位', key: 'zgdw', tooltip: true, minWidth: 100 },
    { title: '危险等级', key: 'wxydjName', tooltip: true, minWidth: 80 },
    { title: '危险源类型', key: 'wxylxName', tooltip: true, minWidth: 100 },
    { title: '区域位置', key: 'applyAreaName', tooltip: true, minWidth: 200 },
    { title: '操作', slot: 'action', width: 120 }
])


const router = useRouter()
function goDetail(id) {
    router.push({
        name: 'hazardousWaste:hazardManageDetail',
        query: {
            id
        }
    })
}

// 批量删除
let ids = []
function handleDelete() {
    if (ids.length === 0) {
        that?.$Message.warning('最少选择一条数据')
        return
    }
    that?.$Modal.confirm({
        title: '提示',
        content: '您确定要删除选中的数据吗？',
        onOk: async() => {
            let res = await hazardManageService.delBatch({ ids })
            if (res.success) {
                that.$Message.success('删除成功')
                handleSubmit()
                ids = []
            }
        }
    })
}
function selectionChange(selected) {
    ids = selected.map((i) => i.id)
}

async function addSubmit(data) {
    router.push({
        path: '/hazardousWaste/hazardManageAdd',
        query: {
        }
    })
}
const deviceRef = ref()
const current = ref({})
function changeDevice(data) {
    console.log(data)
    const deviceCodes = data.map(k => k.deviceId)
    hazardManageService.addDevice({ deviceCodes, id: current.value.id }).then(res => {
        if (res.success) {
            that.$Message.success('绑定成功')
            handleSubmit()
        }
    })
}
function showModal(data) {
    if (!data.bsm) return
    current.value = data
    nextTick(() => {
        deviceRef.value.init()
        deviceRef.value.selectionChange(data.deviceExtendInfoList || [])
    })
}
</script>

<style lang="less" scoped>
.header {
    display: flex;

    .label {
        width: 90px;

    }

    .frame {
        border: 1px solid #165DFF;
        flex: 1;
    }
}

.dialog-form {
    padding: 15px 0;

    .ivu-form-item {
        width: 100%;
    }
}

.operation-btn {
    display: flex;
    justify-content: space-between;
    padding-bottom: 15px;

    div {
        .ivu-btn {
            margin-left: 10px;
        }
    }
}
</style>
