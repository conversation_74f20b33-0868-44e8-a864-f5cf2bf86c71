<template>
    <BreadcrumbCustom>
        <BreadcrumbItem>危废监测</BreadcrumbItem>
        <BreadcrumbItem to="/hazardousWaste/hazardManage">危险源管理</BreadcrumbItem>
        <BreadcrumbItem :to="`/hazardousWaste/hazardManageDetail?id=${$route.query.id}`">详情</BreadcrumbItem>
        <BreadcrumbItem>编辑</BreadcrumbItem>
    </BreadcrumbCustom>
    <Form :model="detailInfo" label-position="top">
        <detailCard :is-back-btn="true" :src="require('@/assets/images/icon-基础信息.png')" @on-back="$router.back()"
            title="基本信息">
            <Row>
                <Col span="8">
                <FormItem label="危险源代码">
                    <Input v-model="detailInfo.hazardousPoint.wxydm" disabled></Input>
                </FormItem>
                </Col>
                <Col span="8">
                <FormItem label="区域位置">
                    <Input v-model="detailInfo.hazardousPoint.areaPath" disabled></Input>
                </FormItem>
                </Col>
            </Row>
            <Row>
                <Col span="8">
                <FormItem label="危险源名称">
                    <Input v-model="detailInfo.hazardousPoint.wxymc" disabled></Input>
                </FormItem>
                </Col>
                <Col span="8">
                <FormItem label="危险源大类">
                    <Input v-model="detailInfo.hazardousPoint.wxydlName" disabled></Input>
                </FormItem>
                </Col>
                <Col span="8">
                <FormItem label="危险源细类">
                    <Input v-model="detailInfo.hazardousPoint.wxyxlName" disabled></Input>
                </FormItem>
                </Col>
                <Col span="8">
                <FormItem label="危险源坐标X">
                    <Input v-model="detailInfo.hazardousPoint.zbX" disabled></Input>
                </FormItem>
                </Col>
                <Col span="8">
                <FormItem label="危险源坐标Y">
                    <Input v-model="detailInfo.hazardousPoint.zbY" disabled></Input>
                </FormItem>
                </Col>
                <Col span="8">
                <FormItem label="危险等级">
                    <Input v-model="wxdjNameList[detailInfo.hazardousPoint.wxdj]" disabled></Input>
                </FormItem>
                </Col>
                <Col span="8">
                <FormItem label="危险源密级">
                    <Input v-model="wxymjNameList[detailInfo.hazardousPoint.wxymj]" disabled></Input>
                </FormItem>
                </Col>
                <Col span="8">
                <FormItem label="主管单位">
                    <Input v-model="detailInfo.hazardousPoint.zgdw" disabled></Input>
                </FormItem>
                </Col>
                <Col span="8">
                <FormItem label="主管单位地址">
                    <Input v-model="detailInfo.hazardousPoint.zgdwdz" disabled></Input>
                </FormItem>
                </Col>
                <Col span="8">
                    <FormItem prop="qualityGrade">
                            <template #label>
                                监测水体等级<Icon type="ios-help-circle-outline" @click="componentName=markRaw(IllustrateCom)" />
                            </template>
                        <Select v-model="detailInfo.qualityGrade">
                            <Option v-for="(item, index) in $enumeration.waterQualityClassify" v-show="index != 6" :value="index" :key="index">{{ item }}</Option>
                        </Select>
                    </FormItem>
                </Col>
            </Row>
        </detailCard>
        <!-- <detailCard title="关联设备" :src="require('@/assets/images/icon-air_quality.png')">
        </detailCard> -->
    </Form>
    <div class="btn">
        <Button @click="$router.back()">取消</Button>
        <Button type="primary" @click="confirm">提交</Button>
    </div>
    <s-modal :width="800" title="监测水体等级说明" :component-name="componentName" @emitClose="componentName = ''" :ref-box="modalRef"
        :transfer="true" footer-hide>
        <component :is="componentName" ref="modalRef" />
    </s-modal>
</template>

<script setup>
import { hazardManageService } from '@/api/livableManage/hazardousWasteService.js'
import { getValue, toFixed } from '@/utils/tool'
import { wxdjNameList, wxymjNameList } from '../data'
import { ref, getCurrentInstance,markRaw } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import IllustrateCom from '@/views/livableManage/waterQuality/devicement/components/illustrateCom.vue'
const that = getCurrentInstance()?.appContext.config.globalProperties
const { $enumeration } = that
const detailInfo = ref({ hazardousPoint: {} })
// 说明弹出层
const componentName = ref('')
const modalRef = ref()
const route = useRoute()
async function getData() {
    let res = await hazardManageService.getDetailInfo(route.query.id)
    if (res.success) {
        if (res.data.qualityGrade) {
          res.data.qualityGrade += ''
        }
        // getValue(res.data, 'devicePropertyStatusList', []).forEach((item) => {
        //     item.value = toFixed(getValue(item, 'value')) + ' ' + getValue(item, 'specsMap.unit')
        // })
        detailInfo.value = res.data
    }
    //   console.log(detailInfo.value);
}
getData()
const router = useRouter()
async function confirm() {
    const params = Object.assign({}, detailInfo.value, { id: route.query.id })
    let res = await hazardManageService.edit(params)
    //   console.log(res);
    if (res.success) {
        that?.$Message.success('编辑成功')
        router.back()
    }
}
function handlejwd(objInfo) {
    if (!objInfo.objX) return ''
    return objInfo.objX + ' , ' + objInfo.objY
}


</script>


<style lang="less" scoped>
.ivu-form-item {
    width: 90%;
}

.btn {
    position: fixed;
    bottom: 0;
    width: 100%;
    text-align: left;
    background-color: #ffffff;
    text-align: right;
    padding: 8px 40px;
    left: 0;
    box-shadow: 0px -3px 12px rgba(45, 75, 103, 0.1);
    z-index: 5;

    .ivu-btn {
        min-width: 68px;
        // margin-left: 8px;
        // margin-right: 8px;
    }

    .ivu-btn+.ivu-btn {
        margin-left: 8px;
    }
}
</style>
