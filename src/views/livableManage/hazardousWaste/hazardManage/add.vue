<template>
    <BreadcrumbCustom>
        <BreadcrumbItem>危废监测</BreadcrumbItem>
        <BreadcrumbItem to="/hazardousWaste/hazardManage">危险源管理</BreadcrumbItem>
        <BreadcrumbItem>新增</BreadcrumbItem>
    </BreadcrumbCustom>
    <detailCard title="新增" :is-back-btn="true" @on-back="onBack">
        <Form ref="formRef" :model="form" :rules="rules" label-position="top">
            <EditBox v-model="form" ref="edit" :is-edit-mode="false" />
            <div class="btn-box">
                <Button type="primary" @click="handleSubmit" :loading="isLoading">提交</Button>
                <Button @click="onBack">取消</Button>
            </div>
        </Form>
    </detailCard>
</template>
<script setup>
import EditBox from './components/editBox.vue'
import {
    getCurrentInstance,
    ref,
    provide
} from 'vue';
import { hazardManageService } from '@/api/livableManage/hazardousWasteService.js'
import { useRouter } from 'vue-router';
const that = getCurrentInstance()?.appContext.config.globalProperties;
const router = useRouter()

const onBack = () => {
    router.back();
}

const isLoading = ref(false)
const formRef = ref()
const form = ref({
    bsm: '',
    wxymc: '',
    wxyType: '',
    wxdj: '',
    areaPath: '',
    wxydz: '',
    coordinates: '',
    wxyyxmj: '',
    zgdwmc: '',
    zgdwfzr: '',
    sjscr: '',
    sjscsj: '',
    sjscrlxfs: '',
    wxyms: ''
});

// 提供表单引用给子组件
provide('formRef', formRef)

// 表单验证
const rules = {
    bsm: [{ required: true, message: '请选择危险源代码', trigger: 'change' }],
};

// 提交表单
async function handleSubmit() {
    const validRes = await formRef.value?.validate()
    if (validRes) {
        isLoading.value = true;
        const params = Object.assign({}, form.value, { szjd: '', szsq: '', szdywg: '' })
        const areaLocationTreeArr = params.areaPath?.split('@') || [];
        params.szjd = areaLocationTreeArr[0]
        params.szsq = areaLocationTreeArr[1]
        params.szdywg = areaLocationTreeArr[2]
        const res = await hazardManageService.addBatch(params)
        if (res.success) {
            that?.$Message.success('新增成功')
            router.back()
        }
        isLoading.value = false;
    }
}

</script>
<style lang="less" scoped>
.row {
    .ivu-col-span-2 {
        margin-left: 8px;
        color: @primary-color;
        cursor: pointer;
    }

    .icon-text {
        position: relative;
        left: -33px;
        color: @primary-color;
        cursor: pointer;
    }
}

.row:not(:first-child) {
    margin-top: 10px;
}
</style>
