<template>
    <BreadcrumbCustom>
        <BreadcrumbItem>危废监测</BreadcrumbItem>
        <BreadcrumbItem>设备管理</BreadcrumbItem>
    </BreadcrumbCustom>
    <ContentCard title="设备管理">
        <BaseForm @handle-submit="handleSubmit" :model="searchObj" inline :label-width="90">
            <template #formitem>
                <FormItem label="设备编号" prop="deviceCode">
                    <Input v-model="searchObj.deviceCode" :maxlength="20" placeholder="请输入" clearable></Input>
                </FormItem>
                <FormItem label="设备名称" prop="sbmc">
                    <Input v-model="searchObj.sbmc" :maxlength="20" placeholder="请输入" clearable></Input>
                </FormItem>
                <FormItem label="区域位置" prop="areaLocation">
                    <AreaSelectTree v-model="searchObj.areaLocation" table-name="livable_hazardous_device" />
                </FormItem>
                <FormItem label="设备标识码" prop="deviceBsm">
                    <Input v-model="searchObj.deviceBsm" :maxlength="20" placeholder="请输入" clearable></Input>
                </FormItem>
                <FormItem label="在线状态" prop="status">
                    <Select :transfer="false" v-model="searchObj.status" clearable>
                        <Option v-for="(item, index) in $enumeration.onlineStatus" :key="index" :value="index" clearable>
                            {{ item.title }}
                        </Option>
                    </Select>
                </FormItem>
                <FormItem label="使用状态" prop="useStatus">
                    <Select :transfer="false" v-model="searchObj.useStatus" clearable>
                        <Option v-for="(item, index) in $enumeration.useStatus" :key="index" :value="index" clearable>
                            {{ item }}
                        </Option>
                    </Select>
                </FormItem>
            </template>
        </BaseForm>
        <TableContentCard :base-btn="false">
            <template #btn>
                <deviceSelect :model-id="35" :multiple="true"
                    v-auth="'environmentMonitoring:hazardousWaste:deviceManage:add'" @on-change="addSubmit" />
            </template>
            <baseTable @on-selection-change="selectionChange" :model="searchObj" ref="listCom" url="/hazardousDevice/list"
                :columns="tableList">
                <!--    1在线0离线  -->
                <template #status="{ row }">
                    <online-status :value="row.status"></online-status>
                </template>
                <!--    1启用0停用  -->
                <template #useStatus="{ row }">
                    <use-status :value="row.useStatus"></use-status>
                </template>
                <template #action="{ row }">
                    <LinkBtn size="small" @click="goDetail(row.deviceCode)">详情</LinkBtn>
                    <LinkBtn size="small" @click="handleDisable(row, $enumeration.useStatus[row.useStatus === 0 ? 1 : 0])">
                        {{ $enumeration.useStatus[row.useStatus === 0 ? 1 : 0] }}
                    </LinkBtn>
                </template>
            </baseTable>
        </TableContentCard>
    </ContentCard>
</template>
<script setup>
import { commonService } from '@/api/commonService'
import TableContentCard from '@/components/global/TableContentCard'
import { hazardDeviceService } from '@/api/livableManage/hazardousWasteService.js'
import { getCurrentInstance, reactive, onMounted, ref } from 'vue'
import { useRouter } from 'vue-router';
import { findValue, toFixed } from '@/utils/tool';
const searchObj = reactive({})
const listCom = ref({})
const that = getCurrentInstance()?.appContext.config.globalProperties
const { $enumeration } = that

function handleSubmit() {
    let params = {}
    params = window.Util.objClone(searchObj)
    if (searchObj.areaLocation) {
        const arr = searchObj.areaLocation.split('/')
        params.szjd = arr[0]
        params.szsq = arr[1]
        params.szdywg = arr[2]
        delete params.areaLocation
    }

    listCom.value.search(params)
}
onMounted(() => {
    handleSubmit()
})
const tableList = ref([
    // { type: 'selection', maxWidth: 40 },
    { title: '设备编号', key: 'deviceCode', tooltip: true },
    { title: '设备名称', key: 'sbmc', tooltip: true },
    { title: '危险源站点', key: 'wxymc', tooltip: true },
    { title: '在线状态', slot: 'status', width: 80, tooltip: true },
    { title: '使用状态', slot: 'useStatus', width: 80 },
    { title: '设备标识码', key: 'deviceBsm', tooltip: true, width: 200 },
    { title: '设备型号', key: 'sbxh', tooltip: true },
    { title: '区域位置', key: 'areaPath', tooltip: true, width: 200 },
    { title: '操作', slot: 'action', width: 100 }
])


const router = useRouter()
function goDetail(deviceCode) {
    router.push({
        name: 'hazardousWaste:deviceManageDetail',
        query: {
            deviceCode
        }
    })
}

async function handleDisable(row, label) {
    const params = {
        deviceCode: row.deviceCode
    }
    if (label === '启用') {
        params.useStatus = 1
    } else {
        params.useStatus = 0
    }
    // let res = await hazardDeviceService.editDevice(params)
    let res = await commonService.updateUseStatus(params.deviceCode, params.useStatus)
    if (res.success) {
        that?.$Message.success(label + '成功')
        handleSubmit()
    }

}


// 批量删除
// let ids = []

// function handleDelete() {
//     if (ids.length === 0) {
//         that?.$Message.warning('最少选择一条数据')
//         return
//     }
//     that?.$Modal.confirm({
//         title: '提示',
//         content: '您确定要删除选中的数据吗？',
//         onOk: async () => {
//             let res = await deleteDevice(ids)
//             if (res.success) {
//                 that.$Message.success('删除成功')
//                 handleSubmit()
//                 ids = []
//             }
//         }
//     })
// }
// function selectionChange(selected) {
//     ids = selected.map((i) => i.id)
// }

async function addSubmit(data) {
    let deviceCodes = data.map((i) => i.deviceId)
    let res = await hazardDeviceService.newAddDevice({ deviceCodes })
    if (res.success) {
        that?.$Message.success('关联成功')
        handleSubmit()
    }
}
</script>

<style lang="less" scoped>
.header {
    display: flex;

    .label {
        width: 90px;

    }

    .frame {
        border: 1px solid #165DFF;
        flex: 1;
    }
}

.dialog-form {
    padding: 15px 0;

    .ivu-form-item {
        width: 100%;
    }
}

.operation-btn {
    display: flex;
    justify-content: space-between;
    padding-bottom: 15px;

    div {
        .ivu-btn {
            margin-left: 10px;
        }
    }
}
</style>
