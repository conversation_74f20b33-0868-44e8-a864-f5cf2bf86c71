<template>
    <BreadcrumbCustom>
        <BreadcrumbItem>危废监测</BreadcrumbItem>
        <BreadcrumbItem>监测数据</BreadcrumbItem>
    </BreadcrumbCustom>
    <monitoring-data :model-id="35" :attr-list="[]" :is-attr-list-from-es="true" />
</template>
<script lang="ts">
import { defineComponent } from 'vue'
import MonitoringData from '../../components/monitoringData.vue'
export default defineComponent({
    components: {
        MonitoringData
    }
})
</script>
<style lang="less" scoped>
</style>
