<template>
    <BreadcrumbCustom>
        <BreadcrumbItem>统计分析</BreadcrumbItem>
        <BreadcrumbItem to="/muckMonitoring/alarmRecords">空气统计分析</BreadcrumbItem>
    </BreadcrumbCustom>
        <div class="grid-container">
            <div class="cell-1">
<!--                空气环境分析-->
                <div class="title">
                    <span class="left">实时气象环境</span>
                    <Select v-model="box_1_device_id" style="width: 132px" placeholder="设备编号" clearable>
                        <Option v-for="item in meteorologicalDeviceOption"
                                :value="item.value"
                                :key="item.value">
                            {{ item.label }}
                        </Option>
                    </Select>
                </div>
                <EnvironmentAnalysis :data="box_1_Data" />
            </div>
            <div class="cell-2">
                <div class="title">
                    <span class="left">空气质量趋势</span>
                    <DateSelect @on-change="changeWaterLevelTime" />
                </div>
                <div class="top">
                    <ScatterPlot :data="box_2_data" />
                </div>
                <div style="height: 32px"></div>
                <div class="bottom">
                    <Factors :data="box_2_data" title="6因子" />
                </div>
            </div>
            <div class="cell-3">
                <!--                空气环境分析-->
                <div class="title">
                    <span class="left">实时空气质量</span>
                    <Select v-model="box_3_device_id" style="width: 132px" placeholder="设备编号" clearable>
                        <Option v-for="item in airQualityDeviceOption"
                                :value="item.value"
                                :key="item.value">
                            {{ item.label }}（{{ item.deviceCode }}）
                        </Option>
                    </Select>
                </div>
                <EnvironmentAnalysis :data="box_3_data" />
            </div>
            <div class="cell-5">
                <PMValueBar title="PM值分析" :data="box_5_data" />
            </div>
            <div class="cell-6">
                <PMTrendBar title="PM趋势" :data="box_6_data" v-model:facility-type="box_6_Type" />
            </div>
            <div class="cell-7">
                <NoiseTrend title="噪音趋势" v-model:facility-type="box_7_Type" :data="box_7_data" />
            </div>
        </div>
</template>

<script setup lang="ts">
import EnvironmentAnalysis
    from '@/views/livableManage/airEnvironmentAnalysis/components/EnvironmentAnalysis.vue';
import ScatterPlot from '@/views/livableManage/airEnvironmentAnalysis/components/ScatterPlot.vue';
import Factors from '@/views/livableManage/airEnvironmentAnalysis/components/Factors.vue';

import { ref, watchEffect } from 'vue';
import PMValueBar from '@/views/livableManage/airEnvironmentAnalysis/components/PMValueBar.vue';
import {
    api_getMonitoringSiteHasDeviceList,
    getAirQualityDeviceList
} from '@/api/livableManage/meteorological';
import {
    getAirEnvStatistics,
    getAirQualityDeviceInfo,
    getAirQualityDeviceListAndInfo,
    getDeviceInfoById, getNoiceTrend, getPmTrend
} from '@/api/livableManage/airQualityService';
import PMTrendBar from '@/views/livableManage/airEnvironmentAnalysis/components/PMTrendBar.vue';
import NoiseTrend from '@/views/livableManage/airEnvironmentAnalysis/components/NoiseTrend.vue';
import DateSelect from '@/components/common/dateSelect/index.vue';
import { isNullOrEmpty } from '@/utils/tool';
import moment from 'moment'


const images = [
    require('./images/风速.png'),
    require('./images/风向.png'),
    require('./images/气压.png'),
    require('./images/气温.png'),
    require('./images/湿度.png'),
    require('./images/雨量.png'),
]

const box_1_device_id = ref(-1)

const box_1_Data = ref({})

const getBox1Data = async() => {
    if (box_1_device_id.value < 0 || !box_1_device_id.value) return;
    const params = {
        monitorStationId: box_1_device_id.value
    }
    const res = await getDeviceInfoById(params)
    if (res.success) {
        box_1_Data.value = res.data.map((item:any, index:number) => {
            return {
                title: item.propName,
                images: images[index],
                value: item.value || '0',
                unitOfMeasurement: JSON.parse(item.specs).unit
            }
        })
    }
}
watchEffect(getBox1Data);



const box_3_device_id = ref('')

const box_3_data = ref([]);

const getBox3Data = async() => {
    if (box_3_device_id.value < 0 || !box_3_device_id.value) return;
    const id = box_3_device_id.value
    const res = await getAirQualityDeviceInfo(id)
    if (res.success) {
        box_3_data.value = res.data.map((item:any, index:number) => {
            return {
                title: item.propName,
                value: item.value || 0,
                unitOfMeasurement: JSON.parse(item.specs).unit
            }
        })
    }
}
watchEffect(getBox3Data)



const data1 = [];
for (let i = 0; i < 10; i++) {
    const x = Math.floor(Math.random() * 100);
    const y = Math.floor(Math.random() * 301);
    data1.push([x, y]);
}

const gradeEnum = {
    '1': '一级',
    '2': '二级',
    '3': '三级',
    '4': '四级',
    '5': '五级',
    '6': '六级',
}
const aqiTypeEnum = {
    [gradeEnum[1]]: '优',
    [gradeEnum[2]]: '良',
    [gradeEnum[3]]: '轻度污染',
    [gradeEnum[4]]: '中度污染',
    [gradeEnum[5]]: '重度污染',
    [gradeEnum[6]]: '严重污染',
}
const splitLevel = (numbers:number[]) => {
    const levels = [
        { min: 0, max: 50 }, // 0-50
        { min: 51, max: 100 }, // 50-100
        { min: 101, max: 150 }, // 100-150
        { min: 151, max: 200 }, // 150-200
        { min: 201, max: 300 }, // 200-300
        { min: 300, max: Infinity } // 300以上
    ];
    return numbers.map((num:number, index:number) => {
        let level = 1; // 默认为等级1
        for (let i = 0; i < levels.length; i++) {
            if (num >= levels[i].min && num < levels[i].max) {
                level = i + 1;
                break;
            }
        }
        return [index, num, level]
    });
}
const startTime = moment().startOf('day').format('YYYY-MM-DD HH:mm:ss')
const endTime = moment().endOf('day').format('YYYY-MM-DD HH:mm:ss')
const box_2_time = ref([startTime, endTime])
const box_2_type = ref(0)
const changeWaterLevelTime = (val:any, type:any) => {
    box_2_time.value = val
    box_2_type.value = type ?? 1;
}

const box_2_data = ref({
    xAxisName: [],
    legendName: ['PM2.5', 'PM10', 'CO', 'SO2', 'NO2', 'O3', 'O3_8'],
    values: [[], [], [], [], [], [], [], []],
    gradeValue: [],
    typeValue: [],
})



const getBox2Data = async() => {
    if (isNullOrEmpty(box_2_type.value)) return;
    const params = {
        dateType: box_2_type.value === 0 ? 1 : 2,
        startTime: box_2_time.value[0],
        endTime: box_2_time.value[1]
    }
    const res = await getAirEnvStatistics(params)
    if (res.success) {
        const data = res.data;
        const xAxisName: string[] = [];
        const pm25List: number[] = [];
        const pm10List: number[] = [];
        const coList: number[] = [];
        const so2List: number[] = [];
        const no2List: number[] = [];
        const o3List: number[] = [];
        const o3_8List: number[] = [];
        const aqiList: number[] = [];

        // aqi指数
        data.forEach((item:any) => {
            xAxisName.push(item.name);
            pm25List.push(item.pm25);
            pm10List.push(item.pm10);
            coList.push(item.co);
            so2List.push(item.so2);
            no2List.push(item.no2);
            o3List.push(item.o3);
            o3_8List.push(item.o3Enight);
            aqiList.push(item.aqi)
        })
        box_2_data.value.xAxisName = xAxisName;
        const newAqiList = splitLevel(aqiList)
        box_2_data.value.values = [pm25List, pm10List, coList, so2List, no2List, o3List, o3_8List, newAqiList]
        let gradeValue: number[];
        box_2_data.value.gradeValue = gradeValue = newAqiList?.map((item:number[]) => gradeEnum[item[2]]);
        box_2_data.value.typeValue = gradeValue.map((item:number) => aqiTypeEnum[item]);
    }
}
watchEffect(getBox2Data)

const box_5_data = ref({
    xAxisName: [],
    legendName: ['PM2.5', 'PM10'],
    values: [[], []]
})

const getBox5Data = async() => {
    const res = await getAirQualityDeviceListAndInfo();
    const newBoxData = {
        xAxisName: [],
        legendName: ['PM2.5', 'PM10'],
        values: [[], []]
    }
    if (res.success) {
         res.data?.forEach((item:any) => {
             newBoxData.xAxisName.push(item.extendInfo.sbmc)
             newBoxData.values[0].push(item.devicePropertyStatusList[1]?.value || '0')
             newBoxData.values[1].push(item.devicePropertyStatusList[0]?.value || '0')
        })
        box_5_data.value = newBoxData;
    }
}
getBox5Data()

const box_6_Type = ref(2)
const box_6_data = ref({
    xAxisName: [],
    legendName: ['PM2.5', 'PM10'],
    barData: [],
    maxData: 10,
    minData: 10
})

const getBox6Data = async() => {
    if (isNullOrEmpty(box_2_type.value)) return;
    const searchType = box_6_Type.value === 2;
    const params = {
        dateType: box_2_type.value === 0 ? 1 : 2,
        startTime: box_2_time.value[0],
        endTime: box_2_time.value[1]
    }
    // debugger
    const res = await getPmTrend(params)
    if (res.success) {
        const xAxisName = []
        const barData = [[], []]
        res.data?.forEach((item:any) => {
            xAxisName.push(item.name);
            if (searchType) {
                barData[0].push(item.pm25 || 0);
                barData[1].push(-item.pm10 || 0);
            } else {
                barData[0].push(item.pm25Max || 0);
                barData[1].push(-item.pm10Max || 0);
            }
        })
        box_6_data.value.barData = barData;
        box_6_data.value.xAxisName = xAxisName;
        box_6_data.value.maxData = Math.max.apply(null, barData[0])
        box_6_data.value.minData = Math.min.apply(null, barData[1])
    }
}

watchEffect(getBox6Data)

const box_7_Type = ref(2)

const box_7_data = ref({
    xAxisName: [],
    values: [],
});

const getBox7Data = async() => {
    console.log('getBox7Data')
    if (isNullOrEmpty(box_2_type.value)) return;
    const searchType = box_7_Type.value === 2;
    const params = {
        dateType: box_2_type.value === 0 ? 1 : 2,
        startTime: box_2_time.value[0],
        endTime: box_2_time.value[1]
    }
    const res = await getNoiceTrend(params);
    if (res.success) {
        const xAxisName = []
        const values = []
        res.data?.forEach((item:any) => {
            xAxisName.push(item.name);
            if (searchType) {
                values.push(item.noise || 0);
            } else {
                values.push(item.noiseMax || 0);
            }
        })
        box_7_data.value.values = values;
        box_7_data.value.xAxisName = xAxisName;
    }
}
watchEffect(getBox7Data)



const meteorologicalDeviceOption = ref([])

const getMeteorologicalDeviceOption = async() => {
    const res = await api_getMonitoringSiteHasDeviceList()
    if (res.success) {
        meteorologicalDeviceOption.value = res.data?.map((item:any) => {
            return {
                label: item.name,
                value: item.id
            }
        })
        if (meteorologicalDeviceOption.value.length > 0) {
            const firstId = meteorologicalDeviceOption.value[0]?.value;
            box_1_device_id.value = firstId;
        }
    }
}
getMeteorologicalDeviceOption();

type AirDeviceItem = {
    label: string,
    value: string,
    deviceCode: string
}

const airQualityDeviceOption = ref<AirDeviceItem[]>([])

const getAirQualityDeviceOption = async() => {
    const res = await getAirQualityDeviceList();
    if (res.success) {
        airQualityDeviceOption.value = res.data.map((item:any) => {
            return {
                label: item.sbmc,
                value: item.id,
                deviceCode: item.deviceCode
            }
        })
        if (airQualityDeviceOption.value.length > 0) {
            const firstId = airQualityDeviceOption.value[0]?.value;
            box_3_device_id.value = firstId;
        }
    }
}
getAirQualityDeviceOption();
</script>

<style scoped lang="less">
.grid-container {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    grid-template-rows: repeat(3, 250px);
    grid-gap: 10px;
    width: 100%; /* 可以根据需要设置容器的宽度 */
    //height: 700px; /* 可以根据需要设置容器的高度 */
    .cell {
        background-color: #fff;
        border-radius: 4px;
    }
    .title{
        width: 100%;
        display: flex;
        justify-content: space-between;
        align-items: center;
        .left{
            font-weight: 600;
            font-size: 16px;
            line-height: 24px;
            color: #1E2A55;
        }
    }
    .cell-1{
        .cell;
        .title;
        grid-column: 1;
        grid-row: 1;
        //background-color: #0E42D2;
        padding: 12px 24px;
        display: flex;
        flex-direction: column;
    }
    .cell-2{
        .cell;
        padding: 16px 24px;
        grid-column: 2 / end;
        grid-row-start: 1;
        grid-row-end: 3;
        display: flex;
        flex-direction: column;
        .title{
            margin-bottom: 16px;
        }
        .top{
            flex: 1;
        }
        .bottom{
            flex: 1;
        }
        .second-title{
            font-size: 12px;
            line-height: 20px;
            color: #798799;
            position: relative;
        }
        .second-title:before{
            position: absolute;
            left: 0;
            bottom: 0;
            content: '';
            width: 24px;
            height: 2px;
            background-color: #165DFF;
        }
        //background-color: #ee9900;
    }
    .cell-3{
        .cell;
        .title;
        grid-column: 1;
        grid-row: 2;
        padding: 12px 24px;
        display: flex;
        flex-direction: column;
    }
    //.cell-4{
    //    .cell;
    //    grid-column: 2 / end;
    //    grid-row: 2;
    //    //background-color: #9a6e3a;
    //}
    .cell-5{
        .cell;
        grid-column: 1;
        grid-row: 3;
        //background-color: #0AA5A8;
    }
    .cell-6{
        .cell;
        grid-column: 2;
        grid-row: 3;
        //background-color: #86909C;
    }
    .cell-7{
        .cell;
        grid-column: 3;
        grid-row: 3;
    }
}
//
</style>
