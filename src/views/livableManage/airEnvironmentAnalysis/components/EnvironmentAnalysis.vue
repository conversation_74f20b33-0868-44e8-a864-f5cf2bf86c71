<template>
    <div class="wrapper">
        <template v-for="(item, index) in data">
            <div class="cell">
                <div class="left">
                    <div v-if="item.images" class="img-wrapper">
                        <img :src="item.images" alt="">
                    </div>
                    <div v-else class="title-round"><span>{{item.title?.replace('浓度', '')}}</span></div>
                    <span class="title">{{ item.title?.replace('浓度', '') }}</span>
                </div>
                <div class="right">
                    <span class="value">
                        {{ item.value }}
                    </span>
                    <span class="unitOfMeasurement">
                        {{ item.unitOfMeasurement }}
                    </span>
                </div>
            </div>
        </template>
<!--        <div class="cell cell-2">2</div>-->
<!--        <div class="cell cell-3">3</div>-->
<!--        <div class="cell cell-4">4</div>-->
<!--        <div class="cell cell-5">5</div>-->
<!--        <div class="cell cell-6">6</div>-->
    </div>
</template>

<script setup lang="ts">
import EchartItem from '@/components/common/EchartItem';
import { computed, defineProps, ref, watch } from 'vue'
import { EchartsTooltip } from '@/utils/tool';

type Props = {
    data: any,
    title: string
}
const props = defineProps<Props>()

const title = computed(() => props.title)

const data = computed<any>(() => props.data)



// const itemsOption

</script>
//
<style scoped lang="less">
.wrapper{
    height: 100%;
    width: 100%;
    padding-top: 16px;
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    grid-template-rows: repeat(3, 54px);
    grid-gap: 8px 16px;
    .cell{
        width: 100%;
        height: 100%;
        border-radius: 4px;
        padding: 0 12px;
        border: 1px solid #A8D1FF;
        display: flex;
        align-items: center;
        justify-content: space-between;
        .left{
            display: flex;
            align-items: center;
            .img-wrapper{
                width: 18px;
                height: 18px;
                text-align: center;
                img{
                    object-fit: contain;
                    height: 18px;
                }
            }
            .title{
                margin-left: 14px;
                font-size: 12px;
                line-height: 20px;
                color: #1E2A55;
            }
        }
        .right{
            .value{
                font-size: 20px;
                line-height: 23px;
                color: #1E2A55;
            }
            .unitOfMeasurement{
                font-size: 12px;
            }
        }
    }
}
.title-round{
    width: 28px;
    height: 28px;
    background: #70B7FF;
    box-shadow: inset 1px 1px 4px rgba(255, 255, 255, 0.8), inset 1px 3px 10px rgba(255, 255, 255, 0.9);
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    span{
        display: inline-block;
        color: #fff;
        white-space: nowrap;
        font-size: 20px;
        scale: 0.4;
    }
}
</style>
