<template>
    <div class="wrapper">
        <EchartItem :option="option"></EchartItem>
    </div>
</template>

<script setup lang="ts">
import EchartItem from '@/components/common/EchartItem';
import { computed, defineProps } from 'vue'
import { EchartsTooltip, tooltipExtraCssText } from '@/utils/tool';

type Data = {
    xAxisName: string[],
    values: number[][],
    legendName: string[]
}
type Props = {
    data: Data,
    title: string,
    legendTop?: number
}
const props = defineProps<Props>()

const title = computed(() => props.title)



const option = computed(() => {
    return {
        title: {
            text: ['{a|6因子}', '{b|}'].join('\n'),
            textStyle: {
                rich: {
                    a: {
                        color: '#798799',
                        lineHeight: 20
                    },
                    b: {
                        backgroundColor: '#165DFF',
                        height: 2,
                        width: 24
                    }
                }
            },
        },
        tooltip: {
            trigger: 'axis',
            borderWidth: 0,
            textStyle: {
                color: '#4E5969',
                fontSize: 12,
            },
            extraCssText: tooltipExtraCssText,
            formatter: (arg:any) => EchartsTooltip(arg, 'μg/m³')
        },
        legend: {
            data: props.data.legendName,
            top: `${props.legendTop}%`
        },
        grid: {
            left: 0,
            right: 0,
            top: 40,
            bottom: 0,
            containLabel: true,
        },
        xAxis: {
            type: 'category',
            data: props.data.xAxisName,
            axisLine: { // 去掉坐标轴轴线
                show: false
            },
            axisTick: { // 去掉坐标轴刻度线
                show: false
            }
        },
        yAxis: {
            type: 'value',
            axisLine: { // 去掉坐标轴轴线
                show: false
            },
            axisTick: { // 去掉坐标轴刻度线
                show: false
            },
            splitLine: {
                show: true,
                lineStyle: {
                    type: 'dashed'
                }
            },
        },
        series: [
            {
                name: props.data.legendName[0],
                type: 'line',
                smooth: true,
                symbol: 'none',
                data: props.data.values[0],
                itemStyle: {
                    color: '#21CCFF', // 柱状颜色
                },
            },
            {
                name: props.data.legendName[1],
                type: 'line',
                smooth: true,
                symbol: 'none',
                data: props.data.values[1],
                itemStyle: {
                    color: '#05D0C4 ', // 柱状颜色
                },
            },
            {
                name: props.data.legendName[2],
                type: 'line',
                smooth: true,
                symbol: 'none',
                data: props.data.values[2],
                itemStyle: {
                    color: '#18D865', // 柱状颜色
                },
            },
            {
                name: props.data.legendName[3],
                type: 'line',
                smooth: true,
                symbol: 'none',
                data: props.data.values[3],
                itemStyle: {
                    color: '#F7BA1E', // 柱状颜色
                },
            },
            {
                name: props.data.legendName[4],
                type: 'line',
                smooth: true,
                symbol: 'none',
                data: props.data.values[4],
                itemStyle: {
                    color: '#FAAC7B', // 柱状颜色
                },
            },
            {
                name: props.data.legendName[5],
                type: 'line',
                smooth: true,
                symbol: 'none',
                data: props.data.values[5],
                itemStyle: {
                    color: '#FF708B', // 柱状颜色
                },
            },
            {
                name: props.data.legendName[6],
                type: 'line',
                smooth: true,
                symbol: 'none',
                data: props.data.values[6],
                itemStyle: {
                    color: '#FF708B', // 柱状颜色
                },
            }
        ],
    };
})
</script>
<style scoped lang="less">
.wrapper{
    height: ~'calc(100% - 21px)';
    width: 100%;
    .title {
        margin: 30px 0 0 0;
        span {
            margin-left: 14px;
            color: #1e2a55;
        }
        img {
            width: 15px;
            height: 4px;
            margin-bottom: 4px;
        }
    }
    :deep(.echart-cont){
        width: 100%;
        height: 100%;
    }
}
</style>
