<template>
    <div class="wrapper">
        <EchartItem :option="option"></EchartItem>
    </div>
</template>

<script setup lang="ts">
import EchartItem from '@/components/common/EchartItem';
import { computed, defineProps, ref, watch } from 'vue'
import { EchartsTooltip, tooltipExtraCssText } from '@/utils/tool';

type Props = {
    data: any,
    title: string
}
const props = defineProps<Props>()

const title = computed(() => props.title)

const data = computed<any>(() => props.data)

const option = ref<any>({});


watch(
    () => data.value,
    () => {
        option.value = {
            tooltip: {
                trigger: 'axis',
                borderWidth: 0,
                textStyle: {
                    color: '#4E5969',
                    fontSize: 12,
                },
                extraCssText: tooltipExtraCssText,
                formatter: (arg:any) => EchartsTooltip(arg, ['次', '元'])
            },
            grid: {
                left: 0,
                right: 0,
                top: 0,
                bottom: 0,
                containLabel: true,
            },
            xAxis: {
                type: 'category',
                data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
                axisLine: { // 去掉坐标轴轴线
                    show: false
                },
                axisTick: { // 去掉坐标轴刻度线
                    show: false
                }
            },
            yAxis: {
                type: 'value',
                axisLine: { // 去掉坐标轴轴线
                    show: false
                },
                axisTick: { // 去掉坐标轴刻度线
                    show: false
                }
            },

            series: [
                {
                    name: data.value.legendNameList[0],
                    stack: 'x',
                    type: 'bar',
                    data: data.value.barData[0],
                    itemStyle: {
                        color: '#246EFF', // 柱状颜色
                    },
                },
                {
                    name: data.value.legendNameList[1],
                    stack: 'x',
                    type: 'bar',
                    data: data.value.barData[1],
                    itemStyle: {
                        color: '#07ee2e', // 柱状颜色
                    },
                },

            ],
        };
    }, {
        immediate: true
    }
);

// symbolSize: function (dataItem) {
//     return dataItem[1] * 4;
// }

</script>
//
<style scoped lang="less">
.wrapper{
    height: 100%;
    width: 100%;
    .title {
        margin: 30px 0 0 0;
        span {
            margin-left: 14px;
            color: #1e2a55;
        }
        img {
            width: 15px;
            height: 4px;
            margin-bottom: 4px;
        }
    }
    :deep(.echart-cont){
        width: 100%;
        height: 100%;
    }
}
</style>
