<template>
    <div class="wrapper">
        <div class="title">
            <span>{{ title }}</span>
            <CheckBtn left-text="平均值" right-text="最大值" v-model="facilityType" />
        </div>
        <EchartItem :option="option"></EchartItem>
    </div>
</template>

<script setup lang="ts">
import EchartItem from '@/components/common/EchartItem';
import { computed, defineEmits, defineProps } from 'vue'
import { EchartsTooltip, tooltipExtraCssText } from '@/utils/tool';
import CheckBtn
    from '@/views/trafficManage/wisdomRoadBridge/statisticalAnalysis/components/CheckBtn.vue';
import { useVModel } from '@/hooks/useVModel';

type Data = {
    xAxisName: string[],
    values: number[],
    legendName: string[]
}
type Props = {
    data: Data,
    title: string,
    facilityType: number,
}
const props = defineProps<Props>()

const title = computed(() => props.title)
const emits = defineEmits(['update:facilityType'])
const facilityType = useVModel(props, 'facilityType', emits)


const option = computed(() => {
    return {
        tooltip: {
            trigger: 'axis',
            borderWidth: 0,
            textStyle: {
                color: '#4E5969',
                fontSize: 12,
            },
            extraCssText: tooltipExtraCssText,
            formatter: (arg:any) => EchartsTooltip(arg, ['dB'])
        },
        grid: {
            left: 0,
            right: 0,
            top: 30,
            bottom: 0,
            containLabel: true,
        },
        xAxis: {
            type: 'category',
            data: props.data.xAxisName,
            axisLine: { // 去掉坐标轴轴线
                show: false
            },
            axisTick: { // 去掉坐标轴刻度线
                show: false
            }
        },
        yAxis: {
            name: '单位:dB',
            nameLocation: 'end',
            type: 'value',
            axisLine: { // 去掉坐标轴轴线
                show: false
            },
            axisTick: { // 去掉坐标轴刻度线
                show: false
            },
            splitLine: {
                show: true,
                lineStyle: {
                    type: 'dashed'
                }
            },
        },
        series: [
            {
                name: '噪音值',
                type: 'line',
                symbol: 'none',
                smooth: true,
                data: props.data.values,
                itemStyle: {
                    color: '#21CCFF', // 柱状颜色
                },
            }
        ],
    };
})
</script>
<style scoped lang="less">
.wrapper{
    height: 100%;
    width: 100%;
    padding: 16px 24px;
    .title {
        display: flex;
        justify-content: space-between;
        margin-bottom: 16px;
        :deep(.btn-wrapper){
            width: 116px;
            height: 24px;
        }
        span {
            .title {
                display: flex;
                justify-content: space-between;
                margin-bottom: 16px;
                span {
                    font-weight: 600;
                    font-size: 16px;
                    line-height: 24px;
                    color: #1E2A55;
                }
            }
            font-weight: 600;
            font-size: 16px;
            line-height: 24px;
            color: #1E2A55;
        }
    }
    :deep(.echart-cont){
        width: 100%;
        height: ~'calc(100% - 40px)';
    }
}
</style>
