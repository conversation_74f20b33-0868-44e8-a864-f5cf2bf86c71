<template>
    <div class="wrapper">
        <div class="title">
            <span>{{ title }}</span>
        </div>
        <EchartItem :option="option"></EchartItem>
    </div>
</template>

<script setup lang="ts">
import EchartItem from '@/components/common/EchartItem';
import { computed, defineProps } from 'vue'
import { EchartsTooltip, tooltipExtraCssText } from '@/utils/tool';


type Data = {
    xAxisName: string[],
    values: number[][],
    legendName: string[]
}
type Props = {
    data: Data,
    title: string,
}
const props = defineProps<Props>()

const title = computed(() => props.title)



const option = computed(() => {
    return {
        tooltip: {
            trigger: 'axis',
            borderWidth: 0,
            textStyle: {
                color: '#4E5969',
                fontSize: 12,
            },
            extraCssText: tooltipExtraCssText,
            formatter: (arg:any) => EchartsTooltip(arg, ['μg/m³', 'μg/m³'])
        },
        legend: {
            data: props.data.legendName,
            itemWidth: 10,
            itemHeight: 10,
        },
        grid: {
            left: 20,
            right: 0,
            top: 30,
            bottom: 0,
            containLabel: true,
        },
        xAxis: {
            type: 'category',
            data: props.data.xAxisName,
            axisLine: { // 去掉坐标轴轴线
                show: false
            },
            axisTick: { // 去掉坐标轴刻度线
                show: false
            }
        },
        yAxis: {
            type: 'value',
            nameTextStyle: {
                textAlign: 'left'
            },
            name: '单位：μg/m³',
            axisLabel: {
                formatter: '{value}'
            },
            axisLine: { // 去掉坐标轴轴线
                show: false
            },
            splitLine: {
              show: true,
              lineStyle: {
                type: 'dashed'
              }
            },
            axisTick: { // 去掉坐标轴刻度线
                show: false
            }
        },
        series: [
            {
                name: props.data.legendName[0],
                type: 'pictorialBar',
                data: props.data.values[0],
                color: '#3491FA', // 柱状颜色
                itemStyle: {
                    normal: {
                        barBorderRadius: 1
                    }
                },
                symbol: 'roundRect',
                symbolOffset: [-8, 0],
                symbolRepeat: true,
                symbolSize: [12, 6]
            },
            {
                name: props.data.legendName[1],
                type: 'pictorialBar',
                data: props.data.values[1],
                color: '#33D1C9', // 柱状颜色
                itemStyle: {
                    normal: {
                        barBorderRadius: 1
                    }
                },
                symbol: 'roundRect',
                symbolOffset: [8, 0],
                symbolRepeat: true,
                symbolSize: [12, 6]
            }
        ],
    };
})
</script>
<style scoped lang="less">
.wrapper{
    height: 100%;
    width: 100%;
    padding: 16px 24px;
    .title {
        display: flex;
        justify-content: space-between;
        margin-bottom: 16px;
        span {
            .title {
            display: flex;
            justify-content: space-between;
            margin-bottom: 16px;
            span {
                font-weight: 600;
                font-size: 16px;
                line-height: 24px;
                color: #1E2A55;
            }
        }
            font-weight: 600;
            font-size: 16px;
            line-height: 24px;
            color: #1E2A55;
        }
    }
    :deep(.echart-cont){
        width: 100%;
        height: ~'calc(100% - 40px)';;
    }
}
</style>
