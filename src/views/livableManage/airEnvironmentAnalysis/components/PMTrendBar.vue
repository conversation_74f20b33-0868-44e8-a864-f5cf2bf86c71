<template>
    <div class="wrapper">
        <div class="title">
            <span>{{ title }}</span>
            <CheckBtn left-text="平均值" right-text="最大值" v-model="facilityType" />
        </div>
        <EchartItem :option="option"></EchartItem>
    </div>
</template>

<script setup lang="ts">
import EchartItem from '@/components/common/EchartItem';
import { computed, defineEmits, defineProps } from 'vue'
import { EchartsTooltip, tooltipExtraCssText } from '@/utils/tool';
import CheckBtn
    from '@/views/trafficManage/wisdomRoadBridge/statisticalAnalysis/components/CheckBtn.vue';
import { useVModel } from '@/hooks/useVModel';

type Props = {
    data: any,
    title: string,
    facilityType: number,
}
const props = defineProps<Props>()

const title = computed(() => props.title)

const data = computed<any>(() => props.data)


const emits = defineEmits(['update:facilityType'])

const facilityType = useVModel(props, 'facilityType', emits)

function roundUpToNearestTen(num:number) {
    console.log('fsdasdfasdf', Math.ceil(num / 10) * 10)
    return Math.ceil(num / 20) * 20
}

const option = computed(() => {
    console.log('asdfasdff', { min: data.value.minData, max: data.value.maxData })
    return {
        tooltip: {
            // trigger: 'axis',
            borderWidth: 0,
            textStyle: {
                color: '#4E5969',
                fontSize: 12,
            },
            extraCssText: tooltipExtraCssText,
            formatter: (arg:any) => EchartsTooltip(arg, ['μg/m³', 'μg/m³'])
        },
        legend: {
            data: props.data.legendName,
            top: 0,
            nameTextStyle: {
                textAlign: 'left'
            },
            itemWidth: 10,
            itemHeight: 10,
        },
        grid: {
            left: 0,
            right: 0,
            top: 30,
            bottom: 0,
            containLabel: true,
        },
        yAxis: [
            {
                interval: 20,
                position: 'left',
                axisLine: {
                    onZero: false
                },
                name: '单位(μg/m³)',
                type: 'value',
                axisLabel: {
                    formatter: (value: number) => {
                        return value > 0 ? value : ''
                    }
                },
                splitLine: {
                    show: true,
                    lineStyle: {
                        type: 'dashed'
                    }
                },
                max: roundUpToNearestTen(data.value.maxData),
                min: -roundUpToNearestTen(-data.value.minData),

            }, {
                interval: 20,
                position: 'left',
                type: 'value',
                splitLine: {
                    show: false,
                },
                axisLabel: {
                    formatter: (value: number) => {
                        return value > 0 ? '' : -value
                    }
                },
                max: roundUpToNearestTen(data.value.maxData),
                min: -roundUpToNearestTen(-data.value.minData)
            }
        ],
        xAxis: [
            {
                splitNumber: 5,
                type: 'category',
                axisTick: {
                    show: false,
                    alignWithLabel: true,
                },
                data: data.value.xAxisName,
                axisLine: {
                    lineStyle: {
                        color: '#E0E6F1'
                    }
                },
                axisLabel: {
                    textStyle: {
                        color: '#798799'
                    }
                }
            }
        ],
        series: [
            {
                name: data.value.legendName[0],
                type: 'bar',
                stack: 'Total',
                yAxisIndex: 0,
                barMaxWidth: 12,
                label: {
                    show: false,
                },
                itemStyle: {
                    color: '#FAAC7B', // 柱状颜色
                },
                data: data.value.barData[0],
            },
            {
                name: data.value.legendName[1],
                type: 'bar',
                stack: 'Total',
                yAxisIndex: 1,
                barMaxWidth: 12,
                label: {
                    show: false,
                    position: 'right',
                    formatter: function(params) { // 将负号替换为空字符串
                        return Math.abs(params.value);
                    }
                },
                itemStyle: {
                    color: '#33D1C9', // 柱状颜色
                },
                data: data.value.barData[1]
            }
        ]
    }
})

</script>
//
<style scoped lang="less">
.wrapper{
    height: 100%;
    width: 100%;
    padding: 16px 24px;
    .title {
        display: flex;
        justify-content: space-between;
        margin-bottom: 16px;
        span {
            font-weight: 600;
            font-size: 16px;
            line-height: 24px;
            color: #1E2A55;
        }
        :deep(.btn-wrapper){
            width: 116px;
            height: 24px;
        }
    }
    :deep(.echart-cont){
        width: 100%;
        height: ~'calc(100% - 40px)';
    }
}

</style>
