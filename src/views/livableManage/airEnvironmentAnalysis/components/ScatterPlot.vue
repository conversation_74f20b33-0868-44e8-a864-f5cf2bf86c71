<template>
    <div class="wrapper">
        <EchartItem :option="option"></EchartItem>
    </div>
</template>

<script setup lang="ts">
import EchartItem from '@/components/common/EchartItem';
import { computed, defineProps, ref, watch } from 'vue'
import { tooltipExtraCssText, hexToRgb } from '@/utils/tool';

type Props = {
    data: any,
    title: string
}
const props = defineProps<Props>()

const title = computed(() => props.title)

const data = computed<any>(() => props.data)

// const option = ref<any>({});
const gradeEnum = {
    '1': '50',
    '2': '100',
    '3': '150',
    '4': '200',
    '5': '300',
    '6': '',
}
const option = computed(() => {
    const colorLine = ['#21CCFF', '#05D0C4', '#18D865', '#F7BA1E', '#FAAC7B', '#FF708B']
    return {
        title: {
            text: ['{a|AQI指数}', '{b|}'].join('\n'),
            textStyle: {
                rich: {
                    a: {
                        color: '#798799',
                        lineHeight: 20
                    },
                    b: {
                        backgroundColor: '#165DFF',
                        height: 2,
                        width: 24
                    }
                }
            },
        },
        grid: {
            left: 10,
            right: 150,
            top: 40,
            bottom: 0,
            containLabel: true
        },
        tooltip: {
            borderWidth: 0,
            extraCssText: tooltipExtraCssText,
            formatter: (arg:any, index) => {
                const { dataIndex } = arg;
                 return `
        <div style="">
        <div style="padding:8px 0 8px 14px;">${arg.name}</div>
        <div style="
        display:flex;
        width: 200px;
        flex-direction: column;
        row-gap: 4px;
        ">
          <div style="
          display:flex;
          align-items: center;
          height: 32px;
          justify-content: flex-start;
          background: rgba(255, 255, 255, 0.9);
          box-shadow: 6px 0px 20px rgba(34, 87, 188, 0.1);
          border-radius: 4px;
          ">
          <span style="margin:0 8px;height:11px;width:11px;border-radius:50%;background:${hexToRgb(arg.color)};"></span>
          <span style="
          color: #4E5969;
          font-size: 12px;
          font-family: 'PingFang SC';
          ">空气质量指数</span>
          <span style="
          color: #1D2129;
          font-size: 13px;
          font-family: 'PingFang SC';
          font-weight: 720;
          margin-left: auto;
          margin-right: 8px;
          ">${(arg.data[1] || arg.data[1] != 0) ? arg.data[1] : '-'}μg/m³</span>
          </div>
          <div style="
          display:flex;
          align-items: center;
          height: 32px;
          justify-content: flex-start;
          background: rgba(255, 255, 255, 0.9);
          box-shadow: 6px 0px 20px rgba(34, 87, 188, 0.1);
          border-radius: 4px;
          ">
          <span style="margin:0 8px;height:11px;width:11px;border-radius:50%;background:${hexToRgb(arg.color)};"></span>
          <span style="
          color: #4E5969;
          font-size: 12px;
          font-family: 'PingFang SC';
          ">空气质量指数级别</span>
          <span style="
          color: #1D2129;
          font-size: 13px;
          font-family: 'PingFang SC';
          font-weight: 720;
          margin-left: auto;
          margin-right: 8px;
          ">${data.value.gradeValue[dataIndex]}</span>
          </div><div style="
          display:flex;
          align-items: center;
          height: 32px;
          justify-content: flex-start;
          background: rgba(255, 255, 255, 0.9);
          box-shadow: 6px 0px 20px rgba(34, 87, 188, 0.1);
          border-radius: 4px;
          ">
          <span style="margin:0 8px;height:11px;width:11px;border-radius:50%;background:${hexToRgb(arg.color)};"></span>
          <span style="
          color: #4E5969;
          font-size: 12px;
          font-family: 'PingFang SC';
          ">空气质量指数类别</span>
          <span style="
          color: #1D2129;
          font-size: 13px;
          font-family: 'PingFang SC';
          font-weight: 720;
          margin-left: auto;
          margin-right: 8px;
          ">${data.value.typeValue[dataIndex]}</span>
          </div>
          </div>
          </div>
        `;
            }
        },
        xAxis: {
            type: 'category',
            data: props.data.xAxisName,
            axisTick: {
                alignWithLabel: true,
                lineStyle: {
                    color: '#C2CAD8'
                }
            },
            axisLine: {
                lineStyle: {
                    color: '#C2CAD8'
                }
            },
            nameTextStyle: {
                color: '#798799'
            },
            axisLabel: {
                color: '#798799',
            }
        },
        yAxis: {
            type: 'value',
            max: 300,
            min: 0,
            interval: 50,
            // axisLabel: {
            //     formatter: (value: number) => {
            //         return gradeEnum[value]
            //     }
            // },
            splitLine: {
                show: true,
                lineStyle: {
                    color: '#E0E6F1',
                        type: 'dashed',
                        width: 0.5
                }
            },
            axisLine: { show: false },
            axisTick: { show: false }
        },
        visualMap: [
            {
                right: 0,
                top: 10,
                dimension: 2,
                min: 1,
                max: 6,
                itemWidth: 10,
                itemHeight: 50,
                calculable: true,
                precision: 0.1,
                text: ['圆形大小：AQI等级'],
                textStyle: {
                    color: '#798799'
                },
                textGap: 15,
                inRange: {
                    symbolSize: [10, 32]
                },
                padding: 0,
                outOfRange: {
                    symbolSize: [10, 32],
                    opacity: 0.5
                },
                controller: {
                    inRange: {
                        color: ['#6AA1FF', '#165DFF']
                    },
                    outOfRange: {
                        color: ['#999']
                    }
                }
            },
            {
                top: 110,
                right: 0,
                bottom: 10,
                dimension: 2,
                min: 1,
                max: 6,
                itemWidth: 10,
                itemHeight: 50,
                text: ['圆形颜色：AQI等级'],
                textStyle: {
                    color: '#798799'
                },
                textGap: 15,
                padding: 0,
                inRange: {
                    color: colorLine,
                },
                controller: {
                    inRange: {
                        color: colorLine
                    },
                    outOfRange: {
                        color: ['#999']
                    }
                }
            }
        ],
        series: [
            {
                name: 'AQI指数',
                type: 'scatter',
                // symbolSize: (value, params) => {
                //     return 10;
                // },
                data: props.data.values[7]
            }
        ]
    }
})

</script>
<style scoped lang="less">
.wrapper{
    height: 100%;
    width: 100%;
    .title {
        margin: 30px 0 0 0;
        span {
            margin-left: 14px;
            color: #1e2a55;
        }
        img {
            width: 15px;
            height: 4px;
            margin-bottom: 4px;
        }
    }
    :deep(.echart-cont){
        width: 100%;
        height: 100%;
    }
}
</style>
