<template>
    <Row :gutter="80">
        <Col span="8">
        <s-label label="计划名称" :value="detailInfo?.planName" />
        </Col>
        <Col span="8">
        <s-label label="计划编号" :value="detailInfo?.code" />
        </Col>
        <Col span="8">
        <s-label label="开启状态">
            <template #value>
                <useStatus :value="detailInfo?.planStatus" />
            </template>
        </s-label>
        </Col>
        <Col span="8">
        <s-label label="收运人" :value="detailInfo?.contactPerson" />
        </Col>
        <Col span="8">
        <s-label label="联系电话" :value="detailInfo?.contactPhone" />
        </Col>
        <Col span="8">
        <s-label label="收运公司" :tooltip="false" :value="detailInfo?.companyName || ''">
            <!-- <template #value>
                <div v-if="detailInfo?.companyId">

                    <collect-company-select :type="typecode" v-model="detailInfo.companyId" classification="label" />
                </div>
                <span v-else>--</span>
            </template> -->
        </s-label>
        </Col>
        <Col span="8">
        <s-label label="收运车" :tooltip="false">
            <template #value>
                <div v-if="detailInfo?.carId">
                    <collect-car-select :type="typecode" v-model="detailInfo.carId" classification="label" />
                </div>
                <span v-else>--</span>
            </template>
        </s-label>
        </Col>
        <Col span="8">
        <s-label label="垃圾处理厂" :tooltip="false">
            <template #value>
                <div v-if="detailInfo?.factoryId">
                    <collect-factory-select :type="typecode" v-model="detailInfo.factoryId" classification="label" />
                </div>
                <span v-else>--</span>
            </template>
        </s-label>
        </Col>
        <Col span="8" v-if="typecode == '2'">
            <s-label label="收集时间" :tooltip="false" :value="detailInfo?.collectTime">
                <template #value>
                    <div v-if="detailInfo?.collectTime">
                        {{ detailInfo?.collectTime[0] }} - {{ detailInfo?.collectTime[1]}}
                    </div>
                    <span v-else>--</span>
            </template>
            </s-label>
        </Col>


        <Col span="24">
        <s-label label="备注" :value="detailInfo?.note" :tooltip="false" />
        </Col>
    </Row>
</template>
<script lang="ts">
import { garbagePlanInfo } from '@/api/livableManage/type'
import { defineComponent, ref, watch } from 'vue';
import collectCompanySelect from '../../components/collectCompanySelect/index'
import collectCarSelect from '../../components/collectCarSelect/index'
import collectFactorySelect from '../../components/collectFactorySelect/index'
import { getRouterQuery } from '@/utils/tool'
export default defineComponent({
    components: {
        collectCompanySelect,
        collectCarSelect,
        collectFactorySelect
    },
    props: {
        detail: {
            type: Object,
            default: () => { }
        }
    },
    setup(props) {
        const detailInfo = ref<garbagePlanInfo>({
            companyId: 0,
            carId: 0,
            factoryId: 0
        });
        const typecode = getRouterQuery('typecode') as string;
        watch(props, () => {
            detailInfo.value = props.detail
        }, { deep: true, immediate: true })
        return {
            detailInfo,
            typecode
        }
    }
})
</script>
