<template>
    <div class="modal-map">
        <div class="device-cont plate-bg">
            <Input suffix="ios-search" placeholder="请输入收运点信息" clearable v-model="searchText" />
            <map-device-list :deviceList="garbagePoint" :searchText="searchText" lableKey="name" valueKey="code"
                :activedObjId="activedObjId" @actived-obj="handleActivedObj" :show-tag="[]" />
        </div>
        <div id="garbage-map-modal" class="garbage-map-box"></div>
    </div>
</template>
<script lang="ts">
import { defineComponent, ref, onMounted, nextTick, watch } from 'vue';
import AMapLoader from '@amap/amap-jsapi-loader';
import { garbagePointInfo } from '@/api/livableManage/type'
import { getMarkerContent } from '@/components/common/mapAreaTreeSelect/markercontent'
import mapDeviceList from '@/components/common/mapDeviceList'
export default defineComponent({
    components: {
        mapDeviceList
    },
    props: {
        garbagePoint: {
            type: Array,
            default: () => []
        }
    },
    setup(props) {
        // *********************
        // 地图
        // *********************
        // 初始化图标
        const map = ref<any>(null);
        const Amap = ref<any>(null);
        const ininMap = () => {
            AMapLoader.load({
                key: 'ea53a5291f8f7c1f215ba20930ea9866',
                version: '2.0'
            }).then((AMap) => {
                Amap.value = AMap
                map.value = new AMap.Map('garbage-map-modal', {
                    resizeEnable: true,
                    // mapStyle: 'https://geohub.amap.com/mapstyle/clone?id=20d3f4f1e43c80b9c68c30fb8a1ab94b',
                    zoom: 16,
                    center: [114.61, 30.45]
                })
                const _val = (props.garbagePoint as garbagePointInfo[]).filter(k => k.checked)
                updateMarker(_val)
            })

        }
        // 更新坐标点
        const updateMarker = (data: garbagePointInfo[]) => {

            if (!map.value) return
            // 清除地图上的图标
            map.value.clearMap()
            if (!data?.length) return
            data.forEach((item: garbagePointInfo) => {

                if (!item.gdx || !item.gdy) return
                if (item.checked) item['alarmState'] = 1
                else item['alarmState'] = 0

                const content = getMarkerContent(item, '', 'hide-tag', (d: garbagePointInfo) => {
                    return require('../images/icon_streetLight_001.png')
                })
                const marker = new Amap.value.Marker({
                    position: [item.gdx, item.gdy],
                    offset: new Amap.value.Pixel(-5, -35),
                    extData: item.id,
                    content,
                })
                marker.on('click', (e: any) => {
                    const id = e.target.getExtData()
                    if (id == activedObjId.value) {
                        activedObjId.value = 0
                    } else {
                        activedObjId.value = id
                    }
                })
                map.value.add([marker])
            })
            nextTick(() => {
                map.value.setFitView();
                nextTick(() => {
                    // map.value.setZoom(map.value.getZoom() - 3);
                })
            })
        }
        const garbagePoint = ref<garbagePointInfo[]>();
        const searchText = ref('')
        const activedObjId = ref<number>(0);
        watch(() => activedObjId.value, () => {

            const overlays: any[] = map.value?.getAllOverlays('marker')
            overlays?.forEach(e => {
                const markerInfo = garbagePoint.value!.filter(k => k.id == e.getExtData())[0] || {}
                const content = getMarkerContent(markerInfo, activedObjId.value, 'hide-tag', (d: garbagePointInfo) => {
                    return require('../images/icon_streetLight_001.png')
                });
                e.setContent(content)
            })
        })
        const handleActivedObj = (data: garbagePointInfo) => {
            if (activedObjId.value == data.id) {
                activedObjId.value = 0
            } else {
                activedObjId.value = data.id as number
            }
        }
        watch(() => props.garbagePoint, (val) => {
            const _val = (val as garbagePointInfo[]).filter(k => k.checked)
            updateMarker(_val)
            garbagePoint.value = _val
        }, { deep: true, immediate: true })
        onMounted(() => {
            nextTick(() => {
                ininMap();
            })
        },)
        return {
            garbagePoint,
            searchText,
            handleActivedObj,
            activedObjId
        }
    },
})
</script>
<style lang="less" scoped>
@import '../../../../../styles/mapPage.less';
@import '../index.less';
</style>
<style lang="less" scoped>
.modal-map {
    display: flex;

    .device-cont {
        width: 180px;
        padding: 16px 8px;

        /deep/.device-info {
            .device-name .name {
                color: #1E2A55;
            }
        }
    }

    .garbage-map-box {
        height: 50vh;
        flex: 1;

        /deep/.hide-tag {
            display: none;
        }
    }
}
</style>
