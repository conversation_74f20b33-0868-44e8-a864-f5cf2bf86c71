<template>
    <Form :model="formItem" inline>
        <FormItem>
            <collect-task-select v-model="formItem.taskId" :type="typecode" :id="id" />
        </FormItem>
        <FormItem>
            <collect-point-select v-model="formItem.pointId" :type="typecode" :id="id" checked />
        </FormItem>
    </Form>
    <base-table ref="recordTbRef" :columns="recordColumns" url="/garbageTaskRecords/list" :page-size="5">
        <template #status="scope">
            <span v-if="scope['row'].status == 1">已收运</span>
            <span v-else>未收运</span>
        </template>
        <template #doneGarbageNum="scope">
            <span v-if="scope['row'].status != 1">--</span>
            <span v-else>{{ scope!['row'].doneGarbageNum }}</span>
        </template>
        <template #weight="scope">
            <span v-if="scope['row'].status != 1">--</span>
            <span v-else>{{ scope!['row'].weight }}</span>
        </template>
        <template #content="scope">
            <span v-if="scope['row'].status != 1">--</span>
            <span v-else>{{ scope!['row'].content }}</span>
        </template>
        <template #contactPerson="scope">
            <span v-if="scope['row'].status != 1">--</span>
            <span v-else>{{ scope!['row'].contactPerson }}</span>
        </template>
        <template #time="scope">
            <span v-if="scope['row'].status != 1">--</span>
            <span v-else>{{ scope!['row'].time }}</span>
        </template>
    </base-table>
</template>
<script lang="ts">
import { recordColumnsList } from '../data/columns';
import { defineComponent, onMounted, ref, watch } from 'vue';
import collectTaskSelect from '../../components/collectTaskSelect/index'
import collectPointSelect from '../../components/collectPointSelect/index'
import { getRouterQuery } from '@/utils/tool'
export default defineComponent({
    components: { collectTaskSelect, collectPointSelect },
    setup() {
        const recordColumns = recordColumnsList();
        const recordTbRef = ref();
        const formItem = ref({
            taskId: null,
            pointId: null
        });
        const typecode = getRouterQuery('typecode') as string;
        const id = getRouterQuery('id') as string;
        const handleSearch = () => {
            const params = {
                planId: id,
                ...formItem.value
            }
            recordTbRef.value?.search(params)
        };
        onMounted(() => {
            handleSearch();
        });
        watch(() => formItem.value, () => {
            handleSearch();
        }, { deep: true });
        return {
            recordColumns,
            recordTbRef,
            formItem,
            id,
            typecode
        };
    }
})
</script>
<style lang="less" scoped>
/deep/.ivu-form-item {
    width: 296px;
    margin-right: 0;

    &+.ivu-form-item {
        margin-left: 24px;
    }
}
</style>
