<template>
    <Form ref="addForm" label-position="top" :rules="ruleValidate" :model="addFormValue">
        <Row :gutter="80">
            <Col span="8">
                <FormItem label="计划名称" prop="planName">
                    <Input v-model="addFormValue.planName" clearable placeholder="请输入"></Input>
                </FormItem>
            </Col>
            <Col span="8">
                <FormItem label="计划编号" prop="code">
                    <Input v-model="addFormValue.code" disabled placeholder="系统自动生成"></Input>
                </FormItem>
            </Col>
            <Col span="8">
                <FormItem label="开启状态" prop="planStatus">
                    <Select v-model="addFormValue.planStatus" clearable placeholder="请选择">
                        <Option
                            v-for="(item, index) in enumeration.deviceUesState"
                            :value="index"
                            :key="index"
                        >
                            {{ item }}
                        </Option>
                    </Select>
                </FormItem>
            </Col>
            <Col span="8">
                <FormItem label="收运人" prop="contactId">
                    <!-- <Input
                        v-if="origin !== '餐厨'"
                        v-model="addFormValue.contactPerson"
                        clearable
                    ></Input> -->
                    <Select
                        @on-clear="
                            () => {
                                addFormValue.contactPhone = '';
                                addFormValue.companyName = '';
                                addFormValue.contactId = '';
                            }
                        "
                        :model-value="addFormValue.contactId?.toString()"
                        placeholder="请选择"
                        clearable
                    >
                        <Option
                            v-for="(item, index) in personList"
                            :key="item.userId"
                            @click="handlePersonSelect(item)"
                            :value="item.userId.toString()"
                            >{{ item.userName }}</Option
                        >
                    </Select>
                </FormItem>
            </Col>
            <Col span="8">
                <FormItem label="联系电话" prop="contactPhone">
                    <Input
                        placeholder="选择收运人自动带出"
                        disabled
                        v-model="addFormValue.contactPhone"
                        clearable
                    ></Input>
                </FormItem>
            </Col>
            <Col span="8">
                <FormItem label="收运公司" prop="companyName">
                    <!-- <collect-company-select v-model="addFormValue.companyName" :type="+typeCode" /> -->
                    <Input
                        :model-value="addFormValue.companyName"
                        placeholder="选择收运人自动带出"
                        disabled
                    ></Input>
                </FormItem>
            </Col>
            <Col span="8">
                <FormItem label="收运车辆" prop="carId">
                    <collect-car-select :type="+typeCode" v-model="addFormValue.carId" />
                </FormItem>
            </Col>
            <Col span="8">
                <FormItem label="垃圾处理厂" prop="factoryId">
                    <collectFactorySelect :type="+typeCode" v-model="addFormValue.factoryId" />
                </FormItem>
            </Col>
            <Col span="8" v-if="typeCode == '2'">
                <FormItem label="收集时间" prop="collectTime">
                   <TimePicker type="timerange" @on-change="chaneTime" v-model="addFormValue.collectTime" format="HH:mm" />
                </FormItem>
            </Col>

            <Col span="24">
                <FormItem label="收运周期" prop="timeType" class="time-type">
                    <RadioGroup v-model="addFormValue.timeType">
                        <Radio :label="1">每天</Radio>
                        <Radio :label="2">每周</Radio>
                        <Radio :label="3">每月</Radio>
                    </RadioGroup>
                </FormItem>
            </Col>
            <Col span="24">
                <FormItem prop="timeValues" class="time-values" v-show="addFormValue.timeType != 1">
                    <CheckboxGroup v-model="addFormValue.timeValues">
                        <div v-if="addFormValue.timeType == 2">
                            <Checkbox :label="`${item}`" v-for="(item, index) in 7" :key="index">{{
                                weekList[item]
                            }}</Checkbox>
                        </div>
                        <div v-if="addFormValue.timeType == 3" class="time-month">
                            <Checkbox :label="`${item}`" v-for="(item, index) in 31" :key="index">{{
                                `${item}`
                            }}</Checkbox>
                        </div>
                    </CheckboxGroup>
                </FormItem>
            </Col>
            <Col span="24">
                <FormItem label="收运点" prop="garbagePlanRefList" class="garbage-plan-list">
                    <div class="garbage-list">
                        <div class="title">
                            <Checkbox
                                class="check-all"
                                :indeterminate="indeterminate"
                                :model-value="checkAll"
                                @click.prevent="handleCheckAll"
                            >
                                待绑收运点
                            </Checkbox>
                            {{ addFormValue.garbagePlanRefList?.length }}/{{
                                getGarbagePointList?.length
                            }}
                        </div>
                        <Input
                            suffix="ios-search"
                            placeholder="请输入"
                            clearable
                            v-model="searchTreeText"
                        />
                        <scorll-box :scorll-id="activedObjId" key="code">
                            <template #cont>
                                <CheckboxGroup
                                    v-model="addFormValue.garbagePlanRefList"
                                    @on-change="checkAllGroupChange"
                                >
                                    <div
                                        class="garbage-checkbox"
                                        v-for="(item, index) in getGarbagePointList.filter(
                                            (k) =>
                                                !searchTreeText || k.name?.includes(searchTreeText)
                                        )"
                                        :key="index"
                                    >
                                        <Checkbox :label="item.id" @click="handleSelect(item)">
                                            <tooltip-auto-show>{{ item.name }}</tooltip-auto-show>
                                        </Checkbox>
                                    </div>
                                </CheckboxGroup>
                            </template>
                        </scorll-box>
                    </div>
                    <div class="garbage-map container-map" id="garbage-map"></div>
                    <div class="position-tip">
                        <div><span class="gray"></span>未绑定</div>
                        <div><span class="green"></span>已绑定</div>
                    </div>
                </FormItem>
            </Col>
            <Col span="24">
                <FormItem label="备注" prop="note">
                    <Input
                        v-model="addFormValue.note"
                        type="textarea"
                        :rows="4"
                        placeholder="请输入"
                        maxlength="200"
                        show-word-limit
                    ></Input>
                </FormItem>
            </Col>
        </Row>
    </Form>
</template>
<script lang="ts">
import Util from "@/utils";
import { defineComponent, ref, onMounted, nextTick, watch, computed } from "vue";
import { contactPersonList } from "@/hooks/contactPerson";
import scorllBox from "@/components/common/scorllBox/index";
import useLoading from "@/hooks/loading";
import AMapLoader from "@amap/amap-jsapi-loader";
import {
    garbagePointList,
    addGarbagePlan,
    garbagePlanEdit,
    garbageCollectCompanyList,
} from "@/api/livableManage/foodWasteManagementService";
import { garbagePointInfo, garbagePlanInfo } from "@/api/livableManage/type";
import collectCompanySelect from "../../components/collectCompanySelect/index";
import collectCarSelect from "../../components/collectCarSelect/index";
import collectFactorySelect from "../../components/collectFactorySelect/index";
import { enumeration } from "@/config/enumeration";
import { validateform } from "@/utils/validateform";
import { Message } from "view-ui-plus";
import { useRouter } from "vue-router";
import { getRouterQuery } from "@/utils/tool";
import { getMarkerContent } from "@/components/common/mapAreaTreeSelect/markercontent";
import { ComponentInfo } from "@/api/manholeCoverService";
import { useRoute } from "vue-router";
import { deepClone } from "wei-util";

export default defineComponent({
    props: {
        detail: {
            type: Object,
            default: () => ({}),
        },
        planId: {
            type: String,
            default: "",
        },
        origin: {
            type: String,
            default: "",
        },
    },
    components: {
        scorllBox,
        collectCompanySelect,
        collectCarSelect,
        collectFactorySelect,
    },
    setup(props) {
        const router = useRouter();
        const route = useRoute();
        const { loading, setLoading } = useLoading();
        // 获取收运人下拉列表
        const personList = ref<any>([]);
        contactPersonList().then((res: any) => {
            personList.value = res;
        });
        // *********************
        // 地图
        // *********************
        // 初始化图标
        const map = ref<any>(null);
        const Amap = ref<any>(null);
        const ininMap = () => {
            AMapLoader.load({
                key: "ea53a5291f8f7c1f215ba20930ea9866",
                version: "2.0",
            }).then((AMap) => {
                Amap.value = AMap;
                map.value = new AMap.Map("garbage-map", {
                    resizeEnable: true,
                    // mapStyle: 'https://geohub.amap.com/mapstyle/clone?id=20d3f4f1e43c80b9c68c30fb8a1ab94b',
                    zoom: 16,
                    center: [114.61, 30.45],
                });
                updateMarker();
            });
        };
        onMounted(() => {
            nextTick(() => {
                ininMap();
            });
        });

        const activedObjId = ref<number>(0);
        // 更新坐标点
        const updateMarker = () => {
            if (!map.value) return;
            // 清除地图上的图标
            map.value.clearMap();
            if (!getGarbagePointList.value.length) return;
            getGarbagePointList.value.forEach((item: garbagePointInfo) => {
                if (!item.gdx || !item.gdy || !item.id) return;
                if ((addFormValue.value.garbagePlanRefList as number[]).includes(item.id))
                    item["alarmState"] = 1;
                else item["alarmState"] = 0;
                const content = getMarkerContent(
                    item,
                    activedObjId.value,
                    "hide-tag",
                    (d: garbagePointInfo) => {
                        return require("../images/icon_streetLight_001.png");
                    }
                );
                const marker = new Amap.value.Marker({
                    position: [item.gdx, item.gdy],
                    offset: new Amap.value.Pixel(-5, -35),
                    extData: item.id,
                    content,
                });
                marker.on("click", (e: any) => {
                    const id = e.target.getExtData() as number;
                    if ((addFormValue.value.garbagePlanRefList as number[]).includes(id)) {
                        addFormValue.value.garbagePlanRefList = (
                            addFormValue.value.garbagePlanRefList as number[]
                        ).filter((k: string | number) => k != id);
                        activedObjId.value = 0;
                    } else {
                        activedObjId.value = id;
                        (addFormValue.value.garbagePlanRefList as number[]).push(id);
                        map.value.setCenter([item.gdx, item.gdy]);
                    }
                    checkAllGroupChange(addFormValue.value.garbagePlanRefList as number[]);
                });
                map.value.add([marker]);
            });
            nextTick(() => {
                map.value.setFitView();
            });
        };
        // 根据状态更新图标
        const updateIcon = (deviceList: any[]) => {
            const overlays: any[] = map.value?.getAllOverlays("marker");
            overlays?.forEach((e) => {
                const id = e.getExtData();
                const markerInfo = deviceList.filter((k) => k.id == id)[0] || {};
                if (deviceList.includes(id)) {
                    markerInfo.alarmState = 1;
                } else {
                    markerInfo.alarmState = 0;
                }
                const content = getMarkerContent(
                    markerInfo,
                    activedObjId.value,
                    "hide-tag",
                    (d: garbagePointInfo) => {
                        return require("../images/icon_streetLight_001.png");
                    }
                );
                e.setContent(content);
            });
        };
        // *********************
        // 收运点列表
        // *********************
        const getGarbagePointList = ref<garbagePointInfo[]>([]);
        watch(getGarbagePointList, () => {
            updateMarker();
        });
        const getGarbagePoint = async () => {
            const res = await garbagePointList({ type: typeCode });
            const { data, success }: { data: garbagePointInfo[]; success: boolean } =
                res as unknown as HttpResponse<garbagePointInfo[]>;
            if (success) {
                console.log("getGarbagePointList");
                getGarbagePointList.value = data;
            }
        };
        // *********************
        // 表单
        // *********************
        //初始化表单
        const addForm = ref();
        const typeCode:string = getRouterQuery("typecode");
        const weekList = ["", "周一", "周二", "周三", "周四", "周五", "周六", "周日"];
        const defaultVal:garbagePlanInfo = {
            planName: "",
            code: "",
            factoryId: undefined,
            planStatus: 1,
            carId: undefined,
            contactPerson: "",
            contactPhone: "",
            timeType: 1,
            timeValues: [],
            companyId: undefined,
            garbagePlanRefList: [],
            note: "",
            type: typeCode,
        };
        const addFormValue = ref<garbagePlanInfo>(deepClone(defaultVal));
        // 表单校验数据
        const ruleValidate = ref({
            planName: [validateform.required],
            factoryId: [validateform.requiredNum],
            planStatus: [validateform.requiredNum],
            carId: [validateform.requiredNum],
            timeType: [validateform.requiredNum],
            contactId: [validateform.selectRequired],
            // companyName: [validateform.required],
            // contactPhone: [validateform.required],
        });
        // 编辑状态，初始化表单数据
        watch(
            () => props.detail,
            () => {
                if (props.detail.id) {
                    const garbagePlanRefList = (props.detail.garbagePointList as garbagePointInfo[])
                        .filter((k) => k.checked)
                        .map((k) => k.id) as unknown as number[];
                    addFormValue.value = {
                        ...(props.detail as garbagePlanInfo),
                        garbagePlanRefList,
                    };
                    getGarbagePointList.value = props.detail.garbagePointList;
                } else {
                    nextTick(() => {
                        getGarbagePoint();
                    });
                }
            },
            { immediate: true }
        );
        watch(
            () => addFormValue.value.timeType,
            () => {
                addFormValue.value.timeValues = [];
            }
        );
        // 提交表单
        const submitFun = async () => {
            const valid = await addForm.value.validate();
            if (!valid) {
                setLoading(false);
                return false;
            }
            if (!addFormValue.value?.timeValues?.length && addFormValue.value?.timeType !== 1) {
                Message.error("请勾选收运周期");
                return false;
            }
            if (!addFormValue.value.garbagePlanRefList!.length) {
                Message.error("请勾选收运点");
                return false;
            }
            setLoading(true);
            const params = {
                ...addFormValue.value,
                garbagePlanRefList: addFormValue.value.garbagePlanRefList!.map((k) => ({
                    pointId: k,
                })),
                timeValues: addFormValue.value.timeValues
                    ? addFormValue.value
                          .timeValues!.map((k) => +k)
                          .sort((a, b) => a - b)
                          .join(",")
                    : "",
            };
            const res = props.detail.id
                ? await garbagePlanEdit(params)
                : await addGarbagePlan(params);
            const { success }: { success: boolean } = res as unknown as HttpResponse<any>;
            if (success) {
                Message.success("计划操作成功");
            }
            return success;
        };
        // *********************
        // 勾选联动
        // *********************
        const checkAll = ref(false);
        const indeterminate = ref(false);
        const searchTreeText = ref("");

        const handleCheckAll = () => {
            if (indeterminate.value) {
                checkAll.value = false;
            } else {
                checkAll.value = !checkAll.value;
            }
            indeterminate.value = false;
            if (checkAll.value) {
                addFormValue.value.garbagePlanRefList = getGarbagePointList.value.map(
                    (item) => item.id
                ) as number[];
            } else {
                addFormValue.value.garbagePlanRefList = [];
            }
            updateIcon(addFormValue.value.garbagePlanRefList);
        };
        const handleSelect = (data: garbagePointInfo) => {
            setTimeout(() => {
                if ((addFormValue.value.garbagePlanRefList as number[]).includes(data.id || 0)) {
                    map.value.setCenter([data.gdx, data.gdy]);
                }
            }, 500);
        };
        const checkAllGroupChange = (data: any[]) => {
            if (data.length === getGarbagePointList.value.length) {
                indeterminate.value = false;
                checkAll.value = true;
            } else if (data.length > 0) {
                indeterminate.value = true;
                checkAll.value = false;
            } else {
                indeterminate.value = false;
                checkAll.value = false;
            }
            updateIcon(data);
        };
        const list = ref<ComponentInfo[]>([]);
        const gardenSoilDeviceList = async () => {
            const params = {
                page: {
                    current: -1,
                    size: -1,
                },
                customQueryParams: {
                    type: route.query.typecode,
                },
            };
            const res = await garbageCollectCompanyList(params);
            const { data, success }: { success: boolean; data: recordsResponse<ComponentInfo[]> } =
                res as unknown as HttpResponse<recordsResponse<ComponentInfo[]>>;
            // 成功
            if (success) {
                list.value = data.records;
            }
        };
        gardenSoilDeviceList();
        const handlePersonSelect = (person: any) => {
            addFormValue.value.contactId = person.userId;
            addFormValue.value.contactPhone = person.contactPhone;
            addFormValue.value.companyName = person.companyName;
        };

        const chaneTime = (data) => {
            if(data && data.length >1) {
                addFormValue.value.collectStartTime = data[0] ? data[0] + ':00' : null
                addFormValue.value.collectEndTime = data[1] ? data[1] + ':00' : null
            }
        }
        return {
            submitFun,
            Util,
            loading,
            addFormValue,
            checkAll,
            indeterminate,
            activedObjId,
            searchTreeText,
            handleCheckAll,
            checkAllGroupChange,
            getGarbagePointList,
            enumeration,
            ruleValidate,
            weekList,
            addForm,
            typeCode,
            handleSelect,
            personList,
            list,
            handlePersonSelect,
            chaneTime
        };
    },
});
</script>
<style lang="less" scoped>
@import "../../../../../styles/mapPage.less";
@import "../index.less";
</style>
