<template>
    <Form :model="formItem">
        <FormItem>
            <DatePicker type="daterange" v-model="formItem.timeRange" placeholder="请选择任务时间" clearable :editable="false"
                style="width: 296px" />
        </FormItem>
    </Form>
    <base-table ref="taskTbRef" :columns="taskColumns" url="/garbagePlanTask/list" :page-size="5">
        <template #time="scope">
            <span>{{ scope['row'].time?.split(' ')[0] || '--' }}</span>
        </template>
    </base-table>
</template>
<script lang="ts">

import { taskColumnsList } from '../data/columns';
import { defineComponent, ref, watch, onMounted } from 'vue';
import { getRouterQuery } from '@/utils/tool'
import Util from '@/utils/index';
export default defineComponent({
    setup() {
        const taskColumns = taskColumnsList()
        const taskTbRef = ref()
        const formItem = ref({
            timeRange: []
        })
        const id = getRouterQuery('id')
        const handleSearch = () => {
            const params = {
                planId: id,
                startTime: formItem.value.timeRange[0] ? `${Util.formatDate(formItem.value.timeRange[0], 'YYYY-MM-DD')} 00:00:00` : null,
                endTime: formItem.value.timeRange[0] ? `${Util.formatDate(formItem.value.timeRange[1], 'YYYY-MM-DD')} 23:59:59` : null,
            }
            taskTbRef.value?.search(params)
        }
        onMounted(() => {
            handleSearch()
        })
        watch(() => formItem.value, () => {
            handleSearch()
        }, { deep: true })
        return {
            taskColumns,
            taskTbRef,
            formItem
        }
    }
})
</script>
