.time-values {
    min-height: 22px;
    margin-bottom: 0px;
    background: #F8FAFB;
    border-radius: 2px;
    display: flex;
    align-items: center;
    padding: 16px 24px;
    margin-top: 8px;

    .time-month {
        display: flex;
        flex-wrap: wrap;

        /deep/.ivu-checkbox-wrapper {
            width: 8%;
            height: 48px;
        }
    }
}

.time-type {
    margin-bottom: 0px;

    /deep/.ivu-radio-wrapper {
        margin-right: 24px;
        line-height: 22px;

        .ivu-radio {
            margin-right: 8px;
        }
    }
}

.garbage-plan-list {
    margin-top: 16px;

    /deep/.ivu-form-item-content {
        display: flex;
        column-gap: 16px;
    }

    .garbage-map {
        height: 427px;
        border: 1px solid #E0E6F1;
        border-radius: 2px;
        flex: 1;
    }

    .garbage-list {
        width: 300px;
        height: 427px;
        border: 1px solid #E0E6F1;
        border-radius: 2px;
        overflow: hidden;
        display: flex;
        flex-direction: column;

        .title {
            height: 40px;
            background: #F8FAFB;
            display: flex;
            align-items: center;
            padding: 0 12px 0 16px;
            justify-content: space-between;
        }

        /deep/.ivu-input-wrapper {
            padding: 12px 8px 0 8px;
            .ivu-input-icon {
                right: 8px;
            }

            .ivu-input-suffix {
                right: 8px;
                height: 32px;
                top: 12px;
            }
        }

        /deep/.scorll-map-cont {
            padding: 0 16px;
        }

        .garbage-checkbox {
            height: 36px;

            /deep/.ivu-form-item-content {
                line-height: 36px;
            }
            /deep/.ivu-checkbox-wrapper {
                display: flex;
                align-items: center;
                white-space: nowrap;
                overflow: hidden;
                .ivu-checkbox-label-text {
                    flex: 1;
                    overflow: hidden;
                }
            }
        }
    }
}
.garbage-map ,.garbage-map-box{
    /deep/.marker-icon {
        height: 40px;
        width: 40px;
        position: relative;
        background: url('./images/icon_map_001.png') no-repeat center;
        background-size: 40px 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        &:hover {
            background: url('./images/icon_map_002.png') no-repeat center;
            background-size: 40px 40px;
        }
        &.actived {
            background: url('./images/icon_map_002.png') no-repeat center;
            background-size: 40px 40px;
        }
        &.alarm-state {
            background: url('./images/icon_map_003.png') no-repeat center;
            background-size: 40px 40px;
            &:hover {
                background: url('./images/icon_map_004.png') no-repeat center;
                background-size: 40px 40px;
            }
            &.actived {
                background: url('./images/icon_map_006.png') no-repeat center;
                background-size: 40px 40px;
            }
        }
        img {
            margin-top:-2px;
            height: 12px;
            width: 12px;
        }
    }
}
.position-tip {
    position: absolute;
    color: #798799;
    font-weight: 400;
    font-size: 12px;
    line-height: 20px;
    left: 330px;
    top: 16px;
    & > div {
        display: flex;
        align-items: center;

    }
    span {
        width: 12px;
        height: 12px;
        border-radius:100%;
        background: #33D1C9;
        margin-right: 6px;
        &.gray {
            background: #C2C6CE;
        }
    }
}
