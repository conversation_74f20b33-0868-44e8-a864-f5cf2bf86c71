<template>
    <BreadcrumbCustom>
        <BreadcrumbItem>垃圾运收管理</BreadcrumbItem>
        <BreadcrumbItem
            :to="`${type == '2' ? '/garbageCollectionManagement/collectionPlan' : '/kitchenWasteManagement/collectionPlan'}`">
            收运计划
        </BreadcrumbItem>
        <BreadcrumbItem>查看</BreadcrumbItem>
    </BreadcrumbCustom>
    <div :style="{ 'marginBottom': isEdit ? '48px' : '' }">
        <detailCard :title-icon="true" :src="require('@/assets/images/icon-基础信息.png')" :is-back-btn="true"
            :loading="loading" @on-submit="submitFun"
            :isEditBtn="Util.checkAuth('garbageCollectionManagement:collectionPlan:edit')" title="计划信息" @on-back="cancel"
            @on-edit="handleEdit" ref="detailRef">
            <info-box v-if="!isEdit" :detail="detail" />
            <edit-box v-if="isEdit" :type="type" :plan-id="planId" :detail="detail" ref="editRef" />
        </detailCard>
        <detailCard title="收运计划" :src="require('@/assets/images/icon-灌溉.png')" v-show="!isEdit">
            <div>
                <s-label label="时间区间" :tooltip="false">
                    <template #value>
                        <s-tag v-if="detail?.timeType == 1" background="#E8F3FF" color="#165DFF">每天</s-tag>
                        <!-- 每周 -->
                        <div class="" v-if="detail?.timeType == 2">
                            <s-tag v-for="(n, i) in detail?.timeValues" background="#E8F3FF" color="#165DFF" size="large"
                                :key="i">
                                {{ weekList[+n] }}
                            </s-tag>
                        </div>
                        <div class="" v-if="detail?.timeType == 3">
                            <s-tag v-for="(n, i) in detail?.timeValues" background="#E8F3FF" color="#165DFF" size="large"
                                :key="i">
                                {{ n }}号
                            </s-tag>
                        </div>
                    </template>
                </s-label>
                <div class="map-label">
                    <span>收运点位：</span>
                    <collection-map-box :data="detail?.garbagePointList" />
                </div>
            </div>
        </detailCard>
        <detailCard :title="''" v-show="!isEdit">
            <s-tab :tab-list="tabList" justify="start">
                <template #task>
                    <task-box />
                </template>
                <template #record>
                    <record-box />
                </template>
            </s-tab>
        </detailCard>
    </div>
</template>
<script lang="ts">
import Util from '@/utils';
import { defineComponent, ref, onMounted } from 'vue';
import useLoading from '@/hooks/loading';
import { garbagePointList, garbagePlanById } from '@/api/livableManage/foodWasteManagementService';
import { garbagePlanInfo, garbagePointInfo } from '@/api/livableManage/type'
import { useRouter } from 'vue-router';
import editBox from './components/edit.vue'
import infoBox from './components/info.vue'
import taskBox from './components/task.vue'
import recordBox from './components/record.vue'
import collectionMapBox from './components/collectionMap.vue'
export default defineComponent({
    components: {
        editBox,
        infoBox,
        recordBox,
        taskBox,
        collectionMapBox
    },
    setup() {
        const router = useRouter();
        const cancel = () => { router.back() }
        const weekList = ['', '周一', '周二', '周三', '周四', '周五', '周六', '周日']
        const { loading, setLoading } = useLoading();
        const isEdit = ref(false)
        const planId = ref<string>('')
        const type = ref<string>('2')
        // 编辑表单
        const handleEdit = (flag: boolean) => {
            isEdit.value = flag
        }
        const editRef = ref()
        // 提交表单
        const detailRef = ref()
        const submitFun = async () => {
            const res = await editRef.value.submitFun()
            if (res) {

                getGarbagePlanById()
                detailRef.value.handleEdit(false)
            }

        }
        const detail = ref<garbagePlanInfo>()
        // 获取收运点列表
        const getGarbagePointList = () => {
            const params = {
                id: planId.value,
                type: type.value
            }
            garbagePointList(params).then((res: any) => {
                const { data, success }: { success: boolean, data: garbagePointInfo[] } = res as unknown as HttpResponse<garbagePointInfo[]>
                if (success) {
                    detail.value!['garbagePointList'] = data as garbagePointInfo[]
                }
            })
        }
        // 获取详情
        const getGarbagePlanById = async () => {
            const res = await garbagePlanById(planId.value)
            const { data, success }: { success: boolean, data: garbagePlanInfo } = res as unknown as HttpResponse<garbagePlanInfo>
            if (success) {
                const { timeValues } = JSON.parse(JSON.stringify(data))
                detail.value = { ...data, timeValues: timeValues?.split(','),
                    collectStartTime: data.collectStartTime ? Util.formatDate(data.collectStartTime, 'HH:mm') : '',
                    collectEndTime: data.collectEndTime ? Util.formatDate(data.collectEndTime, 'HH:mm') : ''
                }
                if(detail.value.collectStartTime) {
                    detail.value.collectTime = [detail.value.collectStartTime, detail.value.collectEndTime]
                }
                getGarbagePointList()
            }
        }
        onMounted(() => {
            if (router.currentRoute.value.query?.id) {
                planId.value = router.currentRoute.value.query.id as string;
                type.value = router.currentRoute.value.query.typecode as string;
                getGarbagePlanById();
            }


        })
        const tabList = ref([
            {
                name: '收运任务',
                key: 'task',

            },
            // {
            //     name: '详细收运记录',
            //     key: 'record',

            // }
        ])
        // *********************
        // 底部tab表格
        // *********************
        return {
            cancel,
            Util,
            loading,
            handleEdit,
            submitFun,
            editRef,
            isEdit,
            type,
            planId,
            detail,
            weekList,
            tabList,
            detailRef
        }
    }
})
</script>
<style lang="less" scoped>
.map-label {
    font-size: 14px;
    color: #798799;
    display: flex;

    .garbage-map-box {
        flex: 1;
    }
}

/deep/.tag-cont {
    margin-bottom: 8px;
}
</style>
