<template>
    <BreadcrumbCustom>
        <BreadcrumbItem>垃圾运收管理</BreadcrumbItem>
        <BreadcrumbItem to="/garbageCollectionManagement/collectionRecord">收运记录</BreadcrumbItem>
        <BreadcrumbItem>详情</BreadcrumbItem>
    </BreadcrumbCustom>
    <!-- 查看信息 -->
    <garbage-task-records :id="id" type="2"  v-show="!isEdit"
        :is-edit-btn="false" @on-edit="onEdit" :is-back-btn="true" @on-back="onBack"/>
    <!-- 编辑信息 -->
    <detailCard ref="detailRef" title="编辑收运记录" :src="require('@/assets/images/icon_detail.png')" :is-back-btn="true"
        @on-submit="onSubmit" @on-back="onBack" :class="[isEdit ? 'edit-card' : '']" v-show="isEdit" :is-edit-btn="isEdit" @on-edit="onEdit">
        编辑信息
    </detailCard>
</template>
<script lang="ts" setup>
import { ref } from 'vue';
import garbageTaskRecords from '../components/garbageTaskRecords/index.vue'
import { getRouterQuery } from '@/utils/tool'
import { useRouter } from "vue-router";
import GarbageTaskRecords
    from "@/views/livableManage/garbageCollectionManagement/components/garbageTaskRecords/index.vue";
const id = getRouterQuery('id') as string;
const isEdit = ref(false);
const isFromRecord = getRouterQuery('isFromRecord') as string;
const router = useRouter();
const onSubmit = () => { }
const onEdit = (flag:boolean) => {
    isEdit.value = flag;
    detailRef.value.isEditFlag = flag
}
const detailRef = ref();
const onBack = () => {
    router.back()
}
</script>
