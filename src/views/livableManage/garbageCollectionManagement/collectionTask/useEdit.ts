import { ref } from 'vue';
import { validateform } from '@/utils/validateform';
import { useRoute, useRouter } from 'vue-router';
import { getGarbageInfo } from '@/api/livableManage/foodWasteManagementService';
import { IModalInfo, ITask } from './type/index';

const createPointInfo = ():ITask => {
    return {
        planName: '', // 收运计划
        name: '', // 收运任务
        time: '', // 任务时间
        contactPerson: '', // 收运人
        companyName: '', // 收运公司
        carLicenseNo: '', // 收运车辆
        factoryName: '', // 垃圾处理厂
        weight: '', // 总收垃圾重量
        sumPointNum: '', // 应收收运点
        donePointNum: '', // 实收收运点
        pointPart: '', // 收运点完成率
        sumGarbageNum: '', // 应收垃圾桶
        doneGarbageNum: '', // 实收垃圾桶
        garbagePart: '', // 垃圾桶完成率
    }
}
const createModalInfo = ():IModalInfo => {
    return {
        planName: '', // 收运计划
        name: '', // 收运任务
        time: '', // 任务时间
        contactPerson: '', // 收运人
        carLicenseNo: '', // 收集车辆
        companyName: '', // 收运公司
        // 收运填报
        garbageCans: [], // 收运数量(垃圾桶)
    }
}
const checkRequired = (rule: any, value: number | string, callback: any) => {
    console.log('validte', { rule, value })
    if (value) {
        callback()
    } else {
        callback(new Error('请完善必填项'))
    }
}
const formRules = {
    name: validateform.required,
    objx: [checkRequired],
    num: [{
        required: true,
        message: '请完善必填项'
    }]
}
// 根据该收运点的垃圾桶数量，自动显示对应的垃圾桶个数的输入框，
// 数字填写，必填项，至少填一个
// 范围0-1000  最多两位小数
// 收运时间，打开弹窗时，默认带出显示当前时间，可以进行人为更改，必填项
const modalRules = {
    time: validateform.required
}
// ^[0-9a-zA-Z]{18}$ 18位字符正则
// /^(?:(?![IOZSV])[\dA-Z]){2}\d{6}(?:(?![IOZSV])[\dA-Z]){10}$/  营业执照18位正则
export const useEdit = () => {
    const isEdit = ref(false);
    const detailInfo = ref<ITask>(createPointInfo());
    const modalInfo = ref<IModalInfo>(createModalInfo());
    const isLoading = ref(false);

    const router = useRouter();
    const route = useRoute();


    const clearModalInfo = () => {
        modalInfo.value = createModalInfo();
    }
    const onInit = async(id: string | undefined) => {
        if (isEdit) {
            console.log('onEdit', id);
            const res = await getGarbageInfo({ id })
            console.log(res);
        }
    }

    return { detailInfo, modalInfo, isEdit, formRules, router, route, onInit, isLoading, clearModalInfo, modalRules }
}

