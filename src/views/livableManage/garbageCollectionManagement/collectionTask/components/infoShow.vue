<template>
    <Row :gutter="80">
        <Col span="8">
            <s-label label="收运计划" :value="detailInfo.planName"></s-label>
        </Col>
        <Col span="8">
            <s-label label="收运任务" :value="detailInfo.name"></s-label>
        </Col>
        <Col span="8">
            <s-label label="任务时间" :value="detailInfo.time"></s-label>
        </Col>
        <Col span="8">
            <s-label label="收运公司" :value="detailInfo.companyName"></s-label>
        </Col>
        <Col span="8">
            <s-label label="收运人" :value="detailInfo.contactPerson"></s-label>
        </Col>
        <Col span="8">
            <s-label label="收运车辆" :value="detailInfo.carLicenseNo"></s-label>
        </Col>
        <Col span="8">
            <s-label label="垃圾处理厂" :value="detailInfo.factoryName" />
        </Col>
        <Col span="8">
            <s-label label="总收垃圾重量" :value="detailInfo.weight" />
        </Col>
        <Col span="8">
            <s-label label="应收收运点" :value="detailInfo.sumPointNum" />
        </Col>
        <Col span="8">
            <s-label label="实收收运点" :value="detailInfo.donePointNum" />
        </Col>
        <Col span="8">
            <s-label label="收运点完成率" :value="detailInfo.pointPart" />
        </Col>
        <Col span="8">
            <s-label label="应收垃圾桶数" :value="detailInfo.sumGarbageNum" />
        </Col>
        <Col span="8">
            <s-label label="实收垃圾桶数" :value="detailInfo.doneGarbageNum" />
        </Col>
        <!-- <Col span="8">
            <s-label label="垃圾桶完成率" :value="detailInfo.garbagePart" />
        </Col> -->
    </Row>
</template>

<script lang="ts" setup>
import { computed, defineProps } from 'vue'

import { ITask } from '../type/index'
const props = defineProps<{detailInfo: ITask}>()
const detailInfo = computed<ITask>(() => props.detailInfo)

</script>

<style lang="less" scoped>
.show-map {
    font-size: 16px;
    color: #165DFF;
.ivu-icon {
    font-weight: 700;
}
}
.license-wrapper {
    margin-bottom:12px;
    .title{
        color: #798799;
        font-size: 14px;
        padding-right: 8px;
    }
}
</style>
