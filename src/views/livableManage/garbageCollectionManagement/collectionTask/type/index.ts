export interface ITask{
    planName: string, // 收运计划
    name: string, // 收运任务
    time: string, // 任务时间
    contactPerson: string, // 收运人
    companyName: string, // 收运公司
    carLicenseNo: string, // 收运车辆
    factoryName: string, // 垃圾处理厂
    weight: string, // 总收垃圾重量
    sumPointNum: string, // 应收收运点
    donePointNum: string, // 实收收运点
    pointPart: string, // 收运点完成率
    sumGarbageNum: string, // 应收垃圾桶
    doneGarbageNum: string, // 实收垃圾桶
    garbagePart: string, // 垃圾桶完成率
}
// export interface IModalInfo{
//     planName: string; // 收运计划
//     taskName: string; // 收运任务
//     taskTime: string; // 任务时间
//     contactPerson: string; // 收运人
//     carLicenseNo: string; // 收集车辆
//     companyName: string; // 收运公司
//     // 收运填报
//     pointName: string; // 收运点
//     pointId?: string; // 收运点id
//     enterpriseName: string; // 所属企业
//     // 可编辑内容
//     garbageCans?: IGarbageCan[]; // 收运数量
//     time?: string; // 收运时间
// }

export interface ICollectRecordDetail {
    pointName: string;
    num: number;
    companyName: string;
    creatorId: any;
    contactPerson: string;
    planName: string;
    remark: any;
    garbageCans: Array<IGarbageCan>;
    type: number;
    content: any;
    carLicenseNo: string;
    orderStr: any;
    modifyTime: string;
    pointId: number;
    planId: any;
    startTime: any;
    id: number;
    recordId:string;
    enterpriseName: string;
    doneGarbageNum: number;
    modifyId: any;
    weight: any;
    deleted: number;
    createTime: string;
    ids: any;
    taskName: string;
    time: any;
    endTime: any;
    taskId: number;
    status: number;
    taskTime: string;
}
export interface IModalInfo {
    companyName: string; // 收运公司
    contactPerson: string; // 收运人
    planName: string; // 收运计划
    taskName?: string; // 任务名称
    taskTime?: string; // 任务任务时间
    planId?: string; // 任务id
    carLicenseNo: string; // 收运车辆
    pointId?: number;
    name: string; // 收运任务
    time: string; // 收运时间
    // 收运数量
    enterpriseName?: string; // 所属公司
    garbageCans?: IGarbageCan[];
}

export interface IGarbageCan{
    name: string; // 垃圾桶名称
    weight: string; // 重量
    key?: string;
}
