<template>
    <BreadcrumbCustom>
        <BreadcrumbItem>垃圾运收管理</BreadcrumbItem>
        <BreadcrumbItem>收运地图</BreadcrumbItem>
    </BreadcrumbCustom>
    <collect-map-component type="2" />
</template>
<script lang="ts">
import { defineComponent } from 'vue';
import collectMapComponent from '../components/collectMapComponent/index.vue';
export default defineComponent({
    components: {
        collectMapComponent
    }
});
</script>
<style lang="less" scoped></style>
