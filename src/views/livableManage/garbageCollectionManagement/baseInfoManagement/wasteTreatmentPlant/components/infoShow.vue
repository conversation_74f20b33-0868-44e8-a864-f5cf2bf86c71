<template>
    <Row :gutter="80">
        <Col span="8">
            <s-label label="处理厂名称" :value="detailInfo.name"></s-label>
        </Col>
        <Col span="8">
            <s-label label="处理厂编号" :value="detailInfo.code"></s-label>
        </Col>
        <Col span="8">
            <s-label label="所属区域" :value="formatAreaStr(detailInfo.area)"></s-label>
        </Col>
        <Col span="8">
            <s-label label="处理厂位置" :value="detailInfo.address" />
        </Col>
        <Col span="8">
            <s-label label="经纬度">
                <template #value>
<!--                    {{ formatPositionPoint }}-->
                    <jwd-map :obj-info="formatPositionPoint"></jwd-map>
<!--                    <span class="show-map"><Icon type="ios-pin-outline" /></span>-->
                </template>
            </s-label>
        </Col>
        <Col span="8">
            <s-label label="设计处理能力">
                <template #value>
                    {{ detailInfo.capacity ? `${detailInfo.capacity}吨`: '--' }}
                </template>
            </s-label>
        </Col>
        <Col span="8">
            <s-label label="处理方式" :value="detailInfo.way">
                <template #value>
                    <DictLabel code="garbage_handle" :value="detailInfo.way"></DictLabel>
                </template>
            </s-label>
        </Col>
        <Col span="8">
            <s-label label="联系人" :value="detailInfo.contactPerson"></s-label>
        </Col>
        <Col span="8">
            <s-label label="联系方式" :value="detailInfo.contactPhone"></s-label>
        </Col>
    </Row>
</template>

<script lang="ts" setup>
import { computed, defineProps } from 'vue'
import {
    IWasteTreatmentPlant
} from '@/views/livableManage/foodWasteManagement/baseInfoManagement/wasteTreatmentPlant/type';

import DictLabel from '@/components/global/dictLabel/index'
import { formatAreaStr } from '@/utils/tool';

const props = defineProps<{detailInfo: IWasteTreatmentPlant}>()
const detailInfo = computed<IWasteTreatmentPlant>(() => props.detailInfo)
const formatPositionPoint = computed(() => {
    return { gdx: detailInfo.value.gdx, gdy: detailInfo.value.gdy }
})
</script>

<style scoped>
.show-map {
    font-size: 16px;
    color: #165DFF;
.ivu-icon {
    font-weight: 700;
}
}
</style>
