<template>
    <BreadcrumbCustom>
        <BreadcrumbItem>垃圾运收管理</BreadcrumbItem>
        <BreadcrumbItem>基础管理</BreadcrumbItem>
        <BreadcrumbItem>垃圾处理厂</BreadcrumbItem>
    </BreadcrumbCustom>
    <ContentCard title="垃圾处理厂" :base-btn="false">
        <BaseForm @handle-submit="onSubmit(listRef)" :model="condition" inline :label-width="90">
            <template #formitem>
                        <FormItem label="处理厂名称" prop="name">
                            <Input v-model="condition.name" :maxlength="20" placeholder="请输入"
                                   clearable
                            ></Input>
                        </FormItem>
                        <FormItem label="所属区域" prop="area">
                            <RegionCascader v-model="condition.area" />
                        </FormItem>
            </template>
        </BaseForm>
        <btn-card>
            <Button type="primary" @click="onAdd" v-auth="'garbageCollectionManagement:basic:factory:add'" icon="md-add">
                新增垃圾处理厂
            </Button>
            <Button icon="ios-trash" @click="onDelete" v-auth="'garbageCollectionManagement:basic:factory:del'">
                删除
            </Button>
        </btn-card>
        <baseTable @on-selection-change="onSelectionChange" :model="condition" ref="listRef"
                   :columns="tableColumn" url="/garbageFactory/list"
        >
            <template #geographicalLocation="{ row }">
                <tooltip-auto-show>
                    {{ (formatAreaStr(row.area) || '')+(row.address || '') }}
                </tooltip-auto-show>
            </template>
            <template #way="{ row }">
                <dict-label :value="row.way" code="garbage_handle"></dict-label>
            </template>
            <template #action="{ row }">
                <LinkBtn size="small" @click="goDetail(row)">
                    详情
                </LinkBtn>
            </template>
        </baseTable>
    </ContentCard>
</template>
<script lang="ts" setup>


import { getCurrentInstance, onMounted, ref } from 'vue';
import { useRouter } from 'vue-router';
import {
    IWasteTreatmentPlant
} from '@/views/livableManage/foodWasteManagement/baseInfoManagement/wasteTreatmentPlant/type';
import { deleteWasteTreatmentPlant } from '@/api/livableManage/foodWasteManagementService';
import RegionCascader from '@/components/common/regionCascader/index.vue'
import DictLabel from '@/components/global/dictLabel';
import { formatAreaStr } from '@/utils/tool';

const router = useRouter();

const that = getCurrentInstance()?.appContext.config.globalProperties;


const listRef = ref(null);

const condition = ref({
    name: '',
    area: '',
    type: 2
})

// 详情
const goDetail = ({ id }) => {
    const query = {
        id
    }
    router.push({ name: 'garbageCollectionManagement:basic:factory:edit', query })
}
// 查询
const onSubmit = (ref: any) => {
    console.log('onSubmit')
    const { name, area, type } = condition.value
    const params = {
        name,
        area,
        type
    }
    ref && ref.search(params);
}
onMounted(() => {
    onSubmit(listRef.value);
})
// 新增
const onAdd = () => {
    router.push({ name: 'garbageCollectionManagement:basic:factory:add' })
}

// 删除
const selectedIds = ref<(string | undefined)[]>([])

const onSelectionChange = (selectedRow: IWasteTreatmentPlant[] = []) => {
    console.log(selectedRow)
    selectedIds.value = selectedRow.map(item => item.id)
}

const onDelete = () => {
    if (selectedIds.value.length === 0) {
        that && that.$Message.warning('最少选择一条数据')
        return
    }
    that && that.$Modal.confirm({
        title: '提示',
        content: '您确定要删除选中的数据吗',
        onOk: () => {
            const ids = selectedIds.value;
            deleteWasteTreatmentPlant({ ids })
                .then(res => {
                    if (res.success) {
                        that?.$Message.success('删除成功')
                        onSubmit(listRef.value);
                        selectedIds.value = []
                    }
                })
        }
    })
}


const tableColumn = [
    { type: 'selection', width: 40 },
    { title: '处理厂名称', minWidth: 110, key: 'name', tooltip: true },
    { title: '处理厂编号', key: 'code', width: 120, tooltip: true },
    { title: '地理位置', slot: 'geographicalLocation', width: 220, tooltip: true },
    { title: '设计处理能力(吨)', key: 'capacity', width: 140, tooltip: true },
    { title: '处理方式', slot: 'way' },
    { title: '联系人', key: 'contactPerson', tooltip: true },
    { title: '联系方式', key: 'contactPhone', tooltip: true },
    { title: '操作', slot: 'action', width: 80, lock: true, fixed: true }
]



</script>

<style lang="less" scoped>
/deep/ .ivu-form-item{
    width: 80%;
}

</style>
