import {
    IWasteTreatmentPlant
} from '@/views/livableManage/foodWasteManagement/baseInfoManagement/wasteTreatmentPlant/type';
import { ref } from 'vue';
import { validateform } from '@/utils/validateform';
import { useRoute, useRouter } from "vue-router";


// 默认数据工厂
const createDefaultInfo = ():IWasteTreatmentPlant => {
    return {
        name: '', // 处理厂名称
        code: '', // 处理厂编号
        area: '', // 所属区域
        address: '', // 位置
        objx: '', // 经度
        objy: '', // 纬度
        capacity: '', // 设计处理能力
        way: '', // 处理方式
        contactPerson: '', // 联系人
        contactPhone: '', // 联系方式
    }
}
const checkRequired = (rule: any, value: number | string, callback: any) => {
    console.log('validte', { rule, value })
    if (value) {
        callback()
    } else {
        callback(new Error('请完善必填项'))
    }
}
const formRules = {
    name: validateform.required,
    objx: [checkRequired],
    contactPhone: [{ pattern: /^1[3456789]\d{9}$/, trigger: 'blur', message: '手机号错误' }],
    capacity: [{
        pattern: /^([1-9]\d{0,3}|0|10000)$/,
        message: '请输入0-10000之间的整数'
    }]
}
export const useEdit = () => {
    const isEdit = ref(false);
    const detailInfo = ref<IWasteTreatmentPlant>(createDefaultInfo());
    const isLoading = ref(false);
    const router = useRouter();
    const route = useRoute();
    const clearDetail = () => {
        detailInfo.value = createDefaultInfo();
    }
    return { detailInfo, isEdit, formRules, router, route, isLoading, clearDetail };
}

