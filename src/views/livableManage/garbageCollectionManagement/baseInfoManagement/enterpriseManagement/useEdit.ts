import { ref } from 'vue';
import { validateform } from '@/utils/validateform';
import { useRoute, useRouter } from 'vue-router';
import { getGarbageInfo } from '@/api/livableManage/foodWasteManagementService';
import {
    IEnterprise
} from './type/index';

const createCarInfo = ():IEnterprise => {
    return {
        name: '', // 公司名称
        license: '', // 执照代码
        objx: 0, // 经度
        objy: 0, // 纬度
        area: '', // 所属区域
        address: '', // 具体位置
        contactType: '', // 企业类型
        legalPerson: '', // 企业法人
        legalPhone: '', // 法人电话
        contactPerson: '', // 联系人
        contactPhone: '', // 联系方式
        licensePhoto: '', // 营业执照照片
        note: '', // 备注
    }
}
const checkRequired = (rule: any, value: number | string, callback: any) => {
    console.log('validte', { rule, value })
    if (value) {
        callback()
    } else {
        callback(new Error('请完善必填项'))
    }
}
const formRules = {
    name: validateform.required,
    objx: [checkRequired],
    license: [{
        pattern: /^[\da-zA-Z]{18}$/,
        message: '请输入18位营业执照代码',
        trigger: 'blur'
    }],
    legalPhone: [{ pattern: /^1[3456789]\d{9}$/, trigger: 'blur', message: '手机号错误' }],
    contactPhone: [{ pattern: /^1[3456789]\d{9}$/, trigger: 'blur', message: '手机号错误' }]
}
// ^[0-9a-zA-Z]{18}$ 18位字符正则
// /^(?:(?![IOZSV])[\dA-Z]){2}\d{6}(?:(?![IOZSV])[\dA-Z]){10}$/  营业执照18位正则
export const useEdit = () => {
    const isEdit = ref(false);
    const detailInfo = ref<IEnterprise>(createCarInfo());
    const isLoading = ref(false);

    const router = useRouter();
    const route = useRoute();

    const onInit = async(id: string | undefined) => {
        if (isEdit) {
            console.log('onEdit', id);
            const res = await getGarbageInfo({ id })
            console.log(res);
        }
    }



    return { detailInfo, isEdit, formRules, router, route, onInit, isLoading }
}

