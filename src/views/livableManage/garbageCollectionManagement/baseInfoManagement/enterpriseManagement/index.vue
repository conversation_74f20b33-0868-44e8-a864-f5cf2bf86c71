<template>
    <BreadcrumbCustom>
        <BreadcrumbItem>垃圾运收管理</BreadcrumbItem>
        <BreadcrumbItem>基础管理</BreadcrumbItem>
        <BreadcrumbItem>企业管理</BreadcrumbItem>
    </BreadcrumbCustom>
    <ContentCard title="企业管理" :base-btn="false">
        <BaseForm @handle-submit="onSubmit(listRef)" :model="condition" inline :label-width="90">
            <template #formitem>
                <FormItem label="企业名称" prop="name">
                    <Input v-model="condition.name" placeholder="请输入"
                           clearable
                    ></Input>
                </FormItem>
                <FormItem label="企业区域" prop="area">
                    <RegionCascader v-model="condition.area"></RegionCascader>
                </FormItem>
                <FormItem label="企业类型" prop="contactType">
                    <dictDropDownSelect v-model="condition.contactType" code="enterprise_type"></dictDropDownSelect>
                </FormItem>
</template>
        </BaseForm>
        <btn-card>
            <Button type="primary" @click="onAdd" icon="md-add" v-auth="'garbageCollectionManagement:basic:enterprise:add'">
                新增企业
            </Button>
            <Button icon="ios-trash" @click="onDelete" v-auth="'garbageCollectionManagement:basic:enterprise:del'">
                删除
            </Button>
        </btn-card>
        <baseTable @on-selection-change="onSelectionChange" :model="condition" ref="listRef"
                   :columns="tableColumn" url="/garbageEnterprise/list"
        >
            <template #contactType="{ row }">
                <dictLabel :value="row.contactType" code="enterprise_type"></dictLabel>
            </template>
            <template #geographicalLocation="{ row }">
                <tooltip-auto-show :content="formatAreaStr(row.area) + row.address"></tooltip-auto-show>
            </template>

            <template #action="{ row }">
                <LinkBtn size="small" @click="goDetail(row)">
                    详情
                </LinkBtn>
            </template>
        </baseTable>
    </ContentCard>
</template>

<script lang="ts" setup>


import { getCurrentInstance, onMounted, ref } from 'vue';
import { useRouter } from 'vue-router';
import { delEnterprise } from '@/api/livableManage/foodWasteManagementService';
import RegionCascader from '@/components/common/regionCascader/index.vue'
import {
    IEnterprise
} from '@/views/livableManage/foodWasteManagement/baseInfoManagement/enterpriseManagement/type';
import { formatAreaStr } from '@/utils/tool';

const router = useRouter();

const that = getCurrentInstance()?.appContext.config.globalProperties;


const listRef = ref(null);

const condition = ref({
    name: '',
    area: '',
    contactType: ''
})

// 详情
const goDetail = ({ id }) => {
    router.push({ name: 'garbageCollectionManagement:basic:enterprise:edit', query: { id }})
}
// 查询
const onSubmit = (ref: any) => {
    console.log('onSubmit')
    const params = Object.assign({}, condition.value, { type: 2 })
    ref && ref.search(params);
}
onMounted(() => {
    onSubmit(listRef.value)
})
// 新增
const onAdd = () => {
    router.push({ name: 'garbageCollectionManagement:basic:enterprise:add' })
}

// 删除
const selectedIds = ref<(string | undefined)[]>([])

const onSelectionChange = (selectedRow: IEnterprise[] = []) => {
    console.log(selectedRow)
    selectedIds.value = selectedRow.map(item => item.id)
}

const onDelete = () => {
    if (selectedIds.value.length === 0) {
        that && that.$Message.warning('最少选择一条数据')
        return
    }
    that && that.$Modal.confirm({
        title: '提示',
        content: '您确定要删除选中的数据吗',
        onOk: () => {
            const ids = selectedIds.value;
            delEnterprise({ ids })
                .then(res => {
                    if (res.success) {
                        selectedIds.value = []
                        onSubmit(listRef.value)
                    }
                })
            console.log('删除id', ids)
        }
    })
}

const tableColumn = ref([
    { type: 'selection', width: 40 },
    { title: '企业名称', key: 'name', minWidth: 160, tooltip: true },
    { title: '执照代码', key: 'license', tooltip: true },
    { title: '企业类型', slot: 'contactType', width: 80, tooltip: true },
    { title: '地理位置', slot: 'geographicalLocation', minWidth: 160, tooltip: true },
    { title: '企业法人', key: 'legalPerson', tooltip: true },
    { title: '法人电话', key: 'legalPhone', tooltip: true },
    { title: '联系人', key: 'contactPerson', tooltip: true },
    { title: '联系人电话', key: 'contactPhone',width: 100, tooltip: true },
    { title: '操作', slot: 'action', width: 80, lock: true, fixed: true }
])


</script>

<style lang="less" scoped>
/deep/ .ivu-form-item{
    width: 80%;
}

</style>
