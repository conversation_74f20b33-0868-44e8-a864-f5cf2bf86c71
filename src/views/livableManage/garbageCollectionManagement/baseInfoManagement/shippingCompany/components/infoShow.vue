<template>
    <Row :gutter="80">
        <Col span="8">
            <s-label label="公司名称" :value="detailInfo.name"></s-label>
        </Col>
        <Col span="8">
            <s-label label="公司编号" :value="detailInfo.code"></s-label>
        </Col>
        <Col span="8">
            <s-label label="执照代码" :value="detailInfo.license"></s-label>
        </Col>
        <Col span="8">
            <s-label label="经纬度">
                <template #value>
                    <jwd-map :obj-info="formatPositionPoint"></jwd-map>
                </template>
            </s-label>
        </Col>
        <Col span="8">
            <s-label label="所在区域" :value="formatAreaStr(detailInfo.area)"></s-label>
        </Col>
        <Col span="8">
            <s-label label="具体位置" :value="detailInfo.address"></s-label>
        </Col>
        <Col span="8">
            <s-label label="公司法人" :value="detailInfo.legalPerson" />
        </Col>
        <Col span="8">
            <s-label label="法人电话" :value="detailInfo.legalPhone" />
        </Col>
        <Col span="8">
            <s-label label="联系人" :value="detailInfo.contactPerson" />
        </Col>
        <Col span="8">
            <s-label label="联系人电话" :value="detailInfo.contactPhone" />
        </Col>
    </Row>
    <Row>
        <div class="license-wrapper">
            <div class="title">营业执照照片:</div>
            <UploadImg v-if="detailInfo.licensePhoto" v-model="detailInfo.licensePhoto" :preview="true" max-length="1" :disabled="true"> </UploadImg>
        </div>
    </Row>
    <Row>
        <Col span="24">
            <s-label label="备注" :tooltip="false">
                <template #value>
                    {{detailInfo.note}}
                </template>
            </s-label>
        </Col>
    </Row>
</template>

<script lang="ts" setup>
import { computed, defineProps } from 'vue'
import {
    IShipCompany
} from '@/views/livableManage/foodWasteManagement/baseInfoManagement/shippingCompany/type';
import UploadImg from '@/components/common/uploadImg/index.vue'
import { formatAreaStr } from '@/utils/tool';
const props = defineProps<{detailInfo: IShipCompany}>()
const detailInfo = computed<IShipCompany>(() => props.detailInfo)

const formatPositionPoint = computed(() => {
    return { gdx: detailInfo.value.gdx, gdy: detailInfo.value.gdy }
})

</script>

<style lang="less" scoped>
.show-map {
    font-size: 16px;
    color: #165DFF;
.ivu-icon {
    font-weight: 700;
}
}
.license-wrapper {
    margin-bottom:12px;
    .title{
        color: #798799;
        font-size: 14px;
        padding-right: 8px;
    }
}
</style>
