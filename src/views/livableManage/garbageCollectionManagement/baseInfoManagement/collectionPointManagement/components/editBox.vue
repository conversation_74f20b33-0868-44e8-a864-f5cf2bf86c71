<template>
    <Row :gutter="80">
        <Col span="8">
            <FormItem label="收运点名称" prop="name" required>
                <Input v-model="form.name" maxlength="20" clearable placeholder="请输入"></Input>
            </FormItem>
        </Col>
        <Col span="8">
            <FormItem label="收运点编号" prop="code">
                <Input v-model="form.code" maxlength="20" clearable placeholder="系统自动生成" disabled></Input>
            </FormItem>
        </Col>
        <Col span="8">
            <PointMarkerFormItem v-model:objx="form.gdx" v-model:objy="form.gdy" @confirm="onMapTapperConfirm" />
        </Col>
    </Row>
    <Row :gutter="80">
        <Col span="8">
            <FormItem label="所属区域" prop="area">
                <RegionCascader v-model="form.area"></RegionCascader>
            </FormItem>
        </Col>
        <Col span="8">
            <FormItem label="具体位置" prop="address">
                <Input v-model="form.address" maxlength="20" clearable placeholder="请输入"></Input>
            </FormItem>
        </Col>
        <Col span="8">
            <FormItem label="区域位置" prop="areaPath" required>
                <AreaSelectTreeNoDevice v-model="handlerAreaPath" />
            </FormItem>
        </Col>
    </Row>
    <Row :gutter="80">
        <Col span="8">
            <FormItem label="垃圾桶数量" prop="num" required>
                    <div style="display: flex">
                        <input-number v-model="form.num" :min="1" :max="10" />
                        <span style="margin-left: 8px">个</span>
                    </div>
            </FormItem>
        </Col>
        <Col span="8">
            <FormItem label="所属企业" prop="enterpriseId">
                <SelectByList url="/garbageEnterprise/list" :type="2" v-model="form.enterpriseId" :disabled="handleDisabled"  />
            </FormItem>
        </Col>
        <Col span="8">
            <FormItem label="收运生效日期" prop="effectiveTime">
                <DatePicker type="date" placeholder="请选择"  @on-change="(date:string)=>form.effectiveTime = date && moment(date).endOf('day').format('YYYY-MM-DD HH:MM:ss')" :model-value="form.effectiveTime?.split(' ')[0]"/>
            </FormItem>
        </Col>
        <Col span="8">
            <FormItem label="收运失效日期" prop="invalidTime">
                <DatePicker type="date"  placeholder="请选择"   @on-change="(date:string)=>form.invalidTime = date && moment(date).endOf('day').format('YYYY-MM-DD HH:MM:ss')" :model-value="form.invalidTime?.split(' ')[0]" />
            </FormItem>
        </Col>
    </Row>
    
    <Row>
        <Col span="24">
            <FormItem label="备注" prop="note">
                <sTextarea v-model="form.note"></sTextarea>
            </FormItem>
        </Col>
    </Row>
</template>

<script lang="ts" setup>
import { defineProps, computed } from 'vue'
import RegionCascader from '@/components/common/regionCascader/index.vue'
import PointMarkerFormItem from '@/components/common/customizedFormItem/pointMarkerFormItem.vue'
import {
    IPoint
} from '../type/index';
import moment from 'moment'
import SelectByList from '@/components/common/selectByList';
import AreaSelectTreeNoDevice from '@/components/global/tree/areaSelectTreeNoDevice.vue';
const props = defineProps<{modelValue: IPoint}>()

const form = computed(() => props.modelValue)
const handleDisabled = computed(()=>window.location.href.indexOf('addPoint') === -1) 

const onMapTapperConfirm = (point:any, address:any) => {
    const { province, city, district, township, street, streetNumber } = address;
    // const fillCondition = !form.value.area && !form.value.address
    const fillCondition = !form.value.area
    if (fillCondition) {
        let area = ''
        area += province ? province + ',' : ''
        area += city ? city + ',' : ''
        area += district || ''
        form.value.area = area;
        // form.value.address = township + street + streetNumber;
    }
}
const handlerAreaPath = computed({
    get() {
        return   form.value.areaPath?.replace(/@/g, '/');
    },
    set(newVal) {
        form.value.areaPath = newVal?.replace(/\//g, '@');
    }
})
</script>

<style lang="less" scoped>
.show-map {
    position: absolute;
    top: 0px;
    left: 100px;
    transform: translate(0, -25%);
    font-size: 16px;
    color: #165DFF;
    cursor: pointer;
    z-index: 1000;
    .ivu-icon {
        font-weight: 700;
    }
}
:deep(.ivu-form-item-label){
    padding-top: 0;
}
:deep(.ivu-cascader-rel .ivu-icon-ios-close-circle){
    font-family: "iconfont" !important;
    color: #4E627E;
    font-size: 16px;
    &:before{
        content: "\e705";
    }
}
</style>
