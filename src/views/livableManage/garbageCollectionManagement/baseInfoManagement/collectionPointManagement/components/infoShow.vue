<template>
    <Row :gutter="80">
        <Col span="8">
            <s-label label="收运点名称" :value="detailInfo.name"></s-label>
        </Col>
        <Col span="16">
            <s-label label="区域位置" :value="detailInfo.areaPath"></s-label>
        </Col>
        <Col span="8">
            <s-label label="收运点编号" :value="detailInfo.code"></s-label>
        </Col>
        <Col span="8">
            <s-label label="经纬度">
                <template #value>
                    <jwd-map :obj-info="formatPositionPoint"></jwd-map>
                </template>
            </s-label>
        </Col>
        <Col span="8">
            <s-label label="所属区域" :value="formatAreaStr(detailInfo.area)"></s-label>
        </Col>
        <Col span="8">
            <s-label label="具体位置" :value="detailInfo.address"></s-label>
        </Col>
        <Col span="8">
            <s-label label="垃圾桶数量">
                <template #value>
                    {{ detailInfo.num ? `${detailInfo.num} 个` : "" }}
                </template>
            </s-label>
        </Col>
        <Col span="8">
            <s-label label="所属企业" :value="detailInfo.enterpriseName" />
        </Col>
        <!-- 新加收运失效日期收运失效日期 -->
        <Col span="8">
            <s-label label="收运生效日期" :value="detailInfo.effectiveTime?.split(' ')[0]" />
        </Col>
        <Col span="8">
            <s-label label="收运失效日期" :value="detailInfo.invalidTime?.split(' ')[0]" />
        </Col>
        <Col span="8">
            <s-label label="关联收运计划" :value="detailInfo.planName" />
        </Col>
        <Col span="8">
            <s-label label="最近收运时间" :value="detailInfo.collectionTime" />
        </Col>
    </Row>
    <Row>
        <Col span="24">
            <s-label label="关联收运周期" :tooltip="false">
                <template #value>
                    <s-tag
                        v-if="detailInfo?.timeType == 1"
                        background="#E8F3FF"
                        color="#165DFF"
                        size="large"
                        >每天</s-tag
                    >
                    <!-- 每周 -->
                    <div class="" v-if="detailInfo?.timeType == 2">
                        <s-tag
                            v-for="(n, i) in detailInfo?.timeValues?.split(',')"
                            background="#E8F3FF"
                            color="#165DFF"
                            size="large"
                            :key="i"
                        >
                            {{ weekList[+n] }}
                        </s-tag>
                    </div>
                    <div class="" v-if="detailInfo?.timeType == 3">
                        <s-tag
                            v-for="(n, i) in detailInfo?.timeValues?.split(',')"
                            background="#E8F3FF"
                            color="#165DFF"
                            size="large"
                            :key="i"
                        >
                            {{ n }}号
                        </s-tag>
                    </div>
                </template>
            </s-label>
        </Col>
    </Row>
    <Row>
        <Col span="24">
            <s-label label="备注" :tooltip="false">
                <template #value>
                    {{ detailInfo.note }}
                </template>
            </s-label>
        </Col>
    </Row>
</template>

<script lang="ts" setup>
import { computed, defineProps } from "vue";
import { IPoint } from "@/views/livableManage/foodWasteManagement/baseInfoManagement/collectionPointManagement/type";
import { timeTypeName } from "../enum/index";
import { formatAreaStr } from "@/utils/tool";
import TooltipAutoShow from "@/components/global/tooltipAutoShow/index.vue";
const props = defineProps<{ detailInfo: IPoint }>();
const detailInfo = computed<IPoint>(() => props.detailInfo);

const formatPositionPoint = computed(() => {
    return { gdx: detailInfo.value.gdx, gdy: detailInfo.value.gdy };
});

const formatTimeRange = computed(() => {
    if (!detailInfo.value.timeType && !detailInfo.value.timeValues) {
        return "--";
    }
    return (
        (timeTypeName[detailInfo.value.timeType] || "") +
        (formatWeek(detailInfo.value.timeValues) || "")
    );
});
const weekList = ["", "周一", "周二", "周三", "周四", "周五", "周六", "周日"];
const formatWeek = (weekStr: string) => {
    const weekArr = weekStr?.split(",");
    const formatArr = weekArr?.map((item) => weekList[item]);
    return formatArr?.join("、");
};
</script>

<style lang="less" scoped>
.show-map {
    font-size: 16px;
    color: #165dff;
    .ivu-icon {
        font-weight: 700;
    }
}
.license-wrapper {
    margin-bottom: 12px;
    .title {
        color: #798799;
        font-size: 14px;
        padding-right: 8px;
    }
}
:deep(.tag-cont.large) {
    margin-bottom: 8px;
}
</style>
