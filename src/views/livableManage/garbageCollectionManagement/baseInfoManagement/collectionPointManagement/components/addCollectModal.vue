<template>
        <div>
            <div class="title">
                <div class="title-icon-img">
                    <img :src="require('@/assets/images/icon_detail.png')" alt="">
                </div>
                <Title level="5">收运信息</Title>
            </div>
            <Row gutter="24">
                <Col span="12">
                    <s-label label="收运计划" :value="modalInfo.planName"></s-label>
                </Col>
                <Col span="12">
                    <s-label label="收运任务" :value="modalInfo.taskName"></s-label>
                </Col>
                <Col span="12">
                    <s-label label="任务时间" :value="modalInfo.taskTime"></s-label>
                </Col>
                <Col span="12">
                    <s-label label="收运人" :value="modalInfo.contactPerson"></s-label>
                </Col>
                <Col span="12">
                    <s-label label="收集车辆" :value="modalInfo.carLicenseNo"></s-label>
                </Col>
                <Col span="12">
                    <s-label label="收运公司" :value="modalInfo.companyName"></s-label>
                </Col>
            </Row>
            <Title level="5">收运填报</Title>
            <Form ref="formRef" :model="modalInfo" :rules="modalRules">
                <Row gutter="24" justify="space-between">
                    <Col span="12">
                        <FormItem label="收运点">
                            <Input v-model="modalInfo.pointName" disabled />
                        </FormItem>
                    </Col>
                    <Col span="12">
                        <FormItem label="所属企业">
                            <Input v-model="modalInfo.enterpriseName" disabled />
                        </FormItem>
                    </Col>
                </Row>
                <Row gutter="24">
                    <Col span="12">
                        <FormItem label="收运时间">
                            <date-picker v-model="formatDateTime" type="datetime"></date-picker>
                        </FormItem>
                    </Col>
                </Row>
                <Row>
                    <Col span="24">
                        <div class="title">
                            收运数量
                        </div>
                        <div class="trashcan-wrapper">
                            <Row gutter="32" justify="flex-start">
                            <template v-for="(item, index) in modalInfo.garbageCans">
                                <Col span="6">
                                    <FormItem :label="item.name" :prop="`garbageCans[${index}].weight`" :rules="garbageCansRule">
                                        <Input v-model="item.weight" clearble placeholder="请输入">
                                            <template #append>
                                                kg
                                            </template>
                                        </Input>
                                    </FormItem>
                                </Col>
                            </template>
                            </Row>
                        </div>
                    </Col>
                </Row>
            </Form>
        </div>
</template>

<script lang="ts" setup>

import { computed, defineExpose, defineProps, getCurrentInstance, ref } from 'vue';
import moment from 'moment'
import { useEdit } from '../useEdit'
import {
    editReceiptAndShipmentRecords,
    getGarbageTaskInfo
} from '@/api/livableManage/foodWasteManagementService';
import { DatePicker } from 'view-ui-plus';
import Util from "@/utils";
import { isNullOrEmpty } from "@/utils/tool";

const { modalRules, modalInfo } = useEdit()

const formRef = ref(null)
const that = getCurrentInstance()?.appContext.config.globalProperties;
const props = defineProps<{id: number}>()

// const modalInfo = computed(() => props.modelValue)

const formatDateTime = computed({
    get() {
        if (!modalInfo.value.time) return new Date();
        return moment(modalInfo.value.time).toDate()
    },
    set(newDateTime) {
        
        if (newDateTime) {
            modalInfo.value.time = moment(newDateTime).format('YYYY-MM-DD HH:mm:ss')
        } else {
            modalInfo.value.time = '';
        }
    }
})
const oldGarbageCans = ref()
const onInitModal = async(id:number) => {
    const res = await getGarbageTaskInfo(id)
    if (res.success) {
        oldGarbageCans.value = Util.objClone(res.data.garbageCans);
        modalInfo.value = res.data;
    }
}
onInitModal(props.id)

const garbageCansRule = [{
    pattern: /^(([1-9]\d{1,2}(\.\d{1,2})?)|\d((\.\d{1,2})?)|1000|1000.0|1000.00)$/,
    message: '数值范围0-1000'
}]

const checkGarbageCansChange = ():boolean => {
    if (isNullOrEmpty(oldGarbageCans.value)) {
        return true;
    }
    if (JSON.stringify(oldGarbageCans.value) === JSON.stringify(modalInfo.value.garbageCans)) {
        return false;
    }
    return true;
}
const handleConfirm = async() => {
    const isValidated = await formRef.value.validate()
    if(!checkGarbageCansChange()){
        that.$Message.warning('至少填写一个垃圾桶');
        return;
    }
    if (isValidated) {
        const params = Object.assign({}, modalInfo.value, { type: 2, status: 1 })
         const res = await editReceiptAndShipmentRecords(params);
         return res.success;
    }
}


defineExpose({ handleConfirm });


</script>

<style lang="less" scoped>
:deep(.ivu-icon-ios-arrow-down){
    color: #4E627E !important;
}
:deep(.ivu-form-item){
    width: 100% !important;
}
:deep(.ivu-form-item-label){
    padding-top: 0;
    padding-bottom: 8px;
}
.title{
    min-height: 34px;
    line-height: 22px;
    color: #798799;
    font-size: 14px;
    padding-right: 8px;
}
.trashcan-wrapper{
    width: 100%;
    background-color: #F8FAFB;
    padding: 16px;
}
.title{
    display: flex;
    .title-icon-img{
        width: 24px;
        height: 24px;
        margin-right: 8px;
        img{
            width: 100%;
            height: 100%;
        }
    }
    h5{
        margin-bottom: 0;
    }
}

</style>
