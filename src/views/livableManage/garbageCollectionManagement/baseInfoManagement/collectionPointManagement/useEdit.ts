import { ref } from 'vue';
import { validateform } from '@/utils/validateform';
import { useRoute, useRouter } from 'vue-router';
import { getGarbageInfo } from '@/api/livableManage/foodWasteManagementService';
import { IModalInfo, IPoint } from './type/index';

const createPointInfo = ():IPoint => {
    return {
        name: '', // 收运点名称
        code: '', // 收运点编号
        objx: 0, // 经度
        objy: 0, // 纬度
        area: '', // 所属区域
        address: '', // 处理厂位置
        num: 1, // 垃圾桶个数
        enterpriseName: '', // 所属企业
        planName: '', // 关联收运计划
        timeValues: '', // 关联收运周期
        collectionTime: '', // 最近收运时间
        note: '', // 备注
    }
}
const createModalInfo = ():IModalInfo => {
    return {
        planName: '', // 收运计划
        taskName: '', // 收运任务
        taskTime: '', // 任务时间
        contactPerson: '', // 收运人
        carLicenseNo: '', // 收集车辆
        companyName: '', // 收运公司
        // 收运填报
        pointName: '', // 收运点
        enterpriseName: '', // 所属企业
        garbageCans: [], // 收运数量(垃圾桶)
        time: '' // 收运时间
    }
}
const checkRequired = (rule: any, value: number | string, callback: any) => {
    console.log('validte', { rule, value })
    if (value) {
        callback()
    } else {
        callback(new Error('请完善必填项'))
    }
}
const checkNum = (rule: any, value: number | string, callback: any) => {
    if (value) {
        if (+value > 10 || +value < 1) {
            callback(new Error('请输入大于1， 小于10的数字'))
        }
    }
    callback()
}
const formRules = {
    name: validateform.required,
    objx: [checkRequired],
    num: [checkRequired, checkNum],
    invalidTime: validateform.required,
    effectiveTime: validateform.required,
    enterpriseId: validateform.selectRequired,
    areaPath: validateform.required
}
// 根据该收运点的垃圾桶数量，自动显示对应的垃圾桶个数的输入框，
// 数字填写，必填项，至少填一个
// 范围0-1000  最多两位小数
// 收运时间，打开弹窗时，默认带出显示当前时间，可以进行人为更改，必填项
const modalRules = {
    time: validateform.required
}
// ^[0-9a-zA-Z]{18}$ 18位字符正则
// /^(?:(?![IOZSV])[\dA-Z]){2}\d{6}(?:(?![IOZSV])[\dA-Z]){10}$/  营业执照18位正则
export const useEdit = () => {
    const isEdit = ref(false);
    const detailInfo = ref<IPoint>(createPointInfo());
    const modalInfo = ref<IModalInfo>(createModalInfo());
    const isLoading = ref(false);

    const router = useRouter();
    const route = useRoute();


    const clearModalInfo = () => {
        modalInfo.value = createModalInfo();
    }
    const onInit = async(id: string | undefined) => {
        if (isEdit) {
            console.log('onEdit', id);
            const res = await getGarbageInfo({ id })
            console.log(res);
        }
    }

    return { detailInfo, modalInfo, isEdit, formRules, router, route, onInit, isLoading, clearModalInfo, modalRules }
}

