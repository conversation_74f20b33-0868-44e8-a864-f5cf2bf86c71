export interface IPoint{
    name: string, // 收运点名称
    code?: string, // 收运点编号
    objx: number, // 经度
    objy: number, // 纬度
    area: string, // 所属区域
    address: string, // 处理厂位置
    num: number, // 垃圾桶个数
    enterpriseName: string, // 所属企业
    enterpriseId?: string, // 所属企业id
    planName: string, // 关联收运计划
    timeValues: string, // 关联收运周期
    collectionTime: string, // 最近收运时间
    note: string; // 备注
    timeType?: string; // 时间类型
    effectiveTime?:string,
    invalidTime?:string,
    areaPath?: string
}

export interface IModalInfo{
    planName: string; // 收运计划
    taskName: string; // 收运任务
    taskTime: string; // 任务时间
    contactPerson: string; // 收运人
    carLicenseNo: string; // 收集车辆
    companyName: string; // 收运公司
    // 收运填报
    pointName: string; // 收运点
    enterpriseName: string; // 所属企业
    // 可编辑内容
    garbageCans?: IGarbageCan[]; // 收运数量
    time?: string; // 收运时间
}

export interface IGarbageCan{
    name: string; // 垃圾桶名称
    weight: string; // 重量
    key?: string;
}
