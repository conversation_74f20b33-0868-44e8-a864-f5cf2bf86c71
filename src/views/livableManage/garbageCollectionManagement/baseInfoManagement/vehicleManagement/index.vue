<template>
    <BreadcrumbCustom>
        <BreadcrumbItem>垃圾运收管理</BreadcrumbItem>
        <BreadcrumbItem>基础管理</BreadcrumbItem>
        <BreadcrumbItem>车辆管理</BreadcrumbItem>
    </BreadcrumbCustom>
    <ContentCard title="车辆管理" :base-btn="false">
        <BaseForm @handle-submit="onSubmit(listRef)" :model="condition" inline :label-width="90">
            <template #formitem>
                <FormItem label="车辆名称" prop="name">
                    <Input v-model="condition.name" placeholder="请输入"
                           clearable
                    ></Input>
                </FormItem>
                <FormItem label="车牌号码" prop="licenseNo">
                    <Input v-model="condition.licenseNo" placeholder="请输入"
                           clearable
                    ></Input>
                </FormItem>
            </template>
        </BaseForm>
        <btn-card >
            <Button type="primary" @click="onAdd" icon="md-add" v-auth="'garbageCollectionManagement:basic:car:add'">
                新增收运车辆
            </Button>
            <Button icon="ios-trash" @click="onDelete" v-auth="'garbageCollectionManagement:basic:car:del'">
                删除
            </Button>
        </btn-card>
        <baseTable @on-selection-change="onSelectionChange" :model="condition" ref="listRef"
                   :columns="tableColumn" url="/garbageCar/list"
        >
<template #action="{ row }">
                <LinkBtn size="small" @click="goDetail(row)">
                    详情
                </LinkBtn>
            </template>
        </baseTable>
    </ContentCard>
</template>

<script lang="ts" setup>


import { getCurrentInstance, onMounted, ref } from 'vue';
import { useRouter } from 'vue-router';
import {
    IWasteTreatmentPlant
} from '@/views/livableManage/foodWasteManagement/baseInfoManagement/wasteTreatmentPlant/type';
import { delCar } from '@/api/livableManage/foodWasteManagementService';

const router = useRouter();

const that = getCurrentInstance()?.appContext.config.globalProperties;


const listRef = ref(null);

const condition = ref({
    name: '',
    licenseNo: '',
})

// 详情
const goDetail = ({ id }) => {
    router.push({ name: 'garbageCollectionManagement:basic:car:edit', query: { id }})
}
// 查询
const onSubmit = (ref: any) => {
    console.log('onSubmit')
    const params = Object.assign({}, condition.value, { type: 2 })
    ref && ref.search(params);
}
onMounted(() => {
    onSubmit(listRef.value)
})
// 新增
const onAdd = () => {
    router.push({ name: 'garbageCollectionManagement:basic:car:add' })
}

// 删除
const selectedIds = ref<(string | undefined)[]>([])

const onSelectionChange = (selectedRow: IWasteTreatmentPlant[] = []) => {
    console.log(selectedRow)
    selectedIds.value = selectedRow.map(item => item.id)
}

const onDelete = () => {
    if (selectedIds.value.length === 0) {
        that && that.$Message.warning('最少选择一条数据')
        return
    }
    that && that.$Modal.confirm({
        title: '提示',
        content: '您确定要删除选中的数据吗',
        onOk: () => {
            const ids = selectedIds.value;
            delCar({ ids })
                .then((res) => {
                    if (res.success) {
                        if (res.data) {
                            that && that.$Message.warning(res.data)
                        } else {
                            onSubmit(listRef.value);
                        }
                        selectedIds.value = []
                    }
                })
        }
    })
}

const tableColumn = ref([
    { type: 'selection', width: 40 },
    { title: '车辆名称', key: 'name', tooltip: true },
    { title: '车牌号码', key: 'licenseNo', tooltip: true },
    { title: '额定载重（吨）', width: 120, key: 'load', tooltip: true },
    { title: '所属收运公司', width: 130, key: 'collectCompanyName', tooltip: true },
    { title: '司机', key: 'driver', tooltip: true },
    { title: '联系方式', key: 'driverPhone', tooltip: true },
    { title: '备注', key: 'note', tooltip: true },
    { title: '操作', slot: 'action', width: 80, lock: true, fixed: true }
])


</script>

<style lang="less" scoped>
/deep/ .ivu-form-item{
    width: 80%;
}

</style>
