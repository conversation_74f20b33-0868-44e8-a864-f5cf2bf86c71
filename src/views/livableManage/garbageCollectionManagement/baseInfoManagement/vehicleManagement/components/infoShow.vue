<template>
    <Row :gutter="80">
        <Col span="8">
            <s-label label="车辆名称" :value="detailInfo.name"></s-label>
        </Col>
        <Col span="8">
            <s-label label="车辆编号" :value="detailInfo.code"></s-label>
        </Col>
        <Col span="8">
            <s-label label="车牌号码" :value="detailInfo.licenseNo"></s-label>
        </Col>
        <Col span="8">
            <s-label label="额定载重" :value="detailInfo.load">
                <template #value>
                    {{detailInfo.load ? `${detailInfo.load} 吨` : '--'}}
                </template>
            </s-label>
        </Col>
        <Col span="8">
            <s-label label="收运公司" :value="detailInfo.collectCompanyName"></s-label>
        </Col>
        <Col span="8">
            <s-label label="司机" :value="detailInfo.driver"></s-label>
        </Col>
        <Col span="8">
            <s-label label="联系方式" :value="detailInfo.driverPhone" />
        </Col>
    </Row>
    <Row>
        <Col span="24">
            <s-label label="备注" :tooltip="false">
                <template #value>
                    {{detailInfo.note}}
                </template>
            </s-label>
        </Col>
    </Row>
</template>

<script lang="ts" setup>
import { computed, defineProps } from 'vue'
import {
    ICarInfo
} from '@/views/livableManage/foodWasteManagement/baseInfoManagement/vehicleManagement/type';
import { garbageCollectCompanyList } from "@/api/livableManage/foodWasteManagementService";
import { ComponentInfo } from "@/api/manholeCoverService";

const props = defineProps<{detailInfo: ICarInfo}>()
const detailInfo = computed<ICarInfo>(() => props.detailInfo)


</script>

<style scoped>
.show-map {
    font-size: 16px;
    color: #165DFF;
.ivu-icon {
    font-weight: 700;
}
}
</style>
