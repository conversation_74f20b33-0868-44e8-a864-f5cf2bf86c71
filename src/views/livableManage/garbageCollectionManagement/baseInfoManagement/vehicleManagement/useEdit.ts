import { ref} from 'vue';
import { validateform } from '@/utils/validateform';
import { useRoute, useRouter } from 'vue-router';
import { getGarbageInfo} from '@/api/livableManage/foodWasteManagementService';
import {
    ICarInfo
} from './type/index';

const createCarInfo = ():ICarInfo => {
    return {
        name: '', // 车辆名称
        code: '', // 车辆编码
        licenseNo: '', // 车牌号码
        carCode: '',
        vehicleNo: '',
        load: '', // 额定载重
        companyId: '', // 收运公司id
        driver: '', // 司机
        driverPhone: '', // 联系方式
        note: '' // 备注
    }
}
const formRules = {
    name: validateform.required,
    licenseNo: [
        validateform.required],
    driverPhone: [{ pattern: /^1[3456789]\d{9}$/, message: '手机号错误' }],
    load: [{
        pattern: /^[1-9]\d{0,8}$/,
        message: '请输入最多9位的正整数'
    }]
}
export const useEdit = () => {
    const isEdit = ref(false);
    const detailInfo = ref<ICarInfo>(createCarInfo());
    const isLoading = ref(false);

    const router = useRouter();
    const route = useRoute();

    const onInit = async(id: string | undefined) => {
        if (isEdit) {
            console.log('onEdit', id);
            const res = await getGarbageInfo({ id })
            console.log(res);
        }
    }



    return { detailInfo, isEdit, formRules, router, route, onInit, isLoading }
}

