import { defineComponent, onMounted, Slots, ref, nextTick } from "vue";
import { Select, Option } from "view-ui-plus";
import { garbagePlanTaskList } from "@/api/livableManage/foodWasteManagementService";
import { ComponentInfo } from "@/api/manholeCoverService";
// 收运任务下拉
export default defineComponent({
    name: "collect-car-select",
    props: {
        type: {
            type: [Number, String],
            default: 1,
        },
        id: {
            type: [Number, String],
        },
    },
    emits: ["update:modelValue", "initSelect", "onChange"],
    setup(props, ctx) {
        // 设备类型url
        const list = ref<ComponentInfo[]>([]);
        const gardenSoilDeviceList = async () => {
            const params = {
                page: {
                    current: 1,
                    size: -1,
                },
                customQueryParams: {
                    type: props.type,
                    planId: props.id,
                },
            };
            const res = await garbagePlanTaskList(params);
            const { data, success }: { success: boolean; data: recordsResponse<ComponentInfo[]> } =
                res as unknown as HttpResponse<recordsResponse<ComponentInfo[]>>;
            // 成功
            if (success) {
                list.value = data.records;
            }
        };
        const handleSelect = (val: any) => {
            const data = list.value.filter((k) => k.id == val)[0];
            ctx.emit("update:modelValue", val || "");
            ctx.emit("onChange", data || {});
        };
        const selectVal = ref("");
        onMounted(() => {
            nextTick(() => {
                gardenSoilDeviceList();
            });
        });

        return () => (
            <Select v-model={selectVal.value} onOnChange={handleSelect} clearable filterable placeholder="请选择收运任务">
                {list.value.map((item, index) => (
                    <Option value={item.id} label={item.name}>
                        {item.name}
                    </Option>
                ))}
            </Select>
        );
    },
});
