<template>
    <!-- <no-data v-if="!garbagePlanDetail" value="暂无收运任务" /> -->
    <div>
        <div class="plate-tit">
            收运点总量
        </div>
        <div class="device-overview">
            <div class="device-num">
                {{ garbagePlanDetail?.pointNum }}
                <div class="online-num">
                    收运完成率<span>{{ ((garbagePlanDetail?.donePointNum || 0) * 100 / (garbagePlanDetail?.pointNum || 1)).toFixed(2)
                    }}%</span>
                </div>
            </div>
            <div class="device-echart">
                <echart-item :option="pieOption" class="pie-echart" ref="pieRef" />
            </div>
        </div>
        <div class="precent-box">
            <div class="name flex-box-between">收运任务<span><span class="blue">{{ garbagePlanDetail?.doneTaskNum || 0
            }}/</span>{{
    garbagePlanDetail?.taskNum || 0 }}</span></div>
            <Progress :percent="(garbagePlanDetail?.doneTaskNum || 0) * 100 / (garbagePlanDetail?.taskNum || 1)"
                :stroke-width="3" stroke-color="#4086FF" hide-info />
        </div>
        <div class="base-info">
            <s-label label="收运垃圾桶总数" :value="garbagePlanDetail?.garbageNum ? `${garbagePlanDetail?.garbageNum}个` : '--'" />
            <s-label label="收运总重量" :value="garbagePlanDetail?.weight ? garbagePlanDetail?.weight + 'kg' : '--'" />
        </div>
        <div class="plate-tit mt-16">
            <img src="../images/icon_manholecover_011.png" class="title-icon" />
            收运点情况
        </div>
        <div class="last-record no-padding">
            <no-data v-if="!list?.length" value="暂无收运记录" />
            <baseTable v-else class="table-container" :show-page="false" :data="list.slice(0, 5)" :columns="tableList">
                <template #todayCompleted="{ row }">
                    <s-tag v-if="row.status == 1" color="#00B42A" background="#E8FFEA">{{ (row.todayCompleted || 0) + '次'
                    }}</s-tag>
                    <s-tag v-else color="#4E627E" background="#F2F3F5">{{ (row.todayCompleted || 0) + '次' }}</s-tag>
                </template>
                <template #doneGarbageNum="{ row }">
                    <tooltip-auto-show
                        :content="row.doneGarbageNum ? (row.doneGarbageNum + '桶') : '--'"></tooltip-auto-show>
                </template>
            </baseTable>
            <!-- <div class="record-box">
                <div class="record-item" v-for="(item, index) in   garbagePlanDetail?.garbageTaskRecordsVos "
                     :key="index">
                     @click="(recordId = item.id) && (componentName = 'garbageTaskRecords')"
                    <span>{{ item.pointName }}</span>
                    <span>{{ item.todayCompleted }}次</span>
                    <span>{{ item.doneGarbageNum }}桶</span>
                    <s-tag v-if="item.status == 1" background="#E8FFEA" color="#00B42A">已收运</s-tag>
                    <s-tag v-else background="#F2F3F5" color="#4E627E">未收运</s-tag>
                    <span>{{ item.time?.split(' ')?.[0] || '--' }}</span>
                </div>
            </div> -->
        </div>
    </div>
    <s-modal :width="625" title="收运记录" :component-name="componentName" @emitClose="componentName = ''" :ref-box="modalRef"
        footerHide>
        <component :is="componentName" ref="modalRef" :id="recordId" type="modal" />
    </s-modal>
</template>
<script lang="ts">
import { defineComponent, ref, watch } from 'vue'
import { garbageTaskInfo, garbagePointInfo } from "@/api/livableManage/type";
import { garbageMapOverview, garbagePointQuery } from '@/api/livableManage/foodWasteManagementService';
import garbageTaskRecords from '../../garbageTaskRecords/index.vue'
import EchartItem from '@/components/common/EchartItem/index'
import { ECOption } from '@/components/common/EchartItem/echart';
import { List } from 'view-ui-plus';
export default defineComponent({
    components: {
        garbageTaskRecords,
        EchartItem
    },
    props: {
        areaPaths: {
            type: Array,
            default: () => []
        },
        type: {
            type: String, //
            default: '1'
        }
    },
    setup(props) {
        const tableList = [
            { title: '收运点', key: 'pointName', tooltip: true },
            { title: '已收运次数', slot: 'todayCompleted', width: 100, tooltip: true },
            { title: '收运垃圾桶数量', slot: 'doneGarbageNum', width: 110, tooltip: true }
        ]
        //饼状图
        const pieOption = ref<ECOption>({
            tooltip: {
                trigger: 'item',
                confine: true,
            },
            legend: {
                top: 'middle',
                left: 120,
                icon: 'circle',
                itemWidth: 6,
                itemHeight: 6,
                orient: 'vertical',
                textStyle: {
                    color: '#4E627E'
                },
            },
            color: ['#7BE188', '#FFCF8B'],
            series: [
                {
                    name: '收运点总量',
                    type: 'pie',
                    radius: ['48%', '68%'],
                    center: ['32%', '50%'],
                    avoidLabelOverlap: false,
                    label: {
                        show: false,
                        position: 'center'
                    },
                    emphasis: {
                        label: {
                            show: true,
                            fontSize: 14,
                            fontWeight: 'bold'
                        }
                    },
                    labelLine: {
                        show: false
                    },
                    data: []
                }
            ]
        })
        const garbagePlanDetail = ref<garbageTaskInfo>();
        const list = ref<garbagePointInfo[]>()
        const getData = async () => {

            const res = await garbageMapOverview(props)
            const { data, success }: { data: garbageTaskInfo, success: boolean } = res as unknown as HttpResponse<garbageTaskInfo>
            if (success) {
                garbagePlanDetail.value = data;
                pieOption.value.series![0].data = [
                    {
                        name: '已收运',
                        value: data.donePointNum
                    },
                    {
                        name: '未收运',
                        value: (data.pointNum || 0) - (data.donePointNum || 0)
                    }
                ]
            }
        }
        const getRecords = async () => {

            const res = await garbagePointQuery(props)
            const { data, success }: { data: recordsResponse, success: boolean } = res as unknown as HttpResponse<recordsResponse>
            if (success) {
                list.value = data.records as garbagePointInfo[];

            }
        }
        watch(() => props.areaPaths, () => {
            getData()
            getRecords()
        }, { immediate: true })
        // 弹框需要的信息
        const componentName = ref<string>('')
        const modalRef = ref()
        const recordId = ref<number>(0)
        return {
            garbagePlanDetail,
            componentName,
            modalRef,
            recordId,
            tableList,
            pieOption,
            list
        }
    }
})
</script>
<style lang="less" scoped>
/deep/.ivu-progress-inner {
    background: #E5E6EB;
}

.table-container {
    margin-top: 12px;

    /deep/ .ivu-table {
        .ivu-table-header {
            thead {
                tr {
                    th {
                        font-size: 12px;
                        padding: 0;
                    }
                }
            }
        }
    }

}
</style>
