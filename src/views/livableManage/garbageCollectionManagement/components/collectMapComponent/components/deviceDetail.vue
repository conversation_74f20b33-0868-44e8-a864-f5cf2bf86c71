<template>
    <no-data v-if="!activedObjId" value="请先选择收运点" />
    <div class="details-container" v-else>
        <div class="head-cont normal">
            <div class="icon-img">
                <img src="../images/icon_002.png" class="top-icon" v-if="type == '1'" />
                <img src="../images/icon_001.png" class="top-icon" v-if="type == '2'" />
            </div>
        </div>
        <div class="name">
            {{ deviceDetail?.name }}
        </div>
        <div class="code">
            {{ deviceDetail?.code }}
        </div>
        <div class="scorll-map-cont">
            <div class="device-info-list">
                <s-label label="区域位置" :value="deviceDetail?.areaPath" />
                <s-label label="垃圾桶个数" :value="deviceDetail?.num" />
                <s-label
                    label="经纬度"
                    :value="
                        deviceDetail?.objx ? `${deviceDetail?.objx},${deviceDetail?.objy}` : '--'
                    "
                />
                <s-label label="所属企业" :value="deviceDetail?.enterpriseName" />
                <s-label label="关联收运计划" :value="deviceDetail?.planName" />
                <s-label label="收运计划状态">
                    <template #value>
                        <useStatus :value="deviceDetail?.planStatus" />
                    </template>
                </s-label>
            </div>
            <div class="last-record">
                <div class="plate-tit">
                    <img src="../images/icon_manholecover_011.png" class="title-icon" />
                    最近收运记录
                </div>
                <no-data v-if="!deviceDetail?.garbageTaskRecordsVos?.length" value="暂无收运记录" />
                <baseTable
                    @on-row-click="(item:any)=>(recordId = item.id) && (componentName = 'garbageTaskRecords')"
                    v-else
                    class="table-container"
                    :show-page="false"
                    :data="deviceDetail?.garbageTaskRecordsVos"
                    :columns="tableList"
                >
                </baseTable>
                <!-- <div class="record-box">
                    <div class="record-item" v-for="(item, index) in deviceDetail?.garbageTaskRecordsVos"
                        @click="(recordId = item.id) && (componentName = 'garbageTaskRecords')" :key="index">
                        <span>{{ item.time }}</span>
                        <span>{{ item.content }}</span>
                        <span>{{ item.contactPerson }}</span>
                    </div>
                </div> -->
            </div>
        </div>
    </div>
    <s-modal
        :width="625"
        title="收运记录"
        :component-name="componentName"
        @emitClose="componentName = ''"
        :ref-box="modalRef"
        footerHide
    >
        <component :is="componentName" ref="modalRef" :id="recordId" type="modal" />
    </s-modal>
</template>
<script lang="ts">
import { defineComponent, ref, watch } from "vue";
import { garbageCarPointId } from "@/api/livableManage/foodWasteManagementService";
import { getDicValue } from "@/utils/tool";
import { garbagePlanInfo, garbagePointInfo } from "@/api/livableManage/type";
import garbageTaskRecords from "../../garbageTaskRecords/index.vue";
export default defineComponent({
    components: {
        garbageTaskRecords,
    },
    props: {
        activedObjId: {
            type: Number,
            default: "",
        },
        type: {
            type: [String, Number],
            default: "",
        },
    },
    setup(props, ctx) {
        const tableList = [
            { title: "收运时间", key: "time", width: 120, tooltip: true },
            { title: "收运详情", key: "content", tooltip: true },
            { title: "收运人", key: "contactPerson", tooltip: true },
        ];
        // 设备详情
        const deviceDetail = ref<garbagePointInfo>();
        const getDetail = async () => {
            if (!props.activedObjId) {
                deviceDetail.value = {};
                return;
            }
            const res = await garbageCarPointId(props.activedObjId);
            const { data, success }: { data: garbagePointInfo; success: boolean } =
                res as unknown as HttpResponse<garbagePointInfo>;
            if (success) {
                deviceDetail.value = data;
            }
        };
        watch(
            () => props.activedObjId,
            () => {
                getDetail();
            },
            { immediate: true }
        );
        // 弹框需要的信息
        const componentName = ref<string>("");
        const modalRef = ref();
        const recordId = ref<number>(0);
        return {
            deviceDetail,
            getDicValue,
            componentName,
            modalRef,
            recordId,
            type: props.type,
            tableList,
        };
    },
});
</script>
<style lang="less" scoped>
.table-container {
    margin-top: 12px;

    /deep/ .ivu-table {
        .ivu-table-header {
            thead {
                tr {
                    th {
                        font-size: 12px;
                        padding: 0;
                    }
                }
            }
        }
        .ivu-table-body {
            .ivu-table-tbody {
                .ivu-table-row {
                    cursor: pointer;
                }
            }
        }
    }
}
</style>
