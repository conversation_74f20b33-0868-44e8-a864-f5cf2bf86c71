/deep/.head-cont {
    &.online {
        .icon-img {
            // background: #FFFFFF url('./images/icon_manholecover_009.png') no-repeat center;
            background-size: 40px 40px;
        }
    }
    .icon-img {
        height: 50px;
        width: 50px;
        // background: #FFFFFF url('./images/icon_manholecover_001.png') no-repeat center;
        background-size: 40px 40px;
        border-radius: 100%;
        position: absolute;
        left: 50%;
        transform: translate(-50%, 35px);
        .top-icon {
            height: 20px !important;;
            width:20px !important;
        }
    }
}
/deep/.right-tab-title {
    &.tab-title .tab-item {
        padding: 0 16px;
    }
}
/deep/.right-tab-body {
    .device-info-list {
        padding: 0 24px 20px 24px!important;
    }
    .alarm-info-list {
        margin: 0 16px;
    }

    .bridge-total-num {
        display: flex;
        padding: 0px 8px 8px 0px;
        align-items: center;
        img {
            display: block;
            width: 42px;
            height: 42px;
            margin-right: 20px;

        }
        .bridge-num {
            color: @text-color;
            font-weight: 400;
            font-size: 16px;
            line-height: 24px;

            .num {
                color: @title-color;
                font-weight: 700;
                font-size: 24px;
                line-height: 32px;
            }
        }
    }
    .bridge-gather {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        column-gap: 26px;
        line-height: 22px;
        color: @text-color;
        padding-bottom: 24px;
        position: relative;
        white-space: nowrap;
        height: 90px;
        img  {
            display: block;
            height: 18px;
            margin-right: 4px;
        }
        .gather-num {
            display: flex;
            align-items: flex-end;
            font-weight: 500;
            font-size: 16px;
            line-height: 19px;
            color: @title-color;
            margin: 8px 0px;
            span {
                font-weight: 400;
                font-size: 12px;
                line-height: 14px;
                color: @input-placeholder-color ;
                margin-left: 5px;
            }
        }
        .percent-box {
            align-items: center;
            height: 20px;
            font-size: 12px;
            .percent-num {
                margin-left: 8px;
            }
        }
    }
}
/deep/.last-record {
    padding: 0 16px;
    .record-box {
        margin-top: 8px;
    }
    .record-item {
        display: flex;
        height: 40px;
        border-bottom: 1px solid #E5E6EB;
        align-items: center;
        padding: 0 16px;
        cursor: pointer;
        &:hover {
            background: #F3F7FB;
            color:#165DFF;
        }
        span {
            flex:1;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            &:last-child {
                text-align: right;
            }
        }
    }
}
/deep/ .precent-box {
    height: 60px;
    background: #F3F7FB;
    margin-bottom: 16px;
    padding: 10px 12px 4px 13px;
    .name {
        color: #1E2A55;
        font-weight: bold;
        font-size: 16px;
        line-height: 24px;
        // margin-bottom: 4px;
        span {
            font-size: 14px;
            font-weight: normal;
            .blue {
                color:#165DFF;
            }
        }
    }
    .num-info {
        font-weight: 400;
        font-size: 12px;
        color: #798799;
        line-height: 20px;
    }
}