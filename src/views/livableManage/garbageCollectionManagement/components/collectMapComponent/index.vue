<template>
    <div>
        <Card dis-hover :bordered="false" class="map-cont" id="fullscreen">
            <div class="face-plate-cont">
                <div class="left-plate">
                    <div class="area-tree plate-bg">
                        <!-- <collect-plan-select :type="type" @onChange="handleChangePlan" /> -->
                        <map-area-tree-select :type="type" @updateAreaPath="updateAreaPath" methodUrl="getGarbagePointTree"
                            modelIds="4,5" />
                    </div>
                    <div class="device-cont plate-bg">
                        <Input suffix="ios-search" placeholder="请输入收运点信息" clearable v-model="searchText" />
                        <scorll-box :scorll-id="activedObjId" key="pointId">
                            <template #cont>
                                <map-device-list :deviceList="deviceList" :searchText="searchText" lableKey="pointName"
                                    :show-tag="[]" valueKey="pointCode" :activedObjId="activedObjId" primaryKey="pointId"
                                    @actived-obj="handleActivedObj" />
                            </template>
                        </scorll-box>
                    </div>
                </div>
                <div class="right-plate">
                    <div class="full-op" @click="handleFullScreen">
                        <Icon :type="fullFlag ? 'md-contract' : 'md-expand'" />
                    </div>
                    <div class="device-info-cont">
                        <s-tab :tab-list="tabList" class="plate-bg" tit-class="right-tab-title"
                            :body-class="`${isToggleDropdown ? '' : 'hide-tab-box'} right-tab-body`"
                            @handleChange="handleChange" :default-active="defaultActive">
                            <template #expand>
                                <div class="toggle" @click="isToggleDropdown = !isToggleDropdown">
                                    <Icon :type="isToggleDropdown ? 'ios-arrow-down' : 'ios-arrow-up'" />
                                </div>
                            </template>
                            <template #overview>
                                <div class="scorll-map-cont manhole-cover-right">
                                    <device-oview ref="overviewRef" :area-paths="areaPaths" :type="type" />
                                </div>
                            </template>
                            <template #detail>
                                <device-detail :actived-obj-id="activedObjId" :type="type" />
                            </template>
                        </s-tab>
                    </div>
                </div>
            </div>
            <div id="container" class="container-map" :class="fullFlag ? 'full-map' : ''"></div>
        </Card>
    </div>
</template>

<script lang="ts">
import AMapLoader from '@amap/amap-jsapi-loader';
import { defineComponent, nextTick, onMounted, ref, Ref, watch } from 'vue';
import { ManholeCoverInfo } from '@/api/manholeCoverService';
import { garbagePointQuery } from '@/api/livableManage/foodWasteManagementService';
import DeviceOview from './components/deviceOview.vue';
import DeviceDetail from './components/deviceDetail.vue';
import { launchIntoFullscreen, exitFullscreen } from '@/utils/tool'
import collectPlanSelect from '../collectPlanSelect/index'
import scorllBox from '@/components/common/scorllBox/index'
import { getMarkerContent, renderClusterMarker, renderMarker, showInfoWindow } from '@/components/common/mapAreaTreeSelect/markercontent'
import mapDeviceList from '@/components/common/mapDeviceList'
import { garbagePointInfo } from "@/api/livableManage/type";
import mapAreaTreeSelect from '@/components/common/mapAreaTreeSelect/index'
export default defineComponent({
    name: 'smartNetwork:wisdomNetworkOverViewMap',
    props: {
        type: {
            type: String,
            default: '1'
        }
    },
    components: {
        DeviceOview,
        DeviceDetail,
        collectPlanSelect,
        scorllBox,
        mapDeviceList,
        mapAreaTreeSelect
    },
    setup(props, ctx) {
        // *********************
        // 地图
        // *********************
        const map = ref<any>(null);
        const Amap = ref<any>(null);
        const cluster = ref<any>(null);
        const imgUrl = require('@/assets/images/icon_map_001.png')
        const ininMap = () => {
            AMapLoader.load({
                key: 'ea53a5291f8f7c1f215ba20930ea9866',
                plugins: ['AMap.MarkerCluster'],
                version: '2.0'
            }).then((AMap) => {
                Amap.value = AMap
                map.value = new AMap.Map('container', {
                    resizeEnable: true,
                    // mapStyle: 'https://geohub.amap.com/mapstyle/clone?id=20d3f4f1e43c80b9c68c30fb8a1ab94b',
                    zoom: 16,
                    center: [114.61, 30.45],
                })
                map.value.on('movestart', () => {
                    map.value?.clearInfoWindow()
                })

                map.value.on('zoomstart', () => {
                    map.value?.clearInfoWindow()
                })
                nextTick(() => {
                    updateMapIcon(deviceList.value)
                })
            })
        }
        const _renderClusterMarker = async (context: any) => {
            renderClusterMarker(context, activedObjId.value)
        }
        const _renderMarker = async (context: any) => {
            renderMarker(context, (id: number) => {
                const activeDevice = deviceList.value.find(k => k.pointId == id)

                return getMarkerContent(activeDevice, activedObjId.value, 'hide-tag', () => {
                    if (props.type == '1') return require('./images/icon_002.png')
                    return require('./images/icon_001.png')
                }, 'pointId');
            })
        }
        // 更新地图上的图标
        const updateMapIcon = (data: garbagePointInfo[]) => {
            if (!map.value) return
            // 清除地图上的图标
            // map.value.clearMap()
            if (cluster.value) {
                cluster.value.setMap(null);
            }
            const points = data?.filter((k: garbagePointInfo) => k?.gdx && k?.gdx).map(k => ({ lnglat: [k?.gdx, k.gdy], exData: `${k.pointId}@${k.pointCode || 0}@${k.pointName}` })) || [];
            cluster.value = new Amap.value.MarkerCluster(map.value, points, {
                gridSize: 30, // 设置网格像素大小
                renderClusterMarker: _renderClusterMarker, // 自定义聚合点样式
                renderMarker: _renderMarker, // 自定义非聚合点样式
            });
            cluster.value.on('click', (e: any) => {
                const { clusterData } = e
                console.log(clusterData.length)
                map.value.clearInfoWindow()
                if (clusterData.length == 1) {
                    const _d = clusterData[0].exData.split('@')[0]
                    if (activedObjId.value == _d) {
                        activedObjId.value = 0
                        defaultActive.value = 0
                        return
                    }
                    defaultActive.value = 1
                    activedObjId.value = +_d
                } else {
                    showInfoWindow(clusterData, e, map.value, activedObjId.value, (_d: string) => {
                        map.value?.clearInfoWindow()
                        if (activedObjId.value == +_d) {
                            activedObjId.value = 0
                            defaultActive.value = 0
                            return
                        }
                        defaultActive.value = 1
                        activedObjId.value = +_d
                    })
                }
            })

            nextTick(() => {
                map.value.setFitView();
            })

        }
        onMounted(() => {
            ininMap();
            window.addEventListener('fullscreenchange', e => {
                // 监听到屏幕变化，在回调中判断是否已退出全屏
                const isFull = document.fullscreenElement || document['mozFullScreenElement'] || document['webkitFullscreenElement'];
                fullFlag.value = isFull ? true : false
            })
        })
        // *********************
        // 左侧计划列表
        // *********************
        // 搜索树
        const areaPaths = ref<string[]>([])
        // 更新当前勾选区域
        const updateAreaPath = (areaPath: string[]) => {
            areaPaths.value = areaPath

            defaultActive.value = 0
            getList()
        }
        // *********************
        // 左侧收运点列表
        // *********************
        // 收运点搜索关键字
        const searchText = ref<string>('')
        // 选中的设施
        const activedObjId = ref<number>(0)
        // 监听选中的设施
        watch(activedObjId, () => {
            const activeDevice = deviceList.value.find(k => k.pointId == activedObjId.value) || {}
            if (activeDevice?.gdx) {
                map.value?.setZoomAndCenter(17, [activeDevice.gdx, activeDevice.gdy])

            }
            const overlays: any[] = map.value?.getAllOverlays('marker')
            overlays?.forEach(e => {
                const markerInfo = deviceList.value.filter(k => k.pointId == e.getExtData())[0]
                if (!markerInfo) return
                const content = getMarkerContent(markerInfo, activedObjId.value, 'hide-tag', (d: ManholeCoverInfo) => {
                    if (props.type == '1') return require('./images/icon_002.png')
                    return require('./images/icon_001.png')
                }, 'pointId')
                e.setContent(`${content}<div class="marker-label-box label-box">${markerInfo.pointName}</div>`)
            })
            if (activedObjId.value) {
                isToggleDropdown.value = true
            }
        })
        // 收运点列表
        const deviceList = ref<garbagePointInfo[]>([])
        // 手动设置当前选中收运点
        const handleActivedObj = (item: garbagePointInfo) => {

            activedObjId.value = (activedObjId.value == item.pointId) ? 0 : (item.pointId || 0)
            if (activedObjId.value) {
                defaultActive.value = 1
                return
            }
            defaultActive.value = 0
        }
        // 查询收运点
        const getList = async () => {
            const params = {
                areaPaths: areaPaths.value,
                type: props.type
            }
            const res = await garbagePointQuery(params)
            const { data, success }: { data: recordsResponse, success: boolean } = res as unknown as HttpResponse<recordsResponse>
            if (success) {
                deviceList.value = data.records as garbagePointInfo[];
                const actived_Obj = deviceList.value.filter(k => k.pointId == activedObjId.value)
                if (!actived_Obj.length) { // 已经勾选的设备不在当前区域树中
                    activedObjId.value = deviceList.value[0]?.pointId || 0
                }
                // 更新地图上的图标
                updateMapIcon(deviceList.value)

            }
        }

        // 地图全屏
        const fullFlag: Ref<boolean | null> = ref(false)
        const handleFullScreen = () => {
            const element = document.getElementById('fullscreen');
            if (!element) return
            // false是进入全屏状态
            if (fullFlag.value) {
                // 退出全屏
                exitFullscreen(element);
            } else {
                launchIntoFullscreen(element);
            }
            fullFlag.value = !fullFlag.value
        }
        // 是否展开右侧
        const isToggleDropdown = ref<boolean>(true)
        const defaultActive = ref<number>(0)
        const tabList = ref([
            {
                name: '收运概览',
                key: 'overview',
                icon: 'unordered-list'
            },
            {
                name: '收运点详情',
                key: 'detail',
                icon: 'common'
            }
        ])
        // 概览ref
        const overviewRef = ref()
        // 切换回需要resize
        const handleChange = (i: number) => {
            defaultActive.value = i
            isToggleDropdown.value = true
        }
        return {
            deviceList,
            tabList,
            activedObjId,
            searchText,
            handleFullScreen,
            fullFlag,
            isToggleDropdown,
            handleChange,
            overviewRef,
            defaultActive,
            handleActivedObj,
            // activePlanId,
            type: props.type,
            updateAreaPath,
            areaPaths
        };
    }
});
</script>

<style lang="less" scoped>
@import '../../../../../styles/mapPage.less';
@import './index.less';
</style>
