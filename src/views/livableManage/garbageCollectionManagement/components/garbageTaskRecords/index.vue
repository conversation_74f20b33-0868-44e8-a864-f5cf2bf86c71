<template>
    <detailCard
        :is-edit-btn="isEditBtn"
        title="收运信息"
        :src="require('@/assets/images/icon-基础信息.png')"
        :class="type == 'modal' ? 'modal-card' : ''"
        :is-back-btn="isBackBtn"
        @on-back="onBack"
        @on-edit="onEdit"
    >
        <Row :gutter="80">
            <Col :span="type == 'modal' ? 12 : 8">
                <s-label label="收运计划" :value="detailInfo?.planName" />
            </Col>
            <Col :span="type == 'modal' ? 12 : 8">
                <s-label label="收运任务" :value="detailInfo?.taskName" />
            </Col>
            <Col :span="type == 'modal' ? 12 : 8">
                <s-label label="任务时间" :value="detailInfo?.taskTime?.split(' ')?.[0]" />
            </Col>
            <Col :span="type == 'modal' ? 12 : 8">
                <s-label label="收运人" :value="detailInfo?.contactPerson" />
            </Col>
            <Col :span="type == 'modal' ? 12 : 8">
                <s-label label="收运公司" :value="detailInfo?.companyName" />
            </Col>
        </Row>
    </detailCard>
    <detailCard
        title="收运填报"
        :src="require('@/assets/images/icon-填报.png')"
        :class="type == 'modal' ? 'modal-card' : ''"
    >
        <Row :gutter="80">
            <Col :span="type == 'modal' ? 12 : 8">
                <s-label label="收运点" :value="detailInfo?.pointName" />
            </Col>
            <Col :span="type == 'modal' ? 12 : 8">
                <s-label label="收运车辆" :value="detailInfo?.carLicenseNo" />
            </Col>
            <Col :span="type == 'modal' ? 12 : 8">
                <s-label label="收运时间" :value="detailInfo?.time" />
            </Col>
            <Col :span="24">
                <s-label label="收运数量" :tooltip="false">
                    <template #value>
                        <div class="num-container" v-if="detailInfo?.garbageCans?.length">
                            <div class="num-box">
                                <div class="num-item">
                                    <span
                                        >收运垃圾桶数量：{{ detailInfo?.num }}</span
                                    >
                                </div>
                            </div>
                            <div class="num-box" v-if="detailInfo?.garbageCans?.length">
                                <div class="num-item">收运详情：</div>
                                <div
                                    class="num-item"
                                    v-for="(item, index) in detailInfo?.garbageCans || []"
                                    :key="index"
                                >
                                    <div>
                                        <span>{{ item.name }}</span
                                        >&nbsp;
                                        <span>{{ item.weight }}kg</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <span v-else>--</span>
                    </template>
                </s-label>
            </Col>
        </Row>
    </detailCard>
</template>
<script lang="ts">
import { defineComponent, ref, onMounted } from "vue";
import { getGarbageTaskInfo } from "@/api/livableManage/foodWasteManagementService";
import { garbageRecordInfo } from "@/api/livableManage/type";
export default defineComponent({
    props: {
        id: {
            type: [Number, String],
            default: 0,
        },
        type: {
            type: String,
            default: "",
        },
        isEditBtn: {
            default: false,
            type: Boolean,
        },
        isBackBtn: {
            default: false,
            type: Boolean,
        },
    },
    emits: ["on-edit", "on-back"],
    setup(props, ctx) {
        const type = ref(props.type);
        const detailInfo = ref<garbageRecordInfo>();
        const getGarbageTaskInfoDetail = async () => {
            if (!props.id) return;
            const res = await getGarbageTaskInfo(props.id);
            const { data, success }: { data: garbageRecordInfo; success: boolean } =
                res as unknown as HttpResponse<garbageRecordInfo>;
            if (success) {
                detailInfo.value = data;
            }
        };
        const onBack = () => {
            ctx.emit("on-back");
        };
        const onEdit = () => {
            ctx.emit("on-edit", true);
        };
        onMounted(() => getGarbageTaskInfoDetail());
        return {
            detailInfo,
            type,
            isEditBtn: props.isEditBtn,
            isBackBtn: props.isBackBtn,
            onEdit,
            onBack,
        };
    },
});
</script>
<style lang="less" scoped>
.modal-card {
    padding: 0;

    &.detail-card {
        padding: 8px 0;
    }
}
.num-container {
    display: flex;
    flex-direction: column;
    width: 100%;
    background: #f8fafb;

    .num-box {
        display: flex;
        flex-wrap: wrap;
        // padding-bottom: 16px;
        &:last-child {
            padding-bottom: 16px;
        }
        &.hide-item {
            // padding-bottom: 16px;
        }

        .num-item {
            display: flex;
            align-items: center;
            column-gap: 16px;
            color: #165dff;
            background: #e8f3ff;
            border-radius: 2px;
            padding: 0 8px;
            margin-top: 16px;
            margin-left: 16px;
            height: 24px;
        }
    }
}
</style>
