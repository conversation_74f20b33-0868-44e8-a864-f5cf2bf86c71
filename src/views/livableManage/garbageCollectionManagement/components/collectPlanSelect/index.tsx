import { defineComponent, onMounted, ref, nextTick } from "vue";
import { Input } from "view-ui-plus";
import { garbageMapPlan } from "@/api/livableManage/foodWasteManagementService";
import { garbagePlanInfo } from "@/api/livableManage/type";
import "./index.less";
// 收运点下拉
export default defineComponent({
    name: "collect-plan-select",
    props: {
        type: {
            type: [String, Number],
            default: 1,
        },
        id: {
            type: [String, Number],
        },
        checked: {
            // 只展示当前ID的收运点
            type: Boolean,
            default: false,
        },
    },
    emits: ["update:modelValue", "initSelect", "onChange"],
    setup(props, ctx) {
        const searchText = ref("");
        const planList = ref<garbagePlanInfo[]>([]);
        const activedId = ref<number>(0);
        const handleChoose = (item: garbagePlanInfo) => () => {
            activedId.value = item.id as number;
            ctx.emit("onChange", item);
        };
        const getGarbageMapPlan = async () => {
            const res = await garbageMapPlan(props.type);
            const { data, success }: { success: boolean; data: garbagePlanInfo[] } =
                res as unknown as HttpResponse<garbagePlanInfo[]>;
            if (success) {
                activedId.value = data[0].id as number;
                ctx.emit("onChange", data[0]);
                planList.value = data;
            }
        };

        onMounted(() => {
            nextTick(() => {
                getGarbageMapPlan();
            });
        });

        return () => (
            <>
                <Input
                    suffix="ios-search"
                    placeholder="请输入计划信息"
                    clearable
                    v-model={searchText.value}
                />
                <div class="plan-list-box">
                    {planList.value
                        .filter((item) => item.planName!.includes(searchText.value))
                        .map((item) => {
                            return (
                                <div
                                    class={
                                        activedId.value == item.id
                                            ? "actived plan-item"
                                            : "plan-item"
                                    }
                                    onClick={handleChoose(item)}
                                >
                                    <span
                                        class={item.planStatus == 1 ? "enable" : "disable"}
                                    ></span>
                                    {item.planName}
                                </div>
                            );
                        })}
                </div>
            </>
        );
    },
});
