import { defineComponent, onMounted, ref, nextTick } from "vue";
import { Select, Option } from "view-ui-plus";
import { garbagePointList } from "@/api/livableManage/foodWasteManagementService";
import { ComponentInfo } from "@/api/manholeCoverService";
// 收运点下拉
export default defineComponent({
    name: "collect-car-select",
    props: {
        type: {
            type: [String, Number],
            default: 1,
        },
        id: {
            type: [String, Number],
        },
        checked: { // 只展示当前ID的收运点
            type: Boolean,
            default: false,
        },
    },
    emits: ["update:modelValue", "initSelect", "onChange"],
    setup(props, ctx) {
        // 设备类型url
        const list = ref<ComponentInfo[]>([]);
        const gardenSoilDeviceList = async () => {
            const params = {
                type: props.type,
                id: props.id,
            };
            const res = await garbagePointList(params);
            const { data, success }: { success: boolean; data: ComponentInfo[] } =
                res as unknown as HttpResponse<ComponentInfo[]>;
            // 成功
            if (success) {
                list.value = data;
            }
        };
        const handleSelect = (val: any) => {
            const data = list.value.filter((k) => k.id == val)[0];
            ctx.emit("update:modelValue", val || "");
            ctx.emit("onChange", data || {});
        };
        const selectVal = ref("");
        onMounted(() => {
            nextTick(() => {
                gardenSoilDeviceList();
            });
        });

        return () => (
            <Select
                v-model={selectVal.value}
                onOnChange={handleSelect}
                clearable
                filterable
                placeholder="请选择收运点"
            >
                {list.value
                    .filter((k) => !props.checked || k.checked)
                    .map((item, index) => (
                        <Option value={item.id} label={item.name}>
                            {item.name}
                        </Option>
                    ))}
            </Select>
        );
    },
});
