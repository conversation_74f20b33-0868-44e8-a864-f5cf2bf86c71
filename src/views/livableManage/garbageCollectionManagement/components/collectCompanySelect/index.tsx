import { defineComponent, onMounted, watch, ref, nextTick } from "vue";
import { Select, Option } from "view-ui-plus";
import { garbageCollectCompanyList } from "@/api/livableManage/foodWasteManagementService";
import { ComponentInfo } from "@/api/manholeCoverService";
// 收运公司下拉
export default defineComponent({
    name: "deviceDropDownSelect",
    props: {
        type: {
            type: [Number, String],
            default: 1,
        },
        modelValue: {
            type: [Number, String],
            default: 0,
        },
        classification: {
            type: String,
            default: "",
        },
        placeholder:{
            type: String,
            default: "请选择",
        }
    },
    emits: ["update:modelValue", "initSelect", "onChange"],
    setup(props, ctx) {
        // 设备类型url
        const list = ref<ComponentInfo[]>([]);
        const gardenSoilDeviceList = async () => {
            const params = {
                page: {
                    current: 1,
                    size: -1,
                },
                customQueryParams: {
                    type: props.type,
                },
            };
            const res = await garbageCollectCompanyList(params);
            const { data, success }: { success: boolean; data: recordsResponse<ComponentInfo[]> } =
                res as unknown as HttpResponse<recordsResponse<ComponentInfo[]>>;
            // 成功
            if (success) {
                list.value = data.records;
            }
        };
        const handleSelect = (val: any) => {
            const data = list.value.filter((k) => k.id == val)[0];
            ctx.emit("update:modelValue", val ? +val : "");
            ctx.emit("onChange", data || {});
        };
        const selectVal = ref();
        watch(
            () => props.modelValue,
            () => {
                console.log("props.modelValue", props.modelValue);
                selectVal.value = props.modelValue + "";
            },
            { immediate: true }
        );
        onMounted(() => {
            nextTick(() => {
                gardenSoilDeviceList();
            });
        });

        return () =>
            props.classification != "label" ? (
                <Select v-model={selectVal.value} onOnChange={handleSelect} clearable filterable placeholder={props.placeholder}>
                    {list.value.map((item, index) => (
                        <Option value={`${item.id}`} label={item.name}>
                            {item.name}
                        </Option>
                    ))}
                </Select>
            ) : (
                <>{list.value.filter((item) => `${item.id}` == selectVal.value)[0]?.name}</>
            );
    },
});
