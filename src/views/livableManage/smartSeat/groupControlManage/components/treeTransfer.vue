<template>
  <div class="ivu-transfer-list ivu-transfer-list-with-footer">
    <div class="ivu-transfer-list-header">
      <Checkbox v-model="unselectedAll" :disabled="!unselectedData.length" @on-change="clickUnSelectedAll" /><span
        class="ivu-transfer-list-header-title">{{
          titles[0] }}</span><span class="ivu-transfer-list-header-count">{{
            isTree?unselectedData.length:( data.length - defaultTargetKeys.length)
  }}</span>
    </div>
    <div class="ivu-transfer-list-body ivu-transfer-list-body-with-search ivu-transfer-list-body-with-footer">
      <div class="ivu-transfer-list-body-search-wrapper">
        <div class="ivu-transfer-list-search">
          <div class="ivu-input-wrapper ivu-input-wrapper-small ivu-input-type-text">
<!----><i
              class="ivu-icon ivu-icon-ios-search ivu-input-icon ivu-input-icon-normal"></i><!---->
            <input @change="searchFilter($event, 'unselected')" autocomplete="off" spellcheck="false" type="text"
              class="ivu-input ivu-input-small search-input" placeholder="请输入搜索内容" number="false"><!---->
          </div>
        </div>
      </div>
      <ul class="ivu-transfer-list-content">
        <Tree :data="unselectedData" @on-check-change="selectedDataChange" show-checkbox class="tree-box" :check-strictly="true">
        </Tree>
        <li class="ivu-transfer-list-content-not-found">列表为空</li>
      </ul>
    </div>
    <div class="ivu-transfer-list-footer">
      <div class="transfer-footer">
<button @click="reset" class="ivu-btn ivu-btn-grey ivu-btn-small"
          type="button">
<span></span>重置
</button>
</div>
    </div>
  </div>
  <div class="ivu-transfer-operation">
    <Button @click="toLeft" :disabled="!unselected.length" class="ivu-btn ivu-btn-primary ivu-btn-small"
      type="button">
<span></span><i class="ivu-icon ivu-icon-ios-arrow-back"></i>
</Button>
    <Button @click="toRight" :disabled="!selected.length" class="ivu-btn ivu-btn-primary ivu-btn-small"
      type="button">
<span></span><!----><i class="ivu-icon ivu-icon-ios-arrow-forward"></i>
</Button>
  </div>

  <div class="ivu-transfer-list ivu-transfer-list-with-footer">
    <div class="ivu-transfer-list-header">
      <Checkbox v-model="computedSelectedAll" @on-change="clickSelectedAll" :disabled="selectedData.length === 0" /><span
        class="ivu-transfer-list-header-title">{{ titles[1] }}</span><span class="ivu-transfer-list-header-count">{{
          selectedData.length
        }}</span>
    </div>
    <div class="ivu-transfer-list-body ivu-transfer-list-body-with-search ivu-transfer-list-body-with-footer">
      <div class="ivu-transfer-list-body-search-wrapper">
        <div class="ivu-transfer-list-search">
          <div class="ivu-input-wrapper ivu-input-wrapper-small ivu-input-type-text">
<!----><i
              class="ivu-icon ivu-icon-ios-search ivu-input-icon ivu-input-icon-normal"></i><!---->
            <input @change="searchFilter($event, 'selected')" autocomplete="off" spellcheck="false" type="text"
              class="ivu-input ivu-input-small search-input" placeholder="请输入搜索内容" number="false"><!---->
          </div>
        </div>
      </div>
      <ul class="ivu-transfer-list-content">
        <Tree :data="selectedData" @on-check-change="unselectedDataChange" show-checkbox class="tree-box" :check-strictly="true">
        </Tree>
        <li class="ivu-transfer-list-content-not-found">列表为空</li>
      </ul>
    </div>
    <div class="ivu-transfer-list-footer">
      <div class="transfer-footer">
<button @click="reset" class="ivu-btn ivu-btn-grey ivu-btn-small"
          type="button">
<span></span>重置
</button>
</div>
    </div>
  </div>
</template>

<script>
import { Button, Checkbox } from 'view-ui-plus'
import { deepClone, dfs, isEmpty } from 'wei-util'

export default {
  name: 'TreeTransfer',
  components: { Button, Checkbox },
  props: {
    width: { default: '333px', type: String },
    height: { default: '277px', type: String },
    data: { default() { return []; } },
    isTree: { default: true },
    titles: { default() { return ['未选择', '已选择']; } },
    defaultTargetKeys: { default() { return []; } }, // 默认已选择
    type: { default: 1, type: Number }, // 新增 type 属性
  },
  data() {
    return {
      treeData: [],
      selected: [],
      initialDefaultTargetKeys: [], // Store the initial state for resetting
      unselected: [],
      unselectedAll: false,
      selectedAll: false,
      unselectedData: [],
      selectedData: [],
      copyUnselectedData: [],
      copySelectedData: [],
    };
  },
  watch: {
    data: {
      deep: true,
      immediate: true,
      handler() {
        this.observeDataChange()
      }
    },
    defaultTargetKeys: {
      deep: true,
      immediate: true,
      handler() {
        this.observeDataChange();
      }
    },
  },
  mounted() {
    // Component is mounted, capture the initial state from props.
    this.initialDefaultTargetKeys = deepClone(this.defaultTargetKeys);
  },
  computed: {
    // 计算已选择区域的全选状态
    computedSelectedAll: {
      get() {
        if (this.selectedData.length === 0) return false;
        let flag = true;
        dfs(this.selectedData, 'children', item => {
          if (!item.disabled && !item.checked) flag = false;
        });
        return flag;
      },
      set(value) {
        this.selectedAll = value;
      }
    }
  },
  methods: {
    isAllSelected() {
      let flag = true
      dfs(this.unselectedData, 'children', item => {
        if (!item.disabled && !item.checked) flag = false
      })
      return flag
    },
    findNode(data, targetId) {
      for (const item of data) {
        if (String(item.id) === String(targetId)) {
          return item;
        }
        if (item.children && item.children.length) {
          const found = this.findNode(item.children, targetId);
          if (found) return found;
        }
      }
      return null;
    },
    transformData(data, lastId) {
      const result = [];
      data.forEach((item) => {
          let disabled = false;
          if (this.type === 2) {
              // 分类分组，只允许分类节点可选，座椅节点禁用
              disabled = item.idType === 2;
          } else if (this.type === 3) {
              // 区域分组，所有节点都可以选择
              disabled = false;
          }
          const newItem = {
              ...item,
              parentId: lastId || item.id,
              id: item.id,
              expand: true,
              selected: false,
              checked: false,
              title: item.name,
              value: item.id,
              disabled,
          };
          if (item.children && Array.isArray(item.children) && item.children.length) {
              newItem.children = this.transformData(item.children, newItem.parentId);
          }
          result.push(newItem);
      });
      return result;
    },
    partitionTree(nodes, selectedKeys) {
        const selectedTree = [];
        const unselectedTree = [];

        nodes.forEach(node => {
            const nodeClone = deepClone(node);
            const isSelected = selectedKeys.some(key => String(key) === String(nodeClone.id));

            // 如果是座椅节点（idType === 2）
            if (nodeClone.idType === 2) {
                if (isSelected) {
                    selectedTree.push(nodeClone);
                } else {
                    unselectedTree.push(nodeClone);
                }
                return;
            }

            // 处理非座椅节点（父节点）
            if (nodeClone.children && nodeClone.children.length > 0) {
                const { selectedTree: selectedChildren, unselectedTree: unselectedChildren } = this.partitionTree(nodeClone.children, selectedKeys);

                // 如果有选中的子节点，将当前节点添加到已选树中
                if (selectedChildren.length > 0) {
                    const selectedParent = deepClone(nodeClone);
                    selectedParent.children = selectedChildren;
                    selectedTree.push(selectedParent);
                }

                // 如果有未选中的子节点，将当前节点添加到未选树中
                if (unselectedChildren.length > 0) {
                    const unselectedParent = deepClone(nodeClone);
                    unselectedParent.children = unselectedChildren;
                    unselectedTree.push(unselectedParent);
                }
            }
        });

        return { selectedTree, unselectedTree };
    },
    observeDataChange() {
      let treeData = deepClone(this.data);
      treeData = this.transformData(treeData);

      const { selectedTree, unselectedTree } = this.partitionTree(treeData, this.defaultTargetKeys);
      
      this.unselectedData = unselectedTree;
      this.selectedData = selectedTree;

      this.copyUnselectedData = deepClone(this.unselectedData);
      this.copySelectedData = deepClone(this.selectedData);

      // Set initial checked state for the unselected tree for UI consistency
      const setCheckedStatus = (nodes) => {
          nodes.forEach(node => {
              node.checked = false;
              if (node.children) {
                  setCheckedStatus(node.children);
              }
          });
      }
      setCheckedStatus(this.unselectedData);
    },
    // 搜索过滤
    searchFilter(e, origin) {
      const { value = '' } = e.target
      const filterDataHandle = (data, copData) => {
        const filterData = this[copData].map(item => {
          if (item.title.includes(value)) {
            return item
          } else {
            if (!isEmpty(item.children)) {
              if (item.children.some(i => i.title.includes(value))) {
                const newItem = deepClone(item)
                newItem.children = newItem.children.filter(i => i.title.includes(value))
                return newItem
              }
            }
          }
        }).filter(Boolean)
        this[data] = (isEmpty(filterData) && !value) ? this[copData] : filterData
      }
      if (origin === 'unselected') {
        filterDataHandle('unselectedData', 'copyUnselectedData')
      } else {
        filterDataHandle('selectedData', 'copySelectedData')
      }

    },
    // 重置
    reset() {
      // 触发重置事件，具体逻辑由父组件处理
      this.$emit('on-reset');
    },
    clickSelectedAll(flag) {
      this.selectedAll = flag;
      dfs(this.selectedData, 'children', item => {
        if (!item.disabled) {
          this.unselected.push(item)
          item.checked = flag
        }
      })
    },
    clickUnSelectedAll(flag) {
      this.unselectedAll = flag;
      dfs(this.unselectedData, 'children', item => {
        if (!item.disabled) {
          this.selected.push(item)
          item.checked = flag
        }
      })
    },
    toLeft() {
      const idsToMove = this.unselected.map(String);
      if (!idsToMove.length) return;

      const nodesToMove = [];
      const collectNodesFromTree = (nodes, ids) => {
          nodes.forEach(node => {
              if (ids.includes(String(node.id))) {
                  nodesToMove.push(deepClone(node));
              }
              if (node.children && node.children.length) {
                  collectNodesFromTree(node.children, ids);
              }
          });
      };

      collectNodesFromTree(this.selectedData, idsToMove);

      this.$emit('to-left', nodesToMove);

      this.unselected = [];
    },
    toRight() {
      // console.log('toRight - this.selected:', this.selected);
      const nodeObjects = [];
      const processedIds = new Set();

      if (this.type === 3) {
        // 区域分组：收集所有被勾选的节点
        const findCheckedNodes = (nodes) => {
          nodes.forEach(node => {
            if (node.checked) {
              if (!processedIds.has(node.id)) {
                nodeObjects.push(node);
                processedIds.add(node.id);
              }
            }
            if (node.children && node.children.length > 0) {
              findCheckedNodes(node.children);
            }
          });
        };
        findCheckedNodes(this.unselectedData);
      } else {
        // 其他分组模式的处理逻辑
        const availableSelected = this.selected.filter(id => {
          const node = this.findNode(this.unselectedData, id);
          return node && !node.disabled;
        });

        availableSelected.forEach(id => {
          const node = this.findNode(this.unselectedData, id);
          if (node) {
            if (this.type === 2 && node.children && node.children.length > 0) {
              const addChildrenRecursively = (children) => {
                children.forEach(child => {
                  if (!processedIds.has(child.id)) {
                    nodeObjects.push(child);
                    processedIds.add(child.id);
                  }
                  if (child.children && child.children.length > 0) {
                    addChildrenRecursively(child.children);
                  }
                });
              };
              addChildrenRecursively(node.children);
            } else {
              if (!processedIds.has(node.id)) {
                nodeObjects.push(node);
                processedIds.add(node.id);
              }
            }
          }
        });
      }

      // console.log('toRight - emitting nodeObjects:', nodeObjects);
      this.$emit('to-right', nodeObjects);
      this.selected = [];
      this.unselectedAll = false;
    },
    // 获取所有子节点id（递归）
    getAllChildrenIds(node) {
      let ids = [];
      if (node.children && node.children.length) {
        node.children.forEach(child => {
          if (this.type === 3) {
            // 区域分组：只获取座椅节点的ID
            if (child.idType === 2) {
              ids.push(child.id);
            }
          } else {
            // 分类分组：获取所有未禁用的子节点ID
            if (!child.disabled) ids.push(child.id);
          }
          ids = ids.concat(this.getAllChildrenIds(child));
        });
      }
      return ids;
    },
    selectedDataChange(allData, nowData) {
      // console.log('selectedDataChange - allData:', allData, 'nowData:', nowData);
      this.unselectedAll = this.isAllSelected();
      
      // 如果 allData 是对象数组，提取ID；如果是ID数组，直接使用
      if (allData.length > 0 && typeof allData[0] === 'object' && allData[0].id !== undefined) {
        this.selected = allData.map(item => item.id);
      } else {
        this.selected = allData;
      }
      
      // 分类分组时，勾选父节点自动全选/全不选其下所有子节点
      if (this.type === 2) {
        // 找到本次操作的节点 - 修复 nowData 参数处理
        let changedNode = null;
        if (nowData && nowData.length > 0) {
          changedNode = nowData[nowData.length - 1];
        } else if (nowData && typeof nowData === 'object' && nowData.id) {
          // 如果 nowData 是单个对象而不是数组
          changedNode = nowData;
        }
        
        // console.log('selectedDataChange - changedNode:', changedNode);
        if (changedNode && changedNode.children && changedNode.children.length) {
          const childrenIds = this.getAllChildrenIds(changedNode);
          // console.log('selectedDataChange - childrenIds:', childrenIds);
          if (changedNode.checked) {
            // 勾选父节点时，子节点全部加入（包括被禁用的子节点）
            const availableChildrenIds = childrenIds.filter(id => {
              const findNode = (data, targetId) => {
                for (let item of data) {
                  if (String(item.id) === String(targetId)) {
                    return item;
                  }
                  if (item.children && item.children.length) {
                    const found = findNode(item.children, targetId);
                    if (found) return found;
                  }
                }
                return null;
              };
              
              const node = findNode(this.unselectedData, id);
              // 在分类分组中，添加所有子节点，包括被禁用的
              return node;
            });
            // console.log('selectedDataChange - availableChildrenIds:', availableChildrenIds);
            this.selected = Array.from(new Set([...this.selected, ...availableChildrenIds]));
          } else {
            // 取消父节点时，子节点全部移除
            this.selected = this.selected.filter(id => !childrenIds.some(childId => String(childId) === String(id)));
          }
        }
      } else if (this.type === 3) {
        // 区域分组：勾选父节点自动全选/全不选其下所有子节点
        const changedNode = nowData;
        // console.log('区域分组 - selectedDataChange - changedNode:', changedNode);

        if (changedNode) {
          const isChecked = changedNode.checked;

          const updateCheckedState = (nodes, targetId, checked) => {
            return nodes.map(node => {
              const newNode = { ...node };
              if (newNode.id === targetId) {
                newNode.checked = checked;
                if (newNode.children && newNode.children.length > 0) {
                  const setChildrenChecked = (children, newChecked) => {
                    return children.map(child => {
                      const newChild = { ...child, checked: newChecked };
                      if (newChild.children && newChild.children.length > 0) {
                        newChild.children = setChildrenChecked(newChild.children, newChecked);
                      }
                      return newChild;
                    });
                  };
                  newNode.children = setChildrenChecked(newNode.children, checked);
                }
              } else if (newNode.children && newNode.children.length > 0) {
                newNode.children = updateCheckedState(newNode.children, targetId, checked);
              }
              return newNode;
            });
          };
          
          this.unselectedData = updateCheckedState(this.unselectedData, changedNode.id, isChecked);

          const newSelectedIds = [];
          const collectCheckedIds = (nodes) => {
            nodes.forEach(node => {
              if (node.checked) {
                newSelectedIds.push(node.id);
              }
              if (node.children) {
                collectCheckedIds(node.children);
              }
            });
          };
          
          collectCheckedIds(this.unselectedData);
          this.selected = newSelectedIds;
        } else {
           this.selected = allData.map(item => item.id);
        }
      }
      // console.log('selectedDataChange - final selected:', this.selected);
    },
    unselectedDataChange(allData, nowData) {
      const changedNode = nowData;
      if (changedNode) {
          const isChecked = changedNode.checked;
          const updateCheckedState = (nodes, targetId, checked) => {
              return nodes.map(node => {
                  const newNode = { ...node };
                  if (newNode.id === targetId) {
                      newNode.checked = checked;
                      if (newNode.children) {
                          const setChildrenChecked = (children, newChecked) => {
                              return children.map(child => {
                                  const newChild = { ...child, checked: newChecked };
                                  if (newChild.children) {
                                      newChild.children = setChildrenChecked(newChild.children, newChecked);
                                  }
                                  return newChild;
                              });
                          };
                          newNode.children = setChildrenChecked(newNode.children, checked);
                      }
                  } else if (newNode.children) {
                      newNode.children = updateCheckedState(newNode.children, targetId, checked);
                  }
                  return newNode;
              });
          };
          this.selectedData = updateCheckedState(this.selectedData, changedNode.id, isChecked);
      }
      
      const newUnselectedIds = [];
      const collectCheckedIds = (nodes) => {
          nodes.forEach(node => {
              if (node.checked) {
                  newUnselectedIds.push(node.id);
              }
              if (node.children) {
                  collectCheckedIds(node.children);
              }
          });
      };
      collectCheckedIds(this.selectedData);
      this.unselected = newUnselectedIds;
      
      // console.log('unselectedDataChange - final unselected:', this.unselected);
    },
    refresh() {
      this.targetKeys = this.defaultTargetKeys;
      this.$emit('on-reset', this.targetKeys);
    },
    handleChange(keys, direction, moveKeys) {
      this.targetKeys = keys;
      this.$emit('on-change', keys, direction, moveKeys);
    }
  }
}
</script>

<style lang="less" scoped>
.ivu-transfer-list {
  width: v-bind(width);
  height: v-bind(height);
}

.search-input {
  z-index: 100;
}
</style>
