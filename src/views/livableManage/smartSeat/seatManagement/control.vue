<template>
    <Modal
        v-model="showFlag"
        title="批量控制"
        :width="550"
        :mask-closable="false"
        @on-visible-change="visibleChange"
    >
    <Form :model="s" :label-width="120" :rules="rules" ref="formRef">
            <FormItem label="无线充电状态" prop="wirelessChargeStatus">
                <RadioGroup v-model="s.wirelessChargeStatus">
                    <Radio label="1">开启</Radio>
                    <Radio label="0">关闭</Radio>
                </RadioGroup>
            </FormItem>
            <FormItem label="USB状态" prop="usbStatus">
                <RadioGroup v-model="s.usbStatus">
                    <Radio label="1">开启</Radio>
                    <Radio label="0">关闭</Radio>
                </RadioGroup>
            </FormItem>
            <FormItem label="蓝牙音响状态" prop="bluetoothAudioStatus">
                <RadioGroup v-model="s.bluetoothAudioStatus">
                    <Radio label="1">开启</Radio>
                    <Radio label="0">关闭</Radio>
                </RadioGroup>
            </FormItem>
            <FormItem label="灯光一状态" prop="light1Status">
                <RadioGroup v-model="s.light1Status">
                    <Radio label="1">启用</Radio>
                    <Radio label="0">停用</Radio>
                </RadioGroup>
            </FormItem>
            <FormItem label="灯光二状态" prop="light2Status">
                <RadioGroup v-model="s.light2Status">
                    <Radio label="1">启用</Radio>
                    <Radio label="0">停用</Radio>
                </RadioGroup>
            </FormItem>
            <FormItem label="加热状态" prop="heatStatus">
                <RadioGroup v-model="s.heatStatus">
                    <Radio label="1">启用</Radio>
                    <Radio label="0">停用</Radio>
                </RadioGroup>
            </FormItem>
            <FormItem label="制冷状态" prop="refrigerationStatus">
                <RadioGroup v-model="s.refrigerationStatus">
                    <Radio label="1">启用</Radio>
                    <Radio label="0">停用</Radio>
                </RadioGroup>
            </FormItem>
        </Form>
        <template #footer>
            <div class="btn-box">
                <Button @click="closeModal">取消</Button>
                <Button type="primary" @click="confirm" :loading="s.loading">确定</Button>
            </div>
        </template>
    </Modal>
</template>

<script>
export default {
    name: 'Control',
    props: {
        show: { default: false },
        seatIds: {
            type: Array,
            default: () => []
        },
        url: {
            type: String,
            default: '',
        }
    },
    data() {
        return {
            s: {
                wirelessChargeStatus: '',
                usbStatus: '',
                bluetoothAudioStatus: '',
                light1Status: '',
                light2Status: '',
                heatStatus: '',
                refrigerationStatus: '',
                loading: false
            },
            formRef: null,
            // 定义校验规则
            rules: {
                wirelessChargeStatus: [
                    { required: true, message: '请选择无线充电状态', trigger: 'change' }
                ],
                usbStatus: [
                    { required: true, message: '请选择 USB 状态', trigger: 'change' }
                ],
                bluetoothAudioStatus: [
                    { required: true, message: '请选择蓝牙音响状态', trigger: 'change' }
                ],
                light1Status: [
                    { required: true, message: '请选择灯光一状态', trigger: 'change' }
                ],
                light2Status: [
                    { required: true, message: '请选择灯光二状态', trigger: 'change' }
                ],
                heatStatus: [
                    { required: true, message: '请选择加热状态', trigger: 'change' }
                ],
                refrigerationStatus: [
                    { required: true, message: '请选择制冷状态', trigger: 'change' }
                ]
            },
        };
    },
    computed: {
        showFlag() {
            return !!this.show;
        },
    },
    methods: {
        confirm() {
            this.$refs.formRef.validate((valid) => {
                if (valid) {
                    this.$Modal.confirm({
                        title: '批量控制',
                        content: `确认所有设备状态是否正确？`,
                        onOk: () => {
                            this.control();
                        },
                    });
                } else {
                    this.$Message.warning('请完成所有状态选择');
                    return;
                }
            });
        },

        control() {
            let param = {
                seatIds: this.seatIds,
                controlConfig: {
                    wireless_charge_status: this.s.wirelessChargeStatus,
                    usb_status: this.s.usbStatus,
                    bluetooth_audio_status: this.s.bluetoothAudioStatus,
                    light_1_status: this.s.light1Status,
                    light_2_status: this.s.light2Status,
                    heat_status: this.s.heatStatus,
                    refrigeration_status: this.s.refrigerationStatus
                }
            };
            this.s.loading = true;
            this.$request(this.url, param, 'post')
                .then((res) => {
                    if (res.success) {
                        this.$Message.success('批量控制成功');
                        this.$emit('on-update');
                        this.closeModal();
                    }
                })
                .finally(() => {
                    this.s.loading = false;
                });
        },
        visibleChange(flag) {
            if (!flag) {
                this.closeModal();
            }
        },
        closeModal() {
            this.$refs.formRef.resetFields();
            this.$emit('on-close');
        },
    },
};
</script>

<style lang="less" scoped>
.control-main {
    padding: 0 16px;
    .ivu-radio-group-item {
        margin: 8px 16px 8px 0;
    }
}
</style>
