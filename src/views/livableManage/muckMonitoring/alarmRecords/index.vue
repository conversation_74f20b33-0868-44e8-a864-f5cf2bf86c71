<template>
    <BreadcrumbCustom>
        <BreadcrumbItem>渣土监测</BreadcrumbItem>
        <BreadcrumbItem>告警记录</BreadcrumbItem>
    </BreadcrumbCustom>
    <ContentCard title="告警记录">
        <BaseForm @handle-submit="handleSubmit" :model="searchObj" inline :label-width="90" >
            <template #formitem>
                <!-- <FormItem label="设备编号" prop="deviceCode">
                    <Input v-model="searchObj.deviceCode" :maxlength="20" placeholder="请输入" clearable></Input>
                </FormItem> -->
                <FormItem label="告警类型" prop="alarmType">
                    <select-alarm-type v-model="searchObj.alarmType" :param="{ modelId: 27, type: 2 }" />
                </FormItem>
                <!-- <FormItem label="告警等级" prop="level">
                    <Select :transfer="false" v-model="searchObj.level" clearable>
                        <Option
                            v-for="(item, index) in $enumeration.alarmGrade.filter((i: string, index: number) => index > 0)"
                            :key="index" :value="index + 1" clearable>
                            {{ item }}
                        </Option>
                    </Select>
                </FormItem> -->
                <FormItem label="车牌号" prop="licensePlateNumber">
                    <Input v-model="searchObj.licensePlateNumber" :maxlength="7" placeholder="请输入" clearable></Input>
                </FormItem>
                <FormItem label="摄像头编号" prop="deviceCodeKey">
                    <Input v-model="searchObj.deviceCodeKey" :maxlength="20" placeholder="请输入" clearable></Input>
                </FormItem>
                <FormItem label="告警时间" prop="timeRange">
                    <DatePicker v-model="searchObj.timeRange" @on-change="changeTime" type="daterange" :editable="false" format="yyyy-MM-dd" placeholder="请选择" />
                </FormItem>
                <FormItem label="告警区域" prop="areaLocation">
                    <AreaSelectTree v-model="searchObj.areaLocation" :is-alarm="true" :model-ids="[27]" />
                </FormItem>
                <!-- <FormItem label="是否推送" prop="pushStatus">
                    <Select :transfer="false" v-model="searchObj.pushStatus" clearable>
                        <Option v-for="(item, index) in $enumeration.isPush" :key="index" :value="index" clearable>
                            {{ item }}
                        </Option>
                    </Select>
                </FormItem> -->
            </template>
        </BaseForm>
        <TableContentCard :base-btn="false">
            <template #btn>
                <!-- <Button type="primary">导出</Button>
                <Button type="primary">告警设置</Button> -->
            </template>
            <baseTable :model="searchObj" ref="listCom" :url="tableUrl" :columns="tableList">
                <!-- <template #level="{ row }">
                    <div>
                        {{ $enumeration.alarmGrade[row.level] }}
                    </div>
                </template> -->
                <template #alarmCaptureUrlList="{ row }">
                    <ViewModal :data="row" title="渣土监测告警抓拍"></ViewModal>
                </template>
                <template #pushStatus="{ row }">
                    <div>
                        <pushStatus :value="row.pushStatus" />
                    </div>
                </template>
                <template #action="{ row }">
                    <LinkBtn size="small" @click="toDetail(row)">详情</LinkBtn>
                </template>
            </baseTable>
        </TableContentCard>
    </ContentCard>
    <!-- <SideDetail :show="showSideDetail" :data="detailInfo" :model-id="19" @on-cancel="closeSide"></SideDetail> -->
</template>

<script lang="ts" setup>
import { getCurrentInstance, onMounted, ref } from 'vue';
import { useRouter } from 'vue-router'
const router = useRouter()
// 通用方法引入
import Util from '@/utils'
import TableContentCard from '@/components/global/TableContentCard'
import ViewModal from '@/views/livableManage/muckMonitoring/alarmRecords/components/viewModal.vue'
// import SideDetail from '@/components/global/SideDetail'
import SelectAlarmType from '@/components/common/selectAlarmType/index.vue';
// const that: any = getCurrentInstance()?.appContext.config.globalProperties
// const { $enumeration } = that

const tableUrl = '/muckMonitoring/alarmManagement/list'
// 表
const tableList = ref<any>([
    { title: '告警区域', key: 'areaPath', tooltip: true },
    { title: '告警类型', key: 'alarmTypeName', tooltip: true },
    { title: '车牌号', key: 'licensePlateNumber', tooltip: true },
    { title: '摄像头编号', key: 'deviceCode', tooltip: true },
    { title: '告警抓拍', slot: 'alarmCaptureUrlList', width: 180, tooltip: true },
    { title: '告警时间', key: 'alarmTime', width: 180, tooltip: true },
    { title: '是否推送', slot: 'pushStatus', tooltip: true, width: 80 },
    { title: '操作', slot: 'action', width: 80 },
    // { title: '设备名称', slot: 'sbmc', tooltip: true },
    // { title: '设备标识码', slot: 'bsm', tooltip: true, minWidth: 100 },
    // { title: '告警等级', slot: 'level', tooltip: true, width: 80 },
    // { title: '告警详情', key: 'content', tooltip: true },
])
// alarmVo
const searchObj = ref<any>({
    deviceCodeKey: '',
    alarmType: '',
    timeRange: [],
    // timeRange: [new Date(new Date().getTime() - 86400000 * 14), new Date()],
    areaLocation: '',
    licensePlateNumber: '',
    // 'level': '',
    // 'pushStatus': '',
})

const listCom = ref()

// 查询按钮操作
function handleSubmit() {

    // 设置告警时间区间
    changeTime(searchObj.value.timeRange)

    let params: any = JSON.parse(JSON.stringify(searchObj.value))
    if (searchObj.value.areaLocation) {
        const arr: Array<string> = searchObj.value.areaLocation.split('/')
        params.szjd = arr[0]
        params.szsq = arr[1]
        params.szdywg = arr[2]
        delete params.areaLocation
    }
    // 内置模块id
    listCom.value.search(params)
}
onMounted(() => {
    handleSubmit()
})

// 时间区间格式化
function changeTime(val: any[]) {
    if (val && val[0]) {
        searchObj.value.startTime = Util.formatDate(val[0], 'YYYY-MM-DD') + ' 00:00:00'
        searchObj.value.endTime = Util.formatDate(val[1], 'YYYY-MM-DD') + ' 23:59:59'
        return
    }
    searchObj.value.startTime = ''
    searchObj.value.endTime = ''
}


// 跳转详情页
function toDetail(row: any) {
    router.push({
        path: '/muckMonitoring/alarmRecordsDetail',
        query: {
            id: row.id,
            // deviceCode: row.deviceCode
        }
    })
}

// 切出详情
// const showSideDetail = ref<boolean>(false)
// const detailInfo = ref<any>(false)
// function checkDetail(row:any) {
//     if (!row.sbmc) {
//         row.sbmc = row.deviceExtendInfo?.sbmc
//     }
//     if (!row.bsm) {
//         row.bsm = row.deviceExtendInfo?.bsm
//     }
//     if (!row.areaPath) {
//         row.areaPath = row.deviceExtendInfo?.areaPath?.replace(/@/g, '/')
//     }
//     showSideDetail.value = true
//     detailInfo.value = row
// }
// function closeSide() {
//     showSideDetail.value = false
// }
</script>

<style lang="less" scoped></style>
