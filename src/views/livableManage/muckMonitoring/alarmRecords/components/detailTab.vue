<script lang="ts" setup>
import picVideoModal from './picVideoModal.vue';
import {
    defineProps,
    ref,
    nextTick,
    onMounted
} from 'vue';
import { getFirstFpsPic } from '@/utils/tool';
interface TabContext {
    name: string;
    key: string;
    icon?: string;
}
const attrs: any = defineProps({
    videoArr: {
        type: Array,
        default: () => []
    },
    picArr: {
        type: Array,
        default: () => []
    },
    carDetailArr: {
        type: Array,
        default: () => []
    },
})
const tabList = ref<Array<TabContext>>([
    {
        name: '抓拍',
        key: 'capture',
        icon: '1',
    },
    {
        name: '视频',
        key: 'video',
        icon: '1',
    },
]);
const modalTitle = ref<string>('');
const firstFpsPicList = ref<Array<string>>([]);
function handleTabChange(index: number) {
    if (index) {
        nextTick(() => {
            attrs.videoArr.forEach((item: string, index: number) => {
                getFirstFpsPic(item, (firstFpsPic: any) => {
                    firstFpsPicList.value[index] = firstFpsPic
                });
            });
            // console.log(firstFpsPicList.value, 'firstFpsPicList.value');
        });
    }
}
const picVideo = ref();
const isVideo = ref<boolean>(false);
function handleClickVideo(origin: string, index: number) {
    if (origin === 'pic') {
        modalTitle.value = '抓拍图片';
        isVideo.value = false;
    } else {
        modalTitle.value = '抓拍视频';
        isVideo.value = true;
    }
    picVideo.value.openModal(index, modalTitle.value);
}

onMounted(() => { });
</script>
<template>
    <detailCard :is-back-btn="false">
        <s-tab @handleChange="handleTabChange" :tab-list="tabList" justify="start">
            <template #capture>
                <div class="pic-container">
                    <div v-if="attrs.picArr.length == 0" style="width:100%">
                        <no-data />
                    </div>
                    <div v-else class="pic-item" @click="handleClickVideo('pic', index)"
                        v-for="(item, index) in attrs.picArr" :key="index">
                        <img :src="item" alt="" v-defaultImg />
                        <!-- <div class="text">抓拍图片</div>
                        <div class="date">2023-02-24 09:56:12</div> -->
                        <div class="img-mask">
                            <Icon type="ios-eye-outline"></Icon>
                        </div>
                    </div>
                </div>
            </template>
            <template #video>
                <div class="pic-container">
                    <div v-if="firstFpsPicList.length == 0" style="width:100%">
                        <no-data />
                    </div>
                    <div v-else class="video-item" @click="handleClickVideo('video', index)"
                        v-for="(item, index) in firstFpsPicList" :key="index">
                        <div class="video">
                            <img :src="item" alt="" />
                            <div class="mas">
                                <img class="suspend" src="./images/play-circle-fill.png" alt="" />
                            </div>
                        </div>
                        <!-- <div class="text">抓拍视频</div>
                        <div class="date">2023-02-24 09:56:12</div> -->
                    </div>
                    <!-- <div>
                        <h4>站务</h4>
                    </div> -->
                </div>
            </template>
        </s-tab>
    </detailCard>
    <picVideoModal :option="{
        title: modalTitle,
        width: 1092,
        styles: {
            position: 'absolute',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
        },
        footerHide: true,
    }" :video-arr="attrs.videoArr" :pic-arr="attrs.picArr"
        :first-fps-pic-list="firstFpsPicList" :car-detail-arr="attrs.carDetailArr" ref="picVideo" :is-video="isVideo">
    </picVideoModal>
</template>

<style lang="less" scoped>
.pic-container {
    width: 100%;
    display: flex;
    gap: 24px;
    flex-wrap: wrap;
    .pic-item,
    .video-item {
        cursor: pointer;
        position: relative;
        display: flex;
        flex-direction: column;
        width: 235px;
        height: 132px;
        &:hover{
           .img-mask{
               display: flex;
           }
        }
        .video {
            position: relative;
            height: 132px;
            .mas {
                width: 100%;
                height: 100%;
                background-color: #33333330;
                top: 0;
                position: absolute;
            }
            .suspend {
                width: 28px;
                height: 28px;
                position: absolute;
                left: 0;
                right: 0;
                top: 0;
                bottom: 0;
                margin: auto;
            }
        }

        img {
            width: 235px;
            height: 132px;
        }
        div {
            width: 100%;

            line-height: 32px;
            font-size: 14px;
        }
        .text {
            font-weight: 600;
            color: #1e2a55;
        }
        .date {
            color: #798799;
        }
    }
}
.img-mask{
    display: none;
    position: absolute;
    left: 0;
    top: 0;
    background: rgba(0,0,0,.6);
    width: 100%;
    height: 100%;
    align-items: center;
    justify-content: center;
}
.img-mask i{
    color: #fff;
    font-size: 20px;
    margin: 0 2px;
}
</style>
