<script lang="ts" setup>
import eModal from '@/components/common/modal/index.vue';
import {
    defineProps,
    defineExpose,
    ref,
    nextTick,
    watch
} from 'vue';
const props = defineProps({
    option: {
        default: () => {
            return {};
        },
    },
    isVideo: {
        default: false, // 默认不是视频播放
    },
    carDetailArr: {
        default: () => [],
    },
    picArr: {
        default: () => [],
    },
    videoArr: {
        default: () => [],
    },
    firstFpsPicList: {
        type: Array,
        default: () => [],
    },
});

const showModal = ref<boolean>(false);
const activeIndex = ref<number>(0);
function openModal(index: number, textTitle: string) {
    showModal.value = true;
    text.value = textTitle;
    bigPic.value = props.picArr[index];
    videoUrl.value = props.videoArr[index];
    activeIndex.value = index;
    // console.log(props.firstFpsPicList, 'firstFpsPicList');
    nextTick(() => {
        const scrollDom = document.querySelector('.scroll');
        scrollDom?.addEventListener(
            'wheel',
            (e) => {
                e.preventDefault();
            },
            { passive: false }
        );
    });
}
const bigPic = ref<string>('');
const text = ref<string>('');
function handleClickPic(picUrl: string, index: number) {
    bigPic.value = picUrl;
    activeIndex.value = index;
}
const videoUrl = ref<string>('');
function handleClickVideo(video: string, index: number) {
    videoUrl.value = video;
    activeIndex.value = index;
}
const transform = [0, 190, 380, 570, 760];
let counts = ref<number>(1);
var manyPic = ref<number>(props.picArr.length || props.videoArr.length);
var remainder = manyPic.value % 5;
var maxPage = ref<number>(1);
var fillPage = Math.floor(manyPic.value / 5);
maxPage.value = Math.ceil(manyPic.value / 5);
function handlePage(count: number) {
    const scrollDom: any = document.querySelector('.scroll');
    counts.value += count;
    if (counts.value <= fillPage + 1) {
        scrollDom.scrollTo({
            left: 950 * (counts.value - 1) - 8,
            behavior: 'smooth',
        });
    } else {
        scrollDom.scrollTo({
            left: 950 * (counts.value - 1) + transform[remainder - 1] - 8,
            behavior: 'smooth',
        });
    }
    // console.log(maxPage.value, counts.value, 'zuidazuixiao');
}
defineExpose({
    openModal,
});
watch(
    () => props.picArr,
    (newVal) => {
        manyPic.value = newVal.length || props.videoArr.length;
        remainder = manyPic.value % 5;
        maxPage.value = Math.ceil(manyPic.value / 5);
        fillPage = Math.floor(manyPic.value / 5);
        counts.value = 1;
    }
);
</script>
<template>
    <eModal class="modal" :option="props.option" @on-cancel="showModal = false" :is-show="showModal">
        <div class="view-container">
            <img v-defaultImg v-if="!isVideo" class="img" :src="bigPic" alt="" />
            <video v-else class="img" controls>
                <source :src="videoUrl" type="video/mp4" />
            </video>
            <div class="detail">
                <s-label label="车辆号码" :value="carDetailArr[0]"></s-label>
                <s-label label="车辆类型" :value="carDetailArr[1]"></s-label>
            </div>
        </div>
        <div class="title-date">
            <span>{{ text }}</span>
            <!-- <span>2023-02-24 09:09:58</span> -->
        </div>
        <div class="bottom">
            <Button size="small" :style="{ visibility: (counts !== 1 && 'visible') || 'hidden' }" class="arrow"
                @click="handlePage(-1)">
                <Icon type="ios-arrow-back" />
            </Button>
            <div class="scroll">
                <div v-if="!isVideo" class="view-list">
                    <!-- :data-count="index + 1" -->
                    <div @click="handleClickPic(item, index)" :class="['item', activeIndex === index ? 'active' : '']"
                        v-for="(item, index) in props.picArr" :key="index">
                        <img :src="item" alt="" />
                    </div>
                </div>
                <div v-else class="view-list">
                    <!-- :data-count="index + 1" -->
                    <div :class="['item', activeIndex === index ? 'active' : '']"
                        @click="handleClickVideo(props.videoArr[index], index)"
                        v-for="(item, index) in props.firstFpsPicList" :key="index">
                        <img class="suspend" src="./images/play-circle-fill.png" />
                        <img :src="item" alt="" v-defaultImg />
                    </div>
                </div>
            </div>
            <Button :style="{ visibility: (counts < maxPage && 'visible') || 'hidden' }" class="arrow"
                @click="handlePage(1)" size="small">
                <Icon type="ios-arrow-forward" />
            </Button>
        </div>
    </eModal>
</template>

<style lang="less" scoped>
.modal {
    .view-container {
        display: flex;
        column-gap: 16px;

        .img {
            width: 808px;
            height: 450px;
        }

        .detail {
            width: 204px;
            height: 450px;
            background: #f3f7fb;

            .s-label {
                flex-direction: column;
                margin-left: 16px;
            }

            .s-label:first-child {
                margin-top: 20px;
            }
        }
    }

    .title-date {
        height: 30px;
        display: flex;
        flex-direction: column;
        justify-content: center;

        span:first-child {
            color: #1e2a55;
            font-size: 14px;
        }

        span:last-child {
            color: #798799;
            font-size: 14px;
        }
    }

    .bottom {
        display: flex;
        flex-direction: row;
        column-gap: 16px;
        align-items: center;
        position: relative;

        &::after {
            content: "";
            display: block;
            position: absolute;
            bottom: 0;
            left: 0;
            height: 10px;
            width: 100%;
            z-index: 100;
            background-color: transparent;
        }

        .arrow {
            background: #f8fafb;
            color: #c2c6ce;
            padding: 0 1px;
            height: 40px;
            display: flex;
            justify-content: center;
            align-items: center;
            cursor: pointer;
            border-radius: 4px;
            width: 24px;

            .ivu-icon {
                font-size: 20px;
                width: 24px;
                color: @primary-color;
            }

            &:hover {
                border-color: #c2c6ce;
            }
        }

        .scroll {
            margin-top: 10px;
            overflow: scroll;
            overflow-x: hidden;
            height: 130px;
            cursor: pointer;
            width: 100%;
            display: flex;
            align-items: center;
            content-visibility: auto;

            .view-list {
                width: 100%;
                display: flex;
                column-gap: 16px;
                height: 100px;

                img {
                    width: 174px;
                    height: 100%;
                }

                .item {
                    position: relative;
                    // &::after {
                    //     content: attr(data-count);
                    //     display: block;
                    //     position: absolute;
                    //     right: -7px;
                    //     top: 0px;
                    //     min-width: 20px;
                    //     background: #165dff;
                    //     height: 20px;
                    //     border: 2px solid #ffffff;
                    //     border-radius: 20px;
                    //     color: #ffffff;
                    //     font-size: 12px;
                    //     text-align: center;
                    //     line-height: 17px;
                    //     padding: 0 5px;
                    // }

                    .suspend {
                        width: 28px;
                        height: 28px;
                        position: absolute;
                        left: 0;
                        right: 0;
                        top: 0;
                        bottom: 0;
                        margin: auto;
                    }
                }

                .item:first-child {
                    margin-left: 8px;
                }

                .active {
                    box-shadow: -2px 1px 13px rgba(83, 117, 167, 0.2);
                    scale: 1.1;
                    border: 2px solid @primary-color;
                }
            }
        }
    }
}
</style>
