
/deep/.ivu-card-body {
    .face-plate-cont {
        justify-content: flex-end!important;
        .online-overview {
            height: auto!important;
            min-height: 50px;
            margin-bottom: 16px;
        }
        .right-tab-title {
            justify-content:center;
            .tab-item.actived {
                background: none;
                color:#4E627E;
            }
        }
        .progress-bar-cont {
            flex:1;
            display: flex;
            align-items: flex-end;
            margin: 0 0 16px 16px;
            .progress-box {
                height:30px;
                display: flex;
                width: 100%;
                position: relative;
                pointer-events: auto;
                align-items: center;
                column-gap: 8px;
                .progress-stop-box {
                    height:30px;
                    width: 30px;
                    border-radius: 100%;
                    background: rgba(29, 33, 41, 0.4);
                    color: #fff;
                    font-size: 18px;
                    font-weight: bold;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    cursor: pointer;
                    /deep/.icon-play-arrow-fill {
                        font-size: 20px;
                    }
                }
                .progress-bar {
                    width: ~'calc(100% - 40px)';
                    background: rgba(29, 33, 41, 0.4);
                    height: 2px;
                    position: relative;
                    // top: 50%;
                    // left: 40px;
                    // right: 0;
                    .time-box {
                        position: absolute;
                        width: 100%;
                        display: flex;
                        justify-content: space-between;
                        span {
                            font-size: 12px;
                            &:first-child {
                                transform: translateX(-30%)
                            }
                            &:last-child {
                                transform: translateX(50%)
                            }
                        }
                    }
                    .progress-bar-inner {
                        width: 0%;
                        height: 2px;
                        position: relative;
                        // transition:width 2s;
                        background: linear-gradient(270deg, rgba(64, 134, 255, 1) 0%, rgba(255, 255, 255, 0.1) 93.1%);
                        .point {
                            cursor: pointer;
                            content:'';
                            height: 10px;
                            position: absolute;
                            width: 10px;
                            border-radius: 100%;
                            border: 2px solid #fff;
                            right: 0;
                            top: 50%;
                            transform: translate(0 , -50%);
                            background: rgba(64, 134, 255, 0.8);
                            &:hover {
                                width: 16px;
                                height: 16px;
                            }
                        }
                    }
                    .current-day {
                        position: absolute;
                        top: -28px;
                        transform: translateX(-50%);
                        background: rgba(29, 33, 41, 0.4);
                        white-space:nowrap;
                        padding: 0 4px;
                        color: #fff;
                        border-radius: 4px;
                        // transition:left 2s;
                    }
                }
            }
        }
        .bridge-total-num {
            display: flex;
            padding: 0px 8px 8px 0px;
            align-items: center;
            .img-box {
                height:42px;
                width: 42px;
                background: #F6F7FB;
                border-radius: 100%;
                display: flex;
                justify-content: center;
                align-items: center;
                margin-right: 20px;
            }
            img {
                display: block;
                width: 28px;
                height: 28px;


            }
            .bridge-num {
                color: @text-color;
                font-weight: 400;
                font-size: 16px;
                line-height: 24px;
                margin-bottom: 16px;

                .num {
                    color: @title-color;
                    font-weight: 700;
                    font-size: 24px;
                    line-height: 32px;
                }
            }
        }
        .bridge-gather {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            column-gap: 8px;
            line-height: 22px;
            color: @text-color;
            padding-bottom: 8px;
            position: relative;
            white-space: nowrap;
            height: 90px;
            .icon-box {
                display: flex;
                column-gap: 2px;
                align-items: center;
                padding-top: 15px;
            }
            img  {
                display: block;
                height: 22px;
                margin-right: 4px;
            }
            .gather-num {
                display: flex;
                align-items: flex-end;
                font-weight: 500;
                font-size: 24px;
                line-height: 19px;
                color: @title-color;
                margin: 8px 0px;
                padding-left: 26px;
                span {
                    font-weight: 400;
                    font-size: 12px;
                    line-height: 14px;
                    color: @input-placeholder-color ;
                    margin-left: 5px;
                }
            }
            .percent-box {
                align-items: center;
                height: 20px;
                font-size: 12px;
                .percent-num {
                    margin-left: 8px;
                }
            }
        }
    }
}


