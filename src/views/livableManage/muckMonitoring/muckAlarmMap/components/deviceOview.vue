<template>
    <!-- <div class="bridge-total-num">
        <div class="img-box"><img src="../images/icon_muck_001.png" /></div>
        <div class="bridge-num">
            今日告警总数
            <div class="num">
                {{ overviewData.todayAlarmNumbers }}
            </div>
        </div>
    </div> -->
    <div class="bridge-gather">
        <div>
            <div class="icon-box"><img src="@/assets/images/icon_date_today.png" />今日告警</div>
            <div class="gather-num">
                {{ overviewData.todayAlarmNumbers }}
            </div>
        </div>
        <div>
            <div class="icon-box"><img src="@/assets/images/icon_date_week.png" />本周告警</div>
            <div class="gather-num">
                {{ overviewData.weekAlarmNumbers }}
            </div>
        </div>
        <div>
            <div class="icon-box"><img src="@/assets/images/icon_date_month.png" />本月告警</div>
            <div class="gather-num">
                {{ overviewData.mouthAlarmNumbers }}
            </div>
        </div>

    </div>
    <div class="plate-tit">
        <img src="../images/icon_muck_004.png" class="title-icon" />
        告警摄像头
    </div>
    <div class="online-overview">
        <rankList :list="alarmCameraList" />
    </div>
    <div class="plate-tit">
        <img src="../images/icon_manholecover_010.png" class="title-icon" />
        最新告警
    </div>
    <div class="alarm-overview">
        <div class="alarm-list">
            <div class="alarm-item" v-for="(item, index) in mapAlarmList || []" :key="index">
                <tooltip-auto-show>{{ item.sbmc }}</tooltip-auto-show>
                <tooltip-auto-show>{{ item.content }}</tooltip-auto-show>
                <span>{{ item.alarmTime }}</span>
            </div>
        </div>
    </div>
</template>
<script lang="ts">
import { defineComponent, ref, onMounted } from 'vue'
import { ManholeCoverAlarm } from '@/api/manholeCoverService';
import { monitorService } from '@/api/livableManage/monitorService'
import EchartItem from '@/components/common/EchartItem/index'
import rankList from '@/components/common/rankList/index.vue'
export default defineComponent({
    components: {
        EchartItem,
        rankList
    },
    setup() {
        interface AlarmMapStatistics {
            top10: any[],
            latest5: any[],
            mouthAlarmNumbers: number,
            todayAlarmNumbers: number,
            weekAlarmNumbers: number,
        }
        // *********************
        // 在线统计
        // *********************
        // 在线统计
        const alarmCameraList = ref()
        const overviewData = ref({
            mouthAlarmNumbers: 0,
            todayAlarmNumbers: 0,
            weekAlarmNumbers: 0,
        })


        // *********************
        // 在线情况柱状图
        // *********************

        // 5条告警列表
        const mapAlarmList = ref<ManholeCoverAlarm[]>([])

        // 告警数据
        const getMapAlarms = async () => {

            const res = await monitorService.muckAlarmMapStatistics()
            const { data, success }: { data: AlarmMapStatistics, success: boolean } = res as unknown as HttpResponse<AlarmMapStatistics>
            if (success) {
                const { top10 } = data;
                // const total = top10.reduce((total, item) => {
                //     return total + item.alarmNumbers
                // }, 0)
                alarmCameraList.value = top10.map(k => {
                    return {
                        name: k.sbmc,
                        value: k.alarmNumbers,
                        percent: top10[0].alarmNumbers ? (k.alarmNumbers / top10[0].alarmNumbers) * 100 : 0
                    }
                });
                mapAlarmList.value = data.latest5;
                overviewData.value = {
                    mouthAlarmNumbers: data.mouthAlarmNumbers,
                    todayAlarmNumbers: data.todayAlarmNumbers,
                    weekAlarmNumbers: data.weekAlarmNumbers,
                }
            }
        }
        onMounted(() => {
            getMapAlarms()
        })
        return {
            mapAlarmList,
            overviewData,
            alarmCameraList
        }
    }
})
</script>
<style lang="less" scoped>
/deep/.ivu-tooltip {
    height: 40px;

    .ivu-tooltip-rel {
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        line-height: 40px;
        width: 100%;
    }
}
</style>
