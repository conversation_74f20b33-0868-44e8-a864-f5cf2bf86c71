<template>
    <div>
        <BreadcrumbCustom>
            <BreadcrumbItem>渣土监测</BreadcrumbItem>
            <BreadcrumbItem>告警地图</BreadcrumbItem>
        </BreadcrumbCustom>
        <Card dis-hover :bordered="false" class="map-cont" id="fullscreen">
            <div class="face-plate-cont">
                <div class="progress-bar-cont">
                    <div class="progress-box">
                        <div class="progress-stop-box" @click="handleStop">
                            <Icon type="ios-pause" v-if="progressTimer" />
                            <Icon type="ios-pause" v-else />
                            <icon custom="iconfont icon-play-arrow-fill" v-else />
                        </div>
                        <div class="progress-bar" ref="progressBarRef">
                            <div class="current-day" :style="{ left: progressVal }">{{ dayList?.[progressDay - 1] }}</div>
                            <div class="progress-bar-inner" :style="{ width: progressVal }" ref="progressBarInnerRef">
                                <div class="point" @mouseover="progressDay <= 30 && handleStop()"
                                    @mouseleave="progressDay <= 30 && handleStop()" @mousedown.stop="handleMove"></div>
                            </div>
                            <div class="time-box">
                                <span>{{ dayList?.[0]?.slice(5,10) }}</span>
                                <span>{{ dayList?.[29]?.slice(5,10) }}</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="right-plate">
                    <div class="full-op" @click="handleFullScreen">
                        <Icon :type="fullFlag ? 'md-contract' : 'md-expand'" />
                    </div>
                    <div class="device-info-cont">
                        <s-tab :tab-list="tabList" class="plate-bg" tit-class="right-tab-title"
                            :body-class="`${isToggleDropdown ? '' : 'hide-tab-box'} right-tab-body`">
                            <template #expand>
                                <div class="toggle" @click="isToggleDropdown = !isToggleDropdown">
                                    <Icon :type="isToggleDropdown ? 'ios-arrow-down' : 'ios-arrow-up'" />
                                </div>
                            </template>
                            <template #overview>
                                <div class="scorll-map-cont manhole-cover-right">
                                    <device-oview />
                                </div>
                            </template>
                        </s-tab>
                    </div>
                </div>
            </div>
            <div id="alarm-container" class="container-map" :class="fullFlag ? 'full-map' : ''"></div>
        </Card>
    </div>
</template>

<script lang="ts">
import AMapLoader from '@amap/amap-jsapi-loader';
import { defineComponent, nextTick, onMounted, onUnmounted, ref, Ref, watch } from 'vue';
import DeviceOview from './components/deviceOview.vue';
import { launchIntoFullscreen, exitFullscreen } from '@/utils/tool'
import Util from '@/utils/index';
export default defineComponent({
    components: {
        DeviceOview,
    },
    setup(props, ctx) {
        // *********************
        // 地图
        // *********************

        const map = ref<any>(null);
        const Amap = ref<any>(null);
        const heatmap = ref()
        const loca = ref()
        const ininMap = () => {
            AMapLoader.load({
                key: 'ea53a5291f8f7c1f215ba20930ea9866',
                version: '2.0',
                'Loca': {
                    'version': '2.0.0'
                },
            }).then((AMap) => {
                Amap.value = AMap
                map.value = new AMap.Map('alarm-container', {
                    resizeEnable: true,
                    zoom: 14.5,
                    center: [114.610474, 30.450645],
                })
                loca.value = new (window as any).Loca.Container({
                    map: map.value,
                });
                heatmap.value = new (window as any).Loca.HeatMapLayer({
                    zIndex: 10,
                    opacity: 1,
                    visible: true,
                    zooms: [2, 22],
                });
                loca.value.add(heatmap.value);
                getHeatmapData()
                nextTick(() => {
                    setHeatMap()
                })
            })
        }

        onMounted(() => {
            ininMap();
            window.addEventListener('fullscreenchange', e => {
                // 监听到屏幕变化，在回调中判断是否已退出全屏
                const isFull = document.fullscreenElement || document['mozFullScreenElement'] || document['webkitFullscreenElement'];
                fullFlag.value = !!isFull
            })
        })

        // *********************
        // 热力图数据
        // *********************
        // 构造静态数据
        const heatmapDatas = ref<any[]>()
        const getHeatmapData = () => {
            const heatmapData = new Array(400).fill({}).map((k, i) => {
                return {
                    type: "Feature",
                    geometry: {
                        type: 'Point',
                        coordinates: [Math.random() * 0.07 + 114.58, Math.random() * 0.03 + 30.43]
                    },

                }
            })
            const others = new Array(30).fill({}).map((k, i) => {
                return {
                    type: "Feature",
                    geometry: {
                        type: 'Point',
                        coordinates: [Math.random() * 0.01 + 114.60, Math.random() * 0.01 + 30.46]
                    },

                }
            })
            const others1 = new Array(10).fill({}).map((k, i) => {
                return {
                    type: "Feature",
                    geometry: {
                        type: 'Point',
                        coordinates: [Math.random() * 0.01 + 114.59, Math.random() * 0.01 + 30.46]
                    },

                }
            })
            const others2 = new Array(20).fill({}).map((k, i) => {
                return {
                    type: "Feature",
                    geometry: {
                        type: 'Point',
                        coordinates: [Math.random() * 0.02 + 114.57, Math.random() * 0.02 + 30.43]
                    },
                }
            })
            heatmapDatas.value = [...heatmapData, ...others, ...others1, ...others2]
        }
        const setHeatMap = () => {
            heatmapDatas.value?.forEach((k, i) => {
                k.properties = {
                    count: Math.random() * 65 + 30,
                }
            })
            const geo = new (window as any).Loca.GeoJSONSource({
                data: {
                    type: 'FeatureCollection',
                    features: heatmapDatas.value,
                },
            });
            heatmap.value.setSource(geo);
            heatmap.value.setStyle(
                {
                    radius: 350,
                    unit: 'meter',
                    max: 150,
                    gradient: {
                        1: '#FF4C2F',
                        0.8: '#FAA53F',
                        0.6: '#FFF100',
                        0.5: '#7DF675',
                        0.4: '#5CE182',
                        0.2: '#29CF6F',
                    },
                    value: (index: number, feature: any) => {
                        return feature.properties.count;
                    },
                    opacity: [0, 1],
                    heightBezier: [0, 0.53, 0.37, 0.68]
                }
            );
            // heatmap.value.addAnimate({
            //     key: 'value',
            //     value: [0.9, 1],
            //     duration: 3000
            //     // transform: 1000,
            //     // random: true,
            //     // delay: 1000,
            //     // easing: 'BounceOut' //https://redmed.github.io/chito/example/easing.html
            // }, () => {
            //     console.log('动画结束')
            // });

        }

        // 地图全屏
        const fullFlag: Ref<boolean | null> = ref(false)
        const handleFullScreen = () => {
            const element = document.getElementById('fullscreen');
            if (!element) return
            // false是进入全屏状态
            if (fullFlag.value) {
                // 退出全屏
                exitFullscreen(element);
            } else {
                launchIntoFullscreen(element);
            }
            fullFlag.value = !fullFlag.value
        }

        // *********************
        // 进度条
        // *********************
        const progressBarRef = ref()
        const progressBarInnerRef = ref()
        const progressVal = ref<string>('0%');
        const progressDay = ref<number>(0);
        const dayList = ref<string[]>()
        const progressTimer = ref();
        // 开始播放
        const startProgress = () => {
            let val = progressDay.value * 100 / 30;
            if (!progressTimer.value && progressDay.value >= 30) {
                val = 0;
                progressDay.value = 0;
                progressVal.value = '0%';
            }
            progressTimer.value = setInterval(() => {
                val += 100 / 30;
                progressDay.value += 1;
                if (val >= 100) {
                    clearInterval(progressTimer.value);
                    progressTimer.value = null;
                    startProgress();
                    return;
                }
                setHeatMap();
                progressVal.value = `${val}%`;
            }, 2000);
        };
        // 操作播放
        const handleStop = () => {
            if (progressTimer.value) {
                clearInterval(progressTimer.value);
                progressTimer.value = null;
            } else {
                startProgress();
            }
        }
        onMounted(() => {
            startProgress();
            dayList.value = new Array(30).fill('').map((k, i) => {
                return Util.formatDate(new Date().getTime() - (29 - i) * 24 * 60 * 60 * 1000, 'YYYY-MM-DD')
            })
        })
        onUnmounted(() => {
            if (progressTimer.value) clearInterval(progressTimer.value)
            progressTimer.value = null;
        })
        // 是否展开右侧
        const isToggleDropdown = ref<boolean>(true);
        const checkMove = (left: number) => {
            const width = progressBarRef.value?.offsetWidth
            const w = +(progressVal.value.replace('%', '')) * width + left * 100
            if (w / width >= 100) return
            // console.log((w / width) * 0.3)
            progressDay.value = Math.round((w / width) * 0.3);
            progressVal.value = `${w / width}%`;
        }
        // 拖拽进度条
        const handleMove = (e: any) => {
            let disX = e.clientX;
            clearInterval(progressTimer.value);
            progressTimer.value = null;
            document.onmousemove = (e) => {
                let left = e.clientX - disX;
                disX = e.clientX;
                checkMove(left);
            };
            handleClear();
        }
        // 清除鼠标动作
        const handleClear = () => {
            document.onmouseup = () => {
                document.onmousemove = null;
                document.onmouseup = null;
            };
        }
        const tabList = ref([
            {
                name: '告警概览',
                key: 'overview',
                icon: 'dashboard'
            },
        ])

        return {
            progressVal,
            tabList,
            handleFullScreen,
            fullFlag,
            isToggleDropdown,
            dayList,
            progressDay,
            handleStop,
            progressTimer,
            handleMove,
            progressBarRef,
            progressBarInnerRef
        };
    }
});
</script>

<style lang="less" scoped>
@import '../../../../styles/mapPage.less';
@import './index.less';
</style>
