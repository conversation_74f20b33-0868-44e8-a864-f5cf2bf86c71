export interface IModalInfo{
    planName: string; // 收运计划
    taskName: string; // 收运任务
    taskTime: string; // 任务时间
    contactPerson: string; // 收运人
    carLicenseNo: string; // 收集车辆
    companyName: string; // 收运公司
    // 收运填报
    pointName: string; // 收运点
    enterpriseName: string; // 所属企业
    // 可编辑内容
    garbageCans?: IGarbageCan[]; // 收运数量
    time?: string; // 收运时间
}

export interface IGarbageCan{
    name: string; // 垃圾桶名称
    weight: string; // 重量
    key?: string;
}
export interface IRecord {
    collectTime: string;
    modifyId: number;
    factoryId: any;
    dataType: number;
    creatorId: number;
    factoryName: string;
    planName: string;
    signUrl: string;
    remark: any;
    carId: any;
    carLicenseNo: string;
    emission: number;
    recordTime: string;
    modifyTime: string;
    deleted: number;
    createTime: string;
    ids: any;
    planId: number;
    startTime: string;
    id: number;
    endTime: string;
}
export interface IDetailInfo {
    // 处理厂
    factoryId: string;
    // 关联收运计划
    planId: string;
    // 填报类型
    dataType: string;
	// 收运时间
    collectTime: string;
    // 排放数据
	emission: string;
    // 填报时间
	recordTime: string;
    // 现场人员签字image
	signUrl: string;
}

