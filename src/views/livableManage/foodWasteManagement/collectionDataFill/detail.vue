<template>
    <BreadcrumbCustom>
        <BreadcrumbItem>餐厨垃圾管理</BreadcrumbItem>
        <BreadcrumbItem to="/kitchenWasteManagement/dataFilling">数据填报</BreadcrumbItem>
        <BreadcrumbItem>详情</BreadcrumbItem>
    </BreadcrumbCustom>
    <!-- 查看信息 -->
    <!-- 编辑信息 -->
    <detailCard ref="detailRef" title="基础信息" :src="require('@/assets/images/icon_detail.png')" :is-back-btn="true"
                @on-submit="onSubmit" @on-back="onBack" :class="[isEdit ? 'edit-card' : '']"  :is-edit-btn="true" @on-edit="onEdit">
        <Form ref="formRef" :rules="formRules" :model="detailInfo">
            <InfoShow v-if="!isEdit" :detail-info="detailInfo" />
            <EditBox v-else v-model="detailInfo" ref="editBoxRef"/>
        </Form>
    </detailCard>
</template>
<script lang="ts" setup>
import { ref } from 'vue';
import { getRouterQuery } from '@/utils/tool'
import EditBox from "./components/editBox.vue";
import { useEdit } from './useEdit'
import {
    editGarbageData, getGarbageDataDetail
} from "@/api/livableManage/foodWasteManagementService";

import InfoShow from './components/infoShow.vue'
const id = getRouterQuery('id') as string;

const { detailInfo, formRules, isEdit, route, router } = useEdit()

const editBoxRef = ref(null)
const formRef = ref(null)
const detailRef = ref();

const onInit = async() => {
    const id = route.query.id;
    if(id){
        const res = await getGarbageDataDetail(id)
        if (res.success) {
            console.log('onInit', res.data)
            detailInfo.value = res.data
        }
    }
}
onInit();

const onSubmit = async() => {
    const validateRes = await formRef.value?.validate()
    if (validateRes) {
        const params = Object.assign({}, detailInfo.value, { type: 1 })
        const res = await editGarbageData(params);
        if (res.success) {
            isEdit.value = false;
        }
    }
}
const onEdit = (flag:boolean) => {
    isEdit.value = flag;
    detailRef.value.isEditFlag = flag
}
const onBack = () => {
    router.back()
}
</script>
