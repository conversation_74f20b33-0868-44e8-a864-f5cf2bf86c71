import { ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { getGarbageInfo } from '@/api/livableManage/foodWasteManagementService';
import { IDetailInfo } from './type/index';

const createDetailInfo = ():IDetailInfo => {
    return {
        // 处理厂
        factoryId: '',
        // 关联收运计划
        planId: '',
        // 填报类型
        dataType: '',
        // 收运时间
        collectTime: '',
        // 排放数据
        emission: '',
        // 填报时间
        recordTime: '',
        // 现场人员签字image
        signUrl: ''
    }
}
const checkRequired = (rule: any, value: number | string, callback: any) => {
    console.log('validte', { rule, value })
    if (value) {
        callback()
    } else {
        callback(new Error('请完善必填项'))
    }
}
const formRules = {
    factoryId: [{ required: true, message: '请完善必填项' }],
    planId: [{ required: true, message: '请完善必填项' }],
    dataType: [{ required: true, message: '请完善必填项' }],
    emission: [{ required: true, message: '请完善必填项' }, {
        pattern: /^(0|([1-9]\d{0,8}))(\.\d{1})?$/,
        message: '请输入最多1位小数,9位整数的数字'
    }],
    recordTime: [{ required: true, message: '请完善必填项' }],
}

export const useEdit = () => {
    const isEdit = ref(false);
    const detailInfo = ref<IDetailInfo>(createDetailInfo());
    const isLoading = ref(false);

    const router = useRouter();
    const route = useRoute();

    const onInit = async(id: string | undefined) => {
        if (isEdit) {
            console.log('onEdit', id);
            const res = await getGarbageInfo({ id })
            console.log(res);
        }
    }

    return { detailInfo, isEdit, formRules, router, route, onInit, isLoading }
}

