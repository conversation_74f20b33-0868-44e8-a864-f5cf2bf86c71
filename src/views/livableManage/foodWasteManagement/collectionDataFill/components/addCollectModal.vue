<template>
        <div>
            <Form ref="formRef" :model="detailInfo" :rules="formRules" label-position="top">
                <Row :gutter="24" justify="space-between">
                    <Col span="12">
                        <FormItem label="处理厂名称" prop="factoryId">
                            <selected-by-list url="/garbageFactory/list" v-model="detailInfo.factoryId" />
                        </FormItem>
                    </Col>
                    <Col span="12">
                        <FormItem label="关联收运计划" prop="planId">
                            <selected-by-list url="/garbagePlan/list"
                                              :params="planParams"
                                              label-name="planName"
                                              v-model="detailInfo.planId" />
                        </FormItem>
                    </Col>
                    <Col span="12">
                        <FormItem label="填报类型" prop="dataType">
                            <Select v-model="formatDataType" clearable>
                                <Option v-for="item in dataTypeOption"
                                        :value="item.value"
                                        :key="item.value">
                                    {{ item.label }}
                                </Option>
                            </Select>
                        </FormItem>
                    </Col>
                    <Col span="12">
                        <FormItem label="收运时间" prop="collectTime">
                            <DatePicker :type="collectTime" placeholder="请选择" :format="collectTimeFormatStr" v-model="formatCollectTime" clearable />
                        </FormItem>
                    </Col>
                    <Col span="12">
                        <FormItem label="排放数据" prop="emission">
                            <Input v-model="detailInfo.emission" placeholder="请输入" clearable>
                                <template #append>
                                    吨
                                </template>
                            </Input>
                        </FormItem>
                    </Col>
                    <Col span="12">
                        <FormItem label="填报时间" prop="recordTime">
                            <date-picker v-model="formatDateTime" placeholder="请选择" type="datetime" clearable></date-picker>
                        </FormItem>
                    </Col>
                </Row>
                <Row :gutter="12">
                    <Col span="24">
                        <FormItem label="现场人员签字" prop="contactPerson">
                            <UploadImage v-model="detailInfo.signUrl" max-length="1" :preview="true" />
                        </FormItem>
                    </Col>
                </Row>
            </Form>
        </div>
</template>

<script lang="ts" setup>


import { computed, defineExpose, defineProps, getCurrentInstance, ref } from 'vue';
import moment from 'moment'
import { useEdit } from '../useEdit'
import {
    editGarbageData, getGarbageDataDetail
} from '@/api/livableManage/foodWasteManagementService';
import { DatePicker } from 'view-ui-plus';
import SelectedByList from '@/components/common/selectByList';
import { dataTypeOption } from '@/views/livableManage/foodWasteManagement/collectionDataFill/enum';
import UploadImage from '@/components/common/uploadImg/index.vue';

const that = getCurrentInstance()?.appContext.config.globalProperties;
const { formRules, detailInfo } = useEdit()

const props = defineProps<{id: string | number}>()

const formRef = ref(null)

const collectTimeFormatStr = computed(() => detailInfo.value.dataType == 1 ? 'yyyy-MM-dd' : 'yyyy-MM')
const collectTime = computed(() => detailInfo.value.dataType == 1 ? 'date' : 'month')

const formatDataType = computed({
    get() {
        return detailInfo.value.dataType + '';
    },
    set(newVal) {
        detailInfo.value.dataType = newVal;
    }
})

const formatDateTime = computed({
    get() {
        if (!detailInfo.value.recordTime) {
            return ''
        } else {
            return moment(detailInfo.value.recordTime).toDate();
        }
    },
    set(newDateTime) {
        console.log('newtime', newDateTime)
        detailInfo.value.recordTime = newDateTime ? moment(newDateTime).format('YYYY-MM-DD HH:mm:ss') : '';
    }
})
const formatCollectTime = computed({
    get() {
        if (!detailInfo.value.collectTime) {
            return ''
        } else {
            return moment(detailInfo.value.collectTime).toDate();
        }
    },
    set(newDateTime) {
        console.log('newtime', newDateTime)
        detailInfo.value.collectTime = newDateTime ? moment(newDateTime).format('YYYY-MM-DD HH:mm:ss') : '';
    }
})

// 关联收运计划下拉 -- 根据垃圾处理厂id查询

const planParams = computed(() => {
    const factoryId = detailInfo.value.factoryId;
    return {
        factoryId
    }
})
const onInitModal = async(id:number | string) => {
    const res = await getGarbageDataDetail(id)
    if (res.success) {
        detailInfo.value = res.data;
    }
}
onInitModal(props.id)



// 提交
const handleConfirm = async() => {
    const isValidated = await formRef.value.validate();
    const id = props.id;

    if (isValidated && id) {
        const params = Object.assign({}, detailInfo.value, { type: 1 })
        const res = await editGarbageData(params);
        if (res.success) {
            that && that.$Message.success('编辑成功')
        }
        return res.success;
    }
}


defineExpose({ handleConfirm });

</script>

<style lang="less" scoped>
:deep(.ivu-form-item){
    width: 100% !important;
}
:deep(.ivu-form-item-label){
    padding-top: 0;
    padding-bottom: 8px;
}
.title{
    min-height: 34px;
    line-height: 22px;
    color: #798799;
    font-size: 14px;
    padding-right: 8px;
}
.trashcan-wrapper{
    width: 100%;
    background-color: #F8FAFB;
    padding: 16px;
}
.title{
    display: flex;
    .title-icon-img{
        width: 24px;
        height: 24px;
        margin-right: 8px;
        img{
            width: 100%;
            height: 100%;
        }
    }
    h5{
        margin-bottom: 0;
    }
}

</style>
