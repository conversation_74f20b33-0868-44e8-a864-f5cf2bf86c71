<template>
    <Row :gutter="80">
        <Col span="8">
            <FormItem label="处理厂名称" prop="factoryId">
                <selected-by-list url="/garbageFactory/list" v-model="form.factoryId" />
            </FormItem>
        </Col>
        <Col span="8">
            <FormItem label="关联收运计划" prop="planId">
                <selected-by-list url="/garbagePlan/list"
                                  :params="planParams"
                                  label-name="planName"
                                  v-model="form.planId" />
            </FormItem>
        </Col>
        <Col span="8">
            <FormItem label="填报类型" prop="dataType">
                <Select v-model="formatDataType" clearable>
                    <Option v-for="item in dataTypeOption"
                            :value="item.value"
                            :key="item.value">
                        {{ item.label }}
                    </Option>
                </Select>
            </FormItem>
        </Col>
        <Col span="8">
            <FormItem label="收运时间" prop="collectTime">
                <DatePicker :type="collectTime" placeholder="请选择" :format="collectTimeFormatStr" v-model="formatCollectTime" clearable />
            </FormItem>
        </Col>
        <Col span="8">
            <FormItem label="排放数据" prop="emission">
                    <Input v-model="form.emission" placeholder="请输入" clearable>
                        <template #append>
                            吨
                        </template>
                    </Input>
            </FormItem>
        </Col>
        <Col span="8">
            <FormItem label="填报时间" prop="recordTime">
                <date-picker v-model="formatDateTime" placeholder="请选择" type="datetime" clearable></date-picker>
            </FormItem>
        </Col>
        <Col span="8">
            <FormItem label="现场人员签字" prop="contactPerson">
                <UploadImage v-model="form.signUrl" max-length="1" :preview="true" />
            </FormItem>
        </Col>
    </Row>
</template>
<script lang="ts" setup>
import { computed, defineProps } from 'vue'
import { IDetailInfo } from '../type/index';
import SelectedByList from '@/components/common/selectByList';
import { dataTypeOption } from '@/views/livableManage/foodWasteManagement/collectionDataFill/enum';
import { DatePicker } from 'view-ui-plus';
import UploadImage from '@/components/common/uploadImg/index.vue'
import moment from 'moment/moment';


const props = defineProps<{modelValue: IDetailInfo}>()

const form = computed(() => props.modelValue)

const collectTimeFormatStr = computed(() => form.value.dataType == 1 ? 'yyyy-MM-dd' : 'yyyy-MM')
const collectTime = computed(() => form.value.dataType == 1 ? 'date' : 'month')

const formatDataType = computed({
    get() {
        return form.value.dataType + '';
    },
    set(newVal) {
        form.value.dataType = newVal;
    }
})

const formatDateTime = computed({
    get() {
        if (!form.value.recordTime) {
            return ''
        } else {
            return moment(form.value.recordTime).toDate();
        }
    },
    set(newDateTime) {
        console.log('newtime', newDateTime)
        form.value.recordTime = newDateTime ? moment(newDateTime).format('YYYY-MM-DD HH:mm:ss') : '';
    }
})
const formatCollectTime = computed({
    get() {
        if (!form.value.collectTime) {
            return ''
        } else {
            return moment(form.value.collectTime).toDate();
        }
    },
    set(newDateTime) {
        console.log('newtime', newDateTime)
        form.value.collectTime = newDateTime ? moment(newDateTime).format('YYYY-MM-DD HH:mm:ss') : '';
    }
})

// 关联收运计划下拉 -- 根据垃圾处理厂id查询

const planParams = computed(() => {
    const factoryId = form.value.factoryId;
    return {
        factoryId
    }
})
</script>

<style lang="less" scoped>
.show-map {
    cursor: pointer;
    margin-right: 8px;
    font-size: 16px;
    color: #165DFF;
    .ivu-icon {
        font-weight: 700;
    }
}
:deep(.ivu-cascader-rel .ivu-icon-ios-close-circle){
    font-family: "iconfont" !important;
    color: #4E627E;
    font-size: 16px;
    &:before{
        content: "\e705";
    }
}
</style>
