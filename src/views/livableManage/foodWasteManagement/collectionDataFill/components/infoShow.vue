<template>
        <Row :gutter="80">
            <Col :span="8">
                <s-label  label="处理厂名称" :value="detailInfo.factoryId" />
            </Col>
            <Col :span="8">
                <s-label label="关联收运计划" :value="detailInfo.planId" />
            </Col>
            <Col :span="8">
                <s-label label="填报类型" :value="detailInfo.dataType" />
            </Col>
            <Col :span="8">
                <s-label label="收运时间" :value="detailInfo.collectTime" />
            </Col>
            <Col :span="8">
                <s-label label="排放数据" :value="detailInfo.emission" />
            </Col>
            <Col :span="8">
                <s-label label="填报时间" :value="detailInfo.recordTime" />
            </Col>
        </Row>
    <Row>
        <Col>
            <s-label label="现场人员签字">
                <template #value>
                    <UploadImage :model-value="detailInfo.signUrl" max-length="1" :disabled="true" />
                </template>
            </s-label>
        </Col>
    </Row>
</template>
<script lang="ts" setup>
import { computed, defineProps } from 'vue';
import UploadImage from '@/components/common/uploadImg/index.vue';
import { IDetailInfo } from '@/views/livableManage/foodWasteManagement/collectionDataFill/type';

const props = defineProps<{detailInfo: IDetailInfo}>()
const detailInfo = computed<IDetailInfo>(() => props.detailInfo)

</script>
<style lang="less" scoped>
.modal-card {
    padding: 0;

    &.detail-card {
        padding: 8px 0;
    }
}

.num-box {
    background: #F8FAFB;
    flex: 1;
    display: flex;
    flex-wrap: wrap;
    padding-bottom: 16px;

    &.hide-item {
        padding-bottom: 16px;
    }

    .num-item {
        display: flex;
        align-items: center;
        column-gap: 16px;
        color: #165DFF;
        background: #E8F3FF;
        border-radius: 2px;
        padding: 0 8px;
        margin-top: 16px;
        margin-left: 16px;
        height: 24px;
    }
}
</style>
