<template>
    <BreadcrumbCustom>
        <BreadcrumbItem>餐厨垃圾管理</BreadcrumbItem>
        <BreadcrumbItem>数据填报</BreadcrumbItem>
    </BreadcrumbCustom>
    <ContentCard title="数据填报" :base-btn="false">
        <BaseForm @handle-submit="onSubmit(listRef)" :model="condition" inline :label-width="100">
            <template #formitem>
                <FormItem label="处理厂名称" prop="factoryId">
                    <select-by-list url="/garbageFactory/list"
                                    v-model="condition.factoryId" />
                </FormItem>
                <FormItem label="关联收运计划" prop="planId">
                    <select-by-list url="/garbagePlan/list"
                                    v-model="condition.planId"
                                    :params="planParams"
                                    label-name="planName" />
                </FormItem>
                <FormItem label="填报类型" prop="dataType">
                    <Select v-model="condition.dataType" clearable>
                        <Option v-for="item in dataTypeOption"
                                :value="item.value"
                                :key="item.value">
                            {{ item.label }}
                        </Option>
                    </Select>
                </FormItem>
                <FormItem label="填报时间" prop="startTime">
                    <date-picker type="datetimerange"
                                 v-model="formatFillTime"
                                 placeholder="请选择"
                                 clearable />
                </FormItem>
                <FormItem label="收运车辆" prop="carId">
                    <select-by-list url="/garbageCar/list"
                                    label-name="licenseNo"
                                    v-model="condition.carId" />
                </FormItem>
            </template>
        </BaseForm>
        <btn-card >
            <Button type="primary" @click="toAdd" icon="md-add" v-auth="'kitchenWasteManagement:dataFilling:add'">
                新增数据填报
            </Button>
            <Button icon="ios-trash" @click="onDelete" v-auth="'kitchenWasteManagement:basic:point:del'">
                删除
            </Button>
        </btn-card>
        <baseTable @on-selection-change="onSelectionChange" :model="condition" ref="listRef"
                   :columns="tableColumn" url="/garbageData/list"
        >
            <template #dataType="{row}">
                {{ dataTypeEnum[row.dataType] }}
            </template>
            <template #collectTime="{ row }">
                {{ formatCollectTime(row) }}
            </template>
            <template #recordTime="{ row }">
                {{ formatTime(row) }}
            </template>
            <template #action="{ row }">
                <LinkBtn size="small" @click="goEdit(row)">
                    编辑
                </LinkBtn>
            </template>
        </baseTable>
    </ContentCard>
    <s-modal title="编辑数据填报" @handleSubmit="onSubmitModal" :styles="{top: '18px' }" :ref-box="addModalRef" ref="modalRef" width="625">
        <AddCollectModal :id="modalId" ref="addModalRef"></AddCollectModal>
    </s-modal>
</template>

<script lang="ts" setup>


import { computed, getCurrentInstance, onMounted, ref } from 'vue';
import { useRouter } from 'vue-router';
import { delGarbageData } from '@/api/livableManage/foodWasteManagementService';
import {
    IEnterprise
} from '@/views/livableManage/foodWasteManagement/baseInfoManagement/enterpriseManagement/type';
import SelectByList from '@/components/common/selectByList';
import {
    dataTypeEnum,
    dataTypeOption
} from '@/views/livableManage/foodWasteManagement/collectionDataFill/enum';
import { DatePicker } from 'view-ui-plus';
import moment from 'moment/moment';
import {
    IRecord
} from './type/index';
import AddCollectModal
    from './components/addCollectModal.vue';
const router = useRouter();

const that = getCurrentInstance()?.appContext.config.globalProperties;


const listRef = ref(null);

const condition = ref({
    factoryId: '', // 收运计划id
    planId: '',
    dataType: '',
    startTime: '',
    endTime: '',
    carId: '',
})

const formatFillTime = computed({
    get() {
        if (!condition.value.startTime) {
            return ''
        }
        return [moment(condition.value.startTime).toDate(), moment(condition.value.endTime).toDate()]
    },
    set(newDateTimeRange) {
            console.log('newTime', newDateTimeRange)
            const [startDateTime, endDateTime] = newDateTimeRange;
            condition.value.startTime = startDateTime ? moment(startDateTime).format('YYYY-MM-DD HH:mm:ss') : '';
            condition.value.endTime = endDateTime ? moment(endDateTime).format('YYYY-MM-DD HH:mm:ss') : '';
    }
})

// 收运弹窗相关
const modalId = ref(-1) // 弹窗id
const modalPointId = ref(-1) // 弹窗pointId
const addModalRef = ref(null)
const modalRef = ref(null)

const goEdit = (row) => {
    if (row.id) {
        modalPointId.value = row.pointId;
        modalId.value = row.id;
        modalRef.value?.handleOpen();
    }
}
const onSubmitModal = () => {
    onSubmit(listRef.value)
}

// 查询
const onSubmit = (ref: any) => {
    const params = Object.assign({}, condition.value, { type: 1 })
    ref && ref.search(params);
}
onMounted(() => {
    onSubmit(listRef.value)
})
// 新增
const toAdd = () => {
    router.push({ name: 'kitchenWasteManagement:dataFilling:add' })
}

// 删除
const selectedIds = ref<(string | undefined)[]>([])

const onSelectionChange = (selectedRow: IEnterprise[] = []) => {
    console.log(selectedRow)
    selectedIds.value = selectedRow.map(item => item.id)
}

const onDelete = () => {
    if (selectedIds.value.length === 0) {
        that && that.$Message.warning('最少选择一条数据')
        return
    }
    that && that.$Modal.confirm({
        title: '提示',
        content: '您确定要删除选中的数据吗',
        onOk: () => {
            const ids = selectedIds.value;
            delGarbageData({ ids })
                .then(res => {
                    if (res.success) {
                        onSubmit(listRef.value)
                        selectedIds.value = []
                    }
                })
            console.log('删除id', ids)
        }
    })
}

// 关联收运计划下拉 -- 根据垃圾处理厂id查询

const planParams = computed(() => {
    const factoryId = condition.value.factoryId;
        return {
            factoryId
        }
})

const formatTime = (row:any) => {
    if (row.recordTime) {
        return moment(row.recordTime).format('YYYY-MM-DD HH:mm');
    }
}

// 根据填报类型返回不同格式的日期格式
const formatCollectTime = (row: IRecord) => {
    let formatStr = ''
    if (row.dataType == 1) {
        formatStr = 'YYYY-MM-DD'
    } else {
        formatStr = 'YYYY-MM'
    }
    if (row.collectTime) {
        return moment(row.collectTime).format(formatStr);
    }
}

const tableColumn = ref([
    { type: 'selection', width: 40 },
    { title: '处理厂名称', minWidth: 100, key: 'factoryName', tooltip: true },
    { title: '关联收运计划', key: 'planName', minWidth: 120, tooltip: true },
    { title: '填报类型', slot: 'dataType', tooltip: true },
    { title: '收运时间', minWidth: 100, slot: 'collectTime', tooltip: true },
    { title: '排放数据(吨)', minWidth: 100, key: 'emission', tooltip: true },
    { title: '填报时间', width: 150, slot: 'recordTime', tooltip: true },
    { title: '收运车辆', minWidth: 120, key: 'carLicenseNo', tooltip: true },
    { title: '操作', slot: 'action', width: 80, lock: true, fixed: true }
])

</script>

<style lang="less" scoped>
/deep/ .ivu-form-item{
    width: 80%;
}

</style>
