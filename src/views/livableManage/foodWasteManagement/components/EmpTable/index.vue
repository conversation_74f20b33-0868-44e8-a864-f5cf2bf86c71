<script lang="ts" setup>
import "./index.less";
import baseTable from "@/components/global/baseTable/index.vue";
import LinkBtn from "@/components/global/LinkBtn/index.vue";
import { Table } from "view-ui-plus";
import SModal from "@/components/common/modal/index.vue";
import shipmentRecords from "@/views/livableManage/foodWasteManagement/components/modalContent.vue";
import {
    onMounted,
    onUpdated,
    getCurrentInstance,
    nextTick,
    defineProps,
    watch,
    ref,
    defineEmits,
} from "vue";
import { delReceiptAndShipmentRecords } from "@/api/livableManage/foodWasteManagementService";
import { useRoute } from "vue-router";
const emit: any = defineEmits();
const route = useRoute();
const props = defineProps({
    dataList: {
        default: [],
    },
    origin: {
        default: "",
    },
    type: {
        default: 1,
    },
    searchObj: {
        default: () => ({}),
    },
});

const that = getCurrentInstance()?.appContext.config.globalProperties;
// 删除选中数据
const onDelete = async (row: any) => {
    if (row.id) {
        that?.$Modal.confirm({
            title: "提示",
            content: "您确定要删除选中的数据吗",
            onOk: () => {
                delReceiptAndShipmentRecords({ ids: [row.id] }).then((res: any) => {
                    if (res.success) {
                        that.$Message.success("删除成功");
                        getList();
                    }
                });
            },
        });
    }
};
const isShow = ref<boolean>(false);
const modalData = ref<any>();
const pointId = ref<number>(0);
const collectRecordId = ref<number>(0);
const modalCom = ref();
const openModal = (row: any, rows: any) => {
    isShow.value = true;
    modalData.value = rows;
    collectRecordId.value = rows.id;
    pointId.value = row.id;
};
// 编辑收运点记录
const editPointRecord = () => {
    modalCom.value.handleConfirm();
};
const statusEnum = ["未收运", "已收运"];
const columns = ref(
    [
        {
            type: "expand",
            tree: true,
            render: (h: any, { row: rows }: any) => {
                return h(
                    Table,
                    {
                        class: "inside-table",
                        columns: [
                            { title: "收运垃圾桶数量", key: "num", tooltip: true, width: 120 },
                            { title: "收运重量(kg)", key: "weight", tooltip: true, width: 100 },
                            { title: "收运人", key: "contactPerson", tooltip: true, width: 140 },
                            { title: "收运车辆", key: "carLicenseNo", width: 100, tooltip: true },
                            { title: "收运详情", key: "content", tooltip: true },
                            { title: "收运时间", key: "time", width: 160, tooltip: true },
                            {
                                title: "操作",
                                slot: "operation",
                                width: 100,
                            },
                        ],
                        data: rows.detailList,
                        // loading: loading.value,
                    },
                    {
                        operation: ({ row }: any) => [
                            h(
                                LinkBtn,
                                {
                                    size: "small",
                                    onClick: () => onDelete(row),
                                },
                                "删除"
                            ),
                            h(
                                LinkBtn,
                                {
                                    size: "small",
                                    onClick: () => openModal(row, rows),
                                },
                                "编辑"
                            ),
                        ],
                    }
                );
            },
            width: 40,
        },
        props.origin === "收运点管理" && {
            title: "关联任务",
            key: "taskName",
            tooltip: true,
        },
        props.origin === "收运点管理" && {
            title: "任务日期",
            slot: "taskTime",
            width: 120,
            tooltip: true,
        },
        props.origin !== "收运点管理" && {
            title: "收运点",
            key: "pointName",
            tooltip: true,
        },
        {
            title: "收运情况",
            slot: "status",
            tooltip: true,
        },
        {
            title: "已收运次数",
            key: "todayCompleted",
            width: 100,
        },
        {
            title: "垃圾桶数量",
            key: "num",
            width: 100,
        },
        {
            title: "实收垃圾桶数量",
            key: "doneGarbageNum",
            width: 120,
        },
        {
            title: "收运重量(kg)",
            key: "weight",
            width: 120,
        },
        {
            title: "最新一次收运时间",
            key: "collectionTime",
            width: 160,
        },
    ].filter(Boolean)
);
watch(
    () => props.dataList,
    (newVal, oldVal) => {
        data.value = newVal;
    }
);
const data = ref([]);
const listRef = ref();
const getList = () => {
    return new Promise((resolve, reject) => {
        resolve(
            nextTick(() => {
                listRef.value.search({
                    [(props.origin === "收运点管理" && "pointId") || "taskId"]: route.query.id,
                    type: props.type,
                });
            })
        );
    });
};
//事件代理代理点击表格行打开子表格
const handleRowClick = (item: any, index: number) => {
    const expand: any = document.querySelectorAll(
        ".ivu-table-cell-expand .ivu-icon-ios-arrow-forward"
    );
    expand[index].click();
};
// 创建dom元素返到展开图标
const createElement = () => {
    const containers = document.querySelectorAll(
        ".ivu-table-cell-expand"
    );
    containers.forEach((container: any) => {
        const dom = document.createElement("div");
        container.style.position = "relative";
        container.appendChild(dom);
        dom.style.position = "absolute";
        dom.style.top = "0";
        dom.style.left = "0";
        dom.style.width = "100%";
        dom.style.height = "100%";
        dom.style.background = "#00000000";
        dom.style.zIndex = "10000";
        dom.onclick = (e: any) => {
            e.target.parentElement.firstElementChild.click();
        };
    });
};

onMounted(async () => {
    await getList();
});
const handleSuccess = () => {
    isShow.value = false;
    emit("on-edit-success");
    getList();
};
const handleDataFinish = () => {
    createElement();
};
watch(
    () => props.searchObj,
    (newVal) => {
        listRef.value.search(
            Object.assign(
                {
                    [(props.origin === "收运点管理" && "pointId") || "taskId"]: route.query.id,
                    type: props.type,
                },
                newVal
            )
        );
    },
    { deep: true }
);
</script>
<template>
    <!-- @on-expand="handleExpand" -->
    <baseTable
        class="my-table"
        no-data-text="--"
        :columns="columns"
        ref="listRef"
        empty-block-str="--"
        url="/garbageTaskRecords/list"
        @on-row-click="handleRowClick"
        @tableData="handleDataFinish"
    >
        <template #taskTime="{ row }">
            <tooltip-auto-show :content="row.taskTime?.split(' ')[0]"></tooltip-auto-show>
        </template>
        <template #status="{ row }">
            {{ statusEnum[row.status] }}
        </template>
    </baseTable>
    <SModal
        @on-cancel="isShow = false"
        @on-confirm="editPointRecord"
        :isShow="isShow"
        :option="{
            title: '编辑收运记录',
        }"
    >
        <shipment-records
            :type="props.type"
            ref="modalCom"
            @on-success="handleSuccess"
            :collectRecordId="collectRecordId"
            :isDetail="true"
            :id="pointId"
            :data="modalData"
        ></shipment-records>
    </SModal>
</template>

<style lang="less" scoped></style>
