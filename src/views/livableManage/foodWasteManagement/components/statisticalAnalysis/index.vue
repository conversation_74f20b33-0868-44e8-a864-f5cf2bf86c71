<script lang="ts" setup>
import CardBox from "./components/cardBox.vue";
import dateSelectT from "@/components/common/dateSelect/dateSelectT.vue";
import collectionAmount from "./components/collectionAmount.vue";
import collectionPointTop from "./components/collectionPointTop.vue";
import collectionFinishDetail from "./components/collectionFinishDetail.vue";
import exportModal from "@/components/common/modal/exportModal.vue";
import deviceTreeSelect from "@/components/common/deviceTreeSelect/index.vue";
import collectCompanySelect from "@/views/livableManage/garbageCollectionManagement/components/collectCompanySelect/index";
import collectCarSelect from "@/views/livableManage/garbageCollectionManagement/components/collectCarSelect/index";
import AMapMarker from "@/components/common/mapMarker/index";
import { onMounted, ref, computed, nextTick } from "vue";
import { getType, getCrumbs } from "../commonData";
import { SearchObj, dateSelected } from "./type";
import { isEmpty } from "wei-util";
import {
    getGarbageCollectionDetail,
    getGarbageTaskDetail,
    getRank,
    getOverview,
    getGarbageAmount,
    getGarbagePoint,
} from "@/api/livableManage/foodWasteManagementService";
const type: number = getType();
const searchObj = ref<SearchObj>({
    startTime: "",
    type,
    endTime: "",
    pointIds: [],
    companyId: null,
    carId: null,
});
const changeDateType = (val: dateSelected) => {
    searchObj.value.startTime = val.startTime;
    searchObj.value.endTime = val.endTime;
    init();
};
const currentCheckedLngLat = ref<any[]>([]);
const handleTreeSelect = (selected: any[], valueArr: string[],currentChecked:any) => {
    searchObj.value.pointIds = valueArr;
    if(currentChecked && !currentChecked.children?.length && currentChecked.checked){
        const point = allGarbagePointData.value.find((item:any)=>item.id == currentChecked.value)
        currentCheckedLngLat.value = [point.gdx,point.gdy]
    }else{
        currentCheckedLngLat.value = []
    }
    init();
};
const changeCompany = (val: any) => {
    searchObj.value.companyId = val.id;
    init();
};
const changeCar = (val: any) => {
    searchObj.value.carId = val.id;
    init();
};
//导出
const exportCom = ref();
const handleExport = () => {
    exportCom.value.export("/garbageStatistical/records/export", searchObj.value);
};
//获取卡片数据
const cardBoxData = ref<any[]>([]);
const getCardBoxData = async () => {
    let { success, data }: { success: boolean; data: any } = (await getOverview(
        type
    )) as unknown as any;
    if (success) cardBoxData.value = data;
};
//获取垃圾收运量
const collectionAmountData = ref<any>({});
const getGarbageCollectionAmount = async () => {
    let { success, data }: { success: boolean; data: any } = (await getGarbageAmount(
        searchObj.value
    )) as unknown as any;
    if (success) collectionAmountData.value = data;
};
//收运点完成情况收运点完成情况
const collectionPointFinishDetailData = ref<any[]>([]);
const getCollectionPointFinish = async () => {
    let { success, data }: { success: boolean; data: any } = (await getGarbageCollectionDetail(
        searchObj.value
    )) as unknown as any;
    if (success) collectionPointFinishDetailData.value = data;
};
//获取排名
const ranking = ref<any[]>([]);
const getRanking = async () => {
    let { success, data }: { success: boolean; data: any } = (await getRank(
        searchObj.value
    )) as unknown as any;
    if (success) ranking.value = data;
};
//收运任务完成情况
const collectionTaskData = ref<any[]>([]);
const getCollectionTaskData = async () => {
    let { success, data }: { success: boolean; data: any } = (await getGarbageTaskDetail(
        searchObj.value
    )) as unknown as any;
    if (success) collectionTaskData.value = data;
};
// 获取收运点
const allGarbagePointData = ref<any[]>([]);
const mapMarkerRef = ref();
const getGarbagePointData = async () => {
    let { success, data }: { success: boolean; data: any } = (await getGarbagePoint(
        type
    )) as unknown as any;
    if (success) {
        allGarbagePointData.value = data.records.map((item:any)=>({
            ...item,
            id:String(item.id),
        }));
    }
};
const init = () => {
    getCardBoxData();
    getGarbageCollectionAmount();
    getCollectionPointFinish();
    getRanking();
    getCollectionTaskData();
};
onMounted(() => {
    init();
    getGarbagePointData();
});
const selectedGarbagePointData = ref<any[]>([]);
// 点击标记点
const markerClick = (data: { id: number }[]) => {
    searchObj.value.pointIds = data.map((item: { id: number }) => item.id);
    selectedGarbagePointData.value = data
    init();
};
</script>
<template>
    <BreadcrumbCustom>
        <BreadcrumbItem>{{ getCrumbs() + "管理" }}</BreadcrumbItem>
        <BreadcrumbItem>统计分析</BreadcrumbItem>
    </BreadcrumbCustom>
    <ContentCard class="content-title" :title="getCrumbs() + '统计分析'"></ContentCard>
    <CardBox :data="cardBoxData"></CardBox>
    <ContentCard>
        <div class="search-box">
            <deviceTreeSelect
                :checked="searchObj.pointIds"
                :clearable="true"
                origin="new"
                searchKey="title"
                title="收运点"
                @on-change="handleTreeSelect"
                class="select"
                :method-url="`/garbageStatistical/tree/${type}`"
                :showBorder="true"
            >
            </deviceTreeSelect>
            <!-- 收运公司 -->
            <collect-company-select
                v-model="searchObj.companyId"
                :type="type"
                placeholder="请选择收运公司"
                @on-change="changeCompany"
            />
            <!-- 收运车辆 -->
            <collect-car-select
                :type="type"
                v-model="searchObj.carId"
                placeholder="请选择收运车辆"
                @on-change="changeCar"
            />
            <dateSelectT defaultDate="nm" @on-change="changeDateType" />
        </div>
        <div class="title-export">
            <div class="title">垃圾收运量</div>
            <Button type="primary" @click="handleExport">
                <i style="font-size: 11px" class="iconfont icon-Vector"></i>
                导出</Button
            >
        </div>
        <div class="line-chart-map">
            <div class="left-part">
                <collectionAmount :data="collectionAmountData" />
            </div>
            <div class="right-part" v-if="getCrumbs() === '垃圾收运'">
                <AMapMarker
                    ref="mapMarkerRef"
                    :markers="allGarbagePointData"
                    :activeMarkerId="searchObj.pointIds"
                    :lngLat="currentCheckedLngLat"
                    :clickMultiple="true"
                    :toggleSelect="true"
                    :marker-icon="require('./images/icon_001.png')"
                    @marker-click="markerClick"
                />
            </div>
        </div>
        <div class="collection-detail-top">
            <div class="detail">
                <div class="title">收运点完成情况</div>
                <collectionFinishDetail :data="collectionPointFinishDetailData" />
            </div>
            <div class="top">
                <div class="title">收运点排名</div>
                <collectionPointTop :data="ranking" />
            </div>
        </div>
        <div class="finish-detail">
            <div class="title">收运任务完成情况</div>
            <collectionFinishDetail :data="collectionTaskData" />
        </div>
    </ContentCard>
    <exportModal ref="exportCom" />
</template>

<style lang="less" scoped>
@title-color: #1e2a55;

.title() {
    color: @title-color;
    font-size: 14px;
    font-weight: bold;
    margin-bottom: 8px;
    position: relative;

    &::before {
        content: "";
        position: absolute;
        left: -8px;
        top: 4px;
        border-radius: 2px;
        display: block;
        width: 2px;
        height: 12px;
        background-color: #1890ff;
    }
}

.content-title {
    :deep(.ivu-typography) {
        margin-bottom: 0;
    }
}

.search-box,
.title-export {
    width: 100%;
    display: flex;
    justify-content: space-between;

    .select {
        width: 265px;
        position: relative;
    }

    .title {
        .title();
    }
}

.search-box {
    margin-bottom: 18px;
}

.collection-detail-top {
    width: 100%;
    display: flex;
    column-gap: 72px;
    padding: 16px 0;

    .detail {
        flex: 2;

        .title {
            .title();
        }
    }

    .top {
        flex: 1;

        .title {
            .title();
        }
    }
}

.finish-detail {
    width: 100%;

    .title {
        .title();
    }
}

.line-chart-map {
    height: 400px;
    margin-top: 16px;
    display: flex;
    justify-content: space-between;

    .left-part {
        flex: 1;
        margin-right: 20px; /* Adjust as needed */
    }

    .right-part {
        flex: 1;
    }
}
</style>
