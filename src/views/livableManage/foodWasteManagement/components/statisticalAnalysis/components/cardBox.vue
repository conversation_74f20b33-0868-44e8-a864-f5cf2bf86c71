<script lang="ts" setup>
import { defineProps, watch, ref } from 'vue'
import { getValue, toFixed } from 'wei-util'
import { CardData } from '../type'
import { getCardTitleName } from '../../commonData'
const props = defineProps({
  data: {
    type: Array,
    default: () => []
  }
})
const data = ref<CardData[]>([])
watch(() => props.data, (newVal: any[]) => {
  data.value[0] = {
    title: '收运任务',
    unit: '个',
    todayValue: getValue(newVal[0], 'sum'),
    record: [
      { title: '已完成', value: getValue(newVal[0], 'do') },
      { title: '未完成', value: getValue(newVal[0], 'unDo') }
    ]
  }
  data.value[1] = {
    title: '收运点',
    unit: '个',
    todayValue: getValue(newVal[1], 'sum'),
    record: [
      { title: '已完成', value: getValue(newVal[1], 'do') },
      { title: '未完成', value: getValue(newVal[1], 'unDo') }
    ]
  }
  data.value[2] = {
    title: '今日收运量',
    unit: 't',
    todayValue: toFixed(getValue(newVal[2], 'today'), 1),
    record: [
      { title: '本月', value: toFixed(getValue(newVal[2], 'month'), 1) },
      { title: '本年', value: toFixed(getValue(newVal[2], 'year'), 1) },
      { title: '累计', value: toFixed(getValue(newVal[2], 'sum'), 1) }
    ]
  }
  data.value[3] = {
    title: getCardTitleName(),
    unit: '个',
    todayValue: getValue(newVal[3], 'sum'),
    record: getCardTitleName() === '企业申报单' ? [
      { title: '已通过', value: getValue(newVal[3], 'pass') },
      { title: '已驳回', value: getValue(newVal[3], 'rejected') },
      { title: '待审核', value: getValue(newVal[3], 'audit') },
    ] : [
      { title: '昨日新增', value: getValue(newVal[3], 'today') },
      { title: '本月新增', value: getValue(newVal[3], 'month') },
    ]
  }
}, { deep: true })
</script>
<template>
  <div class="content-box">
    <div class="container" v-for="(item, index) in data" :key="item.title">
      <div class="title">{{ getValue(item, 'title') }}</div>
      <div class="amount">
        <span class="num">{{ getValue(item, 'todayValue') }}</span>
        <span class="unit">{{ getValue(item, 'unit') }}</span>
      </div>
      <div class="bottom">
        <div class="record" v-for="(i, index2) in getValue(item, 'record')"  :style="`width:calc(90% / ${item.record.length})`" :key="index2">
          <TooltipAutoShow>
            <span class="title">{{ getValue(i, 'title') }}
              <span class="value" :style="{ color: index < 2 ? '#00B42A' : '' }">{{ getValue(i, 'value') }}</span>
            </span>
            <span class="unit">{{ getValue(item, 'unit') }}</span>
          </TooltipAutoShow>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="less" scoped>
@boldColor: #1E2A55;

.content-box {
  width: 100%;
  display: flex;
  column-gap: 16px;
  margin: 8px 0;

  .container {
    width: ~'calc((100% - 48px) / 4)';
    background-color: #fff;
    border-radius: 4px;
    padding: 8px 16px;
    display: flex;
    flex-direction: column;
    height: 104px;

    .title {
      font-weight: bold;
      color: @boldColor;
      font-size: 14px;
      flex: 1;
    }

    .amount {
      flex: 2;
      display: flex;
      column-gap: 2px;
      align-items: center;
      position: relative;

      .num {
        font-weight: bold;
        font-size: 22px;
        color: @boldColor;
      }

      .unit {
        margin-left: 2px;
        color: #798799;
        font-size: 12px;
        height: 38px;
        line-height: 44px;
      }

      &::after {
        content: '';
        display: block;
        background-color: #EFF1F5;
        height: 1px;
        width: 100%;
        position: absolute;
        bottom: 0;
        left: 0;
      }
    }

    .bottom {
      margin-top: 8px;
      width: 100%;
      flex: 1;
      display: flex;
      justify-content: space-between;
      column-gap: 5%;
      position: relative;

      .record {
        width: ~'calc(90% / 3)';
        color:#4E627E;

        .title {
          all: unset;
          color: #4E627E;
          font-size: 12px;

          .value {
            margin-left: 8px;
            font-size: 14px;
          }
        }
      }
      .unit{
        font-size: 12px;
      }

    }
  }
}
</style>