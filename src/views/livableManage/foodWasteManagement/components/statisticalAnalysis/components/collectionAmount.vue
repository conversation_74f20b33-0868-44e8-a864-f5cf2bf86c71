<script lang="ts" setup>
import { defineComponent, reactive, markRaw, watch, onBeforeMount, onMounted, ref, computed } from 'vue'
import EchartItem from '@/components/common/EchartItem';
import { EchartsTooltip } from '@/utils/tool';
import { getValue } from 'wei-util';
const props = defineProps({
  data: {
    default: () => []
  }
})
const option = ref<any>({

})

const linear = {
  type: "linear",
  x: 0,
  y: 0,
  x2: 1,
  y2: 0,
  colorStops: [
    {
      offset: 0.0095,
      color: "#1EE7FF",
    },
    {
      offset: 0.5776,
      color: "#249AFF",
    },
    {
      offset: 0.8584,
      color: "#6F42FB",
    },
  ],
}
const lineItem = (data: number[], smooth: boolean) => {
  return {
    type: "line",
    smooth: smooth,
    showSymbol: false,
    data: data,
    lineStyle: {
      // background: linear-gradient(89.87deg, #1EE7FF 0.95%, #249AFF 57.76%, #6F42FB 85.84%);
      borderWidth: 3,
      color: smooth && linear || '#F7BA1E',
      type: !smooth && 'dashed' // 设置线条样式为虚线
    },
    areaStyle: {
      color: smooth && {
        type: "linear",
        x: 0,
        y: 0,
        x2: 0,
        y2: 1,
        colorStops: [
          {
            offset: 0,
            color: "rgba(17, 126, 255, 0.16)",
          },
          {
            offset: 0.93,
            color: "rgba(17, 128, 255, 0)",
          },
        ],
      } || "#fff",
    },
  }
}
const maxNumber = ref<number>(0)
watch(() => props.data, (newVal) => {
  const xAxisData: string[] = newVal.map(item => getValue(item, 'timeStr'))
  const realWeightData: number[] = newVal.map(item => getValue(item, 'realWeight'))
  const standardWeightData: number[] = newVal.map(item => getValue(item, 'standardWeight'))
  maxNumber.value = Math.max(...[...realWeightData,...standardWeightData])
  option.value = {
    tooltip: {
      trigger: "axis",
      borderWidth: 0,
      formatter: (arg: any) => {
        return EchartsTooltip(arg, '吨')
      }
    },
    legend: {
      icon: 'rect',
      itemHeight: 10,
      itemWidth: 10,
      borderRadius: 1,
      textStyle: {
        color: '#4E5969'
      }
    },
    grid: {
      left: 30,
      right: 10,
      bottom: 20,
      top: 30,
      containLabel: true,
    },
    xAxis: [
      {
        axisTick: {
          alignWithLabel: true
        },
        type: "category",
        data: xAxisData,
        axisLabel: {
          textStyle: {
            color: "#798799", // 设置 Y 轴标签颜色为蓝色
          },
        },
        axisLine: {
          show: false,
          lineStyle: {
            color: "#E0E6F1",
          }
        },
      },
    ],
    yAxis: [
      {
        type: "value",
        name: '单位:吨',
        boundaryGap: false,
        axisLine: {
          show: false,
          lineStyle: {
            color: "#E0E6F1",
          }
        },
        nameTextStyle: {
          color: "#798799",
          align: 'right',
        },
        axisLabel: {
          textStyle: {
            color: "#798799", // 设置 Y 轴标签颜色为蓝色
          },
        },
        splitLine: {
          lineStyle: {
            type: "dashed", //虚线
          },
          show: true, //隐藏
        },
      },
    ],
    color: [linear, '#F7BA1E'],
    series: [
      {
        name: "实际收运量",
        ...lineItem(realWeightData, true)
      },
      {
        name: "标准收运量",
        ...lineItem(standardWeightData, false)
      },
    ]
  }
}, { deep: true })
</script>
<template>
  <div class="container">
    <EchartItem v-if="maxNumber" :option="option" />
    <no-data class="no-data" v-else style="height: 100%;width:100%" />
  </div>
</template>

<style lang="less" scoped>
.container {
  display: grid;
  place-items: center;
  width: 100%;
  height: 100%;
}
</style>