<script lang="ts" setup>
import { nextTick } from 'process'
import { defineProps, onMounted, ref, watch } from 'vue'
import { getValue, carousel } from 'wei-util'
const props = defineProps({
  data: {
    type: Array,
    default: () => []
  }
})
const data = ref<any[]>()
const maxNumber = ref<number>(0)
watch(() => props.data, (newVal:any[]) => {
  data.value = newVal
  maxNumber.value = newVal.length && Math.max(...newVal.map(i=>i.realWeight)) || 0
  nextTick(() => {
    carousel('#ranking', 'top', 4000, true, 5)
  })
}, { deep: true })
</script>
<template>
  <div class="container" id="ranking">
    <div v-if="maxNumber" class="content">
      <div class="top-item" v-for="(item, index) in data" :key="index">
        <div class="left">
          <div :class="['rank', 'active' + (index < 4 && index + 1)]">
            {{ index + 1 }}
          </div>
          <div class="name">
            <tooltipAutoShow :content="getValue(item, 'name')" />
          </div>
        </div>
        <div class="num">
          <tooltipAutoShow>
            <span class="value">{{ getValue(item, 'realWeight') }}</span>
            <span>吨</span>
          </tooltipAutoShow>
        </div>
      </div>
    </div>
    <no-data class="no-data" v-else style="height: 215px;width:100%" />
  </div>
</template>

<style lang="less" scoped>
@text-color: #798799;

::-webkit-scrollbar {
  display: none;
}

.container {
  height: 215px;
  overflow: hidden;
  overflow-y: auto;
  width: 100%;

  .content {
    height: 100%;
    width: 100%;

    .top-item {
      height: ~'calc(100% / 5)';
      width: 100%;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .active {
        background: #EFF1F5;
        color: @text-color;
      }

      .active1 {
        background: #FDA979;
        color: #fff;
        box-shadow: 0px 2px 3px 0px #FFFFFF80 inset;
      }

      .active2 {
        background: #FFC876;
        box-shadow: 0px 2px 3px 0px #FFFFFF80 inset;
        color: #fff;
      }

      .active3 {
        color: #fff;
        background: #FDE079;
        box-shadow: 0px 2px 3px 0px #FFFFFF80 inset;
      }

      .active4 {
        color: @text-color;
        background-color: #EFF1F5;
      }

      .left {
        display: flex;
        column-gap: 8px;
        width: 80%;

        .rank {
          width: 20px;
          height: 20px;
          line-height: 20px;
          text-align: center;
        }

        .name {
          width: ~'calc(100% - 28px)';
          color: @text-color;
          font-size: 12px;
          line-height: 20px;
        }
      }

      .num {
        width: 20%;
        text-align: right;

        .value {
          color: #4E627E;
        }

        span {
          color: @text-color;
        }
      }
    }
  }
}
</style>