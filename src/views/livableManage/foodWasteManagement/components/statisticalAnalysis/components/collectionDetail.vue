<script lang="ts" setup>
import { defineComponent, reactive, watch, toRefs, onBeforeMount, onMounted, ref, computed } from 'vue'
import EchartItem from '@/components/common/EchartItem';
import { EchartsTooltip } from '@/utils/tool';
const props = defineProps({
  option: {
    default: () => []
  }
})
const option = ref<any>({

})
const barItem = {
  itemStyle: {
    normal: {
      barBorderRadius: 1,
    }
  },
  symbolSize: [12, 6],
  type: 'pictorialBar',
  symbol: 'rect',
  symbolRepeat: true,
  smooth: true,
  showSymbol: false,
}
const yAxisItem = (nameTextStyleAlign:string) => {
  return {
    type: "value",
    axisLine: {
      show: false,
      lineStyle: {
        color: "#E0E6F1",
      }
    },
    axisLabel: {
      textStyle: {
        color: "#798799",
      },
    },
    nameTextStyle: {
      color: "#798799",
      align: nameTextStyleAlign
    },
    splitLine: {
      lineStyle: {
        type: "dashed", //虚线
      },
      show: true, //隐藏
    },
  }
}
watch(() => props.option, (newVal) => {
  option.value = {
    tooltip: {
      trigger: "axis",
      borderWidth: 0,
      formatter: (arg: any) => {
        return EchartsTooltip(arg)
      }
    },
    legend: {
      itemHeight:10,
      itemWidth:10,
      borderRadius:1,
      textStyle:{
        color:'#4E5969'
      }
    },
    grid: {
      left: 10,
      right: 10,
      bottom: 20,
      top: 40,
      containLabel: true,
    },
    xAxis: [
      {
        type: "category",
        data: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
        axisLabel: {
          textStyle: {
            color: "#798799", // 设置 Y 轴标签颜色为蓝色
          },
        },
        axisLine: {
          show: false,
          lineStyle: {
            color: "#E0E6F1",
          }
        },
      },
    ],
    yAxis: [
      {
        name:'单位:个',
        ...yAxisItem('right')
      },
      {
        name:'单位:%',
        ...yAxisItem('left')
      }
    ],
    color: ["#3491FA", '#33D1C9', '#F7BA1E'],
    series: [
      {
        name: "已收运",
        symbolOffset: [-15,0],
        ...barItem,
        data: [150, 150, 123, 145, 654, 256, 332, 212, 255, 288],
      },
      {
        name: "应收运",
        ...barItem,
        symbolOffset: [0,0],
        data: [150, 150, 123, 145, 654, 256, 332, 212, 255, 288],
      },
      {
        name: "收运完成率",
        ...barItem,
        symbolOffset: [15,0],
        data: [150, 150, 123, 145, 654, 256, 332, 212, 255, 288],
      },
    ]
  }
}, { immediate: true })
</script>
<template>
  <div class="container">
    <EchartItem :option="option" />
  </div>
</template>

<style lang="less" scoped>
.container {
  width: 100%;
  height: 215px;
}
</style>