<script lang="ts" setup>
import { watch, ref } from 'vue'
import EchartItem from '@/components/common/EchartItem';
import { EchartsTooltip } from '@/utils/tool';
import { getValue } from 'wei-util'
const props = defineProps({
  data: {
    type: Array,
    default: () => []
  }
})

const option = ref<any>({})
const lineItem = (color?: string) => {
  return {
    type: 'line',
    smooth: true,
    showSymbol: false,
    lineStyle: {
      borderWidth: 3,
    }
  }
}
const yAxisItem = (max: number, nameTextStyleAlign?: string) => {
  return {
    type: "value",
    max: Math.ceil(max / 5) * 5,
    interval: Math.ceil(max / 5), // 平均分为5份
    axisLine: {
      show: false,
      lineStyle: {
        color: "#E0E6F1",
      }
    },
    axisLabel: {
      textStyle: {
        color: "#798799",
      },
    },
    nameTextStyle: {
      color: "#798799",
      align: nameTextStyleAlign
    },
    splitLine: {
      lineStyle: {
        type: "dashed", //虚线
      },
      show: true, //隐藏
    },
  }
}
const maxNumber = ref<number>(0)
watch(() => props.data, (newVal) => {
  const xAxisData: string[] = newVal.map(item => getValue(item, 'timeStr'))
  const doneData: number[] = newVal.map(item => getValue(item, 'done'))
  let doneDataMax: number = Math.max.apply(null, doneData)
  const shouldData: number[] = newVal.map(item => getValue(item, 'should'))
  let shouldDataMax: number = Math.max.apply(null, shouldData)
  const partData: number[] = newVal.map(item => getValue(item, 'part'))
  let partDataMax: number = Math.max.apply(null, partData)
  maxNumber.value = Math.max(...[...doneData,...shouldData,...partData])
  const isNumber = (value: any) => {
    return typeof value === 'number'
  }

  let lineMaxData: number = shouldDataMax > doneDataMax ? shouldDataMax : doneDataMax
  lineMaxData = isNumber(lineMaxData) && lineMaxData > 5 ? lineMaxData : 5
  partDataMax = isNumber(partDataMax) && partDataMax > 5 ? partDataMax : 5
  option.value = {
    tooltip: {
      trigger: "axis",
      borderWidth: 0,
      formatter: (arg: any) => {
        return EchartsTooltip(arg, ['个', '个', '%'])
      }
    },
    legend: {
      icon: 'rect',
      itemHeight: 10,
      itemWidth: 10,
      borderRadius: 1,
      textStyle: {
        color: '#4E5969'
      }
    },
    grid: {
      left: 30,
      right: 40,
      bottom: 20,
      top: 40,
      containLabel: true,
    },
    xAxis: [
      {
        type: "category",
        data: xAxisData,
        axisLabel: {
          textStyle: {
            color: "#798799",
          }
        },
        axisTick: {
          alignWithLabel: true
        },
        axisLine: {
          show: false,
          lineStyle: {
            color: "#E0E6F1",
          }
        },
      },
    ],
    yAxis: [
      {
        name: '单位:个',
        boundaryGap: false,
        ...yAxisItem(lineMaxData, 'right')
      },
      {
        name: '单位:%',
        ...yAxisItem(partDataMax, 'left')
      }
    ],
    color: ['#3491FA', '#0FC6C2', '#94BFFF'],
    series: [
      {
        name: "已收运",
        ...lineItem(),
        data: doneData,
      },
      {
        name: "应收运",
        ...lineItem('#0FC6C2'),
        data: shouldData,
      },
      {
        name: "收运完成率",
        type: "bar",
        yAxisIndex: 1,
        itemStyle: {
          color: '#94BFFF'
        },
        data: partData,
      },
    ]
  }
}, { deep: true })
</script>
<template>
  <div class="container">
    <EchartItem v-if="maxNumber" :option="option" />
    <no-data class="no-data" v-else style="height: 215px;width:100%" />
  </div>
</template>

<style lang="less" scoped>
.container {
  display: grid;
  place-items: center;
  width: 100%;
  height: 215px;
}
</style>