<script lang="ts" setup>
import {
    computed,
    defineExpose,
    defineProps,
    getCurrentInstance,
    ref,
    onMounted,
    defineEmits,
} from "vue";
import moment from "moment";
import { useEdit } from "../collectionTask/useEdit";
import SelectByList from "@/components/common/selectByList";
import {
    editReceiptAndShipmentRecords,
    getGarbageTaskInfo,
} from "@/api/livableManage/foodWasteManagementService";
import { DatePicker } from "view-ui-plus";
import SingleTitle from "@/components/common/singleTitle/index.vue";
const that = getCurrentInstance()?.appContext.config.globalProperties;
const { modalRules, modalInfo } = useEdit();
const emit: any = defineEmits();
const formRef = ref();
interface IModalProps {
    id: number | string; // 任务id
    isDetail: boolean;
    collectRecordId: number | string; // 收运记录id
    data: any;
    type: number | string;
}
const props = defineProps<IModalProps>();
// const queryListParams = ref({
//     taskId: props.id,
//     status: props.isDetail ? undefined : 0, // 在详情里面的时候不管是否填报，都要带出来，不在详情的时候只带出未填报的
// });

const formatDateTime = computed({
    get() {
        if (modalInfo.value.time) {
            return moment(modalInfo.value.time).toDate();
        } else {
            return new Date();
        }
    },
    set(newDateTime) {
        if (newDateTime) {
            modalInfo.value.time = moment(newDateTime).format("YYYY-MM-DD HH:mm:ss");
        } else {
            modalInfo.value.time = "";
        }
    },
});

const onInitModal = async (id: number | string) => {
    const res: any = await getGarbageTaskInfo(id);
    if (res.success) {
        modalInfo.value = res.data;
    }
};
onInitModal(props.id);

const garbageCansRule = [
    {
        pattern: /^(([1-9]\d{1,2}(\.\d{1,2})?)|\d((\.\d{1,2})?)|1000|1000.0|1000.00)$/,
        message: "数值范围0-1000",
    },
];

// 提交
const handleConfirm = async () => {
    const isValidated = await formRef.value.validate();
    const id = props.id;
    if (isValidated && id) {
        // const { time, garbageCans,carLicenseNo } = modalInfo.value;
        const params = Object.assign(modalInfo.value, { id, type: props.type, status: 1 });
        const res: any = await editReceiptAndShipmentRecords(params);
        if (res.success) {
            that && that.$Message.success("收运成功");
            emit("on-success");
        }
    }
};

defineExpose({ handleConfirm });
// const garbageLength = ref<number>(0);
const handleChange = () => {
    if(modalInfo.value.num > 10) return
    if (!modalInfo.value.garbageCans) {
        modalInfo.value.garbageCans = new Array(modalInfo.value.num).fill("45").map((i, index) => {
            return {
                name: `垃圾桶${index + 1}`, //垃圾桶名称
                weight: "", //重量
                key: Date.now() * index + i,
            };
        });
        return;
    }
    if (modalInfo.value.num < modalInfo.value.garbageCans.length) {
        const sliceNumber = modalInfo.value.garbageCans.length - modalInfo.value.num;
        modalInfo.value.garbageCans.splice(-sliceNumber);
    } else if (modalInfo.value.num > modalInfo.value.garbageCans.length) {
        const pushNumber = modalInfo.value.num - modalInfo.value.garbageCans.length;
        const pushArray = new Array(pushNumber).fill("22").map((i: any, index) => {
            return {
                name: `垃圾桶${index + 1 + modalInfo.value.garbageCans.length}`, //垃圾桶名称
                weight: "", //重量
                key: Date.now() * index + i,
            };
        });
        modalInfo.value.garbageCans.push(...pushArray);
    }
};

onMounted(() => {
    const upDown = document.querySelector(".ivu-input-number-handler-wrap");
    upDown?.addEventListener("click", () => {
        handleChange();
    });
});
const handleCarChange = (car: any) => {
    modalInfo.value.carId = car.id;
};
</script>

<template>
    <div>
        <div class="title">
            <div class="title-icon-img">
                <img :src="require('@/assets/images/icon_detail.png')" alt="" />
            </div>
            <Title level="5">收运信息</Title>
        </div>
        <Row gutter="24">
            <Col span="12">
                <s-label label="收运计划" :value="modalInfo.planName"></s-label>
            </Col>
            <Col span="12">
                <s-label label="收运任务" :value="modalInfo.taskName"></s-label>
            </Col>
            <Col span="12">
                <s-label label="任务时间" :value="modalInfo.time"></s-label>
            </Col>
            <Col span="12">
                <s-label label="收运人" :value="modalInfo.contactPerson"></s-label>
            </Col>
            <Col span="12">
                <s-label label="收集车辆" :value="modalInfo.carLicenseNo"></s-label>
            </Col>
            <Col span="12">
                <s-label label="收运公司" :value="modalInfo.companyName"></s-label>
            </Col>
        </Row>
        <SingleTitle
            title="收运填报"
            :src="require('@/assets/images/icon-收运填报.png')"
        ></SingleTitle>
        <Form ref="formRef" :model="modalInfo" :rules="modalRules">
            <Row gutter="24" justiry="space-between">
                <Col span="12">
                    <FormItem label="收运点">
                        <Input placeholder="请选择" v-model="modalInfo.pointName" disabled></Input>
                        <!-- <selected-by-list
                            v-model="modalInfo.pointId"
                            url="/garbagePoint/list"
                            :type="props.type"
                            @on-change="onPointChange"
                            :params="queryListParams"
                            disabled
                        ></selected-by-list> -->
                    </FormItem>
                </Col>
                <Col span="12">
                    <FormItem label="收运车辆" prop="carLicenseNo">
                        <select-by-list
                            placeholder="请选择"
                            url="/garbageCar/list"
                            :type="props.type"
                            v-model="modalInfo.carLicenseNo"
                            valueName="licenseNo"
                            label-name="licenseNo"
                            @on-change="handleCarChange"
                        >
                        </select-by-list>
                    </FormItem>
                </Col>
                <!-- <Col span="12">
                    <FormItem label="所属企业">
                        <Input
                            v-model="modalInfo.enterpriseName"
                            placeholder="系统自动带出"
                            disabled
                        />
                    </FormItem>
                </Col> -->
                <Col span="12">
                    <FormItem label="收运时间">
                        <date-picker v-model="formatDateTime" type="datetime"></date-picker>
                    </FormItem>
                </Col>
                <Col span="8">
                    <FormItem label="垃圾桶数量" prop="num">
                        <!-- :max="10" :min="0" -->
                        <InputNumber
                            @on-blur="handleChange"
                            :readonly="!modalInfo.garbageCans"
                            v-model="modalInfo.num"
                        ></InputNumber>
                    </FormItem>
                </Col>
                <Col span="24" v-show="modalInfo.num > 0">
                    <div class="title">收运数量</div>
                    <div class="trashcan-wrapper">
                        <Row gutter="24">
                            <template v-for="(item, index) in modalInfo.garbageCans">
                                <Col span="8">
                                    <FormItem
                                        :label="item.name"
                                        :prop="`garbageCans[${index}].weight`"
                                        :rules="garbageCansRule"
                                    >
                                        <Input v-model="item.weight" clearble placeholder="请输入">
                                            <template #append> kg </template>
                                        </Input>
                                    </FormItem>
                                </Col>
                            </template>
                        </Row>
                    </div>
                </Col>
            </Row>
        </Form>
    </div>
</template>

<style lang="less" scoped>
:deep(.ivu-icon-ios-arrow-down) {
    color: #4e627e !important;
}
:deep(.ivu-form-item) {
    width: 100% !important;
}
:deep(.ivu-form-item-label) {
    padding-top: 0;
    padding-bottom: 8px;
}
.title {
    min-height: 34px;
    line-height: 22px;
    color: #798799;
    font-size: 14px;
    padding-right: 8px;
    display: flex;
    align-items: center;
}
.trashcan-wrapper {
    width: 100%;
    background-color: #f8fafb;
    padding: 16px;
}
.title {
    display: flex;
    .title-icon-img {
        width: 24px;
        height: 24px;
        margin-right: 8px;
        img {
            width: 100%;
            height: 100%;
        }
    }
    h5 {
        margin-bottom: 0;
    }
}
</style>
