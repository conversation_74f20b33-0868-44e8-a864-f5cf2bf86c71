<script lang="ts" setup>
import { validateform } from '@/utils/validateform'
import { getType } from '../commonData'
import { FormT, Resp } from './type'
import { ref, defineExpose } from 'vue'
import { getStandardAmount } from '@/api/livableManage/foodWasteManagementService'
const form = ref<FormT>({
  standard: 0
})
const rules = {
  standard: [
    validateform.requiredNum,
    validateform.nonnegativeNumber,
    validateform.decimals()
  ]
}
const getStandardAmountInfo = async () => {
  const { success, data }: { success: boolean, data: any } = await getStandardAmount(getType()) as unknown as Resp
  if (success) form.value.standard = data.standard
}
getStandardAmountInfo()
const formCom = ref()
const getValidate = async () => {
  let flag: boolean = await formCom.value.validate()
  return {
    flag,
    form: form.value
  }
}
defineExpose({
  getValidate
})
</script>
<template>
  <Form label-position="top" ref="formCom" :model="form" :rules="rules">
    <FormItem label="日标准收运量:" prop="standard">
      <InputNum placeholder="请输入" v-model="form.standard" append="t"></InputNum>
    </FormItem>
  </Form>
</template>

<style lang="less" scoped></style>
