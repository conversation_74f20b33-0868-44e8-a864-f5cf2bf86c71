<script lang="ts" setup>
import sModal from '@/components/common/modal/index.vue';
import ConfigCom from './configuration.vue'
import { getType } from '../commonData'
import { FormT } from './type'
import { ref } from 'vue'
import { standardAmountConfig } from '@/api/livableManage/foodWasteManagementService'
import { MessageSuccess } from '@/hooks/message'
const showModal = ref<boolean>(false)
const configuration = () => {
  showModal.value = true
}
const configCom = ref()
const handelConfirm = async () => {
  const { flag, form }: { flag: boolean, form: FormT } = await configCom.value.getValidate()
  if (flag) {
    showModal.value = false
    const params = {
      type:getType(),
      ...form
    }
    let res: any = await standardAmountConfig(params)
    if (res.success) {
      MessageSuccess('收运量配置成功')
    }
  }
}

</script>
<template>
  <Button @click="configuration"><Icon custom="iconfont icon-settings" />标准收运量配置</Button>
  <sModal :isShow="showModal" @on-confirm="handelConfirm" @on-cancel="showModal = false" :option="{
    title: '标准收运量配置',
    width: 400,
    transfer: true
  }">
    <ConfigCom ref="configCom" />
  </sModal>
</template>

<style lang="less" scoped>

</style>
