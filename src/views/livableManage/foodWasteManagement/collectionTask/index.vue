<template>
    <BreadcrumbCustom>
        <BreadcrumbItem>餐厨垃圾管理</BreadcrumbItem>
        <BreadcrumbItem>收运任务</BreadcrumbItem>
    </BreadcrumbCustom>
    <ContentCard title="收运任务" :base-btn="false">
        <BaseForm @handle-submit="onSubmit(listRef)" :model="condition" inline :label-width="90">
            <template #formitem>
                <FormItem label="收运计划" prop="planId">
                    <SelectByList v-model="condition.planId" :type="1" label-name="planName" url="/garbagePlan/list" clearable></SelectByList>
                </FormItem>
                <FormItem label="收运任务" prop="name">
                    <Input placeholder="请输入" v-model="condition.name" clearable />
                </FormItem>
                <FormItem label="收运车辆" prop="carId">
                    <select-by-list url="/garbageCar/list" :type="1" v-model="condition.carId" label-name="licenseNo" > </select-by-list>
                </FormItem>
        </template>
        </BaseForm>
        <baseTable :model="condition"
                   ref="listRef"
                   :columns="tableColumn"
                   url="/garbagePlanTask/list">
            <template #time="{ row }">
                {{formatTime(row.time)}}
            </template>
            <template #action="{ row }">
                <LinkBtn size="small" @click="goDetail(row)">
                    详情
                </LinkBtn>
                <!--   :disabled="+row.sumPointNum <= +row.donePointNum" 应收的大于实收的显示收运按钮-->
                <LinkBtn  size="small" @click="goCollect(row)">
                    收运
                </LinkBtn>
            </template>
        </baseTable>
    </ContentCard>
    <!-- <s-modal title="新增收运记录" @handleSubmit="onSubmitModal" :styles="{top: '18px' }" :ref-box="addModalRef" ref="modalRef" width="625">
        <AddCollectModal :is-detail="false" v-model="modalInfo" :id="modalId" :plan-id="modalPlanId" ref="addModalRef"></AddCollectModal>
    </s-modal> -->
</template>

<script lang="ts" setup>
import { onMounted, ref } from 'vue';
import SelectByList from '@/components/common/selectByList';
import AddCollectModal
    from './components/addCollectModal.vue';
import { useEdit } from './useEdit';
import moment from 'moment'
import { type } from 'os';
const { router, modalInfo, clearModalInfo } = useEdit();
const listRef = ref();
type row = {
    id:number | string
}
const condition = ref({
    planId: '', // 收运计划id
    name: '', // 收运任务
    carId: '' // 车辆
})

// 详情

const goDetail = ({ id }:row) => {
    router.push({ name: 'kitchenWasteManagement:collectionTask:detail', query: { id }})
}
// 查询
const onSubmit = (ref: any) => {
    const params = Object.assign({}, condition.value, { type: 1 })
    ref && ref.search(params);
}
onMounted(() => {
    onSubmit(listRef.value)
})

// 收运弹窗相关
const modalId = ref(0) // 弹窗id
const addModalRef = ref(null)
const modalRef = ref()
const modalPlanId = ref('') // 计划id， 带给modal查收运点
const goCollect = (row:any) => {
    
    // if (row.planId) {
    //     modalId.value = row.id;
    //     modalPlanId.value = row.planId;
    //     modalRef.value?.handleOpen();
    // }
    router.push({
        name:'kitchenWasteManagement:collectionTask:collection',
        query:{
            id:row.id,
            planId:row.planId,
            typeCode:1,
            origin:'收运任务'
        }
    })
}
// 收运弹窗确认之后刷新列表
const onSubmitModal = () => {
    clearModalInfo();
    onSubmit(listRef.value)
}

const formatTime = (time:string) => {
    if (time) {
        return moment(time).format('YYYY-MM-DD')
    }
}

const tableColumn = ref([
    { title: '收运计划', key: 'planName', minWidth: 180, tooltip: true },
    { title: '收运任务', key: 'name', minWidth: 130, tooltip: true },
    { title: '任务日期', slot: 'time', width: 100, tooltip: true },
    { title: '收运人', key: 'contactPerson', tooltip: true },
    { title: '收运车辆', minWidth: 90, key: 'carLicenseNo', tooltip: true },
    { title: '应收收运点(个)', key: 'sumPointNum', width: 120, tooltip: true },
    { title: '应收垃圾桶数', key: 'sumGarbageNum', width: 110, tooltip: true },
    { title: '实收收运点(个)', key: 'donePointNum', width: 120, tooltip: true },
    { title: '实收垃圾桶数', width: 110, key: 'doneGarbageNum', tooltip: true },
    { title: '完成率（收运点）', width: 140, key: 'pointPart', tooltip: true },
    { title: '操作', slot: 'action', width: 100, lock: true, fixed: true }
])
</script>

<style lang="less" scoped>
/deep/ .ivu-form-item{
    width: 80%;
}
.action-box {
    margin-bottom: 16px;

    .ivu-btn {
        margin-right: 8px;
    }
}
</style>
