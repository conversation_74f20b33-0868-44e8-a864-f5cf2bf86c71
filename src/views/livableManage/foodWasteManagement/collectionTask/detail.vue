<template>
    <div>
        <BreadcrumbCustom>
            <BreadcrumbItem>餐厨垃圾管理</BreadcrumbItem>
            <BreadcrumbItem to="/kitchenWasteManagement/collectionTask">收运任务</BreadcrumbItem>
            <BreadcrumbItem>详情</BreadcrumbItem>
        </BreadcrumbCustom>
        <Form ref="formRef" :model="detailInfo" :rules="formRules" label-position="top">
            <detailCard
                ref="detailRef"
                title="基础信息"
                :src="require('@/assets/images/icon_detail.png')"
                :is-back-btn="true"
                @on-back="onBack"
            >
                <InfoShow :detail-info="detailInfo" />
            </detailCard>
            <detailCard v-if="!isEdit" :class="{ 'bottom-card': isEdit }" title="收运点收运记录">
                <EmpTable @on-edit-success="handleEditSuccess" :dataList="collectRecordList" />
                <!-- <baseTable :columns="columns" ref="listRef" :show-page="false" :data="collectRecordList">
                    <template #serialNumber="{ row, index }">
                        {{index + 1}}
                    </template>
                    <template #status="{ row }">
                        {{ statusEnum[row.status] }}
                    </template>
                    <template #action="{ row }">
                        <template v-if="row.status == '1'">
                            <LinkBtn size="small" @click="openModal(row)">编辑</LinkBtn>
                            <LinkBtn size="small" @click="onDelete(row)">删除</LinkBtn>
                            <LinkBtn size="small" @click="check(row)">查看</LinkBtn>

                        </template>
                        <template v-else>
                            <LinkBtn size="small" @click="check(row)">收运</LinkBtn>
                        </template>
                    </template>
                </baseTable> -->
            </detailCard>
        </Form>
    </div>
    <!-- <s-modal title="收运记录" @closeModal="onCloseModal" :styles="{top: '18px' }" :ref-box="addModalRef" ref="modalRef" width="625">
        <AddCollectModal :is-detail="true" v-model="modalInfo" :id="modalId" ref="addModalRef"></AddCollectModal>
    </s-modal> -->
</template>

<script lang="ts" setup>
import EmpTable from "../components/EmpTable";
import { getCurrentInstance, ref } from "vue";
import InfoShow from "./components/infoShow.vue";
import { useEdit } from "./useEdit";
import {
    delReceiptAndShipmentRecords,
    editPoint,
    getCollectionJobInfo,
    getCollectionRecordList,
} from "@/api/livableManage/foodWasteManagementService";
import AddCollectModal from "./components/addCollectModal.vue";

const that = getCurrentInstance()?.appContext.config.globalProperties;

const detailRef = ref<any>(null);
const formRef = ref(null);

const { isEdit, formRules, detailInfo, router, route, modalInfo, clearModalInfo } = useEdit();

const taskListId = <string>route.query.id;
const onInit = async () => {
    const res = await getCollectionJobInfo(taskListId);
    detailInfo.value = res.data;
};
onInit();
const handleEditSuccess = () => {
    onInit();
};
// 编辑基础信息
const onSubmit = async () => {
    const validateRes = await formRef.value?.validate();
    if (validateRes) {
        const params = Object.assign({}, detailInfo.value, { type: 1 });
        const res = await editPoint(params);
        if (res.success) {
            isEdit.value = false;
            detailRef.value.isEditFlag = false;
            await onInit();
        }
    }
};

const onEdit = (val: boolean) => {
    if (!val) {
        onInit();
    }
    isEdit.value = val;
};

const onBack = () => {
    router.replace({
        path: "/kitchenWasteManagement/collectionTask",
    });
};

// 收运记录相关

const listRef = ref(null);

const condition = ref({
    time: "",
    startTime: "",
    endTime: "",
    status: "",
});

// 删除选中数据
const onDelete = async (row) => {
    if (row.id) {
        that &&
            that.$Modal.confirm({
                title: "提示",
                content: "您确定要删除选中的数据吗",
                onOk: () => {
                    delReceiptAndShipmentRecords({ ids: [row.id] }).then((res) => {
                        if (res.success) {
                            that.$Message.success("删除成功");
                            loadCollectRecordList();
                        }
                    });
                },
            });
    }
};

const statusEnum = ["未收运", "已收运"];

// 获取收运记录全量数据
const collectRecordList = ref([]);
const loadCollectRecordList = async () => {
    const taskId = route.query.id;
    const params = {
        page: {
            current: 0,
            size: -1,
        },
        customQueryParams: {
            taskId,
            type: 1,
        },
    };
    const res = await getCollectionRecordList(params);
    if (res.success) {
        collectRecordList.value = res.data.records;
    }
};
loadCollectRecordList();

const columns = [
    { title: "序号", slot: "serialNumber", width: 80, tooltip: true },
    { title: "收运点", key: "pointName", width: 120, tooltip: true },
    { title: "垃圾桶数", key: "num", tooltip: true },
    { title: "收运情况", slot: "status", tooltip: true },
    { title: "收集垃圾桶数量", minWidth: 120, key: "doneGarbageNum", tooltip: true },
    { title: "收集重量(kg)", key: "weight", width: 120, tooltip: true },
    { title: "收运详情", key: "content", minWidth: 110, tooltip: true },
    { title: "收运人", key: "contactPerson", width: 80, tooltip: true },
    { title: "收集时间", key: "time", minWidth: 120, tooltip: true },
    { title: "操作", slot: "action", width: 80, lock: true, fixed: true },
];

// 弹窗组件相关

const modalId = ref(-1);
const modalRef = ref(null);
const addModalRef = ref(null);
const check = (row: any) => {
    // if (row.id) {
    //     modalId.value = row.id;
    //     modalRef.value?.handleOpen();
    // }
    router.push({
        name: "kitchenWasteManagement:collectionTask:collection",
        query: {
            id: row.id,
            planId: row.planId,
            detailId: route.query.id,
        },
    });
};
const onCloseModal = () => {
    clearModalInfo();
    loadCollectRecordList();
    onInit();
};
</script>

<style lang="less" scoped>
:deep(.tab-title) {
    justify-content: flex-start;
    padding-bottom: 16px;
    height: 48px;
    column-gap: 20px;
}
</style>
