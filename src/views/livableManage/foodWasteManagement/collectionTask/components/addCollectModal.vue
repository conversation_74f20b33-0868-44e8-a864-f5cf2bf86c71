<template>
    <BreadcrumbCustom>
        <BreadcrumbItem>餐厨垃圾管理</BreadcrumbItem>
        <BreadcrumbItem to="/kitchenWasteManagement/collectionTask">收运任务</BreadcrumbItem>
        <BreadcrumbItem
            v-show="$route.query.detailId"
            :to="`/kitchenWasteManagement/collectionTaskDetail?id=${$route.query.detailId}`"
            >详情</BreadcrumbItem
        >
        <BreadcrumbItem v-show="$route.query.origin !== '收运记录'">收运</BreadcrumbItem>
    </BreadcrumbCustom>
    <detailCard
        @on-back="$router.back()"
        :is-back-btn="true"
        :src="require('@/assets/images/icon_detail.png')"
        title="收运信息"
    >
        <Row :gutter="80">
            <Col span="8">
                <s-label label="收运计划" :value="modalInfo.planName"></s-label>
            </Col>
            <Col span="8">
                <s-label label="收运任务" :value="modalInfo.name"></s-label>
            </Col>
            <Col span="8">
                <s-label label="任务时间" :value="modalInfo.taskTime"></s-label>
            </Col>
            <Col span="8">
                <s-label label="收运人" :value="modalInfo.contactPerson"></s-label>
            </Col>
            <Col span="8">
                <s-label label="收集车辆" :value="modalInfo.carLicenseNo"></s-label>
            </Col>
            <Col span="8">
                <s-label label="收运公司" :value="modalInfo.companyName"></s-label>
            </Col>
        </Row>
        <SingleTitle
            title="收运填报"
            :src="require('@/assets/images/icon-收运填报.png')"
        ></SingleTitle>
        <Form ref="formRef" :model="modalInfo" :rules="modalRules">
            <Row gutter="80" justify="space-between">
                <Col span="8">
                    <FormItem label="收运点">
                        <selected-by-list
                            v-model="modalInfo.pointId"
                            url="/garbageTaskRecords/list"
                            value-name="pointId"
                            label-name="pointName"
                            :type="1"
                            @on-change="onPointChange"
                            :params="queryListParams"
                            :disabled="route.query.detailId"
                        ></selected-by-list>
                    </FormItem>
                </Col>
                <Col span="8">
                    <FormItem label="所属企业">
                        <Input
                            v-model="modalInfo.enterpriseName"
                            placeholder="系统自动带出"
                            disabled
                        />
                    </FormItem>
                </Col>
                <Col span="8">
                    <FormItem label="收运时间">
                        <date-picker v-model="formatDateTime" type="datetime"></date-picker>
                    </FormItem>
                </Col>
            </Row>
            <Row>
                <Col span="24">
                    <div class="title">收运数量</div>
                    <div class="trashcan-wrapper">
                        <Row gutter="80" justify="flex-start">
                            <template v-for="(item, index) in modalInfo.garbageCans">
                                <Col span="8">
                                    <FormItem
                                        :label="item.name"
                                        :prop="`garbageCans[${index}].weight`"
                                        :rules="garbageCansRule"
                                    >
                                        <Input clearable v-model="item.weight" placeholder="请输入">
                                            <template #append> kg </template>
                                        </Input>
                                    </FormItem>
                                </Col>
                            </template>
                        </Row>
                    </div>
                </Col>
            </Row>
        </Form>
        <h3 style="font-size: 16px;margin: 16px 0;">收运点收运记录</h3>
        <baseTable :columns="columns" ref="listRef" :data="[{num:1}]" :show-page="false">
            <!-- <template #serialNumber="{ row, index }">
                        {{index + 1}}
                    </template>
                    <template #status="{ row }">
                        {{ statusEnum[row.status] }}
                    </template> -->
            <template #operation="{ row }">
                    <LinkBtn size="small" @click="openModal(row)">编辑</LinkBtn>
                    <LinkBtn size="small" @click="onDelete(row)">删除</LinkBtn>
            </template>
        </baseTable>
        <div class="footer">
            <Button @click="$router.back()"> 取消 </Button>
            <Button type="primary" @click="handleConfirm"> 确定 </Button>
        </div>
    </detailCard>
    <SModal @on-cancel="isShow = false" @on-confirm="editPointRecord" :isShow="isShow" :option="{
        title:'编辑收运记录'
    }">
    <shipmentRecords :data="modalData"></shipmentRecords>
    </SModal>
</template>

<script lang="ts" setup>
import { computed, defineExpose, defineProps, getCurrentInstance, ref } from "vue";
import moment from "moment";
import SModal from '@/components/common/modal'
import shipmentRecords from './modalContent.vue'
import { useEdit } from "../useEdit";
import {
    editReceiptAndShipmentRecords,
    getCollectionJobInfo,
    getGarbageTaskInfo,
    delReceiptAndShipmentRecords,
} from "@/api/livableManage/foodWasteManagementService";
import { DatePicker } from "view-ui-plus";
import SelectedByList from "@/components/common/selectByList";
import { ICollectRecordDetail } from "@/views/livableManage/garbageCollectionManagement/collectionTask/type";
import { isNullOrEmpty } from "@/utils/tool";
import Util from "@/utils";
import SingleTitle from "@/components/common/singleTitle/index.vue";
import { useRoute } from "vue-router";
import router from "@/router/livable";
const route = useRoute();
const that = getCurrentInstance()?.appContext.config.globalProperties;
const { modalRules, modalInfo } = useEdit();

const formRef = ref();
interface IModalProps {
    id: number | string; // 任务id
    isDetail: string;
    planId: string;
}
const props = defineProps<IModalProps>();

const columns = ref<any>([
    { title: "序号", type: "index",width:60 },
    { title: "收集垃圾桶数量", key: "num" },
    { title: "收集重量", key: "num" },
    { title: "收运人", key: "num" },
    { title: "收运车辆", key: "num" },
    { title: "收集时间", key: "num" },
    { title: "操作", slot: "operation", width: 100, fixed: "right" },
]);
const queryListParams = ref({
    taskId: route.query.detailId ? null : route.query.id,
    status: route.query.detailId ? null : 0, // 在详情里面的时候不管是否填报，都要带出来，不在详情的时候只带出未填报的
});
// 根据任务id查的pointId，数据是根据任务id查的任务详情，选择pointId之后，调用任务记录编辑接口，传的id是记录id？
// 记录id从哪里来？应该从pointId选择的时候来的
const formatDateTime = computed({
    get() {
        if (modalInfo.value.time) {
            return moment(modalInfo.value.time).toDate();
        } else {
            return new Date();
        }
    },
    set(newDateTime) {
        console.log(newDateTime);
        if (newDateTime) {
            modalInfo.value.time = moment(newDateTime).format("YYYY-MM-DD HH:mm:ss");
        } else {
            modalInfo.value.time = "";
        }
    },
});
const onInitModal = async () => {
    let res: any;
    if (route.query.detailId) {
        res = await getGarbageTaskInfo(route.query.id);
        if (res.success) {
            modalInfo.value = res.data;
            modalInfo.value.name = res.data.taskName;
            pointRecordDetail.value = Util.objClone(res.data);
        }
    } else {
        res = await getCollectionJobInfo(route.query.id);
        if (res.success) {
            const taskTime = res.data.time; // 调任务详情接口查的time是任务时间
            delete res.data.time;
            modalInfo.value = res.data;
            modalInfo.value.taskTime = taskTime;
        }
    }
};
onInitModal();

const garbageCansRule = [
    {
        pattern: /^(([1-9]\d{1,2}(\.\d{1,2})?)|\d((\.\d{1,2})?)|1000|1000.0|1000.00)$/,
        message: "数值范围0-1000",
    },
];

// 当选择收运点的时候，自动带出 收运点的详情信息给到所属企业和垃圾桶数量
const pointRecordDetail = ref<ICollectRecordDetail>();
const isLoadingDetail = ref(false);
const queryPointDetail = async (id: number) => {
    if (id) {
        if (isLoadingDetail.value) return;
        isLoadingDetail.value = true;
        const res: any = await getGarbageTaskInfo(route.query.id);
        isLoadingDetail.value = false;
        if (res.success) {
            modalInfo.value.enterpriseName = res.data.enterpriseName;
            modalInfo.value.garbageCans = res.data.garbageCans || [];
            pointRecordDetail.value = Util.objClone(res.data);
        }
    }
};

// 根据收运点id查收运点的详情
const onPointChange = async (data: any) => {
    const { id } = data;
    await queryPointDetail(id);
};

const checkGarbageCansChange = (): boolean => {
    if (isNullOrEmpty(pointRecordDetail.value?.garbageCans)) {
        return true;
    }
    if (
        JSON.stringify(pointRecordDetail.value?.garbageCans) ===
        JSON.stringify(modalInfo.value.garbageCans)
    ) {
        return false;
    }
    return true;
};

const handleConfirm = async () => {
    if (!checkGarbageCansChange()) {
        that?.$Message.warning("至少填写一个垃圾桶");
        return;
    }
    const isValidated = await formRef.value.validate();
    const id = pointRecordDetail.value?.id;

    if (isValidated && id) {
        const { time, garbageCans } = modalInfo.value;
        const params = { time, garbageCans, id, type: 1, status: 1 };
        const res: any = await editReceiptAndShipmentRecords(params);
        if (res.success) {
            that && that.$Message.success("收运成功");
            router.back();
        }
        return res.success;
    }
};
const isShow = ref<boolean>(false)
const modalData = ref<any>()
const openModal = (row: any) => {
    isShow.value = true
    modalData.value = row
    // if (row.id) {
    //     modalId.value = row.id;
    //     modalRef.value?.handleOpen();
    // }
};
// 编辑收运点记录
const editPointRecord = ()=>{
    isShow.value = false
}
// 删除选中数据
const onDelete = async (row) => {
    if (row.id) {
        that &&
            that.$Modal.confirm({
                title: "提示",
                content: "您确定要删除选中的数据吗",
                onOk: () => {
                    delReceiptAndShipmentRecords({ ids: [row.id] }).then((res: any) => {
                        if (res.success) {
                            that.$Message.success("删除成功");
                        }
                    });
                },
            });
    }
};
</script>

<style lang="less" scoped>
.footer {
    margin-top: 20px;
}
:deep(.ivu-icon-ios-arrow-down) {
    color: #4e627e !important;
}
:deep(.ivu-form-item) {
    width: 100% !important;
}
:deep(.ivu-form-item-label) {
    padding-top: 0;
    padding-bottom: 8px;
}
.title {
    min-height: 34px;
    line-height: 22px;
    color: #798799;
    font-size: 14px;
    padding-right: 8px;
}
.trashcan-wrapper {
    width: 100%;
    background-color: #f8fafb;
    padding: 16px;
}
.title {
    display: flex;
    .title-icon-img {
        width: 24px;
        height: 24px;
        margin-right: 8px;
        img {
            width: 100%;
            height: 100%;
        }
    }
    h5 {
        margin-bottom: 0;
    }
}
</style>
