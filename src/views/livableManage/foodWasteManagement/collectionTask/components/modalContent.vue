
<script lang="ts" setup>
import { computed, defineExpose, defineProps, getCurrentInstance, ref } from 'vue';
import moment from 'moment'
import { useEdit } from '../useEdit'
import {
    editReceiptAndShipmentRecords,
    getGarbageTaskInfo
} from '@/api/livableManage/foodWasteManagementService';
import { DatePicker } from 'view-ui-plus';
import SelectedByList from "@/components/common/selectByList";
import {
    ICollectRecordDetail
} from "@/views/livableManage/garbageCollectionManagement/collectionTask/type";
import { isNullOrEmpty } from "@/utils/tool";
import Util from "@/utils";
import SingleTitle from "@/components/common/singleTitle/index.vue";

const that = getCurrentInstance()?.appContext.config.globalProperties;
const { modalRules, modalInfo } = useEdit()

const formRef = ref()
interface IModalProps{
    id: number | string; // 任务id
    isDetail: boolean;
    collectRecordId: number | string; // 收运记录id
    data:any;
}
const props = defineProps<IModalProps>()
const queryListParams = ref({
    taskId: props.id,
    status: props.isDetail ? undefined : 0 // 在详情里面的时候不管是否填报，都要带出来，不在详情的时候只带出未填报的
})

const formatDateTime = computed({
    get() {
        if (modalInfo.value.time) {
            return moment(modalInfo.value.time).toDate()
        } else {
            return new Date();
        }
    },
    set(newDateTime) {
        console.log(newDateTime)
        if (newDateTime) {
            modalInfo.value.time = moment(newDateTime).format('YYYY-MM-DD HH:mm:ss')
        } else {
            modalInfo.value.time = '';
        }
    }
})

const onInitModal = async(id:number | string) => {
    const res:any = await getGarbageTaskInfo(id)
    if (res.success) {
        modalInfo.value = res.data;
    }
}
onInitModal(props.id)

const garbageCansRule = [{
    pattern: /^(([1-9]\d{1,2}(\.\d{1,2})?)|\d((\.\d{1,2})?)|1000|1000.0|1000.00)$/,
    message: '数值范围0-1000'
}]

// 当选择收运点的时候，自动带出 收运点的详情信息给到所属企业和垃圾桶数量
const pointRecordDetail = ref<ICollectRecordDetail>()
const isLoadingDetail = ref(false);
const queryPointDetail = async(id: number | string) => {
    if (id) {
        if(isLoadingDetail.value) return;
        isLoadingDetail.value = true;
        const res = await getGarbageTaskInfo(id)
        isLoadingDetail.value = false;
        if (res.success) {
            const { enterpriseName, garbageCans = [], pointId } = <ICollectRecordDetail>res.data;
            pointRecordDetail.value = res.data;
            modalInfo.value.pointId = pointId;
            modalInfo.value.enterpriseName = enterpriseName;
            modalInfo.value.garbageCans = Util.objClone(garbageCans);
        }
    }
}
queryPointDetail(props.collectRecordId);

const onPointChange = async(data:any) => {
    const { id } = data;
    await queryPointDetail(id);
}

const checkGarbageCansChange = ():boolean => {
    if (isNullOrEmpty(pointRecordDetail.value?.garbageCans)){
        return true;
    }
    if (JSON.stringify(pointRecordDetail.value?.garbageCans) === JSON.stringify(modalInfo.value.garbageCans)){
        return false;
    }
    return true;
}
// 提交
const handleConfirm = async() => {
    if(!checkGarbageCansChange()){
        that?.$Message.warning('至少填写一个垃圾桶');
        return;
    }
    const isValidated = await formRef.value.validate();
    const id = props.id;
    if (isValidated && id) {
        const { time, garbageCans } = modalInfo.value;
        const params = { time, garbageCans, id, type: 1, status: 1 }
        const res:any = await editReceiptAndShipmentRecords(params);
        if (res.success) {
            that && that.$Message.success('收运成功')
        }
        return res.success;
    }
}


defineExpose({ handleConfirm });

</script>


<template>
    <div>
        <div class="title">
            <div class="title-icon-img">
                <img :src="require('@/assets/images/icon_detail.png')" alt="" />
            </div>
            <Title level="5">收运信息</Title>
        </div>
        <Row gutter="24">
            <Col span="12">
                <s-label label="收运计划" :value="modalInfo.planName"></s-label>
            </Col>
            <Col span="12">
                <s-label label="收运任务" :value="modalInfo.taskName"></s-label>
            </Col>
            <Col span="12">
                <s-label label="任务时间" :value="modalInfo.time"></s-label>
            </Col>
            <Col span="12">
                <s-label label="收运人" :value="modalInfo.contactPerson"></s-label>
            </Col>
            <Col span="12">
                <s-label label="收集车辆" :value="modalInfo.carLicenseNo"></s-label>
            </Col>
            <Col span="12">
                <s-label label="收运公司" :value="modalInfo.companyName"></s-label>
            </Col>
        </Row>
        <SingleTitle
            title="收运填报"
            :src="require('@/assets/images/icon-收运填报.png')"
        ></SingleTitle>
        <Form ref="formRef" :model="modalInfo" :rules="modalRules">
            <Row gutter="24" justiry="space-between">
                <Col span="12">
                    <FormItem label="收运点">
                        <selected-by-list
                            v-model="modalInfo.pointId"
                            url="/garbagePoint/list"
                            :type="1"
                            @on-change="onPointChange"
                            :params="queryListParams"
                            disabled
                        ></selected-by-list>
                    </FormItem>
                </Col>
                <Col span="12">
                    <FormItem label="所属企业">
                        <Input
                            v-model="modalInfo.enterpriseName"
                            placeholder="系统自动带出"
                            disabled
                        />
                    </FormItem>
                </Col>
                <Col span="12">
                    <FormItem label="收运时间">
                        <date-picker v-model="formatDateTime" type="datetime"></date-picker>
                    </FormItem>
                </Col>
                <Col span="24">
                    <div class="title">收运数量</div>
                    <div class="trashcan-wrapper">
                        <Row gutter="24" justify="flex-start">
                            <template v-for="(item, index) in modalInfo.garbageCans">
                                <Col span="12">
                                    <FormItem
                                        :label="item.name"
                                        :prop="`garbageCans[${index}].weight`"
                                        :rules="garbageCansRule"
                                    >
                                        <Input v-model="item.weight" clearble placeholder="请输入">
                                            <template #append> kg </template>
                                        </Input>
                                    </FormItem>
                                </Col>
                            </template>
                        </Row>
                    </div>
                </Col>
            </Row>
        </Form>
    </div>
</template>

<style lang="less" scoped>
:deep(.ivu-icon-ios-arrow-down){
    color: #4E627E !important;
}
:deep(.ivu-form-item) {
    width: 100% !important;
}
:deep(.ivu-form-item-label){
    padding-top: 0;
    padding-bottom: 8px;
}
.title{
    min-height: 34px;
    line-height: 22px;
    color: #798799;
    font-size: 14px;
    padding-right: 8px;
}
.trashcan-wrapper{
    width: 100%;
    background-color: #F8FAFB;
    padding: 16px;
}
.title{
    display: flex;
    .title-icon-img{
        width: 24px;
        height: 24px;
        margin-right: 8px;
        img{
            width: 100%;
            height: 100%;
        }
    }
    h5{
        margin-bottom: 0;
    }
}

</style>