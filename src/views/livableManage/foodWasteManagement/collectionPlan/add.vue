<template>
    <BreadcrumbCustom>
        <BreadcrumbItem>餐厨垃圾管理</BreadcrumbItem>
        <BreadcrumbItem :replace="true" to="/kitchenWasteManagement/collectionPlan">
            收运计划
        </BreadcrumbItem>
        <BreadcrumbItem>新增</BreadcrumbItem>
    </BreadcrumbCustom>

    <detailCard ref="detail" @on-back="cancel" :is-back-btn="true" title="新建收运计划">
        <editBox ref="editRef" />
        <div class="btn-box">
            <Button type="primary" @click="submitFun" :loading="loading">提交</Button>
            <Button @click="cancel">取消</Button>
</div>
    </detailCard>
</template>

<script lang="ts">
import { defineComponent, ref } from 'vue';
import editBox from '../../garbageCollectionManagement/collectionPlan/components/edit.vue'
import { useRouter } from 'vue-router';
import useLoading from '@/hooks/loading';
export default defineComponent({
    components: {
        editBox
    },
    setup() {
        const router = useRouter();
        const editRef = ref()
        const { loading, setLoading } = useLoading();
        const cancel = () => { router.back() }
        const submitFun = async() => {
            const res = await editRef.value.submitFun()
            if (res) router.back()
        }
        return {
            cancel,
            submitFun,
            loading,
            editRef,
        }
    }
})
</script>
