<template>
    <div>
        <BreadcrumbCustom>
            <BreadcrumbItem>餐厨垃圾管理</BreadcrumbItem>
            <BreadcrumbItem>收运计划</BreadcrumbItem>
        </BreadcrumbCustom>
        <ContentCard title="收运计划">
            <BaseForm :model="formData" :label-width="90" @handle-submit="handleSubmit">
                <template #formitem>
                    <FormItem label="计划名称" prop="planName">
                        <Input v-model="formData.planName" maxlength="20" clearable placeholder="请输入"></Input>
                    </FormItem>
                    <FormItem label="状态" prop="planStatus">
                        <Select v-model="formData.planStatus" clearable placeholder="请选择">
                            <Option v-for="(item, index) in enumeration.deviceUesState" :value="index" :key="index">
                                {{ item }}
                            </Option>
                        </Select>
                    </FormItem>
                    <FormItem label="收运公司" prop="companyId">
                        <collect-company-select v-model="formData.companyId" :type="1" />
                    </FormItem>
                </template>
            </BaseForm>
            <btn-card>
                <Button type="primary" @click="handleAdd" v-auth="'kitchenWasteManagement:collectionPlan:add'"
                    icon="md-add">
                    新增
                </Button>
                <Button v-auth="'kitchenWasteManagement:collectionPlan:del'" icon="ios-trash" @click="handleDel">
                    删除
                </Button>
            </btn-card>
            <base-table ref="coverTb" :columns="columns" url="/garbagePlan/list" @on-selection-change="selectionChange">
                <template #planStatus="{ row }">
                    <useStatus :value="row.planStatus" />
                </template>
                <!--    收运周期  -->
                <template #timeType="{ row }">
                    <span v-if="row.timeType == 1">每天</span>
                    <tooltip-auto-show v-if="row.timeType == 2" :content="`每周${row.timeValues.split(',').map((k: string) =>
                            weekList[+k] || '').filter((k: string) => k).join(',')}`" placement="top">
                        每周{{ row.timeValues.split(',').map((k: string) =>
                            weekList[+k] || '').filter((k: string) => k).join(',') }}
                    </tooltip-auto-show>

                    <tooltip-auto-show v-if="row.timeType == 3" :content="`每月${row.timeValues}号`" placement="top">
                        每月{{ row.timeValues }}号
                    </tooltip-auto-show>
                </template>
                <template #action="{ row }">
                    <LinkBtn size="small" @click="toDetail(row)">查看</LinkBtn>
                    <LinkBtn size="small" @click="editUseState(row)"
                        v-auth="'kitchenWasteManagement:collectionPlan:changeStatus'">
                        {{ row.planStatus == 1 ? '停用' : '启用' }}
                    </LinkBtn>
                </template>
            </base-table>
        </ContentCard>
    </div>
</template>

<script lang="ts">
import { defineComponent, ref, onMounted, nextTick, reactive } from 'vue';
import { manholeCoverService } from '@/api/manholeCoverService';
import { columnList } from './data/columns';
import { Modal, Message } from 'view-ui-plus';
import { useRouter } from 'vue-router';
import { enumeration } from '@/config/enumeration'
import Util from '@/utils';
import collectCompanySelect from '../../garbageCollectionManagement/components/collectCompanySelect/index'
import { garbagePlanStatus, garbagePlanDel } from '@/api/livableManage/foodWasteManagementService';
export default defineComponent({
    components: {
        collectCompanySelect
    },
    setup() {
        const router = useRouter()
        const coverTb = ref()
        const formData = ref({
            planStatus: '',
            planName: '',
            companyId: '',
            type: 1
        })
        const weekList = ['', '一', '二', '三', '四', '五', '六', '日']
        const common = reactive({
            deviceOnlineState: enumeration.deviceOnlineState,
            deviceUesState: enumeration.deviceUesState,
            selectList: []
        })
        // 提交表单
        const handleSubmit = (data: any) => {
            let param = Util.objClone(data)
            coverTb.value.search(param)
        }
        const columns = columnList();
        // 删除
        const handleDel = () => {
            if (common.selectList.length === 0) {
                Message.warning('最少选择一条数据')
                return
            }
            Modal.confirm({
                title: '操作确认',
                content: '您确定要删除选中的数据吗？',
                loading: true,
                onOk: () => {
                    let arr: any[] = []
                    common.selectList.forEach((item: any) => {
                        arr.push(item.id)
                    })
                    delAjax(arr)
                }
            });
        }
        const delAjax = (arr: any) => {
            garbagePlanDel({ ids: arr }).then((res: any) => {
                if (res.success) {
                    Message.success('删除成功')
                    common.selectList = []
                    handleSubmit(formData.value)
                }
            }).finally(() => {
                Modal.remove()
            })
        }
        // 修改使用状态
        const editUseState = (row: any) => {
            let state = row.planStatus
            let str = ''
            if (state == 1) {
                state = 0
                str = `您确定要停用【${row.planName}】计划吗?`
            } else {
                state = 1
                str = '确定启用后，将在次日开始，按照收运计划的收运周期，动态生成收运任务。'
            }
            Modal.confirm({
                title: '提示',
                content: str,
                onOk: () => {
                    garbagePlanStatus({ id: row.id, planStatus: state })
                        .then((res: any) => {
                            if (res.success) {
                                Message.success('操作成功')
                                handleSubmit(formData.value)
                            }
                        }).finally(() => {
                            Modal.remove()
                        })
                }
            })

        }
        // // 新建
        const handleAdd = () => {
            router.push({
                path: '/kitchenWasteManagement/collectionPlanAdd',
                query: {
                    typecode: 1
                }
            })
        }
        const addSubmit = (list: any) => {
            let param: any = {}
            param.deviceCodes = []
            list.forEach((item: any) => {
                param.deviceCodes.push(item.deviceId)
            })
            manholeCoverService.addManholes(param)
                .then((res: any) => {
                    if (res.success) {
                        Message.success('操作成功')
                        coverTb.value.search()
                    }
                })
        }
        // 查看
        const toDetail = (row: any) => {
            router.push({
                path: '/kitchenWasteManagement/collectionPlanDetail',
                query: {
                    id: row.id,
                    typecode: 1
                }
            })
        }
        const selectionChange = (list: any) => {
            common.selectList = list || []
        }
        onMounted(() => {
            nextTick(() => {
                handleSubmit(formData.value)
            })
        })
        return {
            coverTb,
            formData,
            handleSubmit,
            columns,
            handleDel,
            addSubmit,
            toDetail,
            selectionChange,
            editUseState,
            handleAdd,
            enumeration,
            weekList
        };
    }
});
</script>

<style lang="less" scoped>


/deep/.ivu-tooltip {
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    width: 100%;
    display: flex;

    .ivu-tooltip-rel {
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
        width: 100%;
    }
}
</style>
