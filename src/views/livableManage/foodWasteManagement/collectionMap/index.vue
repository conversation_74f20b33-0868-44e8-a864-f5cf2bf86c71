<template>
    <BreadcrumbCustom>
        <BreadcrumbItem>餐厨垃圾管理</BreadcrumbItem>
        <BreadcrumbItem>收运地图</BreadcrumbItem>
    </BreadcrumbCustom>
    <collect-map-component type="1" />
</template>
<script lang="ts">
import { defineComponent } from 'vue';
import collectMapComponent from '../../garbageCollectionManagement/components/collectMapComponent/index.vue';
export default defineComponent({
    components: {
        collectMapComponent
    }
});
</script>
<style lang="less" scoped></style>
