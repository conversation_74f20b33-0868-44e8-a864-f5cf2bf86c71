import { ref, getCurrentInstance } from 'vue';
import { validateform } from '@/utils/validateform';
import { useRoute, useRouter } from 'vue-router';
import {
    getGarbageInfo
} from '@/api/livableManage/foodWasteManagementService';
import {
    IEnterpriseReport
} from './type/index';

const createEnterpriseReportInfo = (): IEnterpriseReport => {
    return {
        name: '', // 企业名称
        code: '', // 企业编码
        area: '', // 所属区域
        address: '', // 具体位置
        office: '', // 办事处
        areaSize: '', // 经营面积
        category: '', // 经营种类
        operateTime: '', // 营业时间
        objx: 0, // 经度
        objy: 0, // 纬度
        legalPerson: '', // 企业法人
        legalPhone: '', // 法人联系方式
        contactPhone: '', // 联系人
        contactPerson: '', // 联系人方式
        contactType: '', // 企业类型
        note: '', // 备注
        license: '', // 执照代码
        duration: '', // 申报时长
        garbageNum: '', // 垃圾产生量
        num: '', // 垃圾桶数量
        amount: '', // 押金金额
        carLicenseNo: '', // 收集车辆车牌号
        collectTime: '', // 收集时间段
        startTime: '', // 开始收集时间
        isNormal: '', // 是否正常收集
        contractUrl: '', // 收运合同照片
        areaPath: '', // 区域位置
        // fileUrl: '', // 附件
    }
}
const formRules = {
    name: validateform.required,
    objx: { required: true, message: '请完善必填项' },
    areaSize: [
        {
            required: true,
            message: '请完善必填项'
        }, {
            pattern: /^[0-9]*$/,
            message: '请输入整数'
        }],
    garbageNum: [{
        pattern: /^[0-9]*$/,
        message: '请输入整数',
        trigger: 'blur'
    }],
    num: [{
        required: true,
        message: '请完善必填项'
    }, {
        pattern: /^([0-9]|10)$/,
        message: '请输入大于0，10以内的正整数',
        trigger: 'blur'
    }],
    category: validateform.selectRequired,
    operateTime: validateform.required,
    duration: validateform.required,
    license: [{
        pattern: /^[\da-zA-Z]{18}$/,
        message: '请输入18位营业执照代码',
        trigger: 'blur'
    }],
    legalPhone: [{ pattern: /^1[3456789]\d{9}$/, trigger: 'blur', message: '手机号错误' }],
    contactPhone: [{ pattern: /^1[3456789]\d{9}$/, trigger: 'blur', message: '手机号错误' }]
}
const approvalRules = {
    startTime: [{ required: true, message: '请完善必填项', trigger: 'blur' }]
}
// ^[0-9a-zA-Z]{18}$ 18位字符正则
// /^(?:(?![IOZSV])[\dA-Z]){2}\d{6}(?:(?![IOZSV])[\dA-Z]){10}$/  营业执照18位正则
export const useEdit = () => {
    const isEdit = ref(false);
    const detailInfo = ref<IEnterpriseReport>(createEnterpriseReportInfo());
    const isLoading = ref(false);
    const uploadLoading = ref(false);
    const that = getCurrentInstance()?.appContext.config.globalProperties
    const router = useRouter();
    const route = useRoute();

    const onInit = async(id: string | undefined) => {
        if (isEdit.value) {
            console.log('onEdit', id);
            const res = await getGarbageInfo({ id })
            console.log(res);
        }
    }


    return { detailInfo, isEdit, formRules, router, route, onInit, isLoading, uploadLoading, approvalRules, that }
}

