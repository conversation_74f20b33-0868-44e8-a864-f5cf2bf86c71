export interface IEnterpriseReport{
    id?: string, // id
    enterpriseId?:number //关联企业id
    name: string, // 企业名称
    code?: string, // 企业编码
    area: string, // 所属区域
    address: string, // 具体位置
    office: string, // 办事处
    areaSize: string, // 经营面积
    category: string, // 经营种类
    objx: number, // 经度
    objy: number, // 纬度
    legalPerson: string, // 企业法人
    legalPhone: string, // 法人联系方式
    contactPhone: string, // 联系人
    contactPerson: string, // 联系人方式
    contactType: string, // 企业类型
    note?: string, // 备注
    license: string, // 执照代码
    duration: string, // 申报时长
    garbageNum: string, // 垃圾产生量
    num: string, // 垃圾桶数量
    operateTime: string, // 营业时间
    approvalStatus?: number, // 审核状态

    amount?: string, // 押金金额
    carId?: string,
    carLicenseNo?: string, // 收集车辆车牌号
    collectTime?: string, // 收集时间段
    startTime?: string, // 开始收集时间
    isNormal?: string, // 是否正常收集
    contractUrl?: string, // 收运合同照片
    areaPath?: string,  //区域位置
    gdx?: number, // 经度
    gdy?: number, // 纬度
    fileUrl?: string, // 附件

}
