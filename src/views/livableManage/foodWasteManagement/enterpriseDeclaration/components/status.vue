<script lang="ts" setup>
import { defineProps } from "vue";
import { AuditStatusEnum } from "../enum/index";

const props = defineProps({
    value: {
        default: -1,
    },
});
const backgroundEnum = {
    0: "#F2F3F5",
    1: "#E8FFFB",
    2: "#FFECE8",
};
const textColorEnum = {
    0: "#4E627E",
    1: "#0FC6C2",
    2: "#F53F3F",
};
const iconClassNameEnum = {
    0: "icon-info-circle-fill1",
    1: "icon-check-circle-fill1",
    2: "icon-close-circle-fill1",
};
</script>
<template>
    <s-tag
        class="s-tag-status"
        :background="backgroundEnum[props.value]"
        :color="textColorEnum[props.value]"
    >
        <i style="font-size: 12px" :class="['iconfont', iconClassNameEnum[props.value]]"></i>
        {{ AuditStatusEnum[props.value] }}
    </s-tag>
</template>

<style lang="less" scoped>
.s-tag-status {
    width: 74px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    column-gap: 5px;
    border-radius: 2px;
}
</style>
