<style lang="less">
.search-select-poptip {
    width: 100%;
    .ivu-poptip-rel {
        width: 100%;
    }
}
.search-select-poptip-popper {
    .ivu-poptip-body-content {
        white-space: normal;
    }
}
.dropdown-title {
    padding: 5px 10px;
    color: #e1e1e1;
    background-color: #f8f8f8;
}
.dropdown-content {
    .content-list {
        margin: 0;
        padding: 0;
        .list-item {
            margin: 0;
            padding: 5px 10px ;
            list-style: none;
            &:hover {
                cursor: pointer;
                background-color: #f7f5f5;
            }
            .item-avatar {
                display: inline-block;
                width: 30px;
                height: 30px;
                line-height: 30px;
                color: #ffffff;
                background-color: #b6c4de;
                border-radius: 50%;
                text-align: center;
                margin-right: 10px;
            }
            .item-name {
                display: inline-block;
                color: #666666;
            }
        }
    }
    .content-msg {
        padding: 10px;
        color: #cccccc;
    }
}

</style>
<template>
  <Poptip v-model="visible" width="500" @on-popper-show="onPopperShow" transfer  :disabled="disabled"
    placement="bottom-start"
    class="search-select-poptip" popper-class="search-select-poptip-popper">
      <Input v-model="inputValue" @on-change="inputValueChange" clearable :disabled="disabled"
        :suffix="visible ? 'ios-arrow-up' : 'ios-arrow-down'" @on-clear="clear"></Input>
      <template #content>
          <div class="input-dropdown">
            <div class="dropdown-content">
                <ul class="content-list" v-for="(item, index) in enterPriseList" v-if="enterPriseList.length > 0" @click="select(item)">
                    <li class="list-item" >
                        <span class="item-name" :value="item.id" >{{item.name}}</span>
                    </li>
                </ul>
                <!-- 没有数据时的提示信息 -->
                <div class="content-msg" v-else>
                    暂无数据
                </div>
            </div>
        </div>

      </template>
      
  </Poptip>

</template>

<script>
import Util from "@/utils";
import { getEnterpriseList } from '@/api/livableManage/foodWasteManagementService';
const defaultSearchObj = {
    name: '',
    type: 1
}
export default {
    name: 'DeviceCodeSelect',
    props: {
      modelValue: {
        type: [Number, String],
        default: null
      },
      disabled: {
        type: Boolean,
        default: false
      }
    },
    data() {
        return {
            visible: false,
            inputValue: '',
            searchObj: Util.objClone(defaultSearchObj),
            enterPriseList:[]
        }
    },
    watch: {
      modelValue(newVal) {
        console.info("====================")
        this.inputValue = newVal;
      }
    },
    created() {
      console.info("=========34343===========")
        this.inputValue = this.modelValue;
    },
    methods: {
        onPopperShow() {
            if (!this.firstShow) {
                this.firstShow = true;
                this.search();
            }
        },
        showPop() {
            this.visible = true
        },
        autoSearch() {
          if(this.debounced) {
            this.debounced.cancel()
          }
          this.debounced = _.debounce(this.search, 300)
          this.debounced()
        },
        search() {
            let params = {
                page: {
                    current: 0,
                    size: -1
                },
                customQueryParams: {
                    type: 1
                }
            }
            getEnterpriseList(params).then(res =>{
                if(res.success){
                    this.enterPriseList = res.data.records
                }
            })
            // this.$refs.baseTb.search(this.searchObj)
		    },
        reset() {
            this.searchObj = Util.objClone(defaultSearchObj)
            this.search()
        },
        inputValueChange() {
          this.$emit('update:modelValue', this.inputValue);
          this.$emit('change', this.inputValue);
          // this.searchObj.code = this.inputValue;
          this.autoSearch();
        },
        select(row) {
            this.inputValue = row.name;
            this.$emit('update:modelValue', row.name);
            this.$emit('change', row);
            this.visible = false;
        },
        clear() {
            this.inputValue = '';
            this.$emit('update:modelValue', '');
            this.$emit('change', '');
        }
    }
}
</script>
