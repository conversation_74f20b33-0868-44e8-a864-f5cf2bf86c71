<template>
    <Row :gutter="80">
        <Col span="8">
            <s-label label="企业名称" :value="detailInfo.name">
            </s-label>
        </Col>
        <Col span="8">
            <s-label label="企业编号" :value="detailInfo.code"></s-label>
        </Col>
        <Col span="8">
            <s-label label="审核状态">
            <template #value>
                 <auditStatus :value="detailInfo?.approvalStatus"></auditStatus>
            </template>
            </s-label>
        </Col>
        <Col span="8">
            <s-label label="经纬度">
                <template #value>
                    <jwd-map :obj-info="formatPositionPoint"></jwd-map>
                </template>
            </s-label>
        </Col>
        <Col span="8">
            <s-label label="所在区域" :value="formatAreaStr(detailInfo.area)"></s-label>
        </Col>
        <Col span="8">
            <s-label label="具体位置" :value="detailInfo.address"></s-label>
        </Col>
        <Col span="8">
            <s-label
                label="办事处"
                :value="$store.getters.dictionary.office_code[detailInfo.office] || ''"
            />
        </Col>
        <Col span="8">
            <s-label label="经营面积">
                <template #value>
                    {{ detailInfo.areaSize ? `${detailInfo.areaSize} ㎡` : "--" }}
                </template>
            </s-label>
        </Col>
        <Col span="8">
            <s-label
                label="经营种类"
                :value="$store.getters.dictionary.business_type[detailInfo.category] || ''"
            />
        </Col>
        <Col span="8">
            <s-label label="营业时间" :value="detailInfo.operateTime?.replace('~', '-')" />
        </Col>
        <Col span="8">
            <s-label label="垃圾产生量">
                <template #value>
                    {{ detailInfo.garbageNum ? `${detailInfo.garbageNum} kg` : "--" }}
                </template>
            </s-label>
        </Col>
        <Col span="8">
            <s-label label="单位法人" :value="detailInfo.legalPerson" />
        </Col>
        <Col span="8">
            <s-label label="法人联系电话" :value="detailInfo.legalPhone" />
        </Col>
        <Col span="8">
            <s-label label="单位联系人" :value="detailInfo.contactPhone" />
        </Col>
        <Col span="8">
            <s-label label="联系人电话" :value="detailInfo.contactPhone" />
        </Col>
        <Col span="8">
            <s-label label="垃圾桶数量" :value="detailInfo.num">
                <template #value>
                    {{ detailInfo.num ? `${detailInfo.num} 个` : "--" }}
                </template>
            </s-label>
        </Col>
        <Col span="8">
            <s-label label="执照代码" :value="detailInfo.license" />
        </Col>
        <Col span="8">
            <s-label label="企业类型">
                <template #value>
                    <dict-label :value="detailInfo.contactType" code="enterprise_type"></dict-label>
                </template>
            </s-label>
        </Col>
        <Col span="8">
            <s-label label="申报时长">
                <template #value>
                    <dict-label
                        :value="detailInfo.duration"
                        code="enterprise_duration"
                    ></dict-label>
                </template>
            </s-label>
        </Col>
        <Col span="24">
            <s-label label="附件">
                <template #value>
                    <upload-file
                        v-model="detailInfo.fileUrl"
                        :max-length="9"
                        :multiple="false"
                        :disabled="true"
                        :show-no-data="false" />
                </template>
            </s-label>
        </Col>
    </Row>
</template>

<script lang="ts" setup>
import { computed, defineProps } from "vue";
import { IEnterpriseReport } from "../type/index";
import DictLabel from "@/components/global/dictLabel";
import auditStatus from './status.vue';
import { formatAreaStr } from "@/utils/tool";
const props = defineProps<{ detailInfo: IEnterpriseReport }>();
const detailInfo = computed<IEnterpriseReport>(() => props.detailInfo);

const formatPositionPoint = computed(() => {
    return { gdx: detailInfo.value.gdx, gdy: detailInfo.value.gdy };
});

</script>

<style lang="less" scoped>
.show-map {
    font-size: 16px;
    color: #165dff;
    .ivu-icon {
        font-weight: 700;
    }
}
.license-wrapper {
    margin-bottom: 12px;
    .title {
        color: #798799;
        font-size: 14px;
        padding-right: 8px;
    }
}
</style>
