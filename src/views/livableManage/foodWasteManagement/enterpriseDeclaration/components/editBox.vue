<template>
    <Row :gutter="80">
        <Col span="8">
            <FormItem label="企业名称" prop="name">
<!--                 <CloudSelect v-model="form.name" @change="changeEnterprise" clearable/>-->
                <Input v-model="form.name" placeholder="请输入"></Input>
                <!-- <select-by-list url="/garbageEnterprise/list"
                                :type="1"
                                v-model="form.name"
                                :disabled="isApproval"
                                value-name="name"
                                allow-create
                                @on-change="changeEnterprise"
                                >
                </select-by-list> -->
            </FormItem>
        </Col>
        <Col span="8">
            <FormItem label="企业编号" prop="code">
                <Input v-model="form.code" maxlength="20" clearable placeholder="系统自动生成" disabled></Input>
            </FormItem>
        </Col>
        <Col span="8">
            <PointMarkerFormItem v-model:objx="form.objx" v-model:objy="form.objy" :disabled="isApproval" @confirm="onMapTapperConfirm" />
        </Col>
        <Col span="8">
            <FormItem label="所在区域" prop="area">
                <RegionCascader v-model="form.area" :disabled="isApproval"></RegionCascader>
            </FormItem>
        </Col>
        <Col span="8">
            <FormItem label="具体位置" prop="address">
                <Input v-model="form.address" maxlength="20" clearable :placeholder="defaultPlaceHolder" :disabled="isApproval"></Input>
            </FormItem>
        </Col>
        <Col span="8">
            <FormItem label="办事处" prop="office">
                <dictDropDownSelect code="office_code" v-model="form.office" :disabled="isApproval"></dictDropDownSelect>
                <!-- <Input v-model="form.office" maxlength="20" clearable :placeholder="defaultPlaceHolder" :disabled="isApproval"></Input> -->
            </FormItem>
        </Col>
        <Col span="8">
            <FormItem label="经营面积" prop="areaSize">
                <Input v-model="form.areaSize" clearable :placeholder="defaultPlaceHolder" :disabled="isApproval">
                    <template #append>
                        ㎡
                    </template>
                </Input>
            </FormItem>
        </Col>
        <Col span="8">
            <FormItem label="经营种类" prop="category">
                <dictDropDownSelect code="business_type" v-model="form.category" :disabled="isApproval"></dictDropDownSelect>
                <!-- <Input v-model="form.category" maxlength="20" clearable :placeholder="defaultPlaceHolder" :disabled="isApproval"></Input> -->
            </FormItem>
        </Col>
        <Col span="8">
            <FormItem label="营业时间" prop="operateTime">
                <TimePicker format="HH:mm" type="timerange" v-model="formatOperateTime" placeholder="请选择" :disabled="isApproval" />
            </FormItem>
        </Col>
        <Col span="8">
            <FormItem label="垃圾产生量" prop="garbageNum">
                <Input v-model="form.garbageNum" clearable :placeholder="defaultPlaceHolder" :disabled="isApproval">
                    <template #append>
                        kg
                    </template>
                </Input>
            </FormItem>
        </Col>
        <Col span="8">
            <FormItem label="垃圾桶数量" prop="num">
                <Input v-model="form.num" maxlength="20" clearable :placeholder="defaultPlaceHolder" :disabled="isApproval">
                    <template #append>
                        个
                    </template>
                </Input>
            </FormItem>
        </Col>
        <Col span="8">
            <FormItem label="单位法人" prop="legalPerson">
                <Input v-model="form.legalPerson" maxlength="20" clearable :placeholder="defaultPlaceHolder" :disabled="isApproval"></Input>
            </FormItem>
        </Col>
        <Col span="8">
            <FormItem label="法人联系电话" prop="legalPhone">
                <Input v-model="form.legalPhone" maxlength="20" clearable :placeholder="defaultPlaceHolder" :disabled="isApproval"></Input>
            </FormItem>
        </Col>
        <Col span="8">
            <FormItem label="单位联系人" prop="contactPerson">
                <Input v-model="form.contactPerson" maxlength="20" clearable :placeholder="defaultPlaceHolder" :disabled="isApproval"></Input>
            </FormItem>
        </Col>
        <Col span="8">
            <FormItem label="联系人电话" prop="contactPhone">
                <Input v-model="form.contactPhone" maxlength="20" clearable :placeholder="defaultPlaceHolder" :disabled="isApproval"></Input>
            </FormItem>
        </Col>
        <Col span="8">
            <FormItem label="执照代码" prop="license">
                <Input v-model="form.license" maxlength="20" clearable :placeholder="defaultPlaceHolder" :disabled="isApproval"></Input>
            </FormItem>
        </Col>
        <Col span="8">
            <FormItem label="企业类型" prop="contactType">
                <dict-drop-down-select code="enterprise_type" v-model="form.contactType" :placeholder="defaultPlaceHolder" :disabled="isApproval"></dict-drop-down-select>
            </FormItem>
        </Col>
        <Col span="8">
            <FormItem label="申报时长" prop="duration">
                <dict-drop-down-select code="enterprise_duration" v-model="form.duration" :placeholder="defaultPlaceHolder" :disabled="isApproval"></dict-drop-down-select>
            </FormItem>
        </Col>
        <Col span="24">
            <FormItem label="附件" prop="fileUrl">
                <upload-file
                    v-model="form.fileUrl"
                    v-model:loading="loading"
                    :max-length="9"
                    :multiple="false"
                    :disabled="isApproval"
                    :show-no-data="false"
                />
            </FormItem>
        </Col>
    </Row>
</template>

<script lang="ts" setup>
import { defineProps, computed, ref } from 'vue'
import RegionCascader from '@/components/common/regionCascader/index.vue'
import PointMarkerFormItem from '@/components/common/customizedFormItem/pointMarkerFormItem.vue'
import {
    IEnterpriseReport
} from '@/views/livableManage/foodWasteManagement/enterpriseDeclaration/type';
import DictDropDownSelect from '@/components/global/dictDropDownSelect';
import SelectByList from '@/components/common/selectByList';
import { useVModel } from '@/hooks/useVModel';
const _ = require('lodash')
const props = defineProps<{modelValue: IEnterpriseReport, isApproval: boolean, loading: boolean}>()
const emits = defineEmits(['update:modelValue'])

const uploadLoading = ref(false);

// 审核模式要将基础数据置灰，显示额外的字段
const isApproval = props.isApproval;

const defaultPlaceHolder = isApproval ? '' : '请输入'

const loading = useVModel(props, 'loading', emits)
const form = useVModel(props, undefined, emits)


const onMapTapperConfirm = (point:any, address:any) => {
    const { province, city, district, township, street, streetNumber } = address;
    // const fillCondition = !form.value.area && !form.value.address
    const fillCondition = !form.value.area
    if (fillCondition) {
        let area = ''
        area += province ? province + ',' : ''
        area += city ? city + ',' : ''
        area += district || ''
        form.value.area = area;
        // form.value.address = township + street + streetNumber;
    }
}

const formatOperateTime = computed({
    get() {
        const [startTime, endTime] = form.value.operateTime?.replace('~', '-')?.split('-') || []
        return [startTime, endTime]
    },
    set(newVal) {
        form.value.operateTime = newVal.join('-')
    }
})
// // 选择企业名称之后，自动带出企业的信息到表单中
// const changeEnterprise = (val:any) => {
//     //id不能转换为新数据的id
//     let enterpriseId = val.id
//     val.id = null;
//     form.value = Object.assign(form.value, val)
//     //特殊处理企业id
//     form.value.enterpriseId = enterpriseId
// }

</script>

<style lang="less" scoped>
.show-map {
    position: absolute;
    top: 0px;
    left: 100px;
    transform: translate(0, -25%);
    font-size: 16px;
    color: #165DFF;
    cursor: pointer;
    z-index: 1000;
    .ivu-icon {
        font-weight: 700;
    }
}
:deep(.ivu-cascader-rel .ivu-icon-ios-close-circle){
    font-family: "iconfont" !important;
    color: #4E627E;
    font-size: 16px;
    &:before{
        content: "\e705";
    }
}
</style>
