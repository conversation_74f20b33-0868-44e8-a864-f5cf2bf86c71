<template>
    <Row :gutter="80">
        <Col span="8">
            <s-label
                label="押金金额"
                :value="`${detailInfo.amount}${detailInfo.amount && '元' || ''} `"
            />
        </Col>
        <Col span="16">
            <s-label label="区域位置" :value="detailInfo.areaPath" />
        </Col>
        <Col span="8">
            <s-label label="收集车辆" :value="detailInfo.carLicenseNo" />
        </Col>
        <Col span="8">
            <s-label label="收集时间段" :value="detailInfo.collectTime" />
        </Col>
        <Col span="8">
            <s-label label="开始收集时间" :value="detailInfo.startTime" />
        </Col>
        <Col span="8">
            <s-label label="是否正常收集" :value="isNormalEnum[detailInfo.isNormal]" />
        </Col>
    </Row>
</template>

<script setup lang="ts">
import { computed, defineProps } from "vue";
import { IEnterpriseReport } from "@/views/livableManage/foodWasteManagement/enterpriseDeclaration/type";

const props = defineProps<{ detailInfo: IEnterpriseReport }>();
const detailInfo = computed<IEnterpriseReport>(() => props.detailInfo);

const isNormalEnum = {
    "0": "否",
    "1": "是",
};
</script>

<style scoped lang="ts"></style>
