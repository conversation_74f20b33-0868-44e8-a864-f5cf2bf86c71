<template>
    <!--    <detailCard title="收运信息" :src="require('@/assets/images/icon_detail_folder.png')" >-->
    <Row :gutter="80">
        <Col span="8">
            <FormItem label="押金金额" prop="amount" :rules="amountRules">
                <Input v-model="form.amount" maxlength="20" clearable placeholder="请输入">
                    <template #append>
                        元
                    </template>
                </Input>
            </FormItem>
        </Col
        >
        <Col span="8">
            <FormItem label="区域位置" prop="areaPath" :rules="areaPathRules">
                <AreaSelectTreeNoDevice v-model="handlerAreaPath" /> 
            </FormItem>
        </Col
        >
        <Col span="8">
            <FormItem label="收集车辆" prop="carId">
                <selected-by-list v-model="form.carId" label-name="licenseNo"
                                  url="/garbageCar/list"></selected-by-list>
            </FormItem>
        </Col
        >
        <Col span="8">
            <FormItem label="收集时间段" prop="collectTime">
                <time-picker
                    v-model="formatCollectTime"
                    format="HH:mm"
                    type="timerange"
                    placeholder="请选择"
                ></time-picker>
            </FormItem>
        </Col
        >
        <Col span="8">
            <FormItem label="开始收集时间" prop="startTime" :rules="startTimeRules">
             <date-picker placeholder="请选择" v-model="formatStartTime" type="datetime" clearable ></date-picker>
            </FormItem>
        </Col
        >
        <Col span="8">
            <FormItem label="是否正常收集" prop="isNormal">
                <Select v-model="form.isNormal">
                    <Option v-for="item in isNormalOption" :value="item.value" :key="item.value">
                        {{
                            item.label
                        }}
                    </Option>
                </Select>
            </FormItem>
        </Col>
    </Row>
</template>

<script lang="ts" setup>
import { computed, defineProps } from 'vue';
import {
    IEnterpriseReport
} from '@/views/livableManage/foodWasteManagement/enterpriseDeclaration/type';
import SelectedByList from '@/components/common/selectByList';
import { DatePicker, TimePicker } from 'view-ui-plus';
import moment from 'moment';

const props = defineProps<{ modelValue: IEnterpriseReport }>();
const form = computed<IEnterpriseReport>(() => props.modelValue);

const formatCollectTime = computed({
    get() {
        const [startTime, endTime] = form.value.collectTime?.split('-') || ['', ''];
        return [startTime, endTime];
    },
    set(newVal) {
        
        form.value.collectTime = newVal.join('-');
    },
});
const formatStartTime = computed({
    get() {
        return form.value.startTime ? moment(form.value.startTime).toDate() : ''
    },
    set(newDateTime) {
        
        if (newDateTime) {
            form.value.startTime = moment(newDateTime).format('YYYY-MM-DD HH:mm:ss');
        } else {
            form.value.startTime = '';
        }
    },
});

const isNormalOption = [
    {
        label: '否',
        value: 0,
    },
    {
        label: '是',
        value: 1,
    },
];

const startTimeRules = [{ required: true, message: '请完善必填项', trigger: 'blur' }]

const areaPathRules = [{ required: true, message: '请完善必填项', trigger: 'change' }]

const amountRules = [
    { required: true, message: '请填写押金金额', trigger: 'blur' },
    {
        pattern: /^(\d+|\d+\.\d{1,2}|0)$/,
        message: '请输入最多两位小数的数字',
        trigger: 'blur'
    }]

const handlerAreaPath = computed({
    get() {
        return form.value.areaPath?.replace(/@/g, '/');
    },
    set(newVal) {
        form.value.areaPath = newVal?.replace(/\//g, '@');
    }
})
</script>

<style lang="less" scoped></style>
