<template>
    <div>
        <BreadcrumbCustom>
            <BreadcrumbItem>餐厨垃圾管理</BreadcrumbItem>
            <BreadcrumbItem to="/kitchenWasteManagement/enterpriseReport">企业申报</BreadcrumbItem>
            <BreadcrumbItem>{{ isEdit ? '编辑' : '详情' }}</BreadcrumbItem>
        </BreadcrumbCustom>
        <Form ref="formRef" :model="detailInfo" :rules="formRulesWithApproval" label-position="top">
            <detailCard
                ref="detailRef"
                title="基础信息"
                :src="require('@/assets/images/icon_detail.png')"
                :is-back-btn="true"
                :subDisabled="uploadLoading"
                :is-edit-btn="$Util.checkAuth('kitchenWasteManagement:enterpriseReport:edit') && detailInfo.approvalStatus != 1"
                @on-submit="onSubmit"
                @on-edit="onEdit"
                @on-back="onBack"
                :class="[isEdit ? 'edit-card' : '' ]"
            >
                <InfoShow v-if="!isEdit" :detail-info="detailInfo"/>
                <EditBox v-else v-model="detailInfo" :is-approval="detailInfo.approvalStatus == 1"/>
<!--                编辑审核-->
                <template v-if="isEdit && detailInfo.approvalStatus == 1">
                    <SingleTitle :src="require('@/assets/images/icon_detail_folder.png')" title="收运信息"></SingleTitle>
                    <edit-approval v-model="detailInfo"/>
                    <SingleTitle :src="require('@/assets/images/icon_detail_folder.png')" title="收运合同"></SingleTitle>
                    <upload-file v-model="detailInfo.contractUrl" :max-length="1" v-model:loading="uploadLoading" :multiple="false" :disabled="!isEdit"/>
                </template>
            </detailCard>
<!--            非编辑审核-->
            <template v-if="!isEdit && detailInfo.approvalStatus == 1">
                <detailCard title="收运信息"
                            :src="require('@/assets/images/icon_detail_folder.png')">
                    <info-show-approval :detail-info="detailInfo"/>
                </detailCard>
                <detailCard title="收运合同"
                            :src="require('@/assets/images/icon_detail_folder.png')">
                    <upload-file v-model="detailInfo.contractUrl" :max-length="1" :multiple="false" :disabled="!isEdit"/>
                </detailCard>
            </template>
        </Form>
    </div>
</template>

<script lang="ts" setup>
import { computed, ref } from 'vue';
import InfoShow from './components/infoShow.vue'
import EditBox from './components/editBox.vue'
import {
    useEdit
} from './useEdit';
import {
    editEnterpriseReport,
    getEnterpriseReportInfo,
} from '@/api/livableManage/foodWasteManagementService';
import EditApproval
    from '@/views/livableManage/foodWasteManagement/enterpriseDeclaration/components/editApproval.vue';
import InfoShowApproval
    from '@/views/livableManage/foodWasteManagement/enterpriseDeclaration/components/infoShowApproval.vue';
import SingleTitle from '@/components/common/singleTitle/index.vue'
import {AuditStatusEnum} from './enum/index' 
const detailRef = ref();
const formRef = ref();
const { isEdit, formRules, detailInfo, router, route, approvalRules,that } = useEdit();

const onInit = async() => {
    const id = route.query.id;
    const res = await getEnterpriseReportInfo(id)
    detailInfo.value = res.data
}
onInit();


const onSubmit = async() => {
    const validateRes = await formRef.value?.validate()
    if (validateRes) {
        const params = detailInfo.value;
        const res:any = await editEnterpriseReport(params);
        if (res.success) {
            isEdit.value = false;
            detailRef.value.isEditFlag = false
            that?.$Message.success('编辑成功')
            await onInit();
        }
    }
}

const onEdit = (val: boolean) => {
    if (!val) {
        onInit();
    }
    isEdit.value = val;
}

const onBack = () => {
    router.back()
}

const formRulesWithApproval = computed(() => {
    return detailInfo.value.approvalStatus == '1' ? approvalRules : formRules
})
</script>

<style lang="less" scoped>
/deep/ .tab-title {
    justify-content: flex-start;
    padding-bottom: 16px;
    height: 48px;
    column-gap: 20px;
}

</style>
