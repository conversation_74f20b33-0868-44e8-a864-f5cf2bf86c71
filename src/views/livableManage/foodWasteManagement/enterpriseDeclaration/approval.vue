<template>
    <div>
        <BreadcrumbCustom>
            <BreadcrumbItem>餐厨垃圾管理</BreadcrumbItem>
            <BreadcrumbItem to="/kitchenWasteManagement/enterpriseReport">企业申报</BreadcrumbItem>
            <BreadcrumbItem>审核</BreadcrumbItem>
        </BreadcrumbCustom>
        <Form ref="formRef" :model="detailInfo" label-position="top">
            <detailCard title="审核" :is-back-btn="true" @on-back="onBack">
                <EditBox v-model="detailInfo" :is-approval="true" ref="edit" />
                <SingleTitle
                    :src="require('@/assets/images/icon_detail_folder.png')"
                    title="收运信息"
                ></SingleTitle>
                <add-approval v-model="detailInfo"></add-approval>
                <SingleTitle
                    :src="require('@/assets/images/icon_detail_folder.png')"
                    title="收运合同"
                ></SingleTitle>
                <upload-file
                    v-model="detailInfo.contractUrl"
                    v-model:loading="isLoading"
                    :max-length="1"
                    :multiple="false"
                />
                <div class="btn-box">
                    <Button @click="onBack">取消</Button>
                    <Button @click="reject">驳回</Button>
                    <Button type="primary" @click="onSubmit" :loading="isLoading">通过</Button>
                </div>
            </detailCard>
        </Form>
    </div>
</template>

<script lang="ts" setup>
import EditBox from '@/views/livableManage/foodWasteManagement/enterpriseDeclaration/components/editBox.vue';
import { useEdit } from './useEdit';
import {
    getEnterpriseReportInfo,
    garbageEnterpriseReportStatus
} from '@/api/livableManage/foodWasteManagementService';
import { getCurrentInstance, ref } from 'vue';
import AddApproval from '@/views/livableManage/foodWasteManagement/enterpriseDeclaration/components/editApproval.vue';
import SingleTitle from '@/components/common/singleTitle/index.vue';

const { detailInfo, router, route } = useEdit();
const that = getCurrentInstance()?.appContext.config.globalProperties;
const formRef = ref();
const isLoading = ref(false);
const onBack = () => {
    router.back();
};

const onInit = async () => {
    const id = route.query.id;
    const res = await getEnterpriseReportInfo(id);
    detailInfo.value = res.data;
};

onInit();
const onSubmit = async () => {
    const validRes = await formRef.value?.validate();
    if (validRes) {
        const { id, amount, carLicenseNo, collectTime, startTime, isNormal, contractUrl,areaPath } =
            detailInfo.value;
        const areaLocationTreeArr = areaPath?.split('@') || [];
        const params = {
            amount,
            carLicenseNo,
            collectTime,
            startTime,
            isNormal,
            contractUrl,
            approvalStatus: 1,
            id,
            areaPath,
            szjd: areaLocationTreeArr[0],
            szsq: areaLocationTreeArr[1],
            szdywg: areaLocationTreeArr[2]
        };
        isLoading.value = true;
        console.log('params', params)
        const res:any = await garbageEnterpriseReportStatus(params);
        if (res.success) {
            that?.$Message.success('提交企业申报审核成功');
            router.back();
        }
        isLoading.value = false;
    }
};
// 驳回
const reject = async () => {
    const params = {
        id: Number(route.query.id), // id
        approvalStatus: 2, // 审核状态(1已审核0未审核2已驳回)
    };
    const res:any = await garbageEnterpriseReportStatus(params);
    if (res.success) {
        that?.$Message.success('驳回成功');
        router.back();
    }
};
</script>

<style lang="less" scoped>
.title-card {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
    padding-bottom: 8px;
    .title-icon {
        background: #6aa1ff;
        border-radius: 2px;
        width: 24px;
        height: 24px;
        margin-right: 8px;
        color: #fff;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    .title-icon-img {
        width: 24px;
        height: 24px;
        margin-right: 8px;
    }
    h5 {
        margin-bottom: 0;
    }
    &.no-title {
        margin-bottom: 0;
    }
}
</style>
