<template>
    <BreadcrumbCustom>
        <BreadcrumbItem>餐厨垃圾管理</BreadcrumbItem>
        <BreadcrumbItem>企业申报</BreadcrumbItem>
    </BreadcrumbCustom>
    <ContentCard title="企业申报" :base-btn="false">
        <BaseForm @handle-submit="onSubmit(listRef)" :model="condition" inline :label-width="90">
            <template #formitem>
                <FormItem label="企业名称" prop="name">
                    <select-by-list url="/garbageEnterprise/list"
                                    :type="1"
                                    v-model="condition.name"
                                    value-name="name"
                                    allow-create>
                    </select-by-list>
                </FormItem>
                <FormItem label="具体位置" prop="address">
                    <Input v-model="condition.address" placeholder="请输入"
                           clearable
                    ></Input>
                </FormItem>
                <FormItem label="审核状态" prop="approvalStatus">
                    <Select v-model="condition.approvalStatus" clearable>
                        <Option v-for="item in AuditStatusOption" :value="item.value" :key="item.value">{{ item.label }}</Option>
                    </Select>
                </FormItem>
        </template>
        </BaseForm>
        <btn-card>
            <Button type="primary" @click="onAdd" icon="md-add" v-auth="'kitchenWasteManagement:enterpriseReport:add'">
                新增企业申报
            </Button>
            <Button icon="ios-trash" @click="onDelete" v-auth="'kitchenWasteManagement:enterpriseReport:del'">
                删除
            </Button>
        </btn-card>
        <baseTable @on-selection-change="onSelectionChange" :model="condition" ref="listRef"
                   :columns="tableColumn" url="/garbageEnterpriseReport/list"
        >
            <template #geographicalLocation="{ row }">
                <tooltip-auto-show :content="formatAreaStr(row.area) + row.address"></tooltip-auto-show>
            </template>
            <template #duration="{row}">
                <dict-label :value="row.duration" code="enterprise_duration"></dict-label>
            </template>
            <template #approvalStatus="{ row }">
                <status :value="row.approvalStatus"></status>
            </template>
            <template #isNormal="{ row }">
                {{ isNormalEnum[row.isNormal] }}
            </template>
            <template #operateTime="{ row }">
                {{ row.operateTime?.replace('~', '-') }}
            </template>

            <template #action="{ row }">
                <LinkBtn size="small" @click="goDetail(row)">
                    详情
                </LinkBtn>
                <LinkBtn :disabled="row.approvalStatus !== 0" size="small" @click="toExamine(row)">
                    审核
                </LinkBtn>
            </template>
        </baseTable>
    </ContentCard>
</template>

<script lang="ts" setup>

import status from './components/status.vue';
import { getCurrentInstance, onMounted, ref } from 'vue';
import { useRouter } from 'vue-router';
import {
    delEnterpriseReport,
    getEnterpriseList
} from '@/api/livableManage/foodWasteManagementService';
import {
    IEnterprise
} from '@/views/livableManage/foodWasteManagement/baseInfoManagement/enterpriseManagement/type';
import {
    AuditStatusEnum
} from '@/views/livableManage/foodWasteManagement/enterpriseDeclaration/enum';
import DictLabel from '@/components/global/dictLabel';
import SelectByList from "@/components/common/selectByList";
import { formatAreaStr } from "@/utils/tool";
const _ = require('lodash')

const router = useRouter();

const that = getCurrentInstance()?.appContext.config.globalProperties;


const listRef = ref(null);

// 列表查询条件
const condition = ref({
    name: '',
    address: '',
    approvalStatus: ''
})

// 详情
const goDetail = ({ id }) => {
    router.push({ name: 'kitchenWasteManagement:enterpriseReport:detail', query: { id }})
}
// 审核
const toExamine = ({ id }) => {
    router.push({ name: 'kitchenWasteManagement:enterpriseReport:approval', query: { id }})
}
// 查询
const onSubmit = (ref: any) => {
    const params = condition.value
    ref && ref.search(params);
}
onMounted(() => {
    onSubmit(listRef.value)
})
// 新增
const onAdd = () => {
    router.push({ name: 'kitchenWasteManagement:enterpriseReport:add' })
}

// 删除相关
const selectedIds = ref<(string | undefined)[]>([])

const onSelectionChange = (selectedRow: IEnterprise[] = []) => {

    selectedIds.value = selectedRow.map(item => item.id)
}

const onDelete = () => {
    if (selectedIds.value.length === 0) {
        that && that.$Message.warning('最少选择一条数据')
        return
    }
    that && that.$Modal.confirm({
        title: '提示',
        content: '您确定要删除选中的数据吗',
        onOk: () => {
            const ids = selectedIds.value;
            delEnterpriseReport({ ids })
                .then(res => {
                    if (res.success) {
                        selectedIds.value = []
                        onSubmit(listRef.value)
                        that?.$Message.success('删除成功')
                    }
                })

        }
    })
}


const tableColumn = ref([
    { type: 'selection', width: 40 },
    { title: '企业名称', key: 'name', minWidth: 160, tooltip: true },
    { title: '具体位置', key: 'address', minWidth: 160, tooltip: true },
    { title: '营业时间', slot: 'operateTime',width: 110, tooltip: true },
    { title: '申报时长', slot: 'duration', width: 80, tooltip: true },
    { title: '开始收集日期', key: 'startTime', width: 160 },
    { title: '是否正常收集', slot: 'isNormal', width: 110, tooltip: true },
    { title: '审核状态', slot: 'approvalStatus' },
    { title: '操作', slot: 'action', width: 100, lock: true, fixed: true }
])

const AuditStatusOption = Object.keys(AuditStatusEnum).map(key => {
    return {
        label: AuditStatusEnum[key],
        value: key
    }
})

const isNormalEnum = {
    '0': '否',
    '1': '是',
}
</script>

<style lang="less" scoped>
/deep/ .ivu-form-item{
    width: 80%;
}

</style>
