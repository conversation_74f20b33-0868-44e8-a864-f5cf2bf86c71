<template>
    <div>
        <BreadcrumbCustom>
            <BreadcrumbItem>餐厨垃圾管理</BreadcrumbItem>
            <BreadcrumbItem>基础管理</BreadcrumbItem>
            <BreadcrumbItem to="/kitchenWasteManagement/basic/point">收运点管理</BreadcrumbItem>
            <BreadcrumbItem>{{ isEdit ? "编辑" : "详情" }}</BreadcrumbItem>
        </BreadcrumbCustom>
        <Form label-position="top" ref="formRef" :model="detailInfo" :rules="formRules">
            <detailCard
                ref="detailRef"
                title="基础信息"
                :src="require('@/assets/images/icon_detail.png')"
                :is-back-btn="true"
                :is-edit-btn="$Util.checkAuth('kitchenWasteManagement:basic:point:edit')"
                @on-submit="onSubmit"
                @on-edit="onEdit"
                @on-back="onBack"
                :class="[isEdit ? 'edit-card' : '']"
            >
                <InfoShow v-if="!isEdit" :detail-info="detailInfo" />
                <EditBox v-else v-model="detailInfo" />
            </detailCard>
        </Form>
        <detailCard v-if="!isEdit" :class="{ 'bottom-card': isEdit }" title="收运记录">
            <div class="condition-wrapper">
                <Form :model="condition" inline>
                    <FormItem>
                        <DatePicker
                            v-model="formatTime"
                            type="daterange"
                            format="yyyy-MM-dd"
                            placeholder="请选择任务日期"
                            clearable
                            :editable="false"
                        />
                    </FormItem>
                    <FormItem>
                        <Select v-model="condition.status" placeholder="请选择收运情况" clearable>
                            <Option
                                v-for="item in situationOption"
                                :value="item.value"
                                :key="item.value"
                                >{{ item.label }}</Option
                            >
                        </Select>
                    </FormItem>
                </Form>
            </div>
            <!-- :columns="columns"
        url="/garbageTaskRecords/list"
        ref="listRef"
        :page-size="7"
        :page-opts="[7, 14, 20, 30, 50, 100]" -->
            <EmpTable @on-edit-success="handleEditSuccess" :type="1" :searchObj="condition" origin="收运点管理" />
            <!-- @hook:mounted="handleSearch" -->
            <!-- url="/garbageTaskRecords/list" -->
            <!-- <baseTable
                no-data-text="--"
                :columns="columns"
                ref="listRef"
                :data="data"
                empty-block-str="--"
            >
                <template #status="{ row }">
                    {{ collectStatusEnum[row.status] }}
                </template>
                <template #taskTime="{ row }">
                    {{ dateFormatter(row.taskTime) }}
                </template>
                <template #weight="{ row }">
                    {{ formatWeight(row.weight) }}
                </template>
                <template #action="{ row }">
                    <LinkBtn size="small" @click="check(row)">查看</LinkBtn>
                    <template v-if="row.status == '1'">
                        <LinkBtn size="small" @click="openModal(row)">编辑</LinkBtn>
                        <LinkBtn size="small" @click="onDelete(row)">删除</LinkBtn>
                    </template>
                    <template v-else>
                        <LinkBtn size="small" @click="openModal(row)">收运</LinkBtn>
                    </template> 
                </template>
            </baseTable> -->
        </detailCard>
    </div>
    <s-modal
        title="收运记录"
        @closeModal="clearModalInfo"
        @handleSubmit="onModalSubmit"
        :styles="{ top: '18px' }"
        :ref-box="addModalRef"
        ref="modalRef"
        width="625"
    >
        <AddCollectModal v-model="modalInfo" :id="modalId" ref="addModalRef"></AddCollectModal>
    </s-modal>
</template>

<script lang="ts" setup>
import { computed, getCurrentInstance, ref, useSlots, watchEffect } from "vue";
import InfoShow from "./components/infoShow.vue";
import EditBox from "./components/editBox.vue";
import EmpTable from "../../components/EmpTable/index.vue";
import { useEdit } from "./useEdit";
import {
    delReceiptAndShipmentRecords,
    editPoint,
    getPointInfo,
} from "@/api/livableManage/foodWasteManagementService";
import moment from "moment";
import AddCollectModal from "./components/addCollectModal.vue";
import { collectStatusEnum } from "./enum/index";

const that = getCurrentInstance()?.appContext.config.globalProperties;

const detailRef = ref<any>(null);
const formRef = ref();

const { isEdit, formRules, detailInfo, router, route, modalInfo, clearModalInfo } = useEdit();

const onInit = async () => {
    const id = route.query.id;
    const res = await getPointInfo(id);
    detailInfo.value = res.data;
};
onInit();
const handleEditSuccess = () => {
    onInit();
};
const onSubmit = async () => {
    const validateRes = await formRef.value?.validate();
    if (validateRes) {
        const params = Object.assign({}, detailInfo.value, { type: 1,szjd:'',szsq:'',szdywg:'' })
        params.areaPath = params.areaPath?.replace(/\//g, '@');
        const areaLocationTreeArr = params.areaPath?.split('@')||[];
        params.szjd = areaLocationTreeArr[0]
        params.szsq = areaLocationTreeArr[1]
        params.szdywg = areaLocationTreeArr[2]
        const res: any = await editPoint(params);
        if (res.success) {
            isEdit.value = false;
            detailRef.value.isEditFlag = false;
            await onInit();
        }
    }
};

const onEdit = (val: boolean) => {
    if (!val) {
        onInit();
    }
    isEdit.value = val;
};

const onDelete = async (row: any) => {
    if (row.id) {
        that &&
            that.$Modal.confirm({
                title: "提示",
                content: "您确定要删除选中的数据吗",
                onOk: () => {
                    delReceiptAndShipmentRecords({ ids: [row.id] }).then((res: any) => {
                        if (res.success) {
                            that.$Message.success("删除成功");
                            loadList();
                        }
                    });
                },
            });
    }
};

const onBack = () => {
    router.back();
};

// 收运记录相关

const listRef = ref();

const condition = ref({
    time: "",
    startTime: "",
    endTime: "",
    status: "",
});

const formatTime = computed({
    get() {
        const { startTime, endTime } = condition.value;
        if (startTime && endTime) {
            return [moment(condition.value.startTime), moment(condition.value.endTime)];
        }
    },
    set(newDateRange) {
        
        const [startDate, endDate]: any = newDateRange;
        condition.value.startTime =
            (startDate && moment(startDate).format("YYYY-MM-DD HH:mm:ss")) || "";
        condition.value.endTime = (endDate && moment(endDate).format("YYYY-MM-DD HH:mm:ss")) || "";
    },
});
const formatWeight = (num: number) => {
    const newNum = +num;
    if (isNaN(newNum) || !num) return "--";
    if (Number.isInteger(newNum)) {
        // 如果是整数，不做处理
        return newNum;
    } else {
        // 如果是小数，保留两位小数
        return newNum.toFixed(2);
    }
};

const situationOption = [
    {
        label: "未收运",
        value: 0,
    },
    {
        label: "已收运",
        value: 1,
    },
];

// const isLoading = ref(false)
// const onLoadDone = () => {
//     isLoading.value = false;
// }
const loadList = () => {
    const pointId = route.query.id;
    if (pointId) {
        const params = Object.assign({}, condition.value, { pointId, type: 1 });
        listRef.value && listRef.value.search(params);
    }
};
watchEffect(loadList, {
    flush: "post",
});

const onModalSubmit = () => {
    onInit();
    // loadList();
};

// const columns = [
//     { title: '关联任务', key: 'taskName', minWidth: 200, tooltip: true },
//     { title: '任务日期', slot: 'taskTime', width: 120, tooltip: true },
//     { title: '收运情况', slot: 'status', tooltip: true },
//     { title: '实收垃圾桶数(个)', key: 'doneGarbageNum', width: 130, tooltip: true },
//     { title: '收运总重量(kg)', width: 130, slot: 'weight' },
//     { title: '收运详情', key: 'content', minWidth: 100, tooltip: true },
//     { title: '收运人', key: 'contactPerson' },
//     { title: '收运时间', key: 'time', width: 160, tooltip: true },
//     { title: '操作', slot: 'action', width: 100, lock: true, fixed: true }
// ]

// 弹窗组件相关

const modalRef = ref();
const addModalRef = ref();

const modalId = ref(0);

const openModal = (row: any) => {
    if (row.id) {
        modalId.value = row.id;
        modalRef.value?.handleOpen();
    }
};

const dateFormatter = (time: string) => {
    return time ? moment(time).format("YYYY-MM-DD") : "--";
};
// 点击查看
const check = (row: any) => {
    // if (row.id) {
    //     modalId.value = row.id;
    //     modalRef.value?.handleOpen();
    // }
    router.push({
        name: "kitchenWasteManagement:collectionTask:collection",
        query: {
            id: row.id,
            planId: row.planId,
            detailId: route.query.id,
        },
    });
};
</script>

<style lang="less" scoped>
:deep(.tab-title) {
    justify-content: flex-start;
    padding-bottom: 16px;
    height: 48px;
    column-gap: 20px;
}
:deep(.condition-wrapper .ivu-form-item) {
    width: 296px;
    margin-right: 0;
    & + .ivu-form-item {
        margin-left: 24px;
    }
}
:deep(.condition-wrapper .ivu-picker-panel-body) {
    min-width: 510px;
}
</style>
