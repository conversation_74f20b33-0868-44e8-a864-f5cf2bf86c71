<template>
    <BreadcrumbCustom>
        <BreadcrumbItem>餐厨垃圾管理</BreadcrumbItem>
        <BreadcrumbItem>基础管理</BreadcrumbItem>
        <BreadcrumbItem>收运点管理</BreadcrumbItem>
    </BreadcrumbCustom>
    <ContentCard title="收运点管理" :base-btn="false">
        <BaseForm @handle-submit="onSubmit(listRef)" :model="condition" inline :label-width="90">
            <template #formitem>
                <FormItem label="区域位置" prop="areaLocation">
                    <AreaSelectTree v-model="condition.areaLocation" url="/garbagePoint/getTree" type="1" />
                    <!-- <RegionCascader v-model="condition.area"></RegionCascader> -->
                </FormItem>
                <FormItem label="收运点名称" prop="name">
                    <Input v-model="condition.name" placeholder="请输入" clearable></Input>
                </FormItem>
                <FormItem label="收运计划" prop="planId">
                    <SelectByList v-model="condition.planId" :type="1" label-name="planName" url="/garbagePlan/list"></SelectByList>
                </FormItem>
            </template>
        </BaseForm>
        <btn-card>
            <!-- <Button type="primary" @click="onAdd" icon="md-add" v-auth="'kitchenWasteManagement:basic:point:add'">
                新增收运点
            </Button> -->
            <Button icon="ios-trash" @click="onDelete" v-auth="'kitchenWasteManagement:basic:point:del'">
                删除
            </Button>
            <config />
        </btn-card>
        <baseTable @on-selection-change="onSelectionChange" :model="condition" ref="listRef"
                   :columns="tableColumn" url="/garbagePoint/list"
        >
            <template #geographicalLocation="{ row }">
                <tooltip-auto-show :content="formatAreaStr(row.area) + row.address" />
            </template>
            <template #collectTransportCycle="{ row }">
                <span v-if="row.timeType == 1">每天</span>
                <tooltip-auto-show v-if="row.timeType == 2" :content="`每周${row.timeValues.split(',').map((k: string) =>
                            weekList[+k] || '').filter((k: string) => k).join(',')}`" placement="top">
                    每周{{ row.timeValues.split(',').map((k: string) =>
                    weekList[+k] || '').filter((k: string) => k).join(',') }}
                </tooltip-auto-show>

                <tooltip-auto-show v-if="row.timeType == 3" :content="`每月${row.timeValues}号`" placement="top">
                    每月{{ row.timeValues }}号
                </tooltip-auto-show>
            </template>
            <template #action="{ row }">
                <LinkBtn size="small" @click="goDetail(row)">
                    详情
                </LinkBtn>
            </template>
        </baseTable>
    </ContentCard>

</template>

<script lang="ts" setup>
import { getCurrentInstance, onMounted, ref } from 'vue';
import config from '@/views/livableManage/foodWasteManagement/components/configCom/index.vue'
import { useRouter } from 'vue-router';
import { delPoint } from '@/api/livableManage/foodWasteManagementService';
// import RegionCascader from '@/components/common/regionCascader/index.vue';
import {
    IEnterprise
} from '@/views/livableManage/foodWasteManagement/baseInfoManagement/enterpriseManagement/type';
import SelectByList from '@/components/common/selectByList';
import TooltipAutoShow from "@/components/global/tooltipAutoShow/index.vue";
import { formatAreaStr } from "@/utils/tool";
const router = useRouter();

const that = getCurrentInstance()?.appContext.config.globalProperties;


const listRef = ref(null);

const condition = ref({
    planId: '', // 收运计划id
    area: '',
    name: '',
    areaLocation:''
})

// 详情
const goDetail = ({ id }:any) => {
    router.push({ name: 'kitchenWasteManagement:basic:point:edit', query: { id,origin:'收运点管理' }})
}
// 查询
const onSubmit = (ref: any) => {
    const params = Object.assign({}, condition.value, { type: 1, szjd:'',szsq:'',szdywg:''})
    const arr: Array<string> = condition.value.areaLocation?.split('/')
    params.szjd = arr[0]
    params.szsq = arr[1]
    params.szdywg = arr[2]
    ref && ref.search(params);
}
onMounted(() => {
    onSubmit(listRef.value)
})
// 新增
const onAdd = () => {
    router.push({ name: 'kitchenWasteManagement:basic:point:add' })
}

// 删除
const selectedIds = ref<(string | undefined)[]>([])

const onSelectionChange = (selectedRow: IEnterprise[] = []) => {

    selectedIds.value = selectedRow.map(item => item.id)
}

const onDelete = () => {
    if (selectedIds.value.length === 0) {
        that && that.$Message.warning('最少选择一条数据')
        return
    }
    that && that.$Modal.confirm({
        title: '提示',
        content: '您确定要删除选中的数据吗',
        onOk: () => {
            const ids = selectedIds.value;
            delPoint({ ids })
                .then((res:any) => {
                    if (res.success) {
                        selectedIds.value = []
                        onSubmit(listRef.value)
                    }
                })

        }
    })
}

const tableColumn = ref([
    { type: 'selection', width: 40 },
    { title: '收运点名称', key: 'name', width: 160, tooltip: true },
    { title: '收运点编号', key: 'code', width: 120, tooltip: true },
    { title: '垃圾桶数量', key: 'num', width: 100, tooltip: true },
    { title: '区域位置', key: 'areaPath', width: 160, tooltip: true },
    { title: '所属企业', key: 'enterpriseName', tooltip: true },
    { title: '关联收运计划', width: 130, key: 'planName', tooltip: true },
    { title: '关联收运周期', width: 130, slot: 'collectTransportCycle' },
    { title: '最近一次收运时间', key: 'collectionTime', width: 160, tooltip: true },
    { title: '操作', slot: 'action', width: 80, lock: true, fixed: true }
])

const weekList = ['', '一', '二', '三', '四', '五', '六', '日']

</script>

<style lang="less" scoped>
//:deep(.ivu-form-item){
//    width: 80%;
//}

</style>
