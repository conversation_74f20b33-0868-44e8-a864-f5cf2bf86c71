<template>
    <Row :gutter="80">
        <Col span="8">
            <FormItem label="处理厂名称" prop="name" required>
                <Input v-model="form.name" maxlength="20" clearable placeholder="请输入"></Input>
            </FormItem>
        </Col>
        <Col span="8">
            <FormItem label="处理厂编号" prop="code">
                <Input v-model="form.code" maxlength="20" clearable placeholder="系统自动生成" disabled></Input>
            </FormItem>
        </Col>
        <Col span="8">
            <PointMarkerFormItem v-model:objx="form.objx" v-model:objy="form.objy" @confirm="onMapTapperConfirm" />
        </Col>
</Row>
    <Row :gutter="80">
        <Col span="8">
            <FormItem label="所属区域" prop="area">
                <RegionCascader v-model="form.area" />
            </FormItem>
        </Col>
        <Col span="8">
            <FormItem label="具体位置" prop="address">
                <Input v-model="form.address" maxlength="20" clearable placeholder="请输入"></Input>
            </FormItem>
        </Col>
        <Col span="8">
            <FormItem label="设计处理能力" prop="capacity">
                <Row>
                    <Input clearable v-model="form.capacity">
                        <template #append>
                            吨
                        </template>
                    </Input>
                </Row>
            </FormItem>
        </Col>
    </Row>
    <Row :gutter="80">
        <Col span="8">
            <FormItem label="处理方式" prop="way">
                <DictDropDownSelect v-model="form.way" code="garbage_handle"></DictDropDownSelect>
            </FormItem>
        </Col>
        <Col span="8">
            <FormItem label="联系人" prop="contactPerson">
                <Input v-model="form.contactPerson" maxlength="20" clearable placeholder="请输入"></Input>
            </FormItem>
        </Col>
        <Col span="8">
            <FormItem label="联系方式" prop="contactPhone">
                <Input v-model="form.contactPhone" maxlength="20" clearable placeholder="请输入"></Input>
            </FormItem>
        </Col>
    </Row>
</template>

<script lang="ts" setup>
import { computed, defineProps } from 'vue'
import {
    IWasteTreatmentPlant
} from '@/views/livableManage/foodWasteManagement/baseInfoManagement/wasteTreatmentPlant/type';
import DictDropDownSelect from '@/components/global/dictDropDownSelect'
import RegionCascader from '@/components/common/regionCascader/index.vue'
import PointMarkerFormItem from '@/components/common/customizedFormItem/pointMarkerFormItem.vue';


const props = defineProps<{modelValue: IWasteTreatmentPlant}>()

const form = computed(() => props.modelValue)

const onMapTapperConfirm = (point:any, address) => {
    const { province, city, district, township, street, streetNumber } = address;
    // const fillCondition = !form.value.area && !form.value.address
    const fillCondition = !form.value.area
    if (fillCondition) {
        let area = ''
        area += province ? province + ',' : ''
        area += city ? city + ',' : ''
        area += district || ''
        form.value.area = area;
        // form.value.address = township + street + streetNumber;
    }
}
</script>

<style lang="less" scoped>
.show-map {
    cursor: pointer;
    margin-right: 8px;
    font-size: 16px;
    color: #165DFF;
    .ivu-icon {
        font-weight: 700;
    }
}
:deep(.ivu-cascader-rel .ivu-icon-ios-close-circle){
    font-family: "iconfont" !important;
    color: #4E627E;
    font-size: 16px;
    &:before{
        content: "\e705";
    }
}
</style>
