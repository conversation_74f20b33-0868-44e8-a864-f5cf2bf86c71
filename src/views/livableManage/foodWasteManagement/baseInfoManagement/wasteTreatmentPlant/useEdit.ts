import { ref } from 'vue';
import { validateform } from '@/utils/validateform';
import { useRoute, useRouter } from 'vue-router';
import {
    IShipCompany
} from '@/views/livableManage/foodWasteManagement/baseInfoManagement/shippingCompany/type';

// 默认数据工厂///
const createDefaultInfo = ():IShipCompany => {
    return {
        name: '', // 公司名称
        code: '', // 公司编号
        license: '', // 执照代码
        objx: 0, // 经度
        objy: 0, // 纬度
        area: '', // 所属区域
        address: '', // 具体位置
        legalPerson: '', // 企业法人
        legalPhone: '', // 法人电话
        contactPerson: '', // 联系人
        contactPhone: '', // 联系方式
        licensePhoto: '', // 营业执照照片
        note: '', // 备注
        gdx: 0, // 经度
        gdy: 0, // 纬度
    }
}
const checkRequired = (rule: any, value: number | string, callback: any) => {
    console.log('validte', { rule, value })
    if (value) {
        callback()
    } else {
        callback(new Error('请完善必填项'))
    }
}
const formRules = {
    name: validateform.required,
    objx: [checkRequired],
    contactPhone: [{ pattern: /^1[3456789]\d{9}$/, trigger: 'blur', message: '手机号错误' }],
    capacity: [{
        pattern: /^([1-9]\d{0,3}|0|10000)$/,
        message: '请输入0-10000之间的整数'
    }]
}
export const useEdit = () => {
    const isEdit = ref(false);
    const detailInfo = ref<IShipCompany>(createDefaultInfo());
    const isLoading = ref(false);
    const router = useRouter();
    const route = useRoute();
    const clearDetail = () => {
        detailInfo.value = createDefaultInfo();
    }
    return { detailInfo, isEdit, formRules, router, route, isLoading, clearDetail };
}

