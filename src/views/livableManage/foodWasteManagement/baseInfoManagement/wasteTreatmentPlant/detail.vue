<template>
    <div>
        <BreadcrumbCustom>
            <BreadcrumbItem>餐厨垃圾处理</BreadcrumbItem>
            <BreadcrumbItem>基础管理</BreadcrumbItem>
            <BreadcrumbItem to="/kitchenWasteManagement/basic/factory">垃圾处理厂</BreadcrumbItem>
            <BreadcrumbItem>{{ isEdit ? '编辑' : '详情' }}</BreadcrumbItem>
        </BreadcrumbCustom>
        <Form ref="formRef" :model="detailInfo" :rules="formRules" label-position="top">
            <detailCard
                ref="detailRef"
                title="基础信息"
                :src="require('@/assets/images/icon_detail.png')"
                :is-back-btn="true"
                :is-edit-btn="$Util.checkAuth('kitchenWasteManagement:basic:factory:edit')"
                @on-submit="onSubmit"
                @on-edit="onEdit"
                @on-back="onBack"
                :class="[isEdit ? 'edit-card' : '' ]"
            >
                <InfoShow v-if="!isEdit" :detail-info="detailInfo" />
                <EditBox v-model="detailInfo" v-else />
            </detailCard>
        </Form>
    </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import InfoShow from './components/infoShow.vue'
import EditBox from './components/editBox.vue'
import {
    useEdit
} from '@/views/livableManage/foodWasteManagement/baseInfoManagement/wasteTreatmentPlant/useEdit';
import {
    editWasteTreatmentPlant, getGarbageInfo
} from '@/api/livableManage/foodWasteManagementService';


const formRef = ref(null);

const { isEdit, formRules, detailInfo, router, route } = useEdit();

const detailRef = ref(null);

const onInit = async() => {
    const id = route.query.id;
    const res = await getGarbageInfo(id)
    if (res.success) {
        console.log('onInit', res.data)
        detailInfo.value = res.data
    }
    // detailInfo.value = {}
}
onInit();

const onSubmit = async() => {
    const validateRes = await formRef.value?.validate()
    if (validateRes) {
        const params = Object.assign({}, detailInfo.value, { type: 1 })
        const res = await editWasteTreatmentPlant(params);
        if (res.success) {
            isEdit.value = false;
            detailRef.value.isEditFlag = false
            await onInit();
        }
    }
}

const onEdit = (val: boolean) => {
    if (!val) {
        onInit();
    }
    isEdit.value = val;
}

const onBack = () => {
    router.back()
}
</script>

<style lang="less" scoped>
/deep/ .tab-title {
    justify-content: flex-start;
    padding-bottom: 16px;
    height: 48px;
    column-gap: 20px;
}
</style>
