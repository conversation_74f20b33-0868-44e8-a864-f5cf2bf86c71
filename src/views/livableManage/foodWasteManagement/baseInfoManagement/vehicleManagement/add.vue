<template>
    <div>
        <BreadcrumbCustom>
            <BreadcrumbItem>餐厨垃圾处理</BreadcrumbItem>
            <BreadcrumbItem>基础管理</BreadcrumbItem>
            <BreadcrumbItem to="/kitchenWasteManagement/basic/car">车辆管理</BreadcrumbItem>
            <BreadcrumbItem>新增</BreadcrumbItem>
        </BreadcrumbCustom>
        <detailCard title="新增" :is-back-btn="true" @on-back="onBack">
            <Form ref="formRef" :model="detailInfo" :rules="formRules" label-position="top">
                <EditBox v-model="detailInfo" ref="edit" />
                <div class="btn-box">
                    <Button type="primary" @click="onSubmit" :loading="isLoading">提交</Button>
                    <Button @click="onBack">取消</Button>
                </div>
            </Form>
        </detailCard>
    </div>
</template>

<script lang="ts" setup>
import { getCurrentInstance, ref } from 'vue'
import EditBox from './components/editBox.vue'
import {
    useEdit
} from './useEdit';
import { addCar } from '@/api/livableManage/foodWasteManagementService';
const that = getCurrentInstance()?.appContext.config.globalProperties

const { detailInfo, formRules, router, isLoading } = useEdit();
const formRef = ref(null)


const onBack = () => {
    router.back();
}


const onSubmit = async() => {
    const validRes = await formRef.value?.validate()
    if (validRes) {
        isLoading.value = true;
        const params = Object.assign({}, detailInfo.value, { type: 1 })
        console.log('asadfg', params)
        const res = await addCar(params)
        if (res.success) {
            that?.$Message.success('新增车辆成功')
            router.back()
        }
        isLoading.value = false;
    }
}

</script>

<style lang="less" scoped>
.btn-box{
    .ivu-btn+.ivu-btn{
        margin-left: 8px;
    }
}
</style>
