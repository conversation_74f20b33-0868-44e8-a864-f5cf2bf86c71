<template>
    <Row :gutter="80">
        <Col span="8">
            <FormItem label="车辆名称" prop="name" required>
                <Input v-model="form.name" maxlength="20" clearable placeholder="请输入"></Input>
            </FormItem>
        </Col>
        <Col span="8">
            <FormItem label="车辆编号" prop="carCode">
                <Input v-model="form.code" maxlength="20" clearable placeholder="系统自动生成" disabled></Input>
            </FormItem>
        </Col>
        <Col span="8">
            <FormItem label="车牌号码" prop="licenseNo" required>
                <Input v-model="form.licenseNo" maxlength="20" clearable placeholder="请输入"></Input>
            </FormItem>
        </Col>
    </Row>
    <Row :gutter="80">
        <Col span="8">
            <FormItem label="额定载重" prop="load">
                <Input v-model="form.load" clearable>
                    <template #append>
                        吨
                    </template>
                </Input>
            </FormItem>
        </Col>
        <Col span="8">
            <FormItem label="收运公司" prop="companyId" :rules="rules">
                <CollectCompanySelect v-model="form.companyId" :type="1" />
            </FormItem>
        </Col>
        <Col span="8">
            <FormItem label="司机" prop="driver">
                <Input v-model="form.driver" maxlength="20" clearable placeholder="请输入"></Input>
            </FormItem>
        </Col>
    </Row>
    <Row :gutter="80">
        <Col span="8">
            <FormItem label="联系方式" prop="driverPhone">
                <Input v-model="form.driverPhone" maxlength="20" clearable placeholder="请输入"></Input>
            </FormItem>
        </Col>
    </Row>
    <Row>
        <Col span="24">
            <FormItem label="备注" prop="note">
                <sTextarea v-model="form.note"></sTextarea>
            </FormItem>
        </Col>
    </Row>
</template>

<script lang="ts" setup>
import { defineProps, computed, defineEmits, ref } from 'vue'
import {
    ICarInfo
} from '../type/index';
import CollectCompanySelect
    from '@/views/livableManage/garbageCollectionManagement/components/collectCompanySelect';
const props = defineProps<{modelValue: ICarInfo}>()
const emits = defineEmits(['update:modelValue'])
const form = computed({
    get() {
        return props.modelValue
    },
    set(newVal) {
        emits('update:modelValue', newVal)
    }
})

const rules = [{
    required: true,
    message: '请输入必填项'
}]

// 收运公司的option

const companyOption = ref([])

</script>

<style lang="less" scoped>
.show-map {
    font-size: 16px;
    color: #165DFF;
    .ivu-icon {
        font-weight: 700;
    }
}
:deep(.ivu-cascader-rel .ivu-icon-ios-close-circle){
    font-family: "iconfont" !important;
    color: #4E627E;
    font-size: 16px;
    &:before{
        content: "\e705";
    }
}
</style>
