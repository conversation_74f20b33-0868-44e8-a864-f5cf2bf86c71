<template>
    <BreadcrumbCustom>
        <BreadcrumbItem>餐厨垃圾管理</BreadcrumbItem>
        <BreadcrumbItem>基础管理</BreadcrumbItem>
        <BreadcrumbItem>收运公司</BreadcrumbItem>
    </BreadcrumbCustom>
    <ContentCard title="收运公司" :base-btn="false">
        <BaseForm @handle-submit="onSubmit(listRef)" :model="condition" inline :label-width="90">
            <template #formitem>
                <FormItem label="公司名称" prop="name">
                    <Input v-model="condition.name" placeholder="请输入"
                           clearable
                    ></Input>
                </FormItem>
                <FormItem label="公司法人" prop="legalPerson">
                    <Input v-model="condition.legalPerson" placeholder="请输入"
                           clearable
                    ></Input>
                </FormItem>
            </template>
        </BaseForm>
        <btn-card >
            <Button type="primary" @click="onAdd" icon="md-add" v-auth="'kitchenWasteManagement:basic:company:add'">
                新增公司
            </Button>
            <Button icon="ios-trash" @click="onDelete" v-auth="'kitchenWasteManagement:basic:company:del'">
                删除
            </Button>
        </btn-card>
        <baseTable @on-selection-change="onSelectionChange" :model="condition" ref="listRef"
                   :columns="tableColumn" url="/garbageCollectCompany/list"
        >
            <template #action="{ row }">
                <LinkBtn size="small" @click="goDetail(row)">
                    详情
                </LinkBtn>
            </template>
        </baseTable>
    </ContentCard>
</template>

<script lang="ts" setup>


import { getCurrentInstance, onMounted, ref } from 'vue';
import { useRouter } from 'vue-router';
import {
    IWasteTreatmentPlant
} from '@/views/livableManage/foodWasteManagement/baseInfoManagement/wasteTreatmentPlant/type';
import {
    delCompany,
} from "@/api/livableManage/foodWasteManagementService";


const router = useRouter();

const that = getCurrentInstance()?.appContext.config.globalProperties;


const listRef = ref(null);

const condition = ref({
    name: '',
    legalPerson: '',
})

// 详情
const goDetail = ({ id }) => {
    router.push({ name: 'kitchenWasteManagement:basic:company:edit', query: { id }})
}
// 查询
const onSubmit = (ref: any) => {
    console.log('onSubmit')
    const params = Object.assign({}, condition.value, { type: 1 })
    ref && ref.search(params);
}
onMounted(() => {
    onSubmit(listRef.value)
})
// 新增
const onAdd = () => {
    router.push({ name: 'kitchenWasteManagement:basic:company:add' })
}

// 删除
const selectedIds = ref<(string | undefined)[]>([])

const onSelectionChange = (selectedRow: IWasteTreatmentPlant[] = []) => {
    console.log(selectedRow)
    selectedIds.value = selectedRow.map(item => item.id)
}

const onDelete = () => {
    if (selectedIds.value.length === 0) {
        that && that.$Message.warning('最少选择一条数据')
        return
    }
    that && that.$Modal.confirm({
        title: '提示',
        content: '您确定要删除选中的数据吗',
        onOk: () => {
            const ids = selectedIds.value;
            delCompany({ ids })
                .then(res => {
                    if (res.success) {
                        if (res.data) {
                            that && that.$Message.warning(res.data)
                        } else {
                            onSubmit(listRef.value);
                        }
                        selectedIds.value = []
                    }
                })
            console.log('删除id', ids)
        }
    })
}
// 公司名称、执照代码、法人、法人电话、联系人、联系方式、操作
const tableColumn = ref([
    { type: 'selection', width: 40 },
    { title: '公司名称', key: 'name', tooltip: true },
    { title: '执照代码', key: 'license', tooltip: true },
    { title: '公司法人', key: 'legalPerson', tooltip: true },
    { title: '法人电话', key: 'legalPhone', tooltip: true },
    { title: '联系人', key: 'contactPerson', tooltip: true },
    { title: '联系方式', key: 'contactPhone', tooltip: true },
    { title: '操作', slot: 'action', width: 80, lock: true, fixed: true }
])


</script>

<style lang="less" scoped>
/deep/ .ivu-form-item{
    width: 80%;
}

</style>
