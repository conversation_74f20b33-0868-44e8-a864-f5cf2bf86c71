<template>
    <Row :gutter="80">
        <Col span="8">
            <FormItem label="公司名称" prop="name" required>
                <Input v-model="form.name" maxlength="20" clearable placeholder="请输入"></Input>
            </FormItem>
        </Col>
        <Col span="8">
            <FormItem label="公司编号" prop="companyCode">
                <Input v-model="form.code" maxlength="20" clearable placeholder="系统自动生成" disabled></Input>
            </FormItem>
        </Col>
        <Col span="8">
            <FormItem label="执照代码" prop="license">
                <Input v-model="form.license" maxlength="20" clearable placeholder="请输入"></Input>
            </FormItem>
        </Col>
    </Row>
    <Row :gutter="80">
        <Col span="8">
            <PointMarkerFormItem v-model:objx="form.gdx" v-model:objy="form.gdy" @confirm="onMapTapperConfirm" />
        </Col>
        <Col span="8">
            <FormItem label="所在区域" prop="formatregionData">
                <RegionCascader v-model="form.area"></RegionCascader>
            </FormItem>
        </Col>
        <Col span="8">
            <FormItem label="具体位置" prop="address">
                <Input v-model="form.address" maxlength="20" clearable placeholder="请输入"></Input>
            </FormItem>
        </Col>
    </Row>
    <Row :gutter="80">
        <Col span="8">
            <FormItem label="公司法人" prop="legalPerson">
                <Input v-model="form.legalPerson" maxlength="20" clearable placeholder="请输入"></Input>
            </FormItem>
        </Col>
        <Col span="8">
            <FormItem label="法人电话" prop="legalPhone">
                <Input v-model="form.legalPhone" maxlength="20" clearable placeholder="请输入"></Input>
            </FormItem>
        </Col>
        <Col span="8">
            <FormItem label="联系人" prop="contactPerson">
                <Input v-model="form.contactPerson" maxlength="20" clearable placeholder="请输入"></Input>
            </FormItem>
        </Col>
    </Row>
    <Row :gutter="80">
        <Col span="8">
            <FormItem label="联系人电话" prop="contactPhone">
                <Input v-model="form.contactPhone" maxlength="20" clearable placeholder="请输入"></Input>
            </FormItem>
        </Col>
    </Row>
    <Row>
        <Col span="8">
            <FormItem label="营业执照照片" prop="licensePhoto">
                <UploadImg v-model="form.licensePhoto" :maxLength="1" :preview="true"> </UploadImg>
            </FormItem>
        </Col>
    </Row>
    <Row>
        <Col span="24">
            <FormItem label="备注" prop="note">
                <sTextarea v-model="form.note"></sTextarea>
            </FormItem>
        </Col>
    </Row>
</template>

<script lang="ts" setup>
import { defineProps, computed } from 'vue'
import {
    IShipCompany
} from '@/views/livableManage/foodWasteManagement/baseInfoManagement/shippingCompany/type';
import UploadImg from '@/components/common/uploadImg/index.vue'
import RegionCascader from '@/components/common/regionCascader'
import PointMarkerFormItem from '@/components/common/customizedFormItem/pointMarkerFormItem.vue';

const props = defineProps<{modelValue: IShipCompany}>()

const form = computed(() => props.modelValue)

const onMapTapperConfirm = (point:any, address) => {
    const { province, city, district, township, street, streetNumber } = address;
    // const fillCondition = !form.value.area && !form.value.address
    const fillCondition = !form.value.area
    if (fillCondition) {
        let area = ''
        area += province ? province + ',' : ''
        area += city ? city + ',' : ''
        area += district || ''
        form.value.area = area;
        // form.value.address = township + street + streetNumber;
    }
}

</script>

<style lang="less" scoped>
.show-map {
    position: absolute;
    top: 0px;
    left: 100px;
    transform: translate(0, -25%);
    font-size: 16px;
    color: #165DFF;
    cursor: pointer;
    z-index: 1000;
    .ivu-icon {
        font-weight: 700;
    }
}
:deep(.ivu-cascader-rel .ivu-icon-ios-close-circle){
    font-family: "iconfont" !important;
    color: #4E627E;
    font-size: 16px;
    &:before{
        content: "\e705";
    }
}
</style>
