
export interface IRecord {
    id: number,
    taskId: number, // 任务id
    pointId: number,
    num: number, // 垃圾桶数量
    planId: string, // 计划id
    taskName: string, // 任务名称
    pointName: string, // 收运点名称
    status: number, // 运收情况(1已运收0未运收)
    doneGarbageNum: number, // 收集垃圾桶数量
    weight: number, // 收集重量
    content: string, // 收集详情
    time: string, // 收集时间
}

export interface ICollectRecordDetail {
	pointName: string;
	num: number;
	companyName: string;
	creatorId: any;
	contactPerson: string;
	planName: string;
	remark: any;
	garbageCans: Array<IGarbageCan>;
	type: number;
	content: any;
	carLicenseNo: string;
	orderStr: any;
	modifyTime: string;
	pointId: number;
	planId: any;
	startTime: any;
	id: number;
	enterpriseName: string;
	doneGarbageNum: number;
	modifyId: any;
	weight: any;
	deleted: number;
	createTime: string;
	ids: any;
	taskName: string;
	time: any;
	endTime: any;
	taskId: number;
	status: number;
	taskTime: string;
}
export interface IModalInfo {
	companyName: string; // 收运公司
	contactPerson: string; // 收运人
	planName: string; // 收运计划
	carLicenseNo: string; // 收运车辆
    pointId?: string;
	name: string; // 收运任务
	time: string; // 任务时间
    // 收运数量
    enterpriseName?: string; // 所属公司
    garbageCans?: IGarbageCan[];
}

export interface IGarbageCan{
    name: string; // 垃圾桶名称
    weight: string; // 重量
    key?: string;
}
