<template>
    <BreadcrumbCustom>
        <BreadcrumbItem>餐厨垃圾管理</BreadcrumbItem>
        <BreadcrumbItem>收运记录</BreadcrumbItem>
    </BreadcrumbCustom>
    <ContentCard title="收运记录" :base-btn="false">
        <BaseForm @handle-submit="onSubmit(listRef)" :model="condition" inline :label-width="90">
            <template #formitem>
                <FormItem label="收运点" prop="pointId">
                    <SelectByList v-model="condition.pointId" :type="1" url="/garbagePoint/list" clearable />
                </FormItem>
                <FormItem label="收运计划" prop="planId">
                    <SelectByList v-model="condition.planId" label-name="planName" value-name="id" url="/garbagePlan/list" :type="1"  placeholder="请输入" clearable />
                </FormItem>
                <FormItem label="收运任务" prop="taskName">
                    <SelectByList v-model="condition.taskName" label-name="name" value-name="name" :type="1" url="/garbagePlanTask/list" clearable />
                </FormItem>
                <FormItem label="收运时间" prop="startTime">
                    <date-picker type="datetimerange"
                                 v-model="formatFillTime"
                                 placeholder="请选择"
                                 clearable />
                </FormItem>
            </template>
        </BaseForm>
        <baseTable :model="condition"
                   ref="listRef"
                   :columns="tableColumn"
                   empty-block-str="--"
                   url="/garbageTaskRecords/detail/list">
            <template #weight="{ row }">
                {{ formatWeight(row.weight) }}
            </template>
            <template #action="{ row }">
                <LinkBtn size="small" @click="goDetail(row)">
                    详情
                </LinkBtn>
                <LinkBtn size="small" @click="goEdit(row)">
                    编辑
                </LinkBtn>
                <LinkBtn size="small" @click="onDelete(row)">
                    删除
                </LinkBtn>
            </template>
        </baseTable>
    </ContentCard>
    <!-- <s-modal title="编辑收运记录" @emit-close="onCloseModal" @handleSubmit="onSubmitModal" :styles="{top: '18px' }" :ref-box="addModalRef" ref="modalRef" width="625">
        <AddCollectModal :is-detail="false" v-model="modalInfo" :id="modalId" ref="addModalRef"></AddCollectModal>
    </s-modal> -->
</template>

<script lang="ts" setup>

import { computed, getCurrentInstance, onMounted, ref } from 'vue';
import SelectByList from '@/components/common/selectByList';
import AddCollectModal
    from './components/addCollectModal.vue'
import { useEdit } from './useEdit';
import {
    delReceiptAndShipmentRecords
} from '@/api/livableManage/foodWasteManagementService';
import { DatePicker } from 'view-ui-plus';
import moment from 'moment';
import tooltipAutoShow from '@/components/global/tooltipAutoShow/index.vue';

const { router, modalInfo, clearModalInfo } = useEdit();

const that = getCurrentInstance()?.appContext.config.globalProperties;

const listRef = ref(null);

const condition = ref({
    pointId: '', //  收运点
    taskId: '', // 收运任务
    taskName: '', // 任务名称
    time: '', // 车辆
    startTime: '',
    endTime: '',
    planId:''

})

// 详情
const goDetail = ({ id }) => {
    router.push({ name: 'kitchenWasteManagement:collectionRecord:detail', query: { id, isFromRecord: 1 }})
}
// 查询
const onSubmit = (ref: any) => {
    const params = Object.assign({}, condition.value, { type: 1, status: 1 })
    ref && ref.search(params);
}
onMounted(() => {
    onSubmit(listRef.value)
})

// 收运弹窗相关
const modalId = ref(-1) // 弹窗id
const modalPointId = ref(-1) // 弹窗pointId
const addModalRef = ref()
const modalRef = ref()
const goEdit = (row:any) => {
    // 
    // if (row.id) {
    //     modalPointId.value = row.pointId;
    //     modalId.value = row.id;
    //     modalRef.value?.handleOpen();
    // }
    router.push({
        name:'kitchenWasteManagement:collectionRecord:collection',
        query:{
            id:row.id,
            planId:row.planId,
            typeCode:1,
        }
    })
}
// 收运弹窗确认之后刷新列表
const onSubmitModal = () => {
    onSubmit(listRef.value)
}
const onCloseModal = () => {
    clearModalInfo();
}
const onDelete = ({ id }) => {
    if (id) {
        that && that.$Modal.confirm({
            title: '提示',
            content: '您确定要删除选中的数据吗',
            onOk: () => {
                const params = {
                    ids: [id]
                }
                delReceiptAndShipmentRecords(params)
                    .then(res => {
                        if (res.success) {
                            that?.$Message.success('删除成功')
                            onSubmit(listRef.value);
                        }
                    })
            }
        })
    }
}

const formatFillTime = computed({
    get() {
        if (!condition.value.startTime) {
            return ''
        }
        return [moment(condition.value.startTime).toDate(), moment(condition.value.endTime).toDate()]
    },
    set(newDateTimeRange) {
        
        const [startDateTime, endDateTime] = newDateTimeRange;
        condition.value.startTime = startDateTime ? moment(startDateTime).format('YYYY-MM-DD HH:mm:ss') : '';
        condition.value.endTime = endDateTime ? moment(endDateTime).format('YYYY-MM-DD HH:mm:ss') : '';
    }
})

const taskParams = computed(() => {
    return {
        pointId: condition.value.pointId
    }
})

const formatWeight = (num:number) => {
    const newNum = +num;
    if (isNaN(newNum) || !num) return '--';
    if (Number.isInteger(newNum)) {
        // 如果是整数，不做处理
        return newNum
    } else {
        // 如果是小数，保留两位小数
        return newNum.toFixed(2);
    }
}

const tableColumn = ref([
    { title: '收运点', key: 'pointName', tooltip: true },
    { title: '收运任务', key: 'taskName', tooltip: true },
    { title: '收运计划', key: 'planName', tooltip: true },
    { title: '应收垃圾桶数', width: 110, key: 'num', tooltip: true },
    { title: '实收垃圾桶数', width: 110, key: 'receivableNum', tooltip: true },
    { title: '收集总重量(kg)', width: 120, slot: 'weight', tooltip: true },
    { title: '收运详情', key: 'content', minWidth: 120, tooltip: true, default: '--' },
    { title: '收运人', width: 90, key: 'contactPerson', tooltip: true },
    { title: '收运时间', width: 160, key: 'time', tooltip: true },
    { title: '操作', slot: 'action', width: 150, lock: true, fixed: true }
])



</script>

<style lang="less" scoped>
/deep/ .ivu-form-item{
    width: 80%;
}
.action-box {
    margin-bottom: 16px;

    .ivu-btn {
        margin-right: 8px;
    }
}
</style>
