import { ref } from 'vue';
import { validateform } from '@/utils/validateform';
import { useRoute, useRouter } from 'vue-router';
import { getGarbageInfo } from '@/api/livableManage/foodWasteManagementService';
import { IGarbageCan, IModalInfo, IRecord } from './type/index';

const createRecordInfo = ():IRecord => {
    return {
        id: 0,
        taskId: 0, // 任务id
        pointId: 0,
        num: 0, // 垃圾桶数量
        planId: '', // 计划id
        taskName: '', // 任务名称
        pointName: '', // 收运点名称
        status: 0, // 运收情况(1已运收0未运收)
        doneGarbageNum: 0, // 收集垃圾桶数量
        weight: 0, // 收集重量
        content: '', // 收集详情
        time: '', // 收集时间
    }
}
const createModalInfo = ():IModalInfo => {
    return {
        companyName: '', // 收运公司
        contactPerson: '', // 收运人
        planName: '', // 收运计划
        carLicenseNo: '', // 收运车辆
        pointId: '',
        name: '', // 收运任务
        time: '', // 任务时间
        // 收运数量
        enterpriseName: '', // 所属公司
        garbageCans: [],
    }
}
const checkRequired = (rule: any, value: number | string, callback: any) => {
    console.log('validte', { rule, value })
    if (value) {
        callback()
    } else {
        callback(new Error('请完善必填项'))
    }
}
const formRules = {
    name: validateform.required,
    objx: [checkRequired],
    num: [{
        required: true,
        message: '请完善必填项'
    }]
}
// 根据该收运点的垃圾桶数量，自动显示对应的垃圾桶个数的输入框，
// 数字填写，必填项，至少填一个
// 范围0-1000  最多两位小数
// 收运时间，打开弹窗时，默认带出显示当前时间，可以进行人为更改，必填项
const modalRules = {
    time: validateform.required
}

export const useEdit = () => {
    const isEdit = ref(false);
    const detailInfo = ref<IRecord>(createRecordInfo());
    const modalInfo = ref<IModalInfo>(createModalInfo());
    const isLoading = ref(false);

    const router = useRouter();
    const route = useRoute();


    const clearModalInfo = () => {
        modalInfo.value = createModalInfo();
    }
    const onInit = async(id: string | undefined) => {
        if (isEdit) {
            console.log('onEdit', id);
            const res = await getGarbageInfo({ id })
            console.log(res);
        }
    }

    return { detailInfo, modalInfo, isEdit, formRules, router, route, onInit, isLoading, clearModalInfo, modalRules }
}

