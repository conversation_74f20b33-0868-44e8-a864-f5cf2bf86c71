<template>
    <Modal v-model="showFlag" title="灌溉控制" :width="420" :mask-closable="false" @on-visible-change="visibleChange">
        <div class="control-main">
            <RadioGroup v-model="s.state" vertical>
                <Radio :label="1">
                    <span>{{controlText.open}}</span>
                </Radio>
                <Radio :label="0">
                    <span>{{controlText.off}}</span>
                </Radio>
            </RadioGroup>
            <div class="h5">浇水时长</div>
            <InputNum v-model="s.controlDuration" @on-change="changeDur" :disabled="s.state != 1" append="分钟"
                style="width: 170px" />
            <div class="msg-box">
                <div class="msg" v-if="msg">{{ msg }}</div>
            </div>

        </div>
        <template #footer>
            <div class="btn-box">
                <Button @click="closeModal">取消</Button>
                <Button type="primary" @click="confirm" :loading="s.loading">确定</Button>
            </div>
        </template>
    </Modal>
</template>

<script>
export default {
    name: 'controlModal',
    props: {
        controlText:{
            default:{
                open:'开启',
                off:'关闭'
            }
        },
        url:{default:'/irrigateControlRecord/irrigateControl'},
        show: { default: false },
        nodeId:{default:'refId'},
        nodeKey: { default:'controlDuration'},
        detail: { default() { return {} } },
        controlSource: { default: 2 }, // 控制源，1单控，2分组控制，3自动控制
        controlType: { default: 1 } // 控制类型，1开关状态
    },
    data() {
        return {
            s: {
                state: 1, // 控制动作（开关状态）1开启，0关闭
                controlDuration: 1,
                loading: false
            },
            msg: ''
        }
    },
    computed: {
        showFlag() {
            return !!this.show
        }
    },
    methods: {
        confirm() {
            if (this.msg) {
                return
            }
            this.$Modal.confirm({
                title: '控制指令发送',
                content: '确认是否要下发控制指令',
                onOk: () => {
                    this.control()
                }
            })
        },
        control() {
            let param = {
                [this.nodeId]: this.detail.id,
                controlSource: this.controlSource,
                controlType: this.controlType,
                switchState: this.s.state
            }
            if (this.s.state == 1) {
                param[this.nodeKey] = this.s.controlDuration
            }
            this.s.loading = true
            this.$request(this.url, param, 'post').then(res => {
                if (res.success) {
                    this.$Message.success('操作成功')
                    this.$emit('on-update')
                    this.closeModal()
                }
            }).finally(() => {
                this.s.loading = false
                this.s = {
                    state: 1,
                    controlDuration: 1,
                    loading: false
                }
            })
        },
        changeDur(value) {
            if (value < 1) {
                this.msg = '请输入有效的浇水时长'
            } else if (value > 480) {
                this.msg = '单次最大浇水时长不能超过480分钟，请重新输入'
            } else {
                let reg = /^[1-9]\d*$/
                if (reg.test(value)) {
                    this.msg = ''
                } else {
                    this.msg = '请输入正整数'
                }
            }
        },
        format(val) {
            return val + '%';
        },
        visibleChange(flag) {
            if (!flag) {
                this.closeModal()
            }
        },
        closeModal() {
            this.$emit('on-close')
            this.s = {
                state: 1,
                controlDuration: 1,
                loading: false
            }
        }
    }
}
</script>

<style lang="less" scoped>
.control-main {
    padding: 0 16px;

    .h5 {
        color: @text-color;
        margin-bottom: 8px;
    }
    .msg-box{
        height: 20px;
        line-height: 20px;
    }
    .msg{
        color: @error-color;
        font-size: 12px;
    }
    .ivu-radio-group-item {
        margin: 8px 0;
    }

    .slide-light {
        display: flex;
        align-items: center;
        margin-left: 24px;

        .ivu-slider {
            width: 200px;
            margin: 0 10px;
        }
    }
}
</style>
