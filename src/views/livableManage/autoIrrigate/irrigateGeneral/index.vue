<template>
    <div>
        <BreadcrumbCustom>
            <BreadcrumbItem>自动灌溉</BreadcrumbItem>
            <BreadcrumbItem>灌溉概况</BreadcrumbItem>
        </BreadcrumbCustom>
        <ContentCard style="margin-bottom: 16px;">
            <div class="state-list">
                <imgCard
                    :img="require('./images/icon_gen1.png')" label="灌溉面积"
                    :value="m.total.irrigateArea?.countValue" unit="m²" />
                <imgCard
                    :img="require('./images/icon_gen2.png')" label="年灌溉用水量"
                    :value="m.total.yearUserWater?.countValue" unit="吨" />
                <imgCard
                    :img="require('./images/image.png')" label="灌溉率"
                    :value="m.total.irrigateRate?.countValue" unit="%" />
                <imgCard
                    :img="require('./images/icon_gen3.png')" label="电磁阀控制器总数"
                    :value="m.total.controlCount?.countValue" unit="个" />
                <imgCard
                    :img="require('./images/icon_gen4.png')" label="电磁阀总数"
                    :value="m.total.termCount?.countValue" unit="个" />
            </div>
        </ContentCard>
        <ContentCard title="灌溉能耗变化曲线" style="margin-bottom: 16px;">
            <echart-item :option="lineOption" class="line-echart" />
        </ContentCard>
        <Row :gutter="16">
            <Col span="12">
                <ContentCard title="用水量周同期对比">
                    <echart-item :option="barOption" class="line-echart" />
                </ContentCard>
            </Col>
            <Col span="12">
                <ContentCard title="近一周用水量分布">
                    <div style="position: relative">
                        <echart-item :option="pieOption" class="line-echart" />
                        <div class="total">
                            <p>总共</p>
                            <p>{{ m.waterVolTotal }}吨</p>
                        </div>
                        <legendCustom
                            :list="pieOption.series[0].data"
                            :color="pieOption.color"
                            :styles="{width: '38%'}" />

                    </div>

                </ContentCard>
            </Col>
        </Row>

    </div>
</template>

<script>
import EchartItem from '@/components/common/EchartItem/index'
import legendCustom from '@/components/common/EchartItem/legendCustom'
import { EchartsTooltip, tooltipExtraCssText } from '@/utils/tool.ts'
export default {
    name: 'Index',
    components: {
        EchartItem,
        legendCustom
    },
    data() {
        return {
            lineOption: {
                tooltip: {
                    trigger: 'axis',
                    confine: true,
                    axisPointer: {},
                    extraCssText: tooltipExtraCssText,
                    formatter: function(arg) {
                        return EchartsTooltip(arg, ['吨', 'kWh'])
                    }
                },
                grid: {
                    top: 30,
                    bottom: 5,
                    left: 10,
                    right: 0,
                    containLabel: true
                },
                legend: {
                    show: true,
                    itemHeight: 10,
                    itemWidth: 10,
                    icon: 'circle',
                    top: 0
                },
                xAxis: [
                    {
                        type: 'category',
                        axisLabel: {
                            color: '#86909C'
                        },
                        axisLine: {
                            show: true,
                            lineStyle: {
                                color: '#E5E6EB'
                            }
                        },
                        axisTick: {
                            show: false,
                            lineStyle: {
                                color: '#86909C'
                            }
                        },
                        data: []
                    }
                ],
                yAxis: [
                    {
                        type: 'value',
                        name: '单位：吨',
                        nameTextStyle: {
                            align: 'left'
                        },
                        splitLine: {
                            show: true,
                            lineStyle: {
                                color: '#F2F3F5'
                            }
                        },
                        axisLabel: {
                            color: '#4E5969'
                        }
                    },
                    {
                        type: 'value',
                        name: '单位：kWh',
                        nameTextStyle: {
                            align: 'right'
                        },
                        splitLine: {
                            show: true,
                            lineStyle: {
                                color: '#F2F3F5'
                            }
                        },
                        axisLabel: {
                            color: '#4E5969'
                        }
                    }
                ],
                color: ['#21CCFF', '#FFCF8B'],
                series: [
                    {
                        data: [],
                        name: '用水量',
                        type: 'line',
                        smooth: true,
                        showSymbol: false,
                        areaStyle: {
                            color: {
                                type: 'linear',
                                x: 0,
                                y: 0,
                                x2: 0,
                                y2: 1,
                                colorStops: [
                                    {
                                        offset: 0,
                                        color: 'rgba(17, 126, 255, 0.16)'
                                    },
                                    {
                                        offset: 1,
                                        color: 'rgba(17, 128, 255, 0)'
                                    }
                                ],
                                global: false
                            }
                        }
                    },
                    {
                        data: [],
                        name: '用电量',
                        type: 'line',
                        smooth: true,
                        showSymbol: false,
                        areaStyle: {
                            color: {
                                type: 'linear',
                                x: 0,
                                y: 0,
                                x2: 0,
                                y2: 1,
                                colorStops: [
                                    {
                                        offset: 0,
                                        color: 'rgba(255, 207, 139, 0.16)'
                                    },
                                    {
                                        offset: 1,
                                        color: 'rgba(255, 207, 139, 0)'
                                    }
                                ],
                                global: false
                            }
                        },
                        yAxisIndex: 1
                    }
                ]
            },
            barOption: {
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow'
                    },
                    extraCssText: tooltipExtraCssText,
                    formatter: function(params) {
                        return EchartsTooltip(params, '吨 ')
                        // let html = `<div>${params[0].axisValue}</div>`
                        // params.forEach(k => {
                        //     html += `<div>${k.marker}${k.seriesName} ${k.value}吨</div>`
                        // })
                        // return html
                    }
                },
                grid: {
                    top: 30,
                    bottom: 30,
                    left: 10,
                    right: 0,
                    containLabel: true
                },
                color: ['#246EFF', '#21CCFF', '#9A81FF'],
                legend: {
                    show: true,
                    itemHeight: 10,
                    itemWidth: 10,
                    icon: 'circle',
                    bottom: 0
                },
                xAxis: [
                    {
                        type: 'category',
                        data: [],
                        axisPointer: {
                            type: 'shadow'
                        },
                        axisLabel: {
                            color: '#86909C'
                        },
                        axisLine: {
                            show: true,
                            lineStyle: {
                                color: '#E5E6EB'
                            }
                        },
                        axisTick: {
                            show: false,
                            lineStyle: {
                                color: '#86909C'
                            }
                        }
                    }
                ],
                yAxis: [
                    {
                        type: 'value',
                        name: '单位：吨',
                        splitLine: {
                            show: true,
                            lineStyle: {
                                color: '#F2F3F5'
                            }
                        },
                        axisLabel: {
                            color: '#4E5969'
                        }
                    }
                ],
                series: [
                    {
                        name: '本期',
                        type: 'bar',
                        barWidth: 24,
                        tooltip: {
                            valueFormatter: function(value) {
                                return value + ' 吨';
                            }
                        },
                        data: []
                    },
                    {
                        name: '同期',
                        type: 'bar',
                        barWidth: 24,
                        tooltip: {
                            valueFormatter: function(value) {
                                return value + ' 吨';
                            }
                        },
                        data: []
                    }
                ]
            },
            pieOption: {
                tooltip: {
                    trigger: 'item',
                    formatter: (params) => {
                        let html = `<div>${params.name}</div>`
                        html += `<div>${params.marker}${params.value}吨</div>`
                        html += `<div>${params.marker}${params.percent}%</div>`
                        return html
                    }
                },
                legend: {
                    show: false
                },
                color: ['#249EFF', '#9391FF', '#FE7B32', '#21CCFF', '#86DF6C', '#0E42D2'],
                series: [
                    {
                        name: '近一周用水量分布',
                        type: 'pie',
                        center: ['30%', '50%'],
                        radius: ['45%', '65%'],
                        avoidLabelOverlap: false,
                        percentPrecision: 0,
                        itemStyle: {
                            borderColor: '#fff',
                            borderWidth: 2,
                            color: (params) => {
                                return this.pieOption.color[params.dataIndex % this.pieOption.color.length]
                            }
                        },
                        label: {
                            show: true,
                            position: 'outside',
                            formatter: '{d}%'
                        },
                        labelLine: {
                            show: true
                        },
                        emphasis: {
                        },
                        data: []
                    }
                ]
            },
            m: {
                total: {},
                waterVolTotal: '', // 用水总量
            }
        }
    },
    created() {
        this.getNumData()
        this.getWaterRangeUseCount()
        this.getWeekWaterCompareCount()
        this.getWeekWaterDistCount()
    },
    methods: {
        getNumData() {
            this.$request('/irrigateView/viewCount', {}, 'post').then(res => {
                if (res.success) {
                    let total = {}
                    res.data.forEach(item => {
                        item.countValue = this.$Util.formatNum(item.countValue)
                        total[item.countKey] = item
                    })
                    this.m.total = total
                }
            })
        },
        getWaterRangeUseCount() {
            let time = this.$Util.getDateRangeS(new Date(), 'cd', 'YYYY-MM-DD HH:mm:ss', false, 14)
            let param = {
                customQueryParams: {
                    startTime: time[0],
                    endTime: time[1]
                },
                page: {
                    current: 1,
                    size: -1
                }
            }
            this.$request('/irrigateView/waterRangeUseCount', param, 'post').then(res => {
                if (res.success) {
                    let obj = {
                        day: [],
                        data: [],
                        d2: []
                    }
                    res.data.forEach(item => {
                        obj.day.push(this.$Util.formatDate(item.countKey, 'YYYY-MM-DD'))
                        obj.data.push(this.$Util.formatNum(item.countValue))
                        obj.d2.push(this.$Util.formatNum(item.extendData || 0))
                    })
                    this.lineOption.xAxis[0].data = obj.day
                    this.lineOption.series[0].data = obj.data
                    this.lineOption.series[1].data = obj.d2
                }
            })
        },
        getWeekWaterCompareCount() {
            this.$request('/irrigateView/weekWaterCompareCount', {}, 'post').then(res => {
                if (res.success) {
                    let obj = {
                        day: [],
                        d1: [],
                        d2: []
                    }
                    res.data.forEach(item => {
                        obj.day.push(item.countKeyName)
                        // obj.day.push(this.$Util.formatDate(item.time, 'YYYY-MM-DD'))
                        obj.d1.push({ value: this.$Util.formatNum(item.countValue), name: item.countKey })
                        obj.d2.push({ value: this.$Util.formatNum(item.extendData.countValue), name: item.extendData.countKey })

                    })
                    this.barOption.xAxis[0].data = obj.day
                    this.barOption.series[0].data = obj.d1
                    this.barOption.series[1].data = obj.d2
                }
            })
        },
        getWeekWaterDistCount() {
            this.$request('/irrigateView/weekWaterDistCount', {}, 'post').then(res => {
                if (res.success) {
                    let obj = {
                        day: []
                    }
                    let waterVolTotal = 0
                    res.data.forEach(item => {
                        let val = this.$Util.formatNum(item.countValue)
                        obj.day.push({ value: val, name: item.countKeyName })
                        waterVolTotal += val
                    })
                    this.m.waterVolTotal = this.$Util.formatNum(waterVolTotal)
                    this.pieOption.series[0].data = obj.day
                }
            })
        }
    }
}
</script>

<style lang="less" scoped>
.state-list{
    display: flex;
    align-items: center;
    padding-left: 9px;

}
.line-echart{
    height: 300px;
}
.total{
    position: absolute;
    width: 24%;
    top: 30%;
    height: 39%;
    left: 18%;
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}
</style>
