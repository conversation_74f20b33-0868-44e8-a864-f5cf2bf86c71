.left-plate {
    .device-box {
        flex: 1;
        position: relative;
        height: ~'calc(45% - 8px)';
        .close-device {
            display: flex;
            justify-content: flex-end;
            height: 20px;
            position: absolute;
            top: -10px;
            right: -10px;

            /deep/.ivu-icon-md-close{
                cursor: pointer;
                width: 20px;
                line-height: 20px;
                background: #fff;
                filter: drop-shadow(0px 1px 6px rgba(61, 103, 175, 0.2));
                border-radius:100%;
            }
        }
        &:hover {
            .device-cont {
                display: flex!important;
            }
        }
        .device-cont {
            height: 100%;
            height: 100%;
            position: absolute;
            left: 224px;
            width: 224px;
            top: 0px;
            overflow: visible!important;
            padding-top: 16px!important;
            display: none!important;
            .name {
                flex: 1;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
            }
        }
        .device-type-list {
            height: 100%;
            width: 220px;
            padding: 0 8px;
            display: flex;
            .device-type-item {
                height: 36px;
                line-height: 36px;
                color: #4E627E;
                font-weight: 400;
                font-size: 14px;
                display: flex;
                justify-content: space-between;
                padding: 0 12px 0 6px;
                // margin-top: 12px;
                cursor: pointer;
                &.actived {
                    background: #F5F7FA;
                    color: @primary-color;
                }
                &:hover {
                    background: #F5F7FA;
                }
            }
        }
    }
}
.right-plate {
    /deep/.device-info-cont {
        .online-overview {
            // height: 220px!important;
        }
    }
}

/deep/.head-cont {
    &.online {
        .icon-img {
            background: #FFFFFF url('./images/icon_manholecover_009.png') no-repeat center;
            background-size: 40px 40px;
        }
    }
    .icon-img {
        height: 50px;
        width: 50px;
        background: #FFFFFF url('./images/icon_manholecover_001.png') no-repeat center;
        background-size: 40px 40px;
        border-radius: 100%;
        position: absolute;
        left: 50%;
        transform: translate(-50%, 35px);
        .top-icon {
            height: 18px!important;
            width: 18px!important;
        }
    }
}
