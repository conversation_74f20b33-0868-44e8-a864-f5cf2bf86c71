<template>
    <div class="details-container">
        <div class="head-cont" :class="deviceDetail?.device?.status == 1 ? 'online' : ''">
            <div class="icon-img">
                <img src="../images/icon_autoIrrigate_002.png" class="top-icon" v-if="deviceDetail?.type == 2" />
                <img src="../images/icon_autoIrrigate_001.png" class="top-icon" v-if="deviceDetail?.type == 1" />
            </div>
            <s-tag :color="deviceDetail?.device?.status == 1 ? '#00B42A' : '#1E2A55'"
                :background="deviceDetail?.device?.status == 1 ? '#E8FFEA' : '#F2F3F5'" class="status-tag">
                {{ deviceDetail?.device?.status == 1 ? '在线' : '离线' }}
            </s-tag>
        </div>
        <div class="name">
            {{ deviceDetail?.deviceExtendInfo?.sbmc }}
        </div>
        <div class="code">
            {{ deviceDetail?.deviceCode }}
        </div>
        <no-data v-if="!activedObjId" value="请先选择设施" />
        <s-tab :tab-list="tabList" class="tab-info" v-else>
            <template #detail>
                <div class="scorll-map-cont">
                    <div class="device-info-list">
                        <s-label label="设备标识码" :value="deviceDetail?.deviceExtendInfo?.bsm" />
                        <s-label label="使用状态" :value="deviceDetail?.useStatus == 1 ? '启用' : '停用'" />
                        <s-label label="区域位置" :value="deviceDetail?.deviceExtendInfo?.areaPath?.replace(/@/g, '/')" />
                        <s-label label="权属单位" :value="deviceDetail?.objInfo?.ownerEnterpriseName" />
                        <s-label label="联 系 人" :value="deviceDetail?.objInfo?.contactPerson || '-'" />
                        <s-label label="联系电话" :value="deviceDetail?.objInfo?.contactPhone || '-'" />
                    </div>
                    <div class="alarm-info-list" v-show="deviceDetail?.devicePropertyStatusList?.length">
                        <s-label v-for="(item, index) in deviceDetail?.devicePropertyStatusList || []" :key="index"
                            :label="findValue((deviceDetail as any)?.physicalModel.properties, item.prop, 'identifier', 'name')"
                            :value="handleValue(item)">
                            <template #value v-if="item.prop == 'switch_state'">
                                <span v-if="item.value == '1'">开</span>
                                <span v-else>关</span>
                            </template>
                        </s-label>
                    </div>
                </div>
            </template>
            <template #alarm>
                <div class="scorll-map-cont">
                    <no-data v-if="!deviceAlarm.length" value="暂无告警" />
                    <Steps :current="2" direction="vertical" size="small">
                        <Step v-for="(item, index) in deviceAlarm || []" :key="index" :title="item.content"
                            :content="item.alarmTime"></Step>
                    </Steps>
                </div>
            </template>
        </s-tab>
    </div>
</template>
<script lang="ts">
import { defineComponent, ref, watch } from 'vue'
import { ManholeCoverAlarm } from '@/api/manholeCoverService'
import { alarm, getIrrigateDeviceDetail } from '@/api/livableManage/autoIrrigate'
import { deviceExtendInfo } from '@/api/livableManage/type'
import { findValue, getValue } from 'wei-util'
export default defineComponent({
    props: {
        activedObjId: {
            type: Number,
            default: ''
        }
    },
    setup(props, ctx) {
        // 设备详情
        const deviceDetail = ref<deviceExtendInfo>()
        const getDetail = async () => {
            if (!props.activedObjId) {
                deviceDetail.value = {
                    objInfo: {},
                    extendInfo: {},
                    deviceExtendInfo: {},
                }
                return
            }
            const res = await getIrrigateDeviceDetail(props.activedObjId)
            const { data, success }: { data: deviceExtendInfo, success: boolean } = res as unknown as HttpResponse<deviceExtendInfo>
            if (success) {
                deviceDetail.value = data
                if (data.deviceCode) {
                    getManholeMapAlarm(data.deviceCode)
                }
            }
        }
        //设备告警
        const deviceAlarm = ref<ManholeCoverAlarm[]>([])
        const getManholeMapAlarm = async (deviceCode: string) => {
            if (!props.activedObjId) return
            const res = await alarm({ deviceCode })
            const { data, success }: { data: ManholeCoverAlarm[], success: boolean } = res as unknown as HttpResponse<ManholeCoverAlarm[]>
            if (success) {
                deviceAlarm.value = data

            }
        }
        watch(() => props.activedObjId, () => {
            getDetail()

        })
        const tabList = ref([
            {
                name: '设备详情',
                key: 'detail',
                icon: '1'
            },
            {
                name: '告警日志',
                key: 'alarm',
                icon: '1'
            }
        ])
        const handleValue = (item: any) => {
            deviceDetail.value as any
            const type = findValue(getValue(deviceDetail.value, 'physicalModel.properties'), item.prop, 'identifier', 'dataType.type')
            if (type === 'enum') {
                return findValue(getValue(deviceDetail.value, 'physicalModel.properties'), item.prop, 'identifier', 'dataType.specs')[getValue(item, 'value')]
            } else {
                return getValue(item, 'value') + findValue(getValue(deviceDetail.value, 'physicalModel.properties'), item.prop, 'identifier', 'dataType.specs.unit')
            }
        }
        return {
            tabList,
            deviceDetail,
            deviceAlarm,
            findValue,
            handleValue
        }
    }
})
</script>
