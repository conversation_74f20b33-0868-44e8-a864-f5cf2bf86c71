<template>
    <div class="plate-tit">
        设备总量
    </div>
    <div class="device-overview">
        <div class="device-num">
            {{ onlineTotal.sum }}
            <div class="online-num">
                在线率<span>{{ onlineTotal.sum ? (onlineTotal.online * 100 / onlineTotal.sum).toFixed(1) : '0' }}%</span>
            </div>
        </div>
        <div class="device-echart">
            <echart-item :option="pieOption" class="pie-echart" ref="pieRef" />
        </div>
    </div>
    <div class="plate-tit">
        <img src="../images/icon_manholecover_011.png" class="title-icon" />
        在线情况
    </div>
    <div class="online-overview">
        <echart-item :option="lineOption" class="pie-echart" ref="lineRef" />
    </div>
    <div class="plate-tit">
        <img src="../images/icon_manholecover_010.png" class="title-icon" />
        告警情况
    </div>
    <div class="alarm-overview">
        <div class="alarm-info">
            <div class="alarm-info">
                <img src="../images/icon_manholecover_012.png" class="title-icon" />
                <div class="ml-6">
                    告警设备数
                    <div class="num">
                        {{ alarmList.alarm }}
                    </div>
                </div>
            </div>
            <div class="alarm-info">
                <img src="../images/icon_manholecover_013.png" class="title-icon" />
                <div class="ml-6">
                    正常设备数
                    <div class="num">
                        {{ alarmList.alarmTotal }}
                    </div>
                </div>
            </div>
        </div>
        <div class="alarm-list">
            <div class="alarm-item" v-for="(item, index) in alarmList.alarmList || []" :key="index">
                <Tooltip :content="item.sbmc" :max-width="200" placement="top" :disabled="(item.sbmc?.length || 0) < 5">
                    {{ item.sbmc }}
                </Tooltip>
                <Tooltip :content="item.content" :max-width="200" placement="top"
                    :disabled="(item.content?.length || 0) < 5">
                    {{ item.content }}
                </Tooltip>
                <span>{{ item.alarmTime }}</span>
            </div>
        </div>
    </div>
</template>
<script lang="ts">
import { defineComponent, ref, watch } from 'vue'
import { ManholeCoverOnline, ManholeCoverAlarmList } from '@/api/manholeCoverService';
import { day, online, alarms } from '@/api/livableManage/autoIrrigate'
import EchartItem from '@/components/common/EchartItem/index'
import { ECOption } from '@/components/common/EchartItem/echart';
export default defineComponent({
    components: {
        EchartItem
    },
    props: {
        areaPaths: {
            type: Array,
            default: () => []
        },
        activedType: {
            type: Number,
            default: ''
            //'灌溉电磁阀控制管理', 4 type1
            //'灌溉电磁阀管理',5
        }
    },
    setup(props) {
        const typeList = {
            1: [4],
            2: [5]
        }
        // *********************
        // 在线统计
        // *********************
        // 在线统计
        const onlineTotal = ref<ManholeCoverOnline>({
            online: 0,
            sum: 0
        })

        // 在线饼状图
        const pieOption = ref<ECOption>({
            tooltip: {
                trigger: 'item',
                confine: true,
            },
            legend: {
                top: 'middle',
                left: 120,
                icon: 'circle',
                itemWidth: 6,
                itemHeight: 6,
                orient: 'vertical',
                textStyle: {
                    color: '#4E627E'
                },
                formatter: (name: String) => {
                    return `${name}  ${name == '在线' ? onlineTotal.value.online : (onlineTotal.value.sum - onlineTotal.value.online)}`
                }
            },
            color: ['#7BE188', '#FFCF8B'],
            series: [
                {
                    name: '设备在离线',
                    type: 'pie',
                    radius: ['48%', '68%'],
                    center: ['32%', '50%'],
                    avoidLabelOverlap: false,
                    label: {
                        show: false,
                        position: 'center'
                    },
                    emphasis: {
                        label: {
                            show: true,
                            fontSize: 14,
                            fontWeight: 'bold'
                        }
                    },
                    labelLine: {
                        show: false
                    },
                    data: []
                }
            ]
        })
        const getMapOnline = async () => {
            const params = {
                areaPaths: props.areaPaths,
                modelIds: typeList[props.activedType] || [4, 5]
            }
            const res = await online(params)
            const { data, success }: { data: ManholeCoverOnline, success: boolean } = res as unknown as HttpResponse<ManholeCoverOnline>
            if (success) {
                onlineTotal.value = data
                pieOption.value.series![0].data = [{ name: '在线', value: data.online }, { name: '离线', value: data.sum - data.online }]

            }
        }
        // *********************
        // 在线情况柱状图
        // *********************
        // 在线柱状图
        const lineOption = ref<ECOption>({
            tooltip: {
                trigger: 'axis',
                axisPointer: {},
                confine: true,
                formatter: '{b}：{c}'
            },
            grid: {
                top: 30,
                bottom: 25,
                left: 0,
                right: 0,
                containLabel: true
            },
            xAxis: [
                {
                    type: 'category',
                    axisLabel: {
                        color: '#4E568C',
                    },
                    axisLine: {
                        show: false
                    },
                    axisTick: {
                        show: false
                    },
                    data: []
                }
            ],
            yAxis: {
                type: 'value',
                axisLine: {
                    show: false
                },
                axisTick: {
                    show: false
                },
                splitLine: {
                    lineStyle: {
                        color: '#F2F3F5'
                    }
                },
            },
            color: ['#4080FF'],
            series: [
                {
                    data: [],
                    name: '在线数',
                    type: 'bar',
                    itemStyle: {
                        color: '#6CDFDF'
                    },
                    barWidth: '10%',
                    barGap: '10%'
                }
            ]
        })
        const getMapOnlineBar = async () => {
            const params = {
                areaPaths: props.areaPaths,
                modelIds: typeList[props.activedType] || [4, 5]
            }
            const res = await day(params)
            const { data, success }: { data: ManholeCoverOnline[], success: boolean } = res as unknown as HttpResponse<ManholeCoverOnline[]>
            if (success) {
                lineOption.value.xAxis![0].data = data.map(k => k.recordTime?.slice(5, 10));
                lineOption.value.series![0].data = data.map(k => {
                    return {
                        name: k.recordTime?.slice(5, 10),
                        value: k.online
                    }
                })
            }
        }
        // 5条告警列表
        const alarmList = ref<ManholeCoverAlarmList>({
            alarm: 0,
            alarmTotal: 0,
            alarmList: []
        })
        // 查询设备在离线告警
        const getMapAlarmsList = async () => {
            const params = {
                areaPaths: props.areaPaths,
                type: props.activedType || null,
                modelIds:["4","5","33"]
            }
            const res = await alarms(params)
            const { data, success }: { data: ManholeCoverAlarmList, success: boolean } = res as unknown as HttpResponse<ManholeCoverAlarmList>
            if (success) {
                alarmList.value = data
            }
        }
        const pieRef = ref()
        const lineRef = ref()
        const handleResize = () => {
            pieRef.value.handleResize()
            lineRef.value.handleResize()
        }
        watch(props, () => {
            getMapOnline()
            getMapOnlineBar()
            getMapAlarmsList()
        }, { immediate: true })
        return {
            pieOption,
            lineOption,
            // mapAlarmList,
            onlineTotal,
            alarmList,
            handleResize,
            pieRef,
            lineRef
        }
    }
})
</script>
<style lang="less" scoped>
/deep/.ivu-tooltip {
    height: 40px;

    .ivu-tooltip-rel {
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        line-height: 40px;
        width: 100%;
    }
}
</style>
