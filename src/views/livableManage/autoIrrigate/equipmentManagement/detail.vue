<script lang="ts" setup>
import { getIrrigateDeviceDetail } from "@/api/livableManage/autoIrrigate";
import SideDetail from "@/components/global/SideDetail/index.vue";
import DeviceMarkerMap from "@/components/common/deviceMarkerMap";
import { reactive, ref, markRaw, computed } from "vue";
import {enumeration} from '@/config/enumeration'
import { useRoute, useRouter } from "vue-router";
import { deleteItem, findValue, getValue } from "wei-util";
import Util from "@/utils/index";
import { GetQueryParams } from "@/hooks/getLocationPrototype";
import { useStore } from 'vuex';
const $store = useStore();
const { deviceCode, types } = new GetQueryParams();
const tabList = ref([
    {
        name: "部件信息",
        key: "unitInfo",
        icon: "1",
    },
    {
        name: "告警信息",
        key: "alarmInfo",
        icon: "1",
    },
    {
        name: "设备列表",
        key: "deviceList",
    },
]);
const tabList2 = ref([
    {
        name: "部件信息",
        key: "unitInfo",
        icon: "1",
    },
    {
        name: "控制记录",
        key: "controlRecord",
        icon: "1",
    },
]);
types == 3 && deleteItem(tabList2.value, "控制记录", "name");
const tableList = ref<any>([
    { title: "设备名称", key: "sbmc", tooltip: true },
    { title: "告警类型", slot: "alarmTypeName", width: 100, tooltip: true },
    { title: "告警等级", slot: "level", width: 100 },
    { title: "告警详情", key: "content", tooltip: true },
    { title: "是否推送", slot: "pushStatus", width: 100 },
    { title: "区域位置", key: "areaPath", tooltip: true, width: 200 },
    { title: "告警时间", key: "alarmTime", tooltip: true },
    { title: "操作", slot: "operation", maxWidth: 80, align: "center", fixed: true },
]);
const controlRecordList = ref<any>([
    { title: "控制源", slot: "controlSource" },
    { title: "控制类型", slot: "controlType" },
    { title: "控制动作", slot: "switchState" },
    { title: "控制人", key: "creatorName" },
    { title: "控制时间", key: "createTime" },
]);
const route = useRoute();
let detailInfo = ref<any>({
    objInfo: {},
    deivce: { status: 1 },
    deviceExtendInfo: {},
    devicePropertyStatusList: [],
    monitorPoint:{}
});
async function getData() {
    let res: any = await getIrrigateDeviceDetail(route.query.id);
    if (res.success) {
        detailInfo.value = res.data;
        debugger
    }
}
getData();
let alarmData = reactive<Object>({});
let isShowSide = ref<boolean>(false);
function goDetail(row: string) {
    alarmData = JSON.parse(row);
    isShowSide.value = true;
}
interface ControlParams {
    irrigateDeviceId: number;
    startTime?: string;
    endTime?: string;
}
const listCom = ref();
const controlRecord = ref();
const controlParams = ref<ControlParams | any>({});
function getList(index: number) {
    switch (index) {
        case 1:
            if (Number(route.query.types) === 1) {
                const params = {
                    deviceCode: route.query.deviceCode,
                };
                listCom.value.search(params);
            } else {
                controlParams.value.irrigateDeviceId = route.query.id;
                controlRecord.value.search(controlParams.value);
            }
            break;
        case 2:
            clickSearch();
            break;
    }
}
const time = Util.getDateRangeS(new Date(), "cd", "YYYY-MM-DD HH:mm:ss", false, 2);
// 控制记录搜索
const form = ref<Array<string>>([time[0], time[1]]);
function handelSearch(times: Array<string>) {
    console.log(times);
    controlParams.value.startTime = times[0];
    controlParams.value.endTime = times[1].replace("00:00:00", "23:59:59");
    form.value = [times[0], times[1].replace("00:00:00", "23:59:59")];
    getList(controlParams.value);
}

const router = useRouter();
function handleEdit() {
    router.push({
        name: "autoIrrigate:equipmentManagementEdit",
        query: {
            deviceCode: route.query.deviceCode,
            type: route.query.type,
            id: route.query.id,
        },
    });
}
function handlejwd(objInfo: any) {
    if (!objInfo.objX) return "";
    return objInfo.objX + " , " + objInfo.objY;
}
const deviceList = [
    { title: "设备编号", slot: "deviceId" },
    { title: "设备名称", slot: "sbmc" },
    { title: "设备型号", slot: "sbxh" },
    { title: "区域位置", slot: "areaPath" },
];
const deviceListCom = ref();
const deviceListSearchObj = ref<any>({
    parentDeviceCode: deviceCode,
    deviceUnit: {},
});
const clickSearch = () => {
    deviceListCom.value.search(deviceListSearchObj.value);
};
const clickReset = () => {
    deviceListSearchObj.value = {
        parentDeviceCode: deviceCode,
        deviceUnit: {},
    };
    clickSearch();
};
const handleValue = (item: any) => {
    const prop = getValue(item, "prop");
    const value = getValue(item, "value", "--");
    const properties = getValue(detailInfo.value, "physicalModel.properties", []);
    const type = findValue(properties, prop, "identifier", "dataType.type");
    const specs: any = findValue(properties, prop, "identifier", "dataType.specs");
    if (type === "enum") {
        return specs[value];
    } else {
        return value + getValue(specs, "unit");
    }
};
</script>
<template>
    <BreadcrumbCustom>
        <BreadcrumbItem>自动灌溉</BreadcrumbItem>
        <BreadcrumbItem to="/autoIrrigate/equipmentManagement">设备管理</BreadcrumbItem>
        <BreadcrumbItem>设备详情</BreadcrumbItem>
    </BreadcrumbCustom>
    <detailCard
        @on-edit="handleEdit"
        :isEditBtn="$Util.checkAuth('autoIrrigate:deviceManage:edit')"
        :src="require('@/assets/images/icon-基础信息.png')"
        :is-back-btn="true"
        @on-back="$router.back()"
        title="基础信息"
    >
        <Row>
            <Col span="8">
                <s-label
                    :bold="true"
                    class="code"
                    label="设备编号"
                    :value="detailInfo.deviceExtendInfo.deviceId"
                />
            </Col>
            <Col span="8">
                <s-label
                    :bold="true"
                    label="区域位置"
                    :value="detailInfo.deviceExtendInfo.areaPath"
                />
            </Col>
        </Row>
        <Row>
            <Col span="8">
                <s-label label="设备名称" :value="detailInfo.deviceExtendInfo.sbmc" />
            </Col>
            <Col span="8">
                <s-label label="设备型号" :value="detailInfo.deviceExtendInfo.sbxh" />
            </Col>
            <Col span="8">
                <s-label
                    label="设备类型"
                    :value="detailInfo.deviceExtendInfo.deviceSecondTypeName"
                />
            </Col>
        </Row>
        <Row>
            <Col span="8">
                <s-label label="设备标识码" :value="detailInfo.deviceExtendInfo.bsm" />
            </Col>
            <Col span="8">
                <s-label
                    label="设备状态"
                    :value="enumeration.deviceStateList[detailInfo.deviceExtendInfo.sbzt] || ''"
                />
            </Col>
            <Col span="8" v-if="types != 3">
                <s-label label="灌溉面积">
                    <template #value>{{ detailInfo.irrigateArea }}m²</template>
                </s-label>
            </Col>
        </Row>
    </detailCard>
    <detailCard :src="require('@/assets/images/icon-设备列表.png')" title="设备属性">
        <Row>
            <Col span="8">
                <s-label label="在线状态">
                    <template #value>
                        <onlineStatus :value="detailInfo?.device?.status"></onlineStatus>
                    </template>
                </s-label>
            </Col>
            <Col span="8" v-for="(item, index) in detailInfo.devicePropertyStatusList" :key="index">
                <s-label
                    :label="
                        findValue(
                            getValue(detailInfo, 'physicalModel.properties'),
                            getValue(item, 'prop'),
                            'identifier',
                            'name'
                        )
                    "
                >
                    <template #value>
                        <div>
                            {{ handleValue(item) }}
                        </div>
                        <!-- <online-status v-else :value="detailInfo.status"></online-status> -->
                    </template>
                </s-label>
            </Col>
            <Col span="8">
                <s-label label="更新时间" :value="detailInfo.modifyTime" />
            </Col>
        </Row>
    </detailCard>
    <detailCard>
        <s-tab v-if="Number($route.query.types) === 1" :tab-list="tabList" @handleChange="getList">
            <template #unitInfo>
                <Row>
                    <Col span="8">
                        <s-label label="点位标识码" :value="detailInfo.monitorPoint?.bsm || ''" />
                    </Col>
                    <Col span="8">
                        <s-label label="点位名称" :value="detailInfo.monitorPoint?.dwmc || ''" />
                    </Col>
                    <Col span="8">
                        <!-- <s-label
                            label="点位类型"
                            :value="$store.getters.dictionary.obj_category[detailInfo.monitorPoint?.dwlx || '']" 
                        /> -->
                        <s-label
                            label="点位类型"
                            :value="detailInfo.monitorPoint?.dwlx || ''" 
                        />
                    </Col>
                </Row>
                <Row>
                    <Col span="8">
                        <s-label label="主管部门" :value="detailInfo.objInfo?.deptName || ''" />
                    </Col>
                    <Col span="8">
                        <s-label
                            label="权属单位"
                            :value="detailInfo.objInfo?.ownerEnterpriseName || ''"
                        />
                    </Col>
                    <Col span="8">
                        <s-label
                            label="养护单位"
                            :value="detailInfo.objInfo?.opEnterpricseName || ''"
                        />
                    </Col>
                </Row>
                <Row>
                    <Col span="8">
                        <s-label
                            label="部件状态"
                            :value="enumeration.objState[detailInfo.objInfo?.objState]"
                        />
                    </Col>
                    <Col span="8">
                        <s-label label="联系人" :value="detailInfo.objInfo?.contactPerson || ''" />
                    </Col>
                    <Col span="8">
                        <s-label label="联系电话" :value="detailInfo.objInfo?.contactPhone || ''" />
                    </Col>
                </Row>
                <Row>
                    <Col span="8">
                        <s-label label="初始时间" :value="detailInfo.objInfo?.initDate || ''" />
                    </Col>
                    <Col span="8">
                        <s-label label="变更时间" :value="detailInfo.objInfo?.modifyDate || ''" />
                    </Col>
                    <Col span="8">
                        <s-label label="经纬度">
                            <template #value>
                                <jwd-map :obj-info="detailInfo.monitorPoint"></jwd-map>
                            </template>
                        </s-label>
                    </Col>
                </Row>
                <Row>
                    <Col span="8">
                        <s-label label="备注" :value="detailInfo.objInfo?.remark || ''" />
                    </Col>
                </Row>
            </template>
            <template #alarmInfo>
                <baseTable :columns="tableList" ref="listCom" url="/irrigateDeviceAlarm/list">
                    <!-- <template #alarmType="{ row }">
            <div>
              {{ enumeration.alarmType[row.alarmType] }}
              <dict-label code="auto_irrigate_alarm_type" :value="row.alarmType" />
            </div>
          </template> -->
                    <template #level="{ row }">
                        <div>
                            {{ enumeration.alarmGrade[row.level] }}
                        </div>
                    </template>
                    <template #pushStatus="{ row }">
                        <div>
                            {{ enumeration.isPush[row.pushStatus] }}
                        </div>
                    </template>
                    <template #operation="{ row }">
                        <LinkBtn size="small" @click="goDetail(JSON.stringify(row))">查看</LinkBtn>
                    </template>
                </baseTable>
            </template>
            <template #deviceList>
                <Form style="margin: 16px 0" :label-width="80">
                    <Row :gutter="80">
                        <Col span="8">
                            <FormItem label="编号名称">
                                <Input
                                    placeholder="请输入"
                                    v-model="deviceListSearchObj.paramKey"
                                    clearable
                                ></Input>
                            </FormItem>
                        </Col>
                        <Col span="8">
                            <FormItem label="设备型号">
                                <Input
                                    placeholder="请输入"
                                    clearable
                                    v-model="deviceListSearchObj.deviceUnit.name"
                                ></Input> </FormItem
                        ></Col>
                        <Col span="8">
                            <Button type="primary" @click="clickSearch">搜索</Button>
                            <Button @click="clickReset">重置</Button>
                        </Col>
                    </Row>
                </Form>
                <baseTable
                    ref="deviceListCom"
                    url="/irrigateDeviceRef/selectSensorDtoPage"
                    :columns="deviceList"
                >
                    <template #deviceId="{ row }">
                        <tooltipAutoShow
                            :content="getValue(row, 'deviceExtendInfo.deviceId')"
                        ></tooltipAutoShow>
                    </template>
                    <template #sbmc="{ row }">
                        <tooltipAutoShow
                            :content="getValue(row, 'deviceExtendInfo.sbmc')"
                        ></tooltipAutoShow>
                    </template>
                    <template #sbxh="{ row }">
                        <tooltipAutoShow
                            :content="getValue(row, 'deviceExtendInfo.sbxh')"
                        ></tooltipAutoShow>
                    </template>
                    <template #areaPath="{ row }">
                        <tooltipAutoShow
                            :content="getValue(row, 'deviceExtendInfo.areaPath')"
                        ></tooltipAutoShow>
                    </template>
                </baseTable>
            </template>
        </s-tab>
        <s-tab v-else :tab-list="tabList2" @handleChange="getList">
            <template #unitInfo>
                <Row>
                    <Col span="8">
                        <s-label label="部件标识码" :value="detailInfo.objInfo?.objId || ''" />
                    </Col>
                    <Col span="8">
                        <s-label label="部件名称" :value="detailInfo.objInfo?.objName || ''" />
                    </Col>
                    <Col span="8">
                        <s-label
                            label="部件类型"
                            :value="detailInfo.objInfo?.secondObjCategoryName || ''"
                        />
                    </Col>
                </Row>
                <Row>
                    <Col span="8">
                        <s-label label="主管部门" :value="detailInfo.objInfo?.deptName || ''" />
                    </Col>
                    <Col span="8">
                        <s-label
                            label="权属单位"
                            :value="detailInfo.objInfo?.ownerEnterpriseName || ''"
                        />
                    </Col>
                    <Col span="8">
                        <s-label
                            label="养护单位"
                            :value="detailInfo.objInfo?.opEnterpricseName || ''"
                        />
                    </Col>
                </Row>
                <Row>
                    <Col span="8">
                        <s-label
                            label="部件状态"
                            :value="enumeration.objState[detailInfo.objInfo?.objState]"
                        />
                    </Col>
                    <Col span="8">
                        <s-label label="联系人" :value="detailInfo.objInfo?.contactPerson || ''" />
                    </Col>
                    <Col span="8">
                        <s-label label="联系电话" :value="detailInfo.objInfo?.contactPhone || ''" />
                    </Col>
                </Row>
                <Row>
                    <Col span="8">
                        <s-label label="初始时间" :value="detailInfo.objInfo?.initDate || ''" />
                    </Col>
                    <Col span="8">
                        <s-label label="变更时间" :value="detailInfo.objInfo?.modifyDate || ''" />
                    </Col>
                    <Col span="8">
                        <s-label label="经纬度">
                            <template #value>
                                <jwd-map :obj-info="detailInfo.objInfo"></jwd-map>
                            </template>
                        </s-label>
                    </Col>
                </Row>
                <Row>
                    <Col span="8">
                        <s-label label="备注" :value="detailInfo.objInfo?.remark || ''" />
                    </Col>
                </Row>
            </template>
            <template #controlRecord>
                <Form inline :label-width="69">
                    <FormItem label="时间范围">
                        <DatePicker
                            :model-value="form"
                            type="daterange"
                            format="yyyy-MM-dd HH:mm:ss"
                            placeholder="请选择日期"
                            @on-change="handelSearch"
                            placement="bottom-end"
                            style="width: 320px"
                            clearable
                            :editable="false"
                        />
                    </FormItem>
                    <!-- <FormItem :label-width="0">
            <Button type="primary">导出</Button>
          </FormItem> -->
                </Form>
                <baseTable
                    :columns="controlRecordList"
                    ref="controlRecord"
                    url="/irrigateControlRecord/getPage"
                >
                    <template #controlSource="{ row }">
                        <div>
                            {{ enumeration.controlSource[row.controlSource - 1] }}
                        </div>
                    </template>
                    <template #controlType="{ row }">
                        <div>
                            {{ row.controlType === 1 ? "开关状态" : "" }}
                        </div>
                    </template>
                    <template #switchState="{ row }">
                        <div>
                            {{ enumeration.switchState[row.switchState] }}
                        </div>
                    </template>
                </baseTable>
            </template>
        </s-tab>
    </detailCard>
    <component :is="componentId" :makers="jwdList" @close-modal="closeModal"></component>
    <SideDetail
        @on-cancel="isShowSide = false"
        :model-id="4"
        :data="alarmData"
        :show="isShowSide"
        title="告警详情"
    ></SideDetail>
</template>

<style lang="less" scoped>
::v-deep .tab-title {
    margin: 0 0 15px 0;
    justify-content: flex-start;
    .tab-item {
        margin-right: 12px;
    }
}

/deep/.code {
    .value {
        font-weight: 600;
        font-size: 16px;
    }
}
</style>
