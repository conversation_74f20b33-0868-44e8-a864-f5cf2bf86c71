export interface searchObjType {
  szjd?: string;
  szsq?: string;
  szdywg?: string;
  deviceCode?: string;
  sbmc?: string;
  bsm?: string;
  status?: number;
  useStatus?: number;
  areaLocation?: string;
  type?: number;
  areaPaths?: Array<string>;
}
export interface DisableType {
  id: number;
  deviceCode: string;
  useStatus?: number;
  type: number;
}
export interface newAddType {
  objId: string;
  deviceId: string;
}
export interface DeviceBindInfo {deviceSecondTypeName:string,code:string}