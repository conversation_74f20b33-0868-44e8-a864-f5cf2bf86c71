<script lang="ts" setup>
import { commonService } from '@/api/commonService'
import TableContentCard from "@/components/global/TableContentCard/index.vue";
import sModal from "@/components/common/modal/index.vue";
import correlationCom from "./components/correlation.vue";
import { getAreaTree } from "@/api/livableManage/parkSoilService";
import { PromiseBody } from "@/api/common";
import { correlationSensor } from "@/api/livableManage/autoIrrigate";
import {
    newAddIrrigateDevice,
    deleteIrrigateDevice,
    editIrrigateDevice,
} from "@/api/livableManage/autoIrrigate";
import ControlModal from "../components/controlModal.vue";
import { treeData } from "@/api/manholeCoverService";
import {
    watch,
    getCurrentInstance,
    markRaw,
    reactive,
    nextTick,
    onMounted,
    ref,
    computed,
} from "vue";
import { searchObjType, DisableType, newAddType } from "./type";
import { useRouter, useRoute, onBeforeRouteLeave } from "vue-router";
import { deepClone, deleteItem, findValue } from "wei-util";
import { MessageSuccess } from "@/hooks/message";
const searchObj = reactive<any>({});
const listCom = ref<any>();
const typeMap = {
    5: 2,
    4: 1,
    33: 3,
};
// 路由守卫监听是否离开页面离开删除标记
onBeforeRouteLeave((to, from, next) => {
    if (to.path !== "/autoIrrigate/equipmentManagementDetail") {
        sessionStorage.removeItem("tabLabel");
    }
    next();
});

function handleSubmit() {
    let params: searchObjType = {};
    params = JSON.parse(JSON.stringify(searchObj));
    if (searchObj.areaLocation) {
        const arr: Array<string> = searchObj.areaLocation.split("/");
        params.szjd = arr[0];
        params.szsq = arr[1];
        params.szdywg = arr[2];
        delete params.areaLocation;
    }

    params.type = typeMap[modelId.value];
    listCom.value.search(params);
}
onMounted(() => {
    handleSubmit();
});
const tableList = ref<any[]>([
    { type: "selection", maxWidth: 40 },
    { title: "设备编号", key: "deviceCode", minWidth: 180 },
    { title: "设备名称", key: "sbmc", tooltip: true },
    { title: "在线状态", slot: "status", minWidth: 100 },
    { title: "使用状态", slot: "useStatus", tooltip: true, minWidth: 100 },
    { title: "设备标识码", key: "bsm", minWidth: 180 },
    { title: "权属单位", key: "ownerEnterpriseName", tooltip: true },
    { title: "区域位置", key: "areaPath", tooltip: true, minWidth: 250 },
    { title: "操作", slot: "action", width: 100, align: "left" },
]);
const router = useRouter();
function goDetail(row: any) {
    router.push({
        name: "autoIrrigate:equipmentManagementDetail",
        query: {
            deviceCode: row.deviceCode,
            id: row.id,
            types: typeMap[modelId.value],
        },
    });
}
// 启用停用editSoilDevice
async function handleDisable(row: any, label: string) {
    const params: DisableType = {
        id: row.id,
        deviceCode: row.deviceCode,
        type: typeMap[modelId.value],
    };
    if (label === "启用") {
        params.useStatus = 1;
    } else {
        params.useStatus = 0;
    }
    // let res: any = await editIrrigateDevice(params);
    let res: any = await commonService.updateUseStatus(params.deviceCode, params.useStatus)
    if (res.success) {
        that?.$Message.success(label + "成功");
        handleSubmit();
    }
}
// 批量删除
let ids: Array<Number> = [];
const that = getCurrentInstance()?.appContext.config.globalProperties;
function handleDelete() {
    if (!ids.length) return that?.$Message.warning("最少选择一条数据");
    that?.$Modal.confirm({
        title: "提示",
        content: "是否删除选中的设备（如果有关联设备，请先移除关联关系）？",
        onOk: async () => {
            let res: any = await deleteIrrigateDevice(ids);
            if (res.success) {
                that.$Message.success("操作成功");
                handleSubmit();
                ids = [];
            }
        },
    });
}
function selectionChange(selected: any) {
    ids = selected.map((i: any) => i.id);
}

async function addSubmit(data: Array<any>) {
    let deviceExtendInfoList: Array<newAddType> = data.map((i: any) => {
        return {
            objId: i.objId,
            deviceId: i.deviceId,
        };
    });
    const params: any = {
        deviceExtendInfoList,
    };
    params.queryType = typeMap[modelId.value];
    let res: any = await newAddIrrigateDevice(params);
    console.log(res);
    if (res.success) {
        that?.$Message.success("关联成功");
        handleSubmit();
    }
}
const tabList = ref([
    { name: "电磁阀控制器", auth: "autoIrrigate:deviceManage:solenoidControl" },
    { name: "电磁阀", auth: "autoIrrigate:deviceManage:solenoid" },
    { name: "传感器", auth: "" },
]);
let showSolenoidValve = ref<boolean>(false);
const baseForm = ref();
let modelId = ref<number>(5);
const label = ref<string>("");
label.value = sessionStorage.getItem("tabLabel") || "电磁阀控制器";
watch(
    label,
    (newVal, oldVal) => {
        if (newVal === "电磁阀") {
            modelId.value = 5;
            showSolenoidValve.value = true;
            tableList.value.splice(5, 0, { title: "开关状态", slot: "switchState", width: 100 });
            tableList.value.splice(tableList.value.length - 1, 1, {
                title: "操作",
                slot: "action",
                width: 140,
                align: "left",
            });
        } else if (newVal === "电磁阀控制器") {
            modelId.value = 4;
            tableList.value.splice(tableList.value.length - 1, 1, {
                title: "操作",
                slot: "action",
                width: 180,
                align: "left",
            });
        } else {
            modelId.value = 33;
            tableList.value.splice(6, 0, { title: "设备型号", slot: "deviceUnit" });
            findValue(tableList.value, "操作", "title").width = 100;
        }
    },
    { immediate: true }
);
const activeLabel = ref<string>(label.value);
function changeTab(name: string) {
    const hasItemForTitle = (title: string) => {
        return tableList.value.some((item) => item.title === title);
    };
    activeLabel.value = name;
    sessionStorage.setItem("tabLabel", name);
    nextTick(() => {
        deleteItem(tableList.value, "设备型号", "title");
        if (name === "电磁阀") {
            showSolenoidValve.value = true;
            if (hasItemForTitle("开关状态")) return;
            modelId.value = 5;
            tableList.value.splice(5, 0, { title: "开关状态", slot: "switchState", width: 100 });
            tableList.value.splice(tableList.value.length - 1, 1, {
                title: "操作",
                slot: "action",
                width: 140,
                align: "left",
            });
        } else if (name === "电磁阀控制器") {
            showSolenoidValve.value = false;
            deleteItem(tableList.value, "开关状态", "title");
            modelId.value = 4;
            tableList.value.splice(tableList.value.length - 1, 1, {
                title: "操作",
                slot: "action",
                width: 180,
                align: "left",
            });
        } else {
            //传感器
            modelId.value = 33;
            deleteItem(tableList.value, "开关状态", "title");
            tableList.value.splice(6, 0, { title: "设备型号", slot: "deviceUnit" });
            findValue(tableList.value, "操作", "title").width = 100;
        }
        handleSubmit();
        baseForm.value.checkFormWidth(window.innerWidth);
    });
}
const controlInfo = ref<any>();
const isShowControl = ref<boolean>(false);
function control(row: any) {
    controlInfo.value = row;
    isShowControl.value = true;
}
const showCorrelation = ref<boolean>(false);
const correlation = (row: any) => {
    showCorrelation.value = true;
    nextTick(() => {
        const { initData } = correlCom.value;
        initData(row, 33);
    });
};
const correlCom = ref();
const clickConfirm = async () => {
    const { formData } = correlCom.value;
    const { success }: PromiseBody = (await correlationSensor(formData)) as unknown as PromiseBody;
    if (success) {
        showCorrelation.value = false;
        MessageSuccess("关联设备成功！");
        handleSubmit();
    }
};
</script>
<template>
    <BreadcrumbCustom>
        <BreadcrumbItem>自动灌溉</BreadcrumbItem>
        <BreadcrumbItem>设备管理</BreadcrumbItem>
    </BreadcrumbCustom>
    <ContentCard :sessionLabel="label" @on-change="changeTab" :tabList="tabList" :showTab="true">
        <BaseForm ref="baseForm" @handle-submit="handleSubmit" :model="searchObj" inline :label-width="90">
            <template #formitem>
                <FormItem label="设备编号" prop="deviceCode">
                    <Input v-model="searchObj.deviceCode" :maxlength="20" placeholder="请输入" clearable></Input>
                </FormItem>
                <FormItem label="设备名称" prop="sbmc">
                    <Input v-model="searchObj.sbmc" :maxlength="20" placeholder="请输入" clearable></Input>
                </FormItem>
                <FormItem label="区域位置" prop="areaLocation">
                    <AreaSelectTree v-model="searchObj.areaLocation" table-name="livable_irrigate_device" />
                </FormItem>
                <FormItem v-if="activeLabel === '电磁阀'" label="开关状态" prop="witchState">
                    <Select :transfer="false" v-model="searchObj.switchState" clearable>
                        <Option v-for="(item, index) in $enumeration.switchState" :key="index" :value="index" clearable>
                            {{ item }}
                        </Option>
                    </Select>
                </FormItem>
                <FormItem label="设备标识码" prop="bsm">
                    <Input v-model="searchObj.bsm" :maxlength="20" placeholder="请输入" clearable></Input>
                </FormItem>
                <FormItem label="设备型号" v-if="activeLabel === '传感器'" prop="deviceUnitName">
                    <Input v-model="searchObj.deviceUnitName" placeholder="请输入" clearable></Input>
                </FormItem>
                <FormItem label="在线状态" prop="status">
                    <Select :transfer="false" v-model="searchObj.status" clearable>
                        <Option v-for="(item, index) in $enumeration.onlineStatus" :key="index" :value="index" clearable>
                            {{ item.title }}
                        </Option>
                    </Select>
                </FormItem>
                <FormItem label="使用状态" prop="useStatus">
                    <Select :transfer="false" v-model="searchObj.useStatus" clearable>
                        <Option v-for="(item, index) in $enumeration.useStatus" :key="index" :value="index" clearable>
                            {{ item }}
                        </Option>
                    </Select>
                </FormItem>
            </template>
        </BaseForm>
        <TableContentCard :baseBtn="false">
            <template #btn>
                <deviceSelect v-auth="'autoIrrigate:deviceManage:joinDevice'" :modelId="modelId" :multiple="true"
                    @on-change="addSubmit" />
                <Dropdown v-auth="['autoIrrigate:deviceManage:batchDel']" placement="bottom-start">
                    <Button custom-icon="iconfont icon-caret-down">批量操作</Button>
                    <template #list>
                        <DropdownMenu style="width: 111px">
                            <!-- <DropdownItem v-auth="'environmentMonitoring:GardenSoil:deviceManage:batchUpdate'"批量修改</DropdownItem> -->
                            <DropdownItem v-auth="'autoIrrigate:deviceManage:batchDel'" @click="handleDelete">批量删除
                            </DropdownItem>
                        </DropdownMenu>
                    </template>
                </Dropdown>
            </template>
            <baseTable @on-selection-change="selectionChange" :model="searchObj" ref="listCom"
                url="/irrigateDevice/selectDtoPage" :columns="tableList">
                <template #status="{ row }">
                    <auto-tag type="1" :value="row.status"></auto-tag>
                </template>
                <template #useStatus="{ row }">
                    <auto-tag type="2" :value="row.useStatus"></auto-tag>
                </template>
                <template #switchState="{ row }">
                    <switch-status :value="row.switchState"></switch-status>
                </template>
                <template #deviceUnit="{ row }">
                    <tooltipAutoShow :content="row.deviceUnitCode + `(${row.deviceUnitName})`"></tooltipAutoShow>
                </template>
                <template #action="{ row }">
                    <LinkBtn size="small" @click="goDetail(row)">详情</LinkBtn>
                    <LinkBtn size="small" v-auth="'autoIrrigate:\deviceManage:\control'" @click="control(row)"
                        v-show="activeLabel !== '传感器'">控制</LinkBtn>
                    <LinkBtn size="small" v-auth="'autoIrrigate:\deviceManage:\control'" v-show="activeLabel === '电磁阀控制器'"
                        @click="correlation(row)">关联</LinkBtn>
                    <LinkBtn size="small" v-auth="'autoIrrigate:\deviceManage\:startStop'" @click="
                        handleDisable(row, $enumeration.useStatus[row.useStatus === 0 ? 1 : 0])
                        ">{{ $enumeration.useStatus[row.useStatus === 0 ? 1 : 0] }}</LinkBtn>
                </template>
            </baseTable>
        </TableContentCard>
    </ContentCard>
    <ControlModal v-bind="{
        [activeLabel === '电磁阀控制器' ? 'controlText' : '']: { open: '全开', off: '全关' },
    }" :show="isShowControl" :url="activeLabel === '电磁阀控制器'
        ? '/irrigateDevice/batchOpenClose'
        : '/irrigateControlRecord/irrigateControl'
    " :nodeKey="activeLabel === '电磁阀控制器' ? 'duration' : 'controlDuration'"
        :nodeId="activeLabel === '电磁阀控制器' ? 'id' : 'refId'" :controlSource="1" :detail="controlInfo"
        @on-update="handleSubmit" @on-close="isShowControl = false" />
    <sModal @on-cancel="showCorrelation = false" @on-confirm="clickConfirm" :option="{ title: '关联' }"
        :is-show="showCorrelation">
        <correlationCom ref="correlCom"></correlationCom>
    </sModal>
</template>

<style lang="less" scoped></style>
