<script lang="ts" setup>
import { ref, defineEmits, watch } from "vue";
import { deepClone, getValue } from "wei-util";
const props = defineProps({
    defaultValue: {
        default: "",
    },
    dataList: {
        type: Array,
        default: () => [],
    },
});
const emit = defineEmits(["radio-change"]);
const radioChange = (val: any) => {
    let code: string = deepClone(getValue(val, 'device.code'))
    let deviceSecondTypeName: string = deepClone(getValue(val, 'deviceExtendInfo.deviceSecondTypeName'))
    filterDataList.value.forEach(item => {
        if (item.id === val.id) {
            if (val.selected) {
                code = ''
            }
            val.selected = !val.selected
        } else {
            if(!(item.otherJoin && item.selected)) item.selected = false
        }
    })
    emit("radio-change", {code,deviceSecondTypeName});
};
const filterDataList = ref<any[]>([]);
watch(
    () => props.dataList,
    (newVal,oldVal) => {
        if(!oldVal.length){
            filterDataList.value = deepClone(newVal);
        }
    },
    { deep: true }
);
const onSearchChange = (el: any) => {
    const { value } = el.currentTarget;
    filterDataList.value = props.dataList.filter((item: any) =>
        getValue(item, "title").includes(value)
    );
};


</script>
<template>
    <div>
        <Input placeholder="搜索" @on-blur="onSearchChange" @on-enter="onSearchChange">
        <template #append>
            <Button icon="ios-search"></Button>
        </template>
        </Input>
        <div class="list">
            <div class="radio-list" v-if="filterDataList.length">
                <Radio :disabled="item.otherJoin && item.selected"
                    :style="(item.otherJoin && item.selected) ? 'pointer-events:none;' : ''"
                    @click.native.prevent="radioChange(item)" v-for="item in filterDataList" :key="item.id"
                    :model-value="item.selected && !item.otherJoin" :label="getValue(item, 'device.code')">{{ getValue(item,
                        "title") }}</Radio>
            </div>
            <div class="no-data" v-else>
                暂无数据
            </div>
        </div>
    </div>
</template>

<style lang="less" scoped>
.list {
    margin-top: 12px;
    padding: 4px 16px;
    border: 1px solid rgb(224, 230, 241);
    max-height: 250px;
    overflow: hidden;
    overflow-y: auto;

    .radio-list {
        display: flex;
        flex-direction: column;
        row-gap: 8px;
    }

    .no-data {
        width: 100%;
        text-align: center;

    }
}</style>
