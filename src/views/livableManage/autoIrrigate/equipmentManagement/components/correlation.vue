<script lang="ts" setup>
import correlationList from "./correlationList.vue";
import { getSensorDtoList } from "@/api/livableManage/autoIrrigate";
import { PromiseBody } from "@/api/common";
import { ref, computed, defineExpose, defineEmits } from "vue";
import { findIndex, getValue } from "wei-util";
import { DeviceBindInfo } from "../type";
const form = ref<any>({
    deviceCodeList: []
});
const emit = defineEmits(['update:modalVale'])
const tabList = [
    {
        name: "水表",
        key: "waterMeter",
    },
    {
        name: "雨量传感器",
        key: "rainSensor",
    },
];

const initData = async (row: any, modelId: number) => {
    form.value = row;
    getAllList()
};
const allListData = ref<any[]>([])
const getAllList = async () => {
    const params = {
        deviceUnit: {
        },
        parentDeviceCode: form.value.deviceCode, //电磁阀控制器设备编码,必传
        paramKey: "", //关键字
    };
    const { success, data }: PromiseBody = (await getSensorDtoList(
        params
    )) as unknown as PromiseBody;
    if (success) {
        allListData.value = data;
        const bindCode: DeviceBindInfo[] = data.map((item: any) => {
            if (item.selected && !item.otherJoin) {
                return {
                    code: getValue(item, 'device.code'),
                    deviceSecondTypeName: getValue(item, 'deviceExtendInfo.deviceSecondTypeName'),
                }
            }
        }).filter(Boolean)
        form.value.deviceCodeList = bindCode
    }
}

const radioChange = (val: DeviceBindInfo, origin: string) => {
    const index: number = findIndex(form.value.deviceCodeList, val.deviceSecondTypeName, 'deviceSecondTypeName')
    if (index !== -1) {
        form.value.deviceCodeList.splice(index, 1)
        form.value.deviceCodeList.push(val)
    } else {
        form.value.deviceCodeList.push(val)
    }

};
const filterList = computed(() => (label: string) => {
    return allListData.value.map(item => {
        if (getValue(item, 'deviceExtendInfo.deviceSecondTypeName') === label) {
            return {
                ...item,
                title: getValue(item, "device.name") + `(${getValue(item, "device.code")})`
            }
        }
    }).filter(Boolean)
})
defineExpose({
    formData: computed(() => {
        return {
            deviceCodeList: form.value.deviceCodeList.map((item: DeviceBindInfo) => item.code).filter(Boolean),
            parentDeviceCode: form.value.deviceCode
        }
    }),
    initData,
});
</script>
<template>
    <s-tab :tab-list="tabList" justify="start">
        <template #waterMeter>
            <correlationList @radio-change="(val: DeviceBindInfo) => radioChange(val, 'waterMeter')"
                :dataList="filterList('冷水表')" />
        </template>
        <template #rainSensor>
            <correlationList @radio-change="(val: DeviceBindInfo) => radioChange(val, 'rainSensor')"
                :dataList="filterList('雨量传感器')" />
        </template>
    </s-tab>
</template>

<style lang="less" scoped></style>
