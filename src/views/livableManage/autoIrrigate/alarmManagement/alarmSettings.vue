<!-- eslint-disable no-undef -->
<template>
    <div>
        <Button type="primary" @click="configuration" v-auth="'autoIrrigate:alarmManagement:set'">告警设置</Button>
        <sModal
            :isShow="showModal"
            @on-confirm="handelConfirm"
            @on-cancel="closeModal"
            :option="{
                title: '告警设置',
                width: 800,
                transfer: true,
            }"
        >
            <Title level="6">告警阀值设定</Title>
            <Form ref="ruleR" :model="searchObj" label-opsition="left">
                <!-- <div v-for="(item, index) in searchObj.configList" :key="index"> -->
                <div v-for="(tri, tindex) in searchObj.irrigateAlarmRules" class="rule-main">
                    <s-label label="触发条件">
                        <template #value>
                            <!--如果是第一条，则不显示移除按钮-->
                            <LinkBtn v-if="tindex!= 0" @click="delTrigger(tindex)">移除</LinkBtn>
                            <LinkBtn
                                @click="addTrigger(tindex)"
                                v-if="tindex == searchObj.irrigateAlarmRules.length - 1"
                                >新增</LinkBtn
                            >
                            <LinkBtn @click="trigger(tri.id)">触发</LinkBtn>
                        </template>
                    </s-label>
                    <Row>
                        <Col span="8">
                            <FormItem
                                label="告警事件类型"
                                :prop="`irrigateAlarmRules.${tindex}.eventCode`"
                            >
                                    <Select v-model="tri.eventCode" @on-change="changeEventCode">
                                        <Option v-for="item in assList" :value="item.code" :key="item.code">{{ item.name }}</Option>
                                    </Select>
                                    <!-- {{ tri.eventCode }} -->
                            </FormItem>
                        </Col>
                        <!-- < v-if="tri.eventCode == '090102'"> -->
                            <Col span="24" v-if="tri.eventCode == '090102'">
                                <FormItem
                                    label="时间范围"
                                    :prop="`irrigateAlarmRules.${tindex}.timeRange`"
                                    class="time-type"
                                >
                                    <RadioGroup v-model="tri.timeRange">
                                        <Radio :label="1">每天</Radio>
                                        <Radio :label="2">每周</Radio>
                                        <Radio :label="3">每月</Radio>
                                    </RadioGroup>
                                </FormItem>
                            </Col>
                            <!-- <Col span="24">
                                <FormItem
                                    :prop="`irrigateAlarmRules.${tindex}.timeRange`"
                                    class="time-values"
                                    v-show="tri.timeRange != 1"
                                >
                                    <CheckboxGroup v-model="tri.timeRange">
                                        <div v-if="tri.timeRange == 2">
                                            <Checkbox
                                                :label="`${item}`"
                                                v-for="(item, index) in 7"
                                                :key="index"
                                            >
                                                {{ weekList[item] }}
                                            </Checkbox>
                                        </div>
                                        <div v-if="tri.timeRange == 3" class="time-month">
                                            <Checkbox
                                                :label="`${item}`"
                                                v-for="(item, index) in 31"
                                                :key="index"
                                                >{{ `${item}` }}</Checkbox
                                            >
                                        </div>
                                    </CheckboxGroup>
                                </FormItem>
                            </Col> -->
                            <Col span="17" v-if="tri.eventCode == '090102'">
                                <FormItem
                                    label="正常浇水范围"
                                    :prop="`irrigateAlarmRules.${tindex}.min`"
                                >
                                <div class="range-input">
                                    <Input v-model="tri.min" placeholder="请输入"
                                        clearable
                                    ></Input>
                                    <span>-</span>
                                    <Input v-model="tri.max" placeholder="请输入"
                                        clearable
                                    ></Input>
                                    <span>吨</span>
                                </div>
                                </FormItem>
                            </Col>
                        <!-- </div> -->
                        <Col span="24">
                            <FormItem
                                label="警告等级"
                                :prop="`irrigateAlarmRules.${tindex}.alarmLevel`"
                            >
                            <Select v-model="tri.alarmLevel" clearable>
                                <Option v-for="(item, index) in $enumeration.alarmLevel" :value="item.value" :key="index">{{ item.name }}</Option>
                            </Select>
                                <!-- <dictDropDownSelect
                                    v-model="tri.streetlightType"
                                    type="1"
                                    code="traffic_streetlight.streetlight_type_"
                                    placeholder="请选择"
                                    clearable
                                /> -->
                            </FormItem>
                        </Col>
                        <Col>
                            <FormItem
                                label="设备范围"
                                :prop="`irrigateAlarmRules.${tindex}.deviceScope`"
                                :rules="[{ required: true, message: '请选择设备范围' }]"
                            >
                                <RadioGroup v-model="tri.deviceScope" vertical>
                                    <Radio :label="0">
                                        <span>全部</span>
                                    </Radio>
                                    <Radio :label="1">
                                        <span>自定义设备</span>
                                    </Radio>
                                </RadioGroup>
                            </FormItem>
                        </Col>
                        <Col style="padding-top: 60px; margin-left: 8px">
                            <Button
                                type="primary"
                                :disabled="tri.deviceScope != 1"
                                @click="showDeviceSelModal(tri, tindex)"
                                >选择设备</Button
                            >
                        </Col>
                    </Row>
                </div>
                <Title level="6">规则设定</Title>
                <Row>
                    <Col span="24">
                        <FormItem label="沉默周期" prop="stopCycle">
                            <div style="display: flex">
                                <input-number v-model="searchObj.stopCycle" :min="0" />
                                <span style="margin-left: 8px">小时</span>
                                <span style="margin-left: 8px; color: #999"
                                    >说明：产生告警后，多长时间内不再重复告警，如为0则每次异常上报都产生告警</span
                                >
                            </div>
                        </FormItem>
                    </Col>
                    <Col span="24">
                        <FormItem label="连续触发阀值" prop="threshold">
                            <div style="display: flex">
                                <input-number v-model="searchObj.threshold" :min="1" />
                                <span style="margin-left: 8px; color: #999"
                                    >说明：连续触发告警一定次数之后，才产生告警，默认值为1</span
                                >
                            </div>
                        </FormItem>
                    </Col>
                </Row>
            </Form>
            <ConfigCom ref="configCom" />
        </sModal>
        <alarmtable ref="selDevice" @on-change="handleDeviceCodes" />
    </div>
</template>
<script>
import sModal from '@/components/common/modal/index.vue';
import alarmtable from './components/alarmtable.vue';
import { commonService } from '@/api/commonService'
import { enumeration } from '@/config/enumeration'
// import { ref } from 'vue'
// import { MessageSuccess } from '@/hooks/message'
// import { rules } from '../../smartSeat/seatManagement/data';
// import { Vue } from 'vue-class-component';
const defaultTriggerObj = {
    deviceScope: 0, // 设备范围 0全部设备 1自定义设备
    timeType: 1, // 时间类型 1每天 2每周 3每月
    max: '', // 正常浇水范围
    min: '', // 正常浇水范围
    alarmLevel: '', // 警告等级
    // deviceList: [] // 设备列表
    irrigateAlarmDeviceList: [],
};
export default {
    name: 'AlarmSettings',
    components: {
        sModal,
        alarmtable,
        enumeration
    },
    props: {
        // modelCode: { default: "" },
    },
    data() {
        return {
            m: {
                moduleList: [],
                assAllList: []
            },
            componentName: '',
            searchObj: {
                // 列表不能为空，要有默认值
                irrigateAlarmRules: [
                    { name: '触发条件1',
                        eventCode: '',
                        modelCode: '0901',
                        stopCycle: '',
                        deviceScope: 0,
                        // timeType: 1,
                        // timeRange: [],
                        max: '',
                        min: '',
                        alarmLevel: '',
                        irrigateAlarmDeviceList: [],
                    },
                ],
                stopCycle: 0, // 沉默周期
                threshold: 1, // 连续触发阀值
            },
            // weekList: ["", "周一", "周二", "周三", "周四", "周五", "周六", "周日"],
            // 需要声明
            showModal: false
        };
    },
    computed: {
        assList() {
            if (this.searchObj.modelCode) {
                return this.m.assAllList.filter(item => {
                    return item.parentCode == this.searchObj.modelCode
                })
            }
            return this.m.assAllList
        }
    },
    watch: {
        modelCode(val) {
            this.getDeviceUnitList(val);
        },
    },
    mounted() {
        this.getModuleData()
        this.getAssType()
        // this.handleDeviceCodes()
    },
    created() {
        this.getDetail()
    },
    methods: {
        changeModule(val) {
            this.$emit('changeModel', this.searchObj.modelCode)
            this.searchObj.eventCode = ''
        },
        changeEventCode(val) {
            if (val) {
                let obj = this.m.assAllList.find(item => item.code == val)
                if (obj) {
                    this.searchObj.modelCode = obj.parentCode
                }
            }
        },
        // 业务模块，去掉080001
        getModuleData() {
            commonService.getAlarmEventList({ type: 2, parentCode: '0901' }).then(res => {
                if (res.success) {
                    this.m.moduleList = (res.data || []).map(item => {
                        item.moduleName = item.fullPathName.split('/')[0]
                        return item
                    })
                }
            })
        },
        // 关联事件类型,去掉080001
        getAssType() {
            commonService.getAlarmEventList({ type: 2, parentCode: '0901', }).then(res => {
                if (res.success) {
                    this.m.assAllList = (res.data || []).filter(item => item.code != '080001').map(item => {
                        item.moduleName = item.fullPathName.split('/')[0]
                        return item
                    })
                }
            })
        },
        resetRule() {
            this.searchObj.irrigateAlarmRules.forEach((item, index) => {
                item.deviceUnitId = ''
            })
        },
        // 清除
        // clear() {
        //     this.$refs.ruleR.resetFields()
        // },
        // 新增触发条件
        addTrigger(index) {
            this.searchObj.irrigateAlarmRules.push(this.$Util.objClone(defaultTriggerObj));
        },
        // 移除触发条件
        delTrigger(index) {
            this.searchObj.irrigateAlarmRules.splice(index, 1);
        },
        // 触发条件
        trigger(ruleId) {
            console.log(ruleId)
            this.$request(`/irrigateAlarmConfig/trigger/${ruleId}`).then(res => {
                if (res.success) {
                    this.$Message.success('触发成功');
                } else {
                    this.$Message.error(res.msg);
                }
            })
        },
        // 选择设备型号
        // changeDeviceUnitId(deviceUnitId, index) {
        //     this.$refs['expression'][index].init(deviceUnitId)
        // },
        getDeviceUnitList(modelCode) {
            if (!modelCode) {
                this.m.deviceUnitList = []
                return
            }
            commonService.getDeviceUnitList({ model: modelCode }).then(res => {
                if (res.success) {
                    this.m.deviceUnitList = res.data || []
                    let devEl = this.$refs.deviceUnitR
                    for (let k in devEl) {
                        devEl[k].lazyUpdateValue()
                    }
                }
            })
        },
        // 把接收到的deviceCodes放到searchObj里面的irrigateAlarmDeviceList里
        handleDeviceCodes(data) {
            const irrigateAlarmDeviceList = data.map(it => ({ deviceCode: it }))
            this.searchObj.irrigateAlarmRules.forEach((item, index) => {
                item.irrigateAlarmDeviceList = irrigateAlarmDeviceList
            })
            console.log(this.searchObj.irrigateAlarmRules)
        },
        // 选择设备
        showDeviceSelModal(item, index) {
            this.componentName = 'alarmtable';
            this.$nextTick(() => {
                this.$refs.selDevice.init(item, index);
            });
        },
        configuration() {
            console.log(11)
            this.showModal = true;
        },
        // 取消,关闭弹窗,清空表单
        closeModal() {
            this.showModal = false;
            // this.$nextTick(() => {
            //     // this.$router.go(0);
            //     // location.reload()
            //     // this.$refs.ruleR.resetFields()
            // });
            // 请求详情接口，获取提交数据
            this.getDetail()
        },
        // 确定提交，接口/irrigateAlarmConfig
        handelConfirm() {
            let param = {
                // 因为只有编辑的时候才会有id，所以这里注释掉
                // id: '5',
                irrigateAlarmRules: this.searchObj.irrigateAlarmRules,
                stopCycle: this.searchObj.stopCycle,
                threshold: this.searchObj.threshold,
            }
            let met = 'post'
            if (this.searchObj && this.searchObj.id) {
                // 编辑，则请求put接口，带上id
                met = 'put'
                param.id = this.searchObj.id
                console.log(param.id)
            }
            this.$request('/irrigateAlarmConfig', param, met).then(res => {
                if (res.success) {
                    this.$Message.success('提交成功');
                    this.showModal = false;
                } else {
                    this.$Message.error(res.msg);
                }
                this.getDetail()
            })
        },
        // 获取详情
        getDetail() {
            this.$request('/irrigateAlarmConfig', {}, 'get').then(res => {
                if (res.success) {
                    this.searchObj = res.data || {}
                    console.log(this.searchObj)
                    // 如果irrigateAlarmRules为空，则初始化一个
                    if (!this.searchObj.irrigateAlarmRules) {
                        this.searchObj.irrigateAlarmRules = [
                            { name: '触发条件1', eventCode: '', modelCode: '0901', stopCycle: '', deviceScope: 0, max: '', min: '', alarmLevel: '', irrigateAlarmDeviceList: [] },
                        ]
                    } else {
                        this.searchObj.irrigateAlarmRules.forEach((item, index) => {
                        item.irrigateAlarmDeviceList = item.deviceList?.map(it => ({ deviceCode: it }))
                    })
                    }
                    // this.irrigateAlarmRules = this.searchObj.irrigateAlarmRules
                    this.stopCycle = res.data.stopCycle
                    this.threshold = res.data.threshold
                } else {
                    this.$Message.error(res.msg);
                }
            })

        }
    },
};

</script>
<style lang="less" scoped>
.rule-main {
    margin-bottom: 20px;
}
.range-input{
    display: flex;
}
</style>
