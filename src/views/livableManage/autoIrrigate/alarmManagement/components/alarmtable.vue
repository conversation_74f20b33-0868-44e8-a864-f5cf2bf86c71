<template>
    <Modal v-model="showFlag" :title="title" :width="80" class-name="fill-page-modal" transfer @on-visible-change="resetStatus"
        :mask-closable="false" :footer-hide="!multiple">
        <chooseDeviceBox v-if="multiple">
            <div class="placeholder" v-show="m.selDevice.length === 0">请在下方选择设备</div>
            <Tooltip v-for="(item, index) in m.selDevice" :max-width="300">
                <Tag color="blue" closable @on-close="chooseMore(item, index)">{{ item.deviceCode }}</Tag>
                <template #content>
                    <p>设备名称: {{ item.sbmc }}</p>
                    <!-- <p>设备型号: {{ item.sbxh }}</p> -->
                    <p>设备编号: {{ item.deviceCode }}</p>
                    <!-- <p>区域位置: {{ item.areaPath }}</p> -->
                </template>
            </Tooltip>
        </chooseDeviceBox>

        <Form :model="searchObj" :label-width="90" @submit.native.prevent>
            <Row>
                <Col span="8">
                <FormItem label="名称编号" prop="deviceCode">
                    <Input v-model="searchObj.deviceCode" @on-change="() => searchInput()"
                        @on-enter="searchInput(100)" @on-clear="() => searchInput(100)" suffix="ios-search" clearable
                        placeholder="请输入名称或设备编号"></Input>
                </FormItem>
                </Col>
                <Col span="8">
                <FormItem label="设备状态" prop="status">
                    <Select v-model="searchObj.status" @on-change="search" clearable placeholder="请选择">
                        <Option v-for="(item, index) in $enumeration.deviceStateList" :value="index" :key="index">
                            {{ item }}
                        </Option>
                    </Select>
                </FormItem>
                </Col>
            </Row>
        </Form>
        <base-table ref="deviceTb" :selected-id="selectedId" :columns="columns" url="/irrigateDevice/selectDtoPage"
            @on-selection-change="selectionChange" :load-done="loadDone" size="small" :page-size="-1">
            <template #status="{ row }">
                <span>{{ $enumeration.deviceStateList[row.status] }}</span>
            </template>
            <template #action="{ row }">
                <link-btn size="small" @click="chooseOne(row)" :disabled="row._disabled">选择</link-btn>
            </template>
        </base-table>
        <template #footer>
            <div class="btn-box">
                <Button @click="handleCancel">取消</Button>
                <Button type="primary" @click="confirm" :loading="m.loading">确定</Button>
            </div>
        </template>
    </Modal>
</template>

<script>
import { commonService } from '@/api/commonService'
import modalMix from './modal-mix'
export default {
    name: 'DeviceModal',
    mixins: [modalMix],
    props: {
        title: { default: '请选择设备' },
        url: { default: '' },
    },
    emits: ['on-change', 'on-cancel'],
    data() {
        let columns = []
        if (this.multiple) {
            columns.push({ type: 'selection', width: 40, align: 'center' })
        }
        columns = columns.concat([
            { title: '设备编号', key: 'deviceCode', tooltip: true },
            { title: '设备名称', key: 'sbmc', tooltip: true },
            { title: '设备状态', slot: 'status', width: 80, tooltip: true },
            { title: '操作', slot: 'action', width: 80 }
        ])
        return {
            columns,
            searchObj: {
                deviceCode: '',
                status: '',
                deviceUnitCode: '',
                modelId: this.modelId
            },
            keyName: 'id',
            deviceCodes: [],

        }
    },

    mounted() {
    },
    computed: {
 selectedId() {
            return this.m.selDevice.map(item => item.id)
        }
    },
    methods: {
        selectionChange(rows) {
            console.log('rows---->',rows);
            this.m.selDevice = rows
        },
        chooseMore(item, index) {
            if(this.m.selDevice.length <= 1){
                this.$message.error('至少选择一条数据')
                return
            }
            this.m.selDevice.splice(index, 1)
        },
        resetStatus() {
            this.searchObj = {
                deviceCode: '',
                status: '',
                deviceUnitCode: '',
                modelId: this.modelId
            }
        },
        getData() {
            this.getDeviceUnitList()
            this.search()
        },
        // 设备型号
        getDeviceUnitList() {
            commonService.getConfigDeviceUnit({ modelId: this.modelId }).then(res => {
                if (res.success) {
                    this.m.deviceUnitList = res.data || []
                }
            })
        },
        handleCancel() {
            this.showFlag = false
            this.$emit('on-cancel')
        },
        // confirm() {
        //     this.$request('/irrigateAlarmConfig').then(res => {
        //         if (res.success) {
        //             // this.m.loading = false
        //             this.showFlag = false
        //             // this.$emit('on-change', this.m.selDevice)
        //         }
        //     })
        // },
        confirm() {
            this.m.selDevice.forEach(item => {
                item.deviceCode = item.deviceCode.trim()
            })
            this.showFlag = false 
            let deviceCodes = this.m.selDevice.map(item => item.deviceCode)
            // this.$emit('on-change', this.m.selDevice)
            this.$emit('on-change', deviceCodes)
            console.log(this.m.selDevice)
            console.log(deviceCodes)
        },
    }
}
</script>

<style lang="less" scoped>

</style>
