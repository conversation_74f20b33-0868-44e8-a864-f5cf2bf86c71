<template>
    <div>
        <BreadcrumbCustom>
            <BreadcrumbItem>自动灌溉</BreadcrumbItem>
            <BreadcrumbItem>分组管理</BreadcrumbItem>
        </BreadcrumbCustom>
        <ContentCard title="分组管理">
            <BaseForm :model="searchObj" :label-width="90" @handle-submit="search">
                <template #formitem>
                    <FormItem label="分组名称" prop="groupName">
                        <Input v-model="searchObj.groupName" clearable placeholder="请输入"></Input>
                    </FormItem>
                </template>
            </BaseForm>

            <btn-card>
                <Button type="primary" @click="add" v-auth="'autoIrrigate:groupManage:add'" icon="md-add">
                    新增
                </Button>
                <Button icon="ios-trash" @click="delMore" v-auth="'autoIrrigate:groupManage:batchDel'">
                    删除
                </Button>
            </btn-card>

            <base-table ref="mainTb" :columns="columns" url="/irrigateGroup/selectDtoPage"
                        @on-selection-change="selectionChange"
            >
                <template #action="{ row }">
                    <link-btn size="small" @click="toDetail(row)">
                        查看
                    </link-btn>
                    <link-btn size="small" @click="control(row)" v-auth="'autoIrrigate:groupManage:control'">
                        控制
                    </link-btn>
                </template>
            </base-table>
        </ContentCard>
        <controlModal :show="m.showControlFlag" :detail="m.curControlObj" @on-update="search" @on-close="m.showControlFlag = false" />

    </div>
</template>

<script>
import controlModal from '../components/controlModal'
export default {
    components: {
        controlModal
    },
    data() {
        return {
            columns: [
                { type: 'selection', width: 40, align: 'center' },
                { title: '分组名称', key: 'groupName', tooltip: true },
                { title: '备注', key: 'remark', tooltip: true },
                { title: '设备数量', key: 'deviceNum', tooltip: true },
                { title: '创建人', key: 'creatorName', tooltip: true },
                { title: '创建时间', key: 'createTime', width: 160, tooltip: true },
                { title: '更新时间', key: 'modifyTime', width: 160, tooltip: true },
                { title: '操作', slot: 'action', width: 100 }
            ],
            searchObj: {
                groupName: ''
            },
            m: {
                selectList: [], // 表格已选数据
                showControlFlag: false,
                curControlObj: {}
            }
        }
    },
    mounted() {
        this.search()
    },
    methods: {
        search() {
            this.$refs.mainTb.search(this.searchObj)
        },
        control(row) {
            this.m.showControlFlag = true
            this.m.curControlObj = row
        },
        add() {
            this.$router.push({
                path: '/autoIrrigate/groupManagementAdd'
            })
        },
        toDetail(row) {
            this.$router.push({
                path: '/autoIrrigate/groupManagementDetail',
                query: {
                    id: row.id
                }
            })
        },
        delMore() {
            if (this.m.selectList.length === 0) {
                this.$Message.warning('最少选择一条数据')
                return
            }
            this.$Modal.confirm({
                title: '提示',
                content: '您确定要删除选中的数据吗',
                onOk: () => {
                    let arr = []
                    this.m.selectList.forEach(item => {
                        arr.push(item.id)
                    })
                    this.delAjax(arr)
                }
            })
        },
        delAjax(arr) {
            this.$request('/irrigateGroup?idList=' + arr.join(','), {}, 'delete').then(res => {
                if (res.success) {
                    this.$Message.success('删除成功')
                    this.m.selectList = []
                    this.search()
                }
            })
        },
        selectionChange(list) {
            this.m.selectList = list || []
        }
    }
}
</script>

<style lang="less" scoped>

</style>
