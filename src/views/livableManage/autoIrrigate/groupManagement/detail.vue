<template>
    <div>
        <BreadcrumbCustom>
            <BreadcrumbItem >自动灌溉</BreadcrumbItem>
            <BreadcrumbItem to="/autoIrrigate/groupManagement">分组管理</BreadcrumbItem>
            <BreadcrumbItem>详情</BreadcrumbItem>
        </BreadcrumbCustom>
        <Form ref="addForm" :model="subObj" :rules="rules" label-position="top">
            <detailCard
                ref="detail"
                title="分组信息"
                :src="require('@/assets/images/icon_detail.png')"
                :isBackBtn="true"
                :isEditBtn="$Util.checkAuth('autoIrrigate:groupManage:edit')"
                :loading="m.loading"
                :class="[isEdit ? 'edit-card' : '' ]"
                @on-submit="submitFun" @on-edit="editChange" @on-back="backPage">
                <infoShow v-if="!isEdit" :subObj="m.device" />
                <editBox v-if="isEdit" ref="edit" :id="this.detailId" />
            </detailCard>
            <detailCard  v-show="!isEdit">
                <s-tab :tab-list="tabList" :default-active="defaultActive"
                       @handleChange="(i) => defaultActive = i">
                    <template #device>
                        <!-- <base-table
                            ref="deviceTb"
                            :columns="d.columns"
                            url="/irrigateGroup/irrigateGroupDeviceList"
                            :loadDone="loadDone"
                        >
                        </base-table> -->
                        <collection-map-box :data="m.mapPointList" :tree-list="m.treeList" />
                    </template>
                </s-tab>
            </detailCard>
        </Form>
    </div>
</template>

<script>
import editBox from "./components/editBox";
import infoShow from "./components/infoShow";
import collectionMapBox from "./components/collectionMap.vue";
import { DetailMix } from "./components/edit-mixin";
export default {
    name: 'detail',
    components: {
        editBox,
        infoShow,
        collectionMapBox
    },
    mixins: [DetailMix],
    data() {
        return {
            isEdit: false, // 是否编辑
            detailId: '',
            defaultActive: 0,
            tabList: [
                {
                    name: '设备列表',
                    key: 'device',
                    icon: '1'
                }
            ],
            d: {
                columns: [
                    { title: '设备编号', key: 'deviceCode', tooltip: true },
                    { title: '设备名称', key: 'deviceName', tooltip: true },
                    { title: '区域位置', key: 'areaPath', tooltip: true }
                ],
                searchObj: {
                    id: ''
                }
            }
        }
    },
    created() {
        if (this.$route.query.id) {
            this.detailId = this.$route.query.id
            this.d.searchObj.id = this.detailId
            this.getDetailById(this.$route.query.id)
            this.getMapPointListById(this.$route.query.id)
        }
    },
    mounted() {
        // this.getDevice()
    },
    methods: {
        loadDone(data, list) {
            this.m.device.deviceNum = data.total
            list.forEach(item => {
                item.deviceCode = item.device.code
                item.deviceName = item.deviceExtendInfo.sbmc
                item.areaPath = item.deviceExtendInfo.areaPath
            })
            return list
        },
        submitFun() {
            this.submitAjax('put')
        },
        getDevice() {
            this.$refs.deviceTb.search(this.d.searchObj)
        },
        editChange(flag) {
            this.isEdit = flag
            if (this.isEdit) {
                this.init()
            }
        }
    }
}
</script>

<style lang="less" scoped>
/deep/ .tab-title {
    justify-content: flex-start;
    padding-bottom: 16px;
    height: 48px;
    column-gap: 20px;


}
</style>
