<template>
    <div class="modal-map">
        <div class="device-cont plate-bg">
            <div class="device-cont-search">
              <Input suffix="ios-search" placeholder="请输入搜索内容" clearable v-model="searchText" />
              <span class="number">已选择电磁阀（{{ garbagePoint.length }}）</span>
            </div>
            <!-- <map-device-list :deviceList="garbagePoint" :searchText="searchText" lableKey="name" valueKey="code"
                :activedObjId="activedObjId" @actived-obj="handleActivedObj" :show-tag="[]" /> -->
            <scorll-box :scorll-id="activedObjId" key="code">
                <template #cont>
                    <Tree :data="filterEquipmentTree" :show-checkbox="false" :multiple="false" :render="renderContent" @on-select-change="handleTreeSelect"></Tree>
                </template>
            </scorll-box>
        </div>
        <div id="garbage-map-modal" class="garbage-map-box"></div>
    </div>
</template>
<script lang="ts">
import { defineComponent, ref, onMounted, nextTick, watch, resolveComponent, computed } from 'vue';
import AMapLoader from '@amap/amap-jsapi-loader';
import { getMarkerContent } from '@/components/common/mapAreaTreeSelect/markercontent'
import mapDeviceList from '@/components/common/mapDeviceList'
import scorllBox from '@/components/common/scorllBox/index';
import { areaTree } from '@/api/manholeCoverService';
import { searchTree, filterTree } from '@/utils/tool';
import { dfs } from 'wei-util'
export default defineComponent({
    components: {
        mapDeviceList,
        scorllBox
    },
    props: {
        treeList: {
            type: Array,
            default: () => []
        },
        garbagePoint: {
            type: Array,
            default: () => []
        }
    },
    setup(props) {
        // *********************
        // 地图
        // *********************
        // 初始化图标
        const map = ref<any>(null);
        const Amap = ref<any>(null);
        const ininMap = () => {
            AMapLoader.load({
                key: 'ea53a5291f8f7c1f215ba20930ea9866',
                version: '2.0'
            }).then((AMap) => {
                Amap.value = AMap
                map.value = new AMap.Map('garbage-map-modal', {
                    resizeEnable: true,
                    // mapStyle: 'https://geohub.amap.com/mapstyle/clone?id=20d3f4f1e43c80b9c68c30fb8a1ab94b',
                    zoom: 16,
                    center: [114.61, 30.45]
                })
                const _val = props.garbagePoint.filter((k: any) => k.selected)
                updateMarker(_val)
            })
        }
        // 更新坐标点
        const updateMarker = (data: any[]) => {
            if (!map.value) return
            // 清除地图上的图标
            map.value.clearMap()
            if (!data?.length) return
            data.forEach((item: any) => {
                if (!item.objx || !item.objy) return
                if (item.selected) item['alarmState'] = 1
                else item['alarmState'] = 0
                const content = getMarkerContent(item, '', 'hide-tag', (d: any) => {
                    return require('../images/icon_streetLight_001.png')
                })
                const marker = new Amap.value.Marker({
                    position: [item.objx, item.objy],
                    offset: new Amap.value.Pixel(-5, -35),
                    extData: item.id,
                    content,
                })
                marker.on('click', (e: any) => {
                    const id = e.target.getExtData()
                    if (id == activedObjId.value) {
                        activedObjId.value = 0
                    } else {
                        activedObjId.value = id
                    }
                })
                map.value.add([marker])
            })
            nextTick(() => {
                map.value.setFitView();
                nextTick(() => {
                    // map.value.setZoom(map.value.getZoom() - 3);
                })
            })
        }
        const equipmentTree = ref<any[]>(props.treeList);
        equipmentTree.value = equipmentTree.value.filter(value => {
            value.children = filterTree(value.children, (item: any) => {
                return item.selected
            })
            return value.children && value.children.length
        })
        dfs(equipmentTree.value, 'children', (item: any) => {
            if (item.type === 2) {
                item.selected = false
            }
        })
        const filterEquipmentTree = computed(() => {
            return searchTree(equipmentTree.value, (item: any) => {
                let str = `${item.name}${item.code}`
                item.selected = false
                if (activedObjId.value === item.id) item.selected = true
                return !searchText.value || (str && str.includes(searchText.value))
            }, true)
        })
        const renderContent = (h: any, { data } : { data: areaTree }) => {
            return h(resolveComponent('tooltip-auto-show'), () => `${data.name}(${data.code})`)
        }
        const handleTreeSelect = (data: areaTree[], node: any) => {
            if (activedObjId.value == node.id) {
                activedObjId.value = 0
            } else {
                activedObjId.value = node.id
            }
        }
        const garbagePoint = ref<any[]>();
        const searchText = ref('')
        const activedObjId = ref<number>(0);
        watch(() => activedObjId.value, () => {

            const overlays: any[] = map.value?.getAllOverlays('marker')
            overlays?.forEach(e => {
                const markerInfo = garbagePoint.value?.filter((k: any) => k.id == e.getExtData())[0] || {}
                const content = getMarkerContent(markerInfo, activedObjId.value, 'hide-tag', (d: any) => {
                    return require('../images/icon_streetLight_001.png')
                });
                e.setContent(content)
            })
        })
        const handleActivedObj = (data: any) => {
            if (activedObjId.value == data.id) {
                activedObjId.value = 0
            } else {
                activedObjId.value = data.id
            }
        }
        watch(() => props.garbagePoint, (val) => {
            const _val = val.filter((k: any) => k.selected)
            updateMarker(_val)
            garbagePoint.value = _val
        }, { deep: true, immediate: true })
        onMounted(() => {
            nextTick(() => {
                ininMap();
            })
        },)
        return {
            garbagePoint,
            searchText,
            handleActivedObj,
            activedObjId,
            filterEquipmentTree,
            equipmentTree,
            renderContent,
            handleTreeSelect
        }
    },
})
</script>
<style lang="less" scoped>
@import '../../../../../styles/mapPage.less';
@import '../index.less';
</style>
<style lang="less" scoped>
.modal-map {
    display: flex;

    .device-cont {
        width: 380px;
        padding: 16px 8px;
        .device-cont-search {
            display: flex;
            align-items: center;
            .number {
                margin-left: 5px;
                flex-shrink: 0;
            }
        }
        /deep/.device-info {
            .device-name .name {
                color: #1E2A55;
            }
        }
    }

    .garbage-map-box {
        height: 50vh;
        flex: 1;

        /deep/.hide-tag {
            display: none;
        }
    }
}
</style>
