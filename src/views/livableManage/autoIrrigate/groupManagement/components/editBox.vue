<template>
    <Row :gutter="80">
        <Col span="8">
            <FormItem label="分组名称" prop="groupName">
                <Input v-model="subObj.groupName" maxlength="20" placeholder="请输入"></Input>
            </FormItem>
        </Col>
    </Row>
    <Row :gutter="80">
        <Col span="24">
            <FormItem label="选择设备">
                <!-- <s-transfer
                    :data="m.deviceList"
                    :defaultTargetKeys="m.defaultTargetKeys"
                    :titles="m.titles"
                    filterable
                    @on-change="handleChange">
                </s-transfer> -->
                <treeLinkageMap :tree-list="treeInfo.treeList" :point-list="treeInfo.deviceList" @on-change="handleChange"></treeLinkageMap>
            </FormItem>
        </Col>
    </Row>
    <Row :gutter="80">
        <Col span="24">
            <FormItem label="备注说明" prop="remark">
                <sTextarea v-model="subObj.remark"></sTextarea>
            </FormItem>
        </Col>
    </Row>
</template>

<script>
import { EditMix } from './edit-mixin';
import treeLinkageMap from './treeLinkageMap.vue'
export default {
    name: 'EditBox',
    components: {
        treeLinkageMap
    },
    mixins: [EditMix],
    data() {
        return {}
    },
    methods: {}
}
</script>

<style lang="less" scoped>

</style>
