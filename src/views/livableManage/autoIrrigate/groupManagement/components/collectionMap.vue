<template>
    <div id="garbage-map" class="garbage-map"></div>
    <s-modal :width="1200" title="设备列表" :component-name="componentName" @emitClose="componentName = ''"
        :ref-box="modalRef" :footer-hide="true">
        <component :is="componentName" ref="modalRef" :tree-list="treeList" :garbage-point="garbagePoint" />
    </s-modal>
</template>
<script lang="ts">
import { defineComponent, ref, onMounted, nextTick, watch } from 'vue';
import AMapLoader from '@amap/amap-jsapi-loader';
import { getMarkerContent } from '@/components/common/mapAreaTreeSelect/markercontent'
import collectionMapModal from './collectionMapModal.vue';
export default defineComponent({
    components: {
        collectionMapModal
    },
    props: {
        data: {
            type: Array,
            default: () => []
        },
        treeList: {
            type: Array,
            default: () => []
        }
    },
    setup(props) {
        // *********************
        // 地图
        // *********************
        // 初始化图标
        const map = ref<any>(null);
        const Amap = ref<any>(null);
        const ininMap = () => {
            AMapLoader.load({
                key: 'ea53a5291f8f7c1f215ba20930ea9866',
                version: '2.0'
            }).then((AMap) => {
                Amap.value = AMap
                map.value = new AMap.Map('garbage-map', {
                    resizeEnable: true,
                    // mapStyle: 'https://geohub.amap.com/mapstyle/clone?id=20d3f4f1e43c80b9c68c30fb8a1ab94b',
                    zoom: 16,
                    center: [114.61, 30.45]
                })
                updateMarker(props.data.filter((k: any) => k.selected))
                map.value.setDefaultCursor('pointer');
                map.value.on('click', (e: any) => {
                    componentName.value = 'collectionMapModal'
                })
            })

        }
        // 更新坐标点
        const updateMarker = (data: any[]) => {

            if (!map.value) return
            // 清除地图上的图标
            map.value.clearMap()
            if (!data.length) return
            data.forEach((item: any) => {

                if (!item.objx || !item.objy) return
                item['alarmState'] = 1
                const content = getMarkerContent(item, '', 'hide-tag', (d: any) => {
                    return require('../images/icon_streetLight_001.png')
                })
                const marker = new Amap.value.Marker({
                    position: [item.objx, item.objy],
                    offset: new Amap.value.Pixel(-5, -35),
                    extData: item.id,
                    content,
                })
                marker.on('click', (e: any) => {
                    componentName.value = 'collectionMapModal'
                })
                map.value.add([marker])
            })
            nextTick(() => {
                map.value.setFitView();
                nextTick(() => {
                    // setTimeout(() => {
                    //     map.value.setZoomAndCenter(map.value.getZoom() + 1, map.value.getCenter());
                    // }, 500);
                })
            })
        }
        const garbagePoint = ref<any[]>()
        watch(() => props.data, (val) => {
            updateMarker(val.filter((k: any) => k.selected))
            garbagePoint.value = val
        }, { deep: true })
        // 弹框数据
        const componentName = ref('')
        const modalRef = ref()
        onMounted(() => {
            nextTick(() => {
                ininMap();
            })
        },)
        return {
            componentName,
            modalRef,
            garbagePoint
        }
    },
})
</script>
<style lang="less" scoped>
@import '../../../../../styles/mapPage.less';
@import '../index.less';
</style>
<style lang="less" scoped>
.garbage-map {
    height: 200px;
    flex: 1;
    &:hover {
        cursor: pointer;
        filter: drop-shadow(0px 1px 6px rgba(61, 103, 175, 0.2));
    }

    /deep/.hide-tag {
        display: none;
    }
}
</style>
