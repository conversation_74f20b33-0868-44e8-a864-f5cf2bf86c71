import { validateform } from '@/utils/validateform'
import { treeToList } from '@/utils/tool';
import { dfs } from 'wei-util'
const defaultSubObj = {
    groupName: '', //
    irrigateDeviceList: [],
    remark: '' //
}

export const EditMix = {
    props: {
        id: { default: '' }
    },
    data() {
        return {
            m: {
                titles: ['未选择电磁阀', ' 已选择电磁阀'],
                deviceList: [],
                defaultTargetKeys: []
            },
            treeInfo: {
                treeList: [],
                deviceList: [],
                defaultTargetKeys: []
            },
            subObj: this.$Util.objClone(defaultSubObj)
        }
    },
    computed: {
    },
    watch: {
        subObj: {
            handler(obj) { },
            deep: true
        }
    },
    mounted() {
        this.getDeviceTreeData()
    },
    methods: {
        init(data = {}) {
            this.subObj = data
        },
        getDeviceData() {
            let param = {
                id: ''
            }
            if (this.id) {
                param.id = this.id
            }
            this.$request('/irrigateGroup/groupSelectDevice', param, 'post').then(res => {
                if (res.success) {
                    let deviceList = []
                    let defaultTargetKeys = []
                    if (res.data) {
                        res.data.forEach(item => {
                            item.label = item.deviceExtendInfo.sbmc
                            item.key = item.id
                            if (item.selected) {
                                defaultTargetKeys.push(item.id)
                            }
                            deviceList.push(item)
                        })
                    }
                    this.subObj.irrigateDeviceList = defaultTargetKeys
                    this.m.defaultTargetKeys = defaultTargetKeys
                    this.m.deviceList = deviceList
                }
            })
        },
        getDeviceTreeData() {
            let param = {
                id: ''
            }
            if (this.id) {
                param.id = this.id
            }
            this.$request('/irrigateGroup/groupSelectDeviceTree', param, 'post').then(res => {
                if (res.success) {
                    let deviceList = []
                    let defaultTargetKeys = []
                    let treeList = res.data;
                    let list = []
                    if (treeList) {
                        dfs(treeList, 'children', (item) => {
                            if (item.device) {
                                item.name = item.device.name
                                item.code = item.device.code
                            }
                            if (item.monitorPoint) {
                                item.objx = item.monitorPoint.gdx && item.monitorPoint.gdx || 0
                                item.objy = item.monitorPoint.gdy && item.monitorPoint.gdy || 0
                            }
                            if (this.subObj.id) {
                                item.checked = item.selected
                            }
                        })
                        list = treeToList(treeList)
                        list.forEach(item => {
                            if (item.type === 2) {
                                if (item.selected) {
                                    defaultTargetKeys.push(item.id)
                                }
                                deviceList.push(item)
                            }
                        })
                    }
                    this.subObj.irrigateDeviceList = defaultTargetKeys
                    this.treeInfo.treeList = treeList
                    this.treeInfo.defaultTargetKeys = defaultTargetKeys
                    this.treeInfo.deviceList = deviceList
                }
            })
        },
        handleChange(keys) {
            this.subObj.irrigateDeviceList = keys
        }
    }
}

export const DetailMix = {
    data() {
        return {
            m: {
                loading: false,
                device: {},
                treeList: [],
                mapPointList: []
            },
            subObj: this.$Util.objClone(defaultSubObj),
            rules: {
                groupName: [validateform.required]
            }
        }
    },
    created() {
    },
    methods: {
        submitAjax(met) {
            this.$refs['addForm'].validate((valid) => {
                if (valid) {
                    this.m.loading = true
                    let param = this.$Util.objClone(this.subObj)
                    let irrigateDeviceList = []
                    if (param.irrigateDeviceList.length > 0) {
                        param.irrigateDeviceList.forEach(i => {
                            irrigateDeviceList.push({ id: i })
                        })
                        param.irrigateDeviceList = irrigateDeviceList
                    }
                    console.log(param)
                    let url = '/irrigateGroup/addIrrigateGroup'
                    if (met == 'put') {
                        url = '/irrigateGroup/updateIrrigateGroup'
                    }
                    this.$request(url, param, met).then(res => {
                        if (res.success) {
                            this.$Message.success('提交成功')
                            if (met === 'put') {
                                this.$refs.detail.handleEdit(false)
                                this.getDetailById(param.id)
                                this.getMapPointListById(param.id)
                                this.getDevice()
                            } else {
                                this.backPage()
                            }
                        }
                    }).finally(() => {
                        this.m.loading = false
                    })
                }
            })
        },
        // 得到详情
        getDetailById(id) {
            this.$request(`/irrigateGroup/${id}`).then(res => {
                let data = res.data
                let subObj = this.$Util.objClone(defaultSubObj)
                this.m.device = data
                for (let k in subObj) {
                    if (data[k] || data[k] === 0) {
                        subObj[k] = data[k]
                    }
                }
                subObj.id = data.id
                this.subObj = subObj
            })
        },
        // 得到详情
        getMapPointListById(id) {
            // this.$request(`/garbagePlan/garbagePoint`, { type: 1, id: 31 }, 'post').then(res => {
            //     this.m.mapPointList = res.data
            // })
            this.$request('/irrigateGroup/groupSelectDeviceTree', { id }, 'post').then(res => {
                if (res.success) {
                    let selectedList = []
                    let treeList = res.data;
                    let list = []
                    if (treeList) {
                        dfs(treeList, 'children', (item) => {
                            if (item.device) {
                                item.name = item.device.name
                                item.code = item.device.code
                            }
                            if (item.monitorPoint) {
                                item.objx = item.monitorPoint.gdx && item.monitorPoint.gdx || 0
                                item.objy = item.monitorPoint.gdy && item.monitorPoint.gdy || 0
                            }
                        })
                        list = treeToList(treeList)
                        list.forEach(item => {
                            if (item.type === 2 && item.selected) {
                                selectedList.push(item)
                            }
                        })
                    }
                    this.m.treeList = treeList
                    this.m.mapPointList = selectedList
                }
            })
        },
        init() {
            this.$nextTick(() => {
                try {
                    this.$refs.edit.init(this.subObj)
                } catch { }
            })
        },
        backPage() {
            if(this.$route.query.type == 2){
                this.$store.dispatch('goBack',this.$route.query.type)
             }else if(this.$route.query.type == 3){
                this.$store.dispatch('goBack',this.$route.query.type)
             }
            this.$router.back()
        }
    }
}
