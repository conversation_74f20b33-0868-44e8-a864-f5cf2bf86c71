<template>
    <div class="tree-map garbage-plan-list">
        <div class="garbage-list">
            <div class="title">
                <Checkbox
                    class="check-all"
                    :indeterminate="indeterminate"
                    :model-value="checkAll"
                    @click.prevent="handleCheckAll"
                >
                    {{ title }}
                </Checkbox>
                {{ equipSelectList?.length }}/{{ mapPointList?.length }}
            </div>
            <Input suffix="ios-search" placeholder="请输入" clearable v-model="searchTreeText" />
            <scorll-box :scorll-id="activedObjId" key="code">
                <template #cont>
                    <Tree :data="filterEquipmentTree" show-checkbox multiple :render="renderContent" @on-check-change="handleTreeCheck"></Tree>
                </template>
            </scorll-box>
        </div>
        <div class="garbage-map container-map" id="garbage-map"></div>
        <div class="position-tip">
            <div><span class="gray"></span>未选择</div>
            <div><span class="green"></span>已选择</div>
        </div>
    </div>
</template>

<script>
import { resolveComponent } from 'vue'
import AMapLoader from '@amap/amap-jsapi-loader';
import { deepClone, dfs } from 'wei-util'
import { searchTree } from '@/utils/tool';
import scorllBox from '@/components/common/scorllBox/index';
import { getMarkerContent } from '@/components/common/mapAreaTreeSelect/markercontent';
export default {
    name: 'TreeLinkageMap',
    components: {
        scorllBox
    },
    props: {
        treeList: {
            default() {
                return [];
            },
        },
        pointList: {
            default() {
                return [];
            },
        },
        title: {
            default: '已选择电磁阀'
        },
        defaultTargetKeys: {
            default() {
                return [];
            },
        }, // 默认已选择
    },
    data() {
        return {
            equipmentTree: [], // 左侧设备数
            mapPointList: [], // 地图点位列表
            equipSelectList: [], // 选中设备数据
            checkAll: false,
            indeterminate: false,
            searchTreeText: '',
            map: null,
            Amap: null,
            activedObjId: 0,
        };
    },
    computed: {
        filterEquipmentTree() {
            let text = this.searchTreeText
            dfs(this.equipmentTree, 'children', (item) => {
                if (item.type === 2) {
                    item.checked = this.equipSelectList.includes(item.id) || false
                } else {
                    item.checked = false
                }
            })
            if (!text && text !== 0) return this.equipmentTree
            return searchTree(this.equipmentTree, (item) => {
                let str = `${item.name}${item.code}`
                return str.includes(text)
            }, true)
        }
    },
    watch: {
        equipSelectList(val) {
            if (val) {
                this.handleChange(val)
            }
        },
        mapPointList(val) {
            if (val && val.length) {
                this.updateMarker()
            }
        },
        treeList: {
            handler(val) {
                if (val) {
                    this.init()
                }
            },
            immediate: true
        }
    },
    methods: {
        init() {
            this.equipmentTree = this.treeList;
            this.mapPointList = deepClone(this.pointList)
            this.equipSelectList = this.mapPointList.filter(v => v.checked).map(v => v.id)
            this.copyEquipmentTree = deepClone(this.equipmentTree)
            this.$nextTick(() => {
                this.ininMap();
            });
        },
        // 全选
        handleCheckAll() {
            if (this.indeterminate) {
                this.checkAll = false;
            } else {
                this.checkAll = !this.checkAll;
            }
            this.indeterminate = false;
            if (this.checkAll) {
                this.equipSelectList = this.mapPointList.map(item => item.id);
            } else {
                this.equipSelectList = [];
            }
            this.updateIcon(this.equipSelectList);
        },
        // 更新全选状态和图标状态
        checkAllAndIconState(data) {
            if (data.length === this.mapPointList.length) {
                this.indeterminate = false;
                this.checkAll = true;
            } else if (data.length > 0) {
                this.indeterminate = true;
                this.checkAll = false;
            } else {
                this.indeterminate = false;
                this.checkAll = false;
            }
            this.updateIcon(data);
        },
        renderContent(h, { root, node, data }) {
            return h(resolveComponent('tooltip-auto-show'), () => `${data.name}(${data.code})`)
        },
        // 树勾选变化值
        handleTreeCheck(data, node) {
            if (this.searchTreeText) {
                let ids = []
                if (node.checked) {
                    ids = data.filter(v => v.type == 2).map(v => v.id)
                    this.equipSelectList.push(...ids)
                } else {
                    ids = node.children.length ? (node.children.filter(v => v.type == 2).map(v => v.id)) : [node.id];
                    this.equipSelectList = this.equipSelectList.filter(v => !ids.includes(v))
                }
            } else {
                this.equipSelectList = data.filter(v => v.type == 2).map(v => v.id)
            }
            this.checkAllAndIconState(this.equipSelectList)
            setTimeout(() => {
                node.type === 2 && node.objx && node.objy && this.map.setCenter([node.objx, node.objy]);
            }, 500);
        },
        // 初始化图标
        ininMap() {
            AMapLoader.load({
                key: 'ea53a5291f8f7c1f215ba20930ea9866',
                version: '2.0',
            }).then((AMap) => {
                this.Amap = AMap;
                this.map = new AMap.Map('garbage-map', {
                    resizeEnable: true,
                    // mapStyle: 'https://geohub.amap.com/mapstyle/clone?id=20d3f4f1e43c80b9c68c30fb8a1ab94b',
                    zoom: 16,
                    center: [114.61, 30.45],
                });
                this.updateMarker();
            });
        },
        // 更新坐标点
        updateMarker() {
            if (!this.map) return;
            let AMap = this.Amap;
            let that = this;
            // 清除地图上的图标
            // this.map.clearMap();
            if (!this.mapPointList.length) return;
            this.mapPointList.forEach((item) => {
                if (!item.objx || !item.objy || !item.id) return;
                if (this.equipSelectList.includes(item.id)) {
                    item['alarmState'] = 1;
                } else {
                    item['alarmState'] = 0;
                }
                const content = getMarkerContent(
                    item,
                    that.activedObjId,
                    'hide-tag',
                    (d) => {
                        return require('../images/icon_streetLight_001.png');
                    }
                );
                const marker = new AMap.Marker({
                    position: [item.objx, item.objy],
                    offset: new AMap.Pixel(-5, -35),
                    extData: item.id,
                    content,
                });
                marker.on('click', (e) => {
                    const id = e.target.getExtData();
                    if ((that.equipSelectList).includes(id)) {
                        that.equipSelectList = that.equipSelectList.filter((k) => k != id);
                        that.activedObjId = 0;
                    } else {
                        that.activedObjId = id;
                        that.equipSelectList.push(id);
                        that.map.setCenter([item.objx, item.objy]);
                    }
                    that.checkAllAndIconState(that.equipSelectList);
                });
                this.map.add([marker]);
            });
            this.$nextTick(() => {
                this.map.setFitView();
            });
        },
        // 根据状态更新图标
        updateIcon(deviceList) {
            let that = this;
            let overlays = this.map?.getAllOverlays('marker');
            overlays?.forEach((e) => {
                const id = e.getExtData();
                const markerInfo = deviceList.filter((k) => k.id == id)[0] || {};
                if (deviceList.includes(id)) {
                    markerInfo.alarmState = 1;
                } else {
                    markerInfo.alarmState = 0;
                }
                const content = getMarkerContent(
                    markerInfo,
                    that.activedObjId,
                    'hide-tag',
                    (d) => {
                        return require('../images/icon_streetLight_001.png');
                    }
                );
                e.setContent(content);
            });
        },
        handleChange(val) {
            this.$emit('on-change', val)
        },
    },
};
</script>

<style lang="less" scoped>
@import "../../../../../styles/mapPage.less";
@import "../index.less";
</style>
