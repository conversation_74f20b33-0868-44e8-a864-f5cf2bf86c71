<template>
    <div>
        <BreadcrumbCustom>
            <BreadcrumbItem>自动灌溉</BreadcrumbItem>
            <BreadcrumbItem to="/autoIrrigate/groupManagement">分组管理</BreadcrumbItem>
            <BreadcrumbItem>新建</BreadcrumbItem>
        </BreadcrumbCustom>
        <detailCard title="新建分组" :is-back-btn="true" @on-back="backPage">
            <Form ref="addForm" :model="subObj" :rules="rules" label-position="top">
                <editBox ref="edit" />
                <div class="btn-box">
                    <Button type="primary" @click="ok" :loading="m.loading">提交</Button>
                    <Button @click="backPage">取消</Button>
                </div>
            </Form>
        </detailCard>
    </div>
</template>

<script>
import editBox from './components/editBox';
import { DetailMix } from './components/edit-mixin';

export default {
    name: 'Add',
    components: {
        editBox
    },
    mixins: [DetailMix],
    data() {
        return {
        }
    },
    mounted() {
        this.init()
    },
    methods: {
        ok() {
            this.submitAjax('post')
        }
    }
}
</script>

<style lang="less" scoped>
.btn-box{
    .ivu-btn+.ivu-btn{
        margin-left: 8px;
    }
}
</style>
