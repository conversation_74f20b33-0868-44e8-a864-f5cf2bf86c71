import { validateform } from '@/utils/validateform'
import { treeToList } from '@/utils/tool';
import { dfs } from 'wei-util'
const defaultParam = {
    startTime: '',
    irrigateDuration: 1,
    irrigateWater: ''
}
const defaultSubObj = {
    planNo: '', //
    planName: '',
    controlType: 1, // 控制设备 1分组，2设备
    refList: [],
    paramList: [Util.objClone(defaultParam)],
    timeValues: '', // 季节：1-4，月：1-12，星期：1-7
    timeType: 1, // 时间类型，1季节，2时间区间-月，3时间区间-星期
    remark: '' //
}


export const EditMix = {
    props: {
        id: { default: '' },
        isAdd: { default: true } // 是否新增
    },
    data() {
        return {
            m: {
            },
            g: {
                titles: ['未选择分组', ' 已选择分组'],
                defaultTargetKeys: [],
                deviceList: []
            },
            d: {
                titles: ['未选择设备', ' 已选择设备'],
                defaultTargetKeys: [],
                deviceList: []
            },
            treeInfo: {
                treeList: [],
                deviceList: [],
                defaultTargetKeys: []
            },
            subObj: this.$Util.objClone(defaultSubObj)
        }
    },
    mounted() {
        if (this.isAdd) {
            this.getDeviceData(1)
            this.getDeviceData(2)
        }

    },
    methods: {
        init(data = {}) {
            this.subObj = data
            this.getDeviceData()
            if (data.controlType == 1) {
                this.getDeviceData(2)
            } else {
                this.getDeviceData(1)
            }
        },
        // controlType 1分组，2设备
        getDeviceData(controlType) {
            let param = {
                id: ''
            }
            if (controlType) {
                param.controlType = controlType
            } else {
                if (!this.id) {
                    return
                }
                param.id = this.id
            }
            this.$request('/irrigatePlan/getPlanRefList', param, 'post').then(res => {
                if (res.success) {
                    let deviceList = []
                    let defaultTargetKeys = []
                    if (res.data.controlType == 1) {
                        res.data.groupDTOList.forEach(item => {
                            item.label = item.groupName
                            item.key = item.id
                            if (item.selected) {
                                defaultTargetKeys.push(item.id)
                            }
                            deviceList.push(item)
                        })
                        this.g.deviceList = deviceList
                        this.g.defaultTargetKeys = defaultTargetKeys
                    } else {
                        // res.data.deviceDTOList.forEach(item => {
                        //     item.label = item.deviceExtendInfo.sbmc
                        //     item.key = item.id
                        //     if (item.selected) {
                        //         defaultTargetKeys.push(item.id)
                        //     }
                        //     deviceList.push(item)
                        // })
                        // this.d.deviceList = deviceList
                        // this.d.defaultTargetKeys = defaultTargetKeys
                        // 改为树结构后的数据处理
                        let treeList = res.data.deviceTreeDTOList;
                        let list = treeToList(treeList)
                        if (treeList) {
                            dfs(treeList, 'children', (item) => {
                                if (item.device) {
                                    item.name = item.device.name
                                    item.code = item.device.code
                                }
                                if (item.monitorPoint) {
                                    item.objx = item.monitorPoint.gdx && item.monitorPoint.gdx || 0
                                    item.objy = item.monitorPoint.gdy && item.monitorPoint.gdy || 0
                                }
                                if (this.subObj.id) {
                                    item.checked = item.selected
                                }
                            })
                            list.forEach(item => {
                                if (item.type === 2) {
                                    if (item.selected) {
                                        defaultTargetKeys.push(item.id)
                                    }
                                    deviceList.push(item)
                                }
                            })
                        }
                        this.treeInfo.treeList = treeList
                        this.treeInfo.defaultTargetKeys = defaultTargetKeys
                        this.treeInfo.deviceList = deviceList
                    }
                    this.subObj.refList = defaultTargetKeys
                }
            })
        },
        // 新增控制内容
        addParam() {
            this.subObj.paramList.push(this.$Util.objClone(defaultParam))
        },
        delParam(index) {
            this.subObj.paramList.splice(index, 1)
        },
        // 时间选择
        timeChoose(data) {
            if (data.type == 2) {
                this.subObj.timeType = 1
            } else {
                if (data.timeType == 2) {
                    this.subObj.timeType = 2
                } else {
                    this.subObj.timeType = 3
                }
            }
            this.subObj.timeValues = data.week.join(',')
        },
        handleGroupChange(keys) {
            this.subObj.refList = keys
        },
        handleDeviceChange(keys) {
            this.subObj.refList = keys
        },
        validatorIrrigateDuration(rule, value, callback) {
            if (value < 1) {
                callback('请输入有效的浇水时长')
            } else if (value > 480) {
                callback('单次最大浇水时长不能超过480分钟，请重新输入')
            } else {
                let reg = /^[1-9]\d*$/
                if (reg.test(value)) {
                    callback()
                } else {
                    callback('请输入正整数')
                }
            }
        },
    }
}

export const DetailMix = {
    data() {
        const validatorTimeType = (rule, value, callback) => {
            if (!this.subObj.timeValues) {
                callback('请选择时间')
            } else {
                callback()
            }
        }
        return {
            m: {
                loading: false,
                device: {
                    paramList: []
                },
                timeList: [],
                mapPointList: [],
                treeList: []
            },
            componentName: '',
            subObj: this.$Util.objClone(defaultSubObj),
            rules: {
                planNo: [validateform.required],
                planName: [validateform.required],
                timeType: [{ required: true, trigger: 'change', validator: validatorTimeType }]
            }
        }
    },
    created() {
    },
    methods: {

        submitAjax(met) {
            this.$refs['addForm'].validate((valid) => {
                if (valid) {
                    this.m.loading = true
                    let param = this.$Util.objClone(this.subObj)
                    console.log(param)
                    let url = '/irrigatePlan/addIrrigatePlan'
                    if (met == 'put') {
                        url = '/irrigatePlan/updateIrrigatePlan'
                    }
                    let refList = []
                    if (param.refList.length > 0) {
                        param.refList.forEach(i => {
                            refList.push({ refId: i })
                        })
                        param.refList = refList
                    }
                    this.$request(url, param, met).then(res => {
                        if (res.success) {
                            this.$Message.success('提交成功')
                            if (met === 'put') {
                                this.$refs.detail.handleEdit(false)
                                this.getDetailById(param.id)
                                this.getDevice()
                            } else {
                                this.backPage()
                            }
                        }
                    }).finally(() => {
                        this.m.loading = false
                    })
                }
            })
        },
        // 得到详情
        getDetailById(id) {
            this.$request(`/irrigatePlan/${id}`).then(res => {
                let data = res.data
                let subObj = this.$Util.objClone(defaultSubObj)
                this.m.device = data
                for (let k in subObj) {
                    if (data[k] || data[k] === 0) {
                        subObj[k] = data[k]
                    }
                }
                let timeList = []
                if (data.timeValues) {
                    let arr = data.timeValues.split(',')
                    if (data.timeType == 1) {
                        arr.forEach(n => {
                            timeList.push(this.$enumeration.seasonList[n - 1])
                        })
                    } else if (data.timeType == 3) {
                        arr.forEach(n => {
                            timeList.push(this.$enumeration.weekList[n - 1])
                        })
                    } else {
                        arr.forEach(n => {
                            timeList.push(n + '号')
                        })
                    }
                }
                this.d.controlType = data.controlType
                if (data.controlType == 1) {
                    this.d.title = '分组列表'
                    this.d.columns = [
                        { title: '分组名称', key: 'groupName', tooltip: true },
                        { title: '设备数量', key: 'deviceNum', tooltip: true },
                        { title: '备注', key: 'remark', tooltip: true },
                        { title: '操作', slot: 'action', width: 100 }
                    ]
                } else {
                    this.d.title = '设备列表'
                    this.d.columns = [
                        { title: '设备编号', key: 'deviceCode', tooltip: true },
                        { title: '设备名称', key: 'deviceName', tooltip: true },
                        { title: '区域位置', key: 'areaPath', tooltip: true }
                    ]
                    this.getDeviceData(id)
                }
                this.m.timeList = timeList
                if (subObj.paramList.length > 0) {
                    subObj.paramList = subObj.paramList.map(item => {
                        return {
                            startTime: item.startTime,
                            irrigateDuration: item.irrigateDuration,
                            irrigateWater: item.irrigateWater
                        }
                    })
                }
                subObj.id = data.id
                this.subObj = subObj
            })
        },
        // 获取设备的树结构数据
        getDeviceData(id) {
            let param = {
                id: id
            }
            this.$request('/irrigatePlan/getPlanRefList', param, 'post').then(res => {
                if (res.success) {
                    if (res.data.controlType == 2) {
                        let selectedList = []
                        let treeList = res.data.deviceTreeDTOList;
                        let list = []
                        if (treeList) {
                            dfs(treeList, 'children', (item) => {
                                if (item.device) {
                                    item.name = item.device.name
                                    item.code = item.device.code
                                }
                                if (item.monitorPoint) {
                                    item.objx = item.monitorPoint.gdx && item.monitorPoint.gdx || 0
                                    item.objy = item.monitorPoint.gdy && item.monitorPoint.gdy || 0
                                }
                            })
                            list = treeToList(treeList)
                            list.forEach(item => {
                                if (item.type === 2 && item.selected) {
                                    selectedList.push(item)
                                }
                            })
                        }
                        this.m.treeList = treeList
                        this.m.mapPointList = selectedList
                    }
                }
            })
        },
        // 根据分组id获取设备详情
        getMapPointListById(id) {
            this.$request('/irrigateGroup/groupSelectDeviceTree', { id }, 'post').then(res => {
                if (res.success) {
                    let selectedList = []
                    let treeList = res.data;
                    let list = []
                    if (treeList) {
                        dfs(treeList, 'children', (item) => {
                            if (item.device) {
                                item.name = item.device.name
                                item.code = item.device.code
                            }
                            if (item.monitorPoint) {
                                item.objx = item.monitorPoint.gdx && item.monitorPoint.gdx || 0
                                item.objy = item.monitorPoint.gdy && item.monitorPoint.gdy || 0
                            }
                        })
                        list = treeToList(treeList)
                        list.forEach(item => {
                            if (item.type === 2 && item.selected) {
                                selectedList.push(item)
                            }
                        })
                    }
                    this.m.treeList = treeList
                    this.m.mapPointList = selectedList
                    this.$nextTick(() => {
                        this.componentName = 'collectionMapModal'
                    })
                }
            })
        },
        init() {
            this.$nextTick(() => {
                try {
                    this.$refs.edit.init(this.subObj)
                } catch { }
            })
        },
        backPage() {
            if(this.$route.query.type == 2){
                this.$store.dispatch('goBack',this.$route.query.type)
             }else if(this.$route.query.type == 3){
                this.$store.dispatch('goBack',this.$route.query.type)
             }
            this.$router.back()
        }
    }
}
