<template>
    <div>
        <Row :gutter="80">
            <Col span="8">
                <s-label label="计划编号" >
                    <template #value><span style="font-size: 16px;font-weight: 600;">{{subObj.planNo}}</span></template>
                </s-label>
            </Col>
        </Row>
        <Row :gutter="80">
            <Col span="8">
                <s-label label="计划名称" :value="subObj.planName" />
            </Col>
            <Col span="8">
                <s-label label="计划状态" :tooltip="false">
                    <template #value>{{ subObj.planStatus || subObj.planStatus === 0 ?
                        $enumeration.useStatus[subObj.planStatus] : '--'}}</template>
                </s-label>
            </Col>
            <Col span="8">
                <s-label label="设备数量" :value="subObj.deviceNum" />
            </Col>
            <Col span="8">
                <s-label label="创建人" :value="subObj.creatorName" />
            </Col>
            <Col span="8">
                <s-label label="创建时间" :value="subObj.createTime" />
            </Col>
            <Col span="8">
                <s-label label="更新时间" :value="subObj.modifyTime" />
            </Col>
            <Col span="24">
                <s-label label="备注" :value="subObj.remark" :tooltip="false" />
            </Col>
        </Row>
    </div>
</template>

<script>
export default {
    name: 'infoShow',
    props: {
        subObj: { default() { return {} } }
    },
    data() {
        return {}
    },
    methods: {
        getLightingType() {
            let obj = this.$enumeration.lightingType.find(item => item.value == this.subObj.lightingType)
            return obj ? obj.name : ''
        },
    }
}
</script>

<style lang="less" scoped>

</style>
