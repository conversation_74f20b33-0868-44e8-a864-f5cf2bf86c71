<template>
    <Row :gutter="80">
        <Col span="8">
            <FormItem label="计划编号" prop="planNo">
                <Input v-model="subObj.planNo" maxlength="20" clearable placeholder="请输入"></Input>
            </FormItem>
        </Col>
    </Row>
    <Row :gutter="80">
        <Col span="8">
            <FormItem label="计划名称" prop="planName">
                <Input v-model="subObj.planName" maxlength="20" clearable placeholder="请输入"></Input>
            </FormItem>
        </Col>
    </Row>

    <Row :gutter="80">
        <Col span="24">
            <FormItem label="时间类型" prop="timeType">
                <timeType :type="subObj.timeType" :value="subObj.timeValues" @on-change="timeChoose" />
            </FormItem>
        </Col>
        <Col span="24">
            <div class="label">
                <span class="xin">*</span>
                <span>控制内容</span>
                <Tooltip :max-width="300" placement="right" style="margin-left: 5px;">
                    <Icon custom="iconfont icon-question-circle" color="#C9CDD4" />
                    <template #content>
                        <p>浇水时间和浇水量是“或”运算的关系，浇水时间达到或者浇水量达到都会停止浇水</p>
                    </template>
                </Tooltip>

            </div>
            <div class="con-list">
                <div class="box" v-for="(item, index) in subObj.paramList">
                    <FormItem
                        :label-width="0"
                        :prop="'paramList.' + index + '.startTime'"
                        :rules="{required: true, message: '必选', trigger: 'change'}"
                    >
                        <TimePicker v-model="item.startTime" clearable type="time" placeholder="开始时间" format="HH:mm" style="width: 125px;margin-right: 8px;" />
                    </FormItem>
                    <FormItem
                        :label-width="0"
                        :prop="'paramList.' + index + '.irrigateDuration'"
                        :rules="{ required: true, validator: validatorIrrigateDuration, trigger: 'change,blur'}"
                    >
                        <InputNum v-model="item.irrigateDuration" append="分钟" placeholder="浇水时长" />
                    </FormItem>
                    <FormItem :label-width="0">
                        <InputNum v-model="item.irrigateWater" append="吨" :min="0" placeholder="浇水量" style="width: 133px" />
                    </FormItem>
                    <div class="add" v-if="index === 0" @click="addParam">
                        <Icon type="md-add-circle" />
                        <span>添加</span>
                    </div>
                    <div class="del" v-else @click="delParam(index)">
                        <Icon type="md-remove-circle" />
                        <span>删除</span>
                    </div>
                    <div class="tip" v-if="index === 0">
                        <Icon custom="iconfont icon-info-circle" color="#C9CDD4" />
                        <span>不输入默认浇水量无限制</span>
                    </div>
                </div>
            </div>

        </Col>
        <Col span="24">
            <FormItem label="控制设备" style="margin-bottom: 8px;">
                <RadioGroup v-model="subObj.controlType">
                    <Radio :label="1">
                        <span>按分组选择</span>
                    </Radio>
                    <Radio :label="2">
                        <span>按设备选择</span>
                    </Radio>
                </RadioGroup>
            </FormItem>
        </Col>
        <Col span="24" v-if="subObj.controlType == 1" style="padding-left: 56px;">
            <s-transfer
                :data="g.deviceList"
                :defaultTargetKeys="g.defaultTargetKeys"
                :titles="g.titles"
                filterable
                @on-change="handleGroupChange">
            </s-transfer>
        </Col>
        <Col span="24" v-if="subObj.controlType == 2" style="padding-left: 56px;">
            <!-- <s-transfer
                :data="d.deviceList"
                :defaultTargetKeys="d.defaultTargetKeys"
                :titles="d.titles"
                filterable
                @on-change="handleDeviceChange">
            </s-transfer> -->
            <treeLinkageMap :tree-list="treeInfo.treeList" :point-list="treeInfo.deviceList" @on-change="handleDeviceChange"></treeLinkageMap>
        </Col>
        <Col span="24" style="margin-top: 20px;">
            <FormItem label="备注说明" prop="remark">
                <sTextarea v-model="subObj.remark"></sTextarea>
            </FormItem>
        </Col>
    </Row>
</template>

<script>
import { EditMix } from "./edit-mixin";
import treeLinkageMap from '../../groupManagement/components/treeLinkageMap.vue'
export default {
    name: 'editBox',
    components: {
        treeLinkageMap
    },
    mixins: [EditMix],
    data() {
        return {}
    },
    methods: {}
}
</script>

<style lang="less" scoped>
.label{
    color: @text-color;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    .xin{
        display: inline-block;
        margin-right: 4px;
        line-height: 1;
        font-family: SimSun;
        font-size: 14px;
        color: #F53F3F;
    }
}
.ivu-form-item{
    width: auto;
}
.con-list{
    .box{
        display: flex;
        position: relative;
        .input-num{
            margin-right: 8px;
            width: 133px;
        }
        .add,
        .del{
            color: @primary-color;
            line-height: 32px;
            cursor: pointer;
            .ivu-icon{
                margin-right: 8px;
            }
        }
        .tip{
            position: absolute;
            display: flex;
            bottom: 0;
            font-size: 12px;
            color: @text-3-1;
            left: 273px;
        }
    }
}
</style>
