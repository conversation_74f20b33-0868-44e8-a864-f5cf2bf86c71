<template>
    <div>
        <BreadcrumbCustom>
            <BreadcrumbItem >自动灌溉</BreadcrumbItem>
            <BreadcrumbItem to="/autoIrrigate/irrigationPlan">灌溉计划</BreadcrumbItem>
            <BreadcrumbItem>详情</BreadcrumbItem>
        </BreadcrumbCustom>
        <Form ref="addForm" :model="subObj" :rules="rules" label-position="top">
            <detailCard
                ref="detail"
                title="计划信息"
                :src="require('@/assets/images/icon_detail.png')"
                :isBackBtn="true"
                :isEditBtn="$Util.checkAuth('autoIrrigate:plan:edit')"
                :loading="m.loading"
                @on-submit="submitFun" @on-edit="editChange" @on-back="backPage"
                :class="[isEdit ? 'edit-card' : '' ]"
            >
                <infoShow v-if="!isEdit" :subObj="m.device" />
                <editBox v-if="isEdit" ref="edit" :id="this.detailId" :isAdd="false" />
            </detailCard>
            <detailCard
                title="灌溉计划"
                :src="require('@/assets/images/icon-灌溉.png')"
                v-show="!isEdit"
            >
                <div>
                    <s-label label="时间类型" :tooltip="false">
                        <template #value>{{m.device.timeType == 1 ? '季节控制' : '日期选择'}}</template>
                    </s-label>
                    <s-label label="时间区间" :tooltip="false">
                        <template #value>
                            <s-tag v-for="n in m.timeList" background="#E8F3FF" color="#165DFF" size="large">{{n}}</s-tag>
                        </template>
                    </s-label>
                    <s-label label="控制内容" :tooltip="false">
                        <template #value>
                            <Table :columns="d.conColumns" :data="m.device.paramList" size="small" class="con-table" style="width: 400px;"></Table>
                        </template>
                    </s-label>
                </div>
            </detailCard>
            <detailCard
                :title="d.title"
                :src="require('@/assets/images/icon-设备列表.png')"
                v-show="!isEdit && d.controlType == 1"
            >
                <base-table
                    ref="deviceTb"
                    :columns="d.columns"
                    url="/irrigatePlan/getPlanRefPageList"
                    :loadDone="loadDone"
                    :pageSize="5"
                >
                <template #action="{ row }">
                    <link-btn size="small" @click="toEquipDetail(row)">
                        查看
                    </link-btn>
                </template>
                </base-table>
            </detailCard>
            <detailCard
                :title="d.title"
                :src="require('@/assets/images/icon-设备列表.png')"
                v-show="!isEdit && d.controlType == 2"
            >
                <collection-map-box :data="m.mapPointList" :tree-list="m.treeList" />
            </detailCard>
        </Form>
        <s-modal :width="1200" title="设备列表" :component-name="componentName" @emitClose="componentName = ''"
        :ref-box="modalRef" :footer-hide="true">
        <component :is="componentName" ref="modalRef" :tree-list="m.treeList" :garbage-point="m.mapPointList" />
    </s-modal>
    </div>
</template>

<script>
import editBox from "./components/editBox";
import infoShow from "./components/infoShow";
import collectionMapBox from "../groupManagement/components/collectionMap.vue";
import collectionMapModal from '../groupManagement/components/collectionMapModal.vue';
import { DetailMix } from "./components/edit-mixin";
export default {
    name: 'detail',
    components: {
        editBox,
        infoShow,
        collectionMapBox,
        collectionMapModal
    },
    mixins: [DetailMix],
    data() {
        return {
            isEdit: false, // 是否编辑
            detailId: '',
            d: {
                title: '',
                columns: [],
                searchObj: {
                    ids: ''
                },
                conColumns: [
                    { title: '开始时间', key: 'startTime', tooltip: true },
                    { title: '浇水时长（分钟）', key: 'irrigateDuration', tooltip: true },
                    { title: '水量（吨）', key: 'irrigateWater', render: (h, params) => {
                            return h('div', params.row.irrigateWater || params.row.irrigateWater === 0 ? params.row.irrigateWater : '/');
                        } }
                ]
            }
        }
    },
    computed: {

    },
    created() {
        if (this.$route.query.id) {
            this.detailId = this.$route.query.id
            this.d.searchObj.ids = this.detailId
            this.getDetailById(this.$route.query.id)
        }
    },
    mounted() {
        this.getDevice()
    },
    methods: {
        loadDone(data, list) {
            list.forEach(item => {
                if (item.device) {
                    item.deviceCode = item.device.code
                    item.deviceName = item.deviceExtendInfo.sbmc
                    item.areaPath = this.$Util.getAddressPath(item.deviceExtendInfo)
                }
            })
            return list
        },
        submitFun() {
            this.submitAjax('put')
        },
        getDevice() {
            this.$refs.deviceTb.search(this.d.searchObj)
        },
        editChange(flag) {
            this.isEdit = flag
            if (this.isEdit) {
                this.init()
            }
        },
        toEquipDetail(row) {
            if (!row.id) return
            this.getMapPointListById(row.id)
        }
    }
}
</script>

<style lang="less" scoped>
/deep/ .tab-title {
    justify-content: flex-start;
    padding-bottom: 16px;
    height: 48px;
    column-gap: 20px;
}
.con-table{
    /deep/.ivu-table{
        td{
            border-color: transparent;
        }
        &::before{
            display: none;
        }
    }
}
</style>
