<template>
    <div>
        <BreadcrumbCustom>
            <BreadcrumbItem>自动灌溉</BreadcrumbItem>
            <BreadcrumbItem>灌溉计划</BreadcrumbItem>
        </BreadcrumbCustom>
        <ContentCard title="灌溉计划">
            <BaseForm :model="searchObj" :label-width="90" @handle-submit="search">
                <template #formitem>
                    <FormItem label="计划名称" prop="planName">
                        <Input v-model="searchObj.planName" clearable placeholder="请输入"></Input>
                    </FormItem>
                    <FormItem label="计划状态" prop="planStatus">
                        <Select v-model="searchObj.planStatus" clearable placeholder="请选择">
                            <Option v-for="(item, index) in $enumeration.deviceUesState" :value="index" :key="index">
                                {{ item }}
                            </Option>
                        </Select>
                    </FormItem>
                </template>
            </BaseForm>

            <btn-card>
                <Button type="primary" @click="add" icon="md-add" v-auth="'autoIrrigate:plan:add'">
                    新增
                </Button>
                <Button icon="ios-trash" @click="delMore" v-auth="'autoIrrigate:plan:batchDel'">
                    删除
                </Button>
            </btn-card>

            <base-table ref="mainTb" :columns="columns" url="/irrigatePlan/selectDtoPage"
                        @on-selection-change="selectionChange"
            >
                <template #timeType="{ row }">
                    <TooltipAutoShow :content="getTimeVal(row)" />
                </template>
                <template #paramList="{ row }">
                    <TooltipAutoShow>
                        <span v-for="item in row.paramList">
                            {{ item.startTime }} 浇水{{ item.irrigateDuration }}分钟,浇水量{{ item.irrigateWater || item.irrigateWater === 0 ? (item.irrigateWater + '吨') : '无限制' }}；
                        </span>
                    </TooltipAutoShow>
                </template>
                <template #planStatus="{ row }">
                    <useStatus :value="row.planStatus" />
                </template>
                <template #action="{ row }">
                    <link-btn size="small" @click="toDetail(row)">
                        查看
                    </link-btn>
                    <link-btn size="small" @click="editUseState(row)" v-auth="'autoIrrigate:plan:control'">
                        {{ row.planStatus == 1 ? '停用' : '启用' }}
                    </link-btn>
                </template>
            </base-table>
        </ContentCard>
</div>
</template>

<script>

export default {
    components: {
    },
    data() {
        return {
            columns: [
                { type: 'selection', width: 40, align: 'center' },
                { title: '计划编号', key: 'planNo', tooltip: true },
                { title: '计划名称', key: 'planName', tooltip: true },
                { title: '时间类型', slot: 'timeType', tooltip: true, resizable: true },
                { title: '控制内容', slot: 'paramList', tooltip: true, resizable: true },
                { title: '设备数量', key: 'deviceNum', width: 80, tooltip: true },
                { title: '计划状态', slot: 'planStatus', width: 80, tooltip: true },
                { title: '创建时间', key: 'createTime', width: 160, tooltip: true },
                { title: '更新时间', key: 'modifyTime', width: 160, tooltip: true },
                { title: '操作', slot: 'action', width: 100 }
            ],
            searchObj: {
                planName: '',
                planStatus: ''
            },
            m: {
                selectList: [], // 表格已选数据
                showControlFlag: false,
                curControlObj: {}
            }
        }
    },
    mounted() {
        this.search()
    },
    methods: {
        search() {
            this.$refs.mainTb.search(this.searchObj)
        },
        getTimeVal(data) {
            let str = data.timeType == 1 ? '季节控制' : '时间区间'
            let timeList = []
            if (data.timeValues) {
                let arr = data.timeValues.split(',')
                if (data.timeType == 1) {
                    arr.forEach(n => {
                        timeList.push(this.$enumeration.seasonList[n - 1])
                    })
                } else if (data.timeType == 3) {
                    arr.forEach(n => {
                        timeList.push(this.$enumeration.weekList[n - 1])
                    })
                } else {
                    arr.forEach(n => {
                        timeList.push(n + '号')
                    })
                }
            }
            str += ':' + timeList.join('、')
            return str
        },
        editUseState(row) {
            let state = row.planStatus
            let str = ''
            if (state == 1) {
                state = 0
                str = '停用'
            } else {
                state = 1
                str = '启用'
            }
            this.$Modal.confirm({
                title: '提示',
                content: `您确定要${str}吗`,
                onOk: () => {
                    this.$request('/irrigatePlan/batchStartStop', { ids: row.id, planStatus: state }, 'post').then(res => {
                        if (res.success) {
                            this.$Message.success('操作成功')
                            this.search()
                        }
                    })
                }
            })
        },
        add() {
            this.$router.push({
                path: '/autoIrrigate/irrigationPlanAdd'
            })
        },
        toDetail(row) {
            console.log("🚀 ~ toDetail ~ row:", row)
            this.$router.push({
                path: '/autoIrrigate/irrigationPlanDetail',
                query: {
                    id: row.id
                }
            })
        },
        delMore() {
            if (this.m.selectList.length === 0) {
                this.$Message.warning('最少选择一条数据')
                return
            }
            this.$Modal.confirm({
                title: '提示',
                content: '您确定要删除选中的数据吗',
                onOk: () => {
                    let arr = []
                    this.m.selectList.forEach(item => {
                        arr.push(item.id)
                    })
                    this.delAjax(arr)
                }
            })
        },
        delAjax(arr) {
            this.$request('/irrigatePlan/batchDelete?idList=' + arr.join(','), {}, 'delete').then(res => {
                if (res.success) {
                    this.$Message.success('删除成功')
                    this.m.selectList.length = []
                    this.search()
                }
            })
        },
        selectionChange(list) {
            this.m.selectList = list || []
        }
    }
}
</script>

<style lang="less" scoped>

</style>
