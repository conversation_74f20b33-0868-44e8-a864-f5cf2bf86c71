
<template>
    <BreadcrumbCustom>
        <BreadcrumbItem>自动灌溉</BreadcrumbItem>
        <BreadcrumbItem>统计分析</BreadcrumbItem>
    </BreadcrumbCustom>
    <ContentCard style="margin-bottom: 8px;">
        <deviceTreeSelect @on-change="changeDevice" style="width: 280px" title="电磁阀控制器"
            method-url="/irrigateStatisticalAnalyze/getTreeIncludeDevice" :show-border="false" />
    </ContentCard>
    <ContentCard title="用水概览" style="margin-bottom: 8px;">
        <energyYear :list="energyStat" />
    </ContentCard>
    <ContentCard title="用电概览" style="margin-bottom: 8px;">
        <energyYear :list="energyEleStat" title="电" unit="kWh" timeKey="Electric" />
    </ContentCard>
    <ContentCard style="margin-bottom: 8px;">
    <Row>
        <Col span="12">
        <div class="search-box">

            <dateSelectT @on-change="changeTime" :default-index="2" />
        </div>
        <div>
            <Title level="6">能耗趋势分析</Title>
            <echart-item :option="lineOption" class="line-echart" />
        </div>
        </Col>
        <Col span="1"><div></div></Col>
        <Col span="11">
            <!--下拉框-->
            <div class="search-box">
                <Select v-model="alarmType" placeholder="全部报警类型" @on-change="changeQueryType">
                    <Option v-for="item in cityList" :value="item.value" :key="item.value">{{ item.label }}</Option>
                </Select>
            </div>
            <Title level="6">报警趋势分析</Title>
            <echart-item :option="lineOption2" class="line-echart" />
        </Col>
    </Row>
        <div>
            <s-tab :tab-list="[
                { name: '用水明细表', key: 'table' },
            ]" tit-class="mid-table-title">
                <template #expand>
                    <Button @click="getExports" type="primary" v-auth="'autoIrrigate:statisticsAnalyze:export'">
                        <i style="font-size: 11px" class="iconfont icon-Vector"></i>导出
                    </Button>
                </template>
                <template #table>
                    <div class="info-time-box">本期：{{ tableInfo.startTime }}～{{ tableInfo.endTime }}</div>
                    <div class="info-box">
                        <Icon color="#165DFF" size="16" type="md-information-circle" />{{ tableInfo.info }}
                    </div>
                    <base-table ref="mainTb" :columns="columns" url="/irrigateStatisticalAnalyze/tableDataStatistics">
                        <template #timeRange="{ row }">
                            {{ row.startTime }}<br />
                            ~<br />
                            {{ row.endTime }}
                        </template>
                        <template #deviceName="{ row }">
                            {{ row.deviceName || '-' }}
                        </template>
                        <template #deviceCode="{ row }">
                            {{ row.deviceCode || '-' }}
                        </template>
                        <template #timeUse="{row}">
                            {{ (+row.timeUse).toFixed(1) }}
                        </template>
                        <template #waterUse="{row}">
                            {{ $Util.formatNum(row.waterUse) }}
                        </template>
                    </base-table>
                </template>
            </s-tab>
        </div>
    </ContentCard>
</template>
<script lang="ts" setup>
import { EchartsTooltip, tooltipExtraCssText } from '@/utils/tool';
import EchartItem from '@/components/common/EchartItem/index.tsx'
import deviceTreeSelect from '@/components/common/deviceTreeSelect/index.vue'
import dateSelectT from '@/components/common/dateSelect/dateSelectT.vue'
import energyYear from './components/energyYear.vue'
import { getCurrentInstance, onMounted, ref } from 'vue';
import { Col } from 'view-ui-plus';
const that = getCurrentInstance()?.appContext.config.globalProperties;
const switchBtn = ref<any>(true)
// 选择的时间
const searchObj = ref<any>({
    startTime: '',
    endTime: '',
    queryType: '', // day日,month月,year年
    irrigateDeviceVoList: [],

})
// 能耗概览
const energyStat = ref<any>([])
const energyEleStat = ref<any>([])
const lineOption = ref<any>({
    tooltip: {
        trigger: 'axis',
        confine: true,
        axisPointer: {},
        extraCssText: tooltipExtraCssText,
        formatter: (arg) => {
            return EchartsTooltip(arg, '')
        }
    },
    grid: {
        top: 30,
        bottom: 30,
        left: 0,
        right: 0,
        containLabel: true
    },
    legend: {
        show: true,
        itemHeight: 10,
        itemWidth: 10,
        icon: 'circle',
        bottom: 0
    },
    xAxis: [
        {
            type: 'category',
            axisLabel: {
                color: '#86909C'
            },
            axisLine: {
                show: true,
                lineStyle: {
                    color: '#E5E6EB'
                }
            },
            axisTick: {
                show: false,
                lineStyle: {
                    color: '#86909C'
                }
            },
            data: []
        }
    ],
    yAxis: [
        {
            type: 'value',
            name: '单位：吨',
            nameTextStyle: {
                align: 'left'
            },
            splitLine: {
                show: true,
                lineStyle: {
                    color: '#F2F3F5'
                }
            },
            axisLabel: {
                color: '#4E5969'
            }
        },
        {
            type: 'value',
            name: '单位：kWh',
            nameTextStyle: {
                align: 'right'
            },
            splitLine: {
                show: true,
                lineStyle: {
                    color: '#F2F3F5'
                }
            },
            axisLabel: {
                color: '#4E5969'
            }
        }
    ],
    color: ['#21CCFF', '#86DF6C'],
    series: [
        {
            data: [],
            name: '用水量(吨)',
            type: 'line',
            smooth: true,
            showSymbol: false,
            areaStyle: {
                color: {
                    type: 'linear',
                    x: 0,
                    y: 0,
                    x2: 0,
                    y2: 1,
                    colorStops: [
                        {
                            offset: 0,
                            color: 'rgba(17, 126, 255, 0.16)'
                        },
                        {
                            offset: 1,
                            color: 'rgba(17, 128, 255, 0)'
                        }
                    ],
                    global: false
                }
            }
        },
        {
            data: [],
            name: '用电量(kWh)',
            type: 'line',
            smooth: true,
            showSymbol: false,
            areaStyle: {
                color: {
                    type: 'linear',
                    x: 0,
                    y: 0,
                    x2: 0,
                    y2: 1,
                    colorStops: [
                        {
                            offset: 0,
                            color: '#86DF6C'
                        },
                        {
                            offset: 1,
                            color: 'rgba(17, 128, 255, 0)'
                        }
                    ],
                    global: false
                }
            },
            yAxisIndex: 1
        }
    ]
})
// 报警趋势分析
const lineOption2 = ref<any>({
    tooltip: {
        trigger: 'axis',
        confine: true,
        axisPointer: {},
        extraCssText: tooltipExtraCssText,
        formatter: (arg) => {
            return EchartsTooltip(arg, '个')
        }
    },
    grid: {
        top: 20,
        bottom: 30,
        left: 0,
        right: 0,
        containLabel: true
    },
    legend: {
        show: true,
        itemHeight: 10,
        itemWidth: 10,
        icon: 'circle',
        bottom: 0
    },
    xAxis: [
        {
            type: 'category',
            axisLabel: {
                color: '#86909C'
            },
            axisLine: {
                show: true,
                lineStyle: {
                    color: '#E5E6EB'
                }
            },
            axisTick: {
                show: false,
                lineStyle: {
                    color: '#86909C'
                }
            },
            data: []
        }
    ],
    yAxis: {
        type: 'value',
        splitLine: {
            show: true,
            lineStyle: {
                color: '#F2F3F5'
            }
        },
        axisLabel: {
            color: '#4E5969'
        }
    },
    color: ['#21CCFF', '#86DF6C'],
    series: [
        {
            data: [],
            name: '告警数量',
            type: 'line',
            smooth: true,
            showSymbol: false,
            // areaStyle: {
            //     color: {
            //         type: 'linear',
            //         x: 0,
            //         y: 0,
            //         x2: 0,
            //         y2: 1,
            //         colorStops: [
            //             {
            //                 offset: 0,
            //                 color: 'rgba(17, 126, 255, 0.16)'
            //             },
            //             {
            //                 offset: 1,
            //                 color: 'rgba(17, 128, 255, 0)'
            //             }
            //         ],
            //         global: false
            //     }
            // }
        }
    ]
})
const columns = ref<any>([
    { title: '统计时间', key: 'dateTimeStr', tooltip: true },
    { title: '采集区间', slot: 'timeRange', tooltip: true, minWidth: 150, },
    { title: '设备编号', slot: 'deviceCode', tooltip: true },
    { title: '设备名称', slot: 'deviceName' },
    { title: '用水量（吨）', slot: 'waterUse' },
    { title: '关联电磁阀控制器', key: 'parentDeviceName', minWidth: 120, tooltip: true },
    { title: '电磁阀总数', key: 'deviceNum' },
    { title: '电磁阀开启时长(H)', slot: 'timeUse', minWidth: 120 }
])
const mainTb = ref<any>()
function changeDevice(list: string[]) {
    searchObj.value.irrigateDeviceVoList = (list || []).map((k: string) => ({ deviceCode: k }))
    getStat()
    getELeStat()
    getData()
}
function changeTime(val: any, item: any) {
    searchObj.value.startTime = val.startTime
    searchObj.value.endTime = val.endTime
    searchObj.value.queryType = ['day', 'month', 'year'][val.dateType]
    getData()
}
// 导出
function getExports() {
    that.$request('/irrigateStatisticalAnalyze/tableDataStatisticsExport', searchObj.value, 'post').then((res: any) => {
        if (res.success) {
            downFile(res.data)
        }
    })
}
function downFile(url: string) {
    const a = document.createElement('a')
    fetch(url).then(res => res.blob()).then(blob => { // 将链接地址字符内容转变成blob地址
        a.href = URL.createObjectURL(blob)
        a.download = url.split('/')[url.split('/').length - 1] //  // 下载文件的名字
        document.body.appendChild(a)
        a.click()
        // 在资源下载完成后 清除 占用的缓存资源
        window.URL.revokeObjectURL(a.href);
        document.body.removeChild(a);
    })
}
function getData() {
    if (!searchObj.value.startTime) {
        return
    }
    if (switchBtn.value) {
        getTrend()
        getAlarmTrend()
        tableStatisticsInfo()
        mainTb.value?.search(searchObj.value)
    }
}
onMounted(() => {
    mainTb.value?.search(searchObj.value)
})
// 用水量趋势图
function getTrend() {
    that.$request('/irrigateStatisticalAnalyze/trendDataStatistics', searchObj.value, 'post').then((res: any) => {
        if (res.success) {
            lineOption.value.xAxis[0].data = res.data.map(k => k.dateTimeStr)
            lineOption.value.series[0].data = res.data.map(k => that.$Util.formatNum(k.waterUse))
            lineOption.value.series[1].data = res.data.map(k => that.$Util.formatNum(k.electricUse))
        }
    })
}
// 下拉框
const cityList = ref<any>([
    { value: '080001', label: '设备离线告警' },
    { value: '090101', label: '电磁阀故障告警' },
    { value: '090102', label: '浇水量异常' },
])
// 每次选择下拉框，返回数据
const alarmType = ref()
const changeQueryType = (val: string) => {
    searchObj.value.alarmType = val
    console.log(searchObj.value.alarmType)
    getAlarmTrend()
}
// 报警趋势分析
function getAlarmTrend() {
    let alam = {
        startTime: searchObj.value.startTime,
        endTime: searchObj.value.endTime,
        irrigateDeviceVoList: searchObj.value.irrigateDeviceVoList,
        queryType: searchObj.value.queryType,
        alarmType: searchObj.value.alarmType
    }
    that.$request('/irrigateStatisticalAnalyze/alarm/trendDataStatistics', alam, 'post').then((res: any) => {
        let data = res.data
        // console.log('----->', data[0].dataList)
        let obj: any = {
            a1: []
        }
        for (let i in data) {
            let t = 0
            data[i].dataList.forEach(item => {
                t += item.alarmCount
            })
            obj.a1.push(t);
        }
        if (res.success) {
            lineOption2.value.xAxis[0].data = res.data.map(k => k.dateTimeStr)
            lineOption2.value.series[0].data = obj.a1
        }
    })
}
const tableInfo = ref({
    endTime: '',
    startTime: '',
    info: ''
})
function tableStatisticsInfo() {
    that.$request('/irrigateStatisticalAnalyze/tableStatisticsInfo', searchObj.value, 'post').then((res: any) => {
        if (res.success) {
            const { tableStatisticsList = [] } = res.data
            const _tableStatisticsList = {
                waterUse: 0,
                avgDayWaterUse: 0,
                dcfUseTime: 0,
                dcfHourAvgUseWater: 0
            }
            tableStatisticsList.forEach((e: any) => {
                _tableStatisticsList[e.key] = e.value
            })
            tableInfo.value = {
                endTime: res.data.endTime?.split(' ')[0],
                startTime: res.data.startTime?.split(' ')[0],
                info: `本期总用水量${Math.round(_tableStatisticsList.waterUse)}吨，平均每日用水量${((+_tableStatisticsList.avgDayWaterUse).toFixed(1))}吨，电磁阀总开启时长${(+_tableStatisticsList.dcfUseTime).toFixed(1)}小时，电磁阀开启平均每小时用水量${(+_tableStatisticsList.dcfHourAvgUseWater).toFixed(1)}吨`
            }
        }
    })
}

// 能耗概览
function getStat() {
    let param = {
        irrigateDeviceVoList: searchObj.value.irrigateDeviceVoList
    }
    that.$request('/irrigateStatisticalAnalyze/topWaterUseStatistics', param, 'post').then((res: any) => {
        if (res.success) {
            energyStat.value = res.data || []
        }
    })
}
// 用电
function getELeStat() {
    let param = {
        irrigateDeviceVoList: searchObj.value.irrigateDeviceVoList
    }
    that.$request('/irrigateStatisticalAnalyze/topElectricUseStatistics', param, 'post').then((res: any) => {
        if (res.success) {
            energyEleStat.value = res.data || []
        }
    })
}
</script>
<style lang="less" scoped>
.line-echart {
    height: 300px;
}

.info-time-box {
    color: #798799;
    display: flex;
    align-items: center;
    font-size: 12px;
}

.info-box {
    margin: 16px 0;
    height: 40px;
    border-radius: 2px;
    background-color: #e8f3ff;
    display: flex;
    align-items: center;
    padding: 12px;
    column-gap: 8px;
    color: #1d2129;
}

.switch {
    height: 32px;
    background: #F3F7FB;
    border-radius: 2px;
    line-height: 32px;
    color: #4E627E;
    width: 110px;
    text-align: center;
    cursor: pointer;
    float: right;

    &:hover {
        color: @primary-color;
    }
}

.search-box {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16px;
}

/deep/.mid-table-title {
    justify-content: space-between;
    font-weight: bold;
    margin-bottom: 16px;
}
</style>
