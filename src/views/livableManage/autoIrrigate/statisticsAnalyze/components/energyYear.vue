<template>
    <div class="energy-year">
        <div v-for="(item, index) in statList" class="box" :key="index">
            <div class="name">{{ item.curName }}用{{ title }}量</div>
            <div class="num"><span>{{ $Util.formatNum(item.thisTime) }}</span>{{ unit }}</div>
            <div class="before">
                <div class="val">{{ item.beforeName }}同期 {{ $Util.formatNum(item.lastTime) }}{{ unit }}</div>
                <div v-if="item.compRate >= 0" class="rad up">
                    <span>{{ (item.compRate * 100).toFixed(2) }}%</span>
                    <img src="@/assets/images/icon_up.png" alt="">
                </div>
                <div v-else class="rad down">
                    <span>{{ (item.compRate * -100).toFixed(2) }}%</span>
                    <img src="@/assets/images/icon_down.png" alt="">
                </div>
            </div>
            <div class="box-bg">
                <img :src="require('@/assets/images/' + item.img)" alt="">
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: 'EnergyYear',
    props: {
        list: { default() { return [] } },
        title: { default: '水' },
        unit: { default: '吨' },
        timeKey: { default: 'Water' },
    },
    data() {
        return {
            txtList: [
                { curName: '今日', beforeName: '昨日', img: 'energy_box_bg.png', key: 'todayStatisticsList', thisTimeKey: 'tdUse', thisTime: 0, lastTimeKey: 'ydUse', lastTime: 0, compRate: 0 },
                { curName: '本周', beforeName: '上周', img: 'energy_box_bg1.png', key: 'curWeekStatisticsList', thisTimeKey: 'twUse', thisTime: 0, lastTimeKey: 'lwUse', lastTime: 0, compRate: 0 },
                { curName: '本月', beforeName: '上月', img: 'energy_box_bg.png', key: 'curMonthStatisticsList', thisTimeKey: 'tmUse', thisTime: 0, lastTimeKey: 'lmUse', lastTime: 0, compRate: 0 },
                { curName: '本年', beforeName: '去年', img: 'energy_box_bg2.png', key: 'curYearStatisticsList', thisTimeKey: 'tyUse', thisTime: 0, lastTimeKey: 'lyUse', lastTime: 0, compRate: 0 }
            ]
        }
    },
    computed: {
        statList() {
            return this.txtList.map((item, index) => {
                const _data = this.list[item.key]
                const thisTime = _data?.find(k => k.key === (item.thisTimeKey + this.timeKey)).value
                const lastTime = _data?.find(k => k.key === (item.lastTimeKey + this.timeKey)).value
                const compRate = _data?.find(k => k.key === 'compRate').value
                return {
                    ...item,
                    thisTime,
                    lastTime,
                    compRate,
                }
            })
        }
    },
    methods: {}
}
</script>

<style lang="less" scoped>
.energy-year {
    display: flex;
    align-items: flex-start;

    .box {
        flex: 1;
        margin-right: 27px;
        background: linear-gradient(180deg, #F2F9FE 0%, #E6F4FE 100%);
        border-radius: 4px;
        height: 136px;
        padding: 16px 20px;
        position: relative;

        .name {
            font-weight: 600;
            font-size: 16px;
            line-height: 24px;
            margin-bottom: 24px;
        }

        .num {
            color: @text-3-1;
            font-size: 12px;
            line-height: 20px;
            margin-bottom: 4px;

            span {
                color: @title-color;
                font-weight: 700;
                font-size: 24px;
                line-height: 26px;
                margin-right: 4px;
            }
        }

        .before {
            display: flex;
            align-items: center;
            justify-content: space-between;

            .val {
                font-size: 12px;
                line-height: 20px;
                color: @text-color;
            }

            .rad {
                display: flex;
                align-items: center;
                font-weight: 500;
                font-size: 14px;
                line-height: 20px;

                img {
                    width: 12px;
                    display: block;
                    margin-left: 4px;
                }

                &.up {
                    color: #F53F3F;
                }

                &.down {
                    color: #00B42A;
                }
            }
        }

        .box-bg {
            position: absolute;
            width: 118px;
            right: 8px;
            bottom: 5px;

            img {
                width: 100%;
                display: block;
            }
        }

        &:nth-child(2) {
            background: linear-gradient(180deg, #F5FEF2 0%, #E6FEEE 100%);
        }

        &:last-child {
            margin-right: 0;
            background: linear-gradient(180deg, #F7F7FF 0%, #ECECFF 100%);
        }
    }
}
</style>
