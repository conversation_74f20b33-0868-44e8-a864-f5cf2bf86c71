<script lang="ts" setup>
import {
    defineComponent,
    reactive,
    markRaw,
    toRefs,
    onBeforeUnmount,
    onMounted,
    ref,
    computed,
} from "vue";
import TableContentCard from '@/components/global/TableContentCard'
import { onBeforeRouteLeave, useRouter } from "vue-router";
import { dictionary } from "@/api/dictionary";
import { Dictionary,ModelSelect } from "./type";
import { commonService } from "@/api/commonService";
import moment from "moment";
import Util from "@/utils";
import { bus, isNullOrEmpty } from "@/utils/tool";
import { useStore } from "vuex";
const store = useStore();
const router = useRouter();
interface DataType {
    searchObj: any;
    tabList: any[];
    tableList: any[];
    listCom: any;
    list2Com: any;
    model: Dictionary[] | any;
}
// 加载路由菜单派发actions
const dispatchGetMenu = async () => await store.dispatch("user/getUserCode");
//加载登入信息
const getInfo = async () => await store.dispatch("user/getInfo");
const getUserCode = async () => await store.dispatch("user/getUserCode");
const getApplicationAuth = async () => await store.dispatch("user/getApplicationAuth");
dispatchGetMenu();
getInfo();
getUserCode();
getApplicationAuth();
const Data = ref<DataType>({
    searchObj: {
        readStatus: 0,
        type: ''
    },
    tabList: [
        {
            name: "未读消息",
            key: "unread",
        },
        {
            name: "已读消息",
            key: "read",
        },
    ],
    tableList: [
        { title: "序号", type: "index", width: 60 },
        { title: "业务模块", key: "modelName", maxWidth: 200, tooltip: true },
        { title: "消息类型", slot: "type", maxWidth: 100, tooltip: true },
        { title: "消息标题", key: "title", maxWidth: 200, tooltip: true },
        { title: "消息内容", key: "content", tooltip: true },
        { title: "通知时间", key: "recordTime", width: 170 },
        // { title: "操作", slot: "operation", width: 80, fixed: "right" },
    ],
    listCom: null,
    list2Com: null,
    model: [],
});
const { searchObj, tabList, tableList, listCom, model, list2Com } = toRefs(Data.value);
const handleSubmit = () => {
    if (sessionStorage.getItem("isRead")) {
        searchObj.value.readStatus = Number(JSON.parse(sessionStorage.getItem("isRead") || ""));
        sessionStorage.removeItem("isRead");
    }
    let params = Util.objClone(searchObj.value);
    console.log(searchObj.value)
    if (params.recordTime) {
        params.endTime = params.recordTime[1];
        params.startTime = params.recordTime[0];
    }
    delete params.recordTime;
    if (params.readStatus === 0) {
        listCom.value?.search(params);
    } else {
        list2Com.value?.search(params);
    }

};
// 查询模块下拉
const getModelSelect = async () => {
    const path: string = window.window.document.location.href;
    let data:ModelSelect = {
        parentCode: "", //08园区畅行   09生态宜居 10生态宜居
    };
    if (path.indexOf("/traffic") !== -1) {
        data.parentCode =  "08"
    } else if (path.indexOf("/safe") !== -1) {
        data.parentCode =  "10"
    } else {
        data.parentCode =  "09"

    }
    commonService.queryModelSelect(data).then((res:any) => {
        if(res.success){
            model.value = res.data;
        }
    })
};
getModelSelect()

// 点击查看
const handleCheck = (id: number, origin: string) => {
    const defaultIndex = origin === "unread" ? 0 : 1;
    sessionStorage.setItem("isRead", JSON.stringify(defaultIndex));
    router.push({
        name: "messages:detail",
        query: {
            id,
            origin,
        },
    });
};
const handleTabChange = (index: number) => {
    searchObj.value.readStatus = index;
    handleSubmit();
};
// 获取未读消息条数
const getUnreadMessagesNumber = () => {
    commonService.getUnreadMessages().then((res: any) => {
        if (res.success) {
            tabList.value[0].name = "未读消息" + " (" + res.data + ")";
        }
    });
};

// 获取所有已读
const getMessage = () => {
    commonService.getMessageList(-1).then((res: any) => {
        if (res.success) {
            tabList.value[1].name = "已读消息" + " (" + res.data.total + ")";
        }
    });
};

onMounted(() => {
    handleSubmit();
    getMessage();
    getUnreadMessagesNumber();
    bus.on("allRead", () => {
        handleSubmit();
        getMessage();
        getUnreadMessagesNumber();
    });
});
onBeforeUnmount(() => {
    bus.off("allRead");
});
onBeforeRouteLeave((to, from, next) => {
    if (to.path !== "/common/messagesDetail") {
        sessionStorage.removeItem("isRead");
    }
    next();
});
const title = computed(() => {
    const path: string = window.window.document.location.href;
    if (path.indexOf("/traffic") !== -1) {
        return "园区畅行";
    } else if (path.indexOf("/safe") !== -1) {
        return "安全守护";
    } else {
        return "生态宜居";
    }
});
// 一键已读
const handleAllRead = () => {
    commonService.oneReadMessage().then((res: any) => {
        if (res.success) {
            getMessage();
            getUnreadMessagesNumber();
            handleSubmit();
            bus.emit("read");
        }
    });
};
</script>

<template>
    <BreadcrumbCustom>
        <BreadcrumbItem>{{ title }}</BreadcrumbItem>
        <BreadcrumbItem>消息通知</BreadcrumbItem>
    </BreadcrumbCustom>
    <ContentCard title="消息通知">
        <BaseForm :model="searchObj" :label-width="90" @handle-submit="handleSubmit">
            <template #formitem>
                <FormItem label="业务模块" prop="model">
                    <Select v-model="searchObj.model" clearable placeholder="请选择">
                        <Option
                            v-for="(item, index) in model"
                            :value="item.code"
                            :key="item.code"
                        >
                            {{ item.name }}
                        </Option>
                    </Select>
                </FormItem>
                <FormItem label="消息标题" prop="title">
                    <Input v-model="searchObj.title" clearable placeholder="请输入"></Input>
                </FormItem>
                <FormItem label="通知时间" prop="recordTime">
                    <DatePicker
                        type="daterange"
                        format="yyyy-MM-dd"
                        v-model="searchObj.recordTime"
                        @on-change="((date:any)=>{
                           searchObj.recordTime = !isNullOrEmpty(date.filter(Boolean)) && [ moment(date[0]).startOf('day').format('YYYY-MM-DD HH:mm:ss'),
                            moment(date[1]).endOf('day').format('YYYY-MM-DD HH:mm:ss')]
                        })"
                        clearable
                        placeholder="请选择时间区间"
                    ></DatePicker>
                </FormItem>
                <FormItem label="消息类型" prop="type">
                    <Select v-model="searchObj.type" clearable placeholder="请选择">
                        <Option
                            v-for="(item, index) in $enumeration.messageType"
                            :value="item.value"
                            :key="item.value"
                        >
                            {{ item.name }}
                        </Option>
                    </Select>
                </FormItem>
            </template>
        </BaseForm>
        <tableContentCard class="table-content-card" :baseBtn="false">

                <span class="all-read" v-show="searchObj.readStatus === 0" @click="handleAllRead"
                    >全部已读</span
                >
                <s-tab
                    :defaultActive="searchObj.readStatus"
                    @handleChange="handleTabChange"
                    titClass="message-s-tab"
                    :tab-list="tabList"
                >
                    <template #unread>
                        <base-table ref="listCom" url="/baseMessage/list" :columns="tableList">
                            <template #type="{ row }">
                                <span>{{ $Util.findNameByList($enumeration.messageType, row.type) }}</span>
                            </template>
                        </base-table>
                    </template>
                    <template #read>
                        <base-table ref="list2Com" url="/baseMessage/list" :columns="tableList">
                            <template #type="{ row }">
                                <span>{{ $Util.findNameByList($enumeration.messageType, row.type) }}</span>
                            </template>
                        </base-table>
                    </template>
                </s-tab>

        </tableContentCard>
    </ContentCard>
</template>

<style lang="less" scoped>
.table-content-card {
    .all-read {
        position: absolute;
        right: 12px;
        top: 10px;
        color: @primary-color;
        cursor: pointer;
        z-index: 10;
    }
}
</style>
