export interface Dictionary {
  requestId?: any;
  requestApplicationId?: any;
  requestApplicationCode?: any;
  code: string;
  parentCode: string;
  type: number;
  name: string;
  fullPathName: string;
  algorithmTypes: string;
  modelId?: any;
  defaultAlarmLevel?: any;
  creatorId?: any;
  createTime?: any;
  modifyId?: any;
  modifyTime?: any;
  remark?: any;
  parentCodeName: string;
  modelIdName: string;
}

export interface ModelSelect{
  parentCode?:string
}