<script lang="ts" setup>
import {
    defineComponent,
    reactive,
    markRaw,
    toRefs,
    onBeforeMount,
    watch,
    ref,
    computed,
} from "vue";
import { commonService } from "@/api/commonService";
import { useRoute } from "vue-router";
import { bus } from "@/utils/tool";
import { useStore } from "vuex";
const store = useStore();
const route = useRoute();
// 加载路由菜单派发actions
const dispatchGetMenu = async () => await store.dispatch("user/getUserCode");
//加载登入信息
const getInfo = async () => await store.dispatch("user/getInfo");
const getUserCode = async () => await store.dispatch("user/getUserCode");
const getApplicationAuth = async () => await store.dispatch("user/getApplicationAuth");
dispatchGetMenu();
getInfo();
getUserCode();
getApplicationAuth();
// 读取单条消息
const readSingleMessage = () => {
    if (route.query.id && route.query.origin === "unread") {
        const id = route.query.id;
        commonService.readMessage(id).then((res: any) => {
            if (res.success) {
                bus.emit("read");
            }
        });
    }
};
watch(
    () => route.query,
    (newVal: any, oldVal: any) => {
        if (newVal.id !== oldVal.id) {
            readSingleMessage();
        }
    },{deep:true}
);
readSingleMessage();
const title = computed(() => {
    const path: string = window.window.document.location.href;
    if (path.indexOf("/traffic") !== -1) {
        return "园区畅行";
    } else if (path.indexOf("/safe") !== -1) {
        return "安全守护";
    } else {
        return "生态宜居";
    }
});
</script>
<template>
    <BreadcrumbCustom>
        <BreadcrumbItem>{{ title }}</BreadcrumbItem>
        <BreadcrumbItem to="/common/messages">消息通知</BreadcrumbItem>
        <BreadcrumbItem>详情</BreadcrumbItem>
    </BreadcrumbCustom>
    <detailCard title="消息详情" style="height: 100vh" :isBackBtn="true" @on-back="$router.back()">
        <div class="container">
            <div class="no-data-img">
                <img src="@/assets/images/no_data.png" alt="" />
            </div>
            <span>敬请期待</span>
        </div>
    </detailCard>
</template>

<style lang="less" scoped>
.container {
    display: flex;
    flex-direction: column;
    row-gap: 20px;
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate3d(-50%, -50%, 0);

    align-items: center;
    .no-data-img {
        width: 80px;
        height: 80px;
        img {
            width: 100%;
            height: 100%;
        }
        span {
            color: #86909c;
        }
    }
}
</style>
