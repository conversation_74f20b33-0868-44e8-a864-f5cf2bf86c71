<template>
    <div class="open-iframe">
        <BreadcrumbCustom>
            <BreadcrumbItem v-for="item in openIframeObj.menuName">{{ item }}</BreadcrumbItem>
            <BreadcrumbItem>{{ openIframeObj.name }}</BreadcrumbItem>
        </BreadcrumbCustom>
        <iframe
            v-if="loginStatus === 1"
            ref="iframeR" :src="openIframeObj.url + `?token=${token}`"
            frameborder="0" class="iframe-main"></iframe>
        <div v-if="loginStatus === 2" class="msg-main">
            <div class="ts">智慧出行模块加载出现问题，请联系管理员。</div>
            <Button @click="reload" type="primary">重新加载</Button>
        </div>
    </div>
</template>

<script>
import { mapGetters } from 'vuex'
import useDebounce from "@/hooks/useDebounce";
export default {
    name: 'openIframe',
    data() {
        return {
            loginStatus: 1
        }
    },
    computed: {
        ...mapGetters(['openIframeObj', 'token'])
    },
    watch: {
        openIframeObj: {
            handler() {
                if (this.openIframeObj.url) {
                    this.loginStatus = 1
                } else {
                    this.loginStatus = 2
                }
            },
            deep: true
        }
    },
    created() {
        window.addEventListener('message', (e) => {
            if (e.data && e.data.code == 401) {
                this.determineLoginStatus()
            }
        })
    },
    mounted() {
        console.log(this.openIframeObj)
        if (!this.openIframeObj.url) {
            this.$router.push('/');
        }
    },
    methods: {
        reload() {
            this.loginStatus = 1
        },
        // 判断登录状态
        determineLoginStatus() {
            useDebounce(() => {
                this.$store.dispatch('user/getInfo').then((res) => {
                    if (res.success) {
                        this.loginStatus = 2
                    }
                })
            }, 200)

        },
        openLogin() {
            this.$store.dispatch('user/logout').then(() => {
                this.$router.push('/login');
            })
        }
    }
}
</script>

<style lang="less" scoped>

.iframe-main{
    width: 100%;
    height: ~"calc(100vh - 48px - 30px )";
    background: #fff;
}
.msg-main{
    width: 100%;
    height: ~"calc(100vh - 48px - 30px )";
    padding: 50px;
    background: #fff;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding-bottom: 80px;
    .ts{
        margin-bottom: 16px;
    }
}
</style>
