<template>
    <div>
        <BreadcrumbCustom>
            <BreadcrumbItem>操作日志</BreadcrumbItem>
        </BreadcrumbCustom>
        <ContentCard title="操作日志">
            <BaseForm :model="searchObj" :label-width="90" @handle-submit="search">
                <template #formitem>
                    <FormItem label="时间区间" prop="time">
                        <DatePicker v-model="searchObj.time" @on-change="changeTime" type="datetimerange" clearable style="width: 100%" placeholder="请选择"  :editable="false" />
                    </FormItem>
                    <FormItem label="操作人" prop="creatorId">
                        <Select v-model="searchObj.creatorId" clearable>
                            <Option v-for="(item, index) in m.userList" :value="item.id" :key="index">{{ item.nickname || item.username }}</Option>
                        </Select>
                    </FormItem>
                    <FormItem label="操作菜单" prop="operatePrivilegeCode">
                        <TreeSelect v-model="searchObj.operatePrivilegeCode" :data="m.treeList" clearable />
                    </FormItem>
                    <FormItem label="操作描述" prop="memo">
                        <Input v-model="searchObj.memo" clearable placeholder="请输入"></Input>
                    </FormItem>
                </template>
            </BaseForm>

            <div class="action-box">
<!--                <Button>导出</Button>-->
            </div>

            <base-table ref="mainTb" :columns="columns" url="/businessLog/getDtoPage"
            >
<!--                <template #actionType="{ row }">-->
<!--                    <span v-if="row.actionType == 1">新增</span>-->
<!--                    <span v-else-if="row.actionType == 2">编辑</span>-->
<!--                    <span v-else-if="row.actionType == 3">删除</span>-->
<!--                    <span v-else-if="row.actionType == 4">关联设备</span>-->
<!--                </template>-->


            </base-table>
        </ContentCard>
    </div>
</template>

<script>
import { loginService } from '@/api/loginService'
import { listToTree } from '@/utils/listToTree'
export default {
    name: 'index',
    components: {
    },
    data() {
        return {
            columns: [
                { title: '操作时间', key: 'createTime', tooltip: true, width: 160 },
                { title: '操作人', key: 'creatorName', tooltip: true, maxWidth: 160 },
                { title: '操作菜单', key: 'operatePrivilegeName', tooltip: true },
                { title: '操作动作', key: 'actionTypeName', width: 140 },
                { title: '操作描述', key: 'memo', tooltip: true }
            ],
            searchObj: {
                applicationId: this.$store.getters.applicationId,
                time: [],
                startTime: '',
                endTime: '',
                creatorId: '',
                operatePrivilegeCode: '', // 操作菜单
                memo: ''
            },
            m: {
                userList: [],
                treeList: [],
                treeCode: {}
            }
        }
    },
    mounted() {
        this.changeTime(this.$Util.getDateRangeS(new Date(), 'h', 'YYYY-MM-DD HH:mm:ss', false))
        this.search()
        this.getUserList()
        this.getTreeData()
    },
    methods: {
        search() {
            let param = this.$Util.objClone(this.searchObj)
            delete param.time
            this.$refs.mainTb.search(param)
        },
        changeTime(val) {
            if (val && val[0]) {
                let sArr = val[0].split(' ')
                if (!sArr[1]) { // 如果为空，加上时分秒
                    val[0] = sArr[0] + ' 00:00:00'
                }
                let eArr = val[1].split(' ')
                if (!eArr[1] || (eArr[1] === '00:00:00')) {
                    // 如果为空或者通过日期修改时，时分秒改为' 23:59:59'
                    val[1] = eArr[0] + ' 23:59:59'
                }
            }
            this.searchObj.time = val
            this.searchObj.startTime = val[0]
            this.searchObj.endTime = val[1]
        },
        changeTree() {},
        getOperName(row) {
            let result = []
            let oper = row.operatePrivilegeCode.split(':')
            let len = oper.length
            for (let i = 1; i <= len; i++) {
                let name = this.m.treeCode[oper.slice(0, i).join(':')]
                if (name) {
                    result.push(name)
                }
            }
            return result.join('-')
        },
        getUserList() {
            let param = {}
            this.$request('/businessLog/selectUserList', param, 'post').then(res => {
                this.m.userList = res.data || []
            })
        },
        getTreeData() {
            loginService.getUserCode('&dependUser=1').then(res => {
                this.m.treeList = this.transformData(res.data)
            })
        },
        // 转换数据格式
        transformData(data) {
            const result = [];
            const treeCode = {}
            data.forEach((item) => {
                if (item.type == 1) {
                    const newItem = this.$Util.objClone(item)
                    newItem.value = item.code
                    newItem.title = item.name
                    newItem.expand = true
                    newItem.selected = false
                    newItem.checked = false
                    result.push(newItem);
                    treeCode[item.code] = item.name
                }
            })
            this.m.treeCode = treeCode
            return listToTree(result);
        }
    }
}
</script>

<style lang="less" scoped>
.action-box{
    .ivu-btn{
        margin-bottom: 16px;
    }
}
</style>
