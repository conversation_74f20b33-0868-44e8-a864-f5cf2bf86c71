<template>
    <div>
        <BreadcrumbCustom>
            <BreadcrumbItem>能源能耗</BreadcrumbItem>
            <BreadcrumbItem>能源流向分析</BreadcrumbItem>
        </BreadcrumbCustom>
        <ContentCard title="能源流向分析">
            <div class="search-box">
                <div class="time-box">
                    <sDatePicker v-model="searchObj.time" type="daterange" :confirm="false" @on-change="changeDate" style="width: 400px" />
                </div>
                <s-tab :tab-list="tabList" justify="start" @handleChange="handleChangeTab">
                    <template #electricity>
                    </template>
                </s-tab>
            </div>
            <EchartItem v-if="hasData" :option="options" class="echart-box" />
            <no-data v-else></no-data>
        </ContentCard>
    </div>
</template>

<script setup lang="ts">
import {onMounted, ref} from "vue";
import EchartItem from '@/components/common/EchartItem/index.tsx'
import { tooltipExtraCssText, EchartsTooltip } from '@/utils/tool';
import moment from "moment";
import {deepClone} from 'wei-util'
import {
    energyConsumptionService,
} from '@/api/safeManage/energyConsumptionService'
import { areaTree } from '@/api/manholeCoverService'
import { treeToList } from '@/utils/listToTree'
const firstDayOfMonth = moment().startOf("month").format("YYYY-MM-DD HH:mm:ss");
const today = moment().endOf("day").format("YYYY-MM-DD HH:mm:ss");
const tabList = ref([
    {
        name: '电',
        key: 'electricity',
        icon: 'dian',
        value: 1
    },
    {
        name: '水',
        key: 'water',
        icon: 'shui',
        value: 2
    },
    {
        name: '气',
        key: 'fire',
        icon: 'huo',
        value: 3
    },
])
const searchObj = ref<any>({
    time: [firstDayOfMonth, today],
    type: 1
})
const hasData = ref(false)
const options = ref<any>({
    tooltip: {
        trigger: 'item',
        triggerOn: 'mousemove',
        extraCssText: tooltipExtraCssText,
        borderWidth: 0,
        formatter: (arg) => {
            return EchartsTooltip(arg, '辆')
        }
    },
    grid: {
        left: 10,
        right: 10,
        bottom: 10,
        top: 10,
        containLabel: true,
    },
    series: [
        {
            type: 'sankey',
            data: [],
            links: [],
            name: '能源流向分析',
            emphasis: {
                focus: 'adjacency'
            },
            levels: [],
            lineStyle: {
                curveness: 0.5
            }
        }
    ]
})
// 存放选中的节点
const selectedNode = ref<areaTree[]>([])
const getTree = async () => {
    const params = deepClone(searchObj.value)
    params.startTime = params.time[0]
    params.endTime = params.time[1]
    delete params.time
    const res = await energyConsumptionService.getEnergyFlowAnalysis(params)
    const { success, data }: { success: boolean; data: any } =
        res as unknown as HttpResponse<areaTree[]>
    if (success) {
        hasData.value = data?.links.some(k => +k.value > 0)
        // let maxLevel = 0
        // const defaultData = [
        //     { children: data, name: '总能耗', id: 0, expand: true, level: 0 },
        // ]
        // console.log(defaultData)
        // let list = treeToList(defaultData)
        // let obj: any = {
        //     data: [],
        //     links: []
        // }
        // obj.data = list.map(k => ({name: k.name}))
        // list.forEach(item => {
        //     if (item.level > maxLevel) {
        //         maxLevel = item.level
        //     }
        //     if (item.children) {
        //         item.children.forEach(k => {
        //             let num = Math.floor(Math.random() * 100)
        //             obj.links.push({
        //                 source: item.name,
        //                 target: k.name,
        //                 value: num
        //             })
        //         })
        //     }
        // })
        // console.log(list)
        // console.log(obj)
        initLevels(energyFlowAnalysis.length - 1)
        options.value.series[0].data = data.data
        options.value.series[0].links = data.links
    }
}


// 初始化层级样式
const energyFlowAnalysis = ['#C1B8F7', '#FF7977', '#FAB86B', '#25FDC2', '#5B75FF', '#0EF5F4', '#FF6E1C', '#4DB644', '#FA6BEC', '#249BCE']
const initLevels = (maxLevel) => {
    let levels: any = []
    for (let i = 0; i <= maxLevel; i++) {
        let color = energyFlowAnalysis[i % energyFlowAnalysis.length]
        levels.push({
            depth: i,
            itemStyle: {
                color
            },
            lineStyle: {
                color: 'source',
                opacity: 0.6
            }
        })
    }
    options.value.series[0].levels = levels
}

const handleChangeTab = (index, item) => {
    searchObj.value.type = item.value
    getTree()
}
const changeDate = (val) => {
    getTree()
}
onMounted(() => {
    getTree()
})
</script>

<style lang="less" scoped>
.search-box{
    .time-box{
        margin-bottom: 8px;
    }
}
.echart-box{
    height: 550px;
}
</style>
