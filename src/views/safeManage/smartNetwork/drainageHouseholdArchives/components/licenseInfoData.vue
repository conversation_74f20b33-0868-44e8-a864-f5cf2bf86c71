<template>
    <Modal v-model="showFlag" :title="title" width="625px"  transfer @on-visible-change="resetStatus"
        :mask-closable="false" :footer-hide="!multiple">
        <div class="title-card" >
            <slot name="title">
                <div class="title-icon-img" >
                    <img :src="require('@/assets/images/icon_detail.png')" alt="" style="width: 100%;height: 100%;">
                </div>
                <Title :level="5">许可证信息详情 </Title>
            </slot>
        </div>
            <Row :gutter="80">
                <Col span="12">
                    <s-label label="编号" :value="data.licenceNo" />
                </Col>
                <Col span="12">
                    <s-label label="排水许可量" :value="data.drainageAllowance || '--'" />
                </Col>
                <Col span="12">
                    <s-label label="所属企业" :value="data.enterprise || '--'" />
                </Col>
                <Col span="12">
                    <!--需要licenseStartTime和licenseEndTime-->
                    <s-label label="有效期" :value="data.licenseStartTime + ' -- ' + data.licenseEndTime" />
                </Col>
                <Col span="12">
                    <s-label label="发证日期" :value="data.licenseIssuanceTime" />
                </Col>
                <Col span="12">
                    <s-label label="所属区域" :value="data.area" />
                </Col>
                <Col span="12">
                    <s-label label="分类" :value="data.drainageHouseholdArchivesTypeName" />
                </Col>
                <Col span="12">
                    <s-label label="是否为重点排污单位" :value="data.drainageHouseholdArchivesLevelName" />
                </Col>
                <Col span="24">
                    <div style="display:flex">
                        <div class="title">许可证文件:</div>
                        <ImageUpload v-model="data.licenseUrl" :disabled="true" v-if="data.licenseUrl"/>
                        <span v-else>--</span>
                    </div>
                </Col>
            </Row>

    </Modal>

</template>

<script>
import { useStore } from "vuex";
const $store = useStore();
export default {
    name: 'editBox',
    data() {
        return {
            showFlag:false,
            data:{}
        }
    },
    components:{},
    methods: {
        open(data){
            this.data=data;
            this.showFlag = true
        }
    },
    props:{
        title:{default:'许可证信息详情'}
    }
}
</script>

<style lang="less" scoped>
.title-card{
    display: flex;
    align-items: center;
    margin-bottom: 16px;
    padding-bottom: 8px;
    .title-icon{
        background: #6AA1FF;
        border-radius: 2px;
        width: 24px;
        height: 24px;
        margin-right: 8px;
        color: #fff;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    .title-icon-img{
        width: 24px;
        height: 24px;
        margin-right: 8px;
    }
    h5{
        margin-bottom: 0;
    }
    &.no-title{
        margin-bottom: 0;
        padding-bottom: 0;
    }
}
.th-box {
    display: grid;
    grid-template-columns: 10% 20% 20% 20% 20% 10%;
    background: #F3F7FB;
    border-bottom: 1px solid #E5E6EB;
    height: 40px;
    align-items: center;
    column-gap: 16px;
}
.form-box {

    .form-box-item {
        display: grid;
        grid-template-columns: 10% 20% 20% 20% 20% 10%;
        height: 40px;
        border-bottom: 1px solid #E5E6EB;
        align-items: center;
        column-gap: 16px;

    }
}

.th-box2 {
    display: grid;
    grid-template-columns: 40% 40% 20%;
    background: #F3F7FB;
    border-bottom: 1px solid #E5E6EB;
    height: 40px;
    align-items: center;
    column-gap: 16px;
}
.form-box2 {

    .form-box-item2 {
        display: grid;
        grid-template-columns: 40% 40% 20%;
        height: 40px;
        border-bottom: 1px solid #E5E6EB;
        align-items: center;
        column-gap: 16px;

    }
}
.title {
    color: #798799;
    font-size: 14px;
    //min-width: 75px;
    padding-right: 8px;
}
</style>
