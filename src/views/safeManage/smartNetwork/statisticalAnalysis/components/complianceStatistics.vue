<template>
    <div>
        <div class="head-box">
            <searchBox :tabIndex="tabIndex" :deviceList="deviceList" @on-change="search" />
        </div>
        <div class="echart-container">
            <EchartItem @init-echart="listenersEvent" :option="options" style="height: 400px" />
            <PipeMap :objId="m.objId" :deviceCode="m.deviceCode"></PipeMap>
        </div>
        <div class="tab-main">
            <s-tab :tab-list="m.tabList" @handleChange="handleTabChange" justify="start">
                <template #collect v-if="m.tabIndex === 0">
                    <div class="top-box">
                        <Button @click="exportFun" type="primary" class="export-btn"><i style="font-size: 11px"
                                class="iconfont icon-Vector"></i>导出</Button>
                    </div>
                    <template v-if="tabIndex === 0">
                        <base-table rowKey="timeStr" :showPage="false" emptyBlockStr="--" :columns="m.allColumns"
                            :data="tableData">
                            <template #valueType="{ row }">
                                {{ valueType[getValue(row, 'valueType', 0)] }}
                            </template>
                            <template #result="{ row }">
                                {{ result[getValue(row, 'result', 0)] }}
                            </template>
                        </base-table>
                    </template>
                    <template v-else>
                        <base-table :openSuffixCheck="true" rowKey="timeStr" :showPage="false" emptyBlockStr="--"
                            :columns="collectColumns" :data="tableData">
                            <template v-for="slotName in monitorSlotMap" :key="slotName" #[slotName]="{ row }">
                                <TooltipAutoShow :content="handlerMonitorCollect(slotName, row)" />
                            </template>
                        </base-table>
                    </template> 
                </template>
                <template #detail v-else>
                    <div class="top-box">
                        <Button @click="exportFun" type="primary" class="export-btn"><i style="font-size: 11px"
                                class="iconfont icon-Vector"></i>导出</Button>
                    </div>
                    <template v-if="tabIndex === 0">
                        <base-table rowKey="timeStr" emptyBlockStr="--" :columns="m.detailAllColumns" :data="tableData">
                            <template #result="{ row }">
                                {{ result[getValue(row, 'result', 0)] }}
                            </template>
                        </base-table>
                    </template>
                    <template v-else>
                        <base-table :openSuffixCheck="true" rowKey="timeStr" emptyBlockStr="--" :columns="detailColumns"
                            :data="tableData">
                            <template v-for="slotName in monitorDetailSlotMap" :key="slotName" #[slotName]="{row}">
                                <TooltipAutoShow :content="handlerMonitorCollect(slotName, row)" />
                            </template>
                        </base-table>
                    </template>
                </template>
            </s-tab>
        </div>
        <exportModal ref="exportR" />
    </div>
</template>

<script>
import searchBox from './searchBox'
import EchartItem from "@/components/common/EchartItem";
import exportModal from '@/components/common/modal/exportModal'
import PipeMap from './PipeMap.vue';
import { getValue, findValue, deleteItem, deepClone, findFilter } from 'wei-util'
import { getPipelineStatisticalKxDataAnalysis, getList, getDetailList } from '@/api/safeManage/pipelineAnalysis'
import {
    tableColumnsMap,
    monitorSlotMap,
    optionMap,
    tabListMap,
    valueType,
    result,
    monitorDetailSlotMap,
    monitorNameMap,
    precipitationNameMap
} from './data'
const publicColumns = [
    { title: '名称', key: 'name', fixed: 'left', width: 100, tooltip: true },
    { title: '类型', slot: 'valueType', tooltip: true },
    { title: '运行结果', slot: 'result', minWidth: 110, tooltip: true },
    { title: '告警率', key: 'alarmRateStr', minWidth: 70, tooltip: true },
    { title: '监测设备数', key: 'deviceNum', minWidth: 100, tooltip: true },
    { title: '期初告警数', key: 'startAlarmNum', minWidth: 110, tooltip: true },
    { title: '期末告警数', key: 'endAlarmNum', minWidth: 100, tooltip: true },
    { title: '告警数变化值', key: 'changeAlarmNum', minWidth: 120, tooltip: true },
]
const allColumns = [
    { title: '告警树最大值设备', key: 'maxDeviceName', minWidth: 140, tooltip: true },
    { title: '告警最大值', key: 'maxAlarmNum', minWidth: 110, tooltip: true },
    { title: '告警数最大值设备管道', key: 'maxPipelineName', minWidth: 160, tooltip: true },
]
const detailPublicColumns = [
    { title: '日期', key: 'timeStr', fixed: 'left', width: 100, tooltip: true },
    { title: '运行结果', slot: 'result', minWidth: 110, tooltip: true },
    { title: '检测设备数', key: 'deviceNum', minWidth: 100, tooltip: true },
    { title: '告警率', key: 'alarmRateStr', minWidth: 110, tooltip: true },
    { title: '环比', key: 'comRatioStr', minWidth: 100, tooltip: true },
]
const detailAllColumns = [
    { title: '告警数最大设备', key: 'maxDeviceName', minWidth: 120, tooltip: true },
    { title: '设备最大告警数', key: 'maxAlarmNum', minWidth: 130, tooltip: true },
    { title: '告警数最大值监测点管道', key: 'maxPipelineName', minWidth: 180, tooltip: true },
]
export default {
    name: 'complianceStatistics',
    components: {
        searchBox,
        EchartItem,
        exportModal,
        PipeMap
    },
    props: {
        tabIndex: { type: Number, default: 0 },
        deviceList: { type: Array, default() { return [] } }
    },
    data() {
        return {
            tableTitle: ['全部/管道显示表字段', '设备显示表字段'],
            getValue,
            monitorSlotMap,
            monitorDetailSlotMap,
            result,
            options: optionMap[this.tabIndex],
            searchObj: {
                "valueType": null,       //类别(1管道 2设备)
                "label": "",    //节点显示名
                "dateType": 2,                       //查询类别（2日3月4年）
                "endTime": "",    //结束时间
                "startTime": "",  //开始时间
                "value": "",        //节点值
            },
            valueType,
            identifiers: [],
            tableData: [],
            m: {
                tabList: tabListMap[this.tabIndex],
                tabIndex: 0,
                timeList: ['', '', '天', '月', '年'],
                allColumns: [
                    ...publicColumns,
                    ...allColumns
                ],
                detailAllColumns: [
                    ...detailPublicColumns,
                    ...detailAllColumns
                ],
                stand: {
                    d1: [],
                    d2: []
                },
                countData: {},
                energyDate: {}
            },
            detailColumns: this.tabIndex ? tableColumnsMap[this.tabIndex].detail : [],
            collectColumns: this.tabIndex ? tableColumnsMap[this.tabIndex].collect : []
        }
    },
    watch: {
        tabIndex(newVal) {
            this.detailColumns = newVal ? tableColumnsMap[newVal].detail : []
            this.collectColumns = newVal ? tableColumnsMap[newVal].collect : []
        },
        // deviceList: {
        //     handler() {
        //         console.log('watch deviceList', deepClone(this.deviceList))
        //     },
        //     deep: true,
        //     immediate: true,
        // }
    },
    methods: {
        listenersEvent(myChart) {
            myChart.on("legendselectchanged", (params) => {
                const tabIndex = this.tabIndex;
                if (tabIndex === 1) {
                    const monitorMap = {}
                    Object.entries(monitorNameMap).forEach(item => {
                        monitorMap[item[1]] = item[0]
                    })
                    const identifiers = Object.entries(params.selected).map(item => {
                        if (item[1]) {
                            return monitorMap[item[0]]
                        }
                    }).filter(Boolean)
                    this.identifiers = identifiers
                    const collectColumns = [
                        { title: '名称', key: 'name', fixed: 'left', width: 100, tooltip: true },
                        { title: '类型', slot: 'valueType', minWidth: 140 }
                    ]
                    const slotRow = identifiers.map(key => {
                        return tableColumnsMap[tabIndex].collect.filter(item => {
                            if (item.hasOwnProperty('slot')) {
                                return ~item.slot.indexOf(key)
                            }
                        })
                    })
                    this.collectColumns = [...collectColumns, ...slotRow.flat()]
                }
            })
        },
        handlerMonitorCollect(slotName, row) {
            if (slotName === 'valueType') {
                return this.valueType[getValue(row, 'valueType', 0)]
            }
            const [slotNamePrefix, key] = slotName.split(':')
            return findValue(row.valueList, slotNamePrefix, 'identifier', key)
        },
        search(obj) {
            this.searchObj = obj
            const { dateType,valueType,value } = obj
            if(!value && !valueType){
                this.m.objId = ''
                this.m.deviceCode = ''
            }
            if(valueType === 1){
                this.m.objId = value
                this.m.deviceCode = ''
            }
            if(valueType === 2){
                this.m.deviceCode = value
                this.m.objId = ''
            }
            const dateTypeTitleMap = {
                2: {
                    avgNum: '平均日雨量',
                    avgRadio: '平均日雨量'
                },
                3: {
                    avgNum: '平均月雨量',
                    avgRadio: '平均月雨量'
                },
                4: {
                    avgNum: '平均年雨量',
                    avgRadio: '平均年雨量对比'
                },
            }
            if (this.m.tabIndex === 1 && this.tabIndex === 2) {
                findValue(this.detailColumns, 'avgNum', 'key').title = dateTypeTitleMap[dateType].avgNum
                findValue(this.detailColumns, 'avgRadio', 'key').title = dateTypeTitleMap[dateType].avgRadio
            }

            if (obj.valueType === 2) {
                this.m.allColumns = [...publicColumns]
                this.m.detailAllColumns = [...detailPublicColumns]
            } else {
                this.m.allColumns = [...publicColumns, ...allColumns]
                this.m.detailAllColumns = [...detailPublicColumns, ...detailAllColumns]
            }
            this.getKData()
            this.getTableData()
        },
        handleTabChange(index) {
            this.m.tabIndex = index
            this.getTableData()
        },
        async getTableData() {
            this.tableData = []
            const apiMap = {
                0: getList,
                1: getDetailList,
            }
            let { success, data } = await apiMap[this.m.tabIndex](this.searchObj, this.tabIndex + 1)
            if (success) this.m.tabIndex === 0 ? this.tableData = [data] : this.tableData = getValue(data, 'records')
        },
        exportFun() {
            const urlMap = {
                0: `/pipelineStatistical/tableDataSummary/export/${this.tabIndex + 1}`,
                1: `/pipelineStatistical/tableData/export/${this.tabIndex + 1}`
            }
            const params = deepClone(this.searchObj)
            if (this.tabIndex === 1 && this.m.tabIndex === 0) {
                params.identifiers = this.identifiers
            }
            this.$refs.exportR.export(urlMap[this.m.tabIndex], params)
        },
        loadDone(data, list) {
            this.m.countData = data.countData
            this.m.energyDate = data.energyDate
            return list
        },
        async getKData() {
            const type = this.tabIndex + 1
            let { success, data } = await getPipelineStatisticalKxDataAnalysis(this.searchObj, type)
            if (success) {
                this.options.xAxis[0].data = (data || []).map(item => item.timeStr)
                switch (this.tabIndex) {
                    case 0:
                        this.options.series[0].data = (data || []).map(item => ({
                            value: item.num || 0,
                            name: item.content || '',
                            result: item.result
                        }))
                        break
                    case 1:
                        this.options.series = Object.keys(monitorNameMap).map(item => {
                            return {
                                name: monitorNameMap[item],
                                type: "line",
                                smooth: false,
                                data: data.map(v => findValue(getValue(v, 'valueList'), item, 'identifier', 'avgValue')),
                            }
                        })
                        break
                    case 2:
                        const { dateType } = this.searchObj
                        const getSeries = (name, key) => {
                            switch (dateType) {
                                case 2:
                                    return {
                                        name,
                                        type: "line",
                                        smooth: false,
                                        data: (data || []).map(item => ({
                                            content: [
                                                { key: 'resultStr', name: '降雨强度', value: item.resultStr },
                                                { key: 'num', name: '日降雨量', value: item.num, unit: 'mm' },
                                                { key: 'liquidValue', name: '平均水位', value: item.liquidValue, unit: 'mm' },
                                            ],
                                            value: item[key]
                                        })),
                                    }
                                case 3:
                                    return {
                                        name,
                                        type: "line",
                                        smooth: false,
                                        data: (data || []).map(item => ({
                                            content: [
                                                { key: 'resultStr', name: '降雨强度', value: item.resultStr },
                                                { key: 'num', name: '月降雨量', value: item.num, unit: 'mm' },
                                                { key: 'avgNum', name: '平均月雨量', value: item.avgNum, unit: 'mm' },
                                                { key: 'avgRadioStr', name: '对比', value: item.avgRadioStr },
                                                { key: 'liquidValue', name: '水位', value: item.liquidValue, unit: 'mm' },
                                            ],
                                            value: item[key]
                                        })),
                                    }
                                case 4:
                                    return {
                                        name,
                                        type: "line",
                                        smooth: false,
                                        data: (data || []).map(item => ({
                                            content: [
                                                { key: 'resultStr', name: '降雨强度', value: item.resultStr },
                                                { key: 'num', name: '年降雨量', value: item.num, unit: 'mm' },
                                                { key: 'avgNum', name: '平均年雨量', value: item.avgNum, unit: 'mm' },
                                                { key: 'avgRadioStr', name: '对比', value: item.avgRadioStr },
                                                { key: 'liquidValue', name: '水位', value: item.liquidValue, unit: 'mm' },
                                            ],
                                            value: item[key]
                                        })),
                                    }

                            }
                        }
                        this.options.series = precipitationNameMap.map(item => getSeries(item.name, item.key))
                        break
                }

            }
        },

    }
}
</script>

<style lang="less" scoped>
.head-box {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 8px;
}
.echart-container{
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;
}
.stand {
    color: rgba(0, 180, 42, 1);
}

.not-stand {
    color: rgba(255, 154, 46, 1);
}

.tab-main {
    position: relative;

    .h4-title {
        margin-bottom: 16px;
    }

    .top-box {
        display: flex;
        align-items: center;
        justify-content: space-between;
        position: relative;

        .export-btn {
            position: absolute;
            right: 0;
            top: -47px;
        }
    }




}
</style>
