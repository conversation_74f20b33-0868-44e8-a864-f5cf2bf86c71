
.left-plate {
    .device-box {
        flex: 1;
        position: relative;
        height: ~'calc(45% - 8px)';
        .close-device {
            display: flex;
            justify-content: flex-end;
            height: 20px;
            position: absolute;
            top: -10px;
            right: -10px;

            /deep/.ivu-icon-md-close{
                cursor: pointer;
                width: 20px;
                line-height: 20px;
                background: #fff;
                filter: drop-shadow(0px 1px 6px rgba(61, 103, 175, 0.2));
                border-radius:100%;
            }
        }
        &:hover {
            .device-cont {
                display: flex!important;
            }
        }
        .device-cont {
            height: 100%;
            height: 100%;
            position: absolute;
            left: 224px;
            width: 224px;
            top: 0px;
            overflow: visible!important;
            padding-top: 16px!important;
            display: none!important;
            .name {
                flex: 1;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
            }
        }
        .device-type-list {
            height: 100%;
            width: 220px;
            padding: 8px 8px 0 8px;
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
            .device-type-item {
                height: 36px;
                line-height: 36px;
                color: #4E627E;
                font-weight: 400;
                font-size: 14px;
                display: flex;
                justify-content: space-between;
                padding: 0 12px 0 6px;
                // margin-top: 12px;
                cursor: pointer;
                &.actived {
                    background: #F5F7FA;
                    color: @primary-color;
                }
                &:hover {
                    background: #F5F7FA;
                }
            }
        }
    }
}
/deep/.right-plate {
    width: 380px!important;
    .details-container .name {
        padding-top: 24px!important;
    }
    .monitor-box {
        background: #F8FAFB;
        min-height: 96px;
        padding: 16px;
        margin-bottom:12px;
        .device-box {
            color: #1E2A55!important;
            font-weight: bold!important;
            display: flex;
            overflow: hidden;
            padding-top: 14px;
            line-height: 22px;
            .label {
                color: #4E627E;
                font-weight: normal;
                width: 70px;
            }
            &>div{
                flex:1;
                overflow: hidden;
            }
        }
        .num-info {
            display: flex;
            align-items: center;
            position: relative;
            &:after {
                content: " ";
                height:48px;
                width: 1px;
                background:#E0E6F1;
                left: ~'calc(50% - 8px)';
                top: 9px;
                position: absolute;
            }
            .num-box {
                display: flex;
                height: 28px;
                align-items: center;
            }
            &>div {
                flex:1;
                overflow: hidden;
                span {
                    color: #4E627E;
                }
                .num {
                    color: #1E2A55;
                    font-weight: bold;
                    font-size: 16px;
                    margin-right: 2px;
                    margin-left: 4px;
                    // margin-bottom: 4px;
                }
            }
            .icon-box {
                align-items: baseline;
                display: flex;
                img {
                    display: block;
                    height: 18px;
                    margin-right: 4px
                }
            }
        }

    }
    .right-tab-title {
        .tab-item {
            padding:0 8px;
        }

    }
    .rank-list {
        width: 100%;
        overflow: hidden;
        height: auto!important;
    }
    .alarm-list {
        .alarm-item {
            grid-template-columns: 50% 20% 30%!important;
            span {
                &:last-child {
                    text-align: right;
                }
            }
        }
    }
    .attr-box {
        min-height:68px;
        background: #F8FAFB;
        margin: 0 16px;
        padding: 14px 12px 0;
    }

}
/deep/.bridge-total-num {
    display: flex;
    padding: 0px 8px 8px 0px;
    align-items: center;
    img {
        display: block;
        width: 42px;
        height: 42px;
        margin-right: 20px;

    }
    .bridge-num {
        color: @text-color;
        font-weight: 400;
        font-size: 16px;
        line-height: 24px;

        .num {
            color: @title-color;
            font-weight: 700;
            font-size: 24px;
            line-height: 32px;
        }
    }
}
/deep/.head-cont {
    &.online {
        .icon-img {
            // background: #FFFFFF url('./images/icon_manholecover_009.png') no-repeat center;
            background-size: 40px 40px;
        }
    }
    .icon-img {
        height: 50px;
        width: 50px;
        // background: #FFFFFF url('./images/icon_manholecover_001.png') no-repeat center;
        background-size: 40px 40px;
        border-radius: 100%;
        position: absolute;
        left: 50%;
        transform: translate(-50%, 35px);
        .top-icon {
            height: 18px!important;
            width: 18px!important;
        }
    }
}
