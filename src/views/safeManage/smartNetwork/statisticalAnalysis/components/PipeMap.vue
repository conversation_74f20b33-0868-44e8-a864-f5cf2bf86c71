<template>
    <div class="wrapper">
        <div class="title">
            <span>管道拓扑图</span>
        </div>
        <div class="main container-map" id="container">
        </div>
    </div>
</template>

<script setup lang="ts">
import AMapLoader from '@amap/amap-jsapi-loader';
import { defineComponent, nextTick, onMounted, ref, Ref, watch, defineExpose } from 'vue';
import { getPipeLines, getMonitorDevicePage } from '@/api/safeManage/drainageHouseholdArchives'
import { getPipelineMonitorDeviceList, getPipelineMonitorDeviceDetail } from '@/api/safeManage/pipelineAnalysis'
import { getMarkerContent, renderClusterMarker, renderMarker, showInfoWindow } from '@/components/common/mapAreaTreeSelect/markercontent'
import { findValue } from 'wei-util';
import Util from '@/libs/util'

interface ComponentInfo {
    id: number;
    [key: string]: any;
}

interface pipeArchives extends ComponentInfo {
    pipeArchivesId: number;
    points: string;
}
interface pipeComponentInfo extends ComponentInfo {
    circuit?: string;
}

interface recordsResponse {
    records: any[];
}

const props = defineProps({
    objId: {
        type: String,
        default: ''
    },
    deviceCode: {
        type: String,
        default: ''
    }
})
// 定义变量
const map = ref<any>(null);
const Amap = ref<any>(null);
const pipeline = ref<any[]>([]);// 地图上所有的折线
const cluster = ref<any>(null);

const modelTypeList = ref<pipeComponentInfo[]>([]);

// *********************
// 地图
// *********************
// 初始化图标
const ininMap = () => {
    AMapLoader.load({
        key: 'ea53a5291f8f7c1f215ba20930ea9866',
        plugins: ['AMap.MarkerCluster'],
        version: '2.0'
    }).then((AMap) => {
        Amap.value = AMap
        map.value = new AMap.Map('container', {
            resizeEnable: true,
            zoom: 16,
            center: [114.61, 30.45],
        })
        // 创建起终图标

        nextTick(() => {
            getQueryDevices([], true)
        })
    })
}



// 查询当前区域的管道
const getQueryDevices = async (paths?: string[], initialization?: boolean) => {
    const params = {
        page: {
            current: 1,
            size: -1
        },
        customQueryParams: {
            objInfo: {
                objId: props.objId
            }
        }
    }
    const res = await getPipeLines(params)
    const { data, success }: { data: recordsResponse, success: boolean } = res as unknown as HttpResponse<recordsResponse>
    if (success) {
        modelTypeList.value = data.records as pipeComponentInfo[];
        updateMapPolyline(modelTypeList.value)
    }
}
const deviceList = ref<any[]>([])
const getPipelineMonitorDeviceListByDeviceCode = async () => {
    const res = await getPipelineMonitorDeviceList({
        deviceCode: props.deviceCode,
        monitorPipeId: props.objId
    })
    const { data, success }: { data: recordsResponse, success: boolean } = res as unknown as HttpResponse<recordsResponse>
    if (success) {
        deviceList.value = data.records
        updateMapDevicePoint()
    }
}
// 清理对应地图对象
const clearMapObj = (arr: any[]) => {
    arr.forEach(e => {
        e.setMap(null)
    })
}

// 清除设备标记点
const clearDeviceMarkers = () => {
    if (cluster.value) {
        cluster.value.setMap(null)
        cluster.value = null
    }
}

const iconImgs = {
    '18': './images/icon_environmental_009.png',// 湖渠监测
}
const showDevicePropInfoWindow = (devicePropsList: any[], position: any) => {
    const info: any = []
    info.push('<div class="input-card content-window-card content-window-card-height-auto" style="width: 250px;">')
    devicePropsList.forEach(ele => {
        info.push(`<div class="window-card-item flex-widow-card-item">`)
        info.push(`<div class="name">${ele.propName}</div>`)
        info.push(`<div class="value">${Util.formatDeviceValue(ele)}</div></div>`)
    })
    info.push('</div>')
    // 创建浮窗
    const deviceInfoWindow = new Amap.value.InfoWindow({
        isCustom: true,
        content: info.join(''),
        anchor: 'bottom-right', // 改为右下角，与设备列表的左下角区分
    });
    deviceInfoWindow.open(map.value, position);
}

const activedObjId = ref<string>('')
const _renderClusterMarker = async (context: any) => {
    // console.log('_renderClusterMarker')
    renderClusterMarker(context, activedObjId.value, (_data: string[][], imgUrl: any) => {
        const activeDevice = _data.find(k => k[0] == activedObjId.value)
        const _name = activeDevice?.[2]
        const _id = activeDevice?.[4] || _data[0][4]
        return getMarkerContent({ code: _name ? activedObjId.value : null }, activedObjId.value, 'hide-tag', () => {
            return _id ? require('./images/icon_environmental_009.png') : imgUrl
        }, 'deviceCode')
    })
}
// 渲染设备标记点
const _renderMarker = async (context: any) => {
    // console.log('_renderMarker')
    renderMarker(context, (id: number) => {
        const activeDevice = deviceList.value.find(k => k.deviceCode == id)
        return getMarkerContent(activeDevice, activedObjId.value, 'hide-tag', (d: any) => {
            return require('./images/icon_environmental_009.png')
        }, 'deviceCode');
    })
}
// 更新设备点
const updateMapDevicePoint = async () => {
    if (!map.value) return

    // 清除旧的集群
    if (cluster.value) {
        cluster.value.setMap(null)
        cluster.value = null
    }

    // 严格验证坐标数据，确保都是有效数字
    const points = deviceList.value.filter((k: any) => {
        const objX = parseFloat(k?.objX);
        const objY = parseFloat(k?.objY);
        return !isNaN(objX) && !isNaN(objY) && objX !== 0 && objY !== 0;
    }).map(k => ({ 
        lnglat: [parseFloat(k.objX), parseFloat(k.objY)], 
        exData: `${k.deviceCode}@${k.deviceCode || 0}@${k.sbmc}(${k?.deviceCode})@${k.unitCode}@${k.id}` 
    })) || [];
    
    // 如果没有有效的点位数据，直接返回
    if (points.length === 0) {
        console.warn('没有有效的设备坐标数据');
        return;
    }

    cluster.value = new Amap.value.MarkerCluster(map.value, points, {
        maxZoom: 24,
        gridSize: 30, // 设置网格像素大小
        renderClusterMarker: _renderClusterMarker, // 自定义聚合点样式
        renderMarker: _renderMarker, // 自定义非聚合点样式
    });
    nextTick(() => {
        map.value?.setFitView();
    })
    cluster.value.on('click', (e: any) => {
        const { clusterData } = e
        // 不要清除所有InfoWindow，允许多个同时存在
        // map.value.clearInfoWindow()
        if (clusterData.length == 1) {
            const _d = clusterData[0].exData.split('@')[0]
            const _id = clusterData[0].exData.split('@')[4]
            setActivedObjId(_d)
            updateMapDevicePoint()
            if (activedObjId.value) {
                getPipelineMonitorDeviceDetail(_id).then((res: any) => {
                    if (res.success) {
                        const { data } = res
                        console.log(data)
                        showDevicePropInfoWindow(data.devicePropertyStatusList, e.marker._position)
                    }
                })
            }
        } else {
            showInfoWindow(clusterData, e, map.value, activedObjId.value, (_d: string) => {
                // 不调用setActivedObjId，避免触发地图更新导致InfoWindow关闭
                // setActivedObjId(_d)
                // updateMapDevicePoint()

                // 直接更新激活状态，但不重新渲染地图
                activedObjId.value = _d

                // 获取设备详情并显示设备属性弹窗
                const _id = clusterData[0].exData.split('@')[4]
                if (activedObjId.value) {
                    getPipelineMonitorDeviceDetail(_id).then((res: any) => {
                        if (res.success) {
                            const { data } = res
                            // 使用不同的位置显示设备属性弹窗，避免与设备列表重叠
                            showDevicePropInfoWindow(data.devicePropertyStatusList, e.marker._position)
                        }
                    }).catch(err => {
                        console.error('获取设备详情失败:', err)
                    })
                }

            })
        }
    })
}
watch(() => activedObjId.value, (newVal) => {
    if (!newVal) {
        map.value?.clearInfoWindow()
    }
})
const setActivedObjId = (_d: string) => {
    if (activedObjId.value == _d) {
        activedObjId.value = ''
        return
    }
    activedObjId.value = _d
}
// 更新管道折现
const updateMapPolyline = async (data: pipeComponentInfo[]) => {
    if (!map.value) return
    await clearMapObj(pipeline.value)
    pipeline.value = []; // 清空数组

    data.forEach(ele => {
        if (!ele.circuit) return
        
        try {
            const lineArr = JSON.parse(ele.circuit)
            // 验证线路数组是否有效
            if (!Array.isArray(lineArr) || lineArr.length < 2) {
                console.warn('管道线路数据无效:', ele.circuit);
                return;
            }
            
            // 验证起始点和结束点坐标
            const startPoint = lineArr[0];
            const endPoint = lineArr[lineArr.length - 1];
            
            if (!Array.isArray(startPoint) || !Array.isArray(endPoint) || 
                startPoint.length < 2 || endPoint.length < 2) {
                console.warn('管道坐标点格式无效:', { startPoint, endPoint });
                return;
            }
            
            const start = new Amap.value.Marker({
                icon: new Amap.value.Icon({
                    size: new Amap.value.Size(30, 46),
                    image: require('./images/icon_electronicFence_006.png'),
                    imageSize: new Amap.value.Size(30, 46),
                }),
                offset: new Amap.value.Pixel(-15, -40),
                extData: 'start'
            });
            const end = new Amap.value.Marker({
                icon: new Amap.value.Icon({
                    size: new Amap.value.Size(30, 46),
                    image: require('./images/icon_electronicFence_007.png'),
                    imageSize: new Amap.value.Size(30, 46)
                }),
                offset: new Amap.value.Pixel(-15, -40),
                extData: 'end'
            })
            
            start.setPosition(startPoint)
            end.setPosition(endPoint)
            map.value.add([start, end])
            
            const _pipeline = new Amap.value.Polyline({
                map: map.value,
                path: lineArr,
                showDir: true,
                strokeColor: "#29CE4F",  //线颜色
                strokeOpacity: 1,     //线透明度
                strokeWeight: 6,      //线宽
                extData: ele.id,
                cursor: 'pointer',
                // strokeStyle: "solid"  //线样式
            });
            
            pipeline.value.push(_pipeline)
            pipeline.value.push(start)
            pipeline.value.push(end)
            
        } catch (error) {
            console.error('解析管道线路数据失败:', error, ele.circuit);
        }
    })
    
    nextTick(() => {
        if (pipeline.value.length > 0) {
            map.value?.setFitView();
        }
    })
}
watch(() => props.objId, () => {
    map.value?.clearInfoWindow()
    getQueryDevices()
    getPipelineMonitorDeviceListByDeviceCode()
}, { immediate: true })
watch(() => props.deviceCode, (newVal) => {
    map.value?.clearInfoWindow()
    activedObjId.value = newVal
    getPipelineMonitorDeviceListByDeviceCode()
})
// 暴露给父组件使用的方法
defineExpose({
    clearDeviceMarkers
})

onMounted(() => {
    ininMap();
})
</script>

<style scoped lang="less">
// @import '../../../../styles/mapPage.less';
@import './index.less';
@import '../../../../../styles/mapPage.less';

.wrapper {
    height: 100%;
    width: 100%;

    .title {
        display: flex;
        justify-content: space-between;
        margin-bottom: 16px;

        span {
            font-weight: 600;
            font-size: 16px;
            line-height: 24px;
            color: #1e2a55;
        }

        .right {
            font-size: 20px;
        }
    }
}

.main {
    width: 100% !important;
    height: ~"calc(100% - 40px)" !important;
    display: flex;
}

.marker-label-box {
    color: #fff;
    background: rgba(0, 0, 0, 0.5);
    padding: 2px 5px;
    border-radius: 2px;
    font-size: 12px;
    white-space: nowrap;
}
</style>
