---
title: 安全守卫
language_tabs:
  - shell: Shell
  - http: HTTP
  - javascript: JavaScript
  - ruby: Ruby
  - python: Python
  - php: PHP
  - java: Java
  - go: Go
toc_footers: []
includes: []
search: true
code_clipboard: true
highlight_theme: darkula
headingLevel: 2
generator: "@tarslib/widdershins v4.0.30"

---

# 安全守卫

Base URLs:

# Authentication

# 门禁管理

## POST 根据条件，分页(不分页)查询

POST /accessControl/list

> Body 请求参数

```json
{
  "page": {
    "records": [
      {
        "id": 0,
        "code": "string",
        "name": "string",
        "szjd": "string",
        "szsq": "string",
        "szdywg": "string",
        "areaPath": "string",
        "controlStatus": 0,
        "creatorId": 0,
        "createTime": "string",
        "modifyId": 0,
        "modifyTime": "string",
        "remark": "string",
        "deleted": 0,
        "ids": [
          0
        ],
        "codeKey": "string",
        "nameKey": "string"
      }
    ],
    "total": 0,
    "size": 0,
    "current": 0,
    "orders": [
      {
        "column": "string",
        "asc": true
      }
    ],
    "optimizeCountSql": true,
    "isSearchCount": true
  },
  "customQueryParams": {
    "id": 0,
    "code": "string",
    "name": "string",
    "szjd": "string",
    "szsq": "string",
    "szdywg": "string",
    "areaPath": "string",
    "controlStatus": 0,
    "creatorId": 0,
    "createTime": "string",
    "modifyId": 0,
    "modifyTime": "string",
    "remark": "string",
    "deleted": 0,
    "ids": [
      0
    ],
    "codeKey": "string",
    "nameKey": "string"
  },
  "sorts": [
    {
      "field": "string",
      "sortRule": "string"
    }
  ]
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|body|body|[RequestModelAccessControlVo](#schemarequestmodelaccesscontrolvo)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "success": false,
  "code": "",
  "level": "",
  "message": "",
  "data": {}
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RestMessage](#schemarestmessage)|

## POST 新增门禁

POST /accessControl

> Body 请求参数

```json
{
  "id": 0,
  "code": "string",
  "name": "string",
  "szjd": "string",
  "szsq": "string",
  "szdywg": "string",
  "areaPath": "string",
  "controlStatus": 0,
  "creatorId": 0,
  "createTime": "string",
  "modifyId": 0,
  "modifyTime": "string",
  "remark": "string",
  "deleted": 0,
  "ids": [
    0
  ]
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|body|body|[AccessControl](#schemaaccesscontrol)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "success": false,
  "code": "",
  "level": "",
  "message": "",
  "data": {}
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RestMessage](#schemarestmessage)|

## DELETE 删除门禁（包含批量删除）

DELETE /accessControl

> Body 请求参数

```json
[
  0
]
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|body|body|array[integer]| 否 |none|

> 返回示例

> 200 Response

```json
{
  "success": false,
  "code": "",
  "level": "",
  "message": "",
  "data": {}
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RestMessage](#schemarestmessage)|

## PUT 编辑门禁

PUT /accessControl

> Body 请求参数

```json
{
  "id": 0,
  "code": "string",
  "name": "string",
  "szjd": "string",
  "szsq": "string",
  "szdywg": "string",
  "areaPath": "string",
  "controlStatus": 0,
  "creatorId": 0,
  "createTime": "string",
  "modifyId": 0,
  "modifyTime": "string",
  "remark": "string",
  "deleted": 0,
  "ids": [
    0
  ]
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|body|body|[AccessControl](#schemaaccesscontrol)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "success": false,
  "code": "",
  "level": "",
  "message": "",
  "data": {}
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RestMessage](#schemarestmessage)|

## GET 根据id查询门禁详情

GET /accessControl/{id}

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|path|integer| 是 |none|

> 返回示例

> 200 Response

```json
{
  "success": false,
  "code": "",
  "level": "",
  "message": "",
  "data": {}
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RestMessage](#schemarestmessage)|

## POST 启用门禁

POST /accessControl/enable

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|query|integer| 是 |none|

> 返回示例

> 200 Response

```json
{
  "success": false,
  "code": "",
  "level": "",
  "message": "",
  "data": {}
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RestMessage](#schemarestmessage)|

## POST 禁用门禁

POST /accessControl/disable

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|query|integer| 是 |none|

> 返回示例

> 200 Response

```json
{
  "success": false,
  "code": "",
  "level": "",
  "message": "",
  "data": {}
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RestMessage](#schemarestmessage)|

# 数据模型

<h2 id="tocS_RestMessage">RestMessage</h2>

<a id="schemarestmessage"></a>
<a id="schema_RestMessage"></a>
<a id="tocSrestmessage"></a>
<a id="tocsrestmessage"></a>

```json
{
  "success": true,
  "code": "string",
  "level": "string",
  "message": "string",
  "data": {}
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|success|boolean|false|none||成功标识|
|code|string|false|none||状态码|
|level|string|false|none||消息级别|
|message|string|false|none||消息内容|
|data|object|false|none||数据|

<h2 id="tocS_"></h2>

<a id="schema"></a>
<a id="schema_"></a>
<a id="tocS"></a>
<a id="tocs"></a>

```json
{}

```

### 属性

*None*

<h2 id="tocS_RequestModelAccessControlVo">RequestModelAccessControlVo</h2>

<a id="schemarequestmodelaccesscontrolvo"></a>
<a id="schema_RequestModelAccessControlVo"></a>
<a id="tocSrequestmodelaccesscontrolvo"></a>
<a id="tocsrequestmodelaccesscontrolvo"></a>

```json
{
  "page": {
    "records": [
      {
        "id": 0,
        "code": "string",
        "name": "string",
        "szjd": "string",
        "szsq": "string",
        "szdywg": "string",
        "areaPath": "string",
        "controlStatus": 0,
        "creatorId": 0,
        "createTime": "string",
        "modifyId": 0,
        "modifyTime": "string",
        "remark": "string",
        "deleted": 0,
        "ids": [
          0
        ],
        "codeKey": "string",
        "nameKey": "string"
      }
    ],
    "total": 0,
    "size": 0,
    "current": 0,
    "orders": [
      {
        "column": "string",
        "asc": true
      }
    ],
    "optimizeCountSql": true,
    "isSearchCount": true
  },
  "customQueryParams": {
    "id": 0,
    "code": "string",
    "name": "string",
    "szjd": "string",
    "szsq": "string",
    "szdywg": "string",
    "areaPath": "string",
    "controlStatus": 0,
    "creatorId": 0,
    "createTime": "string",
    "modifyId": 0,
    "modifyTime": "string",
    "remark": "string",
    "deleted": 0,
    "ids": [
      0
    ],
    "codeKey": "string",
    "nameKey": "string"
  },
  "sorts": [
    {
      "field": "string",
      "sortRule": "string"
    }
  ]
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|page|[PageAccessControlVo](#schemapageaccesscontrolvo)|false|none||分页参数|
|customQueryParams|[AccessControlVo](#schemaaccesscontrolvo)|false|none||业务相关的查询参数|
|sorts|[[SortModel](#schemasortmodel)]|false|none||查询时排序参数|

<h2 id="tocS_SortModel">SortModel</h2>

<a id="schemasortmodel"></a>
<a id="schema_SortModel"></a>
<a id="tocSsortmodel"></a>
<a id="tocssortmodel"></a>

```json
{
  "field": "string",
  "sortRule": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|field|string|false|none||排序的字段|
|sortRule|string|false|none||排序规则 ASC / DESC|

<h2 id="tocS_AccessControlVo">AccessControlVo</h2>

<a id="schemaaccesscontrolvo"></a>
<a id="schema_AccessControlVo"></a>
<a id="tocSaccesscontrolvo"></a>
<a id="tocsaccesscontrolvo"></a>

```json
{
  "id": 0,
  "code": "string",
  "name": "string",
  "szjd": "string",
  "szsq": "string",
  "szdywg": "string",
  "areaPath": "string",
  "controlStatus": 0,
  "creatorId": 0,
  "createTime": "string",
  "modifyId": 0,
  "modifyTime": "string",
  "remark": "string",
  "deleted": 0,
  "ids": [
    0
  ],
  "codeKey": "string",
  "nameKey": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||主键|
|code|string|false|none||门禁编号|
|name|string|false|none||门禁名称|
|szjd|string|false|none||所在街道|
|szsq|string|false|none||所在社区|
|szdywg|string|false|none||所在单元网格|
|areaPath|string|false|none||区域全路径|
|controlStatus|integer|false|none||门禁状态(1启用,0禁用)|
|creatorId|integer(int64)|false|none||创建人id|
|createTime|string|false|none||创建日期|
|modifyId|integer(int64)|false|none||修改人id|
|modifyTime|string|false|none||修改时间|
|remark|string|false|none||备注|
|deleted|integer|false|none||是否删除，0未删除，1已删除|
|ids|[integer]|false|none||none|
|codeKey|string|false|none||门禁编码模糊查询|
|nameKey|string|false|none||门禁名称模糊查询|

<h2 id="tocS_PageAccessControlVo">PageAccessControlVo</h2>

<a id="schemapageaccesscontrolvo"></a>
<a id="schema_PageAccessControlVo"></a>
<a id="tocSpageaccesscontrolvo"></a>
<a id="tocspageaccesscontrolvo"></a>

```json
{
  "records": [
    {
      "id": 0,
      "code": "string",
      "name": "string",
      "szjd": "string",
      "szsq": "string",
      "szdywg": "string",
      "areaPath": "string",
      "controlStatus": 0,
      "creatorId": 0,
      "createTime": "string",
      "modifyId": 0,
      "modifyTime": "string",
      "remark": "string",
      "deleted": 0,
      "ids": [
        0
      ],
      "codeKey": "string",
      "nameKey": "string"
    }
  ],
  "total": 0,
  "size": 0,
  "current": 0,
  "orders": [
    {
      "column": "string",
      "asc": true
    }
  ],
  "optimizeCountSql": true,
  "isSearchCount": true
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|records|[[AccessControlVo](#schemaaccesscontrolvo)]|false|none||none|
|total|integer(int64)|false|none||none|
|size|integer(int64)|false|none||none|
|current|integer(int64)|false|none||none|
|orders|[[OrderItem](#schemaorderitem)]|false|none||none|
|optimizeCountSql|boolean|false|none||none|
|isSearchCount|boolean|false|none||none|

<h2 id="tocS_OrderItem">OrderItem</h2>

<a id="schemaorderitem"></a>
<a id="schema_OrderItem"></a>
<a id="tocSorderitem"></a>
<a id="tocsorderitem"></a>

```json
{
  "column": "string",
  "asc": true
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|column|string|false|none||none|
|asc|boolean|false|none||none|

<h2 id="tocS_AccessControl">AccessControl</h2>

<a id="schemaaccesscontrol"></a>
<a id="schema_AccessControl"></a>
<a id="tocSaccesscontrol"></a>
<a id="tocsaccesscontrol"></a>

```json
{
  "id": 0,
  "code": "string",
  "name": "string",
  "szjd": "string",
  "szsq": "string",
  "szdywg": "string",
  "areaPath": "string",
  "controlStatus": 0,
  "creatorId": 0,
  "createTime": "string",
  "modifyId": 0,
  "modifyTime": "string",
  "remark": "string",
  "deleted": 0,
  "ids": [
    0
  ]
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||主键|
|code|string|false|none||门禁编号|
|name|string|false|none||门禁名称|
|szjd|string|false|none||所在街道|
|szsq|string|false|none||所在社区|
|szdywg|string|false|none||所在单元网格|
|areaPath|string|false|none||区域全路径|
|controlStatus|integer|false|none||门禁状态(1启用,0禁用)|
|creatorId|integer(int64)|false|none||创建人id|
|createTime|string|false|none||创建日期|
|modifyId|integer(int64)|false|none||修改人id|
|modifyTime|string|false|none||修改时间|
|remark|string|false|none||备注|
|deleted|integer|false|none||是否删除，0未删除，1已删除|
|ids|[integer]|false|none||none|

