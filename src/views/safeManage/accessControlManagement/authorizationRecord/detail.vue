<template>
    <div>
        <BreadcrumbCustom>
            <BreadcrumbItem>门禁管理</BreadcrumbItem>
            <BreadcrumbItem to="/accessControl/authorizationRecord">门禁授权记录</BreadcrumbItem>
            <BreadcrumbItem>详情</BreadcrumbItem>
        </BreadcrumbCustom>
        <Form ref="addForm" :model="subObj" :rules="rules" label-position="top">
            <detailCard ref="detail" title="门禁授权记录信息" :src="require('@/assets/images/icon_detail.png')"
                :sub-disabled="m.uploadLoading" :is-back-btn="true"
                :is-edit-btn="false"
                :loading="m.loading" @on-submit="submitFun" @on-edit="editChange" @on-back="backPage">
                <template #content>
                    <infoShow v-if="!isEdit" :subObj="subObj" />
                    <editBox v-else :form="subObj" />
                </template>
            </detailCard>
        </Form>
    </div>
</template>

<script>
import editBox from './components/editBox.vue'
import infoShow from './components/infoShow.vue'
import { EditMix, DetailMix } from './components/edit-mixin.js'
import detailCard from '@/components/global/ContentCard/detailCard.vue'

export default {
    name: 'AuthorizationRecordDetail',
    components: {
        editBox,
        infoShow,
        detailCard
    },
    mixins: [EditMix, DetailMix],
    data() {
        return {
            isEdit: false
        }
    },
    mounted() {
        this.getDetailById(this.$route.query.id)
    },
    methods: {
        backPage() {
            this.$router.back()
        },
        editChange(val) {
            this.isEdit = val
        },
        submitFun() {
            this.submitAjax('put')
        }
    }
}
</script>

<style lang="less" scoped>

</style>
