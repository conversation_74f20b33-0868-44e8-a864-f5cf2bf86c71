---
title: 安全守卫
language_tabs:
  - shell: Shell
  - http: HTTP
  - javascript: JavaScript
  - ruby: Ruby
  - python: Python
  - php: PHP
  - java: Java
  - go: Go
toc_footers: []
includes: []
search: true
code_clipboard: true
highlight_theme: darkula
headingLevel: 2
generator: "@tarslib/widdershins v4.0.30"

---

# 安全守卫

Base URLs:

# Authentication

# 门禁授权管理

## POST 根据条件，分页(不分页)查询

POST /accessAuth/list

> Body 请求参数

```json
{
  "page": {
    "records": [
      {
        "id": 0,
        "accessControlId": 0,
        "accessControlCode": "string",
        "personId": "string",
        "personName": "string",
        "certificateNo": "string",
        "certificateType": "string",
        "phoneNo": "string",
        "birthday": "string",
        "clientId": 0,
        "email": "string",
        "gender": "string",
        "jobNo": "string",
        "orgIndexCode": "string",
        "faces": "string",
        "enterStartTime": "string",
        "enterEndTime": "string",
        "exitStartTime": "string",
        "exitEndTime": "string",
        "creatorId": 0,
        "createTime": "string",
        "modifyId": 0,
        "modifyTime": "string",
        "remark": "string",
        "deleted": 0,
        "ids": [
          0
        ],
        "deviceName": "string",
        "installLocation": "string",
        "ownerEnterpriseName": "string",
        "personNameKey": "string",
        "personIdCardKey": "string",
        "personPhoneKey": "string",
        "accessControlCodeKey": "string",
        "accessControlNameKey": "string",
        "startTime": "string",
        "endTime": "string",
        "authTypeList": [
          0
        ],
        "authStatusList": [
          0
        ],
        "timeLimitTypeList": [
          0
        ],
        "accessControlIdList": [
          0
        ],
        "personIdList": [
          0
        ],
        "authTypeName": "string",
        "authStatusName": "string",
        "timeLimitTypeName": "string",
        "willExpire": true,
        "remainingDays": 0,
        "lastUseTime": "string",
        "useCount": 0
      }
    ],
    "total": 0,
    "size": 0,
    "current": 0,
    "orders": [
      {
        "column": "string",
        "asc": true
      }
    ],
    "optimizeCountSql": true,
    "isSearchCount": true
  },
  "customQueryParams": {
    "id": 0,
    "accessControlId": 0,
    "accessControlCode": "string",
    "personId": "string",
    "personName": "string",
    "certificateNo": "string",
    "certificateType": "string",
    "phoneNo": "string",
    "birthday": "string",
    "clientId": 0,
    "email": "string",
    "gender": "string",
    "jobNo": "string",
    "orgIndexCode": "string",
    "faces": "string",
    "enterStartTime": "string",
    "enterEndTime": "string",
    "exitStartTime": "string",
    "exitEndTime": "string",
    "creatorId": 0,
    "createTime": "string",
    "modifyId": 0,
    "modifyTime": "string",
    "remark": "string",
    "deleted": 0,
    "ids": [
      0
    ],
    "deviceName": "string",
    "installLocation": "string",
    "ownerEnterpriseName": "string",
    "personNameKey": "string",
    "personIdCardKey": "string",
    "personPhoneKey": "string",
    "accessControlCodeKey": "string",
    "accessControlNameKey": "string",
    "startTime": "string",
    "endTime": "string",
    "authTypeList": [
      0
    ],
    "authStatusList": [
      0
    ],
    "timeLimitTypeList": [
      0
    ],
    "accessControlIdList": [
      0
    ],
    "personIdList": [
      0
    ],
    "authTypeName": "string",
    "authStatusName": "string",
    "timeLimitTypeName": "string",
    "willExpire": true,
    "remainingDays": 0,
    "lastUseTime": "string",
    "useCount": 0
  },
  "sorts": [
    {
      "field": "string",
      "sortRule": "string"
    }
  ]
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|body|body|[RequestModelAccessAuthVo](#schemarequestmodelaccessauthvo)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "success": false,
  "code": "",
  "level": "",
  "message": "",
  "data": {}
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RestMessage](#schemarestmessage)|

## POST 新增门禁授权

POST /accessAuth

> Body 请求参数

```json
{
  "id": 0,
  "accessControlId": 0,
  "accessControlCode": "string",
  "personId": "string",
  "personName": "string",
  "certificateNo": "string",
  "certificateType": "string",
  "phoneNo": "string",
  "birthday": "string",
  "clientId": 0,
  "email": "string",
  "gender": "string",
  "jobNo": "string",
  "orgIndexCode": "string",
  "faces": "string",
  "enterStartTime": "string",
  "enterEndTime": "string",
  "exitStartTime": "string",
  "exitEndTime": "string",
  "creatorId": 0,
  "createTime": "string",
  "modifyId": 0,
  "modifyTime": "string",
  "remark": "string",
  "deleted": 0,
  "ids": [
    0
  ]
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|body|body|[AccessAuth](#schemaaccessauth)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "success": false,
  "code": "",
  "level": "",
  "message": "",
  "data": {}
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RestMessage](#schemarestmessage)|

## DELETE 删除门禁授权（包含批量删除）

DELETE /accessAuth

> Body 请求参数

```json
[
  0
]
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|body|body|array[integer]| 否 |none|

> 返回示例

> 200 Response

```json
{
  "success": false,
  "code": "",
  "level": "",
  "message": "",
  "data": {}
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RestMessage](#schemarestmessage)|

## GET 根据id查询门禁授权详情

GET /accessAuth/{id}

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|path|integer| 是 |none|

> 返回示例

> 200 Response

```json
{
  "success": false,
  "code": "",
  "level": "",
  "message": "",
  "data": {}
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RestMessage](#schemarestmessage)|

## POST 撤销门禁授权

POST /accessAuth/revoke/{id}

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|path|integer| 是 |none|
|revokeReason|query|string| 是 |none|

> 返回示例

> 200 Response

```json
{
  "success": false,
  "code": "",
  "level": "",
  "message": "",
  "data": {}
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RestMessage](#schemarestmessage)|

## POST 设置门禁授权进出时间

POST /accessAuth/setAccessTimes

> Body 请求参数

```json
{
  "id": 0,
  "enterStartTime": "string",
  "enterEndTime": "string",
  "exitStartTime": "string",
  "exitEndTime": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|body|body|[AccessTimeSettingDTO](#schemaaccesstimesettingdto)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "success": false,
  "code": "",
  "level": "",
  "message": "",
  "data": {}
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[RestMessage](#schemarestmessage)|

# 数据模型

<h2 id="tocS_RestMessage">RestMessage</h2>

<a id="schemarestmessage"></a>
<a id="schema_RestMessage"></a>
<a id="tocSrestmessage"></a>
<a id="tocsrestmessage"></a>

```json
{
  "success": true,
  "code": "string",
  "level": "string",
  "message": "string",
  "data": {}
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|success|boolean|false|none||成功标识|
|code|string|false|none||状态码|
|level|string|false|none||消息级别|
|message|string|false|none||消息内容|
|data|object|false|none||数据|

<h2 id="tocS_"></h2>

<a id="schema"></a>
<a id="schema_"></a>
<a id="tocS"></a>
<a id="tocs"></a>

```json
{}

```

### 属性

*None*

<h2 id="tocS_AccessAuthVo">AccessAuthVo</h2>

<a id="schemaaccessauthvo"></a>
<a id="schema_AccessAuthVo"></a>
<a id="tocSaccessauthvo"></a>
<a id="tocsaccessauthvo"></a>

```json
{
  "id": 0,
  "accessControlId": 0,
  "accessControlCode": "string",
  "personId": "string",
  "personName": "string",
  "certificateNo": "string",
  "certificateType": "string",
  "phoneNo": "string",
  "birthday": "string",
  "clientId": 0,
  "email": "string",
  "gender": "string",
  "jobNo": "string",
  "orgIndexCode": "string",
  "faces": "string",
  "enterStartTime": "string",
  "enterEndTime": "string",
  "exitStartTime": "string",
  "exitEndTime": "string",
  "creatorId": 0,
  "createTime": "string",
  "modifyId": 0,
  "modifyTime": "string",
  "remark": "string",
  "deleted": 0,
  "ids": [
    0
  ],
  "deviceName": "string",
  "installLocation": "string",
  "ownerEnterpriseName": "string",
  "personNameKey": "string",
  "personIdCardKey": "string",
  "personPhoneKey": "string",
  "accessControlCodeKey": "string",
  "accessControlNameKey": "string",
  "startTime": "string",
  "endTime": "string",
  "authTypeList": [
    0
  ],
  "authStatusList": [
    0
  ],
  "timeLimitTypeList": [
    0
  ],
  "accessControlIdList": [
    0
  ],
  "personIdList": [
    0
  ],
  "authTypeName": "string",
  "authStatusName": "string",
  "timeLimitTypeName": "string",
  "willExpire": true,
  "remainingDays": 0,
  "lastUseTime": "string",
  "useCount": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||主键|
|accessControlId|integer(int64)|false|none||门禁设备ID|
|accessControlCode|string|false|none||门禁编码|
|personId|string|false|none||人员ID，可以指定人员personId，不允许与其他人员personId重复，包括已删除的人员<br />为空时平台自动生成人员ID|
|personName|string|false|none||人员名称，1~32个字符；不能包含 ' / \ : * ? " < >|
|certificateNo|string|false|none||证件号码，1-20位数字字母，平台上人员信息实名标识选择为身份证件时必填|
|certificateType|string|false|none||证件类型，参考附录A%20数据字典，平台上人员信息实名标识选择为身份证件时必填|
|phoneNo|string|false|none||手机号，1-20位数字,平台上人员信息实名标识选择为手机号码时必填|
|birthday|string|false|none||出生日期，举例：1992-09-12|
|clientId|integer|false|none||人员临时标志，当调用方未指定personId时，clientId作为人员标志<br />1~10个字符，只支持数字（批量操作时使用）|
|email|string|false|none||邮箱，举例：<EMAIL>|
|gender|string|false|none||性别，1：男；2：女；0：未知|
|jobNo|string|false|none||工号，1-32个字符|
|orgIndexCode|string|false|none||所属组织标识，必须是已存在组织，从获取组织列表接口获取返回参数orgIndexCode|
|faces|string|false|none||人脸信息（可选，单个添加时使用），存储base64格式的人脸图片数据|
|enterStartTime|string|false|none||进开始时间|
|enterEndTime|string|false|none||进结束时间|
|exitStartTime|string|false|none||出开始时间|
|exitEndTime|string|false|none||出结束时间|
|creatorId|integer(int64)|false|none||创建人id|
|createTime|string|false|none||创建时间|
|modifyId|integer(int64)|false|none||修改人id|
|modifyTime|string|false|none||修改时间|
|remark|string|false|none||备注|
|deleted|integer|false|none||是否删除，0未删除，1已删除|
|ids|[integer]|false|none||none|
|deviceName|string|false|none||门禁设备名称|
|installLocation|string|false|none||安装位置|
|ownerEnterpriseName|string|false|none||权属单位名称|
|personNameKey|string|false|none||人员姓名模糊查询|
|personIdCardKey|string|false|none||身份证号模糊查询|
|personPhoneKey|string|false|none||手机号模糊查询|
|accessControlCodeKey|string|false|none||门禁编码模糊查询|
|accessControlNameKey|string|false|none||门禁名称模糊查询|
|startTime|string|false|none||开始时间|
|endTime|string|false|none||结束时间|
|authTypeList|[integer]|false|none||授权类型集合|
|authStatusList|[integer]|false|none||授权状态集合|
|timeLimitTypeList|[integer]|false|none||时间限制类型集合|
|accessControlIdList|[integer]|false|none||门禁设备ID集合|
|personIdList|[integer]|false|none||人员ID集合|
|authTypeName|string|false|none||授权类型名称|
|authStatusName|string|false|none||授权状态名称|
|timeLimitTypeName|string|false|none||时间限制类型名称|
|willExpire|boolean|false|none||是否即将过期(7天内过期)|
|remainingDays|integer|false|none||剩余有效天数|
|lastUseTime|string|false|none||最后使用时间|
|useCount|integer|false|none||使用次数|

<h2 id="tocS_PageAccessAuthVo">PageAccessAuthVo</h2>

<a id="schemapageaccessauthvo"></a>
<a id="schema_PageAccessAuthVo"></a>
<a id="tocSpageaccessauthvo"></a>
<a id="tocspageaccessauthvo"></a>

```json
{
  "records": [
    {
      "id": 0,
      "accessControlId": 0,
      "accessControlCode": "string",
      "personId": "string",
      "personName": "string",
      "certificateNo": "string",
      "certificateType": "string",
      "phoneNo": "string",
      "birthday": "string",
      "clientId": 0,
      "email": "string",
      "gender": "string",
      "jobNo": "string",
      "orgIndexCode": "string",
      "faces": "string",
      "enterStartTime": "string",
      "enterEndTime": "string",
      "exitStartTime": "string",
      "exitEndTime": "string",
      "creatorId": 0,
      "createTime": "string",
      "modifyId": 0,
      "modifyTime": "string",
      "remark": "string",
      "deleted": 0,
      "ids": [
        0
      ],
      "deviceName": "string",
      "installLocation": "string",
      "ownerEnterpriseName": "string",
      "personNameKey": "string",
      "personIdCardKey": "string",
      "personPhoneKey": "string",
      "accessControlCodeKey": "string",
      "accessControlNameKey": "string",
      "startTime": "string",
      "endTime": "string",
      "authTypeList": [
        0
      ],
      "authStatusList": [
        0
      ],
      "timeLimitTypeList": [
        0
      ],
      "accessControlIdList": [
        0
      ],
      "personIdList": [
        0
      ],
      "authTypeName": "string",
      "authStatusName": "string",
      "timeLimitTypeName": "string",
      "willExpire": true,
      "remainingDays": 0,
      "lastUseTime": "string",
      "useCount": 0
    }
  ],
  "total": 0,
  "size": 0,
  "current": 0,
  "orders": [
    {
      "column": "string",
      "asc": true
    }
  ],
  "optimizeCountSql": true,
  "isSearchCount": true
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|records|[[AccessAuthVo](#schemaaccessauthvo)]|false|none||none|
|total|integer(int64)|false|none||none|
|size|integer(int64)|false|none||none|
|current|integer(int64)|false|none||none|
|orders|[[OrderItem](#schemaorderitem)]|false|none||none|
|optimizeCountSql|boolean|false|none||none|
|isSearchCount|boolean|false|none||none|

<h2 id="tocS_OrderItem">OrderItem</h2>

<a id="schemaorderitem"></a>
<a id="schema_OrderItem"></a>
<a id="tocSorderitem"></a>
<a id="tocsorderitem"></a>

```json
{
  "column": "string",
  "asc": true
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|column|string|false|none||none|
|asc|boolean|false|none||none|

<h2 id="tocS_RequestModelAccessAuthVo">RequestModelAccessAuthVo</h2>

<a id="schemarequestmodelaccessauthvo"></a>
<a id="schema_RequestModelAccessAuthVo"></a>
<a id="tocSrequestmodelaccessauthvo"></a>
<a id="tocsrequestmodelaccessauthvo"></a>

```json
{
  "page": {
    "records": [
      {
        "id": 0,
        "accessControlId": 0,
        "accessControlCode": "string",
        "personId": "string",
        "personName": "string",
        "certificateNo": "string",
        "certificateType": "string",
        "phoneNo": "string",
        "birthday": "string",
        "clientId": 0,
        "email": "string",
        "gender": "string",
        "jobNo": "string",
        "orgIndexCode": "string",
        "faces": "string",
        "enterStartTime": "string",
        "enterEndTime": "string",
        "exitStartTime": "string",
        "exitEndTime": "string",
        "creatorId": 0,
        "createTime": "string",
        "modifyId": 0,
        "modifyTime": "string",
        "remark": "string",
        "deleted": 0,
        "ids": [
          0
        ],
        "deviceName": "string",
        "installLocation": "string",
        "ownerEnterpriseName": "string",
        "personNameKey": "string",
        "personIdCardKey": "string",
        "personPhoneKey": "string",
        "accessControlCodeKey": "string",
        "accessControlNameKey": "string",
        "startTime": "string",
        "endTime": "string",
        "authTypeList": [
          0
        ],
        "authStatusList": [
          0
        ],
        "timeLimitTypeList": [
          0
        ],
        "accessControlIdList": [
          0
        ],
        "personIdList": [
          0
        ],
        "authTypeName": "string",
        "authStatusName": "string",
        "timeLimitTypeName": "string",
        "willExpire": true,
        "remainingDays": 0,
        "lastUseTime": "string",
        "useCount": 0
      }
    ],
    "total": 0,
    "size": 0,
    "current": 0,
    "orders": [
      {
        "column": "string",
        "asc": true
      }
    ],
    "optimizeCountSql": true,
    "isSearchCount": true
  },
  "customQueryParams": {
    "id": 0,
    "accessControlId": 0,
    "accessControlCode": "string",
    "personId": "string",
    "personName": "string",
    "certificateNo": "string",
    "certificateType": "string",
    "phoneNo": "string",
    "birthday": "string",
    "clientId": 0,
    "email": "string",
    "gender": "string",
    "jobNo": "string",
    "orgIndexCode": "string",
    "faces": "string",
    "enterStartTime": "string",
    "enterEndTime": "string",
    "exitStartTime": "string",
    "exitEndTime": "string",
    "creatorId": 0,
    "createTime": "string",
    "modifyId": 0,
    "modifyTime": "string",
    "remark": "string",
    "deleted": 0,
    "ids": [
      0
    ],
    "deviceName": "string",
    "installLocation": "string",
    "ownerEnterpriseName": "string",
    "personNameKey": "string",
    "personIdCardKey": "string",
    "personPhoneKey": "string",
    "accessControlCodeKey": "string",
    "accessControlNameKey": "string",
    "startTime": "string",
    "endTime": "string",
    "authTypeList": [
      0
    ],
    "authStatusList": [
      0
    ],
    "timeLimitTypeList": [
      0
    ],
    "accessControlIdList": [
      0
    ],
    "personIdList": [
      0
    ],
    "authTypeName": "string",
    "authStatusName": "string",
    "timeLimitTypeName": "string",
    "willExpire": true,
    "remainingDays": 0,
    "lastUseTime": "string",
    "useCount": 0
  },
  "sorts": [
    {
      "field": "string",
      "sortRule": "string"
    }
  ]
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|page|[PageAccessAuthVo](#schemapageaccessauthvo)|false|none||分页参数|
|customQueryParams|[AccessAuthVo](#schemaaccessauthvo)|false|none||业务相关的查询参数|
|sorts|[[SortModel](#schemasortmodel)]|false|none||查询时排序参数|

<h2 id="tocS_SortModel">SortModel</h2>

<a id="schemasortmodel"></a>
<a id="schema_SortModel"></a>
<a id="tocSsortmodel"></a>
<a id="tocssortmodel"></a>

```json
{
  "field": "string",
  "sortRule": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|field|string|false|none||排序的字段|
|sortRule|string|false|none||排序规则 ASC / DESC|

<h2 id="tocS_AccessAuth">AccessAuth</h2>

<a id="schemaaccessauth"></a>
<a id="schema_AccessAuth"></a>
<a id="tocSaccessauth"></a>
<a id="tocsaccessauth"></a>

```json
{
  "id": 0,
  "accessControlId": 0,
  "accessControlCode": "string",
  "personId": "string",
  "personName": "string",
  "certificateNo": "string",
  "certificateType": "string",
  "phoneNo": "string",
  "birthday": "string",
  "clientId": 0,
  "email": "string",
  "gender": "string",
  "jobNo": "string",
  "orgIndexCode": "string",
  "faces": "string",
  "enterStartTime": "string",
  "enterEndTime": "string",
  "exitStartTime": "string",
  "exitEndTime": "string",
  "creatorId": 0,
  "createTime": "string",
  "modifyId": 0,
  "modifyTime": "string",
  "remark": "string",
  "deleted": 0,
  "ids": [
    0
  ]
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||主键|
|accessControlId|integer(int64)|false|none||门禁设备ID|
|accessControlCode|string|false|none||门禁编码|
|personId|string|false|none||人员ID，可以指定人员personId，不允许与其他人员personId重复，包括已删除的人员<br />为空时平台自动生成人员ID|
|personName|string|false|none||人员名称，1~32个字符；不能包含 ' / \ : * ? " < >|
|certificateNo|string|false|none||证件号码，1-20位数字字母，平台上人员信息实名标识选择为身份证件时必填|
|certificateType|string|false|none||证件类型，参考附录A%20数据字典，平台上人员信息实名标识选择为身份证件时必填|
|phoneNo|string|false|none||手机号，1-20位数字,平台上人员信息实名标识选择为手机号码时必填|
|birthday|string|false|none||出生日期，举例：1992-09-12|
|clientId|integer|false|none||人员临时标志，当调用方未指定personId时，clientId作为人员标志<br />1~10个字符，只支持数字（批量操作时使用）|
|email|string|false|none||邮箱，举例：<EMAIL>|
|gender|string|false|none||性别，1：男；2：女；0：未知|
|jobNo|string|false|none||工号，1-32个字符|
|orgIndexCode|string|false|none||所属组织标识，必须是已存在组织，从获取组织列表接口获取返回参数orgIndexCode|
|faces|string|false|none||人脸信息（可选，单个添加时使用），存储base64格式的人脸图片数据|
|enterStartTime|string|false|none||进开始时间|
|enterEndTime|string|false|none||进结束时间|
|exitStartTime|string|false|none||出开始时间|
|exitEndTime|string|false|none||出结束时间|
|creatorId|integer(int64)|false|none||创建人id|
|createTime|string|false|none||创建时间|
|modifyId|integer(int64)|false|none||修改人id|
|modifyTime|string|false|none||修改时间|
|remark|string|false|none||备注|
|deleted|integer|false|none||是否删除，0未删除，1已删除|
|ids|[integer]|false|none||none|

<h2 id="tocS_AccessTimeSettingDTO">AccessTimeSettingDTO</h2>

<a id="schemaaccesstimesettingdto"></a>
<a id="schema_AccessTimeSettingDTO"></a>
<a id="tocSaccesstimesettingdto"></a>
<a id="tocsaccesstimesettingdto"></a>

```json
{
  "id": 0,
  "enterStartTime": "string",
  "enterEndTime": "string",
  "exitStartTime": "string",
  "exitEndTime": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||授权ID|
|enterStartTime|string|false|none||进开始时间|
|enterEndTime|string|false|none||进结束时间|
|exitStartTime|string|false|none||出开始时间|
|exitEndTime|string|false|none||出结束时间|

