<template>
    <div>
        <BreadcrumbCustom>
            <BreadcrumbItem>门禁管理</BreadcrumbItem>
            <BreadcrumbItem to="/accessControl/authorizationRecord">门禁授权记录</BreadcrumbItem>
            <BreadcrumbItem>新增</BreadcrumbItem>
        </BreadcrumbCustom>
        <detailCard title="新增授权" :is-back-btn="true" :sub-disabled="m.loading" @on-back="backPage" :is-add="true" @on-submit="submitAjax" @on-cancel="backPage">
            <Form ref="addForm" :model="subObj" :rules="rules" label-position="top">
                <editBox :form="subObj" />
            </Form>
            <!-- 调试信息 -->
            <div style="margin-top: 20px; padding: 10px; background: #f5f5f5; border-radius: 4px;">
                <h4>调试信息：</h4>
                <p>门禁设备ID: {{ subObj.accessControlId }} (类型: {{ typeof subObj.accessControlId }})</p>
                <p>门禁编码: {{ subObj.accessControlCode }}</p>
                <p>人员姓名: {{ subObj.personName }}</p>
                <p>证件号码: {{ subObj.certificateNo }}</p>
                <p>手机号: {{ subObj.phoneNo }}</p>
                <Button @click="debugValidation">检查验证状态</Button>
            </div>
        </detailCard>
    </div>
</template>

<script>
import editBox from './components/editBox.vue'
import { EditMix, DetailMix } from './components/edit-mixin.js'
import { authorizationRecordService } from '@/api/safeManage/accessControlService.ts'

export default {
    name: 'AuthorizationRecordAdd',
    components: {
        editBox
    },
    mixins: [EditMix, DetailMix],
    data() {
        return {}
    },
    mounted() {
        this.init()
    },
    methods: {
        backPage() {
            this.$router.back()
        },
        debugValidation() {
            console.log('=== 调试验证状态 ===')
            console.log('表单数据:', this.subObj)
            console.log('验证规则:', this.rules)

            // 手动验证每个字段
            this.$refs['addForm'].validateField('accessControlId', (error) => {
                console.log('门禁设备验证结果:', error)
            })
            this.$refs['addForm'].validateField('personName', (error) => {
                console.log('人员姓名验证结果:', error)
            })
            this.$refs['addForm'].validateField('certificateNo', (error) => {
                console.log('证件号码验证结果:', error)
            })
            this.$refs['addForm'].validateField('phoneNo', (error) => {
                console.log('手机号验证结果:', error)
            })
        },
        submitAjax() {
            console.log('开始验证表单，当前数据:', this.subObj)
            this.$refs['addForm'].validate((valid) => {
                console.log('表单验证结果:', valid)
                if (valid) {
                    this.m.loading = true
                    let param = JSON.parse(JSON.stringify(this.subObj))

                    // 格式化时间
                    if (param.birthday) {
                        param.birthday = this.$Util.formatDate(param.birthday, 'YYYY-MM-DD')
                    }

                    console.log('准备提交的参数:', param)

                    // 新增门禁授权记录
                    authorizationRecordService.add(param).then(res => {
                        console.log('API响应:', res)
                        if (res.success) {
                            this.$Message.success('提交成功')
                            this.backPage()
                        } else {
                            this.$Message.error(res.message || '提交失败')
                        }
                    }).catch(err => {
                        console.error('提交失败:', err)
                        this.$Message.error('提交失败，请重试')
                    }).finally(() => {
                        this.m.loading = false
                    })
                } else {
                    console.log('表单验证失败')
                    this.$Message.error('请检查表单填写是否正确')
                }
            })
        }
    }
}
</script>

<style lang="less" scoped>

</style>
