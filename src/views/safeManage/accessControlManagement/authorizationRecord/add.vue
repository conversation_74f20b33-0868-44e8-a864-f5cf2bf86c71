<template>
    <div>
        <BreadcrumbCustom>
            <BreadcrumbItem>门禁管理</BreadcrumbItem>
            <BreadcrumbItem to="/accessControl/authorizationRecord">门禁授权记录</BreadcrumbItem>
            <BreadcrumbItem>新增</BreadcrumbItem>
        </BreadcrumbCustom>
        <detailCard title="新增授权" :is-back-btn="true" :sub-disabled="m.uploadLoading" @on-back="backPage" :is-add="true" @on-submit="submitAjax" @on-cancel="backPage">
            <Form ref="addForm" :model="subObj" :rules="rules" label-position="top">
                <editBox :form="subObj" />
            </Form>
        </detailCard>
    </div>
</template>

<script>
import editBox from './components/editBox.vue'
import { EditMix, DetailMix } from './components/edit-mixin.js'
import { authorizationRecordService } from '@/api/safeManage/accessControlService.ts'

export default {
    name: 'AuthorizationRecordAdd',
    components: {
        editBox
    },
    mixins: [EditMix, DetailMix],
    data() {
        return {}
    },
    mounted() {
        this.init()
    },
    methods: {
        backPage() {
            this.$router.back()
        },
        submitAjax() {
            this.$refs['addForm'].validate((valid) => {
                if (valid) {
                    this.m.loading = true
                    let param = JSON.parse(JSON.stringify(this.subObj))

                    // 格式化时间
                    if (param.birthday) {
                        param.birthday = this.$Util.formatDate(param.birthday, 'YYYY-MM-DD')
                    }

                    // 新增门禁授权记录
                    authorizationRecordService.add(param).then(res => {
                        if (res.success) {
                            this.$Message.success('提交成功')
                            this.backPage()
                        }
                    }).finally(() => {
                        this.m.loading = false
                    })
                }
            })
        }
    }
}
</script>

<style lang="less" scoped>

</style>
