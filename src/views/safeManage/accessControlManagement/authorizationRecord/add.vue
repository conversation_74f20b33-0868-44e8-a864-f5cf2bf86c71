<template>
    <div>
        <BreadcrumbCustom>
            <BreadcrumbItem>门禁管理</BreadcrumbItem>
            <BreadcrumbItem to="/accessControl/authorizationRecord">门禁授权记录</BreadcrumbItem>
            <BreadcrumbItem>新增</BreadcrumbItem>
        </BreadcrumbCustom>
        <detailCard title="新增授权" :is-back-btn="true" :sub-disabled="m.loading" @on-back="backPage" :is-add="true" @on-submit="submitAjax" @on-cancel="backPage">
            <Form ref="addForm" :model="subObj" :rules="rules" label-position="top">
                <editBox :form="subObj" />
            </Form>

        </detailCard>
    </div>
</template>

<script>
import editBox from './components/editBox.vue'
import { EditMix, DetailMix } from './components/edit-mixin.js'
import { authorizationRecordService } from '@/api/safeManage/accessControlService.ts'

export default {
    name: 'AuthorizationRecordAdd',
    components: {
        editBox
    },
    mixins: [EditMix, DetailMix],
    data() {
        return {}
    },
    mounted() {
        this.init()
    },
    methods: {
        backPage() {
            this.$router.back()
        },

        submitAjax() {
            console.log('开始验证表单，当前数据:', this.subObj)
            this.$refs['addForm'].validate((valid) => {
                console.log('表单验证结果:', valid)
                if (valid) {
                    this.m.loading = true
                    let param = JSON.parse(JSON.stringify(this.subObj))

                    // 格式化时间
                    if (param.birthday) {
                        param.birthday = this.$Util.formatDate(param.birthday, 'YYYY-MM-DD')
                    }

                    console.log('准备提交的参数:', param)

                    // 新增门禁授权记录
                    authorizationRecordService.add(param).then(res => {
                        console.log('API响应:', res)
                        if (res.success) {
                            this.$Message.success('提交成功')
                            this.backPage()
                        } else {
                            this.$Message.error(res.message || '提交失败')
                        }
                    }).catch(err => {
                        console.error('提交失败:', err)
                        this.$Message.error('提交失败，请重试')
                    }).finally(() => {
                        this.m.loading = false
                    })
                } else {
                    console.log('表单验证失败')
                    this.$Message.error('请检查表单填写是否正确')
                }
            })
        }
    }
}
</script>

<style lang="less" scoped>

</style>
