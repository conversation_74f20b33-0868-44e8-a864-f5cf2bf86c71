<template>
    <div>
        <Row :gutter="24">
            <Col span="8">
            <s-label label="人员姓名">
                <template #value><span style="font-size: 16px;font-weight: 600;">{{ subObj.personName }}</span></template>
            </s-label>
            </Col>
            <Col span="8">
            <s-label label="证件号码">
                <template #value><span style="font-weight: 600;">{{ subObj.certificateNo }}</span></template>
            </s-label>
            </Col>
            <Col span="8">
            <s-label label="证件类型" :value="getCertificateTypeName(subObj.certificateType)" />
            </Col>
        </Row>
        <Row :gutter="24">
            <Col span="8">
            <s-label label="手机号" :value="subObj.phoneNo" />
            </Col>
            <Col span="8">
            <s-label label="性别" :value="getGenderName(subObj.gender)" />
            </Col>
            <Col span="8">
            <s-label label="出生日期" :value="subObj.birthday" />
            </Col>
        </Row>
        <Row :gutter="24">
            <Col span="8">
            <s-label label="门禁编码" :value="subObj.accessControlCode" />
            </Col>
            <Col span="8">
            <s-label label="门禁名称" :value="subObj.deviceName" />
            </Col>
            <Col span="8">
            <s-label label="安装位置" :value="subObj.installLocation" />
            </Col>
        </Row>
        <Row :gutter="24">
            <Col span="8">
            <s-label label="工号" :value="subObj.jobNo" />
            </Col>
            <Col span="8">
            <s-label label="邮箱" :value="subObj.email" />
            </Col>
            <Col span="8">
            <s-label label="权属单位" :value="subObj.ownerEnterpriseName" />
            </Col>
        </Row>
        <Row :gutter="24">
            <Col span="8">
            <s-label label="进入开始时间" :value="subObj.enterStartTime" />
            </Col>
            <Col span="8">
            <s-label label="进入结束时间" :value="subObj.enterEndTime" />
            </Col>
            <Col span="8">
            <s-label label="离开开始时间" :value="subObj.exitStartTime" />
            </Col>
        </Row>
        <Row :gutter="24">
            <Col span="8">
            <s-label label="离开结束时间" :value="subObj.exitEndTime" />
            </Col>
            <Col span="8">
            <s-label label="创建时间" :value="subObj.createTime" />
            </Col>
            <Col span="8">
            <s-label label="修改时间" :value="subObj.modifyTime" />
            </Col>
        </Row>
        <Row :gutter="24" v-if="subObj.remark">
            <Col span="24">
            <s-label label="备注" :value="subObj.remark" />
            </Col>
        </Row>
    </div>
</template>

<script>
export default {
    name: 'AuthorizationRecordInfoShow',
    props: {
        subObj: { default: () => ({}) }
    },
    methods: {
        getCertificateTypeName(type) {
            const typeMap = {
                '111': '身份证',
                '112': '护照',
                '113': '军官证',
                '114': '驾驶证',
                '115': '其他'
            }
            return typeMap[type] || type
        },
        getGenderName(gender) {
            const genderMap = {
                '1': '男',
                '2': '女',
                '0': '未知'
            }
            return genderMap[gender] || gender
        }
    }
}
</script>

<style lang="less" scoped>

</style>
