import { authorizationRecordService } from '@/api/safeManage/accessControlService.ts'

const defaultSubObj = {
    // 主键
    id: null,
    // 门禁设备ID
    accessControlId: '',
    // 门禁编码
    accessControlCode: '',
    // 人员ID
    personId: '',
    // 人员姓名
    personName: '',
    // 证件号码
    certificateNo: '',
    // 证件类型
    certificateType: '111', // 默认身份证
    // 手机号
    phoneNo: '',
    // 出生日期
    birthday: '',
    // 邮箱
    email: '',
    // 性别
    gender: '0', // 0：未知；1：男；2：女
    // 工号
    jobNo: '',
    // 所属组织标识
    orgIndexCode: '',
    // 人脸信息
    faces: '',
    // 备注
    remark: ''
}

export const EditMix = {
    props: {
        id: { default: '' }
    },
    data() {
        return {
            form: this.$Util.objClone(defaultSubObj)
        }
    },
    computed: {
    },
    mounted() {
    },
    methods: {
        init(data = {}) {
            this.form = data
        }
    }
}

export const DetailMix = {
    data() {
        return {
            m: {
                loading: false,
                uploadLoading: false,
                device: {}
            },
            subObj: JSON.parse(JSON.stringify(defaultSubObj)),
            rules: {
                accessControlId: [
                    { required: true, message: '请选择门禁设备', trigger: 'change' },
                    { required: true, message: '请选择门禁设备', trigger: 'blur' }
                ],
                personName: { required: true, message: '请输入人员姓名', trigger: 'blur' },
                certificateNo: [
                    { required: true, message: '请输入证件号码', trigger: 'blur' },
                    { pattern: /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/, message: '请输入正确的身份证号', trigger: 'blur' }
                ],
                certificateType: { required: true, message: '请选择证件类型', trigger: 'change' },
                phoneNo: [
                    { required: true, message: '请输入手机号', trigger: 'blur' },
                    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
                ],
                email: [
                    { pattern: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/, message: '请输入正确的邮箱格式', trigger: 'blur' }
                ]
            }
        }
    },
    methods: {
        // 初始化数据
        init() {
            // subObj已经在data中初始化，这里可以做其他初始化工作
        },
        submitAjax() {
            this.$refs['addForm'].validate((valid) => {
                if (valid) {
                    this.m.loading = true
                    let param = this.$Util.objClone(this.subObj)

                    // 格式化时间
                    if (param.birthday) {
                        param.birthday = this.$Util.formatDate(param.birthday, 'YYYY-MM-DD')
                    }

                    // 新增门禁授权记录
                    authorizationRecordService.add(param).then(res => {
                        if (res.success) {
                            this.$Message.success('提交成功')
                            this.backPage()
                        }
                    }).finally(() => {
                        this.m.loading = false
                    })
                }
            })
        },
        // 得到详情
        getDetailById(id) {
            authorizationRecordService.getById(id).then(res => {
                let data = res.data
                let subObj = this.$Util.objClone(defaultSubObj)

                for (let k in subObj) {
                    if (data[k] || data[k] === 0) {
                        subObj[k] = data[k]
                    }
                }
                this.m.device = this.$Util.objClone(data)
                subObj.id = data.id
                this.subObj = subObj
            })
        },
        backPage() {
            this.$router.back()
        }
    }
}
