<template>
    <div>
    <Row :gutter="24">
        <Col :span="8">
        <FormItem label="门禁设备" prop="accessControlId">
            <Select v-model="form.accessControlId" placeholder="请选择门禁设备" clearable filterable @on-change="onAccessControlChange" :loading="accessControlLoading">
                <Option v-for="item in accessControlList" :key="item.id" :value="item.id">
                    {{ item.name }}{{ item.code ? `(${item.code})` : (item.deviceCode ? `(${item.deviceCode})` : '') }}
                </Option>
            </Select>
        </FormItem>
        </Col>

        <Col :span="8">
        <FormItem label="人员姓名" prop="personName">
            <Input v-model="form.personName" placeholder="请输入人员姓名" maxlength="32" clearable></Input>
        </FormItem>
        </Col>
        <Col :span="8">
        <FormItem label="证件类型" prop="certificateType">
            <Select v-model="form.certificateType" placeholder="请选择证件类型" clearable>
                <Option value="111">身份证</Option>
                <Option value="112">护照</Option>
                <Option value="113">军官证</Option>
                <Option value="114">驾驶证</Option>
                <Option value="115">其他</Option>
            </Select>
        </FormItem>
        </Col>
        <Col :span="8">
        <FormItem label="证件号码" prop="certificateNo">
            <Input v-model="form.certificateNo" placeholder="请输入证件号码" maxlength="20" clearable></Input>
        </FormItem>
        </Col>
        <Col :span="8">
        <FormItem label="手机号" prop="phoneNo">
            <Input v-model="form.phoneNo" placeholder="请输入手机号" maxlength="11" clearable></Input>
        </FormItem>
        </Col>
        <Col :span="8">
        <FormItem label="性别" prop="gender">
            <Select v-model="form.gender" placeholder="请选择性别" clearable>
                <Option value="1">男</Option>
                <Option value="2">女</Option>
                <Option value="0">未知</Option>
            </Select>
        </FormItem>
        </Col>
        <Col :span="8">
        <FormItem label="出生日期">
            <DatePicker v-model="form.birthday" type="date" placeholder="请选择出生日期" format="yyyy-MM-dd" :editable="false" style="width: 100%" />
        </FormItem>
        </Col>
        <Col :span="8">
        <FormItem label="工号">
            <Input v-model="form.jobNo" placeholder="请输入工号" maxlength="32" clearable></Input>
        </FormItem>
        </Col>
        <Col :span="8">
        <FormItem label="邮箱">
            <Input v-model="form.email" placeholder="请输入邮箱" maxlength="50" clearable></Input>
        </FormItem>
        </Col>
        <Col :span="8">
        <FormItem label="人脸照片">
            <div class="face-upload-container">
                <div v-if="!form.faces" class="upload-area" @click="triggerFileInput">
                    <Icon type="ios-camera" size="24" />
                    <p>点击上传人脸照片</p>
                </div>
                <div v-else class="face-preview">
                    <img :src="faceImageUrl" alt="人脸照片" class="face-image" />
                    <div class="face-actions">
                        <Button type="primary" size="small" @click="triggerFileInput">重新上传</Button>
                        <Button type="error" size="small" @click="removeFaceImage">删除</Button>
                    </div>
                </div>
                <input
                    ref="faceFileInput"
                    type="file"
                    accept="image/*"
                    style="display: none"
                    @change="handleFaceUpload"
                />
            </div>
        </FormItem>
        </Col>
    </Row>

    <Row :gutter="24">
        <Col :span="24">
        <FormItem label="备注">
            <Input v-model="form.remark" type="textarea" :rows="3" placeholder="请输入备注信息" maxlength="500"></Input>
        </FormItem>
        </Col>
    </Row>
    </div>
</template>

<script>
import { accessControlService } from '@/api/safeManage/accessControlService.ts'

export default {
    name: 'AuthorizationRecordEditBox',
    props: {
        form: {
            type: Object,
            default: () => ({})
        }
    },
    data() {
        return {
            accessControlList: [], // 门禁设备列表
            accessControlLoading: false // 门禁设备加载状态
        }
    },
    computed: {
        // 人脸图片URL
        faceImageUrl() {
            if (this.form.faces && this.form.faces.startsWith('data:image/')) {
                return this.form.faces
            } else if (this.form.faces) {
                return `data:image/jpeg;base64,${this.form.faces}`
            }
            return ''
        }
    },
    mounted() {
        this.getAccessControlList()
    },
    methods: {
        // 获取门禁设备列表
        getAccessControlList() {
            this.accessControlLoading = true
            accessControlService.getPage({
                page: { current: 1, size: -1 }, // size传-1查询全部
                customQueryParams: {
                    status: 1 // 查询状态为正常的门禁
                }
            }).then(res => {
                if (res.success && res.data && res.data.records) {
                    this.accessControlList = res.data.records
                }
            }).catch(err => {
                console.error('获取门禁设备列表失败:', err)
                this.$Message.error('获取门禁设备列表失败')
            }).finally(() => {
                this.accessControlLoading = false
            })
        },

        // 门禁设备选择变化时的联动
        onAccessControlChange() {
            // 手动触发验证
            this.$nextTick(() => {
                if (this.$parent && this.$parent.$refs && this.$parent.$refs.addForm) {
                    this.$parent.$refs.addForm.validateField('accessControlId')
                }
            })
        },

        // 触发文件选择
        triggerFileInput() {
            this.$refs.faceFileInput.click()
        },

        // 处理人脸照片上传
        handleFaceUpload(event) {
            const file = event.target.files[0]
            if (!file) return

            // 验证文件类型
            if (!file.type.startsWith('image/')) {
                this.$Message.error('请选择图片文件')
                return
            }

            // 验证文件大小（限制为5MB）
            if (file.size > 5 * 1024 * 1024) {
                this.$Message.error('图片大小不能超过5MB')
                return
            }

            // 转换为base64
            const reader = new FileReader()
            reader.onload = (e) => {
                const base64 = e.target.result
                // 去掉data:image/xxx;base64,前缀，只保留base64数据
                const base64Data = base64.split(',')[1]
                this.form.faces = base64Data
                this.$Message.success('人脸照片上传成功')
            }
            reader.onerror = () => {
                this.$Message.error('图片读取失败')
            }
            reader.readAsDataURL(file)

            // 清空input值，允许重复选择同一文件
            event.target.value = ''
        },

        // 删除人脸照片
        removeFaceImage() {
            this.form.faces = ''
            this.$Message.success('人脸照片已删除')
        }
    }
}
</script>

<style lang="less" scoped>
.face-upload-container {
    .upload-area {
        border: 2px dashed #dcdee2;
        border-radius: 6px;
        width: 120px;
        height: 120px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: border-color 0.3s;

        &:hover {
            border-color: #2d8cf0;
        }

        p {
            margin: 8px 0 0 0;
            font-size: 12px;
            color: #999;
        }
    }

    .face-preview {
        position: relative;
        width: 120px;
        height: 120px;

        .face-image {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 6px;
            border: 1px solid #dcdee2;
        }

        .face-actions {
            position: absolute;
            bottom: -30px;
            left: 0;
            right: 0;
            display: flex;
            gap: 8px;

            .ivu-btn {
                flex: 1;
                font-size: 12px;
                height: 24px;
                line-height: 22px;
            }
        }
    }
}
</style>
