<template>
    <div>
    <Row :gutter="24">
        <Col :span="8">
        <FormItem label="门禁设备" prop="accessControlId">
            <Select v-model="form.accessControlId" placeholder="请选择门禁设备" clearable filterable @on-change="onAccessControlChange" :loading="accessControlLoading">
                <Option v-for="item in accessControlList" :key="item.id" :value="item.id">
                    {{ item.name }}
                </Option>
            </Select>
        </FormItem>
        </Col>
        <Col :span="8">
        <FormItem label="门禁编码" prop="accessControlCode">
            <Input v-model="form.accessControlCode" placeholder="门禁编码" readonly></Input>
        </FormItem>
        </Col>
        <Col :span="8">
        <FormItem label="人员姓名" prop="personName">
            <Input v-model="form.personName" placeholder="请输入人员姓名" maxlength="32" clearable></Input>
        </FormItem>
        </Col>
        <Col :span="8">
        <FormItem label="证件类型" prop="certificateType">
            <Select v-model="form.certificateType" placeholder="请选择证件类型" clearable>
                <Option value="111">身份证</Option>
                <Option value="112">护照</Option>
                <Option value="113">军官证</Option>
                <Option value="114">驾驶证</Option>
                <Option value="115">其他</Option>
            </Select>
        </FormItem>
        </Col>
        <Col :span="8">
        <FormItem label="证件号码" prop="certificateNo">
            <Input v-model="form.certificateNo" placeholder="请输入证件号码" maxlength="20" clearable></Input>
        </FormItem>
        </Col>
        <Col :span="8">
        <FormItem label="手机号" prop="phoneNo">
            <Input v-model="form.phoneNo" placeholder="请输入手机号" maxlength="11" clearable></Input>
        </FormItem>
        </Col>
        <Col :span="8">
        <FormItem label="性别" prop="gender">
            <Select v-model="form.gender" placeholder="请选择性别" clearable>
                <Option value="1">男</Option>
                <Option value="2">女</Option>
                <Option value="0">未知</Option>
            </Select>
        </FormItem>
        </Col>
        <Col :span="8">
        <FormItem label="出生日期">
            <DatePicker v-model="form.birthday" type="date" placeholder="请选择出生日期" format="yyyy-MM-dd" :editable="false" style="width: 100%" />
        </FormItem>
        </Col>
        <Col :span="8">
        <FormItem label="工号">
            <Input v-model="form.jobNo" placeholder="请输入工号" maxlength="32" clearable></Input>
        </FormItem>
        </Col>
        <Col :span="8">
        <FormItem label="邮箱">
            <Input v-model="form.email" placeholder="请输入邮箱" maxlength="50" clearable></Input>
        </FormItem>
        </Col>
    </Row>

    <Row :gutter="24">
        <Col :span="24">
        <FormItem label="备注">
            <Input v-model="form.remark" type="textarea" :rows="3" placeholder="请输入备注信息" maxlength="500"></Input>
        </FormItem>
        </Col>
    </Row>
    </div>
</template>

<script>
import { accessControlService } from '@/api/safeManage/accessControlService.ts'

export default {
    name: 'AuthorizationRecordEditBox',
    props: {
        form: {
            type: Object,
            default: () => ({})
        }
    },
    data() {
        return {
            accessControlList: [], // 门禁设备列表
            accessControlLoading: false // 门禁设备加载状态
        }
    },
    mounted() {
        this.getAccessControlList()
    },
    methods: {
        // 获取门禁设备列表
        getAccessControlList() {
            this.accessControlLoading = true
            accessControlService.getPage({
                page: { current: 1, size: -1 }, // size传-1查询全部
                customQueryParams: {
                    status: 1 // 查询状态为正常的门禁
                }
            }).then(res => {
                if (res.success && res.data && res.data.records) {
                    this.accessControlList = res.data.records
                }
            }).catch(err => {
                console.error('获取门禁设备列表失败:', err)
                this.$Message.error('获取门禁设备列表失败')
            }).finally(() => {
                this.accessControlLoading = false
            })
        },

        // 门禁设备选择变化时的联动
        onAccessControlChange(accessControlId) {
            if (accessControlId) {
                const selectedDevice = this.accessControlList.find(item => item.id === accessControlId)
                if (selectedDevice) {
                    // 自动填充门禁编码，优先使用code字段，其次使用deviceCode字段
                    this.form.accessControlCode = selectedDevice.code || selectedDevice.deviceCode || selectedDevice.accessControlCode || ''
                }
            } else {
                // 清空门禁编码
                this.form.accessControlCode = ''
            }
        }
    }
}
</script>

<style lang="less" scoped>

</style>
