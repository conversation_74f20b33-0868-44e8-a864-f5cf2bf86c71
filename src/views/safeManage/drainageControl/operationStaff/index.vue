<template>
  <BreadcrumbCustom>
    <BreadcrumbItem>排水控制管理</BreadcrumbItem>
    <BreadcrumbItem>运营人员</BreadcrumbItem>
  </BreadcrumbCustom>
  <ContentCard title="运营人员">
    <BaseForm inline :label-width="90" :model="searchObj" @handle-submit="search">
      <template #formitem>
        <FormItem label="姓名" prop="name">
          <Input v-model="searchObj.name" :maxlength="20" placeholder="请输入" clearable></Input>
        </FormItem>
        <FormItem label="人员账号" prop="account">
          <Input v-model="searchObj.account" :maxlength="20" placeholder="请输入" clearable></Input>
        </FormItem>
        <FormItem label="关联设备" prop="sbmc">
          <Input v-model="searchObj.sbmc" :maxlength="20" placeholder="请输入" clearable></Input>
        </FormItem>
      </template>
    </BaseForm>
    <btn-card>
      <Button type="primary" icon="md-add" @click="add" v-auth="'drainageControl:operationStaff:add'">
        新增
      </Button>
      <Button icon="ios-trash" @click="delMore" v-auth="'drainageControl:operationStaff:delete'">
        删除
      </Button>
      <!-- <Button icon="ios-trash" @click="delMore">
              导出
          </Button> -->
    </btn-card>
    <base-table ref="listCom" :columns="columns" :url="tableUrl" @on-selection-change="selectionChange">
      <!-- <template #objName="{ row }">
        <tooltipAutoShow :content="row.objInfo.objName" />
      </template> -->
      <template #sex="{ row }">
        <tooltip-auto-show :content="row.sex == 1 ? '男' : row.sex == 2 ? '女' : ''"></tooltip-auto-show>
      </template>
      <template #action="{ row }">
        <link-btn size="small" @click="toDetail(row)">
          查看
        </link-btn>
        <!-- <link-btn size="small" v-auth="'drainageControl:operationStaff:association'">
          关联设备
        </link-btn> -->
        <deviceSelect v-auth="'drainageControl:operationStaff:association'" style="display: line;" :model-id="26" type="drainage" :buttonType="'link'" :multiple="true" @on-change="addSubmit($event, row)" />
      </template>
    </base-table>
  </ContentCard>
</template>

<script lang="ts" setup>
import { getCurrentInstance, onMounted, ref, reactive, computed } from 'vue'
import { useRouter } from 'vue-router'
import { operationStaffService } from '@/api/safeManage/drainageControl_operationStaff'
import moment from 'moment'
const that: any = getCurrentInstance()?.appContext.config.globalProperties
const router = useRouter()


const tableUrl = '/drainageEmergencyPersonDeviceRef/list'
const columns = ref<any>([
  { type: 'selection', width: 50, align: 'center' },
  { title: '人员账号', key: 'account', tooltip: true },
  { title: '姓名', key: 'name' ,tooltip: true },
  { title: '性别', slot: 'sex', tooltip: true },
  { title: '岗位', key: 'job', tooltip: true },
  { title: '关联设备', key: 'sbmcList', tooltip: true },
  { title: '联系电话', key: 'mobile', tooltip: true },
  { title: '更新时间', key: 'updateTime', tooltip: true },
  { title: '创建日期', key: 'createTime', tooltip: true },
  { title: '操作', slot: 'action', width: 130 }
])
const searchObj: any = ref({
  account: '',
  name: '',
  sbmc: '', // 设备名称
})
const m = reactive({
  selectList: [] // 表格已选数据
})
onMounted(() => { search() })
function selectionChange(list: any) {
  m.selectList = list || []
}
const listCom = ref()
function search() {
  listCom.value.search(searchObj.value)
}

function add() {
  router.push({
    path: '/drainageControl/operationStaffAdd'
  })
}
function toDetail(row: any) {
  router.push({
    path: '/drainageControl/operationStaffDetail',
    query: {
      id: row.emergencyPersonId
    }
  })
}
function delMore() {
  if (m.selectList.length === 0) {
    that.$Message.warning('最少选择一条数据')
    return
  }
  that.$Modal.confirm({
    title: '提示',
    content: '您确定要删除选中的数据吗',
    onOk: () => {
      let arr: any[] = []
      m.selectList.forEach((item: any) => {
        arr.push(item.emergencyPersonId)
      })
      delAjax(arr)
    }
  })
}
function delAjax(arr: any[]) {
  operationStaffService.delete({ emergencyPersonId: arr.join(',') })
    .then((res: any) => {
      if (res.success) {
        that.$Message.success('删除成功')
        m.selectList = []
        search()
      }
    })
}

const addSubmit = async (list: any[], row: any) => {
  console.log('选择了哪些设备---', list);
  // form.value.drainageDeviceList = list
  row.drainageDeviceList = list
  let res: any = await operationStaffService.add(row);
  if (res.success) {
    that?.$Message.success('关联成功');
    search()
  }
};

</script>

<style lang="less" scoped></style>
