<template>
  <BreadcrumbCustom>
    <BreadcrumbItem>排水控制管理</BreadcrumbItem>
    <BreadcrumbItem to="/drainageControl/operationStaff">运营人员</BreadcrumbItem>
    <BreadcrumbItem>{{ isEdit ? "编辑" : "详情" }}</BreadcrumbItem>
  </BreadcrumbCustom>
  <safeBaseInfo class="base" @on-change="handleChange" @on-submit="handleSubmit" editAuth="emergencyResponse:emergencyResource:emergencyPerson:edit" ref="safeBaseInfoCom" :list="list" :data="data" :rules="rules" :modelForm="form">
    <template #sex="{ sex }">
      <s-label label="性别" v-if="!isEdit">
        <template #value>
          <RadioGroup :model-value="sex">
            <Radio v-if="sex == 1" :label="1">男</Radio>
            <Radio v-else :label="2">女</Radio>
          </RadioGroup>
        </template>
      </s-label>
      <div v-else style="width: 200px">
        <RadioGroup v-model="form.sex">
          <Radio disabled :label="1">男</Radio>
          <Radio disabled :label="2">女</Radio>
        </RadioGroup>
      </div>
    </template>
    <template #birthday="{ birthday }">
      <s-label label="出生日期" :value="birthday?.split(' ')[0] || '--'" v-if="!isEdit" />
      <DatePicker :model-value="form.birthday" v-else format="yyyy-MM-dd" disabled @on-change="(a: string) => form.birthday = a && moment(a).format('YYYY-MM-DD HH:mm:ss') || ''" type="date" placeholder="请选择" clearable :editable="false" />
    </template>
    <template #health="{ health }">
      <s-label label="健康状况" v-if="!isEdit">
        <template #value>
          {{ $enumeration.healthStatus[health] }}
        </template>
      </s-label>
      <Select clearable v-else v-model="form.health" placeholder="请选择">
        <Option v-for="(item, index) in $enumeration.healthStatus.filter((i: string, index: number) => index !== 0)" :key="index" :value="index + 1">{{ item }}</Option>
      </Select>
    </template>
  </safeBaseInfo>
  <div style="display: flex; width: 100%;">
    <template v-if="isEdit">
      <deviceSelect :model-id="26" type="drainage" :default-sel-list="form.drainageDeviceList" :multiple="true" @on-change="addSubmit" />
      <chooseDeviceBox v-if="form.drainageDeviceList.length > 0" title="" style="width: 100%;">
        <div class="placeholder" v-show="form.drainageDeviceList.length === 0">请选择设备</div>
        <Tooltip v-for="(item, index) in form.drainageDeviceList" :max-width="300">
          <Tag color="blue" :disabled="!isEdit" closable @on-close="delChoose(item, index)">{{ item.deviceCode }}</Tag>
          <template #content>
            <p>设备名称: {{ item.sbmc }}</p>
            <p>设备型号: {{ item.deviceUnitCode }}</p>
            <p>设备编号: {{ item.deviceId }}</p>
            <p>区域位置: {{ item.areaPath }}</p>
          </template>
        </Tooltip>
      </chooseDeviceBox>
    </template>
    <template v-else>
      <chooseDeviceBox v-if="data.drainageDeviceList.length > 0" title="" style="width: 100%;">
        <!-- <div class="placeholder" v-show="data.drainageDeviceList.length === 0">请选择设备</div> -->
        <Tooltip v-for="(item, index) in data.drainageDeviceList" :max-width="300">
          <Tag color="blue" :disabled="true" closable>{{ item.deviceCode }}</Tag>
          <template #content>
            <p>设备名称: {{ item.sbmc }}</p>
            <p>设备型号: {{ item.deviceUnitCode }}</p>
            <p>设备编号: {{ item.deviceId }}</p>
            <p>区域位置: {{ item.areaPath }}</p>
          </template>
        </Tooltip>
      </chooseDeviceBox>
    </template>
  </div>
</template>

<script lang="ts" setup>
import safeBaseInfo from "@/components/common/baseInfo/safeBaseInfo";
import { getCurrentInstance, reactive, ref } from "vue";
import { operationStaffService } from '@/api/safeManage/drainageControl_operationStaff';
import { useRoute } from "vue-router";
import moment from "moment";
import { flatObj, ObjAssign } from "@/utils/tool";
import chooseDeviceBox from '@/components/common/deviceSelect/chooseDeviceBox.vue';
const list = ref<any>([
  { label: "人员账号", key: "account", disabled: true, bold: true },
  { label: "姓名", key: "name", disabled: true },
  { label: "性别", slot: "sex", disabled: true },
  { label: "联系电话", key: "mobile", disabled: true },
  { label: "电子邮箱", key: "email", disabled: true },
  { label: "岗位", key: "job", disabled: true },
  { label: "部门", key: "deptName", disabled: true },
  { label: "出生日期", slot: "birthday", disabled: true },
  { label: "身份证号码", key: "idCard", maxlength: 18, disabled: true },
  { label: "健康状况", slot: "health" },
  { label: "创建时间", key: "createTime", disabled: true },
  { label: "更新时间", key: "updateTime", disabled: true },
  {
    label: "备注",
    key: "remark",
    type: "textarea",
    span: "24",
    maxlength: 200,
    rows: 3,
    tooltip: false,
    showWordLimit: true,
  },
]);
// 获取详情信息
const route = useRoute();
const data = ref<any>({
  drainageDeviceList: []
});
async function getDetailInfo() {
  let res: any = await operationStaffService.getById(route.query.id);
  if (res.success) {
    let drainageDeviceList = JSON.parse(JSON.stringify(res.data.drainageDeviceList || []));
    delete res.data.drainageDeviceList;
    res.data = flatObj(res.data);
    res.data.drainageDeviceList = drainageDeviceList.map(el => {
      return flatObj(el)
    });
    data.value = res.data;
    console.info(data.value)
  }
}
getDetailInfo();
const form = ref<any>({
  drainageDeviceList: []
});
const isEdit = ref<boolean>(false);
function handleChange(flag: boolean, data: any) {
  form.value = data;
  isEdit.value = flag;
  console.log('表单数据是啥????', form.value);
  
  getDetailInfo();
}
// 提交编辑
const that = getCurrentInstance()?.appContext.config.globalProperties;
const safeBaseInfoCom = ref();
async function handleSubmit(data: any) {
  const params = ObjAssign(data, form.value, [list.value]);
  params.drainageDeviceList = form.value.drainageDeviceList
  console.log('提交的数据？？？？？', params);
  
  let res: any = await operationStaffService.update(params);
  if (res.success) {
    isEdit.value = false;
    safeBaseInfoCom.value.isEdit = false;
    getDetailInfo();
    that?.$Message.success("编辑成功");
    safeBaseInfoCom.value.closeEdit();
  }
}
const rules = reactive({
  account: [{ required: true, message: "请选择人员账号", trigger: "blur" }],
  // birthday: [{ required: true, message: "请选择出生日期", trigger: "blur" }],
  idCard: [
    // { required: true, message: "请输入身份证号", trigger: "blur" },
    {
      pattern:
        /^[1-9]\d{5}(?:18|19|20)\d{2}(?:0[1-9]|10|11|12)(?:0[1-9]|[1-2]\d|30|31)\d{3}[\dXx]$/,
      message: "请输入有效的身份证号",
      trigger: "blur",
    },
  ],
  // health: [{ required: true, message: "请选择健康状态", trigger: "blur" }],
});

const addSubmit = async (list: any[]) => {
  console.log('选择了哪些设备---', list);
  form.value.drainageDeviceList = list
};

const delChoose = (row, index) => {
  form.value.drainageDeviceList.splice(index, 1)
}
</script>

<style lang="less" scoped></style>
