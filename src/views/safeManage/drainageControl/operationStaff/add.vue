
<template>
  <BreadcrumbCustom>
    <BreadcrumbItem>排水控制管理</BreadcrumbItem>
    <BreadcrumbItem to="/drainageControl/operationStaff">运营人员</BreadcrumbItem>
    <BreadcrumbItem>新增</BreadcrumbItem>
  </BreadcrumbCustom>
  <detailCard title="选择人员" :is-back-btn="true" @on-back="back">
    <Form ref="formCom" :rules="rules" :model="form" label-position="top">
      <Row :gutter="80">
        <Col span="8">
        <FormItem label="人员账号" prop="emergencyPersonId">
          <Select clearable @on-clear="handleClear" v-model="form.emergencyPersonId" filterable>
            <Option v-for="(option, index) in cloneData" :value="option.emergencyPersonId" :key="index" @click="handleClickList(option)">
              {{ option.name }}
            </Option>
          </Select>
        </FormItem>
        </Col>
      </Row>
      <Row :gutter="80">
        <Col :span="item.span || 8" v-for="(item, index) in formList" :key="index">
        <FormItem :label="item.label" :prop="item.key || ''">
          <Select v-if="item.type === 'select'" v-model="form[item.key]" placeholder="请选择" :disabled="item.disabled" clearable>
            <Option v-for="(item, index) in $enumeration.healthStatus.filter((i: string, index: number) => index !== 0)" :key="index" :value="index + 1">
              {{ item }}
            </Option>
          </Select>
          <DatePicker v-else-if="item.type === 'date'" type="date" placeholder="请选择" :disabled="item.disabled" format="yyyy-MM-dd" @on-change="(a: string) => form[item.key] = a && moment(a).format('YYYY-MM-DD HH:mm:ss') || ''" v-model="form[item.key]" clearable :editable="false" />
          <Input v-else-if="item.key === 'sex'" :model-value="form[item.key] == 1 ? '男' : form[item.key] == 2 ? '女' : ''" :placeholder="item.disabled ? '通过选择人员账号获取' : '请输入'" :disabled="item.disabled" :maxlength="item.maxlength" :show-word-limit="item.showWordLimit" clearable></Input>
          <Input v-else v-model="form[item.key]" :placeholder="item.disabled ? '通过选择人员账号获取' : '请输入'" :disabled="item.disabled" :type="item.type || 'text'" :rows="item.rows" :maxlength="item.maxlength" :show-word-limit="item.showWordLimit" clearable></Input>
        </FormItem>
        </Col>
      </Row>
      <Row>
        <Col span="24">
        <FormItem label="关联设备" prop="drainageDeviceList">
          <div style="display: flex; width: 100%;">
            <deviceSelect :model-id="26" type="drainage" :default-sel-list="form.drainageDeviceList" :multiple="true" @on-change="addSubmit" />
            <chooseDeviceBox v-if="form.drainageDeviceList.length > 0" title="" style="width: 100%;">
              <div class="placeholder" v-show="form.drainageDeviceList.length === 0">请选择设备</div>
              <Tooltip v-for="(item, index) in form.drainageDeviceList" :max-width="300">
                <Tag color="blue" closable @on-close="delChoose(item, index)">{{ item.deviceCode }}</Tag>
                <template #content>
                  <p>设备名称: {{ item.sbmc }}</p>
                  <p>设备型号: {{ item.deviceUnitCode }}</p>
                  <p>设备编号: {{ item.deviceCode }}</p>
                  <p>区域位置: {{ item.areaPath }}</p>
                </template>
              </Tooltip>
            </chooseDeviceBox>
          </div>

        </FormItem>
        </Col>
      </Row>
    </Form>
    <div class="footer-btn">
      <Button type="primary" @click="confirm">提交</Button>
      <Button @click="back">取消</Button>
    </div>
  </detailCard>
</template>

<script lang="ts" setup>
import router from '@/router/livable';
import { FormItem, Select } from 'view-ui-plus';
import { getCurrentInstance, reactive, ref } from 'vue';
import { getPersonList } from '@/api/safeManage/emergencyManagement';
import { operationStaffService } from '@/api/safeManage/drainageControl_operationStaff';
import moment from 'moment';
import chooseDeviceBox from '@/components/common/deviceSelect/chooseDeviceBox.vue';

import Util from '@/utils';

const formList = ref<any>([
  { label: '姓名', key: 'name', disabled: true },
  { label: '性别', key: 'sex', disabled: true },
  { label: '联系电话', key: 'mobile', disabled: true },
  { label: '电子邮箱', key: 'email', disabled: true },
  { label: '岗位', key: 'job', disabled: true },
  { label: '部门', key: 'deptName', disabled: true },
  { label: '出生日期', key: 'birthday', type: 'date', disabled: true },
  { label: '身份证号码', key: 'idCard', maxlength: 18, disabled: true },
  { label: '健康状况', key: 'health', type: 'select'},
  {
    label: '备注',
    key: 'remark',
    type: 'textarea',
    maxlength: 200,
    rows: 3,
    span: 24,
    showWordLimit: true
  },
]);
const form = ref<any>({
  emergencyPersonId: '',
  account: '',
  name: '',
  sex: '',
  mobile: '',
  email: '',
  job: '',
  deptName: '',
  birthday: '',
  idCard: '',
  health: '',
  remark: '',
  drainageDeviceList: [],
});
const params = ref<any>({
  customQueryParams: {
  },
  page: {
    size: -1,
    current: 1,
  },
});
const data = ref<any>([]);
async function getPerson() {
  let res: any = await getPersonList(params.value);
  if (res.success) {
    cloneData.value = Util.objClone(res.data.records).map((item: any) => {
      return {
        ...item.personBigData,
        ...item
      };
    });

    cloneData.value = Util.objClone(cloneData.value).map((item: any) => {
      return {
        ...item,
        name: item.name + `(${item.mobile})`,
        emergencyPersonId: item.id
      };
    });
    data.value = Util.objClone(cloneData.value);
    
  }
}
getPerson();
function back() {
  router.back();
}
const rules = ref({
  emergencyPersonId: [{ type: 'number', required: true, message: '请选择人员账号', trigger: 'blur' }],
  idCard: [
    {
      pattern:
        /^[1-9]\d{5}(?:18|19|20)\d{2}(?:0[1-9]|10|11|12)(?:0[1-9]|[1-2]\d|30|31)\d{3}[\dXx]$/,
      message: '请输入有效的身份证号',
      trigger: 'blur',
    },
  ]
});
const that = getCurrentInstance()?.appContext.config.globalProperties;
const formCom = ref();
function confirm() {
  
  formCom.value.validate(async (flag: boolean) => {
    if (flag) {
      let res: any = await operationStaffService.add(form.value);
      if (res.success) {
        that?.$Message.success('新增成功');
        router.back();
      }
    }
  });
}

function handleClear() {
  const arr = ['birthday', 'idCard', 'health', 'remark']
  for (let key in form.value) {
    if (!arr.includes(key)) {
      form.value[key] = ''
    }
  }
}
const cloneData = ref<any>([]);

function handleClickList(item: any) {
  console.info(item)
  let drainageDeviceList = JSON.parse(JSON.stringify(form.value.drainageDeviceList || []));
  const arr = ['health', 'remark', 'account']
  for (let key in form.value) {
    if (!arr.includes(key)) {
      form.value[key] = item[key]
    }
  }
  form.value.account = item.mobile
  form.value.drainageDeviceList = drainageDeviceList
  
}

const addSubmit = async (list: any[]) => {
    console.log('选择了哪些设备---', list);
  form.value.drainageDeviceList = list
};

const delChoose = (row, index) => {
  form.value.drainageDeviceList.splice(index, 1)
}

</script>

<style lang="less" scoped>
.footer-btn {
  .ivu-btn+.ivu-btn {
    margin-left: 8px;
  }
}
</style>
