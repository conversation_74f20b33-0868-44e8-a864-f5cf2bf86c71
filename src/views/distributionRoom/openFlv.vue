<script>
// 非弹窗flv播放
import flv from 'flv.js'
import { h } from 'vue';
export default {
	name: 'openFlv',
	props: {
		styles: {
			type: [String],
			default: () => {
				return 'width: 100%; height: 100%;object-fit:fill'
			}
		},
		url: {
			type: [String],
			default: ''
		},
		domId: {
			type: [String],
			default: ''
		},
		mainId: {
			type: [String],
			default: ''
		},
		controls: { default: true }
	},
	data() {
		return {
			flvInstance: ''
		}
	},
	watch: {
		styles(newV) {
			if (newV && document.getElementById(this.domId)) {
				newV += ';display: block;'
				document.getElementById(this.domId).setAttribute('style', newV)
			}
		}
	},
	mounted() {
		/* this.open();*/
	},
	activated() {
		this.open()
	},
	deactivated() {
		this.clear()
	},
	methods: {
		open(url) {
			if (!url) {
				return;
			}
			if (document.getElementById(this.domId)) {
				document.getElementById(this.domId).remove();
				this.clear();
			}
			let video = document.createElement('video');

			video.setAttribute('id', this.domId)
			video.setAttribute('style', this.styles)
			video.setAttribute('autoplay', 'autoplay')
			video.style.display = 'block'
			if (this.controls) {
				video.setAttribute('controls', 'controls')
			} else {
				video.addEventListener('canplay', () => {
					console.log(111)
					this.$emit('on-play')
				})
			}
			document.getElementById(this.mainId).appendChild(video)
			this.flvInstance = flv.createPlayer({
				type: 'flv',
				url: url,
				cors: true
			})

			this.flvInstance.attachMediaElement(document.getElementById(this.domId));
			this.flvInstance.load();
			this.flvInstance.play();
		},
		pauseVideo() {
			this.flvInstance.pause();
		},
		playVideo() {
			this.flvInstance.play();
		},
		setVolumeVideo(volume) {
			this.flvInstance.volume = volume
		},
		clear() {
			if (this.flvInstance) {
				this.flvInstance.unload();
				this.flvInstance.destroy();
				this.flvInstance = null;
			}
		}
	},
	render(r) {
		return h('div', {
      id: this.mainId,
		})
	}

}
</script>

<style scoped>
</style>
